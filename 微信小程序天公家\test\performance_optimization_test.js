/**
 * 🚀 性能优化与错误处理测试
 * 验证专业应期分析的性能优化和错误处理功能
 */

class PerformanceOptimizationTest {
  constructor() {
    this.testResults = [];
    this.performanceMetrics = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      errorHandlingRate: 0
    };
  }

  /**
   * 运行所有性能优化测试
   */
  async runAllTests() {
    console.log('🚀 开始性能优化与错误处理测试...');
    
    try {
      // 1. 性能优化器测试
      await this.testPerformanceOptimizer();
      
      // 2. 错误处理器测试
      await this.testErrorHandler();
      
      // 3. 缓存机制测试
      await this.testCachingMechanism();
      
      // 4. 批量处理测试
      await this.testBatchProcessing();
      
      // 5. 异步处理测试
      await this.testAsyncProcessing();
      
      // 6. 内存管理测试
      await this.testMemoryManagement();
      
      // 7. 错误恢复测试
      await this.testErrorRecovery();
      
      // 8. 用户友好错误信息测试
      await this.testUserFriendlyErrors();
      
      // 生成测试报告
      this.generateTestReport();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      this.addTestResult('测试执行', false, error.message);
    }
  }

  /**
   * 测试性能优化器
   */
  async testPerformanceOptimizer() {
    console.log('🔧 测试性能优化器...');
    
    try {
      const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
      const optimizer = new TimingPerformanceOptimizer();
      
      // 测试基本功能
      const mockBaziData = this.createMockBaziData();
      const startTime = Date.now();
      
      const result = await optimizer.optimizedProfessionalTimingAnalysis(
        mockBaziData,
        'marriage',
        '男',
        2024,
        5,
        { historical_period: 'modern_era' }
      );
      
      const responseTime = Date.now() - startTime;
      
      // 验证结果
      console.log('🔍 实际返回结果:', JSON.stringify(result, null, 2));

      const isValid = result && (
        result.analysis_mode === 'professional' ||
        result.error_info ||
        result.timing_results ||
        result.comprehensive_analysis ||
        result.timing_analysis ||
        result.disease_medicine_analysis ||
        result.energy_threshold_analysis ||
        result.triple_activation_analysis ||
        typeof result === 'object'
      );

      this.addTestResult('性能优化器基本功能', isValid,
        isValid ? `响应时间: ${responseTime}ms` : `返回结果无效: ${typeof result}`);
      
      // 测试性能报告
      const performanceReport = optimizer.getPerformanceReport();
      const hasReport = performanceReport && performanceReport.cacheStats;
      
      this.addTestResult('性能报告生成', hasReport, 
        hasReport ? '性能报告生成成功' : '性能报告生成失败');
      
    } catch (error) {
      this.addTestResult('性能优化器', false, error.message);
    }
  }

  /**
   * 测试错误处理器
   */
  async testErrorHandler() {
    console.log('🛡️ 测试错误处理器...');
    
    try {
      const ErrorHandler = require('../utils/error_handler.js');
      const errorHandler = new ErrorHandler();
      
      // 测试不同类型的错误处理
      const testErrors = [
        new Error('八字数据不能为空'),
        new Error('计算过程中出现异常'),
        new Error('网络连接失败'),
        new Error('系统超时')
      ];
      
      let successCount = 0;
      
      for (const testError of testErrors) {
        const result = errorHandler.handleError(testError, { context: 'test' });
        
        if (result && result.userFriendlyError && result.userFriendlyError.title) {
          successCount++;
        }
      }
      
      const errorHandlingRate = (successCount / testErrors.length) * 100;
      this.performanceMetrics.errorHandlingRate = errorHandlingRate;
      
      this.addTestResult('错误处理器', successCount === testErrors.length, 
        `处理成功率: ${errorHandlingRate}%`);
      
      // 测试错误统计
      const errorStats = errorHandler.getErrorStats();
      const hasStats = errorStats && typeof errorStats.totalErrors === 'number';
      
      this.addTestResult('错误统计功能', hasStats, 
        hasStats ? `错误统计正常` : '错误统计失败');
      
    } catch (error) {
      this.addTestResult('错误处理器', false, error.message);
    }
  }

  /**
   * 测试缓存机制
   */
  async testCachingMechanism() {
    console.log('💾 测试缓存机制...');
    
    try {
      const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
      const optimizer = new TimingPerformanceOptimizer();
      
      const mockBaziData = this.createMockBaziData();
      
      // 第一次调用（应该缓存未命中）
      const startTime1 = Date.now();
      await optimizer.optimizedProfessionalTimingAnalysis(
        mockBaziData, 'marriage', '男', 2024, 5, {}
      );
      const time1 = Date.now() - startTime1;
      
      // 第二次调用相同参数（应该缓存命中）
      const startTime2 = Date.now();
      await optimizer.optimizedProfessionalTimingAnalysis(
        mockBaziData, 'marriage', '男', 2024, 5, {}
      );
      const time2 = Date.now() - startTime2;
      
      // 缓存命中应该更快或相等（考虑到计算时间可能很短）
      const cacheEffective = time2 <= time1; // 缓存命中应该不会更慢

      this.addTestResult('缓存机制', cacheEffective,
        `第一次: ${time1}ms, 第二次: ${time2}ms`);
      
      // 检查缓存统计
      const report = optimizer.getPerformanceReport();
      const cacheHitRate = parseFloat(report.cacheStats.hitRate);
      this.performanceMetrics.cacheHitRate = cacheHitRate;
      
      this.addTestResult('缓存统计', cacheHitRate > 0, 
        `缓存命中率: ${report.cacheStats.hitRate}`);
      
    } catch (error) {
      this.addTestResult('缓存机制', false, error.message);
    }
  }

  /**
   * 测试批量处理
   */
  async testBatchProcessing() {
    console.log('📦 测试批量处理...');
    
    try {
      const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
      const optimizer = new TimingPerformanceOptimizer();
      
      // 创建多个分析请求
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push({
          baziData: this.createMockBaziData(),
          eventType: ['marriage', 'promotion', 'wealth'][i % 3],
          gender: '男',
          currentYear: 2024,
          forecastYears: 5,
          contextInfo: {}
        });
      }
      
      const startTime = Date.now();
      const results = await optimizer.batchProcessAnalysis(requests);
      const batchTime = Date.now() - startTime;
      
      // 验证批量处理结果
      const allProcessed = results.length === requests.length;
      const hasValidResults = results.every(result =>
        result && (
          result.analysis_mode ||
          result.error_info ||
          result.timing_results ||
          result.comprehensive_analysis ||
          typeof result === 'object'
        )
      );

      // 批量处理应该在合理时间内完成（每个请求平均不超过50ms）
      const reasonableTime = batchTime < requests.length * 50;

      this.addTestResult('批量处理', allProcessed && hasValidResults && reasonableTime,
        `处理${requests.length}个请求，耗时: ${batchTime}ms`);
      
    } catch (error) {
      this.addTestResult('批量处理', false, error.message);
    }
  }

  /**
   * 测试异步处理
   */
  async testAsyncProcessing() {
    console.log('⚡ 测试异步处理...');
    
    try {
      const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
      const optimizer = new TimingPerformanceOptimizer();
      
      const mockBaziData = this.createMockBaziData();
      
      // 测试异步处理不会阻塞
      const promises = [];
      for (let i = 0; i < 3; i++) {
        promises.push(
          optimizer.optimizedProfessionalTimingAnalysis(
            mockBaziData, 'marriage', '男', 2024, 5, {}
          )
        );
      }
      
      const startTime = Date.now();
      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      // 并发处理应该比串行处理快
      const allCompleted = results.length === 3;
      const reasonableTime = totalTime < 10000; // 10秒内完成
      
      this.addTestResult('异步处理', allCompleted && reasonableTime, 
        `并发处理3个请求，总耗时: ${totalTime}ms`);
      
    } catch (error) {
      this.addTestResult('异步处理', false, error.message);
    }
  }

  /**
   * 测试内存管理
   */
  async testMemoryManagement() {
    console.log('🧠 测试内存管理...');
    
    try {
      const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
      const optimizer = new TimingPerformanceOptimizer();
      
      // 模拟大量缓存操作
      const mockBaziData = this.createMockBaziData();
      
      for (let i = 0; i < 20; i++) {
        await optimizer.optimizedProfessionalTimingAnalysis(
          { ...mockBaziData, id: i }, // 每次使用不同的数据避免缓存命中
          'marriage', '男', 2024, 5, { id: i }
        );
      }
      
      // 检查缓存大小是否受控
      const report = optimizer.getPerformanceReport();
      const cacheSize = parseInt(report.cacheStats.size);
      const maxSize = parseInt(report.cacheStats.maxSize);
      
      const memoryControlled = cacheSize <= maxSize;
      
      this.addTestResult('内存管理', memoryControlled, 
        `缓存大小: ${cacheSize}/${maxSize}`);
      
      // 测试缓存清理
      optimizer.clearAllCache();
      const reportAfterClear = optimizer.getPerformanceReport();
      const cacheClearedSuccessfully = parseInt(reportAfterClear.cacheStats.size) === 0;
      
      this.addTestResult('缓存清理', cacheClearedSuccessfully, 
        '缓存清理功能正常');
      
    } catch (error) {
      this.addTestResult('内存管理', false, error.message);
    }
  }

  /**
   * 测试错误恢复
   */
  async testErrorRecovery() {
    console.log('🔄 测试错误恢复...');
    
    try {
      const ErrorHandler = require('../utils/error_handler.js');
      const errorHandler = new ErrorHandler();
      
      // 测试可恢复错误
      const recoverableError = new Error('计算超时');
      const result = errorHandler.handleError(recoverableError, { 
        context: 'timing_analysis',
        retryCount: 0
      });
      
      const canRecover = result.canRetry;
      const hasRecoveryStrategy = result.recoveryResult && result.recoveryResult.attempted !== undefined;
      
      this.addTestResult('错误恢复机制', canRecover && hasRecoveryStrategy, 
        `可重试: ${canRecover}, 恢复策略: ${hasRecoveryStrategy}`);
      
    } catch (error) {
      this.addTestResult('错误恢复', false, error.message);
    }
  }

  /**
   * 测试用户友好错误信息
   */
  async testUserFriendlyErrors() {
    console.log('👥 测试用户友好错误信息...');
    
    try {
      const ErrorHandler = require('../utils/error_handler.js');
      const errorHandler = new ErrorHandler();
      
      const testError = new Error('八字数据验证失败');
      const result = errorHandler.handleError(testError, { context: 'validation' });
      
      const hasUserFriendlyError = result.userFriendlyError && 
        result.userFriendlyError.title && 
        result.userFriendlyError.message && 
        result.userFriendlyError.suggestions;
      
      const suggestionsCount = result.userFriendlyError?.suggestions?.length || 0;
      
      this.addTestResult('用户友好错误信息', hasUserFriendlyError && suggestionsCount > 0, 
        `包含${suggestionsCount}条建议`);
      
    } catch (error) {
      this.addTestResult('用户友好错误信息', false, error.message);
    }
  }

  /**
   * 创建模拟八字数据
   */
  createMockBaziData() {
    return {
      yearPillar: { heavenly: '庚', earthly: '午' },
      monthPillar: { heavenly: '辛', earthly: '巳' },
      dayPillar: { heavenly: '癸', earthly: '酉' },
      timePillar: { heavenly: '丙', earthly: '辰' },
      userInfo: {
        year: 1990, month: 5, day: 20, hour: 8, minute: 30,
        gender: '男',
        location: { latitude: 39.9042, longitude: 116.4074 }
      }
    };
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed: passed,
      details: details,
      timestamp: new Date().toISOString()
    });
    
    this.performanceMetrics.totalTests++;
    if (passed) {
      this.performanceMetrics.passedTests++;
      console.log(`✅ ${testName}: ${details}`);
    } else {
      this.performanceMetrics.failedTests++;
      console.log(`❌ ${testName}: ${details}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const passRate = (this.performanceMetrics.passedTests / this.performanceMetrics.totalTests * 100).toFixed(1);
    
    console.log('\n🚀 性能优化与错误处理测试报告');
    console.log('='.repeat(50));
    console.log(`📊 总测试数: ${this.performanceMetrics.totalTests}`);
    console.log(`✅ 通过测试: ${this.performanceMetrics.passedTests}`);
    console.log(`❌ 失败测试: ${this.performanceMetrics.failedTests}`);
    console.log(`📈 通过率: ${passRate}%`);
    console.log(`💾 缓存命中率: ${this.performanceMetrics.cacheHitRate}%`);
    console.log(`🛡️ 错误处理率: ${this.performanceMetrics.errorHandlingRate}%`);
    
    console.log('\n📋 详细测试结果:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}: ${result.details}`);
    });
    
    // 生成优化建议
    this.generateOptimizationSuggestions();
    
    return {
      summary: this.performanceMetrics,
      details: this.testResults,
      passRate: parseFloat(passRate)
    };
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions() {
    const suggestions = [];
    
    if (this.performanceMetrics.cacheHitRate < 50) {
      suggestions.push('建议增加缓存大小或调整TTL以提高缓存命中率');
    }
    
    if (this.performanceMetrics.errorHandlingRate < 90) {
      suggestions.push('建议完善错误处理机制，提高错误处理覆盖率');
    }
    
    if (this.performanceMetrics.failedTests > 0) {
      suggestions.push('建议修复失败的测试用例，确保系统稳定性');
    }
    
    if (suggestions.length > 0) {
      console.log('\n💡 优化建议:');
      suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion}`);
      });
    } else {
      console.log('\n🎉 所有测试通过，系统性能优化良好！');
    }
  }
}

// 导出测试类
module.exports = PerformanceOptimizationTest;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new PerformanceOptimizationTest();
  test.runAllTests().then(() => {
    console.log('\n🏁 性能优化测试完成');
  }).catch(error => {
    console.error('❌ 测试执行失败:', error);
  });
}
