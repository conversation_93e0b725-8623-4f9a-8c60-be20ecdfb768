/* 基础样式文件 */

/* 色彩系统 */
page {
  /* 主题色 */
  --primary-color: #4B7BF5;
  --primary-light: #86A8FF;
  --primary-dark: #3B62C4;
  --secondary-color: #86A8FF;
  --secondary-light: #B8CCFF;
  --secondary-dark: #6485CC;
  --accent-color: #FFB6C1;
  --accent-light: #FFD9DF;
  --accent-dark: #CC919A;

  /* 功能色 */
  --success-color: #2ECC71;
  --success-light: #7DEFA1;
  --success-dark: #25A35A;
  --warning-color: #F1C40F;
  --warning-light: #F8DC4D;
  --warning-dark: #C19D0C;
  --danger-color: #E74C3C;
  --danger-light: #F29488;
  --danger-dark: #B93D30;

  /* 中性色 */
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
  --text-disabled: #BDC3C7;
  --background: #F9FAFC;
  --background-light: #FFFFFF;
  --background-dark: #ECF0F1;
  --card-background: #FFFFFF;
  --border-color: #E0E6ED;
  --border-light: #F4F6F8;
  --border-dark: #CBD5E0;
  --shadow-color: rgba(0, 0, 0, 0.08);
  --shadow-light: rgba(0, 0, 0, 0.04);
  --shadow-dark: rgba(0, 0, 0, 0.16);

  /* 字体规范 */
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;

  /* 字号规范 */
  --font-xl: 40rpx;
  --font-lg: 32rpx;
  --font-md: 28rpx;
  --font-sm: 26rpx;
  --font-xs: 24rpx;

  /* 间距规范 */
  --spacing-xl: 32rpx;
  --spacing-lg: 24rpx;
  --spacing-md: 20rpx;
  --spacing-sm: 16rpx;
}

/* 基础容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: var(--spacing-xl);
  background: linear-gradient(180deg, var(--background) 0%, var(--card-background) 100%);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

/* 标题样式 */
.title-xl {
  font-size: var(--font-xl);
  font-weight: bold;
  color: var(--text-primary);
}

.title-lg {
  font-size: var(--font-lg);
  font-weight: bold;
  color: var(--text-primary);
}

/* 基础动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scale {
  from { transform: scale(0.95); }
  to { transform: scale(1); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}