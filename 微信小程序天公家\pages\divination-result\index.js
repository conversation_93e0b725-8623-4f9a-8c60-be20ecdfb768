// pages/divination-result/index.js
// 天公师兄占卜结果展示页面 - 集成天公师父功能

const DivinationCalculator = require('../../utils/divination_calculator');
const NavigationColor = require('../../utils/navigation_color');
const PosterGenerator = require('../../utils/poster_generator');
const config = require('../../utils/config.js');

Page({
  data: {
    // 主题相关
    themeClass: 'tarot-theme',

    // 占卜结果
    result: null,
    analysis: null,

    // 滚动相关
    scrollTop: 0,
    scrollEnabled: true,

    // 问题类型映射
    questionTypeMap: {
      'lost': '失物寻找',
      'wealth': '求财问事',
      'travel': '出行安全',
      'love': '感情问题',
      'study': '学习考试',
      'career': '工作事业',
      'health': '健康状况',
      'weather': '天气预测',
      'disease': '疾病医疗',
      'food': '饮食宜忌',
      'other': '其他事务'
    },
    questionTypeText: '',

    // 界面控制
    showCalculation: true,
    showCalculationDetail: false,
    showShareModal: false,

    // 天公师父集成相关
    isTianggongshifuResult: false, // 是否为天公师父结果
    tianggongshifuInterpretation: null, // 天公师父解读
    alternatives: [], // 备选解读
    showAlternatives: false // 是否显示备选解读
  },

  onLoad(options) {
    console.log('✅ 占卜结果页面加载，集成天公师父功能', options);

    // 检查是否为天公师父结果
    const isTianggongshifuResult = options.source === 'tianggongshifu';
    this.setData({
      isTianggongshifuResult: isTianggongshifuResult
    });

    // 处理路由问题 - 使用安全的方式
    try {
      // 安全获取路由信息
      const currentRoute = 'pages/divination-result/index';

      // 检查全局路由修复函数
      const app = getApp();
      if (app && typeof app._fixRouteIssue === 'function') {
        app._fixRouteIssue(currentRoute);
        console.log('已尝试修复路由问题');
      }

      // 设置页面级路由信息
      this.route = currentRoute;
      console.log('页面路径设置为:', currentRoute);
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }

    try {
      // 显示加载状态
      wx.showLoading({
        title: isTianggongshifuResult ? '正在解析天公师父占卜...' : '正在解析占卜结果...',
        mask: true
      });

      // 设置导航栏颜色
      NavigationColor.setNavigationBarColorByRole('tarot');

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: isTianggongshifuResult ? '天公师父占卜' : '洞察先机'
      });

      // 解析占卜数据
      if (options.data) {
        try {
          const divinationData = JSON.parse(decodeURIComponent(options.data));

          if (isTianggongshifuResult) {
            this.processTianggongshifuData(divinationData);
          } else {
            this.processDivinationData(divinationData);
          }
        } catch (error) {
          console.error('解析占卜数据失败:', error);
          wx.hideLoading();
          this.showError('数据解析失败，请重新占卜');
        }
      } else {
        wx.hideLoading();
        this.showError('缺少占卜数据，请重新开始');
      }

    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.hideLoading();
      this.showError('页面加载失败，请重试');
    }
  },

  /**
   * 处理天公师父占卜数据
   */
  processTianggongshifuData(data) {
    console.log('🔮 处理天公师父占卜数据:', data);

    try {
      // 获取问题类型文本
      const questionTypeText = this.data.questionTypeMap[data.questionType] || '其他事务';

      // 处理天公师父解读数据，确保数据完整性
      const interpretation = this.processInterpretationData(data.tianggongshifuInterpretation, data);
      const alternatives = this.processAlternativesData(data.alternatives || []);

      // 设置天公师父相关数据
      this.setData({
        tianggongshifuInterpretation: interpretation,
        alternatives: alternatives,
        questionTypeText: questionTypeText,
        showAlternatives: false,
        searchStrategy: data.searchStrategy,
        intelligentAnalysis: data.intelligentAnalysis,
        semanticAnalysis: data.semanticAnalysis,
        result: {
          questionType: data.questionType,
          questionText: data.questionText,
          timestamp: data.timestamp,
          method: 'tianggongshifu',
          confidence: interpretation.confidence
        }
      });

      // 保存到历史记录
      this.saveTianggongshifuToHistory(data);

      // 隐藏加载状态
      wx.hideLoading();

      console.log('🔮 天公师父数据处理完成');

    } catch (error) {
      console.error('🔮 处理天公师父数据失败:', error);
      wx.hideLoading();
      this.showError('天公师父数据处理失败: ' + error.message);
    }
  },

  /**
   * 处理解读数据，确保数据完整性
   */
  processInterpretationData(interpretation, data) {
    if (!interpretation) {
      return {
        id: `tianggongshifu_${Date.now()}`,
        title: '天公师父占卜',
        original_text: '古籍原文暂缺',
        description: '现代解读暂缺',
        interpretation: '详细解读暂缺',
        content_type: data.questionType || '综合占',
        luck_level: '中平',
        keywords: [],
        confidence: 0.5,
        source: 'fallback'
      };
    }

    return {
      id: interpretation.id || `tianggongshifu_${Date.now()}`,
      title: interpretation.title || '天公师父占卜',
      original_text: interpretation.original_text || interpretation.original || '古籍原文暂缺',
      description: interpretation.description || interpretation.explanation || '现代解读暂缺',
      interpretation: interpretation.interpretation || interpretation.advice || interpretation.description || '详细解读暂缺',
      content_type: interpretation.content_type || data.questionType || '综合占',
      luck_level: interpretation.luck_level || interpretation.fortune || '中平',
      keywords: interpretation.keywords || [],
      confidence: interpretation.confidence || data.confidence || 0.8,
      source: interpretation.source || 'tianggongshifu_api',
      searchStrategy: data.searchStrategy || 'unknown'
    };
  },

  /**
   * 处理备选解读数据
   */
  processAlternativesData(alternatives) {
    return alternatives.map((alt, index) => ({
      id: alt.id || `alt_${Date.now()}_${index}`,
      title: alt.title || '备选解读',
      description: alt.description || alt.interpretation || '解读内容',
      original_text: alt.original_text || alt.original || '',
      luck_level: alt.luck_level || '中平'
    }));
  },

  /**
   * 保存天公师父结果到历史记录
   */
  saveTianggongshifuToHistory(data) {
    try {
      const historyKey = 'tianggongshifu_divination_history';
      let history = wx.getStorageSync(historyKey) || [];

      const historyItem = {
        id: Date.now().toString(),
        timestamp: data.timestamp,
        questionText: data.questionText,
        questionType: data.questionType,
        interpretation: data.tianggongshifuInterpretation,
        alternatives: data.alternatives,
        isRandom: data.isRandom || false,
        method: 'tianggongshifu'
      };

      // 添加到历史记录开头
      history.unshift(historyItem);

      // 限制历史记录数量（最多保存50条）
      if (history.length > 50) {
        history = history.slice(0, 50);
      }

      wx.setStorageSync(historyKey, history);
      console.log('🔮 天公师父结果已保存到历史记录');

    } catch (error) {
      console.error('🔮 保存天公师父历史记录失败:', error);
    }
  },

  /**
   * 处理传统占卜数据
   */
  processDivinationData(data) {
    console.log('🔮 占卜结果页面处理数据:', {
      method: data.method,
      questionType: data.questionType,
      questionText: data.questionText,
      hasTimeInfo: !!data.timeInfo,
      hasFullTimeInfo: !!data.fullTimeInfo,
      hasNumbers: !!data.numbers,
      hasLocalResult: !!data.localResult,
      hasApiEnhancement: !!data.apiEnhancement,
      dataKeys: Object.keys(data),
      fullData: data
    });

    try {
      let result, analysis;

      // 检查是否有本地计算结果（新的业务逻辑）
      if (data.localResult) {
        console.log('🔮 使用前端本地计算结果');
        result = data.localResult;

        // 生成详细分析
        analysis = DivinationCalculator.generateAnalysis(result);
        console.log('占卜分析结果:', analysis);

        // 如果有API增强，合并增强内容
        if (data.apiEnhancement) {
          console.log('🔮 合并API增强分析:', data.apiEnhancement);
          // 可以在这里合并API增强的内容
        }
      } else {
        console.log('🔮 使用占卜计算器重新计算');
        // 使用占卜计算器计算结果（旧的业务逻辑）
        result = DivinationCalculator.calculate(data);
        console.log('占卜计算结果:', result);

        // 生成详细分析
        analysis = DivinationCalculator.generateAnalysis(result);
        console.log('占卜分析结果:', analysis);
      }

      console.log('智能指引:', analysis.specific.title);
      console.log('针对性建议:', analysis.specific.details);

      // 获取问题类型文本
      const questionTypeText = this.data.questionTypeMap[result.questionType] || '其他事务';

      this.setData({
        result: result,
        analysis: analysis,
        questionTypeText: questionTypeText
      });

      // 保存到本地存储（用于历史记录）
      this.saveToHistory(result, analysis);

      // 隐藏加载状态
      wx.hideLoading();

    } catch (error) {
      console.error('处理占卜数据失败:', error);
      wx.hideLoading();
      this.showError('占卜计算失败，请重新尝试');
    }
  },

  /**
   * 保存到历史记录
   */
  saveToHistory(result, analysis) {
    try {
      const historyKey = 'divination_history';
      const history = wx.getStorageSync(historyKey) || [];
      
      const record = {
        id: Date.now().toString(),
        timestamp: result.timestamp,
        questionType: result.questionType,
        questionText: result.questionText,
        godName: result.god.name,
        fortune: result.god.fortune,
        method: result.method,
        date: new Date().toLocaleDateString()
      };
      
      // 添加到历史记录开头
      history.unshift(record);
      
      // 只保留最近20条记录
      if (history.length > 20) {
        history.splice(20);
      }
      
      wx.setStorageSync(historyKey, history);
      console.log('占卜记录已保存到历史');
      
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    // 2秒后返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  /**
   * 切换计算过程显示
   */
  toggleCalculation() {
    this.setData({
      showCalculationDetail: !this.data.showCalculationDetail
    });
  },

  /**
   * 分享结果
   */
  shareResult() {
    this.setData({
      showShareModal: true
    });
  },

  /**
   * 隐藏分享弹窗
   */
  hideShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  /**
   * 分享给朋友
   */
  shareToFriend() {
    const result = this.data.result;
    const shareContent = this.generateShareContent(result);
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 触发分享
    wx.shareAppMessage({
      title: shareContent.title,
      desc: shareContent.desc,
      path: `/pages/divination-result/index?shared=true`,
      success: () => {
        console.log('分享成功');
        this.hideShareModal();
      },
      fail: (error) => {
        console.error('分享失败:', error);
        wx.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 保存到相册
   */
  async saveToAlbum() {
    try {
      wx.showLoading({
        title: '生成海报中...',
        mask: true
      });

      console.log('✅ 生成海报（已删除二维码和副标题）');

      // 生成海报
      const posterPath = await PosterGenerator.generatePoster(this.data.result, this.data.analysis);

      // 保存到相册
      await PosterGenerator.saveToAlbum(posterPath);

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      this.hideShareModal();

    } catch (error) {
      console.error('保存海报失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成分享内容
   */
  generateShareContent(result) {
    const god = result.god;
    const title = `天公师兄n/遇事不决  可问天公：${god.name}`;
    const desc = `${god.description} - ${god.advice}`;

    return { title, desc };
  },

  /**
   * 返回首页
   */
  goHome() {
    console.log('✅ 返回首页按钮点击');
    wx.reLaunch({
      url: '/pages/assessment-hub/index'
    });
  },

  /**
   * 再次占卜
   */
  newDivination() {
    // 检查是否同一天已经占卜过相同问题
    const today = new Date().toDateString();
    const lastDivinationDate = wx.getStorageSync('last_divination_date');
    const lastQuestion = wx.getStorageSync('last_question');
    
    if (lastDivinationDate === today && lastQuestion === this.data.result.questionText) {
      wx.showModal({
        title: '提醒',
        content: '同一问题一日内不宜重复占卜，再占不验。是否继续？',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.goToNewDivination();
          }
        }
      });
    } else {
      this.goToNewDivination();
    }
  },

  /**
   * 跳转到新占卜
   */
  goToNewDivination() {
    // 保存当前占卜信息
    const today = new Date().toDateString();
    wx.setStorageSync('last_divination_date', today);
    wx.setStorageSync('last_question', this.data.result.questionText);
    
    // 跳转到信息收集页面
    wx.redirectTo({
      url: '/pages/divination-input/index'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const result = this.data.result;
    if (result) {
      const shareContent = this.generateShareContent(result);
      return {
        title: shareContent.title,
        desc: shareContent.desc,
        path: `/pages/assessment-hub/index`
      };
    }

    return {
      title: this.data.isTianggongshifuResult ? '天公师父古法占卜' : '天公师兄占卜',
      desc: '遇事不决，可问天公',
      path: `/pages/assessment-hub/index`
    };
  },

  /**
   * 切换显示备选解读
   */
  toggleAlternatives() {
    this.setData({
      showAlternatives: !this.data.showAlternatives
    });
  },

  /**
   * 选择备选解读
   */
  selectAlternative(e) {
    const index = e.currentTarget.dataset.index;
    const alternative = this.data.alternatives[index];

    if (alternative) {
      // 将当前解读移到备选列表
      const currentInterpretation = this.data.tianggongshifuInterpretation;
      const newAlternatives = [...this.data.alternatives];
      newAlternatives[index] = currentInterpretation;

      // 更新主解读
      this.setData({
        tianggongshifuInterpretation: alternative,
        alternatives: newAlternatives
      });

      wx.showToast({
        title: '已切换解读',
        icon: 'success'
      });
    }
  },

  /**
   * 重新获取天公师父解读
   */
  async refreshTianggongshifuInterpretation() {
    if (!this.data.result || !this.data.result.questionText) {
      wx.showToast({
        title: '无法重新获取解读',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '重新查询天公师父...',
        mask: true
      });

      // 调用天公师父API获取新的解读
      const interpretation = await this.getTianggongshifuInterpretation(
        this.data.result.questionText,
        this.data.result.questionType
      );

      wx.hideLoading();

      if (interpretation.success) {
        this.setData({
          tianggongshifuInterpretation: interpretation.interpretation,
          alternatives: interpretation.alternatives || []
        });

        wx.showToast({
          title: '解读已更新',
          icon: 'success'
        });
      } else {
        throw new Error('获取解读失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('🔮 重新获取天公师父解读失败:', error);
      wx.showToast({
        title: '获取解读失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取天公师父解读
   */
  async getTianggongshifuInterpretation(question, questionType) {
    return new Promise((resolve) => {
      // 模拟API调用
      setTimeout(() => {
        const interpretations = {
          '事业': {
            title: '事业运势解读',
            content: '当前事业运势平稳，建议稳扎稳打，不宜冒进。',
            advice: '保持专注，提升自身能力。',
            lucky_direction: '东南方',
            lucky_time: '午时（11:00-13:00）'
          },
          '感情': {
            title: '感情运势解读',
            content: '感情方面需要更多的耐心和理解。',
            advice: '以诚待人，用心经营感情。',
            lucky_direction: '西南方',
            lucky_time: '酉时（17:00-19:00）'
          }
        };

        const result = interpretations[questionType] || {
          title: '综合运势解读',
          content: '运势总体平稳，建议顺应自然。',
          advice: '心态平和，顺其自然。',
          lucky_direction: '正南方',
          lucky_time: '巳时（09:00-11:00）'
        };

        resolve({
          success: true,
          interpretation: result,
          alternatives: []
        });
      }, 1000);
    });
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline() {
    const result = this.data.result;
    if (result) {
      const shareContent = this.generateShareContent(result);
      return {
        title: shareContent.title,
        query: 'shared=true'
      };
    }
    
    return {
      title: '天公师兄·李淳风六壬时课 - 无事不占，一事一占'
    };
  },

  /**
   * 滚动事件处理
   */
  onScroll(e) {
    this.setData({
      scrollTop: e.detail.scrollTop
    });
  },

  /**
   * 滚动到顶部
   */
  scrollToTop() {
    this.setData({
      scrollTop: 0
    });
  },

  /**
   * 滚动到指定位置
   */
  scrollToPosition(scrollTop) {
    this.setData({
      scrollTop: scrollTop
    });
  },

  // 重复的函数已删除，使用上面的版本
});
