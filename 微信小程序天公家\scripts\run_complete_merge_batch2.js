/**
 * 执行完整数据库合并 - 第二批次
 */

const CompleteDatabaseBatch2Merger = require('../utils/merge_complete_database_batch2.js');

async function main() {
  try {
    const merger = new CompleteDatabaseBatch2Merger();
    const result = await merger.execute();
    
    if (result.success) {
      console.log('\n✅ 完整数据库合并成功!');
      console.log(`📈 数据库已从62位扩展到${result.data.celebrities.length}位名人`);
      process.exit(0);
    } else {
      console.log('\n❌ 合并失败!');
      process.exit(1);
    }
  } catch (error) {
    console.error('执行错误:', error);
    process.exit(1);
  }
}

main();
