// verify_changsheng_authority.js
// 验证长生十二宫表的权威性和准确性

console.log('📚 长生十二宫表权威性验证');
console.log('='.repeat(80));

// 收集不同来源的长生十二宫表进行对比
const changshengSources = {
  // 来源1：传统《三命通会》版本
  sanming: {
    name: '《三命通会》传统版',
    authority: '权威古籍',
    '甲': { '亥': '长生', '子': '沐浴', '丑': '冠带', '寅': '临官', '卯': '帝旺', '辰': '衰', '巳': '病', '午': '死', '未': '墓', '申': '绝', '酉': '胎', '戌': '养' },
    '乙': { '午': '长生', '巳': '沐浴', '辰': '冠带', '卯': '临官', '寅': '帝旺', '丑': '衰', '子': '病', '亥': '死', '戌': '墓', '酉': '绝', '申': '胎', '未': '养' },
    '丙': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '丁': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '戊': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '己': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '庚': { '巳': '长生', '午': '沐浴', '未': '冠带', '申': '临官', '酉': '帝旺', '戌': '衰', '亥': '病', '子': '死', '丑': '墓', '寅': '绝', '卯': '胎', '辰': '养' },
    '辛': { '子': '长生', '亥': '沐浴', '戌': '冠带', '酉': '临官', '申': '帝旺', '未': '衰', '午': '病', '巳': '死', '辰': '墓', '卯': '绝', '寅': '胎', '丑': '养' },
    '壬': { '申': '长生', '酉': '沐浴', '戌': '冠带', '亥': '临官', '子': '帝旺', '丑': '衰', '寅': '病', '卯': '死', '辰': '墓', '巳': '绝', '午': '胎', '未': '养' },
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '午': '死', '未': '墓', '申': '绝', '巳': '胎', '辰': '养' }
  },

  // 来源2：现代权威软件"问真八字"
  wenzhen: {
    name: '"问真八字"现代标准',
    authority: '现代权威软件',
    // 基于实际测试结果反推的表
    '甲': { '午': '绝' }, // 甲在午 → 绝
    '辛': { '丑': '冠带' }, // 辛在丑 → 冠带  
    '壬': { '戌': '衰' }, // 壬在戌 → 衰
    '癸': { '卯': '长生', '丑': '养', '午': '死', '戌': '冠带' } // 癸的测试结果
  },

  // 来源3：《渊海子平》版本
  yuanhai: {
    name: '《渊海子平》古籍版',
    authority: '权威古籍',
    // 与三命通会基本一致，但可能有细微差别
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '午': '死', '未': '墓', '申': '绝', '巳': '胎', '辰': '养' }
  },

  // 来源4：我们当前使用的版本
  current: {
    name: '我们当前使用的版本',
    authority: '待验证',
    '甲': { '亥': '长生', '子': '沐浴', '丑': '冠带', '寅': '临官', '卯': '帝旺', '辰': '衰', '巳': '病', '午': '死', '未': '墓', '申': '绝', '酉': '胎', '戌': '养' },
    '辛': { '子': '长生', '亥': '沐浴', '戌': '冠带', '酉': '临官', '申': '帝旺', '未': '衰', '午': '病', '巳': '死', '辰': '墓', '卯': '绝', '寅': '胎', '丑': '养' },
    '壬': { '申': '长生', '酉': '沐浴', '戌': '冠带', '亥': '临官', '子': '帝旺', '丑': '衰', '寅': '病', '卯': '死', '辰': '墓', '巳': '绝', '午': '胎', '未': '养' },
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '午': '绝', '未': '墓', '申': '死', '巳': '胎', '辰': '养' }
  }
};

// 测试数据
const testCases = [
  { gan: '甲', zhi: '午', expected: '绝', source: 'wenzhen' },
  { gan: '辛', zhi: '丑', expected: '冠带', source: 'wenzhen' },
  { gan: '壬', zhi: '戌', expected: '衰', source: 'wenzhen' },
  { gan: '癸', zhi: '卯', expected: '长生', source: 'wenzhen' },
  { gan: '癸', zhi: '丑', expected: '养', source: 'wenzhen' },
  { gan: '癸', zhi: '午', expected: '死', source: 'wenzhen' },
  { gan: '癸', zhi: '戌', expected: '冠带', source: 'wenzhen' }
];

// 验证各个来源的准确性
function verifySourceAccuracy() {
  console.log('\n🔍 各来源准确性验证:');
  console.log('='.repeat(50));

  Object.entries(changshengSources).forEach(([sourceKey, source]) => {
    console.log(`\n📖 ${source.name} (${source.authority}):`);
    
    let correctCount = 0;
    let totalTests = 0;
    
    testCases.forEach(testCase => {
      if (source[testCase.gan] && source[testCase.gan][testCase.zhi]) {
        const calculated = source[testCase.gan][testCase.zhi];
        const isCorrect = calculated === testCase.expected;
        
        console.log(`  ${testCase.gan}在${testCase.zhi} → ${calculated} ${isCorrect ? '✅' : '❌'} (期望: ${testCase.expected})`);
        
        if (isCorrect) correctCount++;
        totalTests++;
      } else {
        console.log(`  ${testCase.gan}在${testCase.zhi} → 未定义 ❓`);
      }
    });
    
    if (totalTests > 0) {
      const accuracy = (correctCount / totalTests * 100).toFixed(1);
      console.log(`  准确率: ${correctCount}/${totalTests} (${accuracy}%)`);
    }
  });
}

// 分析差异原因
function analyzeDifferences() {
  console.log('\n🔍 差异分析:');
  console.log('='.repeat(50));

  console.log('\n关键差异点:');
  
  // 癸水的差异
  console.log('\n癸水长生表对比:');
  const guiTestZhi = ['丑', '午', '卯', '戌'];
  
  guiTestZhi.forEach(zhi => {
    console.log(`\n癸在${zhi}:`);
    Object.entries(changshengSources).forEach(([key, source]) => {
      if (source['癸'] && source['癸'][zhi]) {
        const state = source['癸'][zhi];
        console.log(`  ${source.name}: ${state}`);
      }
    });
  });

  console.log('\n🤔 可能的差异原因:');
  console.log('1. 古籍版本差异 - 不同古籍可能有不同的长生表');
  console.log('2. 传承流派差异 - 不同流派可能有不同的理解');
  console.log('3. 现代软件算法 - 现代软件可能使用了特定的算法版本');
  console.log('4. 阴干阳干起法 - 阴干是否逆行存在争议');
  console.log('5. 地支藏干影响 - 是否考虑地支藏干的影响');
}

// 权威性评估
function assessAuthority() {
  console.log('\n📊 权威性评估:');
  console.log('='.repeat(50));

  const authorityRanking = [
    {
      name: '《三命通会》',
      authority: 95,
      reason: '明代万民英著，集八字学大成，被誉为八字学圣经',
      pros: ['历史悠久', '理论完整', '广泛认可'],
      cons: ['古文难懂', '部分内容过时']
    },
    {
      name: '《渊海子平》',
      authority: 90,
      reason: '宋代徐大升著，八字学开山之作',
      pros: ['理论基础', '逻辑严密', '影响深远'],
      cons: ['年代久远', '内容相对简略']
    },
    {
      name: '"问真八字"软件',
      authority: 75,
      reason: '现代权威八字软件，算法经过大量验证',
      pros: ['计算准确', '使用广泛', '持续更新'],
      cons: ['算法不透明', '可能有商业考量', '缺乏理论依据说明']
    },
    {
      name: '我们当前版本',
      authority: 60,
      reason: '基于传统古籍，但未经充分验证',
      pros: ['基于古籍', '逻辑清晰'],
      cons: ['未经验证', '可能有错误', '来源不明确']
    }
  ];

  authorityRanking.forEach((item, index) => {
    console.log(`\n${index + 1}. ${item.name} (权威度: ${item.authority}%)`);
    console.log(`   理由: ${item.reason}`);
    console.log(`   优点: ${item.pros.join(', ')}`);
    console.log(`   缺点: ${item.cons.join(', ')}`);
  });
}

// 建议的解决方案
function recommendSolution() {
  console.log('\n💡 建议的解决方案:');
  console.log('='.repeat(50));

  console.log('\n🎯 短期解决方案:');
  console.log('1. 使用"问真八字"的结果作为标准，反推正确的长生表');
  console.log('2. 针对测试案例，修正我们的长生表以匹配权威结果');
  console.log('3. 在系统中标注数据来源和版本信息');

  console.log('\n🎯 长期解决方案:');
  console.log('1. 收集多个权威古籍的长生表进行对比研究');
  console.log('2. 与多个权威八字软件的结果进行交叉验证');
  console.log('3. 咨询八字学专家，确认最权威的版本');
  console.log('4. 建立版本切换机制，支持多种长生表版本');

  console.log('\n🎯 质量保证措施:');
  console.log('1. 建立测试案例库，包含已知正确结果的八字');
  console.log('2. 实现自动化测试，确保修改不会引入新错误');
  console.log('3. 添加数据来源标注，提高透明度');
  console.log('4. 定期与权威软件结果对比，持续改进');
}

// 执行验证
console.log('\n📋 基于"问真八字"的测试案例:');
testCases.forEach(testCase => {
  console.log(`${testCase.gan}在${testCase.zhi} → 期望: ${testCase.expected}`);
});

verifySourceAccuracy();
analyzeDifferences();
assessAuthority();
recommendSolution();

console.log('\n📊 验证总结:');
console.log('='.repeat(40));
console.log('❌ 我们当前的长生表与"问真八字"存在显著差异');
console.log('⚠️ 需要进一步验证长生表的权威性和准确性');
console.log('💡 建议采用"问真八字"的结果作为标准进行修正');
console.log('🔧 需要建立更完善的验证和质量保证机制');

console.log('\n🎯 下一步行动:');
console.log('1. 立即修正长生表以匹配"问真八字"结果');
console.log('2. 建立多版本长生表支持机制');
console.log('3. 增加更多测试案例进行验证');
console.log('4. 咨询八字学专家确认权威版本');
