<!--pages/divination-result/index.wxml-->
<scroll-view class="scroll-container {{themeClass}}" scroll-y="{{true}}" enhanced="{{false}}" show-scrollbar="{{false}}" bounces="{{false}}" scroll-with-animation="{{false}}" bindscroll="onScroll">
  <view class="container">
  <!-- 背景装饰 -->
  <view class="background-decoration"></view>
  
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="master-info">
      <image class="master-avatar" src="{{isTianggongshifuResult ? '/assets/icons/tiangong-shifu.png' : '/assets/icons/tiangong-master.svg'}}"></image>
      <view class="master-text">
        <text class="master-name">{{isTianggongshifuResult ? '天工师父' : '天公师兄'}}</text>
        <text class="master-subtitle">为您解卦</text>
      </view>
    </view>
  </view>

  <!-- 玉匣记结果展示 -->
  <view class="yujiaji-result-section" wx:if="{{isYujiajiResult}}">
    <view class="yujiaji-card">
      <view class="yujiaji-header">
        <view class="yujiaji-icon">
          <text class="yujiaji-title">玉匣记</text>
        </view>
        <view class="yujiaji-info">
          <text class="yujiaji-subtitle">古法占卜</text>
          <text class="yujiaji-luck" wx:if="{{yujiajiInterpretation.luck_level}}">{{yujiajiInterpretation.luck_level}}</text>
        </view>
      </view>

      <!-- 古籍原文 -->
      <view class="yujiaji-original" wx:if="{{yujiajiInterpretation.original_text}}">
        <view class="original-label">古籍原文：</view>
        <text class="original-text">{{yujiajiInterpretation.original_text}}</text>
      </view>

      <!-- 现代解读 -->
      <view class="yujiaji-interpretation">
        <view class="interpretation-label">现代解读：</view>
        <text class="interpretation-text">{{yujiajiInterpretation.description || yujiajiInterpretation.interpretation}}</text>
      </view>

      <!-- 占卜标题 -->
      <view class="yujiaji-title-section" wx:if="{{yujiajiInterpretation.title}}">
        <view class="title-label">占卜要义：</view>
        <text class="title-text">{{yujiajiInterpretation.title}}</text>
      </view>

      <!-- 关键词 -->
      <view class="yujiaji-keywords" wx:if="{{yujiajiInterpretation.keywords && yujiajiInterpretation.keywords.length > 0}}">
        <view class="keywords-label">关键词：</view>
        <view class="keywords-list">
          <text class="keyword-item" wx:for="{{yujiajiInterpretation.keywords}}" wx:key="index">{{item}}</text>
        </view>
      </view>

      <view class="yujiaji-source">
        <text class="source-text">——《玉匣记》古籍</text>
      </view>
    </view>

    <!-- 备选解读 -->
    <view class="alternatives-section" wx:if="{{alternatives && alternatives.length > 0}}">
      <view class="section-title">
        <text>备选解读</text>
        <text class="toggle-btn" bindtap="toggleAlternatives">{{showAlternatives ? '收起' : '展开'}}</text>
      </view>

      <view class="alternatives-list" wx:if="{{showAlternatives}}">
        <view class="alternative-item" wx:for="{{alternatives}}" wx:key="id" bindtap="selectAlternative" data-index="{{index}}">
          <view class="alternative-title">{{item.title}}</view>
          <view class="alternative-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 传统六神结果展示 -->
  <view class="god-result-section" wx:if="{{!isYujiajiResult}}">
    <view class="god-card {{result.god.isAuspicious ? 'auspicious' : 'inauspicious'}} {{result.god.fortuneLevel}}"
          style="border-color: {{result.god.color}}">
      <view class="god-header">
        <view class="god-icon {{result.god.isAuspicious ? 'auspicious-icon' : 'inauspicious-icon'}}"
              style="background: {{result.god.color}}">
          <text class="god-name">{{result.god.name}}</text>
        </view>
        <view class="god-info">
          <text class="god-element">{{result.god.element}}行 · {{result.god.beast}}</text>
          <text class="god-fortune {{result.god.isAuspicious ? 'fortune-auspicious' : 'fortune-inauspicious'}}"
                style="color: {{result.god.color}}">{{result.god.fortune}}</text>
        </view>
      </view>

      <view class="god-description">
        <text class="description-text">{{analysis.overall.description}}</text>
        <text class="source-text">——《玉匣记》</text>
      </view>


    </view>
  </view>

  <!-- 问题回顾 -->
  <view class="question-review">
    <view class="section-title">
      <text>您的问题</text>
    </view>
    <view class="question-content">
      <text class="question-type">{{questionTypeText}}</text>
      <text class="question-text">{{result.questionText}}</text>
    </view>
  </view>

  <!-- 详细分析 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <!-- 针对性建议 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>🎯 针对性指导</text>
      </view>
      <view class="card-content">
        <text class="specific-advice">{{analysis.specific.title}}</text>
        <view class="advice-details" wx:if="{{analysis.specific.details && analysis.specific.details.length > 0}}">
          <text wx:for="{{analysis.specific.details}}" wx:key="index" class="detail-item">
            • {{item}}
          </text>
        </view>
        <view class="action-items" wx:if="{{analysis.specific.actionItems && analysis.specific.actionItems.length > 0}}">
          <text class="action-title">具体行动建议：</text>
          <text wx:for="{{analysis.specific.actionItems}}" wx:key="index" class="action-item">
            ✓ {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 应期时间线 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>📅 应期时间</text>
      </view>
      <view class="timeline-content">
        <view class="timeline-item" wx:for="{{analysis.timeline}}" wx:key="day">
          <view class="timeline-dot"></view>
          <view class="timeline-info">
            <text class="timeline-day">第{{item.day}}日</text>
            <text class="timeline-date">{{item.date}}</text>
            <text class="timeline-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 化解建议（凶卦专用） -->
    <view class="analysis-card" wx:if="{{!result.god.isAuspicious && result.god.resolution}}">
      <view class="card-title">
        <text>🙏 化解建议</text>
      </view>
      <view class="resolution-content">
        <view class="resolution-section">
          <text class="resolution-subtitle">化解方法：</text>
          <view class="resolution-methods">
            <view class="method-item" wx:for="{{result.god.resolution.methods}}" wx:key="*this">
              <text class="method-text">• {{item}}</text>
            </view>
          </view>
        </view>

        <view class="resolution-section">
          <text class="resolution-subtitle">注意事项：</text>
          <view class="resolution-taboos">
            <view class="taboo-item" wx:for="{{result.god.resolution.taboos}}" wx:key="*this">
              <text class="taboo-text">⚠️ {{item}}</text>
            </view>
          </view>
        </view>

        <view class="resolution-timing">
          <text class="timing-text">⏰ {{result.god.resolution.timing}}</text>
        </view>
      </view>
    </view>

    <!-- 注意事项 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>⚠️ 注意事项</text>
      </view>
      <view class="card-content">
        <view class="precaution-list">
          <text wx:for="{{analysis.precautions}}" wx:key="index" class="precaution-item">
            • {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 化解方法 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>🙏 化解方法</text>
      </view>
      <view class="card-content">
        <view class="ritual-list">
          <text wx:for="{{analysis.rituals}}" wx:key="index" class="ritual-item">
            • {{item}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 占卜过程（可展开） -->
  <view class="calculation-section" wx:if="{{showCalculation}}">
    <view class="section-title" bindtap="toggleCalculation">
      <text>🔍 占卜过程</text>
      <text class="toggle-icon">{{showCalculationDetail ? '▼' : '▶'}}</text>
    </view>
    
    <view class="calculation-detail" wx:if="{{showCalculationDetail}}">
      <!-- 智能问题分析信息 -->
      <view wx:if="{{result.intelligentAnalysis}}" class="intelligent-analysis">
        <text class="calc-subtitle">🤖 智能问题分析</text>
        <text class="calc-step">问题类型：{{result.intelligentAnalysis.questionType}}</text>
        <text class="calc-step">分析置信度：{{result.intelligentAnalysis.confidence * 100}}%</text>
        <text class="calc-step">情感倾向：{{result.intelligentAnalysis.emotion.dominant}} ({{result.intelligentAnalysis.emotion.confidence * 100}}%)</text>
        <text class="calc-step">时间语境：{{result.intelligentAnalysis.timeContext.timeFrame}} ({{result.intelligentAnalysis.timeContext.confidence * 100}}%)</text>
        <text wx:if="{{result.intelligentAnalysis.keywords.length > 0}}" class="calc-step">关键词：{{result.intelligentAnalysis.keywords.join(', ')}}</text>
        <text class="calc-divider">--- 占卜计算 ---</text>
      </view>

      <view wx:if="{{result.calculation.method === 'time'}}" class="time-calculation">
        <text class="calc-title">李淳风六壬时课占卜过程：</text>
        <text class="calc-subtitle">📅 时间信息</text>
        <text class="calc-step">公历时间：{{result.calculation.solarTime}}</text>
        <text class="calc-step">真太阳时：{{result.calculation.trueSolarTime}}</text>
        <text class="calc-step">农历时间：{{result.calculation.lunarTime}}</text>
        <text class="calc-step">当前时辰：{{result.calculation.shichen}}</text>
        <text class="calc-subtitle">🔮 三步推演法</text>
        <text class="calc-step">第一步：月份落位 - 农历{{result.calculation.lunarMonth}}月 → 第{{result.calculation.monthPosition}}位</text>
        <text class="calc-step">第二步：日辰落位 - 农历{{result.calculation.lunarDay}}日 → 第{{result.calculation.dayPosition}}位</text>
        <text class="calc-step">第三步：时辰落位 - {{result.calculation.shichen}} → 第{{result.calculation.finalPosition}}位</text>
        <text class="calc-note">💡 六神顺序：大安(1) → 留连(2) → 速喜(3) → 赤口(4) → 小吉(5) → 空亡(6)</text>
        <text class="calc-result">最终结果：第{{result.calculation.finalPosition}}位 → {{result.god.name}}</text>
        <view wx:if="{{analysis.overall.confidence}}" class="confidence-info">
          <text class="confidence-label">🎯 分析置信度：</text>
          <text class="confidence-value">{{analysis.overall.confidence}}%</text>
          <text class="confidence-desc">{{analysis.overall.confidence >= 80 ? '高置信度' : analysis.overall.confidence >= 60 ? '中等置信度' : '低置信度'}}</text>
        </view>
        <text wx:if="{{result.calculation.approximate}}" class="calc-warning">⚠️ 农历时间为近似值，建议使用专业农历查询</text>
      </view>
      
      <view wx:if="{{result.calculation.method === 'number'}}" class="number-calculation">
        <text class="calc-title">数字占卜过程：</text>
        <text class="calc-step">输入数字：{{result.calculation.numbers.join(' + ')}}</text>
        <text class="calc-step">数字之和：{{result.calculation.sum}}</text>
        <text class="calc-step">位置计算：(({{result.calculation.sum}} - 1) ÷ 6) 取余 + 1 = {{result.calculation.position}}</text>
        <text class="calc-step">六神位置：第{{result.calculation.position}}位 → {{result.god.name}}</text>
        <text class="calc-note">💡 六神顺序：大安(1) → 留连(2) → 速喜(3) → 赤口(4) → 小吉(5) → 空亡(6)</text>
        <text class="calc-result">最终结果：{{result.god.name}}</text>
        <view wx:if="{{analysis.overall.confidence}}" class="confidence-info">
          <text class="confidence-label">🎯 分析置信度：</text>
          <text class="confidence-value">{{analysis.overall.confidence}}%</text>
          <text class="confidence-desc">{{analysis.overall.confidence >= 80 ? '高置信度' : analysis.overall.confidence >= 60 ? '中等置信度' : '低置信度'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-btn tertiary" bindtap="goHome">
        <text>返回首页</text>
      </button>
      <button class="action-btn secondary" bindtap="shareResult">
        <text>分享结果</text>
      </button>
      <button class="action-btn primary" bindtap="newDivination">
        <text>再次占卜</text>
      </button>
    </view>
    
    <view class="divination-note">
      <text>💡 同一问题一日内不宜重复占卜</text>
    </view>
  </view>

  <!-- 分享遮罩 -->
  <view class="share-overlay" wx:if="{{showShareModal}}" bindtap="hideShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text>分享占卜结果</text>
        <text class="close-btn" bindtap="hideShareModal">✕</text>
      </view>
      <view class="share-options">
        <view class="share-option" bindtap="shareToFriend">
          <text class="share-icon">👥</text>
          <text>分享给朋友</text>
        </view>
        <view class="share-option" bindtap="saveToAlbum">
          <text class="share-icon">📱</text>
          <text>保存到相册</text>
        </view>
      </view>
    </view>
  </view>
  </view>
</scroll-view>

<!-- 隐藏的Canvas用于海报生成 -->
<canvas id="poster-canvas" type="2d" style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"></canvas>
