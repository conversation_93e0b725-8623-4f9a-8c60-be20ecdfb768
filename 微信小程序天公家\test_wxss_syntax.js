// test_wxss_syntax.js
// 测试WXSS文件语法是否正确

const fs = require('fs');

console.log('🧪 开始测试WXSS文件语法...');

try {
  // 读取WXSS文件
  const wxssContent = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
  
  console.log(`📄 文件大小: ${wxssContent.length} 字符`);
  console.log(`📄 行数: ${wxssContent.split('\n').length}`);
  
  // 检查常见的语法问题
  const lines = wxssContent.split('\n');
  let hasErrors = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 检查通配符选择器
    if (line.includes('*') && !line.trim().startsWith('/*') && !line.trim().startsWith('*') && !line.includes('*/')) {
      // 检查是否是CSS选择器中的通配符
      const trimmed = line.trim();
      if (trimmed.match(/^\s*\*\s*\{/) || trimmed.match(/[^\/]\*\s*\{/)) {
        console.error(`❌ 第${lineNum}行发现通配符选择器: ${line.trim()}`);
        hasErrors = true;
      }
    }
    
    // 检查括号匹配
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    
    // 检查特殊字符
    for (let j = 0; j < line.length; j++) {
      const char = line[j];
      const charCode = char.charCodeAt(0);
      
      // 检查不可见字符（除了常见的空白字符）
      if (charCode < 32 && charCode !== 9 && charCode !== 10 && charCode !== 13) {
        console.error(`❌ 第${lineNum}行第${j+1}个字符发现不可见字符: ASCII ${charCode}`);
        hasErrors = true;
      }
    }
  }
  
  // 检查整体括号匹配
  const totalOpenBraces = (wxssContent.match(/\{/g) || []).length;
  const totalCloseBraces = (wxssContent.match(/\}/g) || []).length;
  
  if (totalOpenBraces !== totalCloseBraces) {
    console.error(`❌ 括号不匹配: 开括号${totalOpenBraces}个，闭括号${totalCloseBraces}个`);
    hasErrors = true;
  }
  
  if (!hasErrors) {
    console.log('✅ WXSS文件语法检查通过');
    console.log(`✅ 括号匹配正确: ${totalOpenBraces}对`);
  } else {
    console.log('❌ WXSS文件存在语法错误');
  }
  
} catch (error) {
  console.error('❌ 读取WXSS文件失败:', error.message);
}

console.log('🏁 WXSS语法测试完成');
