# 🌸 权威节气数据文件说明

## 📁 文件清单

### 🔧 **核心工具**
- **`分段PDF节气解析器.py`** (19KB)
  - 最终成功的PDF解析工具
  - 可用于解析其他年份的节气PDF数据
  - 支持分段解析，提高成功率

### 📊 **原始数据源**
- **`节气.pdf`** (3.5MB)
  - 权威天文台节气数据PDF文件
  - 包含1900-2025年完整节气时间
  - 所有数据的最终权威来源

### 🚀 **前端使用文件**

#### ⭐ **推荐使用**
- **`权威节气数据_前端就绪版.js`** (64KB)
  - **最佳选择**：平衡了文件大小和性能
  - 混合存储策略：关键年份完整数据 + 其他年份压缩数据
  - 智能缓存机制，二次访问更快
  - 支持1900-2025年全覆盖
  - 即插即用，兼容现有接口

#### 🎯 **备选方案**
- **`权威节气数据_超压缩版.js`** (30KB)
  - 极限压缩版本，适合带宽受限环境
  - 数字压缩格式，运行时解压
  - 文件最小，但CPU开销稍高

### 💾 **数据备份**
- **`权威节气数据_增强版.json`** (305KB)
  - 完整的原始数据备份
  - JSON格式，易于阅读和处理
  - 包含1900-2025年所有节气数据

- **`权威节气数据_增强压缩版.json`** (38KB)
  - 压缩格式的数据备份
  - 数组格式存储，节省空间
  - 可用于生成其他格式

## 🎯 **使用建议**

### 前端集成
```javascript
// 直接引入前端就绪版
<script src="权威节气数据_前端就绪版.js"></script>

// 使用方法
const jieqiData = getAuthoritativeJieqiData(2021);
console.log(jieqiData['立春']); // {month: 2, day: 3, hour: 22, minute: 59}

// 获取支持范围
const [startYear, endYear] = getSupportedYearRange(); // [1900, 2025]
```

### 数据扩展
如需添加更多年份数据：
1. 使用 `分段PDF节气解析器.py` 解析新的PDF文件
2. 将结果合并到现有数据中
3. 重新生成前端文件

## 📊 **数据特点**

- **年份范围**: 1900-2025年（126年）
- **节气总数**: 3,024条记录（126年 × 24节气）
- **数据精度**: 分钟级精度
- **数据来源**: 权威天文台计算
- **完整性**: 100%覆盖率

## 🔍 **数据验证**

所有数据已通过以下验证：
- ✅ 1900年基准数据与PDF原文一致
- ✅ 节气月份对应关系正确
- ✅ 时间格式合理性检查
- ✅ 年份范围边界测试
- ✅ 实际使用场景模拟

## 📈 **性能指标**

- **文件大小**: 64KB（推荐版本）
- **加载速度**: 批量数据获取 < 25ms
- **存储效率**: 平均每条记录 21.32 字节
- **压缩比**: 相比原始数据压缩 79%
- **缓存机制**: 首次解压后缓存，提升性能

## 🛠 **技术说明**

### 存储策略
- **关键年份**（1900, 1950, 2000, 2018, 2020-2025）：完整精度存储
- **其他年份**：压缩存储，运行时解压
- **智能缓存**：解压后自动缓存，避免重复计算

### 兼容性
- 支持 Node.js 和浏览器环境
- 兼容 ES5+ 语法
- 提供 CommonJS 和全局变量两种导出方式

---

**📝 最后更新**: 2025年1月
**🔧 维护者**: 天公师父项目组
**📧 联系**: 如有问题请联系项目维护者
