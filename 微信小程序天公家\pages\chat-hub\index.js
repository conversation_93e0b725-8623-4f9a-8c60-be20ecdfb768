// pages/chat-hub/index.js
const app = getApp();

Page({
  data: {
    currentDate: '',
    currentTime: '',
    greeting: '欢迎来到天公师兄占卜'
  },

  onLoad() {
    console.log('占卜中心页面加载');
    this.setCurrentDate();
  },

  onShow() {
    console.log('占卜中心页面显示');
  },

  // 设置当前日期和时间
  setCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    this.setData({
      currentDate: `${year}-${month}-${day}`,
      currentTime: `${hour}:${minute}`
    });
  },

  // 开始占卜
  startDivination() {
    wx.navigateTo({
      url: '/pages/divination-input/index',
      success: () => {
        console.log('成功跳转到占卜输入页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '天公师兄占卜',
      path: '/pages/chat-hub/index'
    };
  }
});
    currentRole: 'student',  // 当前选中的角色
    currentIndex: 0,  // 当前swiper索引
    inputValue: '',  // 输入框内容
    sending: false,  // 是否正在发送消息
    currentDate: '',  // 当前日期
    showSwipeHint: false,  // 是否显示滑动提示
    vertical: true,  // 设置swiper为垂直方向

    // 角色特定的颜色
    roleColors: {
      student: '#3b82f6',
      parent: '#f59e0b',
      teacher: '#05cb6d',
      doctor: '#0ea5e9'
    },

    // 角色特定的文本
    roleTexts: {
      student: {
        title: '学生',
        placeholder: '输入你的回答...'
      },
      parent: {
        title: '家长',
        placeholder: '输入您的回答...'
      },
      teacher: {
        title: '教师',
        placeholder: '输入您的回答...'
      },
      doctor: {
        title: '医生',
        placeholder: '输入您的专业评估...'
      }
    },

    // 学生对话数据
    studentMessages: [],
    studentTyping: false,
    studentScrollId: '',
    studentQuestionsIndex: 0,
    studentQuestions: [],

    // 家长对话数据
    parentMessages: [],
    parentTyping: false,
    parentScrollId: '',
    parentQuestionsIndex: 0,
    parentQuestions: [],

    // 教师对话数据
    teacherMessages: [],
    teacherTyping: false,
    teacherScrollId: '',
    teacherQuestionsIndex: 0,
    teacherQuestions: [],

    // 医生对话数据
    doctorMessages: [],
    doctorTyping: false,
    doctorScrollId: '',
    doctorQuestionsIndex: 0,
    doctorQuestions: []
  },

  onLoad() {
    // 设置当前日期
    this.setCurrentDate();

    // 初始化所有角色的评估问题
    this.initAllQuestions();

    // 检查是否有指定的角色
    const role = app.globalData.initialRole || 'student';
    const roleToIndex = {
      student: 0,
      parent: 1,
      teacher: 2,
      doctor: 3
    };

    if (role && roleToIndex[role] !== undefined) {
      this.setData({
        currentRole: role,
        currentIndex: roleToIndex[role]
      });
    }

    // 显示滑动提示
    this.showSwipeHintWithDelay();
  },

  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

    const weekday = weekdays[now.getDay()];
    const month = months[now.getMonth()];
    const day = now.getDate();

    const formattedDate = `${weekday}，${month} ${day}`;
    this.setData({
      currentDate: formattedDate.toUpperCase()
    });
  },

  // 初始化所有角色的评估问题
  initAllQuestions() {
    this.initStudentQuestions();
    this.initParentQuestions();
    this.initTeacherQuestions();
    this.initDoctorQuestions();
  },

  // 初始化学生评估问题
  initStudentQuestions() {
    const questions = [
      {
        id: "academic_performance",
        text: "你对自己最近的学习成绩满意吗？有哪些科目表现得特别好或特别差？",
        dimension: "academic",
        followUp: "你认为影响你学习表现的主要因素是什么？"
      },
      {
        id: "study_habits",
        text: "你通常如何安排自己的学习时间？课后会花多少时间学习和做作业？",
        dimension: "learning",
        followUp: "你觉得这种学习方式对你有效吗？为什么？"
      },
      // 更多问题...
    ];
    this.setData({ studentQuestions: questions });
  },

  // 初始化家长评估问题
  initParentQuestions() {
    const questions = [
      {
        id: "child_behavior",
        text: "您的孩子在家中的行为表现如何？是否有明显的情绪波动或行为变化？",
        dimension: "behavior",
        followUp: "这些行为变化是什么时候开始的？有什么可能的诱因吗？"
      },
      {
        id: "academic_attitude",
        text: "孩子对待学习的态度怎样？是否主动完成作业和学习任务？",
        dimension: "academic",
        followUp: "您平时如何帮助孩子处理学习任务？"
      },
      // 更多问题...
    ];
    this.setData({ parentQuestions: questions });
  },

  // 初始化教师评估问题
  initTeacherQuestions() {
    const questions = [
      {
        id: "academic_performance",
        text: "这位学生的学习成绩如何？在您的课堂上表现得怎么样？",
        dimension: "academic",
        followUp: "学生的优势和薄弱学科分别是什么？"
      },
      {
        id: "classroom_behavior",
        text: "学生在课堂上的行为表现如何？是否积极参与课堂活动和讨论？",
        dimension: "behavior",
        followUp: "学生在不同类型的课堂活动中表现有何不同？"
      },
      // 更多问题...
    ];
    this.setData({ teacherQuestions: questions });
  },

  // 初始化医生评估问题
  initDoctorQuestions() {
    const questions = [
      {
        id: "basic_info",
        text: "请简单描述一下孩子的基本情况，包括年龄、性别和主要咨询原因。",
        dimension: "info",
        followUp: "孩子目前是否正在接受其他治疗或干预？"
      },
      {
        id: "development_history",
        text: "孩子的发育史如何？是否有任何发育延迟或早期的健康问题？",
        dimension: "development",
        followUp: "有没有家族病史需要注意的？"
      },
      // 更多问题...
    ];
    this.setData({ doctorQuestions: questions });
  },

  // 滑动切换角色（改为垂直方向）
  onSwiperChange(e) {
    const index = e.detail.current;
    const roles = ['student', 'parent', 'teacher', 'doctor'];

    if (index >= 0 && index < roles.length) {
      const newRole = roles[index];

      this.setData({
        currentRole: newRole,
        currentIndex: index
      });

      // 如果切换了角色，显示滑动提示
      if (this.data.currentRole !== newRole) {
        this.showSwipeHintWithDelay();
      }
    }
  },

  // 点击角色标签切换
  switchRole(e) {
    const { role, index } = e.currentTarget.dataset;

    this.setData({
      currentRole: role,
      currentIndex: parseInt(index)
    });
  },

  // 延迟显示滑动提示（更新提示内容为上下滑动）
  showSwipeHintWithDelay() {
    this.setData({ showSwipeHint: true });

    setTimeout(() => {
      this.setData({ showSwipeHint: false });
    }, 3000);
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  sendMessage() {
    const { inputValue, sending, currentRole } = this.data;

    // 如果正在发送或输入为空，不执行操作
    if (sending || !inputValue.trim()) return;

    // 标记为正在发送
    this.setData({ sending: true });

    // 根据当前角色发送消息
    switch(currentRole) {
      case 'student':
        this.sendStudentMessage(inputValue);
        break;
      case 'parent':
        this.sendParentMessage(inputValue);
        break;
      case 'teacher':
        this.sendTeacherMessage(inputValue);
        break;
      case 'doctor':
        this.sendDoctorMessage(inputValue);
        break;
    }

    // 清空输入框
    this.setData({ inputValue: '' });

    // 重置发送状态
    setTimeout(() => {
      this.setData({ sending: false });
    }, 300);
  },

  // 发送学生消息
  sendStudentMessage(content) {
    const { studentMessages } = this.data;
    const messageId = Date.now();

    // 创建用户消息
    const userMessage = {
      id: messageId,
      type: 'user',
      content,
      time: this.getFormatTime()
    };

    // 添加到消息列表
    studentMessages.push(userMessage);

    this.setData({
      studentMessages,
      studentScrollId: `student-msg-${messageId}`
    });

    // 模拟AI回复
    this.simulateStudentAIResponse();
  },

  // 发送家长消息
  sendParentMessage(content) {
    const { parentMessages } = this.data;
    const messageId = Date.now();

    // 创建用户消息
    const userMessage = {
      id: messageId,
      type: 'user',
      content,
      time: this.getFormatTime()
    };

    // 添加到消息列表
    parentMessages.push(userMessage);

    this.setData({
      parentMessages,
      parentScrollId: `parent-msg-${messageId}`
    });

    // 模拟AI回复
    this.simulateParentAIResponse();
  },

  // 发送教师消息
  sendTeacherMessage(content) {
    const { teacherMessages } = this.data;
    const messageId = Date.now();

    // 创建用户消息
    const userMessage = {
      id: messageId,
      type: 'user',
      content,
      time: this.getFormatTime()
    };

    // 添加到消息列表
    teacherMessages.push(userMessage);

    this.setData({
      teacherMessages,
      teacherScrollId: `teacher-msg-${messageId}`
    });

    // 模拟AI回复
    this.simulateTeacherAIResponse();
  },

  // 发送医生消息
  sendDoctorMessage(content) {
    const { doctorMessages } = this.data;
    const messageId = Date.now();

    // 创建用户消息
    const userMessage = {
      id: messageId,
      type: 'user',
      content,
      time: this.getFormatTime()
    };

    // 添加到消息列表
    doctorMessages.push(userMessage);

    this.setData({
      doctorMessages,
      doctorScrollId: `doctor-msg-${messageId}`
    });

    // 模拟AI回复
    this.simulateDoctorAIResponse();
  },

  // 模拟学生AI回复
  simulateStudentAIResponse() {
    const { studentQuestionsIndex, studentQuestions } = this.data;

    // 显示正在输入状态
    this.setData({ studentTyping: true });

    setTimeout(() => {
      // 如果还有问题要问
      if (studentQuestionsIndex < studentQuestions.length) {
        const nextQuestion = studentQuestions[studentQuestionsIndex];
        const messageId = Date.now();

        // 创建AI消息
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: nextQuestion.text,
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.studentMessages, aiMessage];

        this.setData({
          studentMessages: newMessages,
          studentTyping: false,
          studentQuestionsIndex: studentQuestionsIndex + 1,
          studentScrollId: `student-msg-${messageId}`
        });
      } else {
        // 评估完成
        const messageId = Date.now();
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: "感谢你的回答！我已完成评估，正在生成报告...",
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.studentMessages, aiMessage];

        this.setData({
          studentMessages: newMessages,
          studentTyping: false,
          studentScrollId: `student-msg-${messageId}`
        });

        // 生成结果报告
        setTimeout(() => this.generateStudentReport(), 2000);
      }
    }, 1000);
  },

  // 模拟家长AI回复
  simulateParentAIResponse() {
    const { parentQuestionsIndex, parentQuestions } = this.data;

    // 显示正在输入状态
    this.setData({ parentTyping: true });

    setTimeout(() => {
      // 如果还有问题要问
      if (parentQuestionsIndex < parentQuestions.length) {
        const nextQuestion = parentQuestions[parentQuestionsIndex];
        const messageId = Date.now();

        // 创建AI消息
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: nextQuestion.text,
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.parentMessages, aiMessage];

        this.setData({
          parentMessages: newMessages,
          parentTyping: false,
          parentQuestionsIndex: parentQuestionsIndex + 1,
          parentScrollId: `parent-msg-${messageId}`
        });
      } else {
        // 评估完成
        const messageId = Date.now();
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: "感谢您的回答！我已完成评估，正在生成报告...",
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.parentMessages, aiMessage];

        this.setData({
          parentMessages: newMessages,
          parentTyping: false,
          parentScrollId: `parent-msg-${messageId}`
        });

        // 生成结果报告
        setTimeout(() => this.generateParentReport(), 2000);
      }
    }, 1000);
  },

  // 模拟教师AI回复
  simulateTeacherAIResponse() {
    const { teacherQuestionsIndex, teacherQuestions } = this.data;

    // 显示正在输入状态
    this.setData({ teacherTyping: true });

    setTimeout(() => {
      // 如果还有问题要问
      if (teacherQuestionsIndex < teacherQuestions.length) {
        const nextQuestion = teacherQuestions[teacherQuestionsIndex];
        const messageId = Date.now();

        // 创建AI消息
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: nextQuestion.text,
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.teacherMessages, aiMessage];

        this.setData({
          teacherMessages: newMessages,
          teacherTyping: false,
          teacherQuestionsIndex: teacherQuestionsIndex + 1,
          teacherScrollId: `teacher-msg-${messageId}`
        });
      } else {
        // 评估完成
        const messageId = Date.now();
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: "感谢您的回答！我已完成评估，正在生成报告...",
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.teacherMessages, aiMessage];

        this.setData({
          teacherMessages: newMessages,
          teacherTyping: false,
          teacherScrollId: `teacher-msg-${messageId}`
        });

        // 生成结果报告
        setTimeout(() => this.generateTeacherReport(), 2000);
      }
    }, 1000);
  },

  // 模拟医生AI回复
  simulateDoctorAIResponse() {
    const { doctorQuestionsIndex, doctorQuestions } = this.data;

    // 显示正在输入状态
    this.setData({ doctorTyping: true });

    setTimeout(() => {
      // 如果还有问题要问
      if (doctorQuestionsIndex < doctorQuestions.length) {
        const nextQuestion = doctorQuestions[doctorQuestionsIndex];
        const messageId = Date.now();

        // 创建AI消息
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: nextQuestion.text,
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.doctorMessages, aiMessage];

        this.setData({
          doctorMessages: newMessages,
          doctorTyping: false,
          doctorQuestionsIndex: doctorQuestionsIndex + 1,
          doctorScrollId: `doctor-msg-${messageId}`
        });
      } else {
        // 评估完成
        const messageId = Date.now();
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: "感谢您的专业回答！我已完成评估，正在生成医学报告...",
          time: this.getFormatTime()
        };

        const newMessages = [...this.data.doctorMessages, aiMessage];

        this.setData({
          doctorMessages: newMessages,
          doctorTyping: false,
          doctorScrollId: `doctor-msg-${messageId}`
        });

        // 生成结果报告
        setTimeout(() => this.generateDoctorReport(), 2000);
      }
    }, 1000);
  },

  // 生成学生评估报告
  generateStudentReport() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "✅ 学生评估完成!\n\n📊 总体表现评分: 85分\n🔍 详细分析:\n• 学业表现: 82分 🟢\n• 学习习惯: 75分 🟢\n• 注意力集中: 70分 🟡\n• 同伴关系: 90分 🟢\n• 压力水平: 65分 🟡\n\n💡 整体评价:\n你的学习状态良好，继续保持这种积极的态度！建议适当关注压力管理和注意力训练。",
      time: this.getFormatTime()
    };

    const newMessages = [...this.data.studentMessages, aiMessage];

    this.setData({
      studentMessages: newMessages,
      studentScrollId: `student-msg-${messageId}`
    });
  },

  // 生成家长评估报告
  generateParentReport() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "✅ 家长评估完成!\n\n📊 儿童整体状况评分: 80分\n🔍 详细分析:\n• 行为表现: 75分 🟢\n• 学习态度: 82分 🟢\n• 亲子沟通: 85分 🟢\n• 家庭关系: 90分 🟢\n• 电子设备使用: 65分 🟡\n\n💡 整体评价:\n孩子的整体情况良好，家庭环境支持得当。建议关注孩子的电子设备使用情况，可以设定合理的使用时间和内容限制。",
      time: this.getFormatTime()
    };

    const newMessages = [...this.data.parentMessages, aiMessage];

    this.setData({
      parentMessages: newMessages,
      parentScrollId: `parent-msg-${messageId}`
    });
  },

  // 生成教师评估报告
  generateTeacherReport() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "✅ 教师评估完成!\n\n📊 学生整体状况评分: 78分\n🔍 详细分析:\n• 学业表现: 80分 🟢\n• 课堂行为: 75分 🟢\n• 注意力集中: 68分 🟡\n• 同伴关系: 85分 🟢\n• 师生互动: 90分 🟢\n\n💡 整体评价:\n该学生整体表现良好，在学校环境中适应情况佳。建议关注其注意力集中问题，可尝试多样化的教学方法提高课堂参与度。",
      time: this.getFormatTime()
    };

    const newMessages = [...this.data.teacherMessages, aiMessage];

    this.setData({
      teacherMessages: newMessages,
      teacherScrollId: `teacher-msg-${messageId}`
    });
  },

  // 生成医生评估报告
  generateDoctorReport() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "✅ 医学评估完成!\n\n📊 儿童发展状况评分: 82分\n🔍 临床分析:\n• 身体发育: 90分 🟢\n• 认知发展: 85分 🟢\n• 情绪调节: 75分 🟡\n• 行为控制: 70分 🟡\n• 社交能力: 88分 🟢\n\n💡 医学建议:\n该儿童整体发展情况良好，建议关注情绪调节和行为控制方面的能力培养。可考虑适当的心理咨询或家庭指导，以提供更有针对性的支持。",
      time: this.getFormatTime()
    };

    const newMessages = [...this.data.doctorMessages, aiMessage];

    this.setData({
      doctorMessages: newMessages,
      doctorScrollId: `doctor-msg-${messageId}`
    });
  },

  // 获取格式化的当前时间
  getFormatTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 开始学生评估
  startStudentAssessment() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "你好！我是你的学生评估助手。请告诉我你最近在学校的感受和学习情况？",
      time: this.getFormatTime()
    };

    this.setData({
      studentMessages: [aiMessage],
      studentScrollId: `student-msg-${messageId}`
    });
  },

  // 开始家长评估
  startParentAssessment() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "您好！我是家长评估助手。请告诉我您孩子的年龄和最近在家中的表现情况？",
      time: this.getFormatTime()
    };

    this.setData({
      parentMessages: [aiMessage],
      parentScrollId: `parent-msg-${messageId}`
    });
  },

  // 开始教师评估
  startTeacherAssessment() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "您好！我是教师评估助手。请告诉我您所教学生的班级和年龄段，以及您想评估的学生情况？",
      time: this.getFormatTime()
    };

    this.setData({
      teacherMessages: [aiMessage],
      teacherScrollId: `teacher-msg-${messageId}`
    });
  },

  // 开始医生评估
  startDoctorAssessment() {
    const messageId = Date.now();
    const aiMessage = {
      id: messageId,
      type: 'ai',
      content: "您好！我是医学评估助手。请简单描述一下您需要评估的儿童情况和主要咨询原因？",
      time: this.getFormatTime()
    };

    this.setData({
      doctorMessages: [aiMessage],
      doctorScrollId: `doctor-msg-${messageId}`
    });
  },

  // 选择选项
  selectOption(e) {
    const { role, option } = e.currentTarget.dataset;

    // 根据角色处理选项选择
    switch(role) {
      case 'student':
        this.sendStudentMessage(option);
        break;
      case 'parent':
        this.sendParentMessage(option);
        break;
      case 'teacher':
        this.sendTeacherMessage(option);
        break;
      case 'doctor':
        this.sendDoctorMessage(option);
        break;
    }
  },

  // 激活语音输入
  activateVoiceInput() {
    // 调用微信的录音接口
    wx.showToast({
      title: '语音输入暂未开启',
      icon: 'none'
    });
  },

  // 导航到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '儿童心理评估小助手',
      path: '/pages/chat-hub/index'
    };
  }
});