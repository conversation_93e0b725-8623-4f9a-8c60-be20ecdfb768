// test_data_conversion.js
// 测试数据格式转换功能

console.log('🧪 测试数据格式转换功能...');

// 模拟前端计算结果（真实格式）
const mockFrontendResult = {
  bazi: {
    year: { gan: '甲', zhi: '子' },
    month: { gan: '丙', zhi: '寅' },
    day: { gan: '戊', zhi: '午' },
    hour: { gan: '庚', zhi: '申' }
  },
  formatted: {
    year: '甲子',
    month: '丙寅',
    day: '戊午',
    hour: '庚申'
  },
  nayin: {
    year: '海中金',
    month: '炉中火',
    day: '天上火',
    hour: '石榴木'
  },
  five_elements: {
    wood: 2,
    fire: 3,
    earth: 2,
    metal: 1,
    water: 0
  },
  source: 'frontend_calculation',
  timestamp: new Date().toISOString()
};

// 模拟出生信息
const mockBirthInfo = {
  name: '张三',
  gender: '男',
  year: 1990,
  month: 7,
  day: 21,
  hour: 18,
  minute: 2,
  birthCity: '北京'
};

// 模拟转换函数
function convertFrontendDataToDisplayFormat(frontendResult, birthInfo, analysisMode) {
  console.log('🔄 开始数据格式转换...');
  
  // 辅助函数：格式化时间
  function formatTime(hour, minute) {
    const h = parseInt(hour) || 0;
    const m = parseInt(minute) || 0;
    const period = h < 12 ? '上午' : '下午';
    const displayHour = h === 0 ? 12 : (h > 12 ? h - 12 : h);
    return `${period}${displayHour}:${m.toString().padStart(2, '0')}`;
  }

  // 辅助函数：获取生肖
  function getZodiac(year) {
    const zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
    return zodiacs[(year - 4) % 12];
  }
  
  // 转换出生信息格式
  const convertedUserInfo = {
    name: birthInfo.name || '用户',
    gender: birthInfo.gender || '未知',
    birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
    birthTime: formatTime(birthInfo.hour, birthInfo.minute),
    location: birthInfo.birthCity || birthInfo.location || '未知',
    zodiac: getZodiac(birthInfo.year),
    solar_time: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日 ${formatTime(birthInfo.hour, birthInfo.minute)}`,
    lunar_time: frontendResult.lunar_date || '未知',
    true_solar_time: formatTime(birthInfo.hour, birthInfo.minute),
    longitude: '未知',
    latitude: '未知',
    timezone: 'UTC+8',
    solar_term: frontendResult.solar_term || '未知'
  };
  
  // 转换八字信息格式
  const convertedBaziInfo = {
    yearPillar: {
      heavenly: frontendResult.bazi?.year?.gan || '甲',
      earthly: frontendResult.bazi?.year?.zhi || '子',
      nayin: frontendResult.nayin?.year || '海中金'
    },
    monthPillar: {
      heavenly: frontendResult.bazi?.month?.gan || '丙',
      earthly: frontendResult.bazi?.month?.zhi || '寅',
      nayin: frontendResult.nayin?.month || '炉中火'
    },
    dayPillar: {
      heavenly: frontendResult.bazi?.day?.gan || '戊',
      earthly: frontendResult.bazi?.day?.zhi || '午',
      nayin: frontendResult.nayin?.day || '天上火'
    },
    timePillar: {
      heavenly: frontendResult.bazi?.hour?.gan || '庚',
      earthly: frontendResult.bazi?.hour?.zhi || '申',
      nayin: frontendResult.nayin?.hour || '石榴木'
    }
  };
  
  // 转换五行信息
  const convertedFiveElements = {
    wood: frontendResult.five_elements?.wood || 0,
    fire: frontendResult.five_elements?.fire || 0,
    earth: frontendResult.five_elements?.earth || 0,
    metal: frontendResult.five_elements?.metal || 0,
    water: frontendResult.five_elements?.water || 0
  };
  
  // 组装完整数据
  const convertedData = {
    userInfo: convertedUserInfo,
    baziInfo: convertedBaziInfo,
    fiveElements: convertedFiveElements,
    analysisMode: analysisMode,
    dataSource: 'converted_frontend_result',
    originalData: frontendResult,
    conversionTimestamp: new Date().toISOString()
  };
  
  console.log('✅ 数据格式转换完成');
  return convertedData;
}

// 执行测试
console.log('\n📊 原始前端数据:');
console.log(JSON.stringify(mockFrontendResult, null, 2));

console.log('\n👤 原始出生信息:');
console.log(JSON.stringify(mockBirthInfo, null, 2));

const convertedData = convertFrontendDataToDisplayFormat(mockFrontendResult, mockBirthInfo, 'comprehensive');

console.log('\n✅ 转换后的显示数据:');
console.log(JSON.stringify(convertedData, null, 2));

// 验证转换结果
console.log('\n🔍 转换结果验证:');
console.log('✅ 用户信息格式:', convertedData.userInfo.birthDate, convertedData.userInfo.birthTime);
console.log('✅ 八字格式:', 
  convertedData.baziInfo.yearPillar.heavenly + convertedData.baziInfo.yearPillar.earthly,
  convertedData.baziInfo.monthPillar.heavenly + convertedData.baziInfo.monthPillar.earthly,
  convertedData.baziInfo.dayPillar.heavenly + convertedData.baziInfo.dayPillar.earthly,
  convertedData.baziInfo.timePillar.heavenly + convertedData.baziInfo.timePillar.earthly
);
console.log('✅ 五行数据:', convertedData.fiveElements);
console.log('✅ 数据来源:', convertedData.dataSource);

console.log('\n🎯 修复效果预期:');
console.log('- 前端计算的真实八字数据能正确显示');
console.log('- 不再显示测试用户的模板数据');
console.log('- 数据格式完全匹配结果页面期望');
console.log('- 用户看到的是自己真实的八字分析');

console.log('\n🏁 数据格式转换测试完成');
