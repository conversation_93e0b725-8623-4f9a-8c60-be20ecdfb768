/* components/conversational-option/index.wxss */
.option-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  opacity: 0;
  transform: translateY(20rpx);
}

.option-item.selected {
  background-color: #e8f5e9;
  border-color: #4CAF50;
  box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.15);
}

.option-label {
  width: 40rpx;
  height: 40rpx;
  background-color: #eee;
  color: #666;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background-color: #4CAF50;
  color: white;
}

.option-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.emoji {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

.selection-indicator {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 16rpx;
}

.checkmark {
  width: 22rpx;
  height: 22rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.checkmark::after {
  content: '';
  width: 10rpx;
  height: 6rpx;
  border-left: 2rpx solid white;
  border-bottom: 2rpx solid white;
  transform: rotate(-45deg) translate(0, -1rpx);
}

/* Grade-specific styles */
.option-item.elementary {
  border-radius: 16rpx;
  padding: 20rpx 16rpx;
  background-color: #f0f7ff;
}

.option-item.elementary.selected {
  background-color: #e3f2fd;
  border-color: #2196F3;
}

.option-item.elementary.selected .option-label {
  background-color: #2196F3;
}

.option-item.elementary .checkmark {
  background-color: #2196F3;
}

.option-item.junior {
  border-radius: 8rpx;
  background-color: #f9f9f9;
}

.option-item.junior.selected {
  background-color: #e8eaf6;
  border-color: #3f51b5;
}

.option-item.junior.selected .option-label {
  background-color: #3f51b5;
}

.option-item.junior .checkmark {
  background-color: #3f51b5;
}

.option-item.senior {
  border-radius: 6rpx;
  background-color: #fafafa;
}

.option-item.senior.selected {
  background-color: #f3e5f5;
  border-color: #9c27b0;
}

.option-item.senior.selected .option-label {
  background-color: #9c27b0;
}

.option-item.senior .checkmark {
  background-color: #9c27b0;
} 