// utils/intelligent_matcher.js - 智能问题匹配系统
// 用于改进玉匣记占卜的问题理解和内容推荐

class IntelligentMatcher {
  constructor() {
    // 问题类型的语义词典
    this.semanticDictionary = {
      '求财': {
        keywords: ['财运', '赚钱', '收入', '投资', '生意', '经济', '金钱', '财富', '发财', '挣钱', '盈利', '资金', '股票', '理财', '创业'],
        synonyms: ['财', '钱', '富', '利', '益', '收', '入', '得'],
        weight: 1.0
      },
      '感情': {
        keywords: ['爱情', '恋爱', '结婚', '婚姻', '感情', '情侣', '男友', '女友', '老公', '老婆', '夫妻', '相亲', '表白', '分手', '复合', '桃花'],
        synonyms: ['爱', '情', '恋', '婚', '配', '偶', '缘'],
        weight: 1.0
      },
      '出行': {
        keywords: ['出行', '旅游', '出差', '搬家', '迁移', '外出', '远行', '旅行', '出门', '路途', '交通', '安全', '顺利'],
        synonyms: ['行', '游', '移', '迁', '途', '路'],
        weight: 1.0
      },
      '健康': {
        keywords: ['健康', '身体', '疾病', '生病', '医院', '治疗', '康复', '养生', '体检', '病情', '症状', '药物', '手术', '痊愈'],
        synonyms: ['病', '疾', '康', '健', '体', '身'],
        weight: 1.0
      },
      '学习': {
        keywords: ['学习', '考试', '成绩', '升学', '教育', '学校', '老师', '同学', '作业', '课程', '知识', '技能', '培训', '进修'],
        synonyms: ['学', '考', '试', '习', '读', '书'],
        weight: 1.0
      },
      '工作': {
        keywords: ['工作', '职业', '事业', '升职', '跳槽', '面试', '同事', '老板', '公司', '职场', '晋升', '薪水', '辞职', '求职'],
        synonyms: ['职', '业', '事', '工', '作'],
        weight: 1.0
      },
      '家庭': {
        keywords: ['家庭', '父母', '孩子', '子女', '家人', '亲情', '家事', '教育', '成长', '关系', '和睦', '矛盾'],
        synonyms: ['家', '亲', '父', '母', '子', '女', '儿'],
        weight: 1.0
      },
      '天气': {
        keywords: ['天气', '下雨', '晴天', '阴天', '雨', '雪', '风', '雷', '电', '台风', '暴雨', '干旱', '湿度', '温度', '气候', '季节', '春夏秋冬'],
        synonyms: ['雨', '晴', '阴', '雪', '风', '雷', '电', '热', '冷', '湿', '干'],
        weight: 1.0
      },
      'weather': {
        keywords: ['weather', 'rain', 'sunny', 'cloudy', 'snow', 'wind', 'storm', 'temperature', 'climate', '会下雨吗', '天气如何', '下雨', '晴天'],
        synonyms: ['rain', 'sun', 'cloud', 'snow', 'wind', '雨', '晴', '阴'],
        weight: 1.0
      }
    };

    // 情感倾向词典
    this.emotionDictionary = {
      positive: ['好', '顺', '利', '成', '功', '吉', '喜', '乐', '开心', '高兴', '满意', '成功'],
      negative: ['坏', '差', '难', '苦', '失', '败', '凶', '忧', '愁', '担心', '焦虑', '失败'],
      neutral: ['如何', '怎样', '什么', '是否', '能否', '会不会', '可以', '应该']
    };

    // 时间词典
    this.timeDictionary = {
      near: ['今天', '明天', '后天', '这周', '本周', '近期', '最近', '现在', '当前'],
      far: ['明年', '后年', '将来', '未来', '以后', '长远', '今后'],
      past: ['昨天', '前天', '上周', '之前', '过去', '以前', '曾经']
    };

    this.debug = true;
  }

  /**
   * 智能分析问题并匹配最佳类型
   */
  analyzeQuestion(questionText) {
    if (!questionText || typeof questionText !== 'string') {
      return this.getDefaultResult();
    }

    const cleanText = this.preprocessText(questionText);
    
    if (this.debug) {
      console.log('🤖 智能问题分析开始:', cleanText);
    }

    // 多维度分析
    const semanticScores = this.calculateSemanticScores(cleanText);
    const emotionAnalysis = this.analyzeEmotion(cleanText);
    const timeAnalysis = this.analyzeTime(cleanText);
    const contextAnalysis = this.analyzeContext(cleanText);

    // 综合评分
    const finalScores = this.calculateFinalScores(semanticScores, emotionAnalysis, contextAnalysis);
    
    // 选择最佳匹配
    const bestMatch = this.selectBestMatch(finalScores);

    const result = {
      questionType: bestMatch.type,
      confidence: bestMatch.score,
      emotion: emotionAnalysis,
      timeContext: timeAnalysis,
      allScores: finalScores,
      keywords: this.extractKeywords(cleanText),
      searchTerms: this.generateSearchTerms(bestMatch.type, cleanText)
    };

    if (this.debug) {
      console.log('🎯 智能分析结果:', result);
    }

    return result;
  }

  /**
   * 文本预处理
   */
  preprocessText(text) {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 保留中文、英文、数字
      .trim();
  }

  /**
   * 计算语义得分
   */
  calculateSemanticScores(text) {
    const scores = {};

    Object.keys(this.semanticDictionary).forEach(category => {
      const dict = this.semanticDictionary[category];
      let score = 0;

      // 关键词匹配
      dict.keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 2.0; // 完整关键词匹配权重更高
        }
      });

      // 同义词匹配
      dict.synonyms.forEach(synonym => {
        if (text.includes(synonym)) {
          score += 1.0;
        }
      });

      // 模糊匹配
      score += this.fuzzyMatch(text, dict.keywords) * 0.5;

      scores[category] = score * dict.weight;
    });

    return scores;
  }

  /**
   * 模糊匹配
   */
  fuzzyMatch(text, keywords) {
    let fuzzyScore = 0;
    
    keywords.forEach(keyword => {
      // 检查是否包含关键词的部分字符
      for (let i = 0; i < keyword.length - 1; i++) {
        const partial = keyword.substring(i, i + 2);
        if (text.includes(partial)) {
          fuzzyScore += 0.3;
        }
      }
    });

    return Math.min(fuzzyScore, 2.0); // 限制最大模糊匹配得分
  }

  /**
   * 情感分析
   */
  analyzeEmotion(text) {
    const emotions = { positive: 0, negative: 0, neutral: 0 };

    Object.keys(this.emotionDictionary).forEach(emotion => {
      this.emotionDictionary[emotion].forEach(word => {
        if (text.includes(word)) {
          emotions[emotion] += 1;
        }
      });
    });

    const total = emotions.positive + emotions.negative + emotions.neutral;
    if (total === 0) return { dominant: 'neutral', confidence: 0.5 };

    const dominant = Object.keys(emotions).reduce((a, b) => 
      emotions[a] > emotions[b] ? a : b
    );

    return {
      dominant: dominant,
      confidence: emotions[dominant] / total,
      scores: emotions
    };
  }

  /**
   * 时间分析
   */
  analyzeTime(text) {
    const timeScores = { near: 0, far: 0, past: 0 };

    Object.keys(this.timeDictionary).forEach(timeType => {
      this.timeDictionary[timeType].forEach(timeWord => {
        if (text.includes(timeWord)) {
          timeScores[timeType] += 1;
        }
      });
    });

    const total = Object.values(timeScores).reduce((a, b) => a + b, 0);
    if (total === 0) return { timeFrame: 'present', confidence: 0.5 };

    const dominantTime = Object.keys(timeScores).reduce((a, b) => 
      timeScores[a] > timeScores[b] ? a : b
    );

    return {
      timeFrame: dominantTime,
      confidence: timeScores[dominantTime] / total,
      scores: timeScores
    };
  }

  /**
   * 上下文分析
   */
  analyzeContext(text) {
    const contextClues = {
      isQuestion: /[？?]/.test(text) || /如何|怎样|什么|是否|能否|会不会/.test(text),
      isUrgent: /急|紧急|马上|立即|赶紧/.test(text),
      isImportant: /重要|关键|重大|严重/.test(text),
      hasNumbers: /\d+/.test(text),
      length: text.length
    };

    return contextClues;
  }

  /**
   * 计算最终得分
   */
  calculateFinalScores(semanticScores, emotionAnalysis, contextAnalysis) {
    const finalScores = { ...semanticScores };

    // 根据情感调整得分
    Object.keys(finalScores).forEach(category => {
      if (emotionAnalysis.dominant === 'positive') {
        finalScores[category] *= 1.1; // 积极情感略微加分
      } else if (emotionAnalysis.dominant === 'negative') {
        finalScores[category] *= 1.2; // 消极情感更需要关注
      }
    });

    // 根据上下文调整得分
    if (contextAnalysis.isUrgent) {
      Object.keys(finalScores).forEach(category => {
        finalScores[category] *= 1.15;
      });
    }

    return finalScores;
  }

  /**
   * 选择最佳匹配
   */
  selectBestMatch(scores) {
    const entries = Object.entries(scores);
    
    if (entries.length === 0) {
      return { type: 'other', score: 0.1 };
    }

    // 找到最高分
    const [bestType, bestScore] = entries.reduce((a, b) => a[1] > b[1] ? a : b);

    // 如果最高分太低，返回通用类型
    if (bestScore < 0.5) {
      return { type: 'other', score: 0.3 };
    }

    // 计算置信度（0-1之间）
    const totalScore = entries.reduce((sum, [, score]) => sum + score, 0);
    const confidence = Math.min(bestScore / Math.max(totalScore, 1), 1.0);

    return {
      type: this.mapToStandardType(bestType),
      score: confidence
    };
  }

  /**
   * 映射到标准类型
   */
  mapToStandardType(type) {
    const mapping = {
      '求财': 'wealth',
      '感情': 'love',
      '出行': 'travel',
      '健康': 'health',
      '学习': 'study',
      '工作': 'career',
      '家庭': 'family',
      '天气': 'weather',
      'weather': 'weather'  // 支持直接的英文类型
    };

    return mapping[type] || 'other';
  }

  /**
   * 提取关键词
   */
  extractKeywords(text) {
    const keywords = [];
    
    Object.values(this.semanticDictionary).forEach(dict => {
      dict.keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          keywords.push(keyword);
        }
      });
    });

    return keywords;
  }

  /**
   * 生成搜索词
   */
  generateSearchTerms(questionType, originalText) {
    const baseTerms = [];
    
    // 根据问题类型添加基础搜索词
    if (this.semanticDictionary[this.reverseMapType(questionType)]) {
      const dict = this.semanticDictionary[this.reverseMapType(questionType)];
      baseTerms.push(...dict.keywords.slice(0, 3)); // 取前3个关键词
    }

    // 添加原文中的关键词
    const extractedKeywords = this.extractKeywords(originalText);
    baseTerms.push(...extractedKeywords.slice(0, 2));

    // 去重并返回
    return [...new Set(baseTerms)];
  }

  /**
   * 反向映射类型
   */
  reverseMapType(standardType) {
    const reverseMapping = {
      'wealth': '求财',
      'love': '感情',
      'travel': '出行',
      'health': '健康',
      'study': '学习',
      'career': '工作',
      'family': '家庭',
      'weather': '天气',
      'other': 'weather'  // other类型默认映射到weather，避免总是返回求财
    };

    return reverseMapping[standardType] || '天气';  // 默认改为天气而不是求财
  }

  /**
   * 获取默认结果
   */
  getDefaultResult() {
    return {
      questionType: 'other',
      confidence: 0.3,
      emotion: { dominant: 'neutral', confidence: 0.5 },
      timeContext: { timeFrame: 'present', confidence: 0.5 },
      allScores: {},
      keywords: [],
      searchTerms: ['吉凶', '运势', '占卜']
    };
  }
}

// 创建全局实例
const intelligentMatcher = new IntelligentMatcher();

module.exports = intelligentMatcher;
