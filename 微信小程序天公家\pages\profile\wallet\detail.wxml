<!--pages/profile/wallet/detail.wxml-->
<view class="wallet-detail-container">
  <view class="header">
    <view class="title">钱包明细</view>
  </view>

  <!-- 标签切换 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">消费明细</view>
    <view class="tab {{activeTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">充值记录</view>
  </view>

  <!-- 筛选选项 -->
  <view class="filter-container">
    <view 
      wx:for="{{filterOptions}}" 
      wx:key="value" 
      class="filter-option {{filterPeriod === item.value ? 'active' : ''}}" 
      bindtap="switchPeriod" 
      data-period="{{item.value}}"
    >
      {{item.text}}
    </view>
  </view>

  <!-- 消费明细列表 -->
  <view class="records-container" wx:if="{{activeTab === 0}}">
    <block wx:if="{{consumeRecords.length > 0}}">
      <view 
        class="record-item" 
        wx:for="{{consumeRecords}}" 
        wx:key="id" 
        bindtap="viewRecordDetail" 
        data-id="{{item.id}}" 
        data-type="consume"
      >
        <view class="record-left">
          <view class="record-title">{{item.title}}</view>
          <view class="record-date">{{item.date}}</view>
        </view>
        <view class="record-right">
          <view class="record-amount consume">{{item.amount}}</view>
          <view class="record-status">{{item.status === 'completed' ? '已完成' : '处理中'}}</view>
        </view>
      </view>
    </block>
    <view wx:else class="empty-records">
      <image class="empty-icon" src="/assets/icons/profile.png"></image>
      <view class="empty-text">暂无消费记录</view>
    </view>
  </view>

  <!-- 充值记录列表 -->
  <view class="records-container" wx:if="{{activeTab === 1}}">
    <block wx:if="{{rechargeRecords.length > 0}}">
      <view 
        class="record-item" 
        wx:for="{{rechargeRecords}}" 
        wx:key="id" 
        bindtap="viewRecordDetail" 
        data-id="{{item.id}}" 
        data-type="recharge"
      >
        <view class="record-left">
          <view class="record-title">{{item.title}}</view>
          <view class="record-date">{{item.date}}</view>
        </view>
        <view class="record-right">
          <view class="record-amount recharge">+{{item.amount}}</view>
          <view class="record-status">{{item.status === 'completed' ? '已完成' : '处理中'}}</view>
        </view>
      </view>
    </block>
    <view wx:else class="empty-records">
      <image class="empty-icon" src="/assets/icons/profile.png"></image>
      <view class="empty-text">暂无充值记录</view>
    </view>
  </view>
</view>