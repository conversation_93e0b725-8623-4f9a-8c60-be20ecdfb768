// pages/profile/wallet/detail.js
Page({
  data: {
    activeTab: 0, // 0: 消费明细, 1: 充值记录
    filterPeriod: 3, // 默认显示3个月
    consumeRecords: [],
    rechargeRecords: [],
    filterOptions: [
      { value: 3, text: '近3个月' },
      { value: 6, text: '近6个月' },
      { value: 12, text: '近12个月' }
    ]
  },

  onLoad: function() {
    this.getWalletRecords();
  },

  // 切换标签
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeTab: index
    });
  },

  // 切换筛选周期
  switchPeriod: function(e) {
    const period = e.currentTarget.dataset.period;
    this.setData({
      filterPeriod: period
    });
    this.getWalletRecords();
  },

  // 获取钱包记录
  getWalletRecords: function() {
    // 模拟获取消费记录
    const mockConsumeRecords = [
      {
        id: 'c001',
        type: 'assessment',
        title: '心理健康初筛',
        amount: -20,
        date: '2023-10-15 14:30',
        status: 'completed'
      },
      {
        id: 'c002',
        type: 'package',
        title: '心灵守护套餐',
        amount: -99.9,
        date: '2023-09-28 10:15',
        status: 'completed'
      },
      {
        id: 'c003',
        type: 'consultation',
        title: '专家一对一咨询',
        amount: -50,
        date: '2023-09-15 16:45',
        status: 'completed'
      }
    ];

    // 模拟获取充值记录
    const mockRechargeRecords = [
      {
        id: 'r001',
        type: 'recharge',
        title: '余额充值',
        amount: 100,
        date: '2023-10-10 09:30',
        status: 'completed'
      },
      {
        id: 'r002',
        type: 'package',
        title: '心灵守护套餐',
        amount: 99.9,
        date: '2023-09-28 10:15',
        status: 'completed'
      },
      {
        id: 'r003',
        type: 'coupon',
        title: '新用户赠送优惠券',
        amount: 10,
        date: '2023-09-01 08:00',
        status: 'completed'
      }
    ];

    this.setData({
      consumeRecords: mockConsumeRecords,
      rechargeRecords: mockRechargeRecords
    });

    // 实际应用中应该从云数据库获取
    // wx.cloud.callFunction({
    //   name: 'getWalletRecords',
    //   data: {
    //     period: this.data.filterPeriod
    //   },
    //   success: res => {
    //     this.setData({
    //       consumeRecords: res.result.consumeRecords,
    //       rechargeRecords: res.result.rechargeRecords
    //     });
    //   },
    //   fail: err => {
    //     console.error('获取钱包记录失败', err);
    //     wx.showToast({
    //       title: '获取记录失败',
    //       icon: 'none'
    //     });
    //   }
    // });
  },

  // 查看记录详情
  viewRecordDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type;
    
    wx.showToast({
      title: '记录详情功能开发中',
      icon: 'none'
    });
    
    // 实际应用中应该跳转到详情页面
    // wx.navigateTo({
    //   url: `/pages/profile/wallet/record_detail?id=${id}&type=${type}`
    // });
  }
})