/**
 * 专业级五行计算引擎 (Professional Wuxing Calculation Engine)
 * 基于《五行计算.txt》文档标准 V4.0 - 集成版
 * 
 * 实现三层权重模型：
 * 1. 天干基础力量计算 (Step 2)
 * 2. 地支藏干精确力量计算 (Step 3) 
 * 3. 月令季节修正 (Step 4)
 */

class ProfessionalWuxingEngine {
  constructor() {
    this.initializeDataStructures();
    this.initializeCache();
    // 🔧 新增：存储详细计算过程
    this.calculationDetails = {
      ganPowers: [],
      zhiPowers: [],
      seasonalInfo: {}
    };
  }

  /**
   * 初始化核心数据结构
   * 严格按照文档标准定义
   */
  initializeDataStructures() {
    // 天干地支常量定义 (用于输入验证)
    this.HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    // Step 1: 初始化五行力量容器 [cite: 22]
    this.elementPowers = { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 };

    // Step 2: 天干五行映射 [cite: 23]
    this.HEAVENLY_STEM_WUXING = {
      '甲': '木', '乙': '木',
      '丙': '火', '丁': '火',
      '戊': '土', '己': '土',
      '庚': '金', '辛': '金',
      '壬': '水', '癸': '水'
    };

    // 天干基础分值 [cite: 23]
    this.HEAVENLY_STEM_SCORE = 10;

    // Step 3: 地支藏干权威配比表 [cite: 25-28]
    // 根据"主气、中气、余气"权威力量配比
    this.HIDDEN_STEM_STRENGTH = {
      '寅': { '木': 0.6, '火': 0.3, '土': 0.1 }, // 主气木，中气火，余气土
      '卯': { '木': 1.0 },                        // 纯木
      '辰': { '土': 0.6, '木': 0.3, '水': 0.1 }, // 主气土，中气木，余气水 [cite: 26]
      '巳': { '火': 0.6, '金': 0.3, '土': 0.1 }, // 主气火，中气金，余气土
      '午': { '火': 0.7, '土': 0.3 },             // 主气火，中气土
      '未': { '土': 0.6, '火': 0.3, '木': 0.1 }, // 主气土，中气火，余气木 [cite: 26]
      '申': { '金': 0.6, '水': 0.3, '土': 0.1 }, // 主气金，中气水，余气土 [cite: 27]
      '酉': { '金': 1.0 },                        // 纯金
      '戌': { '土': 0.6, '金': 0.3, '火': 0.1 }, // 主气土，中气金，余气火 [cite: 27]
      '亥': { '水': 0.7, '木': 0.3 },             // 主气水，中气木
      '子': { '水': 1.0 },                        // 纯水
      '丑': { '土': 0.6, '水': 0.3, '金': 0.1 }  // 主气土，中气水，余气金 [cite: 28]
    };

    // 地支基础分值 [cite: 29]
    this.BASE_SCORE_BRANCH = 10;

    // Step 4: 季节修正系数表 [cite: 31-37]
    // 体现"得令者旺"的最高原则
    this.SEASONAL_MULTIPLIER = {
      '春': { '木': 1.5, '火': 1.2, '水': 0.8, '金': 0.6, '土': 0.4 }, // 木旺，火相，水休，金囚，土死
      '夏': { '火': 1.5, '土': 1.2, '木': 0.8, '水': 0.6, '金': 0.4 }, // 火旺，土相，木休，水囚，金死 [cite: 4]
      '秋': { '金': 1.5, '水': 1.2, '土': 0.8, '火': 0.6, '木': 0.4 }, // 金旺，水相，土休，火囚，木死
      '冬': { '水': 1.5, '木': 1.2, '金': 0.8, '土': 0.6, '火': 0.4 }  // 水旺，木相，金休，土囚，火死
    };

    // 地支季节映射
    this.BRANCH_SEASON_MAP = {
      '寅': '春', '卯': '春', '辰': '春',  // 春季：寅卯辰
      '巳': '夏', '午': '夏', '未': '夏',  // 夏季：巳午未
      '申': '秋', '酉': '秋', '戌': '秋',  // 秋季：申酉戌
      '亥': '冬', '子': '冬', '丑': '冬'   // 冬季：亥子丑
    };
  }

  /**
   * 核心计算方法：五行静态力量量化
   * 实现文档 Part 2 的完整算法流程
   * 
   * @param {Array} fourPillars - 四柱八字 [{gan: '甲', zhi: '子'}, ...]
   * @returns {Object} 五行力量分布 {'木': 120.5, '火': 80.0, ...}
   */
  calculateStaticPowers(fourPillars) {
    // Step 1: 重置五行力量容器和详细记录
    this.elementPowers = { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 };
    this.calculationDetails = {
      ganPowers: [],
      zhiPowers: [],
      seasonalInfo: {}
    };

    console.log('🚀 开始专业级五行静态力量量化计算...');
    console.log('📋 输入四柱:', fourPillars.map(p => p.gan + p.zhi).join(' '));

    // Step 2: 计算天干的基础力量 [cite: 23-25]
    this.addHeavenlyStemPowers(fourPillars);

    // Step 3: 计算地支藏干的精确力量 [cite: 25-31]
    this.addHiddenStemPowers(fourPillars);

    // Step 4: 应用月令（季节）旺衰修正 [cite: 31-37]
    this.applySeasonalModification(fourPillars[1].zhi); // 月支

    console.log('✅ 五行静态力量量化完成:', this.elementPowers);
    return { ...this.elementPowers };
  }

  /**
   * Step 2: 计算天干的基础力量 [cite: 23-25]
   * 为每个天干设定基础分值（10分），按五行累加
   */
  addHeavenlyStemPowers(fourPillars) {
    console.log('\n📊 Step 2: 计算天干基础力量...');

    fourPillars.forEach((pillar, index) => {
      const gan = pillar.gan;
      const wuxing = this.HEAVENLY_STEM_WUXING[gan];
      const score = this.HEAVENLY_STEM_SCORE;

      this.elementPowers[wuxing] += score;

      // 🔧 记录详细计算过程
      this.calculationDetails.ganPowers.push({
        gan: gan,
        element: wuxing,
        score: score,
        pillar: ['年', '月', '日', '时'][index]
      });

      const pillarNames = ['年', '月', '日', '时'];
      console.log(`  ${pillarNames[index]}干 ${gan}(${wuxing}) +${score}分`);
    });

    console.log('  天干力量小计:', { ...this.elementPowers });
  }

  /**
   * Step 3: 计算地支藏干的精确力量 [cite: 25-31]
   * 根据"主气、中气、余气"权威配比计算
   */
  addHiddenStemPowers(fourPillars) {
    console.log('\n📊 Step 3: 计算地支藏干精确力量...');
    
    fourPillars.forEach((pillar, index) => {
      const zhi = pillar.zhi;
      const hiddenStrength = this.HIDDEN_STEM_STRENGTH[zhi];
      const baseScore = this.BASE_SCORE_BRANCH;
      
      const pillarNames = ['年', '月', '日', '时'];
      console.log(`  ${pillarNames[index]}支 ${zhi}:`);
      
      // 🔧 记录地支详细信息
      const zhiDetail = {
        zhi: zhi,
        pillar: pillarNames[index],
        details: []
      };

      Object.entries(hiddenStrength).forEach(([wuxing, ratio]) => {
        const score = baseScore * ratio;
        this.elementPowers[wuxing] += score;

        const qiType = ratio >= 0.6 ? '主气' : ratio >= 0.3 ? '中气' : '余气';

        // 记录详细信息
        zhiDetail.details.push({
          qiType: qiType,
          wuxing: wuxing,
          ratio: ratio,
          score: Math.round(score * 10) / 10
        });

        console.log(`    ${qiType} ${wuxing} +${score.toFixed(1)}分 (${(ratio*100).toFixed(0)}%)`);
      });

      this.calculationDetails.zhiPowers.push(zhiDetail);
    });

    console.log('  地支藏干力量小计:', { ...this.elementPowers });
  }

  /**
   * Step 4: 应用月令（季节）旺衰修正 [cite: 31-37]
   * 根据出生月份地支确定季节，应用对应的五行旺衰修正系数
   */
  applySeasonalModification(monthZhi) {
    console.log('\n📊 Step 4: 应用月令季节修正...');

    // 确定季节 [cite: 33, 35]
    const season = this.BRANCH_SEASON_MAP[monthZhi];
    const multipliers = this.SEASONAL_MULTIPLIER[season];

    console.log(`  月支: ${monthZhi} → 季节: ${season}`);
    console.log(`  季节修正系数:`, multipliers);

    // 🔧 记录季节信息
    this.calculationDetails.seasonalInfo = {
      monthZhi: monthZhi,
      season: season,
      multipliers: []
    };

    // 应用季节修正 [cite: 35-37]
    Object.keys(this.elementPowers).forEach(wuxing => {
      const originalPower = this.elementPowers[wuxing];
      const multiplier = multipliers[wuxing];
      const modifiedPower = originalPower * multiplier;

      this.elementPowers[wuxing] = Math.round(modifiedPower * 10) / 10; // 保留1位小数

      const status = multiplier >= 1.5 ? '旺' : multiplier >= 1.2 ? '相' : multiplier >= 0.8 ? '休' : multiplier >= 0.6 ? '囚' : '死';

      // 记录详细修正信息
      this.calculationDetails.seasonalInfo.multipliers.push({
        element: wuxing,
        originalPower: Math.round(originalPower * 10) / 10,
        multiplier: multiplier,
        finalPower: this.elementPowers[wuxing],
        status: status
      });

      console.log(`    ${wuxing}: ${originalPower.toFixed(1)} × ${multiplier} = ${this.elementPowers[wuxing]} (${status})`);
    });

    console.log('  季节修正后最终力量:', { ...this.elementPowers });
  }

  /**
   * 获取五行力量统计信息
   * @returns {Object} 包含总分、最强/最弱五行、平衡度等信息
   */
  getWuxingStatistics() {
    const powers = Object.values(this.elementPowers);
    const total = powers.reduce((sum, power) => sum + power, 0);

    // 改进边界条件处理
    if (total <= 0) {
      return {
        totalPower: 0,
        strongest: { element: '未知', power: 0 },
        weakest: { element: '未知', power: 0 },
        balanceIndex: 0,
        balanceStatus: '数据异常',
        powerDistribution: { ...this.elementPowers }
      };
    }

    const max = Math.max(...powers);
    const min = Math.min(...powers);

    const strongest = Object.keys(this.elementPowers).find(key => this.elementPowers[key] === max);
    const weakest = Object.keys(this.elementPowers).find(key => this.elementPowers[key] === min);

    // 计算平衡度 (标准差越小越平衡)
    const average = total / 5;
    const variance = powers.reduce((sum, power) => sum + Math.pow(power - average, 2), 0) / 5;
    const standardDeviation = Math.sqrt(variance);
    const balanceIndex = average > 0 ? Math.max(0, 100 - (standardDeviation / average) * 100) : 0;
    
    return {
      totalPower: Math.round(total * 10) / 10,
      strongest: { element: strongest, power: max },
      weakest: { element: weakest, power: min },
      balanceIndex: Math.round(balanceIndex),
      balanceStatus: this.getBalanceStatus(balanceIndex),
      powerDistribution: { ...this.elementPowers }
    };
  }

  /**
   * 获取平衡状态描述
   */
  getBalanceStatus(balanceIndex) {
    if (balanceIndex >= 80) return '五行平衡，配置协调';
    if (balanceIndex >= 60) return '五行基本平衡，略有偏颇';
    if (balanceIndex >= 40) return '五行不够平衡，存在明显偏颇';
    return '五行严重失衡，需要调理';
  }

  /**
   * 初始化缓存系统
   * 实现智能缓存机制，提升重复计算性能
   */
  initializeCache() {
    // 计算结果缓存
    this.calculationCache = new Map();

    // 🔧 缓存配置 - 临时禁用缓存以修复硬编码数据问题
    this.cacheConfig = {
      maxSize: 1000,           // 最大缓存条目数
      ttl: 30 * 60 * 1000,    // 缓存生存时间 (30分钟)
      enableCache: false       // 🔧 临时禁用缓存，强制每次重新计算
    };

    // 缓存统计
    this.cacheStats = {
      hits: 0,                // 缓存命中次数
      misses: 0,              // 缓存未命中次数
      evictions: 0,           // 缓存清理次数
      totalRequests: 0        // 总请求次数
    };
  }

  /**
   * 生成四柱缓存键
   * @param {Array} fourPillars - 四柱八字
   * @returns {string} 缓存键
   */
  generateCacheKey(fourPillars) {
    return fourPillars.map(pillar => `${pillar.gan}${pillar.zhi}`).join('-');
  }

  /**
   * 获取缓存结果
   * @param {string} cacheKey - 缓存键
   * @returns {Object|null} 缓存的计算结果或null
   */
  getCachedResult(cacheKey) {
    if (!this.cacheConfig.enableCache) {
      return null;
    }

    const cached = this.calculationCache.get(cacheKey);

    if (!cached) {
      this.cacheStats.misses++;
      return null;
    }

    // 检查缓存是否过期
    if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
      this.calculationCache.delete(cacheKey);
      this.cacheStats.misses++;
      this.cacheStats.evictions++;
      return null;
    }

    this.cacheStats.hits++;
    return cached.result;
  }

  /**
   * 设置缓存结果
   * @param {string} cacheKey - 缓存键
   * @param {Object} result - 计算结果
   */
  setCachedResult(cacheKey, result) {
    if (!this.cacheConfig.enableCache) {
      return;
    }

    // 检查缓存大小限制
    if (this.calculationCache.size >= this.cacheConfig.maxSize) {
      this.evictOldestCache();
    }

    // 深拷贝结果以避免引用问题
    const cachedResult = JSON.parse(JSON.stringify(result));

    this.calculationCache.set(cacheKey, {
      result: cachedResult,
      timestamp: Date.now()
    });
  }

  /**
   * 清理最旧的缓存条目
   */
  evictOldestCache() {
    if (this.calculationCache.size === 0) {
      return;
    }

    // 找到最旧的缓存条目
    let oldestKey = null;
    let oldestTimestamp = Date.now();

    for (const [key, value] of this.calculationCache.entries()) {
      if (value.timestamp < oldestTimestamp) {
        oldestTimestamp = value.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.calculationCache.delete(oldestKey);
      this.cacheStats.evictions++;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, value] of this.calculationCache.entries()) {
      if (now - value.timestamp > this.cacheConfig.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.calculationCache.delete(key);
      this.cacheStats.evictions++;
    });
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const hitRate = this.cacheStats.totalRequests > 0
      ? (this.cacheStats.hits / this.cacheStats.totalRequests * 100).toFixed(2)
      : '0.00';

    return {
      ...this.cacheStats,
      hitRate: `${hitRate}%`,
      cacheSize: this.calculationCache.size,
      maxSize: this.cacheConfig.maxSize
    };
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.calculationCache.clear();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    };
  }

  /**
   * 输入验证
   * @param {Array} fourPillars - 四柱八字
   * @throws {Error} 输入验证失败时抛出异常
   */
  validateInput(fourPillars) {
    // 检查输入是否为空
    if (fourPillars === null || fourPillars === undefined) {
      throw new Error('输入不能为空');
    }

    // 检查输入是否为数组
    if (!Array.isArray(fourPillars)) {
      throw new Error('输入必须是数组格式');
    }

    // 检查四柱数量
    if (fourPillars.length !== 4) {
      throw new Error('必须提供完整的四柱八字');
    }

    // 检查每个柱的格式
    for (let i = 0; i < fourPillars.length; i++) {
      const pillar = fourPillars[i];

      if (!pillar || typeof pillar !== 'object') {
        throw new Error(`第${i + 1}柱格式错误`);
      }

      if (!pillar.gan || !pillar.zhi) {
        throw new Error('每个柱都必须包含天干和地支');
      }

      // 检查天干是否有效
      if (!this.HEAVENLY_STEMS.includes(pillar.gan)) {
        throw new Error(`包含无效的天干: ${pillar.gan}`);
      }

      // 检查地支是否有效
      if (!this.EARTHLY_BRANCHES.includes(pillar.zhi)) {
        throw new Error(`包含无效的地支: ${pillar.zhi}`);
      }
    }
  }

  /**
   * 获取详细的计算报告
   * @param {Array} fourPillars - 四柱八字
   * @returns {Object} 详细的计算过程和结果
   */
  generateDetailedReport(fourPillars) {
    // 输入验证
    this.validateInput(fourPillars);

    // 更新请求统计
    this.cacheStats.totalRequests++;

    // 生成缓存键
    const cacheKey = this.generateCacheKey(fourPillars);

    // 🔧 强制禁用缓存，确保每次都重新计算
    console.log(`🔄 强制重新计算（缓存已禁用）: ${cacheKey}`);

    const finalPowers = this.calculateStaticPowers(fourPillars);
    const statistics = this.getWuxingStatistics();
    
    const result = {
      algorithm: '专业级三层权重模型',
      version: 'V4.0 - 基于《五行计算.txt》文档标准',
      inputData: {
        fourPillars: fourPillars.map(p => p.gan + p.zhi).join(' '),
        monthBranch: fourPillars[1].zhi,
        season: this.BRANCH_SEASON_MAP[fourPillars[1].zhi]
      },
      calculationSteps: {
        step1: '初始化五行力量容器',
        step2: '天干基础力量计算 (10分/个)',
        step3: '地支藏干精确力量计算 (主气60%、中气30%、余气10%)',
        step4: '月令季节修正 (春木旺1.5倍等)'
      },
      // 🔧 新增：详细计算过程数据
      calculationDetails: this.calculationDetails,
      results: {
        finalPowers,
        statistics,
        professionalLevel: true,
        accuracy: '符合传统命理学标准'
      }
    };

    // 🔧 不再存入缓存，确保每次都是新计算
    console.log('🎯 返回新计算结果（未缓存）');

    return result;
  }
}

// 导出模块
module.exports = ProfessionalWuxingEngine;
