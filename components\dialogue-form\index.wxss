/* components/dialogue-form/index.wxss */
.form-wrapper {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #6236FF, #9370DB);
  border-radius: 2rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.arrow {
  font-size: 20rpx;
  color: #999;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #6236FF 0%, #9370DB 100%);
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 12rpx rgba(98, 54, 255, 0.3);
  transition: opacity 0.3s;
}

.submit-button:active {
  opacity: 0.8;
}

.form-tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 30rpx;
}
