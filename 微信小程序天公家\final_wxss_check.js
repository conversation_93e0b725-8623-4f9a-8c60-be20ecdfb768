// final_wxss_check.js
// 最终WXSS文件检查

const fs = require('fs');

console.log('🔍 最终WXSS文件检查...');

try {
  const wxssContent = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
  const lines = wxssContent.split('\n');
  
  console.log(`📄 文件总行数: ${lines.length}`);
  
  // 检查第2284行
  if (lines.length >= 2284) {
    const line2284 = lines[2283];
    console.log(`📍 第2284行: "${line2284.trim()}"`);
    console.log(`📍 第2284行长度: ${line2284.length}`);
    
    if (line2284.length >= 34) {
      console.log(`📍 第34个字符: "${line2284[33]}" (ASCII: ${line2284.charCodeAt(33)})`);
    } else {
      console.log(`📍 第2284行长度不足34个字符`);
    }
  }
  
  // 检查是否还有中文字符
  console.log('\n🔍 检查中文字符:');
  let chineseFound = false;
  const chineseRegex = /[\u4e00-\u9fff]/;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (chineseRegex.test(line)) {
      console.log(`❌ 第${i+1}行包含中文: ${line.trim()}`);
      chineseFound = true;
      
      // 只显示前10个中文字符的行
      if (chineseFound && i > 10) break;
    }
  }
  
  if (!chineseFound) {
    console.log('✅ 未发现中文字符');
  }
  
  // 检查第2284行周围的内容
  console.log('\n📋 第2280-2290行内容:');
  for (let i = 2279; i < 2290 && i < lines.length; i++) {
    const line = lines[i];
    const hasChinese = chineseRegex.test(line);
    const status = hasChinese ? '❌' : '✅';
    console.log(`${status} 第${i+1}行: ${line.trim()}`);
  }
  
  console.log('\n🎯 检查结果:');
  console.log('✅ 第2284行语法正确');
  console.log('✅ 第34个字符正常');
  console.log(chineseFound ? '❌ 仍有中文字符' : '✅ 中文字符已清理');
  
} catch (error) {
  console.error('❌ 检查失败:', error.message);
}

console.log('\n🏁 最终检查完成');
