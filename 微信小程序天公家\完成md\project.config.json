{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "天公师兄", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "ignoreUploadUnusedFiles": true}, "appid": "wxb3f6fac4630bf848", "libVersion": "3.8.12", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "simulatorPluginLibVersion": {}, "cloud": false}