// verify_complete_tabs.js
// 验证完整标签页功能

const fs = require('fs');

console.log('🔍 验证八字分析结果页面完整功能...');

// 1. 检查WXML内容完整性
try {
  const wxmlContent = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
  
  console.log('\n📋 标签页内容完整性检查:');
  
  // 基本信息页面
  const basicInfo = {
    hasPersonalInfo: wxmlContent.includes('个人信息'),
    hasBirthTime: wxmlContent.includes('出生时间'),
    hasLocationInfo: wxmlContent.includes('地理信息'),
    hasTimeItems: wxmlContent.includes('time-item'),
    hasLocationGrid: wxmlContent.includes('location-info-grid')
  };
  
  console.log('  基本信息页面:');
  console.log(`    ${basicInfo.hasPersonalInfo ? '✅' : '❌'} 个人信息模块`);
  console.log(`    ${basicInfo.hasBirthTime ? '✅' : '❌'} 出生时间模块`);
  console.log(`    ${basicInfo.hasLocationInfo ? '✅' : '❌'} 地理信息模块`);
  
  // 四柱排盘页面
  const paipanInfo = {
    hasBaziDisplay: wxmlContent.includes('bazi-display'),
    hasWuxingAnalysis: wxmlContent.includes('wuxing-analysis'),
    hasShishenDetail: wxmlContent.includes('shishen-detail-card'),
    hasNayin: wxmlContent.includes('pillar-nayin')
  };
  
  console.log('  四柱排盘页面:');
  console.log(`    ${paipanInfo.hasBaziDisplay ? '✅' : '❌'} 八字显示`);
  console.log(`    ${paipanInfo.hasWuxingAnalysis ? '✅' : '❌'} 五行分析`);
  console.log(`    ${paipanInfo.hasShishenDetail ? '✅' : '❌'} 十神详解`);
  console.log(`    ${paipanInfo.hasNayin ? '✅' : '❌'} 纳音显示`);
  
  // 神煞星曜页面
  const advancedInfo = {
    hasStarsGrid: wxmlContent.includes('stars-grid'),
    hasAuspiciousStars: wxmlContent.includes('auspicious-stars-card'),
    hasInauspiciousStars: wxmlContent.includes('inauspicious-stars-card'),
    hasSummaryStats: wxmlContent.includes('summary-stats')
  };
  
  console.log('  神煞星曜页面:');
  console.log(`    ${advancedInfo.hasStarsGrid ? '✅' : '❌'} 神煞网格布局`);
  console.log(`    ${advancedInfo.hasAuspiciousStars ? '✅' : '❌'} 吉星分析`);
  console.log(`    ${advancedInfo.hasInauspiciousStars ? '✅' : '❌'} 凶煞分析`);
  console.log(`    ${advancedInfo.hasSummaryStats ? '✅' : '❌'} 统计总结`);
  
  // 大运流年页面
  const fortuneInfo = {
    hasDayunInfo: wxmlContent.includes('dayun-info'),
    hasTimeline: wxmlContent.includes('dayun-timeline'),
    hasLiunian: wxmlContent.includes('liunian-list'),
    hasDayunChars: wxmlContent.includes('dayun-chars')
  };
  
  console.log('  大运流年页面:');
  console.log(`    ${fortuneInfo.hasDayunInfo ? '✅' : '❌'} 当前大运`);
  console.log(`    ${fortuneInfo.hasTimeline ? '✅' : '❌'} 大运时间线`);
  console.log(`    ${fortuneInfo.hasLiunian ? '✅' : '❌'} 流年分析`);
  console.log(`    ${fortuneInfo.hasDayunChars ? '✅' : '❌'} 大运干支`);
  
  // 专业细盘页面
  const professionalInfo = {
    hasPatternAnalysis: wxmlContent.includes('pattern-analysis-card'),
    hasYongshen: wxmlContent.includes('yongshen-card'),
    hasShishenGrid: wxmlContent.includes('shishen-grid'),
    hasAdvice: wxmlContent.includes('professional-advice-card')
  };
  
  console.log('  专业细盘页面:');
  console.log(`    ${professionalInfo.hasPatternAnalysis ? '✅' : '❌'} 格局分析`);
  console.log(`    ${professionalInfo.hasYongshen ? '✅' : '❌'} 用神喜忌`);
  console.log(`    ${professionalInfo.hasShishenGrid ? '✅' : '❌'} 十神详解`);
  console.log(`    ${professionalInfo.hasAdvice ? '✅' : '❌'} 专业建议`);
  
  // 古籍分析页面
  const classicalInfo = {
    hasQuotes: wxmlContent.includes('classical-quotes'),
    hasJudgment: wxmlContent.includes('judgment-sections'),
    hasPatterns: wxmlContent.includes('classical-pattern-card'),
    hasRemedy: wxmlContent.includes('remedy-methods')
  };
  
  console.log('  古籍分析页面:');
  console.log(`    ${classicalInfo.hasQuotes ? '✅' : '❌'} 古籍引用`);
  console.log(`    ${classicalInfo.hasJudgment ? '✅' : '❌'} 古法断语`);
  console.log(`    ${classicalInfo.hasPatterns ? '✅' : '❌'} 古籍格局`);
  console.log(`    ${classicalInfo.hasRemedy ? '✅' : '❌'} 古法改运`);
  
} catch (error) {
  console.error('❌ WXML内容检查失败:', error.message);
}

// 2. 检查样式完整性
try {
  const wxssContent = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
  
  console.log('\n🎨 样式完整性检查:');
  
  const styleChecks = {
    hasStarsStyles: wxssContent.includes('.stars-grid') && wxssContent.includes('.star-item'),
    hasDayunStyles: wxssContent.includes('.dayun-info') && wxssContent.includes('.dayun-char'),
    hasProfessionalStyles: wxssContent.includes('.pattern-info') && wxssContent.includes('.yongshen-analysis'),
    hasClassicalStyles: wxssContent.includes('.classical-quotes') && wxssContent.includes('.quote-item'),
    hasTimelineStyles: wxssContent.includes('.timeline-item') && wxssContent.includes('.timeline-char'),
    hasElementStyles: wxssContent.includes('.element-item') && wxssContent.includes('.elements-row')
  };
  
  console.log(`  ${styleChecks.hasStarsStyles ? '✅' : '❌'} 神煞星曜样式`);
  console.log(`  ${styleChecks.hasDayunStyles ? '✅' : '❌'} 大运流年样式`);
  console.log(`  ${styleChecks.hasProfessionalStyles ? '✅' : '❌'} 专业分析样式`);
  console.log(`  ${styleChecks.hasClassicalStyles ? '✅' : '❌'} 古籍分析样式`);
  console.log(`  ${styleChecks.hasTimelineStyles ? '✅' : '❌'} 时间线样式`);
  console.log(`  ${styleChecks.hasElementStyles ? '✅' : '❌'} 五行元素样式`);
  
  // 统计样式数量
  const importantCount = (wxssContent.match(/!important/g) || []).length;
  const classCount = (wxssContent.match(/\.[a-zA-Z-_]+\s*{/g) || []).length;
  
  console.log(`  📊 样式统计: ${classCount}个CSS类, ${importantCount}个强制样式`);
  
} catch (error) {
  console.error('❌ WXSS样式检查失败:', error.message);
}

// 3. 检查数据完整性
try {
  const jsContent = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
  
  console.log('\n📊 数据完整性检查:');
  
  const dataChecks = {
    hasExtendedUserInfo: jsContent.includes('zodiac') && jsContent.includes('solar_time'),
    hasStarsData: jsContent.includes('auspiciousStars') && jsContent.includes('inauspiciousStars'),
    hasDayunData: jsContent.includes('currentDayun') && jsContent.includes('dayunTimeline'),
    hasLiunianData: jsContent.includes('liunianData'),
    hasPatternData: jsContent.includes('pattern') && jsContent.includes('yongshen'),
    hasShishenData: jsContent.includes('shishenDetail'),
    hasClassicalData: jsContent.includes('classicalQuotes')
  };
  
  console.log(`  ${dataChecks.hasExtendedUserInfo ? '✅' : '❌'} 扩展用户信息`);
  console.log(`  ${dataChecks.hasStarsData ? '✅' : '❌'} 神煞星曜数据`);
  console.log(`  ${dataChecks.hasDayunData ? '✅' : '❌'} 大运数据`);
  console.log(`  ${dataChecks.hasLiunianData ? '✅' : '❌'} 流年数据`);
  console.log(`  ${dataChecks.hasPatternData ? '✅' : '❌'} 格局用神数据`);
  console.log(`  ${dataChecks.hasShishenData ? '✅' : '❌'} 十神详解数据`);
  console.log(`  ${dataChecks.hasClassicalData ? '✅' : '❌'} 古籍分析数据`);
  
} catch (error) {
  console.error('❌ JS数据检查失败:', error.message);
}

console.log('\n🎯 完整功能总结:');
console.log('✅ 基本信息页面：个人信息、出生时间、地理信息');
console.log('✅ 四柱排盘页面：八字显示、五行分析、十神详解');
console.log('✅ 神煞星曜页面：吉星凶煞、化解方法、统计分析');
console.log('✅ 大运流年页面：当前大运、时间线、流年分析');
console.log('✅ 专业细盘页面：格局分析、用神喜忌、专业建议');
console.log('✅ 古籍分析页面：古籍引用、古法断语、改运方法');

console.log('\n📱 预期显示效果:');
console.log('- 每个标签页都有丰富的专业内容');
console.log('- 统一的天公师父品牌样式');
console.log('- 清晰的视觉层次和布局');
console.log('- 专业的命理分析和建议');
console.log('- 完整的数据展示和交互');

console.log('\n🏁 完整标签页验证完成');
