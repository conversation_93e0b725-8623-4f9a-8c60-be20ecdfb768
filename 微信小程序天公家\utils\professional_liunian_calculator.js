// 引入现有的权威数据系统
const TrueSolarTimeCorrector = require('./true_solar_time_corrector.js');

/**
 * 专业级流年计算系统
 * 基于《三命通会》、《滴天髓》等古籍理论
 *
 * 核心特性：
 * 1. 动态干支计算（基于黄帝纪元法）
 * 2. 集成权威节气数据表（1900-2025年，分钟级精度）
 * 3. 集成真太阳时修正系统（306个城市支持）
 * 4. 十神关系分析
 * 5. 五行力量变化
 * 6. 神煞激活检测
 * 7. 大运流年交互影响
 * 8. 关键转折点识别
 */

class ProfessionalLiunianCalculator {
  constructor() {
    // 初始化权威数据系统
    this.timeCorrector = new TrueSolarTimeCorrector();

    // 天干地支序列
    this.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    // 十神映射表
    this.tenGodsMap = {
      '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
      '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
      '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
      '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
      '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
      '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
      '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
      '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
      '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
      '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
    };
    
    // 五行属性
    this.wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土', '己': '土',
      '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
    
    // 神煞配置
    this.shensha = {
      '驿马': { '申子辰': '寅', '寅午戌': '申', '巳酉丑': '亥', '亥卯未': '巳' },
      '桃花': { '申子辰': '酉', '寅午戌': '卯', '巳酉丑': '午', '亥卯未': '子' },
      '天乙贵人': {
        '甲戊庚': ['丑', '未'], '乙己': ['子', '申'], '丙丁': ['亥', '酉'],
        '壬癸': ['巳', '卯'], '辛': ['寅', '午']
      }
    };

    console.log('🌟 专业级流年计算系统初始化完成');
    console.log('🌅 集成真太阳时修正系统（306个城市支持）');
    console.log('🌸 集成权威节气数据表（1900-2025年，分钟级精度）');
  }

  /**
   * 计算指定年份的流年干支
   * 基于黄帝纪元法（公元前2637年为甲子年）
   */
  calculateLiunianGanzhi(year) {
    const BASE_YEAR = 2637; // 黄帝纪元基准年
    const ganIndex = (year + BASE_YEAR - 1) % 10;
    const zhiIndex = (year + BASE_YEAR - 1) % 12;
    
    return {
      gan: this.tiangan[ganIndex],
      zhi: this.dizhi[zhiIndex],
      ganzhi: this.tiangan[ganIndex] + this.dizhi[zhiIndex]
    };
  }

  /**
   * 分析流年与八字的十神关系
   */
  analyzeTenGodsRelation(dayMaster, liunianGan) {
    const tenGod = this.tenGodsMap[dayMaster][liunianGan];
    
    const tenGodAnalysis = {
      '比肩': { type: '助身', strength: '中', description: '朋友助力，合作机会' },
      '劫财': { type: '助身', strength: '强', description: '竞争激烈，破财风险' },
      '食神': { type: '泄身', strength: '中', description: '才华展现，收入稳定' },
      '伤官': { type: '泄身', strength: '强', description: '创新突破，变动较大' },
      '偏财': { type: '耗身', strength: '中', description: '投资机会，偏财运佳' },
      '正财': { type: '耗身', strength: '中', description: '正财稳定，感情和谐' },
      '七杀': { type: '克身', strength: '强', description: '压力挑战，需要谨慎' },
      '正官': { type: '克身', strength: '中', description: '升职机会，名声提升' },
      '偏印': { type: '生身', strength: '中', description: '学习机会，思维活跃' },
      '正印': { type: '生身', strength: '强', description: '贵人相助，学业有成' }
    };
    
    return {
      tenGod: tenGod,
      analysis: tenGodAnalysis[tenGod] || { type: '未知', strength: '中', description: '需要综合分析' }
    };
  }

  /**
   * 计算流年五行力量变化
   */
  calculateWuxingChange(bazi, liunianGanzhi) {
    const liunianWuxing = {
      [this.wuxingMap[liunianGanzhi.gan]]: 1.5, // 天干力量
      [this.wuxingMap[liunianGanzhi.zhi]]: 1.0  // 地支力量
    };
    
    // 分析五行增减
    const changes = {};
    for (const element of ['木', '火', '土', '金', '水']) {
      const increase = liunianWuxing[element] || 0;
      changes[element] = {
        change: increase,
        impact: increase > 1 ? '增强' : increase > 0 ? '略增' : '无变化'
      };
    }
    
    return changes;
  }

  /**
   * 检测神煞激活
   */
  detectShenshaActivation(bazi, liunianZhi) {
    const activatedShensha = [];
    
    // 检测驿马
    for (const [pattern, yima] of Object.entries(this.shensha.驿马)) {
      if (pattern.includes(bazi.dayPillar.zhi) && liunianZhi === yima) {
        activatedShensha.push({
          name: '驿马',
          description: '主动变迁，出行机会增多',
          impact: '变动'
        });
      }
    }
    
    // 检测桃花
    for (const [pattern, taohua] of Object.entries(this.shensha.桃花)) {
      if (pattern.includes(bazi.dayPillar.zhi) && liunianZhi === taohua) {
        activatedShensha.push({
          name: '桃花',
          description: '感情机会，人际关系活跃',
          impact: '感情'
        });
      }
    }
    
    return activatedShensha;
  }

  /**
   * 分析大运流年交互影响
   */
  analyzeDayunLiunianInteraction(currentDayun, liunianGanzhi) {
    const interactions = [];
    
    // 天干合化检测
    const ganHe = {
      '甲己': '土', '乙庚': '金', '丙辛': '水', '丁壬': '木', '戊癸': '火'
    };
    
    const heKey = [currentDayun.gan, liunianGanzhi.gan].sort().join('');
    if (ganHe[heKey]) {
      interactions.push({
        type: '天干合化',
        result: ganHe[heKey],
        description: `${currentDayun.gan}${liunianGanzhi.gan}合化${ganHe[heKey]}，主和谐发展`
      });
    }
    
    // 地支冲克检测
    const zhiChong = {
      '子午': '冲', '丑未': '冲', '寅申': '冲', '卯酉': '冲', '辰戌': '冲', '巳亥': '冲'
    };
    
    const chongKey = [currentDayun.zhi, liunianGanzhi.zhi].sort().join('');
    if (zhiChong[chongKey]) {
      interactions.push({
        type: '地支相冲',
        result: '冲突',
        description: `${currentDayun.zhi}${liunianGanzhi.zhi}相冲，主变动不安`
      });
    }
    
    return interactions;
  }

  /**
   * 评估流年运势等级
   */
  evaluateFortuneLevel(tenGodAnalysis, wuxingChanges, shenshaList, interactions) {
    let score = 50; // 基础分数
    
    // 十神影响
    const tenGodImpact = {
      '助身': { '强': 15, '中': 10, '弱': 5 },
      '生身': { '强': 20, '中': 15, '弱': 8 },
      '泄身': { '强': -5, '中': 5, '弱': 10 },
      '耗身': { '强': -10, '中': 0, '弱': 5 },
      '克身': { '强': -20, '中': -10, '弱': -5 }
    };
    
    const impact = tenGodImpact[tenGodAnalysis.analysis.type]?.[tenGodAnalysis.analysis.strength] || 0;
    score += impact;
    
    // 神煞影响
    shenshaList.forEach(shensha => {
      if (shensha.impact === '感情') score += 8;
      if (shensha.impact === '变动') score += 5;
    });
    
    // 交互影响
    interactions.forEach(interaction => {
      if (interaction.type === '天干合化') score += 10;
      if (interaction.type === '地支相冲') score -= 15;
    });
    
    // 等级评定
    if (score >= 80) return { level: '大吉', score: score, description: '运势极佳，诸事顺利' };
    if (score >= 65) return { level: '中吉', score: score, description: '运势良好，多有收获' };
    if (score >= 50) return { level: '平稳', score: score, description: '运势平稳，稳步发展' };
    if (score >= 35) return { level: '小凶', score: score, description: '运势欠佳，需要谨慎' };
    return { level: '大凶', score: score, description: '运势不利，宜守不宜攻' };
  }

  /**
   * 生成流年建议
   */
  generateAdvice(tenGodAnalysis, fortuneLevel, shenshaList) {
    const advice = [];
    
    // 基于十神的建议
    const tenGodAdvice = {
      '比肩': '适合合作投资，注意朋友关系',
      '劫财': '防范破财，避免借贷担保',
      '食神': '发挥才华，稳定收入来源',
      '伤官': '创新求变，但需控制情绪',
      '偏财': '把握投资机会，防范风险',
      '正财': '稳定发展，感情和谐',
      '七杀': '面对挑战，提升能力',
      '正官': '争取升职，维护名声',
      '偏印': '学习充电，开拓思维',
      '正印': '依靠贵人，提升学历'
    };
    
    advice.push(tenGodAdvice[tenGodAnalysis.tenGod] || '综合分析，稳步发展');
    
    // 基于运势等级的建议
    if (fortuneLevel.level === '大吉') {
      advice.push('运势极佳，可大胆进取，把握机遇');
    } else if (fortuneLevel.level === '大凶') {
      advice.push('运势不利，宜静不宜动，保守为上');
    }
    
    // 基于神煞的建议
    shenshaList.forEach(shensha => {
      if (shensha.name === '驿马') advice.push('适合出行、搬迁或工作变动');
      if (shensha.name === '桃花') advice.push('感情机会增多，单身者有望脱单');
    });
    
    return advice;
  }

  /**
   * 计算完整的流年分析
   */
  calculateCompleteLiunianAnalysis(bazi, startYear, yearCount = 5, currentDayun = null) {
    console.log(`🔮 开始计算${startYear}年起${yearCount}年流年分析...`);
    
    const results = [];
    
    for (let i = 0; i < yearCount; i++) {
      const year = startYear + i;
      const age = year - bazi.birthInfo.year;
      
      // 计算流年干支
      const liunianGanzhi = this.calculateLiunianGanzhi(year);
      
      // 十神分析
      const tenGodAnalysis = this.analyzeTenGodsRelation(bazi.dayPillar.gan, liunianGanzhi.gan);
      
      // 五行变化
      const wuxingChanges = this.calculateWuxingChange(bazi, liunianGanzhi);
      
      // 神煞激活
      const activatedShensha = this.detectShenshaActivation(bazi, liunianGanzhi.zhi);
      
      // 大运交互（如果提供了当前大运）
      const interactions = currentDayun ? 
        this.analyzeDayunLiunianInteraction(currentDayun, liunianGanzhi) : [];
      
      // 运势评估
      const fortuneLevel = this.evaluateFortuneLevel(
        tenGodAnalysis, wuxingChanges, activatedShensha, interactions
      );
      
      // 生成建议
      const advice = this.generateAdvice(tenGodAnalysis, fortuneLevel, activatedShensha);
      
      results.push({
        year: year,
        age: age,
        ganzhi: liunianGanzhi.ganzhi,
        gan: liunianGanzhi.gan,
        zhi: liunianGanzhi.zhi,
        tenGod: tenGodAnalysis.tenGod,
        tenGodAnalysis: tenGodAnalysis.analysis,
        wuxingChanges: wuxingChanges,
        activatedShensha: activatedShensha,
        interactions: interactions,
        fortuneLevel: fortuneLevel,
        advice: advice,
        isCurrent: year === new Date().getFullYear(),
        basis: '《三命通会·流年章》',
        calculation: {
          method: '黄帝纪元法',
          ganIndex: (year + 2637 - 1) % 10,
          zhiIndex: (year + 2637 - 1) % 12
        }
      });
    }
    
    console.log(`✅ 流年分析计算完成，共${results.length}年`);
    return results;
  }

  /**
   * 获取当前流年状态
   */
  getCurrentLiunianStatus(bazi, currentDayun = null) {
    const currentYear = new Date().getFullYear();
    const liunianAnalysis = this.calculateCompleteLiunianAnalysis(
      bazi, currentYear, 1, currentDayun
    );
    
    return liunianAnalysis[0];
  }

  /**
   * 获取权威节气数据
   * 使用现有的权威节气数据表（1900-2025年）
   */
  getAuthoritativeJieqiData(year) {
    try {
      // 尝试使用全局的权威节气数据函数
      if (typeof getAuthoritativeJieqiData === 'function') {
        return getAuthoritativeJieqiData(year);
      }

      // 尝试使用模块方式加载
      if (typeof require !== 'undefined') {
        try {
          const jieqiModule = require('../权威节气数据_前端就绪版.js');
          if (jieqiModule && jieqiModule.getAuthoritativeJieqiData) {
            return jieqiModule.getAuthoritativeJieqiData(year);
          }
        } catch (error) {
          console.warn('⚠️ 无法通过require加载权威节气数据:', error.message);
        }
      }

      console.warn('⚠️ 权威节气数据不可用，使用降级计算');
      return null;
    } catch (error) {
      console.error('❌ 获取权威节气数据失败:', error);
      return null;
    }
  }

  /**
   * 计算真太阳时修正
   * 使用现有的306个城市经纬度数据库
   */
  calculateTrueSolarTimeCorrection(beijingTime, cityName) {
    try {
      if (this.timeCorrector && cityName) {
        return this.timeCorrector.calculateTrueSolarTimeByCity(beijingTime, cityName);
      } else if (this.timeCorrector) {
        // 默认使用北京经度
        return this.timeCorrector.calculateTrueSolarTime(beijingTime, 116.4074);
      }

      console.warn('⚠️ 真太阳时修正系统不可用');
      return {
        result: { trueSolarTime: beijingTime },
        input: { beijingTime: beijingTime },
        calculation: { direction: '无修正', timeOffsetMinutes: 0 }
      };
    } catch (error) {
      console.error('❌ 真太阳时修正失败:', error);
      return {
        result: { trueSolarTime: beijingTime },
        input: { beijingTime: beijingTime },
        calculation: { direction: '修正失败', timeOffsetMinutes: 0 }
      };
    }
  }

  /**
   * 获取系统状态信息
   */
  getSystemStatus() {
    const authoritativeJieqiAvailable = typeof getAuthoritativeJieqiData === 'function';
    const trueSolarTimeAvailable = this.timeCorrector !== null;

    return {
      name: '专业级流年计算系统',
      version: '1.0.0',
      authoritativeJieqi: {
        available: authoritativeJieqiAvailable,
        yearRange: authoritativeJieqiAvailable ? '1900-2025' : '不可用',
        precision: authoritativeJieqiAvailable ? '分钟级' : '不可用'
      },
      trueSolarTime: {
        available: trueSolarTimeAvailable,
        supportedCities: trueSolarTimeAvailable ? '306个城市' : '不可用',
        correctionType: trueSolarTimeAvailable ? '经度修正' : '不可用'
      },
      features: [
        '黄帝纪元法干支计算',
        '十神关系分析',
        '神煞激活检测',
        '运势等级评估',
        authoritativeJieqiAvailable ? '权威节气数据集成' : '基础节气计算',
        trueSolarTimeAvailable ? '真太阳时修正集成' : '标准时间计算'
      ]
    };
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProfessionalLiunianCalculator;
} else if (typeof window !== 'undefined') {
  window.ProfessionalLiunianCalculator = ProfessionalLiunianCalculator;
}

console.log('📦 专业级流年计算系统模块加载完成');
