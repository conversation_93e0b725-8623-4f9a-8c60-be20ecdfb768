/* 天公师父品牌规范的八字分析结果页面样式 - 优化版 */

/* 强制覆盖全局样式 */
.tianggong-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
  color: #2C1810 !important;
  box-sizing: border-box !important;
  line-height: 1.6 !important;
}

/* 全局页面样式 */
page {
  background-color: #FFF8DC !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
  color: #2C1810 !important;
  line-height: 1.6 !important;
}

/* 重置默认样式 */
view, text, button {
  box-sizing: border-box !important;
}

/* 确保文字显示正常 */
text {
  display: inline !important;
  word-wrap: break-word !important;
  color: inherit !important;
  line-height: 1.6 !important;
}

/* 主容器 */
.tianggong-container {
  min-height: 100vh !important;
  height: 100vh !important;
  background: linear-gradient(135deg, #FFF8DC 0%, #FAF0E6 100%) !important;
  width: 100% !important;
  position: relative !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 加载状态 */
.tianggong-loading-state {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
}

.loading-content {
  text-align: center;
  color: #FFD700;
  z-index: 1;
}

.tianggong-logo {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
}

.loading-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
  display: block;
}

.loading-progress {
  width: 200rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #DAA520);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 错误状态 */
.tianggong-error-state {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 75% 75%, rgba(220, 20, 60, 0.1) 0%, transparent 50%);
}

.error-content {
  text-align: center;
  color: #FFD700;
  z-index: 1;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  display: block;
}

.tianggong-retry-btn {
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #2C1810;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 主要内容区域 */
.tianggong-main-content {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
}



/* 标签页导航 */
.tianggong-tab-nav {
  position: relative;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
  margin: 30rpx 30rpx 0;
  border-radius: 15rpx 15rpx 0 0;
  overflow: hidden;
}

.tab-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 30% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
}

.tab-items {
  position: relative;
  z-index: 1;
  display: flex;
  padding: 10rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  color: rgba(255, 255, 255, 0.9) !important; /* 🔧 改为白色，提高对比度 */
  transition: all 0.3s ease;
  border-radius: 10rpx;
  margin: 0 5rpx;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.15) !important; /* 🔧 白色半透明背景 */
  color: #FFFFFF !important; /* 🔧 纯白色文字，最高对比度 */
  transform: translateY(-2rpx);
  font-weight: bold; /* 🔧 加粗突出激活状态 */
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: inherit; /* 🔧 继承父元素颜色，确保一致性 */
}

.tab-text {
  font-size: 20rpx;
  display: block;
  color: inherit; /* 🔧 继承父元素颜色，确保一致性 */
  white-space: pre-line; /* 🔧 支持换行符显示 */
  text-align: center; /* 🔧 文字居中对齐 */
  line-height: 1.3; /* 🔧 适当的行高，保持紧凑 */
}

/* 标签页内容 */
.tianggong-tab-content {
  background: #FFFFFF;
  margin: 0 30rpx;
  border-radius: 0 0 15rpx 15rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 页面底部渐变效果 */
.tianggong-tab-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: linear-gradient(to top, rgba(255, 248, 220, 0.8) 0%, transparent 100%);
  pointer-events: none;
  z-index: 10;
  border-radius: 0 0 15rpx 15rpx;
}

.tab-panel {
  padding: 30rpx 30rpx 60rpx 0; /* 🔧 完全移除左内边距，让内容从最左边开始 */
  background: #FFFFFF;
  flex: 1;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
}

/* 卡片样式 - 优化版 */
/* 🔧 强制对齐样式 - 高优先级 */
.tab-panel .tianggong-card {
  background: #FFFFFF !important;
  border-radius: 20rpx !important;
  margin: 0 30rpx 25rpx 0 !important; /* 🔧 左边距为0，与导航栏左边界完美对齐 */
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.08) !important;
  border: 1rpx solid rgba(139, 69, 19, 0.1) !important;
  padding: 0 !important;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tianggong-card {
  background: #FFFFFF !important;
  border-radius: 20rpx !important;
  margin: 0 30rpx 25rpx 0 !important; /* 🔧 左边距改为0，让卡片从最左边开始 */
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.08) !important;
  border: 1rpx solid rgba(139, 69, 19, 0.1) !important;
  padding: 0 !important;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tianggong-card:hover {
  box-shadow: 0 8rpx 25rpx rgba(139, 69, 19, 0.12) !important;
  transform: translateY(-2rpx);
}

/* 🔧 强制标题对齐样式 - 高优先级 */
.tab-panel .tianggong-card .card-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 35rpx 35rpx 25rpx 0 !important; /* 🔧 左内边距为0，标题从绿线位置开始 */
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.08) !important;
  background: linear-gradient(135deg, #FAF0E6 0%, #F5F5DC 100%) !important;
  position: relative;
}

.card-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 35rpx 35rpx 25rpx 0 !important; /* 🔧 左内边距为0，标题从绿线位置开始 */
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.08) !important;
  background: linear-gradient(135deg, #FAF0E6 0%, #F5F5DC 100%) !important;
  position: relative;
}

/* 🔧 移除深色竖线装饰 - 不再需要 */
/* .card-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #8B4513 0%, #A0522D 100%);
} */

.header-icon {
  font-size: 40rpx !important;
  margin-right: 18rpx !important;
  display: inline-block;
  filter: drop-shadow(0 2rpx 4rpx rgba(139, 69, 19, 0.2));
}

.card-title {
  font-size: 34rpx !important;
  font-weight: 600 !important;
  color: #2C1810 !important;
  display: inline-block;
  letter-spacing: 0.5rpx;
}

.card-subtitle {
  font-size: 24rpx !important;
  color: #8B4513 !important;
  margin-left: 10rpx !important;
  opacity: 0.8;
}

/* 🔧 强制内容对齐样式 - 高优先级 */
.tab-panel .tianggong-card .card-content {
  padding: 35rpx 35rpx 35rpx 0 !important; /* 🔧 左内边距为0，内容从绿线位置开始 */
  background: #FFFFFF !important;
  line-height: 1.7 !important;
}

.card-content {
  padding: 35rpx 35rpx 35rpx 0 !important; /* 🔧 左内边距为0，内容从绿线位置开始 */
  background: #FFFFFF !important;
  line-height: 1.7 !important;
}

/* 用户信息网格 - 优化版 */
.user-info-grid {
  display: flex !important;
  flex-direction: column !important;
  gap: 25rpx;
  width: 100%;
}

.info-row {
  display: flex !important;
  gap: 25rpx;
  width: 100%;
}

.info-item {
  flex: 1 !important;
  background: linear-gradient(135deg, #FAF0E6 0%, #F8F8FF 100%) !important;
  padding: 25rpx !important;
  border-radius: 15rpx !important;
  border-left: 5rpx solid #DAA520 !important;
  min-height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.05);
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.08);
}

.info-label {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  margin-bottom: 10rpx !important;
  display: block !important;
  line-height: 1.3;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.info-value {
  font-size: 30rpx !important;
  color: #2C1810 !important;
  font-weight: 600 !important;
  display: block !important;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}

.info-value.primary {
  color: #8B4513 !important;
  font-weight: 700 !important;
  font-size: 32rpx !important;
}

.info-value.highlight {
  color: #DAA520 !important;
  font-weight: 700 !important;
}

/* 时间信息 */
.time-info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #FAF0E6;
  border-radius: 10rpx;
  border-left: 4rpx solid #DAA520;
}

.time-item.primary {
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6);
  border-left-color: #8B4513;
}

.time-label {
  display: flex;
  align-items: center;
}

.time-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.time-text {
  font-size: 28rpx;
  color: #2C1810;
  font-weight: 500;
}

.time-value {
  font-size: 26rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 八字显示样式 */
.bazi-display {
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6) !important;
  padding: 30rpx !important;
  border-radius: 15rpx !important;
  border: 2rpx solid #DAA520 !important;
}

.bazi-pillars {
  display: flex !important;
  justify-content: space-between !important;
  align-items: stretch;
  gap: 15rpx;
  padding: 20rpx 0;
}

.pillar {
  text-align: center !important;
  flex: 1;
  background: linear-gradient(135deg, #FAF0E6 0%, #F8F8FF 100%);
  border-radius: 15rpx;
  padding: 20rpx 10rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
  box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.05);
  transition: all 0.3s ease;
}

.pillar:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 5rpx 15rpx rgba(139, 69, 19, 0.1);
}

.pillar.highlight {
  border: 2rpx solid #DAA520;
  background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%);
}

.pillar-label {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  margin-bottom: 18rpx !important;
  display: block !important;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.pillar-chars {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 12rpx;
}

.pillar-char {
  width: 70rpx !important;
  height: 70rpx !important;
  background: linear-gradient(135deg, #DAA520 0%, #FFD700 50%, #FFA500 100%) !important;
  color: #2C1810 !important;
  border-radius: 12rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 28rpx !important;
  font-weight: 700 !important;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.pillar-char::before {
  content: '';
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  right: 2rpx;
  height: 20rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, transparent 100%);
  border-radius: 8rpx 8rpx 0 0;
}

.pillar-nayin {
  font-size: 22rpx !important;
  color: #8D6E63 !important;
  margin-top: 12rpx !important;
  display: block !important;
  text-align: center !important;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 五行分析样式 */
.wuxing-analysis {
  padding: 20rpx 0 !important;
}

.wuxing-stats {
  margin-bottom: 30rpx !important;
}

.wuxing-item {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 20rpx !important;
  gap: 15rpx;
}

.wuxing-name {
  width: 40rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #2C1810 !important;
  text-align: center !important;
}

.wuxing-bar {
  flex: 1 !important;
  height: 24rpx !important;
  background: #F0F0F0 !important;
  border-radius: 12rpx !important;
  overflow: hidden !important;
  margin: 0 15rpx !important;
  border: 1rpx solid rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

.wuxing-fill {
  height: 100% !important;
  border-radius: 12rpx !important;
  transition: width 0.5s ease !important;
  min-width: 2rpx !important;
  position: relative !important;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.3) !important;
}

.wuxing-fill.wood {
  background: linear-gradient(90deg, #228B22, #32CD32) !important;
  border: 1rpx solid #1F7A1F !important;
}

.wuxing-fill.fire {
  background: linear-gradient(90deg, #DC143C, #FF6347) !important;
  border: 1rpx solid #B91C3C !important;
}

.wuxing-fill.earth {
  background: linear-gradient(90deg, #CD853F, #DEB887) !important;
  border: 1rpx solid #A0522D !important;
}

.wuxing-fill.metal {
  background: linear-gradient(90deg, #B8B8B8, #D3D3D3) !important;
  border: 1rpx solid #A0A0A0 !important;
}

.wuxing-fill.water {
  background: linear-gradient(90deg, #4682B4, #87CEEB) !important;
  border: 1rpx solid #2F5F8F !important;
}

.wuxing-count {
  width: 40rpx !important;
  font-size: 24rpx !important;
  font-weight: bold !important;
  color: #2C1810 !important;
  text-align: center !important;
}

.wuxing-summary {
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6) !important;
  padding: 20rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
}

.summary-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 10rpx !important;
}

.summary-desc {
  font-size: 24rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  line-height: 1.4 !important;
}

/* 地理信息样式 */
.location-info-grid {
  display: flex !important;
  flex-direction: column !important;
  gap: 20rpx;
}

/* 简单内容样式 */
.simple-content {
  text-align: center !important;
  padding: 40rpx 20rpx !important;
}

.content-text {
  font-size: 32rpx !important;
  color: #2C1810 !important;
  font-weight: bold !important;
  display: block !important;
  margin-bottom: 20rpx !important;
}

.content-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  line-height: 1.4;
}

.tianggong-tabs {
  background: #FFFFFF;
  display: flex;
  border-bottom: 1rpx solid #E0E0E0;
  margin: 0 30rpx;
  border-radius: 15rpx 15rpx 0 0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 28rpx;
  color: #8D6E63;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #8B4513;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(45deg, #DAA520, #FFD700);
  border-radius: 2rpx;
}

/* 内容区域 */
.tianggong-content {
  background: #FFFFFF;
  margin: 0 30rpx;
  border-radius: 0 0 15rpx 15rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-section {
  padding: 40rpx 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2C1810;
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 3rpx;
  background: linear-gradient(45deg, #DAA520, #FFD700);
  border-radius: 2rpx;
}

/* 基本信息样式 */
.basic-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.info-item {
  background: #FAF0E6;
  padding: 20rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #DAA520;
}

.info-label {
  font-size: 24rpx;
  color: #8D6E63;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #2C1810;
  font-weight: 500;
}

/* 八字显示 */
.bazi-display {
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6);
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid #DAA520;
}

.bazi-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
}

.bazi-pillars {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.pillar {
  text-align: center;
  flex: 1;
}

.pillar-label {
  font-size: 24rpx;
  color: #8D6E63;
  margin-bottom: 10rpx;
}

.pillar-chars {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pillar-char {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #2C1810;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

/* 五行平衡组件样式 */
.enhanced-balance-meter {
  background: #FFFFFF;
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
}

.balance-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.balance-subtitle {
  font-size: 24rpx;
  color: #8D6E63;
}

.balance-main {
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-score {
  font-size: 72rpx;
  font-weight: bold;
  color: #DAA520;
}

.balance-unit {
  font-size: 28rpx;
  color: #8D6E63;
  margin-left: 10rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 10rpx;
}

.balance-actions {
  text-align: center;
  margin-top: 30rpx;
}

.action-btn {
  background: linear-gradient(45deg, #DAA520, #FFD700);
  color: #2C1810;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin: 0 10rpx;
}

.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* Main content area */
.main-content {
  padding: 20rpx;
  margin-top: 20rpx; /* Further reduced top margin */
  background: transparent;
  /* Allow content overflow to support scrolling */
}

/* Tiangong Master brand header */
.header {
  background: var(--primary-gradient);
  padding: 30rpx 40rpx 15rpx; /* Further compressed padding */
  color: white;
  text-align: center;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.3);
  position: relative;
  z-index: 10;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  font-weight: 300;
}

/* Basic styles */
.analysis-container {
  background: white;
  border-radius: 8rpx;
  margin: 20rpx 0;
  padding: 15rpx;
}

/* 神煞星曜样式 - 优化版 */
.stars-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 25rpx !important;
  padding: 10rpx 0;
}

.star-item {
  background: linear-gradient(135deg, #FAF0E6 0%, #F8F8FF 100%) !important;
  padding: 25rpx !important;
  border-radius: 15rpx !important;
  border-left: 6rpx solid #DAA520 !important;
  box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.star-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 5rpx 15rpx rgba(139, 69, 19, 0.1);
}

.star-item.auspicious {
  border-left-color: #228B22 !important;
  background: linear-gradient(135deg, #F0FFF0 0%, #F5FFFA 50%, #E6FFE6 100%) !important;
}

.star-item.auspicious::before {
  content: '吉';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #228B22;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  font-weight: bold;
}

.star-item.inauspicious {
  border-left-color: #DC143C !important;
  background: linear-gradient(135deg, #FFF0F0 0%, #FFFAFA 50%, #FFE6E6 100%) !important;
}

.star-item.inauspicious::before {
  content: '凶';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #DC143C;
  color: white;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  font-weight: bold;
}

.star-item.neutral {
  border-left-color: #4682B4 !important;
  background: linear-gradient(135deg, #F0F8FF 0%, #F8F8FF 50%, #E6F3FF 100%) !important;
}

.star-item.neutral::before {
  content: '中';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #4682B4;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  font-weight: bold;
}

.star-name {
  font-size: 30rpx !important;
  font-weight: 700 !important;
  color: #2C1810 !important;
  display: block !important;
  margin-bottom: 12rpx !important;
  letter-spacing: 0.5rpx;
}

.star-position {
  font-size: 24rpx !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 12rpx !important;
  font-weight: 500;
  background: rgba(139, 69, 19, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.star-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  line-height: 1.6 !important;
  margin-bottom: 12rpx !important;
}

.star-resolve {
  font-size: 24rpx !important;
  color: #DC143C !important;
  display: block !important;
  line-height: 1.6 !important;
  background: rgba(220, 20, 60, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #DC143C;
}

.star-pillar {
  font-size: 20rpx !important;
  color: #666 !important;
  margin-top: 5rpx !important;
  font-weight: 600 !important;
}

.star-strength {
  font-size: 18rpx !important;
  color: #888 !important;
  margin-top: 3rpx !important;
  opacity: 0.8;
}

.no-stars-tip {
  text-align: center !important;
  padding: 40rpx 20rpx !important;
  color: #999 !important;
}

.tip-icon {
  font-size: 48rpx !important;
  display: block !important;
  margin-bottom: 10rpx !important;
}

.tip-text {
  font-size: 24rpx !important;
  color: #666 !important;
}

.summary-stats {
  display: flex !important;
  justify-content: space-around !important;
  margin-bottom: 30rpx !important;
  padding: 20rpx !important;
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6) !important;
  border-radius: 10rpx !important;
}

.stat-item {
  text-align: center !important;
}

.stat-number {
  font-size: 48rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
}

.stat-label {
  font-size: 24rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  margin-top: 8rpx !important;
}

/* 大运流年样式 - 优化版 */
.dayun-info {
  padding: 25rpx 0 !important;
}

.dayun-main {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 35rpx !important;
  padding: 35rpx !important;
  background: linear-gradient(135deg, #FFF8DC 0%, #FAF0E6 50%, #F0F8FF 100%) !important;
  border-radius: 20rpx !important;
  border: 2rpx solid #DAA520 !important;
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.1);
  position: relative;
}

.dayun-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #DAA520 0%, #FFD700 50%, #FFA500 100%);
  border-radius: 20rpx 20rpx 0 0;
}

.dayun-chars {
  display: flex !important;
  gap: 18rpx !important;
  margin-right: 35rpx !important;
}

.dayun-char {
  width: 90rpx !important;
  height: 90rpx !important;
  background: linear-gradient(135deg, #DAA520 0%, #FFD700 50%, #FFA500 100%) !important;
  color: #2C1810 !important;
  border-radius: 15rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 38rpx !important;
  font-weight: 700 !important;
  box-shadow: 0 6rpx 15rpx rgba(139, 69, 19, 0.2) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.dayun-char::before {
  content: '';
  position: absolute;
  top: 3rpx;
  left: 3rpx;
  right: 3rpx;
  height: 25rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, transparent 100%);
  border-radius: 12rpx 12rpx 0 0;
}

.dayun-details {
  flex: 1 !important;
}

.dayun-age {
  font-size: 36rpx !important;
  font-weight: 700 !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 12rpx !important;
  letter-spacing: 0.5rpx;
}

.dayun-nayin {
  font-size: 28rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  margin-bottom: 12rpx !important;
  background: rgba(139, 69, 19, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
}

.dayun-status {
  font-size: 30rpx !important;
  color: #228B22 !important;
  font-weight: 700 !important;
  display: block !important;
  background: rgba(34, 139, 34, 0.1);
  padding: 8rpx 15rpx;
  border-radius: 12rpx;
  display: inline-block;
  border-left: 4rpx solid #228B22;
}

.dayun-analysis {
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%) !important;
  padding: 30rpx !important;
  border-radius: 15rpx !important;
  border-left: 6rpx solid #DAA520 !important;
  box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.05);
  line-height: 1.7;
}

.analysis-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #2C1810 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
}

.analysis-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

/* 命理详情表格样式 - 完全对齐修复 */
.basic-info-table .info-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* 重置文本样式 - 避免使用通配符选择器 */
.basic-info-table .table-row,
.basic-info-table .table-cell,
.basic-info-table .table-cell text,
.basic-info-table .table-cell view {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.basic-info-table .table-row {
  display: flex;
  align-items: center;
  min-height: 80rpx;
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.1);
  padding: 15rpx 0;
  position: relative;
  box-sizing: border-box;
}

.basic-info-table .table-row:last-child {
  border-bottom: none;
}

.basic-info-table .solar-term-row {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.05), rgba(139, 69, 19, 0.05));
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(218, 165, 32, 0.2);
  min-height: 100rpx;
}

.basic-info-table .table-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 60rpx;
  padding: 0;
  margin: 0;
}

.basic-info-table .table-cell.label {
  flex: 0 0 130rpx;
  font-size: 28rpx;
  color: #8D6E63;
  font-weight: 600;
  margin-right: 25rpx;
  text-align: left;
  display: flex;
  align-items: center;
  min-height: 60rpx;
  line-height: 1;
}

.basic-info-table .table-cell.value {
  flex: 0 0 150rpx;
  font-size: 30rpx;
  color: #2C1810;
  font-weight: 500;
  display: flex;
  align-items: center;
  min-height: 60rpx;
  line-height: 1;
  margin-right: 20rpx;
}

.basic-info-table .table-cell.value text {
  line-height: 1.2;
  vertical-align: baseline;
  display: inline-block;
  margin: 0;
  padding: 0;
}

/* 修复文字对齐问题 */
.basic-info-table .table-cell.label,
.basic-info-table .table-cell.value {
  text-align: left;
  vertical-align: middle;
  box-sizing: border-box;
}

.basic-info-table .table-cell text {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.2;
  margin: 0;
  padding: 0;
  text-align: left;
}

.basic-info-table .table-cell.full-width {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 60rpx;
  line-height: 1;
}

/* 🔧 新增：两列布局样式 */
.basic-info-table .two-column-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 30rpx;
}

.basic-info-table .column-pair {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 60rpx;
}

.basic-info-table .column-pair .table-cell.label {
  flex: 0 0 80rpx;
  margin-right: 15rpx;
  font-size: 28rpx;
  color: #8D6E63;
  font-weight: 600;
  text-align: left;
}

.basic-info-table .column-pair .table-cell.value {
  flex: 1;
  font-size: 30rpx;
  color: #2C1810;
  font-weight: 500;
  text-align: left;
  margin-right: 0;
}

.basic-info-table .column-pair .table-cell.value text {
  display: inline-block;
  line-height: 1.2;
  vertical-align: middle;
}

/* ==================== 四柱八字分析卡片样式 - 整合模块 ==================== */
.bazi-comprehensive-card {
  background: linear-gradient(135deg, #FFF8E1 0%, #F5F5DC 50%, #FFF8E1 100%);
  border: 3rpx solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 12rpx 40rpx rgba(218, 165, 32, 0.2),
    0 4rpx 16rpx rgba(218, 165, 32, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.bazi-comprehensive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #DAA520, #B8860B, #CD853F);
  z-index: -1;
  margin: -3rpx;
  border-radius: inherit;
}

.bazi-comprehensive-card .card-header {
  background: linear-gradient(135deg, #DAA520 0%, #B8860B 50%, #8B7355 100%);
  color: white;
  padding: 30rpx 35rpx;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
  overflow: hidden;
}

.bazi-comprehensive-card .card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

.bazi-comprehensive-card .header-icon {
  font-size: 36rpx;
  margin-right: 18rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.bazi-comprehensive-card .card-title {
  font-size: 34rpx;
  font-weight: 700;
  letter-spacing: 3rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.bazi-comprehensive-card .card-subtitle {
  font-size: 26rpx;
  opacity: 0.95;
  margin-left: 12rpx;
  font-weight: 400;
  letter-spacing: 1rpx;
}

/* 四柱八字分析表格样式 */
.bazi-comprehensive-card .simple-content {
  padding: 0;
  background: rgba(255, 255, 255, 0.4);
}

.bazi-comprehensive-card .table-row {
  display: flex;
  align-items: center;
  min-height: 110rpx;
  border-bottom: 2rpx solid rgba(218, 165, 32, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.bazi-comprehensive-card .table-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, rgba(218, 165, 32, 0.1), transparent);
  transition: width 0.4s ease;
}

.bazi-comprehensive-card .table-row:hover::before {
  width: 100%;
}

.bazi-comprehensive-card .table-row:hover {
  background: rgba(218, 165, 32, 0.08);
  transform: translateX(5rpx);
  box-shadow: 0 4rpx 16rpx rgba(218, 165, 32, 0.15);
}

.bazi-comprehensive-card .table-row:last-child {
  border-bottom: none;
}

.bazi-comprehensive-card .header-row {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(184, 134, 11, 0.15));
  font-weight: 700;
  min-height: 90rpx;
  border-bottom: 3rpx solid rgba(218, 165, 32, 0.3);
}

.bazi-comprehensive-card .header-row:hover {
  transform: none;
  box-shadow: none;
}

.bazi-comprehensive-card .table-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 15rpx;
  text-align: center;
  position: relative;
}

.bazi-comprehensive-card .header-cell {
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 700;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(139, 69, 19, 0.1);
}

.bazi-comprehensive-card .label-cell {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.12), rgba(184, 134, 11, 0.12));
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 700;
  border-right: 2rpx solid rgba(218, 165, 32, 0.25);
  letter-spacing: 1rpx;
}

/* 十神样式 */
.bazi-comprehensive-card .star-cell {
  position: relative;
}

.bazi-comprehensive-card .star-text {
  font-size: 30rpx;
  color: #D2691E;
  font-weight: 700;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 16rpx;
  border: 2rpx solid rgba(218, 165, 32, 0.4);
  box-shadow: 0 4rpx 12rpx rgba(218, 165, 32, 0.2);
  transition: all 0.3s ease;
}

.bazi-comprehensive-card .star-text:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(218, 165, 32, 0.3);
}

/* 天干地支样式 */
.bazi-comprehensive-card .pillar-cell {
  flex-direction: column;
  gap: 10rpx;
}

.bazi-comprehensive-card .pillar-char {
  font-size: 36rpx;
  font-weight: 800;
  color: #8B4513;
  text-shadow: 0 2rpx 4rpx rgba(139, 69, 19, 0.2);
}

.bazi-comprehensive-card .pillar-char.gan {
  color: #CD853F;
}

.bazi-comprehensive-card .pillar-char.zhi {
  color: #A0522D;
}

.bazi-comprehensive-card .element-symbol {
  font-size: 22rpx;
  opacity: 0.9;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

/* 纳音样式 */
.bazi-comprehensive-card .nayin-cell {
  position: relative;
}

.bazi-comprehensive-card .nayin-text {
  font-size: 26rpx;
  color: #8A2BE2;
  font-weight: 600;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(255, 255, 255, 0.8));
  border-radius: 12rpx;
  border: 2rpx solid rgba(138, 43, 226, 0.3);
  box-shadow: 0 3rpx 10rpx rgba(138, 43, 226, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(138, 43, 226, 0.2);
}

.bazi-comprehensive-card .nayin-text:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(138, 43, 226, 0.25);
}

/* 主气样式 */
.bazi-comprehensive-card .main-qi-cell {
  position: relative;
}

.bazi-comprehensive-card .main-qi {
  font-size: 32rpx;
  font-weight: 800;
  color: #CD853F;
  padding: 12rpx 18rpx;
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 14rpx;
  border: 2rpx solid rgba(205, 133, 63, 0.4);
  box-shadow: 0 4rpx 12rpx rgba(205, 133, 63, 0.2);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(205, 133, 63, 0.3);
}

.bazi-comprehensive-card .main-qi:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(205, 133, 63, 0.3);
}

/* 藏干样式 */
.bazi-comprehensive-card .canggan-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.bazi-comprehensive-card .canggan-item {
  font-size: 26rpx;
  font-weight: 700;
  color: #4682B4;
  padding: 8rpx 12rpx;
  background: linear-gradient(135deg, rgba(70, 130, 180, 0.15), rgba(240, 248, 255, 0.9));
  border-radius: 12rpx;
  border: 2rpx solid rgba(70, 130, 180, 0.4);
  min-width: 50rpx;
  box-shadow: 0 3rpx 10rpx rgba(70, 130, 180, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(70, 130, 180, 0.2);
}

.bazi-comprehensive-card .canggan-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(70, 130, 180, 0.25);
}

/* 藏干十神样式 */
.bazi-comprehensive-card .tengod-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.bazi-comprehensive-card .tengod-item {
  font-size: 24rpx;
  font-weight: 600;
  color: #8B4513;
  padding: 6rpx 10rpx;
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 10rpx;
  border: 2rpx solid rgba(139, 69, 19, 0.4);
  box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(139, 69, 19, 0.2);
}

.bazi-comprehensive-card .tengod-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(139, 69, 19, 0.25);
}

/* 藏干强度样式 */
.bazi-comprehensive-card .strength-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.bazi-comprehensive-card .strength-item {
  font-size: 24rpx;
  font-weight: 700;
  padding: 6rpx 10rpx;
  border-radius: 10rpx;
  min-width: 45rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.bazi-comprehensive-card .strength-item:hover {
  transform: translateY(-1rpx);
}

.bazi-comprehensive-card .strength-item.strong {
  color: #228B22;
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.15), rgba(240, 255, 240, 0.9));
  border: 2rpx solid rgba(34, 139, 34, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(34, 139, 34, 0.2);
}

.bazi-comprehensive-card .strength-item.strong:hover {
  box-shadow: 0 5rpx 14rpx rgba(34, 139, 34, 0.25);
}

.bazi-comprehensive-card .strength-item.medium {
  color: #FF8C00;
  background: linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(255, 248, 220, 0.9));
  border: 2rpx solid rgba(255, 140, 0, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(255, 140, 0, 0.2);
}

.bazi-comprehensive-card .strength-item.medium:hover {
  box-shadow: 0 5rpx 14rpx rgba(255, 140, 0, 0.25);
}

.bazi-comprehensive-card .strength-item.weak {
  color: #DC143C;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.15), rgba(255, 240, 245, 0.9));
  border: 2rpx solid rgba(220, 20, 60, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(220, 20, 60, 0.2);
}

.bazi-comprehensive-card .strength-item.weak:hover {
  box-shadow: 0 5rpx 14rpx rgba(220, 20, 60, 0.25);
}

/* 副星行样式 */
.bazi-comprehensive-card .auxiliary-star-cell {
  padding: 15rpx;
  text-align: center;
  vertical-align: middle;
}

.bazi-comprehensive-card .auxiliary-star-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #8e44ad;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border: 2rpx solid #8e44ad;
  display: inline-block;
  min-width: 60rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.bazi-comprehensive-card .auxiliary-star-text:hover {
  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
  color: #ffffff;
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(142, 68, 173, 0.25);
}

/* 自坐分析样式 */
.bazi-comprehensive-card .self-sitting-cell {
  position: relative;
}

.bazi-comprehensive-card .self-sitting-text {
  font-size: 24rpx;
  color: #8A2BE2;
  font-weight: 600;
  padding: 10rpx 14rpx;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(255, 255, 255, 0.8));
  border-radius: 12rpx;
  border: 2rpx solid rgba(138, 43, 226, 0.3);
  box-shadow: 0 3rpx 10rpx rgba(138, 43, 226, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(138, 43, 226, 0.2);
  line-height: 1.4;
  text-align: left;
}

.bazi-comprehensive-card .self-sitting-text:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(138, 43, 226, 0.25);
}

/* 高亮效果 */
.bazi-comprehensive-card .highlight {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(218, 165, 32, 0.2));
  border-left: 4rpx solid #DAA520;
  border-right: 4rpx solid #DAA520;
  box-shadow: inset 0 0 20rpx rgba(218, 165, 32, 0.1);
}

/* ==================== 原十神分析卡片样式 - 精美升级版 ==================== */
.shishen-card {
  background: linear-gradient(135deg, #FFF8E1 0%, #F5F5DC 50%, #FFF8E1 100%);
  border: 3rpx solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 12rpx 40rpx rgba(218, 165, 32, 0.2),
    0 4rpx 16rpx rgba(218, 165, 32, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.shishen-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #DAA520, #B8860B, #CD853F);
  z-index: -1;
  margin: -3rpx;
  border-radius: inherit;
}

.shishen-card .card-header {
  background: linear-gradient(135deg, #DAA520 0%, #B8860B 50%, #8B7355 100%);
  color: white;
  padding: 30rpx 35rpx;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
  overflow: hidden;
}

.shishen-card .card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.shishen-card .header-icon {
  font-size: 36rpx;
  margin-right: 18rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.shishen-card .card-title {
  font-size: 34rpx;
  font-weight: 700;
  letter-spacing: 3rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 十神分析表格样式 - 现代化升级 */
.shishen-card .simple-content {
  padding: 0;
  background: rgba(255, 255, 255, 0.4);
}

.shishen-card .table-row {
  display: flex;
  align-items: center;
  min-height: 110rpx;
  border-bottom: 2rpx solid rgba(218, 165, 32, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.shishen-card .table-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, rgba(218, 165, 32, 0.1), transparent);
  transition: width 0.4s ease;
}

.shishen-card .table-row:hover::before {
  width: 100%;
}

.shishen-card .table-row:hover {
  background: rgba(218, 165, 32, 0.08);
  transform: translateX(5rpx);
  box-shadow: 0 4rpx 16rpx rgba(218, 165, 32, 0.15);
}

.shishen-card .table-row:last-child {
  border-bottom: none;
}

.shishen-card .header-row {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(184, 134, 11, 0.15));
  font-weight: 700;
  min-height: 90rpx;
  border-bottom: 3rpx solid rgba(218, 165, 32, 0.3);
}

.shishen-card .header-row:hover {
  transform: none;
  box-shadow: none;
}

.shishen-card .table-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 15rpx;
  text-align: center;
  position: relative;
}

.shishen-card .header-cell {
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 700;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(139, 69, 19, 0.1);
}

.shishen-card .label-cell {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.12), rgba(184, 134, 11, 0.12));
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 700;
  border-right: 2rpx solid rgba(218, 165, 32, 0.25);
  letter-spacing: 1rpx;
}

.shishen-card .star-cell {
  position: relative;
}

.shishen-card .star-text {
  font-size: 30rpx;
  color: #D2691E;
  font-weight: 700;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 16rpx;
  border: 2rpx solid rgba(218, 165, 32, 0.4);
  box-shadow: 0 4rpx 12rpx rgba(218, 165, 32, 0.2);
  transition: all 0.3s ease;
}

.shishen-card .star-text:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(218, 165, 32, 0.3);
}

.shishen-card .pillar-cell {
  flex-direction: column;
  gap: 10rpx;
}

.shishen-card .pillar-char {
  font-size: 36rpx;
  font-weight: 800;
  color: #8B4513;
  text-shadow: 0 2rpx 4rpx rgba(139, 69, 19, 0.2);
}

.shishen-card .pillar-char.gan {
  color: #CD853F;
}

.shishen-card .pillar-char.zhi {
  color: #A0522D;
}

.shishen-card .element-symbol {
  font-size: 22rpx;
  opacity: 0.9;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.shishen-card .highlight {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(218, 165, 32, 0.2));
  border-left: 4rpx solid #DAA520;
  border-right: 4rpx solid #DAA520;
  box-shadow: inset 0 0 20rpx rgba(218, 165, 32, 0.1);
}

/* ==================== 藏干分析卡片样式 - 统一风格版 ==================== */
.canggan-card {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 3rpx solid #17a2b8;
  box-shadow: 0 12rpx 40rpx rgba(23, 162, 184, 0.25);
}

.canggan-card .card-header {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
}

.canggan-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 30rpx;
}

.canggan-pillar {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #17a2b8;
  transition: all 0.3s ease;
}

.canggan-pillar:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(23, 162, 184, 0.15);
}

.canggan-pillar.day-canggan {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-left-color: #ffc107;
  position: relative;
}

.canggan-pillar.day-canggan::after {
  content: '★';
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  color: #ffc107;
  font-size: 24rpx;
  animation: twinkle 2s ease-in-out infinite;
}

.canggan-header {
  font-size: 26rpx;
  font-weight: bold;
  color: #17a2b8;
  margin-bottom: 15rpx;
  text-align: center;
  padding: 8rpx 0;
  background: rgba(23, 162, 184, 0.1);
  border-radius: 8rpx;
}

.canggan-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.canggan-main {
  font-size: 32rpx;
  font-weight: bold;
  color: #17a2b8;
  text-align: center;
  padding: 12rpx 20rpx;
  background: rgba(23, 162, 184, 0.1);
  border-radius: 10rpx;
  border: 2rpx solid rgba(23, 162, 184, 0.2);
}

.canggan-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.canggan-summary {
  margin: 20rpx 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  border-left: 4rpx solid #17a2b8;
}

.canggan-summary .summary-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #17a2b8;
  margin-bottom: 12rpx;
  display: block;
}

.canggan-summary .summary-content {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
}

/* 动画效果 */
@keyframes twinkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}









.canggan-card .pillar-char {
  font-size: 36rpx;
  font-weight: 800;
  color: #2F4F4F;
  text-shadow: 0 2rpx 4rpx rgba(47, 79, 79, 0.2);
}

.canggan-card .pillar-char.zhi {
  color: #4682B4;
}

.canggan-card .element-symbol {
  font-size: 22rpx;
  opacity: 0.9;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.canggan-card .main-qi-cell {
  position: relative;
}

.canggan-card .main-qi {
  font-size: 32rpx;
  font-weight: 800;
  color: #CD853F;
  padding: 12rpx 18rpx;
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 14rpx;
  border: 2rpx solid rgba(205, 133, 63, 0.4);
  box-shadow: 0 4rpx 12rpx rgba(205, 133, 63, 0.2);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(205, 133, 63, 0.3);
}

.canggan-card .main-qi:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(205, 133, 63, 0.3);
}

.canggan-card .canggan-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.canggan-card .canggan-item {
  font-size: 26rpx;
  font-weight: 700;
  color: #4682B4;
  padding: 8rpx 12rpx;
  background: linear-gradient(135deg, rgba(70, 130, 180, 0.15), rgba(240, 248, 255, 0.9));
  border-radius: 12rpx;
  border: 2rpx solid rgba(70, 130, 180, 0.4);
  min-width: 50rpx;
  box-shadow: 0 3rpx 10rpx rgba(70, 130, 180, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(70, 130, 180, 0.2);
}

.canggan-card .canggan-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(70, 130, 180, 0.25);
}

.canggan-card .tengod-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.canggan-card .tengod-item {
  font-size: 24rpx;
  font-weight: 600;
  color: #8B4513;
  padding: 6rpx 10rpx;
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 10rpx;
  border: 2rpx solid rgba(139, 69, 19, 0.4);
  box-shadow: 0 3rpx 10rpx rgba(139, 69, 19, 0.15);
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(139, 69, 19, 0.2);
}

.canggan-card .tengod-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 5rpx 14rpx rgba(139, 69, 19, 0.25);
}

.canggan-card .strength-cell {
  gap: 8rpx;
  flex-wrap: wrap;
}

.canggan-card .strength-item {
  font-size: 24rpx;
  font-weight: 700;
  padding: 6rpx 10rpx;
  border-radius: 10rpx;
  min-width: 45rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.canggan-card .strength-item:hover {
  transform: translateY(-1rpx);
}

.canggan-card .strength-item.strong {
  color: #228B22;
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.15), rgba(240, 255, 240, 0.9));
  border: 2rpx solid rgba(34, 139, 34, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(34, 139, 34, 0.2);
}

.canggan-card .strength-item.strong:hover {
  box-shadow: 0 5rpx 14rpx rgba(34, 139, 34, 0.25);
}

.canggan-card .strength-item.medium {
  color: #FF8C00;
  background: linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(255, 248, 220, 0.9));
  border: 2rpx solid rgba(255, 140, 0, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(255, 140, 0, 0.2);
}

.canggan-card .strength-item.medium:hover {
  box-shadow: 0 5rpx 14rpx rgba(255, 140, 0, 0.25);
}

.canggan-card .strength-item.weak {
  color: #DC143C;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.15), rgba(255, 240, 245, 0.9));
  border: 2rpx solid rgba(220, 20, 60, 0.4);
  text-shadow: 0 1rpx 2rpx rgba(220, 20, 60, 0.2);
}

.canggan-card .strength-item.weak:hover {
  box-shadow: 0 5rpx 14rpx rgba(220, 20, 60, 0.25);
}

/* 特殊高亮效果 - 日柱重点突出 */
.canggan-card .highlight {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(218, 165, 32, 0.2));
  border-left: 4rpx solid #DAA520;
  border-right: 4rpx solid #DAA520;
  box-shadow: inset 0 0 20rpx rgba(218, 165, 32, 0.15);
  position: relative;
}

.canggan-card .highlight::after {
  content: '★';
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  color: #DAA520;
  font-size: 24rpx;
  animation: twinkle 2s ease-in-out infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}



/* 响应式优化 */
@media (max-width: 750rpx) {
  .bazi-comprehensive-card .table-cell,
  .shishen-card .table-cell,
  .canggan-card .table-cell {
    padding: 15rpx 8rpx;
  }

  .bazi-comprehensive-card .pillar-char,
  .shishen-card .pillar-char,
  .canggan-card .pillar-char {
    font-size: 30rpx;
  }

  .bazi-comprehensive-card .star-text {
    font-size: 26rpx;
    padding: 10rpx 16rpx;
  }

  .bazi-comprehensive-card .nayin-text {
    font-size: 22rpx;
    padding: 8rpx 12rpx;
  }

  .bazi-comprehensive-card .main-qi {
    font-size: 28rpx;
    padding: 10rpx 14rpx;
  }

  .bazi-comprehensive-card .canggan-item {
    font-size: 22rpx;
    padding: 6rpx 10rpx;
  }

  .bazi-comprehensive-card .tengod-item {
    font-size: 20rpx;
    padding: 5rpx 8rpx;
  }

  .bazi-comprehensive-card .strength-item {
    font-size: 20rpx;
    padding: 5rpx 8rpx;
  }

  .bazi-comprehensive-card .self-sitting-text {
    font-size: 20rpx;
    padding: 8rpx 10rpx;
    line-height: 1.3;
  }

  .canggan-card .main-qi {
    font-size: 28rpx;
    padding: 10rpx 14rpx;
  }

  .canggan-card .canggan-item {
    font-size: 22rpx;
    padding: 6rpx 10rpx;
  }

  .canggan-card .tengod-item {
    font-size: 20rpx;
    padding: 5rpx 8rpx;
  }

  .canggan-card .strength-item {
    font-size: 20rpx;
    padding: 5rpx 8rpx;
  }
}

/* ==================== 四柱排盘主卡片样式 ==================== */
.paipan-card {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 3rpx solid #007bff;
  box-shadow: 0 12rpx 40rpx rgba(0, 123, 255, 0.25);
}

.paipan-card .card-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.paipan-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 30rpx;
}

.paipan-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  border: 1rpx solid rgba(0, 123, 255, 0.2);
  transition: all 0.3s ease;
}

.paipan-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 123, 255, 0.15);
}

.paipan-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.paipan-ganzhi {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
}

.paipan-gan, .paipan-zhi {
  font-size: 32rpx;
  font-weight: 800;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border: 1rpx solid;
  min-width: 50rpx;
  text-align: center;
}

/* 十天干颜色 - 使用类名方式 */
.paipan-gan.gan-jia {
  color: #228B22 !important; /* 森林绿 - 甲木 */
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.1), rgba(34, 139, 34, 0.05)) !important;
  border-color: rgba(34, 139, 34, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(34, 139, 34, 0.3) !important;
}

.paipan-gan.gan-yi {
  color: #32CD32 !important; /* 酸橙绿 - 乙木 */
  background: linear-gradient(135deg, rgba(50, 205, 50, 0.1), rgba(50, 205, 50, 0.05)) !important;
  border-color: rgba(50, 205, 50, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(50, 205, 50, 0.3) !important;
}

.paipan-gan.gan-bing {
  color: #FF4500 !important; /* 橙红色 - 丙火 */
  background: linear-gradient(135deg, rgba(255, 69, 0, 0.1), rgba(255, 69, 0, 0.05)) !important;
  border-color: rgba(255, 69, 0, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(255, 69, 0, 0.3) !important;
}

.paipan-gan.gan-ding {
  color: #DC143C !important; /* 深红色 - 丁火 */
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.1), rgba(220, 20, 60, 0.05)) !important;
  border-color: rgba(220, 20, 60, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(220, 20, 60, 0.3) !important;
}

.paipan-gan.gan-wu {
  color: #D2691E !important; /* 巧克力色 - 戊土 */
  background: linear-gradient(135deg, rgba(210, 105, 30, 0.1), rgba(210, 105, 30, 0.05)) !important;
  border-color: rgba(210, 105, 30, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(210, 105, 30, 0.3) !important;
}

.paipan-gan.gan-ji {
  color: #CD853F !important; /* 秘鲁色 - 己土 */
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.1), rgba(205, 133, 63, 0.05)) !important;
  border-color: rgba(205, 133, 63, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(205, 133, 63, 0.3) !important;
}

.paipan-gan.gan-geng {
  color: #C0C0C0 !important; /* 银色 - 庚金 */
  background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), rgba(192, 192, 192, 0.05)) !important;
  border-color: rgba(192, 192, 192, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(192, 192, 192, 0.3) !important;
}

.paipan-gan.gan-xin {
  color: #FFD700 !important; /* 金色 - 辛金 */
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05)) !important;
  border-color: rgba(255, 215, 0, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(255, 215, 0, 0.3) !important;
}

.paipan-gan.gan-ren {
  color: #4169E1 !important; /* 皇家蓝 - 壬水 */
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(65, 105, 225, 0.05)) !important;
  border-color: rgba(65, 105, 225, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(65, 105, 225, 0.3) !important;
}

.paipan-gan.gan-gui {
  color: #191970 !important; /* 午夜蓝 - 癸水 */
  background: linear-gradient(135deg, rgba(25, 25, 112, 0.1), rgba(25, 25, 112, 0.05)) !important;
  border-color: rgba(25, 25, 112, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(25, 25, 112, 0.3) !important;
}

/* 十二地支颜色 - 使用类名方式 */
.paipan-zhi.zhi-zi {
  color: #000080 !important; /* 海军蓝 - 子水 */
  background: linear-gradient(135deg, rgba(0, 0, 128, 0.1), rgba(0, 0, 128, 0.05)) !important;
  border-color: rgba(0, 0, 128, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 128, 0.3) !important;
}

.paipan-zhi.zhi-chou {
  color: #8B4513 !important; /* 马鞍棕 - 丑土 */
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(139, 69, 19, 0.05)) !important;
  border-color: rgba(139, 69, 19, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(139, 69, 19, 0.3) !important;
}

.paipan-zhi.zhi-yin {
  color: #006400 !important; /* 深绿色 - 寅木 */
  background: linear-gradient(135deg, rgba(0, 100, 0, 0.1), rgba(0, 100, 0, 0.05)) !important;
  border-color: rgba(0, 100, 0, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(0, 100, 0, 0.3) !important;
}

.paipan-zhi.zhi-mao {
  color: #9ACD32 !important; /* 黄绿色 - 卯木 */
  background: linear-gradient(135deg, rgba(154, 205, 50, 0.1), rgba(154, 205, 50, 0.05)) !important;
  border-color: rgba(154, 205, 50, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(154, 205, 50, 0.3) !important;
}

.paipan-zhi.zhi-chen {
  color: #DAA520 !important; /* 金杆色 - 辰土 */
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(218, 165, 32, 0.05)) !important;
  border-color: rgba(218, 165, 32, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(218, 165, 32, 0.3) !important;
}

.paipan-zhi.zhi-si {
  color: #B22222 !important; /* 火砖色 - 巳火 */
  background: linear-gradient(135deg, rgba(178, 34, 34, 0.1), rgba(178, 34, 34, 0.05)) !important;
  border-color: rgba(178, 34, 34, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(178, 34, 34, 0.3) !important;
}

.paipan-zhi.zhi-wu {
  color: #FF6347 !important; /* 番茄色 - 午火 */
  background: linear-gradient(135deg, rgba(255, 99, 71, 0.1), rgba(255, 99, 71, 0.05)) !important;
  border-color: rgba(255, 99, 71, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(255, 99, 71, 0.3) !important;
}

.paipan-zhi.zhi-wei {
  color: #F4A460 !important; /* 沙棕色 - 未土 */
  background: linear-gradient(135deg, rgba(244, 164, 96, 0.1), rgba(244, 164, 96, 0.05)) !important;
  border-color: rgba(244, 164, 96, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(244, 164, 96, 0.3) !important;
}

.paipan-zhi.zhi-shen {
  color: #708090 !important; /* 石板灰 - 申金 */
  background: linear-gradient(135deg, rgba(112, 128, 144, 0.1), rgba(112, 128, 144, 0.05)) !important;
  border-color: rgba(112, 128, 144, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(112, 128, 144, 0.3) !important;
}

.paipan-zhi.zhi-you {
  color: #B8860B !important; /* 深金杆色 - 酉金 */
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1), rgba(184, 134, 11, 0.05)) !important;
  border-color: rgba(184, 134, 11, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(184, 134, 11, 0.3) !important;
}

.paipan-zhi.zhi-xu {
  color: #A0522D !important; /* 赭色 - 戌土 */
  background: linear-gradient(135deg, rgba(160, 82, 45, 0.1), rgba(160, 82, 45, 0.05)) !important;
  border-color: rgba(160, 82, 45, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(160, 82, 45, 0.3) !important;
}

.paipan-zhi.zhi-hai {
  color: #483D8B !important; /* 深石板蓝 - 亥水 */
  background: linear-gradient(135deg, rgba(72, 61, 139, 0.1), rgba(72, 61, 139, 0.05)) !important;
  border-color: rgba(72, 61, 139, 0.3) !important;
  text-shadow: 0 2rpx 4rpx rgba(72, 61, 139, 0.3) !important;
}

/* 默认样式 */
.paipan-gan.gan-default, .paipan-zhi.zhi-default {
  color: #666 !important;
  background: rgba(0, 0, 0, 0.05) !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
  text-shadow: none !important;
}

/* ==================== 主星分析卡片样式 ==================== */
.zhuxing-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 3rpx solid #ffc107;
  box-shadow: 0 12rpx 40rpx rgba(255, 193, 7, 0.25);
}

.zhuxing-card .card-header {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: white;
}

.zhuxing-table {
  width: 100%;
  border-collapse: collapse;
}

.zhuxing-header {
  display: flex;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(224, 168, 0, 0.15));
  font-weight: 700;
  min-height: 90rpx;
  border-bottom: 3rpx solid rgba(255, 193, 7, 0.3);
}

.zhuxing-row {
  display: flex;
  align-items: center;
  min-height: 110rpx;
  border-bottom: 2rpx solid rgba(255, 193, 7, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.zhuxing-row:hover {
  background: rgba(255, 193, 7, 0.08);
  transform: translateX(5rpx);
}

.zhuxing-row.highlight {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(218, 165, 32, 0.2));
  border-left: 4rpx solid #DAA520;
  border-right: 4rpx solid #DAA520;
}

.zhuxing-table .header-cell,
.zhuxing-table .cell-label,
.zhuxing-table .cell-star,
.zhuxing-table .cell-feature,
.zhuxing-table .cell-influence {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 15rpx;
  text-align: center;
}

.zhuxing-table .header-cell {
  font-size: 28rpx;
  color: #e0a800;
  font-weight: 700;
  letter-spacing: 2rpx;
}

.zhuxing-table .cell-label {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.12), rgba(224, 168, 0, 0.12));
  font-size: 28rpx;
  color: #e0a800;
  font-weight: 700;
  border-right: 2rpx solid rgba(255, 193, 7, 0.25);
}

.zhuxing-table .cell-star {
  font-size: 32rpx;
  font-weight: 800;
  color: #ffc107;
  text-shadow: 0 2rpx 4rpx rgba(255, 193, 7, 0.3);
}

.zhuxing-table .cell-feature,
.zhuxing-table .cell-influence {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* ==================== 副星分析卡片样式 ==================== */
.fuxing-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 3rpx solid #6c757d;
  box-shadow: 0 12rpx 40rpx rgba(108, 117, 125, 0.25);
}

.fuxing-card .card-header {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

.fuxing-grid {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
  padding: 30rpx;
}

.fuxing-category {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #6c757d;
}

.category-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15rpx;
  display: block;
  text-align: center;
}

.star-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.star-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 10rpx;
  min-width: 120rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.star-item.lucky {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.15), rgba(212, 237, 218, 0.9));
  border: 2rpx solid rgba(40, 167, 69, 0.4);
}

.star-item.unlucky {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.15), rgba(248, 215, 218, 0.9));
  border: 2rpx solid rgba(220, 53, 69, 0.4);
}

.star-item.neutral {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.15), rgba(233, 236, 239, 0.9));
  border: 2rpx solid rgba(108, 117, 125, 0.4);
}

.star-name {
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.star-item.lucky .star-name {
  color: #28a745;
}

.star-item.unlucky .star-name {
  color: #dc3545;
}

.star-item.neutral .star-name {
  color: #6c757d;
}

.star-desc {
  font-size: 20rpx;
  color: #666;
  line-height: 1.3;
}

.star-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* ==================== 纳音五行卡片样式 ==================== */
.nayin-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border: 3rpx solid #28a745;
  box-shadow: 0 12rpx 40rpx rgba(40, 167, 69, 0.25);
}

.nayin-card .card-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.nayin-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
  padding: 30rpx;
}

.nayin-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  border: 1rpx solid rgba(40, 167, 69, 0.2);
}

.nayin-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.nayin-value {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #28a745;
}

/* ==================== 长生十二宫卡片样式 ==================== */
.changsheng-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 3rpx solid #6f42c1;
  box-shadow: 0 12rpx 40rpx rgba(111, 66, 193, 0.25);
}

.changsheng-card .card-header {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: white;
}

.changsheng-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 30rpx;
}

.changsheng-pillar {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #6f42c1;
}

.changsheng-pillar.day-changsheng {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-left-color: #d4a574;
}

.changsheng-header {
  font-size: 26rpx;
  font-weight: bold;
  color: #6f42c1;
  margin-bottom: 15rpx;
  text-align: center;
}

.changsheng-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.changsheng-state {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 10rpx;
  background: rgba(111, 66, 193, 0.1);
  border-radius: 8rpx;
}

.changsheng-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* ==================== 自坐分析卡片样式 ==================== */
.selfsitting-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 3rpx solid #d4a574;
  box-shadow: 0 12rpx 40rpx rgba(212, 165, 116, 0.25);
}

.selfsitting-card .card-header {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
}

.selfsitting-content {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  margin: 20rpx;
  border-left: 4rpx solid #d4a574;
}

.selfsitting-text {
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
}

/* ==================== 空亡分析卡片样式 ==================== */
.kongwang-card {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 3rpx solid #e53e3e;
  box-shadow: 0 12rpx 40rpx rgba(229, 62, 62, 0.25);
}

.kongwang-card .card-header {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
}

.kongwang-container {
  padding: 30rpx;
}

.kongwang-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 15rpx;
  border-radius: 8rpx;
}

.kongwang-label {
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
}

.kongwang-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #e53e3e;
  flex: 1;
}

.kongwang-analysis {
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 8rpx;
}

.kongwang-title {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.kongwang-desc {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
}

/* ==================== 命卦分析卡片样式 ==================== */
.minggua-card {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  border: 3rpx solid #38b2ac;
  box-shadow: 0 12rpx 40rpx rgba(56, 178, 172, 0.25);
}

.minggua-card .card-header {
  background: linear-gradient(135deg, #38b2ac 0%, #2c7a7b 100%);
  color: white;
}

.minggua-container {
  padding: 30rpx;
}

.minggua-main {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 8rpx;
}

.minggua-gua, .minggua-number, .minggua-category, .minggua-element, .minggua-direction {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 15rpx;
  border-radius: 8rpx;
}

.minggua-label {
  font-size: 22rpx;
  color: #666;
  min-width: 80rpx;
}

.minggua-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #38b2ac;
}

.minggua-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #38b2ac;
}

.minggua-analysis {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(56, 178, 172, 0.2);
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 8rpx;
}

.minggua-title {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.minggua-desc {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
}

/* 加载状态和数据就绪状态样式 */
.loading-text {
  color: #B8860B !important;
  font-style: italic;
  opacity: 0.7;
  animation: pulse 1.5s ease-in-out infinite;
}

.data-ready {
  color: #2C1810 !important;
  font-weight: 500;
}

/* 加载动画 */
@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 出生天体图样式 - 增强版 */
.celestial-chart-card {
  background: linear-gradient(135deg, rgba(25, 25, 112, 0.08), rgba(72, 61, 139, 0.08));
  border: 2rpx solid rgba(25, 25, 112, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(25, 25, 112, 0.15);
  overflow: hidden;
}

.celestial-chart-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
}

.celestial-chart {
  position: relative;
  width: 450rpx;
  height: 450rpx;
  border-radius: 50%;
  border: 4rpx solid #191970;
  background: radial-gradient(circle at center,
    rgba(25, 25, 112, 0.15) 0%,
    rgba(72, 61, 139, 0.1) 30%,
    rgba(138, 43, 226, 0.05) 60%,
    rgba(25, 25, 112, 0.02) 100%);
  box-shadow:
    inset 0 0 50rpx rgba(25, 25, 112, 0.2),
    0 0 30rpx rgba(25, 25, 112, 0.3);
  animation: celestial-glow 4s ease-in-out infinite alternate;
}

@keyframes celestial-glow {
  0% {
    box-shadow:
      inset 0 0 50rpx rgba(25, 25, 112, 0.2),
      0 0 30rpx rgba(25, 25, 112, 0.3);
  }
  100% {
    box-shadow:
      inset 0 0 60rpx rgba(25, 25, 112, 0.3),
      0 0 40rpx rgba(25, 25, 112, 0.4);
  }
}

/* 宫位分割线 */
.house-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.house-line {
  position: absolute;
  width: 2rpx;
  height: 50%;
  background: linear-gradient(to bottom, rgba(25, 25, 112, 0.3), transparent);
  top: 0;
  left: 50%;
  margin-left: -1rpx;
  transform-origin: 1rpx 225rpx;
}

.zodiac-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 宫位数字 */
.house-numbers {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.house-number {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  top: 50%;
  left: 50%;
  margin-left: -20rpx;
  margin-top: -20rpx;
  transform-origin: 20rpx 20rpx;
}

.house-text {
  font-size: 20rpx;
  color: #8D6E63;
  font-weight: 600;
  display: block;
  text-align: center;
  line-height: 40rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  border: 1rpx solid rgba(141, 110, 99, 0.3);
}

/* 上升点标识 */
.ascendant-marker {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.ascendant-line {
  position: absolute;
  width: 4rpx;
  height: 50%;
  background: linear-gradient(to bottom, #FF6B35, #FF8E53);
  top: 0;
  left: 50%;
  margin-left: -2rpx;
  transform-origin: 2rpx 225rpx;
  box-shadow: 0 0 8rpx rgba(255, 107, 53, 0.6);
}

.ascendant-label {
  position: absolute;
  top: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.9), rgba(255, 142, 83, 0.8));
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  border: 2rpx solid #FF6B35;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.ascendant-text {
  display: block;
  font-size: 22rpx;
  color: white;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.ascendant-sign {
  display: block;
  font-size: 18rpx;
  color: white;
  font-weight: 500;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 天顶标识 */
.midheaven-marker {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.midheaven-line {
  position: absolute;
  width: 4rpx;
  height: 50%;
  background: linear-gradient(to bottom, #4A90E2, #7BB3F0);
  top: 0;
  left: 50%;
  margin-left: -2rpx;
  transform-origin: 2rpx 225rpx;
  box-shadow: 0 0 8rpx rgba(74, 144, 226, 0.6);
}

.midheaven-label {
  position: absolute;
  top: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.9), rgba(123, 179, 240, 0.8));
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  border: 2rpx solid #4A90E2;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.midheaven-text {
  display: block;
  font-size: 22rpx;
  color: white;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.midheaven-sign {
  display: block;
  font-size: 18rpx;
  color: white;
  font-weight: 500;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

.zodiac-sign {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  top: 15rpx;
  left: 50%;
  margin-left: -30rpx;
  transform-origin: 30rpx 210rpx;
}

.zodiac-text {
  font-size: 26rpx;
  color: #191970;
  font-weight: 600;
  display: block;
  text-align: center;
  line-height: 60rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  border: 1rpx solid rgba(25, 25, 112, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(25, 25, 112, 0.2);
}

.planets-container {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.planet {
  position: absolute;
  width: 45rpx;
  height: 45rpx;
  top: 50%;
  left: 50%;
  margin-left: -22.5rpx;
  margin-top: -22.5rpx;
  transform-origin: 0 0;
  animation: planet-pulse 3s ease-in-out infinite;
}

.planet-symbol {
  font-size: 28rpx;
  color: #191970;
  font-weight: bold;
  display: block;
  text-align: center;
  line-height: 45rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
  border-radius: 50%;
  width: 45rpx;
  height: 45rpx;
  box-shadow:
    0 4rpx 16rpx rgba(25, 25, 112, 0.4),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(25, 25, 112, 0.2);
  margin-bottom: 4rpx;
}

.planet-degree {
  font-size: 18rpx;
  color: #8D6E63;
  font-weight: 600;
  text-align: center;
  display: block;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  padding: 2rpx 6rpx;
  border: 1rpx solid rgba(141, 110, 99, 0.2);
  line-height: 1.2;
}

@keyframes planet-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60rpx;
  height: 60rpx;
  margin-left: -30rpx;
  margin-top: -30rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #191970, #483D8B);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 6rpx 20rpx rgba(25, 25, 112, 0.5),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  animation: center-rotate 20s linear infinite;
}

.center-symbol {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  display: block;
  line-height: 1.2;
}

.center-text {
  color: white;
  font-size: 18rpx;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  display: block;
  line-height: 1.2;
  margin-top: 2rpx;
}

.birth-info {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16rpx;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  display: block;
  line-height: 1.2;
  margin-top: 4rpx;
}

@keyframes center-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.celestial-legend {
  margin-top: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, rgba(25, 25, 112, 0.05), rgba(72, 61, 139, 0.05));
  border-radius: 20rpx;
  border: 2rpx solid rgba(25, 25, 112, 0.15);
  box-shadow: 0 4rpx 20rpx rgba(25, 25, 112, 0.1);
}

.legend-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(25, 25, 112, 0.1);
}

.legend-title {
  display: block;
  font-size: 32rpx;
  color: #191970;
  font-weight: 700;
  margin-bottom: 10rpx;
}

.legend-subtitle {
  display: block;
  font-size: 24rpx;
  color: #8D6E63;
  font-weight: 500;
}

.legend-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 248, 255, 0.7));
  border-radius: 16rpx;
  border: 1rpx solid rgba(25, 25, 112, 0.2);
  box-shadow: 0 3rpx 12rpx rgba(25, 25, 112, 0.15);
  transition: all 0.3s ease;
}

.legend-item:hover {
  transform: translateY(-3rpx);
  box-shadow: 0 6rpx 24rpx rgba(25, 25, 112, 0.25);
}

.legend-symbol-container {
  flex: 0 0 60rpx;
  margin-right: 20rpx;
}

.legend-symbol {
  font-size: 32rpx;
  color: #191970;
  font-weight: bold;
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
  background: linear-gradient(135deg, rgba(25, 25, 112, 0.15), rgba(72, 61, 139, 0.1));
  border-radius: 50%;
  border: 2rpx solid rgba(25, 25, 112, 0.2);
  display: block;
}

.legend-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.legend-name {
  font-size: 28rpx;
  color: #2C1810;
  font-weight: 700;
  line-height: 1.2;
}

.legend-position {
  font-size: 26rpx;
  color: #191970;
  font-weight: 600;
  line-height: 1.2;
}

.legend-meaning {
  font-size: 24rpx;
  color: #8D6E63;
  font-weight: 500;
  line-height: 1.2;
}

.legend-house {
  font-size: 22rpx;
  color: #B8860B;
  font-weight: 600;
  background: rgba(184, 134, 11, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  display: inline-block;
  width: fit-content;
  line-height: 1.2;
}

/* 专业分析样式 */
.pattern-info {
  padding: 20rpx 0 !important;
}

.pattern-main {
  text-align: center !important;
  padding: 30rpx !important;
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6) !important;
  border-radius: 15rpx !important;
  border: 2rpx solid #DAA520 !important;
  margin-bottom: 30rpx !important;
}

.pattern-name {
  font-size: 36rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 10rpx !important;
}

.pattern-level {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  display: block !important;
  margin-bottom: 10rpx !important;
}

.pattern-score {
  font-size: 28rpx !important;
  color: #228B22 !important;
  font-weight: bold !important;
  display: block !important;
}

.pattern-analysis {
  background: #FFFFFF !important;
  padding: 25rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
}

.pattern-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

.yongshen-analysis {
  padding: 20rpx 0 !important;
}

.yongshen-section {
  margin-bottom: 30rpx !important;
}

.section-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #2C1810 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
  text-align: center !important;
}

.elements-row {
  display: flex !important;
  gap: 15rpx !important;
  justify-content: center !important;
}

.element-item {
  padding: 15rpx 25rpx !important;
  border-radius: 25rpx !important;
  text-align: center !important;
}

.element-item.favorable {
  background: linear-gradient(45deg, #228B22, #32CD32) !important;
  color: #FFFFFF !important;
}

.element-item.unfavorable {
  background: linear-gradient(45deg, #DC143C, #FF6347) !important;
  color: #FFFFFF !important;
}

.element-name {
  font-size: 24rpx !important;
  font-weight: bold !important;
  display: block !important;
  margin-bottom: 5rpx !important;
}

.element-desc {
  font-size: 20rpx !important;
  display: block !important;
}

.shishen-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 20rpx !important;
}

.shishen-item {
  background: #FAF0E6 !important;
  padding: 20rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
  text-align: center !important;
}

.shishen-name {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #2C1810 !important;
  display: block !important;
  margin-bottom: 8rpx !important;
}

.shishen-count {
  font-size: 24rpx !important;
  color: #8B4513 !important;
  font-weight: bold !important;
  display: block !important;
  margin-bottom: 8rpx !important;
}

.shishen-desc {
  font-size: 22rpx !important;
  color: #8D6E63 !important;
  line-height: 1.3 !important;
  display: block !important;
}

.advice-sections {
  display: flex !important;
  flex-direction: column !important;
  gap: 25rpx !important;
}

.advice-section {
  background: #FAF0E6 !important;
  padding: 25rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
}

.advice-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
}

.advice-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

/* 古籍分析样式 */
.classical-quotes {
  display: flex !important;
  flex-direction: column !important;
  gap: 25rpx !important;
}

.quote-item {
  background: linear-gradient(135deg, #FFF8DC, #FAF0E6) !important;
  padding: 25rpx !important;
  border-radius: 15rpx !important;
  border-left: 4rpx solid #8B4513 !important;
}

.quote-source {
  font-size: 24rpx !important;
  color: #8B4513 !important;
  font-weight: bold !important;
  display: block !important;
  margin-bottom: 10rpx !important;
}

.quote-text {
  font-size: 26rpx !important;
  color: #2C1810 !important;
  font-style: italic !important;
  line-height: 1.4 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
  padding: 15rpx !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border-radius: 8rpx !important;
}

.quote-analysis {
  font-size: 24rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

.judgment-sections {
  display: flex !important;
  flex-direction: column !important;
  gap: 25rpx !important;
}

.judgment-section {
  background: #FAF0E6 !important;
  padding: 25rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
}

.judgment-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
}

.judgment-content {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

.remedy-methods {
  display: flex !important;
  flex-direction: column !important;
  gap: 20rpx !important;
}

.remedy-item {
  background: #FAF0E6 !important;
  padding: 25rpx !important;
  border-radius: 10rpx !important;
  border-left: 4rpx solid #DAA520 !important;
}

.remedy-title {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #8B4513 !important;
  display: block !important;
  margin-bottom: 15rpx !important;
}

.remedy-desc {
  font-size: 26rpx !important;
  color: #8D6E63 !important;
  line-height: 1.5 !important;
  display: block !important;
}

/* 🎨 应期分析页面样式 */
.timing-panel {
  padding: 20rpx 20rpx 20rpx 0; /* 🔧 左内边距为0，让卡片与导航栏左边界对齐 */
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  min-height: 100vh;
}

/* 🔧 应期分析卡片对齐样式 */
.timing-panel .timing-card {
  margin: 0 30rpx 25rpx 0 !important; /* 🔧 左边距为0，与导航栏左边界对齐 */
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0,0,0,0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 应期分析卡片基础样式 */
.timing-card {
  margin: 0 30rpx 25rpx 0 !important; /* 🔧 左边距为0，与导航栏左边界对齐 */
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0,0,0,0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.timing-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 40rpx rgba(0,0,0,0.12);
}

/* 🔧 应期分析卡片标题对齐样式 */
.timing-panel .timing-card .card-header {
  padding: 35rpx 35rpx 25rpx 0 !important; /* 🔧 左内边距为0，标题从绿线位置开始 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.timing-card .card-header {
  padding: 35rpx 35rpx 25rpx 0 !important; /* 🔧 左内边距为0，标题从绿线位置开始 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.timing-card .card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #667eea 100%);
}

.timing-card .card-title {
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8rpx;
  display: block;
}

.timing-card .card-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.3;
  display: block;
}

/* 🔧 应期分析卡片内容对齐样式 */
.timing-panel .timing-card .card-content {
  padding: 35rpx 35rpx 35rpx 0 !important; /* 🔧 左内边距为0，内容从绿线位置开始 */
}

.timing-card .card-content {
  padding: 35rpx 35rpx 35rpx 0 !important; /* 🔧 左内边距为0，内容从绿线位置开始 */
}



/* 🎨 现代病药平衡卡片样式 */
.disease-medicine-modern-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left .header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

.header-text .card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 6rpx;
}

.header-text .card-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1;
}

.header-badge {
  padding: 8rpx 16rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.badge-text {
  font-size: 20rpx;
  font-weight: 500;
}

.balance-overview {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.balance-formula {
  padding: 20rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.formula-text {
  font-size: 26rpx;
  font-weight: 500;
  line-height: 1.4;
}

.disease-medicine-grid {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.event-analysis-card {
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.event-analysis-card.marriage {
  background: linear-gradient(135deg, #ffeef0 0%, #ffe0e6 100%);
  border-color: #ff9a9e;
}

.event-analysis-card.promotion {
  background: linear-gradient(135deg, #e8f5ff 0%, #d4edff 100%);
  border-color: #4fc3f7;
}

.event-analysis-card.wealth {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ffb74d;
}

.event-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.event-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.event-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.event-score {
  font-size: 24rpx;
  font-weight: 700;
  color: #667eea;
  padding: 6rpx 12rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
}

.disease-medicine-pair {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.disease-side, .medicine-side {
  flex: 1;
  text-align: center;
}

.side-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.side-value {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.side-desc {
  font-size: 20rpx;
  color: #888;
  line-height: 1;
}

.balance-arrow {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 700;
  margin: 0 20rpx;
}

.balance-meter {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.8s ease;
}

/* 古籍引用样式 */
.ancient-quotes-section {
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.quotes-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.quotes-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
}

.quotes-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.quote-card {
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.quote-source {
  display: block;
  font-size: 22rpx;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8rpx;
}

.quote-content {
  font-size: 24rpx;
  color: #495057;
  line-height: 1.5;
  font-style: italic;
}

/* 🎨 现代能量阈值卡片样式 */
.energy-threshold-modern-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.threshold-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.threshold-header .header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

.threshold-header .card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 6rpx;
}

.threshold-header .card-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.threshold-summary {
  text-align: center;
  padding: 16rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.summary-text {
  font-size: 24rpx;
  font-weight: 500;
}

.threshold-dashboard {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.threshold-event-card {
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.threshold-event-card.marriage-card {
  background: linear-gradient(135deg, #ffeef0 0%, #ffe0e6 100%);
  border-color: #ff9a9e;
}

.threshold-event-card.promotion-card {
  background: linear-gradient(135deg, #e8f5ff 0%, #d4edff 100%);
  border-color: #4fc3f7;
}

.threshold-event-card.wealth-card {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ffb74d;
}

.event-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.event-card-header .event-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.event-card-header .event-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.event-card-header .event-status {
  font-size: 22rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.threshold-metrics {
  margin-bottom: 16rpx;
}

.metric-item {
  margin-bottom: 16rpx;
}

.metric-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.metric-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.8s ease;
}

.progress-fill.marriage-fill {
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
}

.progress-fill.promotion-fill {
  background: linear-gradient(90deg, #4fc3f7 0%, #29b6f6 100%);
}

.progress-fill.wealth-fill {
  background: linear-gradient(90deg, #ffb74d 0%, #ffa726 100%);
}

.progress-text {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  min-width: 80rpx;
  text-align: right;
}

.ancient-reference {
  padding: 16rpx;
  background: rgba(255,255,255,0.6);
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.reference-text {
  font-size: 22rpx;
  color: #495057;
  line-height: 1.4;
  font-style: italic;
}

/* 🎯 现代三重引动机制卡片样式 */
.triple-activation-modern-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.activation-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.activation-header .header-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.activation-header .header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

.activation-header .card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 6rpx;
}

.activation-header .card-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.priority-badge {
  text-align: center;
  padding: 12rpx 20rpx;
  background: rgba(255,255,255,0.15);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.priority-text {
  font-size: 24rpx;
  font-weight: 600;
}

.activation-mechanism {
  padding: 30rpx;
}

.mechanism-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
}

.section-desc {
  font-size: 22rpx;
  color: #666;
}

/* 病药平衡法则样式 */
.balance-analysis {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.disease-section, .medicine-section, .balance-result {
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid;
}

.disease-section {
  background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
  border-left-color: #e91e63;
}

.medicine-section {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
  border-left-color: #4caf50;
}

.balance-result {
  background: linear-gradient(135deg, #e3f2fd 0%, #e1f5fe 100%);
  border-left-color: #2196f3;
}

.section-title, .result-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.disease-text, .medicine-text, .result-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  display: block;
}

/* 能量阈值模型样式 */
.threshold-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.threshold-item {
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  border: 2rpx solid #dee2e6;
}

.threshold-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.threshold-bar {
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.threshold-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
  border-radius: 6rpx;
  transition: width 0.8s ease;
}

.threshold-value {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.threshold-status {
  font-size: 26rpx;
  font-weight: 500;
  display: block;
}

/* 三重引动机制样式 */
.activation-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.activation-item {
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #ff9800;
}

.activation-item .item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.activation-item .item-desc {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  display: block;
}

/* 动态分析引擎样式 */
.dynamic-analysis {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.dynamic-analysis .analysis-item {
  padding: 24rpx;
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #9c27b0;
}

.dynamic-analysis .item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.dynamic-analysis .item-desc {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  display: block;
}

/* 应期预测样式 */
.period-prediction {
  padding: 24rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #4caf50;
  margin-top: 20rpx;
}

/* 🆕 统一应期结果显示样式 */
.unified-timing-results {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.timing-event-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
}

.timing-event-item:last-child {
  border-bottom: none;
}

.event-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
  flex-shrink: 0;
}

.event-result {
  font-size: 26rpx;
  color: #4caf50;
  font-weight: 600;
  flex: 1;
}

.event-result.threshold-not-met {
  color: #ff9800;
  font-weight: 400;
  font-style: italic;
}

.event-result.age-not-met {
  color: #9c27b0;
  font-weight: 400;
  font-style: italic;
}

.event-result.calculating {
  color: #2196f3;
  font-weight: 400;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* 🆕 统一应期结果显示样式 */
.unified-timing-results {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.timing-event-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
}

.timing-event-item:last-child {
  border-bottom: none;
}

.event-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
  flex-shrink: 0;
}

.event-result {
  font-size: 26rpx;
  color: #4caf50;
  font-weight: 600;
  flex: 1;
}

.event-result.threshold-not-met {
  color: #ff9800;
  font-weight: 400;
  font-style: italic;
}

/* 算法验证信息样式 */
.validation-info {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #9c27b0;
}

.validation-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.validation-stats {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

/* 引动综合评估样式 */
.activation-summary {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #e1f5fe 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #2196f3;
}

.summary-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.confidence-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.confidence-display:last-child {
  border-bottom: none;
}

.confidence-label {
  font-size: 26rpx;
  color: #555;
}

.confidence-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2196f3;
}

/* 地域文化适配样式 */
.cultural-adaptation {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #ff9800;
}

.adaptation-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.adaptation-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.adaptation-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

/* 历史案例验证样式 */
.historical-validation {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #e91e63;
}

.case-examples {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.case-item {
  padding: 16rpx;
  background: rgba(255,255,255,0.6);
  border-radius: 12rpx;
}

.case-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.case-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

.case-source {
  font-size: 22rpx;
  color: #999;
  font-style: italic;
  margin-top: 6rpx;
  display: block;
}

/* 数据库统计样式 */
.database-stats {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: rgba(255,255,255,0.4);
  border-radius: 12rpx;
}

.stats-item {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  display: block;
  margin-bottom: 4rpx;
}

/* 相似名人样式 */
.similar-celebrities {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.celebrity-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 🎨 优化名人库样式 - 改善文字间距和可读性 */
.celebrity-item {
  padding: 24rpx;
  background: rgba(255,255,255,0.95);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0,0,0,0.08);
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.celebrity-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
}

.celebrity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.celebrity-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  line-height: 1.4;
}

.similarity-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  min-width: 80rpx;
}

.similarity-text {
  font-size: 20rpx;
  color: rgba(255,255,255,0.8);
  margin-bottom: 2rpx;
}

.similarity-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.celebrity-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.detail-label {
  font-size: 26rpx;
  color: #7F8C8D;
  font-weight: 500;
  min-width: 80rpx;
  margin-right: 12rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #34495E;
  flex: 1;
  word-break: break-all;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 16rpx;
  border: 2rpx dashed #E0E0E0;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #7F8C8D;
  text-align: center;
}

/* 无结果状态样式 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 16rpx;
  border: 2rpx dashed #E0E0E0;
}

.no-results-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.no-results-text {
  font-size: 28rpx;
  color: #7F8C8D;
  text-align: center;
  margin-bottom: 20rpx;
}

.retry-button {
  font-size: 26rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
  justify-content: center;
}

.action-button {
  font-size: 26rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  text-align: center;
  transition: all 0.3s ease;
}

.action-button:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.action-button.secondary {
  color: #95A5A6;
  background: rgba(149, 165, 166, 0.1);
  border-color: rgba(149, 165, 166, 0.3);
}

.action-button.secondary:active {
  background: rgba(149, 165, 166, 0.2);
}

.name-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10rpx;
  flex: 1;
  margin-right: 16rpx;
}

.celebrity-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 1.5rpx;
  line-height: 1.4;
  margin-bottom: 2rpx;
}

.celebrity-dynasty {
  font-size: 26rpx;
  color: #7f8c8d;
  font-weight: 500;
  align-self: flex-end;
  margin-top: 4rpx;
  font-style: italic;
}

.similarity-score {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 700;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.25);
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.similarity-score::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.similarity-score:hover::before {
  left: 100%;
}

.celebrity-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
}

.celebrity-bazi, .celebrity-pattern, .celebrity-achievement {
  font-size: 26rpx;
  color: #34495e;
  line-height: 1.5;
  display: block;
  padding: 6rpx 0;
  letter-spacing: 0.5rpx;
}

.celebrity-bazi {
  font-family: 'Courier New', monospace;
  background: rgba(236, 240, 241, 0.5);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.celebrity-pattern {
  color: #8e44ad;
  font-weight: 500;
}

.celebrity-achievement {
  color: #27ae60;
  line-height: 1.6;
}

.timing-verification {
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  background: rgba(241, 196, 15, 0.05);
  padding: 12rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
}

.verification-event {
  font-size: 24rpx;
  color: #f39c12;
  font-weight: 500;
}

.verification-accuracy {
  font-size: 22rpx;
  color: #e67e22;
  font-weight: 400;
}

.verification-event, .verification-accuracy {
  font-size: 22rpx;
  color: #666;
  display: block;
}

/* 验证结果分解样式 */
.validation-breakdown {
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(255,255,255,0.4);
  border-radius: 12rpx;
}

.breakdown-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-label {
  font-size: 22rpx;
  color: #666;
}

.breakdown-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #4caf50;
}

.activation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

.activation-item.marriage-item {
  background: linear-gradient(135deg, #ffeef0 0%, #ffe0e6 100%);
  border-color: #ff9a9e;
}

.activation-item.promotion-item {
  background: linear-gradient(135deg, #e8f5ff 0%, #d4edff 100%);
  border-color: #4fc3f7;
}

.activation-item.wealth-item {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ffb74d;
}

.item-event {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  min-width: 60rpx;
}

.item-star {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  text-align: center;
}

.item-year {
  font-size: 22rpx;
  font-weight: 500;
  color: #667eea;
  min-width: 80rpx;
  text-align: right;
}

.palace-grid, .god-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.palace-item, .god-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.palace-name, .god-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  min-width: 80rpx;
}

.palace-action, .god-effect {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  text-align: center;
}

.palace-effect, .god-year {
  font-size: 22rpx;
  color: #667eea;
  min-width: 100rpx;
  text-align: right;
}

.god-item.auspicious {
  border-left-color: #ff9a9e;
}

.god-item.power {
  border-left-color: #4fc3f7;
}

.god-item.wealth {
  border-left-color: #ffb74d;
}

/* 🚀 现代动态分析引擎卡片样式 */
.dynamic-engine-modern-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.engine-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.engine-header .header-content {
  display: flex;
  align-items: center;
}

.engine-header .header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

.engine-header .card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 6rpx;
}

.engine-header .card-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.engine-status {
  padding: 8rpx 16rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

.dynamic-dashboard {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.linkage-panel, .force-panel, .turning-panel {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
}

.panel-header {
  text-align: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #dee2e6;
}

.panel-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.panel-subtitle {
  font-size: 22rpx;
  color: #666;
}

.linkage-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
}

.flow-node {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 8rpx;
}

.flow-node.original {
  background: linear-gradient(135deg, #ffeef0 0%, #ffe0e6 100%);
  border: 2rpx solid #ff9a9e;
}

.flow-node.dayun {
  background: linear-gradient(135deg, #e8f5ff 0%, #d4edff 100%);
  border: 2rpx solid #4fc3f7;
}

.flow-node.liunian {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border: 2rpx solid #ffb74d;
}

.node-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.node-content {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
}

.flow-arrow {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 700;
  margin: 0 12rpx;
}

.current-period {
  display: flex;
  gap: 16rpx;
}

.period-item {
  flex: 1;
  padding: 16rpx;
  background: white;
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid #e9ecef;
}

.period-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.period-value {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.period-years {
  font-size: 20rpx;
  color: #667eea;
}

.force-meters {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.force-meter {
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.meter-label {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.meter-bar {
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.meter-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.8s ease;
}

.meter-fill.dayun-fill {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.meter-fill.liunian-fill {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.meter-fill.age-fill {
  background: linear-gradient(90deg, #ffa726 0%, #ff7043 100%);
}

.meter-value {
  display: inline-block;
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
}

.meter-formula {
  font-size: 20rpx;
  color: #666;
  font-style: italic;
}

.turning-timeline {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.timeline-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.timeline-item.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f2ff 0%, #e8ebff 100%);
}

.timeline-year {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  min-width: 80rpx;
}

.timeline-event {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  text-align: center;
}

.timeline-confidence {
  font-size: 22rpx;
  font-weight: 600;
  color: #667eea;
  min-width: 60rpx;
  text-align: right;
}

/* 🎯 现代专业预测卡片样式 */
.professional-prediction-modern-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.prediction-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.prediction-header .header-content {
  display: flex;
  align-items: center;
}

.prediction-header .header-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
}

.prediction-header .card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 6rpx;
}

.prediction-header .card-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.prediction-dashboard {
  padding: 30rpx;
}

.prediction-explanation {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.explanation-title {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.explanation-content {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

.prediction-card {
  margin-bottom: 24rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
}

.prediction-card.marriage-prediction {
  background: linear-gradient(135deg, #ffeef0 0%, #ffe0e6 100%);
  border-color: #ff9a9e;
}

.prediction-card.promotion-prediction {
  background: linear-gradient(135deg, #e8f5ff 0%, #d4edff 100%);
  border-color: #4fc3f7;
}

.prediction-card.wealth-prediction {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ffb74d;
}

.prediction-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.prediction-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.prediction-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.prediction-probability {
  font-size: 22rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.prediction-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.detail-label {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  min-width: 80rpx;
}

.detail-value {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

/* 冲突分析和时机推荐样式 */
.conflict-recommendation-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #e9ecef;
}

.section-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.section-subtitle {
  font-size: 22rpx;
  color: #666;
}

.conflict-analysis, .optimal-timing {
  margin-bottom: 30rpx;
}

.analysis-title, .timing-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.conflict-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.conflict-card {
  padding: 20rpx;
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
}

.conflict-year {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #856404;
  margin-bottom: 8rpx;
}

.conflict-description {
  display: block;
  font-size: 22rpx;
  color: #856404;
  margin-bottom: 8rpx;
}

.conflict-suggestion {
  display: block;
  font-size: 22rpx;
  color: #495057;
  margin-bottom: 6rpx;
  line-height: 1.4;
}

.conflict-priority {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 500;
}

.timing-timeline {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.timeline-period {
  padding: 20rpx;
  background: #d4edda;
  border: 2rpx solid #c3e6cb;
  border-radius: 12rpx;
}

.period-year {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #155724;
  margin-bottom: 8rpx;
}

.period-focus {
  display: block;
  font-size: 22rpx;
  font-weight: 600;
  color: #155724;
  margin-bottom: 8rpx;
}

.period-advice {
  display: block;
  font-size: 22rpx;
  color: #495057;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.period-score {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 500;
}

/* 阈值说明样式 */
.threshold-explanation {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 12rpx;
}

.explanation-text {
  font-size: 22rpx;
  color: #495057;
  line-height: 1.4;
}

.metric-explanation {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-style: italic;
}

.metric-meaning {
  display: block;
  font-size: 20rpx;
  color: #495057;
  margin-top: 8rpx;
  font-weight: 500;
}

.reference-title {
  font-size: 20rpx;
  color: #666;
  margin-right: 8rpx;
}

/* 三重引动机制说明样式 */
.mechanism-explanation {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.explanation-title {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.explanation-content {
  font-size: 22rpx;
  line-height: 1.4;
  opacity: 0.9;
}

.item-trigger {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

.item-explanation {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-top: 6rpx;
  font-style: italic;
  text-align: center;
}

/* 🆕 专业应期分析样式 */
.professional-timing-container {
  margin-bottom: 40rpx;
}

.professional-mode-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #f8f4ff 0%, #e8f5e8 100%);
  border: 2rpx solid #9C27B0;
}

.mode-badge {
  font-size: 22rpx;
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 10rpx;
}

.mode-badge.legacy {
  color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
}

.analysis-summary {
  padding: 20rpx 0;
}

.summary-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
  margin-bottom: 15rpx;
}

.confidence-info {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
}

.confidence-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.confidence-value {
  font-size: 24rpx;
  font-weight: bold;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.confidence-value.high {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.confidence-value.medium {
  color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
}

.confidence-value.low {
  color: #F44336;
  background: rgba(244, 67, 54, 0.1);
}

.confidence-percentage {
  font-size: 22rpx;
  color: #999;
  margin-left: 5rpx;
}

/* 优先级事件样式 */
.priority-events-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.priority-event-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(255, 193, 7, 0.2);
}

.priority-event-item:last-child {
  border-bottom: none;
}

.event-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.event-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #E65100;
  margin-right: 15rpx;
}

.event-year {
  font-size: 26rpx;
  color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
}

.event-confidence {
  font-size: 22rpx;
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.event-reason {
  font-size: 24rpx;
  color: #555;
  line-height: 1.5;
}

.ancient-basis {
  font-size: 22rpx;
  color: #8D6E63;
  font-style: italic;
  background: rgba(141, 110, 99, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #8D6E63;
}

/* 专业建议样式 */
.professional-recommendations-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.recommendation-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(33, 150, 243, 0.2);
}

.recommendation-item:last-child {
  border-bottom: none;
}

.recommendation-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.recommendation-event {
  font-size: 26rpx;
  font-weight: bold;
  color: #1976D2;
  margin-right: 15rpx;
}

.recommendation-timing {
  font-size: 24rpx;
  color: #2196F3;
  background: rgba(33, 150, 243, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.recommendation-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.preparation-advice {
  font-size: 22rpx;
  color: #666;
  background: rgba(33, 150, 243, 0.05);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #2196F3;
}

/* 时间冲突样式 */
.timing-conflicts-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.conflict-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(244, 67, 54, 0.2);
}

.conflict-item:last-child {
  border-bottom: none;
}

.conflict-year {
  font-size: 26rpx;
  font-weight: bold;
  color: #D32F2F;
  margin-bottom: 8rpx;
}

.conflict-events {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 10rpx;
}

.conflict-event {
  font-size: 22rpx;
  color: #F44336;
  background: rgba(244, 67, 54, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.conflict-recommendation {
  font-size: 24rpx;
  color: #555;
  line-height: 1.5;
}

/* 🆕 增强版专业应期分析样式 */

/* 通用增强卡片样式 */
.enhanced-card {
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-card:hover {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}

.enhanced-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  transition: background 0.3s ease;
}

.enhanced-header:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.animated-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.header-text {
  display: flex;
  flex-direction: column;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.card-content.collapsed {
  transition: max-height 0.3s ease;
}

.card-content.expanded {
  transition: max-height 0.3s ease;
}

/* 神煞分析专用样式 */
.gods-analysis-card {
  border-left: 6rpx solid #9C27B0;
  margin-bottom: 20rpx;
}

.gods-analysis-card .subtitle {
  font-size: 24rpx;
  color: #9C27B0;
  margin-left: 10rpx;
}

.gods-overview {
  padding: 20rpx;
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  margin-bottom: 20rpx;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #9C27B0;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.enhanced-group {
  margin-bottom: 24rpx;
}

.event-title-enhanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.event-title-enhanced:active {
  background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
  transform: scale(0.98);
}

.event-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.event-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.event-badge {
  background: #9C27B0;
  color: white;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  min-width: 40rpx;
  text-align: center;
}

.badge-count {
  font-weight: bold;
}

.gods-list.collapsed {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.gods-list.expanded {
  max-height: 2000rpx;
  transition: max-height 0.3s ease;
}

.enhanced-god-item {
  margin-bottom: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border-left: 4rpx solid #9C27B0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-god-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.enhanced-god-header {
  margin-bottom: 12rpx;
}

.god-name-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.god-name {
  font-weight: bold;
  color: #9C27B0;
  font-size: 28rpx;
}

.god-status {
  display: flex;
  align-items: center;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.god-status.high {
  background: #e8f5e8;
  color: #2e7d32;
}

.god-status.medium {
  background: #fff3e0;
  color: #f57c00;
}

.god-status.low {
  background: #ffebee;
  color: #c62828;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 6rpx;
}

.god-status.high .status-dot {
  background: #4caf50;
}

.god-status.medium .status-dot {
  background: #ff9800;
}

.god-status.low .status-dot {
  background: #f44336;
}

.god-metrics {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.metric-label {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9C27B0 0%, #e1bee7 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-fill.activation {
  background: linear-gradient(90deg, #4caf50 0%, #81c784 100%);
}

.metric-value {
  font-size: 22rpx;
  color: #666;
  min-width: 50rpx;
  text-align: right;
}

.god-content {
  margin-top: 12rpx;
}

.god-description {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.ancient-reference {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.reference-icon {
  font-size: 20rpx;
  margin-top: 2rpx;
}

.ancient-basis {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.event-gods-section {
  margin-bottom: 30rpx;
}

.gods-event-group {
  border: 2rpx solid #E1BEE7;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #F3E5F5;
}

.event-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #9C27B0;
  margin-bottom: 15rpx;
  text-align: center;
}

.god-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: white;
  border-radius: 8rpx;
  border-left: 4rpx solid #9C27B0;
}

.god-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.god-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #9C27B0;
}

.god-weight, .activation-level {
  font-size: 24rpx;
  color: #666;
  background: #F5F5F5;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

.god-description {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.ancient-basis {
  font-size: 24rpx;
  color: #9C27B0;
  font-style: italic;
  display: block;
}

/* 🆕 病药分析卡片样式 - 增强版 */
.disease-medicine-card {
  border-left: 6rpx solid #4CAF50;
  margin-bottom: 20rpx;
}

.disease-medicine-card .subtitle {
  font-size: 24rpx;
  color: #4CAF50;
  margin-left: 10rpx;
}

/* 病药平衡概览 */
.balance-overview {
  padding: 24rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.balance-circle {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.circle-progress {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #4CAF50 0deg, #4CAF50 calc(var(--progress) * 3.6deg), #e0e0e0 calc(var(--progress) * 3.6deg), #e0e0e0 360deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.circle-progress::before {
  content: '';
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: white;
}

.balance-score {
  position: relative;
  z-index: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
  line-height: 1;
}

.balance-label {
  position: relative;
  z-index: 1;
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}

.balance-details {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.detail-item.disease .detail-icon {
  color: #f44336;
}

.detail-item.medicine .detail-icon {
  color: #4CAF50;
}

.detail-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 4rpx 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
}

/* 病药平衡指示器 */
.balance-indicator {
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.balance-indicator.good {
  background: #e8f5e8;
  color: #2e7d32;
}

.balance-indicator.medium {
  background: #fff3e0;
  color: #f57c00;
}

.balance-indicator.poor {
  background: #ffebee;
  color: #c62828;
}

/* 病药内容区域 */
.disease-medicine-content.collapsed {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.disease-medicine-content.expanded {
  max-height: 3000rpx;
  transition: max-height 0.3s ease;
}

.enhanced-section {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title-enhanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.section-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.section-badge {
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: white;
  min-width: 32rpx;
  text-align: center;
}

.disease-badge {
  background: #f44336;
}

.medicine-badge {
  background: #4CAF50;
}

.items-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.enhanced-item {
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-item:active {
  transform: scale(0.98);
  background: #e9ecef;
}

.disease-item.enhanced-item {
  border-left-color: #f44336;
}

.medicine-item.enhanced-item {
  border-left-color: #4CAF50;
}

.item-header {
  margin-bottom: 8rpx;
}

.item-name-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.disease-name, .medicine-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.severity-indicator, .effectiveness-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.severity-bar, .effectiveness-bar {
  width: 60rpx;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.severity-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.effectiveness-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #81c784 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.severity-text, .effectiveness-text {
  font-size: 20rpx;
  font-weight: bold;
}

.item-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.optimal-timing-section {
  display: flex;
  align-items: flex-start;
  gap: 6rpx;
  margin-bottom: 8rpx;
  padding: 8rpx;
  background: #e3f2fd;
  border-radius: 6rpx;
}

.timing-icon {
  font-size: 18rpx;
  margin-top: 2rpx;
}

.optimal-timing {
  font-size: 22rpx;
  color: #1976d2;
  line-height: 1.3;
  flex: 1;
}

/* 病药平衡总结 */
.balance-summary-section {
  margin-top: 20rpx;
}

.balance-summary-card {
  padding: 20rpx;
  background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
  border-radius: 12rpx;
  border: 2rpx solid #8bc34a;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.summary-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.summary-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.balance-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.balance-scale {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  width: 100%;
}

.scale-left, .scale-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.scale-left {
  background: #ffebee;
  border: 2rpx solid #ffcdd2;
}

.scale-right {
  background: #e8f5e8;
  border: 2rpx solid #c8e6c9;
}

.scale-left.heavy {
  transform: translateY(8rpx);
  box-shadow: 0 4rpx 8rpx rgba(244, 67, 54, 0.2);
}

.scale-right.heavy {
  transform: translateY(8rpx);
  box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.2);
}

.scale-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.scale-weight {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.scale-center {
  font-size: 32rpx;
  color: #666;
}

.balance-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.result-score {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.result-score.good {
  color: #4CAF50;
}

.result-score.medium {
  color: #ff9800;
}

.result-score.poor {
  color: #f44336;
}

.result-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.event-disease-section {
  margin-bottom: 30rpx;
}

.disease-event-group {
  border: 2rpx solid #C8E6C9;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #E8F5E8;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 15rpx;
  text-align: center;
  padding: 8rpx 0;
  border-bottom: 2rpx solid #4CAF50;
}

.diseases-section, .medicines-section {
  margin-bottom: 20rpx;
}

.disease-item, .medicine-item {
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: white;
  border-radius: 8rpx;
  border-left: 4rpx solid #F44336;
}

.medicine-item {
  border-left-color: #4CAF50;
}

.disease-header, .medicine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.disease-name, .medicine-name {
  font-size: 28rpx;
  font-weight: bold;
}

.disease-name {
  color: #F44336;
}

.medicine-name {
  color: #4CAF50;
}

.disease-severity, .medicine-effectiveness {
  font-size: 24rpx;
  color: #666;
  background: #F5F5F5;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

.disease-description, .medicine-description {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.optimal-timing {
  font-size: 24rpx;
  color: #4CAF50;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.balance-score-section {
  margin-top: 20rpx;
  padding: 15rpx;
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
  border-radius: 8rpx;
  text-align: center;
}

.balance-score {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}

.score-label {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: bold;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.score-value.good {
  background: #4CAF50;
  color: white;
}

.score-value.medium {
  background: #FF9800;
  color: white;
}

.score-value.poor {
  background: #F44336;
  color: white;
}

/* 🆕 文化语境适配卡片样式 - 增强版 */
.cultural-context-card {
  border-left: 6rpx solid #FF9800;
  margin-bottom: 20rpx;
}

.cultural-context-card .subtitle {
  font-size: 24rpx;
  color: #FF9800;
  margin-left: 10rpx;
}

/* 文化语境概览 */
.cultural-overview {
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.context-timeline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.timeline-item.active .timeline-dot {
  background: #FF9800;
  box-shadow: 0 0 0 4rpx rgba(255, 152, 0, 0.2);
}

.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ddd;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-period {
  font-size: 24rpx;
  font-weight: bold;
  color: #FF9800;
  margin-bottom: 4rpx;
}

.timeline-description {
  font-size: 20rpx;
  color: #666;
}

.timeline-connector {
  position: absolute;
  top: 8rpx;
  left: 50%;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #FF9800 0%, #ddd 100%);
  z-index: 0;
}

.timeline-item:last-child .timeline-connector {
  display: none;
}

/* 文化适配内容 */
.cultural-content.collapsed {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.cultural-content.expanded {
  max-height: 2000rpx;
  transition: max-height 0.3s ease;
}

.enhanced-cultural-info {
  padding: 20rpx;
}

.cultural-card {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.cultural-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.cultural-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.cultural-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.cultural-description {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.adaptation-factors {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.factor-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #FF9800;
}

.factor-icon {
  font-size: 24rpx;
  margin-top: 2rpx;
}

.factor-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.factor-label {
  font-size: 24rpx;
  font-weight: bold;
  color: #FF9800;
  margin-bottom: 4rpx;
}

.factor-value {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.adaptation-badge {
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: bold;
  background: #e8f5e8;
  color: #2e7d32;
}

.badge-text {
  font-size: 20rpx;
}

.event-cultural-section {
  margin-bottom: 30rpx;
}

.cultural-event-group {
  border: 2rpx solid #FFE0B2;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #FFF8E1;
}

.cultural-info {
  padding: 15rpx;
  background: white;
  border-radius: 8rpx;
  border-left: 4rpx solid #FF9800;
}

.cultural-description {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 传统应期分析样式（保持兼容） */
.legacy-timing-container {
  opacity: 0.9;
}

.current-timing-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
}

.current-timing-info {
  padding: 20rpx;
}

.timing-year {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.year-chars {
  display: flex;
  margin-right: 20rpx;
}

.year-char {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border-radius: 50%;
  margin-right: 10rpx;
  font-weight: bold;
  font-size: 28rpx;
}

.year-desc {
  color: #2e7d32;
  font-size: 28rpx;
  font-weight: bold;
}

.timing-summary {
  background: rgba(76, 175, 80, 0.1);
  padding: 15rpx;
  border-radius: 8rpx;
}

.summary-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #2e7d32;
  margin-bottom: 10rpx;
}

.summary-desc {
  color: #4caf50;
  line-height: 1.5;
}

/* 应期事件样式 */
.timing-events {
  padding: 20rpx;
}

.timing-event {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.timing-event.suitable {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
  border-color: #4CAF50;
}

.timing-event.unsuitable {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  border-color: #f39c12;
}

.timing-event.warning {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-color: #f44336;
}

.event-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.event-info {
  flex: 1;
}

.event-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.event-timing {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.event-desc {
  display: block;
  font-size: 24rpx;
  color: #888;
  line-height: 1.4;
}

.event-status {
  margin-left: 20rpx;
}

.status-text {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.suitable .status-text {
  background: #4CAF50;
}

.unsuitable .status-text {
  background: #f39c12;
}

.warning .status-text {
  background: #f44336;
}

/* 应期时间线样式 */
.timing-timeline {
  padding: 20rpx;
}

.timeline-year {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #007bff;
}

.year-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.year-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #007bff;
  margin-right: 20rpx;
}

.year-ganzhi {
  font-size: 28rpx;
  color: #6c757d;
}

.year-events {
  padding-left: 20rpx;
}

.year-event {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #dee2e6;
}

.year-event:last-child {
  border-bottom: none;
}

.event-type {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.event-level {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
}

.event-level.good {
  background: #28a745;
}

.event-level.normal {
  background: #6c757d;
}

.event-level.bad {
  background: #dc3545;
}

/* 六亲分析样式 */
.liuqin-panel {
  padding: 20rpx 20rpx 20rpx 0; /* 🔧 左内边距为0，让卡片与导航栏左边界对齐 */
}

.spouse-analysis-card,
.children-analysis-card,
.siblings-analysis-card,
.parents-analysis-card {
  margin-bottom: 20rpx;
}

.spouse-info,
.children-info,
.siblings-info,
.parents-info {
  padding: 20rpx;
}

.spouse-palace,
.children-palace {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
}

.palace-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15rpx;
}

.palace-chars {
  display: flex;
  margin-bottom: 15rpx;
}

.palace-char {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: white;
  border-radius: 50%;
  margin-right: 10rpx;
  font-weight: bold;
  font-size: 28rpx;
}

.palace-desc {
  font-size: 26rpx;
  color: #6c757d;
  text-align: center;
  line-height: 1.4;
}

.spouse-star,
.children-star,
.siblings-star {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #ffc107;
}

.star-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 10rpx;
}

.star-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #b8860b;
  margin-bottom: 8rpx;
}

.star-status {
  display: block;
  font-size: 26rpx;
  color: #856404;
  margin-bottom: 8rpx;
}

.star-desc {
  display: block;
  font-size: 24rpx;
  color: #6c5ce7;
  line-height: 1.4;
}

.marriage-luck,
.children-luck,
.siblings-luck {
  padding: 20rpx;
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #17a2b8;
}

.luck-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #0c5460;
  margin-bottom: 15rpx;
}

.luck-score {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.score-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #17a2b8;
  margin-right: 15rpx;
}

.score-level {
  font-size: 28rpx;
  font-weight: bold;
  color: #0c5460;
}

.luck-desc {
  font-size: 24rpx;
  color: #0c5460;
  line-height: 1.4;
}

.parent-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #6c757d;
}

.parent-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 10rpx;
}

.parent-desc {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.parent-influence {
  display: block;
  font-size: 24rpx;
  color: #868e96;
  line-height: 1.4;
}

.parents-overall {
  padding: 20rpx;
  background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #6c757d;
}

.overall-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 10rpx;
}

.overall-desc {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.4;
}

/* 兄弟分析特定样式 */
.siblings-relationship {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
}

.relationship-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #155724;
  margin-bottom: 10rpx;
}

.relationship-desc {
  display: block;
  font-size: 26rpx;
  color: #28a745;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.relationship-advice {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.4;
  padding: 10rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6rpx;
}

/* 六亲关系总结样式 */
.liuqin-summary-card {
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25rpx;
  padding: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  min-width: 120rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6c757d;
  text-align: center;
}

.summary-text {
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border-left: 4rpx solid #007bff;
}

.summary-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 15rpx;
}

.summary-desc {
  display: block;
  font-size: 26rpx;
  color: #495057;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.summary-advice {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.5;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

/* ==================== 十神分析和长生十二宫子模块样式 ==================== */

/* 子模块通用样式 */
.sub-module {
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  overflow: hidden;
  border: 2rpx solid rgba(218, 165, 32, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(218, 165, 32, 0.15);
}

.sub-module-header {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(184, 134, 11, 0.2));
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid rgba(218, 165, 32, 0.25);
}

.sub-module-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.sub-module-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #8B4513;
  letter-spacing: 2rpx;
  margin-right: 15rpx;
}

.sub-module-desc {
  font-size: 24rpx;
  color: #A0522D;
  opacity: 0.9;
  font-weight: 500;
}

.sub-module-content {
  padding: 30rpx;
}

/* 十神分析样式 */
.shishen-section {
  margin-bottom: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 主星配置样式 */
.main-stars-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.star-item.main-star {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
}

.star-label {
  font-size: 24rpx;
  color: #e0e0e0;
  min-width: 120rpx;
}

.star-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
  min-width: 80rpx;
  text-align: center;
}

.star-meaning {
  font-size: 22rpx;
  color: #cccccc;
  flex: 1;
  text-align: right;
}

/* 副星配置样式 */
.auxiliary-stars-grid {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.aux-star-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6rpx;
  padding: 10rpx 14rpx;
}

.aux-star-label {
  font-size: 22rpx;
  color: #e0e0e0;
  min-width: 100rpx;
}

.aux-star-list {
  font-size: 24rpx;
  color: #ffffff;
  flex: 1;
  text-align: right;
}

/* 格局分析样式 */
.pattern-analysis {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 10rpx;
  padding: 16rpx;
}

.pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.pattern-label {
  font-size: 24rpx;
  color: #e0e0e0;
  min-width: 120rpx;
}

.pattern-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
  flex: 1;
  text-align: right;
}

.pattern-summary {
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.summary-text {
  font-size: 24rpx;
  line-height: 1.6;
  color: #f0f0f0;
}

/* 强度徽章样式 */
.strength-badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  margin: 0 6rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  text-align: center;
  min-width: 40rpx;
  transition: all 0.3s ease;
}

.strength-badge.strong {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: #ffffff;
  border: 2rpx solid #27ae60;
  box-shadow: 0 2rpx 8rpx rgba(39, 174, 96, 0.3);
}

.strength-badge.medium {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: #ffffff;
  border: 2rpx solid #f39c12;
  box-shadow: 0 2rpx 8rpx rgba(243, 156, 18, 0.3);
}

.strength-badge.weak {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: #ffffff;
  border: 2rpx solid #e74c3c;
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
}

.strength-badge:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.star-category {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  padding: 25rpx;
  border: 2rpx solid rgba(218, 165, 32, 0.15);
}

.category-title {
  font-size: 26rpx;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(218, 165, 32, 0.2);
}

.star-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.star-item {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(255, 248, 225, 0.9));
  border: 2rpx solid rgba(218, 165, 32, 0.3);
  border-radius: 12rpx;
  padding: 10rpx 16rpx;
  transition: all 0.3s ease;
}

.star-item.noble {
  border-color: rgba(255, 215, 0, 0.5);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 248, 225, 0.9));
}

.star-item.shensha {
  border-color: rgba(138, 43, 226, 0.5);
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.15), rgba(248, 240, 255, 0.9));
}

.star-item.placeholder {
  opacity: 0.6;
  border-style: dashed;
}

.star-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(218, 165, 32, 0.25);
}

.star-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #8B4513;
}

.star-item.noble .star-name {
  color: #B8860B;
}

.star-item.shensha .star-name {
  color: #8A2BE2;
}

.star-item.ten-god {
  border-color: rgba(30, 144, 255, 0.5);
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.15), rgba(240, 248, 255, 0.9));
  flex-direction: column;
  align-items: center;
  padding: 15rpx 20rpx;
}

.star-item.ten-god .star-name {
  color: #1E90FF;
  font-size: 26rpx;
  font-weight: 700;
  margin-bottom: 5rpx;
}

.star-item.ten-god .star-desc {
  color: #4682B4;
  font-size: 20rpx;
  opacity: 0.8;
  text-align: center;
}

.auxiliary-summary {
  background: rgba(218, 165, 32, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid rgba(218, 165, 32, 0.2);
}

.auxiliary-summary .summary-text {
  font-size: 26rpx;
  color: #8B4513;
  font-weight: 600;
}

/* 星运分析样式 */
.star-fortune-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25rpx;
  margin-bottom: 25rpx;
}

.fortune-pillar {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  padding: 25rpx;
  border: 2rpx solid rgba(218, 165, 32, 0.15);
  text-align: center;
  transition: all 0.3s ease;
}

.fortune-pillar:hover {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 24rpx rgba(218, 165, 32, 0.2);
  border-color: rgba(218, 165, 32, 0.4);
}

.fortune-pillar.highlight {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(218, 165, 32, 0.2));
  border-color: #DAA520;
  border-width: 3rpx;
}

.pillar-name {
  font-size: 26rpx;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 15rpx;
  display: block;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid rgba(218, 165, 32, 0.2);
}

.fortune-state {
  font-size: 28rpx;
  font-weight: 800;
  color: #D2691E;
  margin-bottom: 12rpx;
  display: block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, rgba(210, 105, 30, 0.15), rgba(255, 248, 225, 0.9));
  border-radius: 10rpx;
  border: 2rpx solid rgba(210, 105, 30, 0.3);
}

.fortune-desc {
  font-size: 22rpx;
  color: #A0522D;
  line-height: 1.5;
  display: block;
  opacity: 0.9;
}

.star-fortune-summary {
  background: rgba(218, 165, 32, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid rgba(218, 165, 32, 0.2);
}

.star-fortune-summary .summary-text {
  font-size: 26rpx;
  color: #8B4513;
  font-weight: 600;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .auxiliary-stars-grid {
    flex-direction: column;
    gap: 20rpx;
  }

  .star-fortune-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }

  .sub-module-content {
    padding: 20rpx;
  }

  .star-category,
  .fortune-pillar {
    padding: 20rpx;
  }

  .sub-module-title {
    font-size: 28rpx;
  }

  .sub-module-desc {
    font-size: 22rpx;
  }
}

/* 四柱八字分析颜色样式 */
.pillar-highlight {
  color: #8B4513 !important;
  font-weight: 700 !important;
  font-size: 32rpx !important;
  text-shadow: 1rpx 1rpx 2rpx rgba(139, 69, 19, 0.3);
}

.day-master {
  color: #DC143C !important;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.1) 0%, rgba(220, 20, 60, 0.05) 100%);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(220, 20, 60, 0.3);
}

.day-master-text {
  color: #DC143C !important;
  font-weight: 700 !important;
}

.nayin-highlight {
  color: #4169E1 !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1) 0%, rgba(65, 105, 225, 0.05) 100%);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(65, 105, 225, 0.2);
}

.day-nayin {
  color: #FF6347 !important;
  font-weight: 700 !important;
  background: linear-gradient(135deg, rgba(255, 99, 71, 0.15) 0%, rgba(255, 99, 71, 0.08) 100%);
  border: 1rpx solid rgba(255, 99, 71, 0.3);
}

.main-qi-highlight {
  color: #228B22 !important;
  font-weight: 700 !important;
  font-size: 28rpx !important;
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.1) 0%, rgba(34, 139, 34, 0.05) 100%);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(34, 139, 34, 0.2);
  margin-right: 4rpx;
}

.day-qi {
  color: #FF4500 !important;
  background: linear-gradient(135deg, rgba(255, 69, 0, 0.15) 0%, rgba(255, 69, 0, 0.08) 100%);
  border: 1rpx solid rgba(255, 69, 0, 0.3);
}

.qi-label {
  color: #666666 !important;
  font-size: 20rpx !important;
  margin: 0 6rpx;
}

.hidden-qi-highlight {
  color: #9370DB !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, rgba(147, 112, 219, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(147, 112, 219, 0.2);
}

/* ==================== 专业级五行分析样式 ==================== */

/* 专业级五行卡片 */
.wuxing-enhanced-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.wuxing-enhanced-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.professional-badge {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 数据状态指示器 */
.data-status-indicator {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 15rpx;
  margin: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.status-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.status-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
}

.status-success .status-text {
  color: #4ecdc4;
  font-weight: bold;
}

.status-error .status-text {
  color: #f9ca24;
  font-weight: bold;
}

.status-info .status-text {
  color: #45b7d1;
  font-weight: 500;
}

/* 静态vs动态对比样式 */
.static-dynamic-comparison {
  margin: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.comparison-header {
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.comparison-header:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.section-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  flex: 1;
}

.section-subtitle {
  font-size: 22rpx;
  opacity: 0.8;
  margin-left: 10rpx;
}

.toggle-icon {
  font-size: 24rpx;
  margin-left: 15rpx;
  transition: transform 0.3s ease;
}

.comparison-content {
  padding: 25rpx;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.comparison-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.element-comparison {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.element-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.change-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.change-indicator.change-increase {
  background: linear-gradient(45deg, #4CAF50, #8BC34A);
  color: white;
}

.change-indicator.change-decrease {
  background: linear-gradient(45deg, #F44336, #FF5722);
  color: white;
}

.change-indicator.change-stable {
  background: linear-gradient(45deg, #9E9E9E, #607D8B);
  color: white;
}

.power-row {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.power-label {
  font-size: 22rpx;
  color: #666;
  width: 60rpx;
  text-align: center;
}

.power-bar {
  flex: 1;
  height: 16rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  overflow: hidden;
}

.power-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

.power-fill.static {
  background: linear-gradient(90deg, #9E9E9E, #BDBDBD);
}

.power-fill.dynamic {
  background: linear-gradient(90deg, #2196F3, #03DAC6);
}

.power-value {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  width: 80rpx;
  text-align: right;
}

/* 交互关系详情样式 */
.interaction-details {
  margin: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.interaction-header {
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.interaction-header:active {
  background: linear-gradient(135deg, #FF5252 0%, #26C6DA 100%);
}

.interaction-content {
  padding: 25rpx;
  animation: slideDown 0.3s ease;
}

.interaction-category {
  margin-bottom: 25rpx;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 15rpx;
  padding: 12rpx 18rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  border-left: 4rpx solid #667eea;
}

.category-icon {
  font-size: 24rpx;
}

.category-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.category-count {
  font-size: 20rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
}

.interaction-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.interaction-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
}

.interaction-icon {
  font-size: 22rpx;
}

.interaction-desc {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.interaction-effect {
  font-size: 22rpx;
  color: #666;
  text-align: right;
  max-width: 200rpx;
}

.strength-indicator {
  width: 8rpx;
  height: 40rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.strength-indicator.strength-strong {
  background: linear-gradient(180deg, #4CAF50, #8BC34A);
}

.strength-indicator.strength-medium {
  background: linear-gradient(180deg, #FF9800, #FFC107);
}

.strength-indicator.strength-weak {
  background: linear-gradient(180deg, #9E9E9E, #BDBDBD);
}

/* 影响评估结果样式 */
.impact-evaluation {
  margin: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.evaluation-header {
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  background: linear-gradient(135deg, #A8E6CF 0%, #88D8A3 100%);
  color: #2E7D32;
  cursor: pointer;
  transition: all 0.3s ease;
}

.evaluation-header:active {
  background: linear-gradient(135deg, #81C784 0%, #66BB6A 100%);
}

.evaluation-content {
  padding: 25rpx;
  animation: slideDown 0.3s ease;
}

.impact-metrics {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.metric-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.metric-icon {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  display: block;
}

.metric-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.metric-value {
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.metric-value.impact-high {
  color: #F44336;
}

.metric-value.impact-medium {
  color: #FF9800;
}

.metric-value.impact-low {
  color: #4CAF50;
}

.metric-value.direction-positive {
  color: #4CAF50;
}

.metric-value.direction-negative {
  color: #F44336;
}

.metric-value.direction-neutral {
  color: #9E9E9E;
}

.metric-value.trend-auspicious {
  color: #4CAF50;
}

.metric-value.trend-inauspicious {
  color: #F44336;
}

.metric-value.trend-neutral {
  color: #FF9800;
}

.metric-detail {
  font-size: 20rpx;
  color: #888;
  display: block;
}

.metric-bar {
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
  margin-top: 10rpx;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.detailed-metrics {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10rpx;
  padding: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 22rpx;
  color: #666;
}

.detail-value {
  font-size: 22rpx;
  font-weight: bold;
}

.detail-value.balance-improved {
  color: #4CAF50;
}

.detail-value.balance-declined {
  color: #F44336;
}

.detail-value.stability-stable {
  color: #4CAF50;
}

.detail-value.stability-affected {
  color: #FF9800;
}

.detail-value.stability-damaged {
  color: #F44336;
}

/* 个性化建议样式 */
.personalized-recommendations {
  margin: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.recommendations-header {
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  background: linear-gradient(135deg, #FFD54F 0%, #FFCA28 100%);
  color: #F57F17;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendations-header:active {
  background: linear-gradient(135deg, #FFCC02 0%, #FFC107 100%);
}

.recommendations-content {
  padding: 25rpx;
  animation: slideDown 0.3s ease;
}

.primary-recommendation {
  margin-bottom: 25rpx;
}

.recommendation-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 25rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.recommendation-card.primary {
  border-left: 4rpx solid #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.recommendation-icon {
  font-size: 28rpx;
  margin-bottom: 12rpx;
  display: block;
}

.recommendation-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.recommendation-content {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.confidence-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.confidence-label {
  font-size: 20rpx;
  color: #666;
}

.confidence-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.confidence-value {
  font-size: 20rpx;
  font-weight: bold;
  color: #4CAF50;
}

.recommendations-subtitle {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
  font-weight: 500;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.item-icon {
  font-size: 20rpx;
  margin-top: 2rpx;
}

.item-content {
  font-size: 22rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}

/* 响应式设计优化 */
@media screen and (max-height: 600px) {
  .tab-panel {
    padding: 20rpx 30rpx 40rpx 0; /* 🔧 移除左内边距，通过卡片margin对齐 */
  }

  .tianggong-tab-content::after {
    height: 30rpx;
  }
}

@media screen and (min-height: 800px) {
  .tab-panel {
    padding: 40rpx 30rpx 80rpx 30rpx; /* 🔧 保持与导航栏一致的左边距：30rpx */
  }

  .tianggong-tab-content::after {
    height: 50rpx;
  }
}

/* 确保在所有设备上的兼容性 */
.tianggong-main-content,
.tianggong-tab-content,
.tab-panel {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 操作按钮样式 */
.wuxing-actions {
  margin: 20rpx;
  padding: 20rpx 0;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  border: none;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.refresh-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.refresh-btn:active {
  background: linear-gradient(45deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(1rpx);
}

.refresh-btn[disabled] {
  background: linear-gradient(45deg, #BDBDBD 0%, #9E9E9E 100%);
  color: #666;
  transform: none;
}

.share-btn {
  background: linear-gradient(45deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
}

.share-btn:active {
  background: linear-gradient(45deg, #26C6DA 0%, #00ACC1 100%);
  transform: translateY(1rpx);
}

.btn-icon {
  font-size: 22rpx;
}

.btn-text {
  font-size: 22rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .comparison-grid {
    gap: 15rpx;
  }

  .element-comparison {
    padding: 15rpx;
  }

  .impact-metrics {
    gap: 15rpx;
  }

  .metric-card {
    padding: 15rpx;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12rpx;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* ==================== 小运分析样式 ==================== */

/* 小运卡片 */
.minor-fortune-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
  border: 2px solid #90EE90;
  box-shadow: 0 8px 32px rgba(144, 238, 144, 0.3);
}

.minor-fortune-card .card-badge {
  background: linear-gradient(45deg, #32CD32, #228B22);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 当前小运 */
.current-minor-fortune {
  background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid #90EE90;
}

.minor-fortune-header {
  margin-bottom: 20rpx;
}

.minor-fortune-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E8B57;
}

.minor-fortune-main {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.minor-fortune-chars {
  display: flex;
  gap: 10rpx;
}

.minor-fortune-char {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #32CD32, #228B22);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 4px 16px rgba(50, 205, 50, 0.4);
}

.minor-fortune-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.minor-fortune-direction {
  font-size: 28rpx;
  color: #2E8B57;
  font-weight: bold;
}

.minor-fortune-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 小运序列 */
.minor-fortune-sequence {
  margin-bottom: 30rpx;
}

.sequence-header {
  margin-bottom: 20rpx;
}

.sequence-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2E8B57;
}

.sequence-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
}

.sequence-item {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.sequence-item.current {
  background: linear-gradient(135deg, #32CD32, #228B22);
  color: white;
  border-color: #32CD32;
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(50, 205, 50, 0.4);
}

.sequence-age {
  font-size: 24rpx;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.sequence-chars {
  display: flex;
  justify-content: center;
  gap: 4rpx;
}

.sequence-char {
  font-size: 28rpx;
  font-weight: bold;
}

.sequence-item.current .sequence-char {
  color: white;
}

/* 小运说明 */
.minor-fortune-note {
  background: linear-gradient(135deg, #fffacd 0%, #fff8dc 100%);
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1px solid #daa520;
}

.note-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.note-icon {
  font-size: 32rpx;
}

.note-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #b8860b;
}

.note-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.note-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.note-basis {
  font-size: 24rpx;
  color: #b8860b;
  font-style: italic;
}

/* 小运不适用卡片 */
.minor-fortune-na-card {
  background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
  border: 2px solid #ffb3b3;
}

.na-content {
  display: flex;
  align-items: center;
  gap: 30rpx;
  padding: 30rpx;
}

.na-icon {
  font-size: 60rpx;
  color: #ff6b6b;
}

.na-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.na-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #d63031;
}

.na-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.na-age {
  font-size: 24rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .sequence-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .minor-fortune-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
  }

  .na-content {
    flex-direction: column;
    text-align: center;
    gap: 20rpx;
  }
}

/* ==================== 大运流程时间线样式 ==================== */

/* 大运流程卡片 */
.dayun-timeline-card {
  background: linear-gradient(135deg, #FFF8E1 0%, #F5F5DC 50%, #FFF8E1 100%);
  border: 2rpx solid rgba(218, 165, 32, 0.2);
  box-shadow:
    0 8rpx 24rpx rgba(218, 165, 32, 0.15),
    0 2rpx 8rpx rgba(218, 165, 32, 0.08),
    inset 0 1rpx 4rpx rgba(255, 255, 255, 0.5);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
}

.dayun-timeline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #DAA520 0%, #FFD700 50%, #FFA500 100%);
  z-index: 1;
}

.dayun-timeline-card .card-header {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.08), rgba(255, 215, 0, 0.03));
  border-bottom: 1rpx solid rgba(218, 165, 32, 0.15);
  padding: 25rpx 30rpx;
  position: relative;
  z-index: 2;
}

.dayun-timeline-card .card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #8B4513;
  letter-spacing: 0.5rpx;
}

.dayun-timeline-card .header-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

/* 大运时间线容器 */
.dayun-timeline {
  padding: 25rpx 30rpx 25rpx 50rpx;
  position: relative;
}

.dayun-timeline::before {
  content: '';
  position: absolute;
  left: 30rpx;
  top: 25rpx;
  bottom: 25rpx;
  width: 3rpx;
  background: linear-gradient(180deg, #DAA520 0%, #FFD700 50%, #FFA500 100%);
  border-radius: 2rpx;
  z-index: 1;
}

/* 时间线项目 */
.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  padding: 8rpx 0;
  padding-left: 0;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 17rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 3rpx solid #DAA520;
  background: #FFF8E1;
  z-index: 3;
  transition: all 0.3s ease;
}

/* 时间线状态样式 */
.timeline-item.past::before {
  background: #DAA520;
  border-color: #B8860B;
}

.timeline-item.current::before {
  background: #FFD700;
  border-color: #FFA500;
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.6);
  animation: pulse 2s infinite;
}

.timeline-item.future::before {
  background: #F5F5DC;
  border-color: #DDD;
}

@keyframes pulse {
  0% { box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.6); }
  50% { box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.6); }
}

/* 年龄标签 */
.timeline-age {
  flex: 0 0 100rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #8B4513;
  text-align: center;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.08), rgba(255, 215, 0, 0.03));
  padding: 10rpx 6rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(218, 165, 32, 0.15);
  margin-right: 20rpx;
  margin-left: 50rpx;
  line-height: 1.2;
}

/* 天干地支字符 */
.timeline-chars {
  display: flex;
  gap: 8rpx;
  margin-right: 20rpx;
}

.timeline-char {
  width: 50rpx;
  height: 50rpx;
  background: linear-gradient(135deg, #DAA520 0%, #FFD700 50%, #FFA500 100%);
  color: #2C1810;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 700;
  box-shadow: 0 3rpx 8rpx rgba(139, 69, 19, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.timeline-char::before {
  content: '';
  position: absolute;
  top: 1rpx;
  left: 1rpx;
  right: 1rpx;
  height: 12rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: 6rpx 6rpx 0 0;
}

/* 运势描述 */
.timeline-desc {
  flex: 1;
  font-size: 28rpx;
  color: #2C1810;
  font-weight: 500;
  line-height: 1.3;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8rpx;
  border-left: 3rpx solid #DAA520;
  box-shadow: 0 1rpx 4rpx rgba(139, 69, 19, 0.08);
}

/* 不同状态的样式变化 */
.timeline-item.past .timeline-age {
  color: #8D6E63;
  background: rgba(141, 110, 99, 0.06);
  border-color: rgba(141, 110, 99, 0.15);
}

.timeline-item.past .timeline-char {
  background: linear-gradient(135deg, #8D6E63 0%, #A1887F 100%);
  color: #FFF;
  box-shadow: 0 2rpx 6rpx rgba(141, 110, 99, 0.2);
}

.timeline-item.past .timeline-desc {
  color: #8D6E63;
  border-left-color: #8D6E63;
  background: rgba(141, 110, 99, 0.03);
}

.timeline-item.current .timeline-age {
  color: #FF6F00;
  background: rgba(255, 111, 0, 0.12);
  border-color: rgba(255, 111, 0, 0.3);
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 0, 0.15);
}

.timeline-item.current .timeline-char {
  background: linear-gradient(135deg, #FF6F00 0%, #FFB74D 100%);
  color: #FFF;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 0, 0.25);
  transform: scale(1.05);
}

.timeline-item.current .timeline-desc {
  color: #E65100;
  border-left-color: #FF6F00;
  font-weight: 600;
  background: rgba(255, 111, 0, 0.08);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 0, 0.1);
}

.timeline-item.current::before {
  background: #FF6F00;
  border-color: #FF8F00;
  box-shadow: 0 0 15rpx rgba(255, 111, 0, 0.4);
  animation: pulse 2s infinite;
}

.timeline-item.future .timeline-age {
  color: #9E9E9E;
  background: rgba(158, 158, 158, 0.05);
  border-color: rgba(158, 158, 158, 0.12);
}

.timeline-item.future .timeline-char {
  background: linear-gradient(135deg, #BDBDBD 0%, #E0E0E0 100%);
  color: #757575;
  box-shadow: 0 1rpx 4rpx rgba(189, 189, 189, 0.15);
}

.timeline-item.future .timeline-desc {
  color: #9E9E9E;
  border-left-color: #BDBDBD;
  background: rgba(158, 158, 158, 0.02);
}

/* ==================== 专业级大运分析样式 ==================== */

/* 专业级大运卡片 */
.professional-dayun-card {
  background: linear-gradient(135deg, #f8f4ff 0%, #fff8f0 100%);
  border: 2px solid #9370DB;
  box-shadow: 0 8px 32px rgba(147, 112, 219, 0.3);
  margin-bottom: 30rpx;
}

.professional-dayun-card .card-badge.professional {
  background: linear-gradient(45deg, #9370DB, #6A5ACD);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 起运信息 */
.qiyun-info {
  background: linear-gradient(135deg, #f5f0ff 0%, #faf5ff 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid #DDA0DD;
}

.qiyun-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.qiyun-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #6A5ACD;
}

.qiyun-badge {
  background: linear-gradient(45deg, #9370DB, #8A2BE2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.qiyun-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.qiyun-desc {
  font-size: 28rpx;
  color: #4B0082;
  line-height: 1.5;
}

.qiyun-method {
  font-size: 24rpx;
  color: #8A2BE2;
  font-style: italic;
}

/* 当前大运状态 */
.current-dayun-status {
  background: linear-gradient(135deg, #fff0f8 0%, #ffeef8 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid #DDA0DD;
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B008B;
}

.status-progress {
  background: linear-gradient(45deg, #FF69B4, #FF1493);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.status-main {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.status-chars {
  display: flex;
  gap: 10rpx;
}

.status-char {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #9370DB, #8A2BE2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 4px 16px rgba(147, 112, 219, 0.4);
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.status-age {
  font-size: 28rpx;
  color: #6A5ACD;
  font-weight: bold;
}

.status-remaining {
  font-size: 26rpx;
  color: #8A2BE2;
}

/* 大运序列预览 */
.dayun-sequence-preview {
  margin-bottom: 30rpx;
}

.sequence-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.sequence-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4B0082;
}

.sequence-direction {
  font-size: 26rpx;
  color: #8A2BE2;
  background: linear-gradient(45deg, #E6E6FA, #DDA0DD);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.sequence-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.sequence-item {
  background: linear-gradient(135deg, #f8f8ff 0%, #f0f0ff 100%);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 1px solid #E6E6FA;
  transition: all 0.3s ease;
}

.sequence-item.current {
  background: linear-gradient(135deg, #9370DB, #8A2BE2);
  color: white;
  border-color: #6A5ACD;
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(147, 112, 219, 0.4);
}

.sequence-step {
  font-size: 24rpx;
  color: #8A2BE2;
  margin-bottom: 8rpx;
}

.sequence-item.current .sequence-step {
  color: #E6E6FA;
}

.sequence-chars {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.sequence-char {
  font-size: 28rpx;
  font-weight: bold;
  color: #4B0082;
}

.sequence-item.current .sequence-char {
  color: white;
}

.sequence-age {
  font-size: 24rpx;
  color: #6A5ACD;
}

.sequence-item.current .sequence-age {
  color: #E6E6FA;
}

/* 专业特性说明 */
.professional-features {
  background: linear-gradient(135deg, #fffacd 0%, #fff8dc 100%);
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1px solid #daa520;
}

.features-header {
  margin-bottom: 16rpx;
}

.features-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #B8860B;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.feature-icon {
  color: #228B22;
  font-weight: bold;
  font-size: 24rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #8B4513;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .sequence-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .status-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
  }

  .qiyun-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .status-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
}

/* 专业级流年分析样式 */
.professional-liunian {
  margin-top: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.professional-liunian .card-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx 20rpx 0 0;
}

.professional-liunian .card-title {
  color: white;
  font-weight: bold;
}

/* 当前流年概览 */
.current-liunian-overview {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(10rpx);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.overview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.overview-year {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.overview-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-ganzhi {
  display: flex;
  gap: 12rpx;
}

.overview-ganzhi .ganzhi-char {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.overview-analysis {
  text-align: right;
}

.overview-level {
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  display: inline-block;
}

.overview-score {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 专业流年列表 */
.professional-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.professional-list .liunian-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.professional-list .liunian-item.current-year {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.liunian-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.professional-list .liunian-year {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.professional-list .liunian-age {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.professional-list .liunian-chars {
  display: flex;
  gap: 8rpx;
}

.professional-list .liunian-char {
  width: 50rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.analysis-main {
  margin-bottom: 16rpx;
}

.professional-list .liunian-title {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.ten-god-icon {
  font-size: 24rpx;
}

.professional-list .liunian-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.analysis-details {
  margin-bottom: 16rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

.shensha-text {
  color: #ffd700 !important;
}

.analysis-advice {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx;
  border-left: 4rpx solid rgba(255, 255, 255, 0.3);
}

.advice-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8rpx;
}

.advice-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 运势等级颜色 */
.level-excellent {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.level-good {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}

.level-stable {
  background: linear-gradient(135deg, #45b7d1, #3498db);
  color: white;
}

.level-poor {
  background: linear-gradient(135deg, #f9ca24, #f0932b);
  color: white;
}

.level-bad {
  background: linear-gradient(135deg, #6c5ce7, #5f3dc4);
  color: white;
}

/* 流年统计摘要 */
.liunian-summary {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
  backdrop-filter: blur(10rpx);
}

.summary-title {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  text-align: center;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.summary-value {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

.best-year {
  color: #4ecdc4 !important;
}

.worst-year {
  color: #f9ca24 !important;
}

/* 🔧 新增：加载状态样式 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  margin-top: 24rpx;
  backdrop-filter: blur(10rpx);
}

.loading-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #4ecdc4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🔧 新增：错误状态样式 */
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  background: rgba(249, 202, 36, 0.1);
  border: 2rpx solid rgba(249, 202, 36, 0.3);
  border-radius: 16rpx;
  margin-top: 24rpx;
  backdrop-filter: blur(10rpx);
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 26rpx;
  color: #f9ca24;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-align: center;
}

.error-detail {
  font-size: 22rpx;
  color: rgba(249, 202, 36, 0.7);
  text-align: center;
}

/* 🚀 增强版界面样式 */

/* 增强算法标识 */
.enhanced-badge {
  background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  margin-left: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.badge-text {
  color: white !important;
}

/* 格局详细信息 */
.pattern-details {
  margin-top: 24rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
  border-left: 4rpx solid #D4AF37;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 8rpx 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #8B4513 !important;
  font-size: 24rpx;
  font-weight: 500;
}

.detail-value {
  color: #2C1810 !important;
  font-size: 24rpx;
  font-weight: bold;
}

/* 用神决策过程 */
.yongshen-decision {
  margin-top: 24rpx;
  padding: 20rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.3);
}

.decision-header {
  margin-bottom: 16rpx;
}

.decision-title {
  color: #8B4513 !important;
  font-size: 26rpx;
  font-weight: bold;
}

.decision-steps {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.decision-step {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.step-number {
  background: #D4AF37;
  color: white !important;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-desc {
  color: #2C1810 !important;
  font-size: 24rpx;
  line-height: 1.5;
  flex: 1;
}

/* 元素优先级标识 */
.element-priority {
  background: rgba(212, 175, 55, 0.2);
  color: #8B4513 !important;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  margin-left: 8rpx;
}

/* 建议详情 */
.advice-details {
  margin-top: 12rpx;
  padding-left: 16rpx;
}

.advice-details .detail-item {
  color: #5D4E37 !important;
  font-size: 22rpx;
  line-height: 1.6;
  margin-bottom: 8rpx;
  display: block;
}

/* 建议置信度 */
.advice-confidence {
  margin-top: 24rpx;
  padding: 16rpx;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.confidence-label {
  color: #4CAF50 !important;
  font-size: 24rpx;
  font-weight: 500;
}

.confidence-value {
  color: #2E7D32 !important;
  font-size: 26rpx;
  font-weight: bold;
}

/* 🏛️ 历史名人验证模块样式 */

/* 历史验证卡片 */
.historical-verification-card {
  margin-top: 32rpx !important;
}

/* 相似度结果 */
.similarity-results {
  margin-bottom: 32rpx;
}

.results-header {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #F3E5F5 0%, #E8F5E8 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #9C27B0;
}

.results-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #4A148C !important;
  margin-bottom: 8rpx;
}

.results-desc {
  display: block;
  font-size: 26rpx;
  color: #6A1B9A !important;
  line-height: 1.5;
}

/* 名人列表 */
.celebrity-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.celebrity-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
  border-radius: 20rpx;
  border: 2rpx solid #E0E0E0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.celebrity-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 名人头像 */
.celebrity-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-text {
  color: #FFFFFF !important;
  font-size: 32rpx;
  font-weight: bold;
}

/* 名人信息 */
.celebrity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.celebrity-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1A1A1A !important;
}

.celebrity-title {
  font-size: 26rpx;
  color: #666666 !important;
}

.celebrity-pattern {
  font-size: 24rpx;
  color: #9C27B0 !important;
  background: #F3E5F5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

/* 相似度分数 */
.similarity-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  flex-shrink: 0;
}

.score-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50 !important;
}

.score-level {
  font-size: 20rpx;
  color: #2E7D32 !important;
  background: #E8F5E8;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  gap: 16rpx;
}

.loading-icon {
  font-size: 48rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666 !important;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  gap: 16rpx;
}

.empty-icon {
  font-size: 64rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999999 !important;
}

.retry-btn {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%) !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 24rpx !important;
  padding: 16rpx 32rpx !important;
  font-size: 26rpx !important;
  margin-top: 16rpx;
}

/* 验证统计 */
.verification-stats {
  margin-top: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #E3F2FD 0%, #F1F8E9 100%);
  border-radius: 16rpx;
  border-left: 6rpx solid #2196F3;
}

.stats-header {
  margin-bottom: 20rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976D2 !important;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976D2 !important;
}

.stat-label {
  font-size: 22rpx;
  color: #666666 !important;
  text-align: center;
}

/* 操作按钮 */
.verification-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  border: none !important;
  border-radius: 24rpx !important;
  padding: 20rpx 24rpx !important;
  font-size: 26rpx !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%) !important;
  color: #FFFFFF !important;
}

.action-btn.secondary {
  background: #FFFFFF !important;
  color: #9C27B0 !important;
  border: 2rpx solid #9C27B0 !important;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 🆕 重新设计的专业流年分析模块样式 */
.new-professional-liunian-card {
  margin: 30rpx 20rpx;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  color: #333333;
}

/* 卡片头部 */
.new-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.header-icon {
  font-size: 32rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.professional-tag {
  background: #667eea;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #4CAF50;
}

.status-dot.error {
  background: #f44336;
}

/* 调试面板 */
.debug-panel {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.debug-title {
  display: block;
  font-size: 24rpx;
  margin-bottom: 16rpx;
  opacity: 0.8;
}

.debug-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.debug-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

/* 🆕 流年统计摘要样式 */
.new-liunian-summary {
  margin: 30rpx;
  padding: 0;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.summary-icon {
  font-size: 28rpx;
}

.summary-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.summary-grid {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.summary-card {
  flex: 1;
  min-width: 200rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 28rpx 20rpx;
  text-align: center;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.summary-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.summary-card.average {
  border-left: 4rpx solid #4CAF50;
}

.summary-card.best {
  border-left: 4rpx solid #FF9800;
}

.summary-card.worst {
  border-left: 4rpx solid #f44336;
}

.card-label {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 12rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.card-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333333;
  line-height: 1.2;
}

.card-score {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 400;
  margin-top: 4rpx;
}

/* 🆕 当前流年状态样式 */
.new-current-year {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.current-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.current-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.current-ganzhi {
  display: flex;
  gap: 8rpx;
}

.gan, .zhi {
  background: #e9ecef;
  color: #495057;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.current-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-level {
  font-size: 26rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #e9ecef;
  color: #495057;
}

.status-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
}

/* 🆕 流年详细列表样式 */
.new-liunian-list {
  margin: 30rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.list-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-count {
  font-size: 22rpx;
  color: #6c757d;
  background: #e9ecef;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.liunian-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.new-liunian-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
  margin-bottom: 16rpx;
}

.new-liunian-item.is-current {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
  box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.3);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.year-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.year {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.age {
  font-size: 22rpx;
  color: #6c757d;
  margin-top: 4rpx;
}

.ganzhi-info {
  display: flex;
  gap: 8rpx;
}

.ganzhi-info .gan,
.ganzhi-info .zhi {
  background: #e9ecef;
  color: #495057;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.score-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.score {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFD700;
}

.level {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-top: 4rpx;
  font-weight: 500;
}

.level-excellent {
  background: #4CAF50;
  color: white;
}

.level-good {
  background: #FF9800;
  color: white;
}

.level-average {
  background: #2196F3;
  color: white;
}

.level-poor {
  background: #f44336;
  color: white;
}

.item-content {
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  padding-top: 16rpx;
}

.main-analysis {
  margin-bottom: 12rpx;
}

.ten-god {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #FFD700;
}

.description {
  display: block;
  font-size: 24rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.extra-info {
  margin: 12rpx 0;
  padding: 12rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8rpx;
}

.shensha-label {
  font-size: 22rpx;
  opacity: 0.7;
}

.shensha-value {
  font-size: 22rpx;
  color: #FF9800;
  font-weight: 500;
}

.advice-section {
  margin-top: 12rpx;
  padding: 12rpx;
  background: rgba(76, 175, 80, 0.2);
  border-radius: 8rpx;
  border-left: 3rpx solid #4CAF50;
}

.advice-label {
  font-size: 22rpx;
  font-weight: bold;
  color: #4CAF50;
}

.advice-content {
  display: block;
  font-size: 22rpx;
  line-height: 1.4;
  margin-top: 6rpx;
  opacity: 0.9;
}

/* 🆕 加载状态样式 */
.new-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 26rpx;
  color: white;
  opacity: 0.8;
  margin-bottom: 30rpx;
}

.loading-progress {
  width: 200rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2rpx;
  overflow: hidden;
}

.loading-progress::after {
  content: '';
  display: block;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: loading-slide 1.5s infinite;
}

@keyframes loading-slide {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(300%); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 🆕 错误状态样式 */
.new-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  color: #f44336;
}

.error-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.error-desc {
  font-size: 24rpx;
  opacity: 0.7;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.error-actions {
  display: flex;
  gap: 20rpx;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 🆕 空数据状态样式 */
.new-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  opacity: 0.7;
  line-height: 1.4;
}

/* ==================== 优化的五行模块样式 ==================== */

/* 专业级五行分析卡片优化 */
.professional-wuxing-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid #dee2e6;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.professional-wuxing-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.professional-wuxing-card .header-icon {
  font-size: 32rpx;
}

.professional-wuxing-card .card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
}

/* 五行力量分布样式优化 */
.power-distribution {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  margin: 20rpx;
  border: 1rpx solid #e2e8f0;
}

.dist-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 24rpx;
  text-align: center;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #e2e8f0;
}

.power-bars {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.power-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx 0;
  transition: all 0.3s ease;
}

.power-item:hover {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  padding: 12rpx;
  margin: 0 -12rpx;
}

.element-name {
  width: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 8rpx 0;
  border: 1rpx solid #dee2e6;
}

.power-bar {
  flex: 1;
  height: 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
  border: 1rpx solid #dee2e6;
  position: relative;
}

.power-fill {
  height: 100%;
  border-radius: 16rpx;
  transition: width 0.8s ease;
  position: relative;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
}

.power-fill.wood {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  border: 1rpx solid #229954;
}

.power-fill.fire {
  background: linear-gradient(90deg, #e74c3c, #f39c12);
  border: 1rpx solid #cb4335;
}

.power-fill.earth {
  background: linear-gradient(90deg, #d68910, #f4d03f);
  border: 1rpx solid #b7950b;
}

.power-fill.metal {
  background: linear-gradient(90deg, #85929e, #aeb6bf);
  border: 1rpx solid #717d8a;
}

.power-fill.water {
  background: linear-gradient(90deg, #3498db, #5dade2);
  border: 1rpx solid #2980b9;
}

.power-value {
  width: 80rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: right;
  color: #2c3e50;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  border: 1rpx solid #dee2e6;
}

/* 平衡指数分析样式优化 */
.balance-analysis {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  margin: 20rpx;
  border: 1rpx solid #e2e8f0;
}

.balance-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
  text-align: center;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #e2e8f0;
}

.balance-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
  gap: 16rpx;
}

.balance-item {
  flex: 1;
  text-align: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  border: 1rpx solid #dee2e6;
}

.balance-label {
  font-size: 22rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
  display: block;
}

.balance-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #2c3e50;
  display: block;
}

.balance-status {
  font-size: 28rpx;
  font-weight: 700;
  color: #e74c3c;
  display: block;
}

.strongest-weakest {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.strong-label, .weak-label {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.strong-label {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1rpx solid #c3e6cb;
}

.weak-label {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1rpx solid #f5c6cb;
}

/* 五行强弱分析卡片优化 */
.wuxing-strength-card {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 2rpx solid #fc8181;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.wuxing-strength-card .card-header {
  background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
  color: white;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.strength-chart {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  margin: 20rpx;
  border: 1rpx solid #e2e8f0;
}

.strength-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f7fafc;
  transition: all 0.3s ease;
}

.strength-item:last-child {
  border-bottom: none;
}

.strength-item:hover {
  background: rgba(229, 62, 62, 0.05);
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  margin: 0 -12rpx;
}

.strength-bar {
  flex: 1;
  height: 28rpx;
  background: #f7fafc;
  border-radius: 14rpx;
  overflow: hidden;
  border: 1rpx solid #e2e8f0;
}

.strength-level {
  width: 100rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #f7fafc;
  color: #2d3748;
  border: 1rpx solid #e2e8f0;
}

.wuxing-balance {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  border: 2rpx solid #4fd1c7;
  margin: 20rpx;
}

.balance-score {
  font-size: 48rpx;
  font-weight: 700;
  color: #2c7a7b;
  margin-bottom: 8rpx;
  display: block;
}

.balance-desc {
  font-size: 24rpx;
  color: #4a5568;
  display: block;
}

.wuxing-summary {
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  border-radius: 12rpx;
  margin: 20rpx;
  border: 1rpx solid #e2e8f0;
}

.summary-text {
  font-size: 26rpx;
  color: #2d3748;
  line-height: 1.6;
  text-align: center;
}

/* 五行动态交互卡片优化 */
.wuxing-interaction-card {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border: 2rpx solid #68d391;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.wuxing-interaction-card .card-header {
  background: linear-gradient(135deg, #38a169 0%, #68d391 100%);
  color: white;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.interaction-analysis {
  padding: 30rpx;
}

.interaction-section {
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.interaction-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #e2e8f0;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #38a169, #68d391);
}

.interaction-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.interaction-item {
  background: #f7fafc;
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
}

.interaction-item:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.interaction-item.sanhui {
  border-left: 4rpx solid #f56565;
}

.interaction-item.sanhe {
  border-left: 4rpx solid #ed8936;
}

.interaction-item.liuhe {
  border-left: 4rpx solid #38b2ac;
}

.interaction-item.liuchong {
  border-left: 4rpx solid #9f7aea;
}

.interaction-type {
  font-size: 26rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
  display: block;
}

.interaction-desc {
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 6rpx;
  display: block;
  line-height: 1.4;
}

.interaction-effect {
  font-size: 22rpx;
  color: #718096;
  font-style: italic;
  display: block;
  line-height: 1.3;
}

.no-interaction {
  text-align: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  border: 2rpx dashed #cbd5e0;
}

.no-interaction-text {
  font-size: 28rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 600;
}

.no-interaction-desc {
  font-size: 24rpx;
  color: #718096;
  display: block;
  line-height: 1.5;
}
