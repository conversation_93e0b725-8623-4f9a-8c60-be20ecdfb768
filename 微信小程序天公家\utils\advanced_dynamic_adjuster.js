/**
 * 高级动态力量调整算法
 * 基于传统命理学理论，实现更精确的五行力量动态调整
 * 
 * 核心功能：
 * 1. 智能优先级管理 (会>合>冲>刑)
 * 2. 月令支持度判断 (合化成功/失败)
 * 3. 位置影响系数 (年月日时不同权重)
 * 4. 累积效应计算 (多重交互叠加)
 * 5. 平衡性检查 (防止过度调整)
 */

class AdvancedDynamicAdjuster {
  constructor() {
    this.initializeAdvancedCoefficients();
    this.initializePositionWeights();
    this.initializeSeasonalSupport();
  }

  /**
   * 初始化高级调整系数
   */
  initializeAdvancedCoefficients() {
    // 基础调整系数
    this.BASE_COEFFICIENTS = {
      threeDirectional: { boost: 3.0, suppress: 0.2 },
      threeHarmony: { boost: 2.5, suppress: 0.3 },
      sixCombination: { boost: 1.2, suppress: 0.8 },
      fiveCombination: { boost: 1.15, suppress: 0.85 },
      sixClash: { near: 0.4, far: 0.7 },
      threePunishment: { suppress: 0.7 }
    };

    // 位置修正系数 (年月日时的重要性不同)
    this.POSITION_MODIFIERS = {
      0: 0.8,  // 年柱 - 影响较小
      1: 1.2,  // 月柱 - 月令最重要
      2: 1.0,  // 日柱 - 标准权重
      3: 0.9   // 时柱 - 影响中等
    };

    // 累积效应衰减系数 (防止过度调整)
    this.ACCUMULATION_DECAY = {
      first: 1.0,    // 第一次调整 - 全效
      second: 0.8,   // 第二次调整 - 80%效果
      third: 0.6,    // 第三次调整 - 60%效果
      fourth: 0.4    // 第四次及以上 - 40%效果
    };
  }

  /**
   * 初始化位置权重
   */
  initializePositionWeights() {
    this.PILLAR_IMPORTANCE = {
      year: { weight: 0.8, description: '祖上根基' },
      month: { weight: 1.2, description: '月令司权' },
      day: { weight: 1.0, description: '日主本体' },
      hour: { weight: 0.9, description: '子息后代' }
    };
  }

  /**
   * 初始化季节支持度
   */
  initializeSeasonalSupport() {
    // 月令对合化的支持度
    this.SEASONAL_SUPPORT = {
      '春': { '木': 'strong', '火': 'medium', '土': 'weak', '金': 'weak', '水': 'medium' },
      '夏': { '火': 'strong', '土': 'medium', '金': 'weak', '水': 'weak', '木': 'medium' },
      '秋': { '金': 'strong', '水': 'medium', '木': 'weak', '火': 'weak', '土': 'medium' },
      '冬': { '水': 'strong', '木': 'medium', '火': 'weak', '土': 'weak', '金': 'medium' }
    };

    this.SUPPORT_MULTIPLIERS = {
      'strong': 1.3,   // 强支持 - 合化成功
      'medium': 1.0,   // 中等支持 - 半合化
      'weak': 0.7      // 弱支持 - 合化失败，变合绊
    };
  }

  /**
   * 高级动态力量调整主方法
   * @param {Object} staticPowers - 静态五行力量
   * @param {Object} interactions - 动态交互关系
   * @param {string} season - 当前季节
   * @returns {Object} 高级调整结果
   */
  performAdvancedAdjustment(staticPowers, interactions, season = '夏') {
    console.log('\n🔧 开始高级动态力量调整...');
    console.log('📊 输入静态力量:', staticPowers);
    console.log('🌸 当前季节:', season);

    // 深拷贝静态力量
    const adjustedPowers = JSON.parse(JSON.stringify(staticPowers));
    const adjustmentHistory = [];
    const elementAdjustmentCount = {};

    // 初始化调整计数
    Object.keys(adjustedPowers).forEach(element => {
      elementAdjustmentCount[element] = 0;
    });

    // 按优先级顺序应用调整
    const adjustmentSequence = [
      { type: 'threeDirectional', interactions: interactions.threeDirectional },
      { type: 'threeHarmony', interactions: interactions.threeHarmony },
      { type: 'sixCombinations', interactions: interactions.sixCombinations },
      { type: 'fiveCombinations', interactions: interactions.fiveCombinations },
      { type: 'sixClashes', interactions: interactions.sixClashes },
      { type: 'threePunishments', interactions: interactions.threePunishments }
    ];

    adjustmentSequence.forEach(({ type, interactions: typeInteractions }) => {
      if (typeInteractions && typeInteractions.length > 0) {
        console.log(`\n🔄 处理 ${type} (${typeInteractions.length}个)`);
        
        typeInteractions.forEach(interaction => {
          const adjustment = this.calculateSingleAdjustment(
            interaction, 
            type, 
            season, 
            elementAdjustmentCount
          );
          
          this.applyAdjustment(adjustedPowers, adjustment, adjustmentHistory);
          
          // 更新调整计数
          if (adjustment.affectedElements) {
            adjustment.affectedElements.forEach(element => {
              elementAdjustmentCount[element]++;
            });
          }
        });
      }
    });

    // 平衡性检查
    const balanceCheck = this.performBalanceCheck(staticPowers, adjustedPowers);

    console.log('\n📊 高级调整完成');
    console.log('最终力量:', adjustedPowers);
    console.log('平衡性检查:', balanceCheck.status);

    return {
      adjustedPowers,
      adjustmentHistory,
      balanceCheck,
      elementAdjustmentCount,
      totalAdjustments: adjustmentHistory.length
    };
  }

  /**
   * 计算单个交互的调整参数
   */
  calculateSingleAdjustment(interaction, type, season, adjustmentCount) {
    const adjustment = {
      type: type,
      interaction: interaction,
      season: season,
      adjustments: [],
      affectedElements: []
    };

    switch (type) {
      case 'threeDirectional':
        adjustment.adjustments.push(this.calculateDirectionalAdjustment(interaction, adjustmentCount));
        adjustment.affectedElements.push(interaction.element);
        break;

      case 'threeHarmony':
        adjustment.adjustments.push(this.calculateHarmonyAdjustment(interaction, adjustmentCount));
        adjustment.affectedElements.push(interaction.element);
        break;

      case 'sixCombinations':
      case 'fiveCombinations':
        adjustment.adjustments.push(...this.calculateCombinationAdjustment(interaction, season, adjustmentCount));
        adjustment.affectedElements.push(...this.getElementsFromInteraction(interaction));
        break;

      case 'sixClashes':
        adjustment.adjustments.push(...this.calculateClashAdjustment(interaction, adjustmentCount));
        adjustment.affectedElements.push(...this.getElementsFromInteraction(interaction));
        break;

      case 'threePunishments':
        adjustment.adjustments.push(...this.calculatePunishmentAdjustment(interaction, adjustmentCount));
        adjustment.affectedElements.push(...this.getElementsFromInteraction(interaction));
        break;
    }

    return adjustment;
  }

  /**
   * 计算三会方调整
   */
  calculateDirectionalAdjustment(interaction, adjustmentCount) {
    const element = interaction.element;
    const adjustmentTimes = adjustmentCount[element] || 0;
    const decayFactor = this.getDecayFactor(adjustmentTimes);
    
    const baseCoeff = this.BASE_COEFFICIENTS.threeDirectional.boost;
    const finalCoeff = 1 + (baseCoeff - 1) * decayFactor;

    return {
      element: element,
      coefficient: finalCoeff,
      reason: `三会方${interaction.description}`,
      decayFactor: decayFactor,
      originalCoeff: baseCoeff
    };
  }

  /**
   * 计算三合局调整
   */
  calculateHarmonyAdjustment(interaction, adjustmentCount) {
    const element = interaction.element;
    const adjustmentTimes = adjustmentCount[element] || 0;
    const decayFactor = this.getDecayFactor(adjustmentTimes);
    
    const baseCoeff = this.BASE_COEFFICIENTS.threeHarmony.boost;
    const finalCoeff = 1 + (baseCoeff - 1) * decayFactor;

    return {
      element: element,
      coefficient: finalCoeff,
      reason: `三合局${interaction.description}`,
      decayFactor: decayFactor,
      originalCoeff: baseCoeff
    };
  }

  /**
   * 计算合化调整 (考虑月令支持度)
   */
  calculateCombinationAdjustment(interaction, season, adjustmentCount) {
    const adjustments = [];
    
    if (interaction.element) {
      // 合化目标元素
      const support = this.SEASONAL_SUPPORT[season][interaction.element];
      const supportMultiplier = this.SUPPORT_MULTIPLIERS[support];
      
      const adjustmentTimes = adjustmentCount[interaction.element] || 0;
      const decayFactor = this.getDecayFactor(adjustmentTimes);
      
      if (support === 'strong') {
        // 合化成功 - 增强合化元素
        const baseCoeff = this.BASE_COEFFICIENTS.sixCombination.boost;
        const finalCoeff = 1 + (baseCoeff - 1) * supportMultiplier * decayFactor;
        
        adjustments.push({
          element: interaction.element,
          coefficient: finalCoeff,
          reason: `合化成功${interaction.description}`,
          support: support,
          decayFactor: decayFactor
        });
      } else {
        // 合化失败 - 合绊效应
        if (interaction.pair) {
          interaction.pair.forEach(item => {
            const element = this.getElementFromItem(item);
            const adjustmentTimes = adjustmentCount[element] || 0;
            const decayFactor = this.getDecayFactor(adjustmentTimes);
            
            const baseCoeff = this.BASE_COEFFICIENTS.sixCombination.suppress;
            const finalCoeff = baseCoeff + (1 - baseCoeff) * (1 - decayFactor);
            
            adjustments.push({
              element: element,
              coefficient: finalCoeff,
              reason: `合绊${interaction.description}`,
              support: support,
              decayFactor: decayFactor
            });
          });
        }
      }
    }

    return adjustments;
  }

  /**
   * 计算冲克调整
   */
  calculateClashAdjustment(interaction, adjustmentCount) {
    const adjustments = [];
    
    if (interaction.pair) {
      const baseCoeff = interaction.clashType === 'near' 
        ? this.BASE_COEFFICIENTS.sixClash.near
        : this.BASE_COEFFICIENTS.sixClash.far;

      interaction.pair.forEach(item => {
        const element = this.getElementFromItem(item);
        const adjustmentTimes = adjustmentCount[element] || 0;
        const decayFactor = this.getDecayFactor(adjustmentTimes);
        
        const finalCoeff = baseCoeff + (1 - baseCoeff) * (1 - decayFactor);
        
        adjustments.push({
          element: element,
          coefficient: finalCoeff,
          reason: `${interaction.description}`,
          clashType: interaction.clashType,
          decayFactor: decayFactor
        });
      });
    }

    return adjustments;
  }

  /**
   * 计算刑害调整
   */
  calculatePunishmentAdjustment(interaction, adjustmentCount) {
    const adjustments = [];
    
    if (interaction.pair) {
      const baseCoeff = this.BASE_COEFFICIENTS.threePunishment.suppress;

      interaction.pair.forEach(item => {
        const element = this.getElementFromItem(item);
        const adjustmentTimes = adjustmentCount[element] || 0;
        const decayFactor = this.getDecayFactor(adjustmentTimes);
        
        const finalCoeff = baseCoeff + (1 - baseCoeff) * (1 - decayFactor);
        
        adjustments.push({
          element: element,
          coefficient: finalCoeff,
          reason: `${interaction.description}`,
          decayFactor: decayFactor
        });
      });
    }

    return adjustments;
  }

  /**
   * 应用调整到五行力量
   */
  applyAdjustment(powers, adjustment, history) {
    adjustment.adjustments.forEach(adj => {
      const originalPower = powers[adj.element];
      const newPower = originalPower * adj.coefficient;
      powers[adj.element] = newPower;

      const historyEntry = {
        element: adj.element,
        originalPower: originalPower,
        newPower: newPower,
        coefficient: adj.coefficient,
        reason: adj.reason,
        change: ((newPower - originalPower) / originalPower * 100).toFixed(1) + '%'
      };

      history.push(historyEntry);
      
      console.log(`  ${adj.element}: ${originalPower.toFixed(1)} → ${newPower.toFixed(1)} (×${adj.coefficient.toFixed(2)}) - ${adj.reason}`);
    });
  }

  /**
   * 获取衰减因子
   */
  getDecayFactor(adjustmentTimes) {
    if (adjustmentTimes === 0) return this.ACCUMULATION_DECAY.first;
    if (adjustmentTimes === 1) return this.ACCUMULATION_DECAY.second;
    if (adjustmentTimes === 2) return this.ACCUMULATION_DECAY.third;
    return this.ACCUMULATION_DECAY.fourth;
  }

  /**
   * 从交互中获取元素
   */
  getElementsFromInteraction(interaction) {
    const elements = [];
    
    if (interaction.element) {
      elements.push(interaction.element);
    }
    
    if (interaction.pair) {
      interaction.pair.forEach(item => {
        const element = this.getElementFromItem(item);
        if (element && !elements.includes(element)) {
          elements.push(element);
        }
      });
    }

    return elements;
  }

  /**
   * 从项目获取元素
   */
  getElementFromItem(item) {
    const elementMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
      '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
      '戌': '土', '亥': '水'
    };
    
    return elementMap[item];
  }

  /**
   * 平衡性检查
   */
  performBalanceCheck(originalPowers, adjustedPowers) {
    const changes = {};
    let maxChange = 0;
    let totalChange = 0;

    Object.keys(originalPowers).forEach(element => {
      const original = originalPowers[element];
      const adjusted = adjustedPowers[element];
      const changeRate = original > 0 ? Math.abs((adjusted - original) / original) : 0;
      
      changes[element] = {
        original: original,
        adjusted: adjusted,
        changeRate: changeRate,
        changePercent: (changeRate * 100).toFixed(1) + '%'
      };

      maxChange = Math.max(maxChange, changeRate);
      totalChange += changeRate;
    });

    const avgChange = totalChange / Object.keys(originalPowers).length;

    return {
      status: maxChange < 0.8 ? 'balanced' : 'extreme',
      maxChangeRate: (maxChange * 100).toFixed(1) + '%',
      avgChangeRate: (avgChange * 100).toFixed(1) + '%',
      changes: changes,
      recommendation: maxChange > 0.8 ? '建议检查调整参数，变化过于剧烈' : '调整合理，符合传统命理学'
    };
  }
}

// 导出模块
module.exports = AdvancedDynamicAdjuster;
