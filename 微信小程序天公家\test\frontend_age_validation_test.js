// test/frontend_age_validation_test.js
// 测试前端年龄验证集成

const path = require('path');

class FrontendAgeValidationTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 模拟前端页面的数据处理逻辑
   */
  simulateFrontendDataProcessing() {
    console.log('🧪 模拟前端数据处理逻辑...\n');

    // 模拟刚出生婴儿的八字数据
    const babyBaziData = {
      baziInfo: {
        yearPillar: { heavenly: '甲', earthly: '辰' },
        monthPillar: { heavenly: '丙', earthly: '寅' },
        dayPillar: { heavenly: '戊', earthly: '午' },
        timePillar: { heavenly: '壬', earthly: '戌' }
      },
      userInfo: {
        birthYear: 2024, // 刚出生
        birthMonth: 7,
        birthDay: 31,
        birthHour: 17,
        gender: '男'
      }
    };

    // 模拟前端的 buildStandardizedBaziData 方法
    const standardizedBazi = {
      year_pillar: {
        heavenly: babyBaziData.baziInfo.yearPillar.heavenly,
        earthly: babyBaziData.baziInfo.yearPillar.earthly
      },
      month_pillar: {
        heavenly: babyBaziData.baziInfo.monthPillar.heavenly,
        earthly: babyBaziData.baziInfo.monthPillar.earthly
      },
      day_pillar: {
        heavenly: babyBaziData.baziInfo.dayPillar.heavenly,
        earthly: babyBaziData.baziInfo.dayPillar.earthly
      },
      time_pillar: {
        heavenly: babyBaziData.baziInfo.timePillar.heavenly,
        earthly: babyBaziData.baziInfo.timePillar.earthly
      },
      day_master: babyBaziData.baziInfo.dayPillar.heavenly,
      birth_info: {
        year: babyBaziData.userInfo.birthYear,
        month: babyBaziData.userInfo.birthMonth,
        day: babyBaziData.userInfo.birthDay,
        hour: babyBaziData.userInfo.birthHour,
        gender: babyBaziData.userInfo.gender
      }
    };

    console.log('📊 标准化八字数据:');
    console.log(`  出生年份: ${standardizedBazi.birth_info.year}`);
    console.log(`  当前年份: ${new Date().getFullYear()}`);
    console.log(`  计算年龄: ${new Date().getFullYear() - standardizedBazi.birth_info.year}岁`);

    return { standardizedBazi, gender: babyBaziData.userInfo.gender };
  }

  /**
   * 测试算法年龄验证
   */
  async testAlgorithmAgeValidation() {
    console.log('\n🔬 测试算法年龄验证...');

    try {
      const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');
      const engine = new ProfessionalTimingEngine();

      const { standardizedBazi, gender } = this.simulateFrontendDataProcessing();
      const currentYear = new Date().getFullYear();

      // 测试三种事件类型
      const eventTypes = ['marriage', 'promotion', 'wealth'];
      let passedTests = 0;

      for (const eventType of eventTypes) {
        console.log(`\n  测试 ${eventType} 事件:`);
        
        const result = engine.analyzeProfessionalTiming(
          standardizedBazi, eventType, gender, currentYear, 5
        );

        const isAgeNotMet = result.timing_prediction?.threshold_status === 'age_not_met';
        const hasAgeValidation = result.age_validation && !result.age_validation.valid;
        const currentAge = result.age_validation?.current_age;
        const minimumAge = result.timing_prediction?.minimum_age;

        if (isAgeNotMet && hasAgeValidation) {
          console.log(`    ✅ 正确识别年龄不符: 当前${currentAge}岁，需要${minimumAge}岁`);
          passedTests++;
        } else {
          console.log(`    ❌ 年龄验证失败`);
          console.log(`      - 阈值状态: ${result.timing_prediction?.threshold_status}`);
          console.log(`      - 年龄验证: ${result.age_validation?.valid}`);
        }
      }

      this.addTestResult(
        '算法年龄验证',
        passedTests === eventTypes.length,
        `${passedTests}/${eventTypes.length} 事件类型正确处理年龄限制`
      );

    } catch (error) {
      this.addTestResult('算法年龄验证', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 测试前端数据处理逻辑
   */
  async testFrontendDataProcessing() {
    console.log('\n🖥️ 测试前端数据处理逻辑...');

    try {
      // 模拟前端的 performOptimizedTimingAnalysis 逻辑
      const { standardizedBazi, gender } = this.simulateFrontendDataProcessing();
      const currentYear = new Date().getFullYear();

      // 模拟算法返回的年龄不符结果
      const mockAnalysisResult = {
        timing_prediction: {
          threshold_status: 'age_not_met',
          message: '您目前0岁，婚姻分析需要成年后进行。建议18岁后（2042年）再进行marriage分析。',
          minimum_age: 18,
          current_age: 0,
          estimated_earliest_year: 2042
        },
        age_validation: {
          valid: false,
          current_age: 0,
          minimum_age: 18
        },
        gods_analysis: []
      };

      // 模拟前端处理逻辑
      const thresholdStatus = mockAnalysisResult.timing_prediction?.threshold_status || 'unknown';
      let frontendResult = {};

      if (thresholdStatus === 'age_not_met') {
        frontendResult = {
          raw_analysis: mockAnalysisResult,
          threshold_status: 'age_not_met',
          confidence: 0.1,
          best_year: null,
          message: mockAnalysisResult.timing_prediction.message,
          minimum_age: mockAnalysisResult.timing_prediction.minimum_age,
          current_age: mockAnalysisResult.timing_prediction.current_age,
          estimated_earliest_year: mockAnalysisResult.timing_prediction.estimated_earliest_year,
          ancient_basis: '传统命理学认为不同人生阶段有其相应的分析重点',
          gods_analysis: mockAnalysisResult.gods_analysis,
          age_validation: mockAnalysisResult.age_validation
        };
      }

      // 验证前端处理结果
      const isCorrectlyProcessed = (
        frontendResult.threshold_status === 'age_not_met' &&
        frontendResult.best_year === null &&
        frontendResult.message.includes('岁') &&
        frontendResult.minimum_age === 18 &&
        frontendResult.current_age === 0
      );

      this.addTestResult(
        '前端数据处理',
        isCorrectlyProcessed,
        isCorrectlyProcessed ? '✅ 前端正确处理年龄不符状态' : '❌ 前端处理逻辑有误'
      );

      console.log('  前端处理结果预览:');
      console.log(`    阈值状态: ${frontendResult.threshold_status}`);
      console.log(`    最佳年份: ${frontendResult.best_year}`);
      console.log(`    消息内容: ${frontendResult.message}`);

    } catch (error) {
      this.addTestResult('前端数据处理', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 运行完整测试
   */
  async runCompleteTest() {
    console.log('🔧 开始前端年龄验证集成测试...\n');

    await this.testAlgorithmAgeValidation();
    await this.testFrontendDataProcessing();

    this.printResults();
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed: passed,
      details: details
    });
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🔧 前端年龄验证集成测试结果');
    console.log('='.repeat(60));

    let passedCount = 0;
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}: ${result.details}`);
      if (result.passed) passedCount++;
    });

    console.log('-'.repeat(60));
    const totalTests = this.testResults.length;
    const passRate = ((passedCount / totalTests) * 100).toFixed(1);
    
    console.log(`\n📊 总体结果: ${passedCount}/${totalTests} 测试通过 (${passRate}%)`);
    
    if (passedCount === totalTests) {
      console.log('🎉 所有测试通过！前端年龄验证集成成功！');
    } else {
      console.log('⚠️ 部分测试失败，需要进一步修复');
    }
    console.log('='.repeat(60));
  }
}

// 运行测试
const test = new FrontendAgeValidationTest();
test.runCompleteTest().catch(console.error);
