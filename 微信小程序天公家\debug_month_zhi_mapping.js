// 调试月支映射问题
// 检查地支序号和月份的对应关系

console.log('🔍 调试月支映射问题');

const dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"];

console.log('\n📋 地支序号对应表:');
dizhi.forEach((zhi, index) => {
  console.log(`${index}: ${zhi}`);
});

console.log('\n🌙 干支历月份对应:');
console.log('寅月(正月) = 1');
console.log('卯月(二月) = 2');
console.log('辰月(三月) = 3');
console.log('巳月(四月) = 4');
console.log('午月(五月) = 5');
console.log('未月(六月) = 6');
console.log('申月(七月) = 7');
console.log('酉月(八月) = 8');
console.log('戌月(九月) = 9');
console.log('亥月(十月) = 10');
console.log('子月(十一月) = 11');
console.log('丑月(十二月) = 12');

console.log('\n🔧 正确的月支映射:');
const correctMapping = {
  1: '寅',  // 寅月(正月)
  2: '卯',  // 卯月(二月)
  3: '辰',  // 辰月(三月)
  4: '巳',  // 巳月(四月)
  5: '午',  // 午月(五月)
  6: '未',  // 未月(六月)
  7: '申',  // 申月(七月)
  8: '酉',  // 酉月(八月)
  9: '戌',  // 戌月(九月)
  10: '亥', // 亥月(十月)
  11: '子', // 子月(十一月)
  12: '丑'  // 丑月(十二月)
};

Object.entries(correctMapping).forEach(([month, zhi]) => {
  console.log(`月序号${month} → ${zhi}月`);
});

console.log('\n🧪 测试1953年6月15日:');
console.log('6月15日 > 芒种(6月6日) → 应该是午月');
console.log('午月序号: 5');
console.log('地支数组中午的索引:', dizhi.indexOf('午'));

// 问题分析
console.log('\n❌ 问题分析:');
console.log('当前错误: dizhi[solarMonth - 1] = dizhi[5 - 1] = dizhi[4] = 巳');
console.log('正确应该: 午月序号5 → 午');

console.log('\n✅ 正确的映射方法:');
console.log('方法1: 使用映射表 correctMapping[solarMonth]');
console.log('方法2: 调整数组索引 dizhi[(solarMonth + 1) % 12]');

// 验证修复
console.log('\n🔧 验证修复:');
const solarMonth = 5; // 午月
console.log(`节气月序号: ${solarMonth}`);
console.log(`错误方法: dizhi[${solarMonth} - 1] = ${dizhi[solarMonth - 1]}`);
console.log(`正确方法1: correctMapping[${solarMonth}] = ${correctMapping[solarMonth]}`);
console.log(`正确方法2: dizhi[(${solarMonth} + 1) % 12] = ${dizhi[(solarMonth + 1) % 12]}`);

// 测试所有月份
console.log('\n🧪 测试所有月份映射:');
for (let month = 1; month <= 12; month++) {
  const wrongZhi = dizhi[month - 1];
  const correctZhi = correctMapping[month];
  const method2Zhi = dizhi[(month + 1) % 12];
  
  console.log(`月${month}: 错误=${wrongZhi}, 正确=${correctZhi}, 方法2=${method2Zhi} ${correctZhi === method2Zhi ? '✅' : '❌'}`);
}

console.log('\n📋 修复建议:');
console.log('需要修复前端getSolarMonthByNodeQi函数中的地支映射');
console.log('将 dizhi[solarMonth - 1] 改为 correctMapping[solarMonth]');
console.log('或者使用 dizhi[(solarMonth + 1) % 12]');
