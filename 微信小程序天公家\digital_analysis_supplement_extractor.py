#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数字化分析补充提取工具
专门针对数字化分析模块的61条缺口进行补充
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class DigitalAnalysisSupplementExtractor:
    def __init__(self):
        self.rule_id_counter = 900000
        self.target_supplement = 61  # 需要补充的数量
        
        # 数字化分析专用提取配置
        self.digital_analysis_config = {
            "千里命稿": {
                "file": "千里命稿.txt",
                "focus_patterns": [
                    # 强弱程度分析
                    r'[^。]*?[强弱|轻重|深浅|高低|大小|多少][^。]*?[程度|等级|分数|力量|影响][^。]*?。',
                    r'[^。]*?[分析|计算|评估|判断|测算|推断][^。]*?[格局|用神|五行|十神][^。]*?。',
                    r'[^。]*?[旺相休囚死|强弱中和][^。]*?[分数|等级|程度][^。]*?。',
                    r'[^。]*?[数值|分值|评分|打分][^。]*?[命理|八字|格局][^。]*?。',
                    r'[^。]*?[量化|测量|衡量|评定][^。]*?[五行|旺衰|强弱][^。]*?。',
                    r'[^。]*?[比较|对比|相较|较之][^。]*?[优劣|好坏|高低|强弱][^。]*?。',
                    r'[^。]*?[最|极|很|较|稍|更|甚|尤][^。]*?[强|弱|旺|衰|重|轻][^。]*?。',
                    r'[^。]*?[如何|怎样|方法][^。]*?[分析|判断|计算|评估][^。]*?[强弱|程度][^。]*?。'
                ],
                "target": 35
            },
            "五行精纪": {
                "file": "五行精纪.docx",
                "focus_patterns": [
                    # 五行力量数值化
                    r'[^。]*?[金木水火土][^。]*?[力量|强度|影响|作用|效果|威力][^。]*?。',
                    r'[^。]*?[旺相休囚死][^。]*?[分数|等级|程度|强弱|力度][^。]*?。',
                    r'[^。]*?[最|极|很|较|稍|更|甚|尤][^。]*?[旺|衰|强|弱|重|轻|深|浅][^。]*?。',
                    r'[^。]*?[量化|测量|计算|评估|衡量|评定][^。]*?[五行|旺衰|强弱][^。]*?。',
                    r'[^。]*?[数值|分值|评分|打分|指数|系数][^。]*?[五行|元素|属性][^。]*?。',
                    r'[^。]*?[调候][^。]*?[程度|等级|强弱|轻重][^。]*?。'
                ],
                "target": 20
            },
            "渊海子平": {
                "file": "渊海子平.docx",
                "focus_patterns": [
                    # 格局用神数值分析
                    r'[^。]*?[格局|用神|十神][^。]*?[强弱|程度|等级|力量][^。]*?[分析|判断|评估][^。]*?。',
                    r'[^。]*?[正官|偏官|正财|偏财|正印|偏印|食神|伤官|比肩|劫财][^。]*?[强弱|力量|影响][^。]*?。',
                    r'[^。]*?[日主|日元|日干][^。]*?[强弱|旺衰][^。]*?[程度|等级|分析][^。]*?。'
                ],
                "target": 6
            }
        }
        
        # 数字化分析关键词
        self.digital_keywords = [
            "强弱", "程度", "等级", "分析", "计算", "评估", "量化", "数值", "分数",
            "力量", "强度", "影响", "作用", "效果", "威力", "旺衰", "深浅", "高低",
            "大小", "多少", "轻重", "比较", "对比", "衡量", "评定", "测算", "判断"
        ]
    
    def load_book_content(self, book_name: str) -> str:
        """加载古籍内容"""
        if book_name not in self.digital_analysis_config:
            return ""
        
        filename = self.digital_analysis_config[book_name]["file"]
        file_path = os.path.join("古籍资料", filename)
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在: {filename}")
            return ""
        
        try:
            if filename.endswith('.txt'):
                return self._load_txt_file(file_path)
            elif filename.endswith('.docx'):
                return self._load_docx_file(file_path)
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
            return ""
        
        return ""
    
    def _load_txt_file(self, file_path: str) -> str:
        """加载TXT文件"""
        encodings = ['utf-8', 'gbk', 'gb2312']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    print(f"  ✅ TXT加载成功: {len(content):,} 字符")
                    return content
            except UnicodeDecodeError:
                continue
        return ""
    
    def _load_docx_file(self, file_path: str) -> str:
        """加载DOCX文件"""
        try:
            from docx import Document
            doc = Document(file_path)
            content = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            print(f"  ✅ DOCX加载成功: {len(content):,} 字符")
            return content
        except ImportError:
            print("  需要安装python-docx库")
            return ""
        except Exception:
            return ""
    
    def extract_digital_analysis_rules(self, content: str, book_name: str, config: Dict) -> List[Dict]:
        """提取数字化分析规则"""
        if not content:
            return []
        
        patterns = config["focus_patterns"]
        target = config["target"]
        
        all_extracted = []
        
        print(f"  🎯 专门提取数字化分析规则 (目标: {target}条)...")
        
        # 1. 专用模式匹配
        for i, pattern in enumerate(patterns):
            try:
                matches = re.findall(pattern, content)
                print(f"    专用模式{i+1}: {len(matches)}条")
                
                for match in matches:
                    cleaned_text = self._clean_digital_text(match)
                    if self._validate_digital_rule(cleaned_text):
                        rule = self._create_digital_rule(cleaned_text, book_name, f"专用模式{i+1}")
                        all_extracted.append(rule)
            except Exception as e:
                continue
        
        # 2. 关键词密集提取
        keyword_rules = self._extract_digital_keywords(content, book_name)
        all_extracted.extend(keyword_rules)
        
        # 3. 数值化表达提取
        numeric_rules = self._extract_numeric_expressions(content, book_name)
        all_extracted.extend(numeric_rules)
        
        # 4. 去重和筛选
        unique_rules = self._deduplicate_digital_rules(all_extracted)
        
        # 5. 按质量排序并限制数量
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        final_rules = unique_rules[:target]
        
        print(f"  ✅ 提取完成: {len(final_rules)}条数字化分析规则")
        return final_rules
    
    def _extract_digital_keywords(self, content: str, book_name: str) -> List[Dict]:
        """基于关键词提取数字化分析规则"""
        rules = []
        sentences = re.split(r'[。；！？]', content)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if 25 <= len(sentence) <= 400:
                # 检查数字化分析关键词
                keyword_count = sum(1 for keyword in self.digital_keywords if keyword in sentence)
                if keyword_count >= 2:  # 至少包含2个相关关键词
                    cleaned_text = self._clean_digital_text(sentence)
                    if self._validate_digital_rule(cleaned_text):
                        rule = self._create_digital_rule(cleaned_text, book_name, "关键词提取")
                        rules.append(rule)
        
        return rules
    
    def _extract_numeric_expressions(self, content: str, book_name: str) -> List[Dict]:
        """提取数值化表达"""
        rules = []
        
        # 寻找包含数值化表达的句子
        numeric_patterns = [
            r'[^。]*?[一二三四五六七八九十][^。]*?[分|等|级|度][^。]*?。',
            r'[^。]*?[几|多|少|半|全][^。]*?[分|成|倍][^。]*?。',
            r'[^。]*?[上中下|高中低|强中弱][^。]*?[等|级|品][^。]*?。'
        ]
        
        for pattern in numeric_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                cleaned_text = self._clean_digital_text(match)
                if self._validate_digital_rule(cleaned_text):
                    rule = self._create_digital_rule(cleaned_text, book_name, "数值化表达")
                    rules.append(rule)
        
        return rules
    
    def _clean_digital_text(self, text: str) -> str:
        """清理数字化分析文本"""
        if not text:
            return ""
        
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 移除无关内容
        text = text.replace('注：', '').replace('按：', '')
        text = text.replace('又云：', '').replace('古云：', '')
        
        return text
    
    def _validate_digital_rule(self, text: str) -> bool:
        """验证数字化分析规则"""
        if not text or len(text) < 20 or len(text) > 500:
            return False
        
        # 必须包含数字化分析相关关键词
        digital_indicators = ["强弱", "程度", "等级", "分析", "计算", "评估", "量化", "数值"]
        has_digital_keyword = any(indicator in text for indicator in digital_indicators)
        
        # 必须包含命理相关词汇
        theory_indicators = ["格局", "用神", "五行", "十神", "旺衰", "日主", "命理", "八字"]
        has_theory_keyword = any(indicator in text for indicator in theory_indicators)
        
        return has_digital_keyword and has_theory_keyword
    
    def _create_digital_rule(self, text: str, book_name: str, method: str) -> Dict:
        """创建数字化分析规则"""
        # 计算置信度
        confidence = 0.90 + (len(text) / 1000) * 0.03
        confidence = min(0.96, confidence)
        
        rule = {
            "rule_id": f"DIGITAL_{self.rule_id_counter:06d}",
            "pattern_name": f"《{book_name}》·数字化分析补充规则",
            "category": "数字化分析",
            "dimension_type": "数字化分析",
            "book_source": book_name,
            "extraction_method": method,
            "original_text": text,
            "interpretations": f"出自《{book_name}》的数字化分析权威理论，专门补充提取",
            "confidence": confidence,
            "digital_analysis_supplement": True,
            "extracted_at": datetime.now().isoformat(),
            "extraction_phase": "数字化分析专项补充",
            "rule_type": "数字化分析补充规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _deduplicate_digital_rules(self, rules: List[Dict]) -> List[Dict]:
        """去重数字化分析规则"""
        seen_texts = set()
        unique_rules = []
        
        for rule in rules:
            text = rule.get('original_text', '')
            simplified = re.sub(r'[\s\W]', '', text)[:25]  # 检查前25个字符
            
            if simplified not in seen_texts and len(simplified) > 10:
                seen_texts.add(simplified)
                unique_rules.append(rule)
        
        return unique_rules
    
    def execute_digital_supplement(self) -> Dict:
        """执行数字化分析补充"""
        print("🚀 开始数字化分析专项补充...")
        print(f"🎯 目标补充: {self.target_supplement}条规则")
        
        all_digital_rules = []
        total_extracted = 0
        
        for book_name, config in self.digital_analysis_config.items():
            print(f"\n📚 处理《{book_name}》...")
            
            content = self.load_book_content(book_name)
            if not content:
                continue
            
            rules = self.extract_digital_analysis_rules(content, book_name, config)
            all_digital_rules.extend(rules)
            total_extracted += len(rules)
        
        # 去重和最终筛选
        unique_rules = self._deduplicate_digital_rules(all_digital_rules)
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        # 限制到目标数量
        final_rules = unique_rules[:self.target_supplement]
        
        result_data = {
            "metadata": {
                "supplement_type": "数字化分析专项补充",
                "supplement_date": datetime.now().isoformat(),
                "target_supplement": self.target_supplement,
                "actual_extracted": len(final_rules),
                "completion_rate": f"{len(final_rules)/self.target_supplement*100:.1f}%",
                "books_processed": len(self.digital_analysis_config)
            },
            "supplement_rules": final_rules
        }
        
        return {
            "success": True,
            "data": result_data,
            "summary": {
                "目标补充": self.target_supplement,
                "实际提取": len(final_rules),
                "完成率": f"{len(final_rules)/self.target_supplement*100:.1f}%"
            }
        }

def main():
    """主函数"""
    extractor = DigitalAnalysisSupplementExtractor()
    
    result = extractor.execute_digital_supplement()
    
    if result.get("success"):
        output_filename = f"digital_analysis_supplement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        print("\n" + "="*80)
        print("🎉 数字化分析专项补充完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print(f"\n✅ 补充结果已保存到: {output_filename}")
        print(f"💡 下一步：补充匹配分析模块")
        
    else:
        print(f"❌ 补充失败")

if __name__ == "__main__":
    main()
