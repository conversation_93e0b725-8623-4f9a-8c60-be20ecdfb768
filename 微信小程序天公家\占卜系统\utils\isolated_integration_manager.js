// utils/isolated_integration_manager.js
// 隔离集成管理器 - 管理不同模块间的隔离集成

class IsolatedIntegrationManager {
  constructor() {
    this.integrations = new Map();
    this.isolationLevel = 'standard';
    this.initialized = false;
  }

  // 初始化管理器
  init(config = {}) {
    try {
      this.isolationLevel = config.isolationLevel || 'standard';
      this.initialized = true;
      console.log('✅ IsolatedIntegrationManager 初始化成功');
      return true;
    } catch (error) {
      console.error('❌ IsolatedIntegrationManager 初始化失败:', error);
      return false;
    }
  }

  // 注册集成模块
  register(name, module, options = {}) {
    if (!this.initialized) {
      console.warn('⚠️ IsolatedIntegrationManager 未初始化');
      return false;
    }

    try {
      this.integrations.set(name, {
        module: module,
        options: options,
        isolated: options.isolated !== false,
        priority: options.priority || 0
      });
      
      console.log(`✅ 模块 ${name} 注册成功`);
      return true;
    } catch (error) {
      console.error(`❌ 模块 ${name} 注册失败:`, error);
      return false;
    }
  }

  // 获取集成模块
  get(name) {
    const integration = this.integrations.get(name);
    return integration ? integration.module : null;
  }

  // 执行隔离集成
  integrate(name, data, context = {}) {
    const integration = this.integrations.get(name);
    if (!integration) {
      console.warn(`⚠️ 未找到集成模块: ${name}`);
      return null;
    }

    try {
      // 创建隔离环境
      const isolatedContext = this.createIsolatedContext(context, integration.options);
      
      // 执行集成
      if (typeof integration.module.integrate === 'function') {
        return integration.module.integrate(data, isolatedContext);
      } else {
        return integration.module(data, isolatedContext);
      }
    } catch (error) {
      console.error(`❌ 集成执行失败 ${name}:`, error);
      return null;
    }
  }

  // 创建隔离上下文
  createIsolatedContext(context, options) {
    const isolatedContext = {
      ...context,
      isolated: true,
      timestamp: Date.now(),
      isolationLevel: this.isolationLevel
    };

    // 根据隔离级别添加限制
    if (this.isolationLevel === 'strict') {
      isolatedContext.restricted = true;
      isolatedContext.allowedOperations = options.allowedOperations || [];
    }

    return isolatedContext;
  }

  // 清理集成
  cleanup() {
    this.integrations.clear();
    this.initialized = false;
    console.log('✅ IsolatedIntegrationManager 清理完成');
  }

  // 获取状态
  getStatus() {
    return {
      initialized: this.initialized,
      integrationCount: this.integrations.size,
      isolationLevel: this.isolationLevel
    };
  }
}

// 创建单例实例
const isolatedIntegrationManager = new IsolatedIntegrationManager();

module.exports = {
  IsolatedIntegrationManager,
  default: isolatedIntegrationManager,
  manager: isolatedIntegrationManager
};
