<!--pages/profile/index.wxml-->
<view class="profile-container">
  <!-- 顶部用户信息 -->
  <view class="user-info-section">
    <view class="avatar-container" bindtap="{{hasUserInfo ? '' : 'getUserProfile'}}">
      <image class="avatar" src="{{userInfo.avatarUrl || '/assets/icons/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="edit-badge" wx:if="{{hasUserInfo}}">
        <image src="/assets/icons/edit.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="user-details">
      <view class="username">{{hasUserInfo ? userInfo.nickName : '点击登录'}}</view>
      <view class="user-subtitle" wx:if="{{hasUserInfo}}">已完成 {{assessmentCount}} 次心理评估</view>
      <view class="user-subtitle" wx:else>登录后查看更多功能</view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section" wx:if="{{hasUserInfo}}">
    <view class="stat-item" bindtap="navigateToHistory">
      <view class="stat-value">{{assessmentCount}}</view>
      <view class="stat-label">评估记录</view>
    </view>
    <view class="divider"></view>
    <view class="stat-item">
      <view class="stat-value">{{lastAssessmentDate || '无'}}</view>
      <view class="stat-label">最近评估</view>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-list">
    <view class="menu-section-title">功能</view>
    
    <view class="menu-item" bindtap="navigateToHistory">
      <view class="menu-icon">
        <image src="/assets/icons/history.png" mode="aspectFit"></image>
      </view>
      <view class="menu-text">历史评估</view>
      <view class="menu-arrow">
        <image src="/assets/icons/arrow.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="menu-item">
      <view class="menu-icon">
        <image src="/assets/icons/notification.png" mode="aspectFit"></image>
      </view>
      <view class="menu-text">消息通知</view>
      <view class="menu-arrow">
        <image src="/assets/icons/arrow.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="menu-section-title">设置</view>
    
    <view class="menu-item" bindtap="contactService">
      <view class="menu-icon">
        <image src="/assets/icons/customer-service.png" mode="aspectFit"></image>
      </view>
      <view class="menu-text">联系客服</view>
      <view class="menu-arrow">
        <image src="/assets/icons/arrow.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="menu-item" bindtap="aboutUs">
      <view class="menu-icon">
        <image src="/assets/icons/about.png" mode="aspectFit"></image>
      </view>
      <view class="menu-text">关于我们</view>
      <view class="menu-arrow">
        <image src="/assets/icons/arrow.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <view class="version-info">版本 1.0.0</view>
  
</view>

<!-- 修改昵称弹窗 -->
<view class="modal" wx:if="{{showNicknameModal}}">
  <view class="modal-content">
    <view class="modal-header">修改昵称</view>
    <view class="modal-body">
      <input class="nickname-input" value="{{userInfo.name}}" bindinput="onNicknameInput" placeholder="请输入新昵称" maxlength="12" />
      <view class="nickname-limit">本周剩余修改次数: {{nicknameEditLimit}}</view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="cancelNicknameEdit">取消</button>
      <button class="confirm-btn" bindtap="confirmNicknameEdit">确认</button>
    </view>
  </view>
</view>

<!-- 充值选项弹窗 -->
<view class="modal" wx:if="{{showRechargeModal}}">
  <view class="modal-content">
    <view class="modal-header">选择充值套餐</view>
    <view class="modal-body">
      <view class="package-options">
        <view class="package-option {{selectedPackage === index ? 'selected' : ''}}" 
              wx:for="{{rechargePackages}}" 
              wx:key="id" 
              bindtap="selectPackage" 
              data-index="{{index}}">
          <view class="package-option-name">{{item.name}}</view>
          <view class="package-option-price">¥{{item.price}}</view>
          <view class="package-option-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="cancelRecharge">取消</button>
      <button class="confirm-btn" bindtap="confirmRecharge">确认支付</button>
    </view>
  </view>
</view>

<!-- 动态头像预览弹窗 -->
<view class="modal" wx:if="{{showAvatarPreview}}">
  <view class="modal-content avatar-preview-content">
    <view class="modal-header">动态头像预览</view>
    <view class="modal-body avatar-preview-body">
      <image class="avatar-preview-image" src="{{previewAvatarUrl}}" mode="aspectFit"></image>
    </view>
    <view class="modal-footer">
      <button class="confirm-btn" bindtap="closeAvatarPreview">关闭</button>
    </view>
  </view>
</view>

<wxs module="utils">
module.exports = {
  formatDate: function(dateStr) {
    if (!dateStr) return '';
    var date = getDate(dateStr);
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
  }
}
</wxs>