/* pages/bazi-result/index.wxss */
/* 八字排盘结果展示页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15rpx;
}

.confidence-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 基本信息卡片 */
.basic-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.card-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

/* 出生信息 */
.birth-info {
  margin-bottom: 25rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx 0;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 四柱显示 */
.four-pillars {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  text-align: center;
}

.pillars-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 12rpx;
}

.pillars-content {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  letter-spacing: 8rpx;
  font-family: 'KaiTi', serif;
}

/* 基础分析 */
.basic-analysis {
  border-top: 1rpx solid #e9ecef;
  padding-top: 20rpx;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.item-label {
  font-size: 26rpx;
  color: #6c757d;
}

.item-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tab-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 22rpx;
  font-weight: 500;
}

/* 标签页内容 */
.tab-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  min-height: 300rpx;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 分析区块 */
.analysis-section {
  margin-bottom: 30rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 15rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}

.section-arrow {
  font-size: 24rpx;
  color: #adb5bd;
}

/* 五行统计 */
.wuxing-stats {
  display: flex;
  justify-content: space-around;
  padding: 25rpx;
}

.wuxing-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wuxing-name {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.wuxing-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

/* 十神摘要 */
.shishen-summary {
  padding: 25rpx;
  text-align: center;
}

.summary-text {
  font-size: 26rpx;
  color: #6c757d;
}

/* 专业/古籍/综合摘要 */
.professional-summary,
.classical-summary,
.comprehensive-summary {
  border: 1rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 25rpx;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.summary-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.summary-title {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}

.summary-arrow {
  font-size: 24rpx;
  color: #adb5bd;
}

.summary-desc {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.5;
}

/* 评价项目 */
.evaluation-items {
  margin-top: 20rpx;
}

.eval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f1f3f4;
}

.eval-item:last-child {
  border-bottom: none;
}

.eval-label {
  font-size: 26rpx;
  color: #6c757d;
}

.eval-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 26rpx;
}

/* 弹窗样式 */
.detail-modal,
.report-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.detail-modal.show,
.report-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 600rpx;
  max-height: 70vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.large {
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
  flex-shrink: 0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.modal-action {
  font-size: 28rpx;
  color: #667eea;
  padding: 10rpx;
}

.modal-close {
  font-size: 32rpx;
  color: #adb5bd;
  padding: 10rpx;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.detail-content,
.report-content {
  line-height: 1.6;
}

.detail-text,
.report-text {
  font-size: 26rpx;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 数字化分析样式 */
.digital-analysis-container {
  margin: 30rpx 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.digital-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.digital-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.digital-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 组件容器样式 */
.digital-analysis-container wuxing-radar,
.digital-analysis-container enhanced-balance-meter {
  margin: 20rpx 0;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .digital-analysis-container {
    margin: 20rpx 0;
    padding: 16rpx;
  }

  .digital-title {
    font-size: 28rpx;
  }

  .digital-subtitle {
    font-size: 22rpx;
  }
}
