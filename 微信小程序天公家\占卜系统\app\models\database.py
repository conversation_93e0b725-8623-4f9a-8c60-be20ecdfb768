#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
《玉匣记》数据库模型定义
使用SQLAlchemy ORM
"""

from sqlalchemy import Column, String, Integer, Text, Float, DateTime, JSON, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()

class DivinationEntry(Base):
    """占卜条目表"""
    __tablename__ = "divination_entries"
    
    # 主键和基础信息
    id = Column(String(50), primary_key=True)
    title = Column(String(300), nullable=False)
    original_text = Column(Text, nullable=False)
    description = Column(Text, nullable=False)
    interpretation = Column(Text)
    
    # 分类信息
    content_type = Column(String(50), nullable=False)
    main_category = Column(String(100), nullable=False)
    sub_category = Column(String(100))
    
    # 吉凶等级
    luck_level = Column(String(20), nullable=False, default='unknown')
    
    # 结构化数据 (JSON格式)
    date_info = Column(JSON)
    time_info = Column(JSON)
    constellation_info = Column(JSON)
    direction = Column(String(20))
    
    # 关键词 (JSON数组)
    keywords = Column(JSON)
    
    # 元数据
    source_line = Column(Integer)
    confidence_score = Column(Float, default=1.0)
    
    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 创建索引
    __table_args__ = (
        Index('idx_content_type', 'content_type'),
        Index('idx_luck_level', 'luck_level'),
        Index('idx_main_category', 'main_category'),
        Index('idx_confidence', 'confidence_score'),
        Index('idx_created_at', 'created_at'),
    )

class Chapter(Base):
    """章节表"""
    __tablename__ = "chapters"
    
    id = Column(String(50), primary_key=True)
    title = Column(String(200), nullable=False)
    content_type = Column(String(50), nullable=False)
    description = Column(Text)
    entry_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class Keyword(Base):
    """关键词表"""
    __tablename__ = "keywords"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    keyword = Column(String(50), unique=True, nullable=False)
    category = Column(String(50))
    frequency = Column(Integer, default=1)
    description = Column(Text)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_keyword', 'keyword'),
        Index('idx_category', 'category'),
        Index('idx_frequency', 'frequency'),
    )

class QueryHistory(Base):
    """查询历史表"""
    __tablename__ = "query_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query_type = Column(String(50), nullable=False)
    query_params = Column(JSON)
    result_count = Column(Integer)
    response_time = Column(Float)  # 响应时间(秒)
    user_ip = Column(String(45))
    
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_query_type', 'query_type'),
        Index('idx_created_at', 'created_at'),
    )

class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), unique=True, nullable=False)
    config_value = Column(Text)
    description = Column(Text)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_config_key', 'config_key'),
    )
