/* pages/teacher-assessment/index.wxss */

.chat-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #16a34a; /* 教师评估绿色 */
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #16a34a, #0f7a36);
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

/* 确保其他元素在背景图案上方 */
.nav-bar, .content-container, .welcome-message, .assessment-container, .input-area {
  position: relative;
  z-index: 2;
}

/* 顶部导航栏 */
.nav-bar {
  padding: 40rpx 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 100;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.back-button {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
}

.back-button image {
  width: 40rpx;
  height: 40rpx;
}

/* 日期显示 */
.date-display {
  padding: 20rpx 40rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 0 30rpx;
  overflow-y: auto;
  position: relative;
}

/* 欢迎消息 */
.welcome-message {
  width: 85%;
  margin: 40rpx auto;
  padding: 40rpx 30rpx;
  border-radius: 40rpx;
  background-color: rgba(255, 255, 255, 0.07);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15), inset 0 1px 1px rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5rpx);
}

.welcome-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.welcome-subtitle {
  font-size: 28rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 30rpx;
}

.welcome-instruction {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  padding: 20rpx 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 评估问卷区域 */
.assessment-container {
  padding-bottom: 120rpx;
}

.question-card {
  width: 85%;
  margin: 20rpx auto 40rpx;
  padding: 40rpx 30rpx;
  border-radius: 40rpx;
  background-color: rgba(255, 255, 255, 0.07);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15), inset 0 1px 1px rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5rpx);
}

.question-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.question-description {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 选项列表 */
.options-list {
  margin-top: 20rpx;
}

.option-item {
  width: 90%;
  margin: 20rpx auto;
  padding: 25rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.07);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: center;
  backdrop-filter: blur(5rpx);
}

.option-item.selected {
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.1);
  transform: translateY(-5rpx);
}

.option-radio {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  margin-right: 20rpx;
  position: relative;
  flex-shrink: 0;
}

.option-item.selected .option-radio {
  border-color: white;
}

.option-item.selected .option-radio::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.option-text {
  font-size: 28rpx;
  flex: 1;
}

/* 进度指示器 */
.progress-container {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  padding: 0 10rpx;
}

.progress-bar {
  flex: 1;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5rpx;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 20rpx;
}

/* 提交按钮区域 */
.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.nav-button {
  padding: 20rpx 35rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.07);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.nav-button.next {
  background: rgba(255, 255, 255, 0.15);
}

.nav-button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.nav-button image {
  width: 32rpx;
  height: 32rpx;
}

.prev-button image {
  margin-right: 10rpx;
}

.next-button image {
  margin-left: 10rpx;
}

/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  align-items: center;
  background: rgba(22, 163, 74, 0.9); /* 教师主题色 */
  backdrop-filter: blur(10rpx);
  z-index: 100;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
  color: white;
  font-size: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.send-button, .voice-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(22, 163, 74, 0.8); /* 教师主题色 */
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.send-button {
  margin-right: 10rpx;
}

.send-button.active {
  background: rgba(22, 163, 74, 1.0); /* 教师主题色全不透明 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.send-button image, .voice-button image {
  width: 40rpx;
  height: 40rpx;
}

.button-hover {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 结果页样式 */
.result-container {
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.result-summary {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.score-label {
  font-size: 28rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.25);
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  margin-top: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.share-button text {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.share-button image {
  width: 32rpx;
  height: 32rpx;
}

/* 屏幕适配调整 */
@media screen and (max-height: 700px) {
  .welcome-message, .question-card, .result-container {
    padding: 20rpx;
    margin-bottom: 30rpx;
  }
  
  .welcome-title, .result-title {
    font-size: 34rpx;
    margin-bottom: 15rpx;
  }
  
  .welcome-subtitle, .question-title {
    font-size: 26rpx;
    margin-bottom: 20rpx;
  }
  
  .option-item {
    padding: 16rpx;
    margin-bottom: 15rpx;
  }
}

/* 自定义导航栏 - 添加教师主题色 */
.custom-nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 100;
  background: rgba(22, 163, 74, 0.8); /* 教师主题色半透明 */
}

.settings-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(22, 163, 74, 0.3); /* 教师主题色 */
  border-radius: 50%;
}

.settings-icon:active {
  background: rgba(22, 163, 74, 0.5);
}

/* 标签容器 */
.tab-container {
  display: flex;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  background: rgba(22, 163, 74, 0.7); /* 教师主题色半透明 */
}

.tab.active {
  background: rgba(22, 163, 74, 0.9); /* 教师主题色 */
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 修改按钮样式使用教师主题色 */
.welcome-start-button {
  background: rgba(22, 163, 74, 0.9); /* 教师主题色 */
}

/* 打字动画点 */
.typing-dot {
  background: #16a34a; /* 教师主题色 */
}

/* 查看报告按钮 */
.action-button.view-report {
  background: rgba(22, 163, 74, 0.9); /* 教师主题色 */
}

/* 教师主题专用样式 */
.teacher-theme .custom-nav-bar {
  background: rgba(22, 163, 74, 0.8);
}

.teacher-theme .settings-icon {
  background: rgba(22, 163, 74, 0.3);
}

.teacher-theme .settings-icon:active {
  background: rgba(22, 163, 74, 0.5);
}

.teacher-theme .tab.active {
  background: rgba(22, 163, 74, 0.9);
}

.teacher-theme .welcome-start-button {
  background: rgba(22, 163, 74, 0.9);
}

.teacher-theme .send-button {
  background: rgba(22, 163, 74, 0.8);
}

.teacher-theme .send-button.active {
  background: rgba(22, 163, 74, 1.0);
}

.teacher-theme .voice-button {
  background: rgba(22, 163, 74, 0.5);
}

.teacher-theme .voice-button:active {
  background: rgba(22, 163, 74, 0.7);
}

.teacher-theme .typing-dot {
  background: #16a34a;
}

.teacher-theme .action-button.view-report {
  background: rgba(22, 163, 74, 0.9);
} 