#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自坐分析系统
实现日干在各地支的坐支分析，对标"问真八字"的自坐功能
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class ZuozhiInfo:
    """坐支信息"""
    day_gan: str                    # 日干
    day_zhi: str                    # 日支
    zuozhi_type: str               # 坐支类型
    zuozhi_strength: str           # 坐支强度
    hidden_gan_analysis: Dict      # 藏干分析
    shishen_relationship: Dict     # 十神关系
    wuxing_relationship: str       # 五行关系
    energy_support: str            # 能量支撑
    personality_influence: List[str] # 性格影响
    marriage_influence: str        # 婚姻影响
    career_influence: str          # 事业影响
    health_influence: str          # 健康影响
    fortune_characteristics: str   # 运势特征
    development_advice: List[str]  # 发展建议

@dataclass
class ComprehensiveZuozhiResult:
    """完整自坐分析结果"""
    zuozhi_info: ZuozhiInfo        # 坐支信息
    compatibility_analysis: Dict   # 配合度分析
    life_foundation: str           # 人生根基
    core_characteristics: List[str] # 核心特征
    relationship_pattern: str      # 关系模式
    success_factors: List[str]     # 成功要素
    challenge_areas: List[str]     # 挑战领域
    optimization_suggestions: List[str] # 优化建议

class ComprehensiveZuozhiAnalyzer:
    """完整的自坐分析系统"""
    
    def __init__(self):
        """初始化自坐分析器"""
        # 天干五行属性
        self.tiangan_wuxing = {
            "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
            "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水"
        }
        
        # 地支五行属性
        self.dizhi_wuxing = {
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
            "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
        }
        
        # 地支藏干
        self.dizhi_canggan = {
            "子": ["癸"], "丑": ["己", "癸", "辛"], "寅": ["甲", "丙", "戊"],
            "卯": ["乙"], "辰": ["戊", "乙", "癸"], "巳": ["丙", "戊", "庚"],
            "午": ["丁", "己"], "未": ["己", "丁", "乙"], "申": ["庚", "壬", "戊"],
            "酉": ["辛"], "戌": ["戊", "辛", "丁"], "亥": ["壬", "甲"]
        }
        
        # 坐支类型数据库
        self.zuozhi_database = self._init_zuozhi_database()
    
    def _init_zuozhi_database(self) -> Dict:
        """初始化坐支类型数据库"""
        return {
            # 甲木坐支
            "甲子": {"type": "偏印坐支", "strength": "中", "characteristics": "聪明好学，但容易孤独"},
            "甲寅": {"type": "比肩坐支", "strength": "强", "characteristics": "自立自强，领导能力强"},
            "甲辰": {"type": "偏财坐支", "strength": "中", "characteristics": "善于理财，但需防小人"},
            "甲午": {"type": "伤官坐支", "strength": "中", "characteristics": "才华横溢，但易冲动"},
            "甲申": {"type": "偏官坐支", "strength": "弱", "characteristics": "有压力，需要坚持"},
            "甲戌": {"type": "偏财坐支", "strength": "中", "characteristics": "财运不错，但需努力"},
            
            # 乙木坐支
            "乙丑": {"type": "偏财坐支", "strength": "中", "characteristics": "勤俭持家，财运稳定"},
            "乙卯": {"type": "比肩坐支", "strength": "强", "characteristics": "性格温和，人缘好"},
            "乙巳": {"type": "正财坐支", "strength": "强", "characteristics": "财运亨通，事业有成"},
            "乙未": {"type": "偏印坐支", "strength": "中", "characteristics": "有艺术天赋，但需专注"},
            "乙酉": {"type": "正官坐支", "strength": "强", "characteristics": "有权威，事业发达"},
            "乙亥": {"type": "正印坐支", "strength": "强", "characteristics": "学识渊博，德高望重"},
            
            # 丙火坐支
            "丙子": {"type": "正官坐支", "strength": "中", "characteristics": "有责任心，但需要支持"},
            "丙寅": {"type": "偏印坐支", "strength": "强", "characteristics": "创新能力强，适合技术"},
            "丙辰": {"type": "伤官坐支", "strength": "中", "characteristics": "聪明机智，但需要耐心"},
            "丙午": {"type": "比肩坐支", "strength": "极强", "characteristics": "热情洋溢，领导力强"},
            "丙申": {"type": "偏财坐支", "strength": "中", "characteristics": "善于投资，机遇多"},
            "丙戌": {"type": "伤官坐支", "strength": "中", "characteristics": "有才华，但需要约束"},
            
            # 丁火坐支
            "丁丑": {"type": "食神坐支", "strength": "中", "characteristics": "温和善良，有艺术天赋"},
            "丁卯": {"type": "偏印坐支", "strength": "中", "characteristics": "直觉敏锐，适合研究"},
            "丁巳": {"type": "劫财坐支", "strength": "强", "characteristics": "意志坚强，但需合作"},
            "丁未": {"type": "食神坐支", "strength": "强", "characteristics": "才华出众，享受生活"},
            "丁酉": {"type": "正财坐支", "strength": "强", "characteristics": "财运佳，善于经营"},
            "丁亥": {"type": "正官坐支", "strength": "强", "characteristics": "有威信，事业成功"},
            
            # 戊土坐支
            "戊子": {"type": "正财坐支", "strength": "中", "characteristics": "理财有方，但需努力"},
            "戊寅": {"type": "偏官坐支", "strength": "中", "characteristics": "有魄力，适合管理"},
            "戊辰": {"type": "比肩坐支", "strength": "强", "characteristics": "稳重可靠，基础扎实"},
            "戊午": {"type": "伤官坐支", "strength": "强", "characteristics": "创新能力强，但需专注"},
            "戊申": {"type": "食神坐支", "strength": "强", "characteristics": "享受生活，有口福"},
            "戊戌": {"type": "比肩坐支", "strength": "强", "characteristics": "忠诚可靠，但需变通"},
            
            # 己土坐支
            "己丑": {"type": "比肩坐支", "strength": "强", "characteristics": "踏实稳重，值得信赖"},
            "己卯": {"type": "偏官坐支", "strength": "中", "characteristics": "有压力，但能成长"},
            "己巳": {"type": "正印坐支", "strength": "强", "characteristics": "学识丰富，有修养"},
            "己未": {"type": "比肩坐支", "strength": "强", "characteristics": "性格温和，人际和谐"},
            "己酉": {"type": "食神坐支", "strength": "强", "characteristics": "有口才，善于表达"},
            "己亥": {"type": "正财坐支", "strength": "强", "characteristics": "财运亨通，生活富足"},
            
            # 庚金坐支
            "庚子": {"type": "偏印坐支", "strength": "中", "characteristics": "聪明机智，但需要耐心"},
            "庚寅": {"type": "偏财坐支", "strength": "中", "characteristics": "财运波动，需要稳定"},
            "庚辰": {"type": "伤官坐支", "strength": "中", "characteristics": "有才华，但需要约束"},
            "庚午": {"type": "正官坐支", "strength": "中", "characteristics": "有权威，但需要支持"},
            "庚申": {"type": "比肩坐支", "strength": "极强", "characteristics": "意志坚强，独立性强"},
            "庚戌": {"type": "伤官坐支", "strength": "中", "characteristics": "创新能力强，适合技术"},
            
            # 辛金坐支
            "辛丑": {"type": "食神坐支", "strength": "强", "characteristics": "有品味，享受生活"},
            "辛卯": {"type": "正财坐支", "strength": "强", "characteristics": "财运佳，善于理财"},
            "辛巳": {"type": "正印坐支", "strength": "强", "characteristics": "有学识，品格高尚"},
            "辛未": {"type": "食神坐支", "strength": "强", "characteristics": "温文尔雅，有艺术天赋"},
            "辛酉": {"type": "比肩坐支", "strength": "极强", "characteristics": "精明能干，追求完美"},
            "辛亥": {"type": "偏财坐支", "strength": "强", "characteristics": "机遇多，善于把握"},
            
            # 壬水坐支
            "壬子": {"type": "比肩坐支", "strength": "极强", "characteristics": "智慧过人，适应性强"},
            "壬寅": {"type": "食神坐支", "strength": "强", "characteristics": "才华横溢，创造力强"},
            "壬辰": {"type": "偏财坐支", "strength": "中", "characteristics": "财运不错，但需努力"},
            "壬午": {"type": "正财坐支", "strength": "中", "characteristics": "理财有方，但需平衡"},
            "壬申": {"type": "偏印坐支", "strength": "强", "characteristics": "直觉敏锐，适合研究"},
            "壬戌": {"type": "偏财坐支", "strength": "中", "characteristics": "善于投资，机会多"},
            
            # 癸水坐支
            "癸丑": {"type": "偏财坐支", "strength": "中", "characteristics": "勤俭节约，财运稳定"},
            "癸卯": {"type": "食神坐支", "strength": "强", "characteristics": "温和善良，有艺术天赋"},
            "癸巳": {"type": "正官坐支", "strength": "强", "characteristics": "有威信，事业有成"},
            "癸未": {"type": "偏财坐支", "strength": "中", "characteristics": "理财有方，但需耐心"},
            "癸酉": {"type": "偏印坐支", "strength": "中", "characteristics": "聪明机智，但需专注"},
            "癸亥": {"type": "比肩坐支", "strength": "极强", "characteristics": "智慧深沉，洞察力强"}
        }
    
    def analyze_zuozhi(self, day_gan: str, day_zhi: str) -> ComprehensiveZuozhiResult:
        """分析日干坐支"""
        # 获取坐支信息
        zuozhi_info = self._analyze_zuozhi_info(day_gan, day_zhi)
        
        # 配合度分析
        compatibility_analysis = self._analyze_compatibility(day_gan, day_zhi)
        
        # 人生根基
        life_foundation = self._analyze_life_foundation(zuozhi_info)
        
        # 核心特征
        core_characteristics = self._extract_core_characteristics(zuozhi_info)
        
        # 关系模式
        relationship_pattern = self._analyze_relationship_pattern(zuozhi_info)
        
        # 成功要素
        success_factors = self._identify_success_factors(zuozhi_info)
        
        # 挑战领域
        challenge_areas = self._identify_challenge_areas(zuozhi_info)
        
        # 优化建议
        optimization_suggestions = self._generate_optimization_suggestions(zuozhi_info)
        
        return ComprehensiveZuozhiResult(
            zuozhi_info=zuozhi_info,
            compatibility_analysis=compatibility_analysis,
            life_foundation=life_foundation,
            core_characteristics=core_characteristics,
            relationship_pattern=relationship_pattern,
            success_factors=success_factors,
            challenge_areas=challenge_areas,
            optimization_suggestions=optimization_suggestions
        )
    
    def _analyze_zuozhi_info(self, day_gan: str, day_zhi: str) -> ZuozhiInfo:
        """分析坐支信息"""
        ganzhi_key = day_gan + day_zhi
        zuozhi_data = self.zuozhi_database.get(ganzhi_key, {
            "type": "一般坐支", "strength": "中", "characteristics": "性格平和，发展稳定"
        })
        
        # 藏干分析
        hidden_gans = self.dizhi_canggan.get(day_zhi, [])
        hidden_gan_analysis = self._analyze_hidden_gans(day_gan, hidden_gans)
        
        # 十神关系
        shishen_relationship = self._analyze_shishen_relationship(day_gan, hidden_gans)
        
        # 五行关系
        wuxing_relationship = self._analyze_wuxing_relationship(day_gan, day_zhi)
        
        # 能量支撑
        energy_support = self._analyze_energy_support(day_gan, day_zhi, hidden_gans)
        
        # 各种影响分析
        personality_influence = self._analyze_personality_influence(zuozhi_data, hidden_gan_analysis)
        marriage_influence = self._analyze_marriage_influence(day_gan, day_zhi, zuozhi_data)
        career_influence = self._analyze_career_influence(zuozhi_data, shishen_relationship)
        health_influence = self._analyze_health_influence(day_gan, day_zhi)
        fortune_characteristics = self._analyze_fortune_characteristics(zuozhi_data)
        development_advice = self._generate_development_advice(zuozhi_data, shishen_relationship)
        
        return ZuozhiInfo(
            day_gan=day_gan,
            day_zhi=day_zhi,
            zuozhi_type=zuozhi_data["type"],
            zuozhi_strength=zuozhi_data["strength"],
            hidden_gan_analysis=hidden_gan_analysis,
            shishen_relationship=shishen_relationship,
            wuxing_relationship=wuxing_relationship,
            energy_support=energy_support,
            personality_influence=personality_influence,
            marriage_influence=marriage_influence,
            career_influence=career_influence,
            health_influence=health_influence,
            fortune_characteristics=fortune_characteristics,
            development_advice=development_advice
        )
    
    def _analyze_hidden_gans(self, day_gan: str, hidden_gans: List[str]) -> Dict:
        """分析藏干"""
        analysis = {"主气": "", "中气": [], "余气": [], "支撑度": ""}
        
        if hidden_gans:
            analysis["主气"] = hidden_gans[0]
            if len(hidden_gans) > 1:
                analysis["中气"] = hidden_gans[1:2]
            if len(hidden_gans) > 2:
                analysis["余气"] = hidden_gans[2:]
            
            # 计算支撑度
            same_element_count = sum(1 for gan in hidden_gans 
                                   if self.tiangan_wuxing[gan] == self.tiangan_wuxing[day_gan])
            if same_element_count >= 2:
                analysis["支撑度"] = "强"
            elif same_element_count == 1:
                analysis["支撑度"] = "中"
            else:
                analysis["支撑度"] = "弱"
        
        return analysis
    
    def _analyze_shishen_relationship(self, day_gan: str, hidden_gans: List[str]) -> Dict:
        """分析十神关系"""
        relationships = {}
        
        for gan in hidden_gans:
            if gan == day_gan:
                relationships[gan] = "比肩"
            else:
                # 简化的十神关系判断
                day_wuxing = self.tiangan_wuxing[day_gan]
                gan_wuxing = self.tiangan_wuxing[gan]
                
                if day_wuxing == gan_wuxing:
                    relationships[gan] = "劫财"
                elif self._is_sheng_relation(day_wuxing, gan_wuxing):
                    relationships[gan] = "食神"
                elif self._is_sheng_relation(gan_wuxing, day_wuxing):
                    relationships[gan] = "正印"
                elif self._is_ke_relation(day_wuxing, gan_wuxing):
                    relationships[gan] = "正财"
                elif self._is_ke_relation(gan_wuxing, day_wuxing):
                    relationships[gan] = "正官"
                else:
                    relationships[gan] = "比肩"
        
        return relationships
    
    def _is_sheng_relation(self, element1: str, element2: str) -> bool:
        """判断五行相生关系"""
        sheng_relations = {
            "木": "火", "火": "土", "土": "金", "金": "水", "水": "木"
        }
        return sheng_relations.get(element1) == element2
    
    def _is_ke_relation(self, element1: str, element2: str) -> bool:
        """判断五行相克关系"""
        ke_relations = {
            "木": "土", "土": "水", "水": "火", "火": "金", "金": "木"
        }
        return ke_relations.get(element1) == element2
    
    def _analyze_wuxing_relationship(self, day_gan: str, day_zhi: str) -> str:
        """分析五行关系"""
        gan_wuxing = self.tiangan_wuxing[day_gan]
        zhi_wuxing = self.dizhi_wuxing[day_zhi]
        
        if gan_wuxing == zhi_wuxing:
            return "同气相求，根基稳固"
        elif self._is_sheng_relation(zhi_wuxing, gan_wuxing):
            return "地支生天干，得到支撑"
        elif self._is_sheng_relation(gan_wuxing, zhi_wuxing):
            return "天干生地支，有所消耗"
        elif self._is_ke_relation(zhi_wuxing, gan_wuxing):
            return "地支克天干，受到压制"
        elif self._is_ke_relation(gan_wuxing, zhi_wuxing):
            return "天干克地支，需要调和"
        else:
            return "关系平和，相对稳定"
    
    def _analyze_energy_support(self, day_gan: str, day_zhi: str, hidden_gans: List[str]) -> str:
        """分析能量支撑"""
        gan_wuxing = self.tiangan_wuxing[day_gan]
        support_count = 0
        
        # 检查地支本气
        zhi_wuxing = self.dizhi_wuxing[day_zhi]
        if zhi_wuxing == gan_wuxing or self._is_sheng_relation(zhi_wuxing, gan_wuxing):
            support_count += 2
        
        # 检查藏干
        for gan in hidden_gans:
            gan_wuxing_hidden = self.tiangan_wuxing[gan]
            if gan_wuxing_hidden == gan_wuxing or self._is_sheng_relation(gan_wuxing_hidden, gan_wuxing):
                support_count += 1
        
        if support_count >= 3:
            return "强力支撑，根基深厚"
        elif support_count >= 2:
            return "良好支撑，发展稳定"
        elif support_count >= 1:
            return "一般支撑，需要努力"
        else:
            return "支撑不足，需要外助"
    
    def _analyze_personality_influence(self, zuozhi_data: Dict, hidden_gan_analysis: Dict) -> List[str]:
        """分析性格影响"""
        influences = [zuozhi_data["characteristics"]]
        
        # 基于支撑度的性格特征
        support_level = hidden_gan_analysis.get("支撑度", "中")
        if support_level == "强":
            influences.append("自信心强，独立性好")
        elif support_level == "弱":
            influences.append("需要他人支持，依赖性较强")
        
        return influences
    
    def _analyze_marriage_influence(self, day_gan: str, day_zhi: str, zuozhi_data: Dict) -> str:
        """分析婚姻影响"""
        # 基于坐支类型分析婚姻
        zuozhi_type = zuozhi_data["type"]
        
        if "正财" in zuozhi_type or "正官" in zuozhi_type:
            return "婚姻稳定，配偶贤良"
        elif "偏财" in zuozhi_type or "偏官" in zuozhi_type:
            return "感情丰富，需要沟通"
        elif "比肩" in zuozhi_type or "劫财" in zuozhi_type:
            return "重视平等，需要理解"
        elif "食神" in zuozhi_type:
            return "感情温和，家庭和睦"
        elif "伤官" in zuozhi_type:
            return "感情复杂，需要包容"
        elif "正印" in zuozhi_type or "偏印" in zuozhi_type:
            return "重视精神交流，感情深厚"
        else:
            return "感情平稳，需要经营"
    
    def _analyze_career_influence(self, zuozhi_data: Dict, shishen_relationship: Dict) -> str:
        """分析事业影响"""
        zuozhi_type = zuozhi_data["type"]
        
        career_map = {
            "正官": "适合管理、公职等权威性工作",
            "偏官": "适合竞争性强的行业",
            "正财": "适合金融、商业等财务工作",
            "偏财": "适合投资、贸易等灵活性工作",
            "食神": "适合文艺、教育等创意性工作",
            "伤官": "适合技术、创新等专业性工作",
            "比肩": "适合独立创业",
            "劫财": "适合合作性工作",
            "正印": "适合教育、文化等知识性工作",
            "偏印": "适合研究、技术等专业性工作"
        }
        
        for shishen in career_map:
            if shishen in zuozhi_type:
                return career_map[shishen]
        
        return "事业发展平稳，需要努力"
    
    def _analyze_health_influence(self, day_gan: str, day_zhi: str) -> str:
        """分析健康影响"""
        gan_wuxing = self.tiangan_wuxing[day_gan]
        
        health_map = {
            "木": "注意肝胆、眼部健康",
            "火": "注意心脏、血压健康",
            "土": "注意脾胃、消化健康",
            "金": "注意肺部、呼吸健康",
            "水": "注意肾脏、泌尿健康"
        }
        
        return health_map.get(gan_wuxing, "身体健康，注意保养")
    
    def _analyze_fortune_characteristics(self, zuozhi_data: Dict) -> str:
        """分析运势特征"""
        strength = zuozhi_data["strength"]
        characteristics = zuozhi_data["characteristics"]
        
        if strength == "极强":
            return f"运势强劲，{characteristics}，发展潜力巨大"
        elif strength == "强":
            return f"运势良好，{characteristics}，前景光明"
        elif strength == "中":
            return f"运势平稳，{characteristics}，需要努力"
        else:
            return f"运势一般，{characteristics}，需要调整"
    
    def _generate_development_advice(self, zuozhi_data: Dict, shishen_relationship: Dict) -> List[str]:
        """生成发展建议"""
        advice = []
        
        # 基于坐支强度的建议
        strength = zuozhi_data["strength"]
        if strength in ["极强", "强"]:
            advice.append("发挥自身优势，积极进取")
        else:
            advice.append("加强基础建设，寻求外部支持")
        
        # 基于十神关系的建议
        if "正官" in shishen_relationship.values():
            advice.append("培养责任感，建立权威")
        if "正财" in shishen_relationship.values():
            advice.append("注重理财，稳定收入")
        if "食神" in shishen_relationship.values():
            advice.append("发挥才华，享受生活")
        
        return advice[:3]  # 返回前3个建议
    
    def _analyze_compatibility(self, day_gan: str, day_zhi: str) -> Dict:
        """分析配合度"""
        gan_wuxing = self.tiangan_wuxing[day_gan]
        zhi_wuxing = self.dizhi_wuxing[day_zhi]
        
        compatibility = {"五行配合": "", "能量配合": "", "整体评价": ""}
        
        if gan_wuxing == zhi_wuxing:
            compatibility["五行配合"] = "完美配合"
            compatibility["能量配合"] = "能量集中"
            compatibility["整体评价"] = "配合度极佳，根基稳固"
        elif self._is_sheng_relation(zhi_wuxing, gan_wuxing):
            compatibility["五行配合"] = "相生配合"
            compatibility["能量配合"] = "得到支撑"
            compatibility["整体评价"] = "配合度良好，发展顺利"
        else:
            compatibility["五行配合"] = "一般配合"
            compatibility["能量配合"] = "需要调和"
            compatibility["整体评价"] = "配合度一般，需要努力"
        
        return compatibility
    
    def _analyze_life_foundation(self, zuozhi_info: ZuozhiInfo) -> str:
        """分析人生根基"""
        strength = zuozhi_info.zuozhi_strength
        support = zuozhi_info.energy_support
        
        if strength in ["极强", "强"] and "强力" in support:
            return "根基深厚，人生基础扎实"
        elif strength == "中" and "良好" in support:
            return "根基稳定，发展平稳"
        else:
            return "根基一般，需要加强"
    
    def _extract_core_characteristics(self, zuozhi_info: ZuozhiInfo) -> List[str]:
        """提取核心特征"""
        characteristics = []
        characteristics.extend(zuozhi_info.personality_influence)
        characteristics.append(zuozhi_info.wuxing_relationship)
        return characteristics[:3]
    
    def _analyze_relationship_pattern(self, zuozhi_info: ZuozhiInfo) -> str:
        """分析关系模式"""
        return zuozhi_info.marriage_influence
    
    def _identify_success_factors(self, zuozhi_info: ZuozhiInfo) -> List[str]:
        """识别成功要素"""
        factors = []
        
        if zuozhi_info.zuozhi_strength in ["极强", "强"]:
            factors.append("自身实力强")
        
        if "强力" in zuozhi_info.energy_support:
            factors.append("根基深厚")
        
        if "正官" in zuozhi_info.zuozhi_type or "正财" in zuozhi_info.zuozhi_type:
            factors.append("格局正统")
        
        return factors[:3]
    
    def _identify_challenge_areas(self, zuozhi_info: ZuozhiInfo) -> List[str]:
        """识别挑战领域"""
        challenges = []
        
        if zuozhi_info.zuozhi_strength == "弱":
            challenges.append("自身实力需要提升")
        
        if "不足" in zuozhi_info.energy_support:
            challenges.append("需要外部支持")
        
        if "伤官" in zuozhi_info.zuozhi_type:
            challenges.append("需要控制情绪")
        
        return challenges[:3]
    
    def _generate_optimization_suggestions(self, zuozhi_info: ZuozhiInfo) -> List[str]:
        """生成优化建议"""
        suggestions = []
        suggestions.extend(zuozhi_info.development_advice)
        
        # 基于健康的建议
        suggestions.append(f"健康方面：{zuozhi_info.health_influence}")
        
        return suggestions[:4]


# 测试和使用示例
def main():
    """测试自坐分析系统"""
    print("🎯 自坐分析系统测试")
    print("=" * 50)
    
    # 创建自坐分析器
    analyzer = ComprehensiveZuozhiAnalyzer()
    
    # 测试数据 - 日柱
    day_gan = "丁"
    day_zhi = "丑"
    
    # 进行自坐分析
    result = analyzer.analyze_zuozhi(day_gan, day_zhi)
    
    # 显示坐支信息
    zuozhi = result.zuozhi_info
    print(f"🏛️ 日柱自坐分析 ({day_gan}{day_zhi}):")
    print(f"   坐支类型: {zuozhi.zuozhi_type}")
    print(f"   坐支强度: {zuozhi.zuozhi_strength}")
    print(f"   五行关系: {zuozhi.wuxing_relationship}")
    print(f"   能量支撑: {zuozhi.energy_support}")
    print(f"   运势特征: {zuozhi.fortune_characteristics}")
    
    # 显示藏干分析
    print(f"\n🔍 藏干分析:")
    for key, value in zuozhi.hidden_gan_analysis.items():
        print(f"   {key}: {value}")
    
    # 显示十神关系
    print(f"\n⭐ 十神关系:")
    for gan, shishen in zuozhi.shishen_relationship.items():
        print(f"   {gan} → {shishen}")
    
    # 显示各种影响
    print(f"\n👤 性格影响: {', '.join(zuozhi.personality_influence)}")
    print(f"💑 婚姻影响: {zuozhi.marriage_influence}")
    print(f"💼 事业影响: {zuozhi.career_influence}")
    print(f"🏥 健康影响: {zuozhi.health_influence}")
    
    # 显示配合度分析
    print(f"\n🤝 配合度分析:")
    for key, value in result.compatibility_analysis.items():
        print(f"   {key}: {value}")
    
    # 显示综合分析
    print(f"\n🏗️ 人生根基: {result.life_foundation}")
    print(f"🎭 核心特征: {', '.join(result.core_characteristics)}")
    print(f"💕 关系模式: {result.relationship_pattern}")
    
    # 显示成功要素和挑战
    if result.success_factors:
        print(f"✅ 成功要素: {', '.join(result.success_factors)}")
    if result.challenge_areas:
        print(f"⚠️ 挑战领域: {', '.join(result.challenge_areas)}")
    
    # 显示发展建议
    print(f"\n💡 发展建议:")
    for i, advice in enumerate(zuozhi.development_advice, 1):
        print(f"   {i}. {advice}")
    
    # 显示优化建议
    print(f"\n🔧 优化建议:")
    for i, suggestion in enumerate(result.optimization_suggestions, 1):
        print(f"   {i}. {suggestion}")
    
    print("\n✅ 自坐分析系统测试完成！")


if __name__ == "__main__":
    main()
