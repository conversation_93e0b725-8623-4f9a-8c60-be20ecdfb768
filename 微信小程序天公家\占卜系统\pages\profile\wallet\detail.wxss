/* pages/profile/wallet/detail.wxss */

.wallet-detail-container {
  padding: 30rpx;
  background-color: #f8f9fc;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
}

/* 标签切换 */
.tab-container {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #6C5CE7;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #6C5CE7;
  border-radius: 3rpx;
}

/* 筛选选项 */
.filter-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  margin-left: 16rpx;
}

.filter-option.active {
  color: #ffffff;
  background-color: #6C5CE7;
}

/* 记录列表 */
.records-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-left {
  flex: 1;
}

.record-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.record-date {
  font-size: 24rpx;
  color: #999999;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.record-amount.consume {
  color: #FF6B6B;
}

.record-amount.recharge {
  color: #6C5CE7;
}

.record-status {
  font-size: 24rpx;
  color: #999999;
}

/* 空记录提示 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}