/**
 * 测试《千里命稿》权威神煞计算系统
 * 验证与"问真八字"标准的匹配度
 */

// 模拟前端计算函数
const Qi<PERSON>liShenshaCalculator = {
  // 天乙贵人（《千里命稿》权威版本）
  calculateQianliTianyiGuiren: function(dayGan, fourPillars) {
    const qianliTianyiMap = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['酉', '亥'], '丁': ['酉', '亥'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };

    const targets = qianliTianyiMap[dayGan] || [];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    targets.forEach(target => {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === target) {
          result.push({
            name: '天乙贵人',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    });

    return result;
  },

  // 文昌贵人（《千里命稿》权威版本）
  calculateQianliWenchangGuiren: function(dayGan, fourPillars) {
    const qianliWenchangMap = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉',
      '戊': '申', '己': '酉', '庚': '亥', '辛': '子',
      '壬': '寅', '癸': '卯'
    };

    const target = qianliWenchangMap[dayGan];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === target) {
          result.push({
            name: '文昌贵人',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  },

  // 桃花（咸池）（《千里命稿》权威版本）
  calculateQianliTaohua: function(yearZhi, fourPillars) {
    const qianliTaohuaMap = {
      '申': '酉', '子': '酉', '辰': '酉',  // 申子辰年逢酉
      '亥': '子', '卯': '子', '未': '子',  // 亥卯未年逢子
      '寅': '卯', '午': '卯', '戌': '卯',  // 寅午戌年逢卯
      '巳': '午', '酉': '午', '丑': '午'   // 巳酉丑年逢午
    };

    const target = qianliTaohuaMap[yearZhi];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === target) {
          result.push({
            name: '桃花',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  },

  // 驿马（《千里命稿》权威版本）
  calculateQianliYima: function(yearZhi, fourPillars) {
    const qianliYimaMap = {
      '亥': '巳', '卯': '巳', '未': '巳',  // 亥卯未年逢巳
      '寅': '申', '午': '申', '戌': '申',  // 寅午戌年逢申
      '申': '寅', '子': '寅', '辰': '寅',  // 申子辰年逢寅
      '巳': '亥', '酉': '亥', '丑': '亥'   // 巳酉丑年逢亥
    };

    const target = qianliYimaMap[yearZhi];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === target) {
          result.push({
            name: '驿马',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  },

  // 天德（《千里命稿》权威版本）
  calculateQianliTiande: function(monthZhi, fourPillars) {
    const qianliTiandeMap = {
      '寅': '丁', '卯': '申', '辰': '壬', '巳': '辛',  // 正月-四月
      '午': '亥', '未': '甲', '申': '癸', '酉': '寅',  // 五月-八月
      '戌': '丙', '亥': '乙', '子': '巳', '丑': '庚'   // 九月-十二月
    };

    const target = qianliTiandeMap[monthZhi];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === target || pillar.zhi === target) {
          result.push({
            name: '天德',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  },

  // 月德（《千里命稿》权威版本）
  calculateQianliYuede: function(yearZhi, fourPillars) {
    const qianliYuedeMap = {
      '寅': '丙', '午': '丙', '戌': '丙',  // 寅午戌年逢丙
      '申': '壬', '子': '壬', '辰': '壬',  // 申子辰年逢壬
      '亥': '甲', '卯': '甲', '未': '甲',  // 亥卯未年逢甲
      '巳': '庚', '酉': '庚', '丑': '庚'   // 巳酉丑年逢庚
    };

    const target = qianliYuedeMap[yearZhi];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === target) {
          result.push({
            name: '月德',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  },

  // 月德合（《千里命稿》权威版本）
  calculateQianliYuehe: function(yearZhi, fourPillars) {
    const qianliYuedeMap = {
      '寅': '丙', '午': '丙', '戌': '丙',  // 寅午戌年逢丙
      '申': '壬', '子': '壬', '辰': '壬',  // 申子辰年逢壬
      '亥': '甲', '卯': '甲', '未': '甲',  // 亥卯未年逢甲
      '巳': '庚', '酉': '庚', '丑': '庚'   // 巳酉丑年逢庚
    };

    const ganHeMap = {
      '甲': '己', '己': '甲',
      '乙': '庚', '庚': '乙',
      '丙': '辛', '辛': '丙',
      '丁': '壬', '壬': '丁',
      '戊': '癸', '癸': '戊'
    };

    const yuede = qianliYuedeMap[yearZhi];
    const target = yuede ? ganHeMap[yuede] : null;
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (target) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === target) {
          result.push({
            name: '月德合',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  }
};

// 测试数据：2021年6月24日 19:30 (辛丑 甲午 癸卯 壬戌)
const testFourPillars = [
  { gan: '辛', zhi: '丑' },  // 年柱
  { gan: '甲', zhi: '午' },  // 月柱
  { gan: '癸', zhi: '卯' },  // 日柱
  { gan: '壬', zhi: '戌' }   // 时柱
];

const dayGan = '癸';
const yearZhi = '丑';
const monthZhi = '午';

console.log('🧪 测试《千里命稿》权威神煞计算系统');
console.log('📅 测试数据: 2021年6月24日 19:30 (辛丑 甲午 癸卯 壬戌)');
console.log('');

// 测试各种神煞计算
const tianyiGuiren = QianliShenshaCalculator.calculateQianliTianyiGuiren(dayGan, testFourPillars);
const wenchangGuiren = QianliShenshaCalculator.calculateQianliWenchangGuiren(dayGan, testFourPillars);
const taohua = QianliShenshaCalculator.calculateQianliTaohua(yearZhi, testFourPillars);
const yima = QianliShenshaCalculator.calculateQianliYima(yearZhi, testFourPillars);
const tiande = QianliShenshaCalculator.calculateQianliTiande(monthZhi, testFourPillars);
const yuede = QianliShenshaCalculator.calculateQianliYuede(yearZhi, testFourPillars);
const yuehe = QianliShenshaCalculator.calculateQianliYuehe(yearZhi, testFourPillars);

console.log('📊 《千里命稿》神煞计算结果:');
console.log('天乙贵人:', tianyiGuiren);
console.log('文昌贵人:', wenchangGuiren);
console.log('桃花:', taohua);
console.log('驿马:', yima);
console.log('天德:', tiande);
console.log('月德:', yuede);
console.log('月德合:', yuehe);

// 统计结果
const allShensha = [
  ...tianyiGuiren,
  ...wenchangGuiren,
  ...taohua,
  ...yima,
  ...tiande,
  ...yuede,
  ...yuehe
];

console.log('');
console.log('📈 统计结果:');
console.log(`总计神煞: ${allShensha.length} 个`);

// 按柱分组统计
const byPillar = {
  '年柱': allShensha.filter(s => s.position === '年柱'),
  '月柱': allShensha.filter(s => s.position === '月柱'),
  '日柱': allShensha.filter(s => s.position === '日柱'),
  '时柱': allShensha.filter(s => s.position === '时柱')
};

console.log('按柱分布:');
Object.keys(byPillar).forEach(pillar => {
  console.log(`${pillar}: ${byPillar[pillar].length} 个 - ${byPillar[pillar].map(s => s.name).join(', ')}`);
});

console.log('');
console.log('🎯 与"问真八字"标准对比:');
console.log('期望结果: 年柱(福星贵人, 月德合), 月柱(天乙贵人, 桃花, 元辰), 日柱(天乙贵人, 文昌贵人, 天厨贵人, 福星贵人, 德秀贵人, 童子煞, 灾煞, 丧门, 血刃), 时柱(寡宿, 披麻)');
console.log('实际结果:', JSON.stringify(byPillar, null, 2));
