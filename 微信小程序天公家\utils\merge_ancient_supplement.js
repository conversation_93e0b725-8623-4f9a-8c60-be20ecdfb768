/**
 * 古代补充数据合并工具
 * 将5个古代补充数据文件合并为完整的古代补充数据库
 */

const ancientSupplementPart1 = require('../data/ancient_supplement_part1');
const ancientSupplementPart2 = require('../data/ancient_supplement_part2');
const ancientSupplementPart3 = require('../data/ancient_supplement_part3');
const ancientSupplementPart4 = require('../data/ancient_supplement_part4');
const ancientSupplementPart5 = require('../data/ancient_supplement_part5');

function mergeAncientSupplementData() {
  console.log('开始合并古代补充数据...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...ancientSupplementPart1.celebrities,
    ...ancientSupplementPart2.celebrities,
    ...ancientSupplementPart3.celebrities,
    ...ancientSupplementPart4.celebrities,
    ...ancientSupplementPart5.celebrities
  ];

  // 统计朝代分布
  const dynastyStats = {};
  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建合并后的数据结构
  const mergedData = {
    metadata: {
      title: "古代补充人物数据库",
      description: "第四批次古代补充人物数据，包含秦朝到清朝的重要历史人物",
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      dataSource: "《史记》《汉书》《后汉书》《隋书》《旧五代史》《宋史》《元史》《明史》《清史稿》",
      verificationStandard: "专家交叉校验+古籍依据双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      dynastyDistribution: dynastyStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按朝代分类的统计信息
    dynastyAnalysis: {
      秦朝: {
        count: dynastyStats["秦朝"] || 0,
        representatives: ["李斯", "蒙恬", "扶苏"],
        characteristics: "法家思想、统一制度、军事扩张"
      },
      东汉: {
        count: dynastyStats["东汉"] || 0,
        representatives: ["班固", "张衡", "蔡伦", "马援"],
        characteristics: "史学发达、科技创新、文化繁荣"
      },
      隋朝: {
        count: dynastyStats["隋朝"] || 0,
        representatives: ["杨坚", "杨广"],
        characteristics: "统一南北、大运河、制度创新"
      },
      五代: {
        count: dynastyStats["五代"] || 0,
        representatives: ["朱温", "李存勖", "柴荣"],
        characteristics: "乱世争雄、政权更迭、为宋奠基"
      },
      北宋: {
        count: dynastyStats["北宋"] || 0,
        representatives: ["包拯", "司马光", "沈括"],
        characteristics: "文治兴盛、科技发达、理学兴起"
      },
      南宋: {
        count: dynastyStats["南宋"] || 0,
        representatives: ["岳飞", "文天祥", "辛弃疾", "朱熹", "李清照"],
        characteristics: "抗金抗元、文学繁荣、理学成熟"
      },
      元朝: {
        count: dynastyStats["元朝"] || 0,
        representatives: ["忽必烈", "郭守敬", "关汉卿", "马可·波罗"],
        characteristics: "民族融合、科技进步、文化交流"
      },
      明朝: {
        count: dynastyStats["明朝"] || 0,
        representatives: ["郑和", "于谦", "海瑞", "徐霞客", "李时珍", "宋应星"],
        characteristics: "海上丝路、科技发明、地理探索"
      },
      清朝: {
        count: dynastyStats["清朝"] || 0,
        representatives: ["康熙", "雍正", "乾隆", "和珅", "纪晓岚"],
        characteristics: "康乾盛世、文化集成、满汉融合"
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 古代补充数据合并完成 ===');
  console.log(`总计名人数量: ${mergedData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${mergedData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${mergedData.metadata.qualityGrade}`);
  console.log('\n朝代分布:');
  Object.entries(dynastyStats).forEach(([dynasty, count]) => {
    console.log(`  ${dynasty}: ${count}位`);
  });

  return mergedData;
}

// 数据质量检查
function validateAncientSupplementData(data) {
  console.log('\n开始数据质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查验证分数范围
  celebrities.forEach((celebrity, index) => {
    const score = celebrity.verification?.algorithmMatch;
    if (score && (score < 0 || score > 1)) {
      issues.push(`验证分数超出范围: ${celebrity.basicInfo?.name || index} (${score})`);
    }
  });

  // 检查古籍依据格式
  celebrities.forEach((celebrity, index) => {
    const evidence = celebrity.verification?.ancientTextEvidence;
    if (!evidence || !Array.isArray(evidence) || evidence.length === 0) {
      issues.push(`缺少古籍依据: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`数据质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const mergedData = mergeAncientSupplementData();
    const validation = validateAncientSupplementData(mergedData);
    
    if (validation.isValid) {
      console.log('\n✅ 数据质量检查通过！');
      
      // 保存合并后的数据
      const fs = require('fs');
      const outputPath = 'data/ancient_supplement_complete.js';
      const fileContent = `/**
 * 古代补充人物完整数据库
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 5个古代补充数据文件合并
 */

const ancientSupplementComplete = ${JSON.stringify(mergedData, null, 2)};

module.exports = ancientSupplementComplete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 合并数据已保存到: ${outputPath}`);
    } else {
      console.log('\n❌ 数据质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeAncientSupplementData,
  validateAncientSupplementData
};
