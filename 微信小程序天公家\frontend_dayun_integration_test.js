/**
 * 前端大运集成测试
 * 验证专业级大运计算器在微信小程序中的集成情况
 */

// 加载权威节气数据
const jieqiDataModule = require('./权威节气数据_前端就绪版.js');
global.FrontendReadyJieqiData = jieqiDataModule.FrontendReadyJieqiData;

const ProfessionalDayunCalculator = require('./utils/professional_dayun_calculator.js');

console.log('🧪 开始前端大运集成测试...\n');

/**
 * 模拟微信小程序环境的八字数据
 */
function createMockBaziData() {
  return {
    baziInfo: {
      yearPillar: { heavenly: '辛', earthly: '丑' },
      monthPillar: { heavenly: '甲', earthly: '午' },
      dayPillar: { heavenly: '癸', earthly: '卯' },
      timePillar: { heavenly: '壬', earthly: '戌' }
    },
    userInfo: {
      gender: '男'
    },
    birthInfo: {
      year: 2021,
      month: 6,
      day: 24,
      hour: 19,
      minute: 30
    }
  };
}

/**
 * 转换大运数据结构以适配前端显示
 */
function transformDayunDataForFrontend(dayunResult) {
  console.log('🔄 转换大运数据结构...');

  try {
    // 提取起运信息
    const qiyunInfo = {
      ageText: `${dayunResult.calculation.qiyunResult.qiyunAge.years}岁${dayunResult.calculation.qiyunResult.qiyunAge.months}个月`,
      description: `起运时间：${dayunResult.calculation.qiyunResult.qiyunAge.years}岁${dayunResult.calculation.qiyunResult.qiyunAge.months}个月${dayunResult.calculation.qiyunResult.qiyunAge.days}天`,
      qiyunDate: dayunResult.calculation.qiyunResult.qiyunDate,
      targetTerm: dayunResult.calculation.qiyunResult.targetTerm
    };

    // 提取方向信息
    const direction = {
      isForward: dayunResult.calculation.direction.isForward,
      description: dayunResult.calculation.direction.description,
      rule: dayunResult.calculation.direction.rule
    };

    // 转换大运序列
    const dayunSequence = dayunResult.calculation.dayunSequence.map((item) => ({
      step: item.sequence,
      gan: item.gan,
      zhi: item.zhi,
      ageStart: item.startAge,
      ageEnd: item.endAge,
      isCurrent: dayunResult.analysis.currentDayun && dayunResult.analysis.currentDayun.sequence === item.sequence
    }));

    // 提取当前大运信息
    let currentDayun = null;
    if (dayunResult.analysis.currentDayun && dayunResult.analysis.currentDayun.status === 'current') {
      const current = dayunResult.analysis.currentDayun;
      currentDayun = {
        gan: current.gan,
        zhi: current.zhi,
        ageRange: `${current.startAge}-${current.endAge}岁`,
        progressText: current.progressDescription,
        remainingYears: Math.ceil(current.remainingYears),
        status: current.status
      };
    }

    return {
      qiyunInfo: qiyunInfo,
      direction: direction,
      dayunSequence: dayunSequence,
      currentDayun: currentDayun,
      timeline: dayunResult.calculation.dayunTimeline,
      metadata: dayunResult.metadata
    };

  } catch (error) {
    console.error('❌ 数据转换失败:', error);
    return {
      qiyunInfo: { ageText: '转换失败', description: '数据转换过程中发生错误' },
      direction: { description: '转换失败' },
      dayunSequence: [],
      currentDayun: null
    };
  }
}

/**
 * 模拟前端计算方法
 */
function calculateProfessionalDayun(baziData) {
  console.log('🌟 模拟前端计算专业级大运数据...');

  try {
    // 使用专业级大运计算器
    const calculator = new ProfessionalDayunCalculator();

    // 构建八字数据格式
    const bazi = {
      yearPillar: {
        gan: baziData.baziInfo.yearPillar.heavenly,
        zhi: baziData.baziInfo.yearPillar.earthly
      },
      monthPillar: {
        gan: baziData.baziInfo.monthPillar.heavenly,
        zhi: baziData.baziInfo.monthPillar.earthly
      },
      dayPillar: {
        gan: baziData.baziInfo.dayPillar.heavenly,
        zhi: baziData.baziInfo.dayPillar.earthly
      },
      timePillar: {
        gan: baziData.baziInfo.timePillar.heavenly,
        zhi: baziData.baziInfo.timePillar.earthly
      },
      gender: baziData.userInfo.gender === '男' ? '男' : '女'
    };

    // 构建出生信息
    const birthInfo = {
      year: baziData.birthInfo.year,
      month: baziData.birthInfo.month,
      day: baziData.birthInfo.day,
      hour: baziData.birthInfo.hour,
      minute: baziData.birthInfo.minute || 0
    };

    // 构建地理位置信息（默认使用北京）
    const location = '北京';

    // 执行专业级大运计算
    const dayunResult = calculator.calculateProfessionalDayun(bazi, birthInfo, location);

    // 转换数据结构以适配前端显示
    const frontendData = transformDayunDataForFrontend(dayunResult);

    return {
      success: true,
      data: frontendData,
      basis: "《渊海子平》《协纪辨方书》专业算法",
      features: [
        "精确节气计算（分钟级精度）",
        "真太阳时修正",
        "专业起运时间算法",
        "完整大运序列分析"
      ],
      note: "基于权威天文数据的专业级大运计算系统"
    };

  } catch (error) {
    console.error('❌ 专业级大运计算失败:', error);
    return {
      success: false,
      error: error.message,
      reason: '专业级大运计算系统异常'
    };
  }
}

/**
 * 验证前端数据结构
 */
function validateFrontendDataStructure(data) {
  console.log('🔍 验证前端数据结构...');
  
  const tests = [
    {
      name: '基础结构验证',
      test: () => data && typeof data === 'object'
    },
    {
      name: '成功状态验证',
      test: () => data.hasOwnProperty('success')
    },
    {
      name: '数据内容验证',
      test: () => data.success ? data.hasOwnProperty('data') : data.hasOwnProperty('error')
    },
    {
      name: '特性列表验证',
      test: () => data.success ? Array.isArray(data.features) : true
    },
    {
      name: '起运信息验证',
      test: () => data.success ? data.data.hasOwnProperty('qiyunInfo') : true
    },
    {
      name: '大运序列验证',
      test: () => data.success ? Array.isArray(data.data.dayunSequence) : true
    },
    {
      name: '方向信息验证',
      test: () => data.success ? data.data.hasOwnProperty('direction') : true
    }
  ];

  let passedTests = 0;
  tests.forEach(test => {
    const result = test.test();
    console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? '通过' : '失败'}`);
    if (result) passedTests++;
  });

  const successRate = (passedTests / tests.length * 100).toFixed(1);
  console.log(`\n📊 前端数据结构验证: ${passedTests}/${tests.length} (${successRate}%)`);
  
  return successRate >= 85;
}

/**
 * 验证WXML绑定数据
 */
function validateWXMLBindings(data) {
  console.log('\n🎨 验证WXML数据绑定...');
  
  if (!data.success) {
    console.log('⚠️ 计算失败，跳过WXML绑定验证');
    return false;
  }

  const bindings = [
    {
      path: 'professionalDayunData.data.qiyunInfo.ageText',
      value: data.data.qiyunInfo?.ageText,
      required: true
    },
    {
      path: 'professionalDayunData.data.qiyunInfo.description',
      value: data.data.qiyunInfo?.description,
      required: true
    },
    {
      path: 'professionalDayunData.data.currentDayun',
      value: data.data.currentDayun,
      required: false
    },
    {
      path: 'professionalDayunData.data.dayunSequence',
      value: data.data.dayunSequence,
      required: true
    },
    {
      path: 'professionalDayunData.data.direction.description',
      value: data.data.direction?.description,
      required: true
    },
    {
      path: 'professionalDayunData.features',
      value: data.features,
      required: true
    }
  ];

  let validBindings = 0;
  bindings.forEach(binding => {
    const isValid = binding.value !== undefined && binding.value !== null;
    const status = isValid ? '✅' : (binding.required ? '❌' : '⚠️');
    console.log(`${status} ${binding.path}: ${isValid ? '有效' : '无效'}`);
    if (isValid) validBindings++;
  });

  const bindingRate = (validBindings / bindings.length * 100).toFixed(1);
  console.log(`\n📊 WXML绑定验证: ${validBindings}/${bindings.length} (${bindingRate}%)`);
  
  return bindingRate >= 80;
}

/**
 * 执行完整的前端集成测试
 */
function runFrontendIntegrationTest() {
  console.log('🚀 执行完整的前端集成测试...\n');
  
  // 1. 创建模拟数据
  const mockData = createMockBaziData();
  console.log('✅ 模拟数据创建完成');
  
  // 2. 执行计算
  const result = calculateProfessionalDayun(mockData);
  console.log(`${result.success ? '✅' : '❌'} 专业级大运计算: ${result.success ? '成功' : '失败'}`);
  
  if (!result.success) {
    console.log(`❌ 计算失败原因: ${result.reason}`);
    console.log(`❌ 错误详情: ${result.error}`);
    return false;
  }
  
  // 3. 验证数据结构
  const structureValid = validateFrontendDataStructure(result);
  
  // 4. 验证WXML绑定
  const bindingValid = validateWXMLBindings(result);
  
  // 5. 生成测试报告
  console.log('\n📋 前端集成测试报告');
  console.log('============================================================');
  console.log(`✅ 计算执行: ${result.success ? '成功' : '失败'}`);
  console.log(`${structureValid ? '✅' : '❌'} 数据结构: ${structureValid ? '通过' : '失败'}`);
  console.log(`${bindingValid ? '✅' : '❌'} WXML绑定: ${bindingValid ? '通过' : '失败'}`);
  
  const overallSuccess = result.success && structureValid && bindingValid;
  console.log(`\n🎯 总体评估: ${overallSuccess ? '✅ 集成成功' : '❌ 需要修复'}`);
  console.log('============================================================\n');
  
  return overallSuccess;
}

// 执行测试
const testResult = runFrontendIntegrationTest();

if (testResult) {
  console.log('🎉 前端大运集成测试全部通过！');
  console.log('✅ 专业级大运计算器已成功集成到微信小程序前端');
} else {
  console.log('⚠️ 前端大运集成测试发现问题，需要进一步调试');
}
