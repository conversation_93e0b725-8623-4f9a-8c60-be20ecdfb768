/* components/wuxing-radar/index.wxss */
/* 五行雷达图组件样式 */

.wuxing-radar-container {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 雷达图头部 */
.radar-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.radar-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.radar-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 雷达图画布区域 */
.radar-canvas-wrapper {
  position: relative;
  width: 100%;
  height: 600rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
}

.radar-canvas {
  width: 600rpx;
  height: 600rpx;
}

/* 中心信息显示 */
.radar-center-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.center-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.center-score {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #2196F3;
}

/* 五行图例 */
.radar-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: center;
  margin-bottom: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 10rpx 16rpx;
  min-width: 140rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.legend-name {
  font-size: 24rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 500;
}

.legend-score {
  font-size: 22rpx;
  color: #666;
  margin-right: 8rpx;
}

.legend-strength {
  font-size: 20rpx;
  color: #999;
}

/* 详细数据展示 */
.radar-details {
  border-top: 1rpx solid #eee;
  padding-top: 30rpx;
}

.details-header {
  margin-bottom: 20rpx;
}

.details-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.details-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.detail-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.detail-info {
  flex: 1;
}

.detail-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.detail-score {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.detail-bar {
  width: 100%;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.detail-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.detail-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .radar-canvas-wrapper {
    height: 500rpx;
  }
  
  .radar-canvas {
    width: 500rpx;
    height: 500rpx;
  }
  
  .legend-item {
    min-width: 120rpx;
    padding: 8rpx 12rpx;
  }
  
  .legend-name {
    font-size: 22rpx;
  }
  
  .legend-score {
    font-size: 20rpx;
  }
}

/* 动画效果 */
.radar-canvas {
  transition: opacity 0.3s ease;
}

.detail-item {
  transition: background-color 0.2s ease;
}

.detail-item:active {
  background: #e9ecef;
}

/* 无障碍支持 */
.legend-item,
.detail-item {
  outline: none;
}

.legend-item:focus,
.detail-item:focus {
  box-shadow: 0 0 0 2rpx #2196F3;
}
