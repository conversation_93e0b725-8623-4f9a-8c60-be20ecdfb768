<!--components/trae-view/index.wxml-->
<view class="trae-view">
  <!-- 调试信息 - 仅在调试模式显示 -->
  <view class="debug-info" wx:if="{{isDebugMode}}" style="padding: 10px; background-color: #f0f0f0; margin-bottom: 10px; font-size: 12px;">
    <view>问题数量: {{questions.length || 0}}</view>
    <view>当前问题索引: {{currentQuestionIndex}}</view>
    <view>当前问题: {{questions[currentQuestionIndex] ? '存在' : '不存在'}}</view>
  </view>

  <view class="question-container">
    <!-- 当前问题 -->
    <view class="question" wx:if="{{questions && questions.length > 0 && questions[currentQuestionIndex]}}">
      <view class="question-number">问题 {{currentQuestionIndex + 1}}/{{questions.length}}</view>
      
      <!-- 如果有会话问题，使用会话问题内容，否则使用原始问题内容 -->
      <view class="question-text {{useConversationalFormat && conversationalQuestions[currentQuestionIndex] ? 'conversational-question' : ''}}">
        <block wx:if="{{useConversationalFormat && conversationalQuestions[currentQuestionIndex]}}">
          {{conversationalQuestions[currentQuestionIndex].text || conversationalQuestions[currentQuestionIndex].content}}
        </block>
        <block wx:else>
          {{questions[currentQuestionIndex].text || questions[currentQuestionIndex].content}}
        </block>
      </view>
      
      <!-- 速答标记 -->
      <view class="speed-answer-badge" wx:if="{{isSpeedAnswer}}">
        <image class="speed-icon" src="/assets/icons/time.png" mode="aspectFit"></image>
        <text>速答</text>
      </view>
      
      <!-- 选项列表 - 使用新的conversational-option组件 -->
      <view class="options-list">
        <!-- 处理字符串数组格式的选项 - 直接使用原始选项，因为我们已经更新了各年级文件中的选项为日常对话语言 -->
        <block wx:if="{{questions[currentQuestionIndex].options && questions[currentQuestionIndex].options.length > 0}}">
          <conversational-option
            wx:for="{{questions[currentQuestionIndex].options}}" 
            wx:key="*this"
            text="{{item}}"
            index="{{index}}"
            selected="{{selectedOption === index}}"
            gradeStyle="{{getGradeStyle(grade)}}"
            hasEmoji="{{hasEmoji(item)}}"
            bind:select="selectOption"
          />
        </block>
      </view>
    </view>
    
    <!-- 当问题不存在时显示错误信息 -->
    <view class="error-message" wx:else style="color: red; padding: 20px; text-align: center;">
      当前问题数据不存在，请检查量表数据是否正确加载。
      <view>年级: {{grade || '未设置'}}</view>
      <view>分类: {{gradeCategory || '未设置'}}</view>
    </view>
  </view>
</view>