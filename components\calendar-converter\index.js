/**
 * 万年历转换组件
 * 提供阳历农历互转功能
 */

// 🔧 导入权威农历转换器
const AuthoritativeLunarConverter = require('../../utils/authoritative_lunar_data.js');

Component({
  properties: {
    // 是否显示组件
    show: {
      type: Boolean,
      value: false
    },
    // 初始日期类型 'solar' | 'lunar'
    initialType: {
      type: String,
      value: 'solar'
    },
    // 初始年份
    initialYear: {
      type: Number,
      value: 2025
    },
    // 初始月份
    initialMonth: {
      type: Number,
      value: 1
    },
    // 初始日期
    initialDay: {
      type: Number,
      value: 1
    }
  },

  data: {
    // 当前选择的日期类型
    dateType: 'solar', // 'solar' | 'lunar'
    
    // 输入的日期
    inputDate: {
      year: 2025,
      month: 1,
      day: 1
    },
    
    // 转换结果
    convertResult: {
      solar: '',
      lunar: '',
      lunarDetail: '',
      success: false
    },
    
    // 年份范围
    years: [],
    yearIndex: 0,
    
    // 月份范围
    months: [],
    monthIndex: 0,
    
    // 日期范围
    days: [],
    dayIndex: 0,
    
    // 是否正在转换
    converting: false
  },

  lifetimes: {
    attached() {
      this.initializeData();
    }
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.initializeData();
      }
    }
  },

  methods: {
    // 初始化数据
    initializeData() {
      const currentYear = new Date().getFullYear();
      
      // 生成年份列表（1900-2039）
      const years = [];
      for (let year = 1900; year <= 2039; year++) {
        years.push(`${year}年`);
      }
      
      // 生成月份列表
      const months = [];
      for (let month = 1; month <= 12; month++) {
        months.push(`${month}月`);
      }
      
      // 生成日期列表
      const days = [];
      for (let day = 1; day <= 31; day++) {
        days.push(`${day}日`);
      }
      
      // 设置初始值
      const initialYear = this.properties.initialYear || currentYear;
      const initialMonth = this.properties.initialMonth || 1;
      const initialDay = this.properties.initialDay || 1;
      
      this.setData({
        dateType: this.properties.initialType,
        'inputDate.year': initialYear,
        'inputDate.month': initialMonth,
        'inputDate.day': initialDay,
        years: years,
        months: months,
        days: days,
        yearIndex: initialYear - 1900,
        monthIndex: initialMonth - 1,
        dayIndex: initialDay - 1
      });
      
      // 执行初始转换
      this.performConversion();
    },

    // 切换日期类型
    switchDateType(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        dateType: type
      });
      this.performConversion();
    },

    // 年份选择
    onYearChange(e) {
      const index = e.detail.value;
      const year = 1900 + index;
      
      this.setData({
        yearIndex: index,
        'inputDate.year': year
      });
      
      this.updateDaysInMonth();
      this.performConversion();
    },

    // 月份选择
    onMonthChange(e) {
      const index = e.detail.value;
      const month = index + 1;
      
      this.setData({
        monthIndex: index,
        'inputDate.month': month
      });
      
      this.updateDaysInMonth();
      this.performConversion();
    },

    // 日期选择
    onDayChange(e) {
      const index = e.detail.value;
      const day = index + 1;
      
      this.setData({
        dayIndex: index,
        'inputDate.day': day
      });
      
      this.performConversion();
    },

    // 更新月份天数
    updateDaysInMonth() {
      const { year, month } = this.data.inputDate;
      const daysInMonth = this.getDaysInMonth(year, month);
      
      const days = [];
      for (let day = 1; day <= daysInMonth; day++) {
        days.push(`${day}日`);
      }
      
      // 调整当前选择的日期
      let dayIndex = this.data.dayIndex;
      if (dayIndex >= daysInMonth) {
        dayIndex = daysInMonth - 1;
      }
      
      this.setData({
        days: days,
        dayIndex: dayIndex,
        'inputDate.day': dayIndex + 1
      });
    },

    // 获取月份天数
    getDaysInMonth(year, month) {
      const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
      
      // 闰年处理
      if (month === 2 && this.isLeapYear(year)) {
        return 29;
      }
      
      return daysInMonth[month - 1];
    },

    // 判断闰年
    isLeapYear(year) {
      return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    },

    // 执行转换
    performConversion() {
      if (this.data.converting) return;
      
      this.setData({ converting: true });
      
      const { dateType, inputDate } = this.data;
      
      try {
        if (dateType === 'solar') {
          // 阳历转农历
          this.solarToLunar(inputDate);
        } else {
          // 农历转阳历
          this.lunarToSolar(inputDate);
        }
      } catch (error) {
        console.error('日期转换失败:', error);
        this.setData({
          'convertResult.success': false,
          converting: false
        });
      }
    },

    // 阳历转农历
    solarToLunar(solarDate) {
      try {
        const date = new Date(solarDate.year, solarDate.month - 1, solarDate.day);
        const lunarResult = AuthoritativeLunarConverter.solarToLunar(date);
        
        this.setData({
          'convertResult.solar': `${solarDate.year}年${solarDate.month}月${solarDate.day}日`,
          'convertResult.lunar': lunarResult.formatted,
          'convertResult.lunarDetail': `${lunarResult.year}年${lunarResult.monthName}${lunarResult.dayName}`,
          'convertResult.success': true,
          converting: false
        });
      } catch (error) {
        console.error('阳历转农历失败:', error);
        this.setData({
          'convertResult.success': false,
          converting: false
        });
      }
    },

    // 农历转阳历
    lunarToSolar(lunarDate) {
      try {
        const solarResult = AuthoritativeLunarConverter.lunarToSolar(
          lunarDate.year, lunarDate.month, lunarDate.day, false
        );
        
        this.setData({
          'convertResult.solar': solarResult.formatted,
          'convertResult.lunar': `${lunarDate.year}年${lunarDate.month}月${lunarDate.day}日`,
          'convertResult.lunarDetail': `农历${lunarDate.year}年${lunarDate.month}月${lunarDate.day}日`,
          'convertResult.success': true,
          converting: false
        });
      } catch (error) {
        console.error('农历转阳历失败:', error);
        this.setData({
          'convertResult.success': false,
          converting: false
        });
      }
    },

    // 确认选择
    onConfirm() {
      const { dateType, inputDate, convertResult } = this.data;
      
      if (!convertResult.success) {
        wx.showToast({
          title: '转换失败，请检查日期',
          icon: 'none'
        });
        return;
      }
      
      // 触发确认事件
      this.triggerEvent('confirm', {
        dateType: dateType,
        inputDate: inputDate,
        convertResult: convertResult
      });
    },

    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 关闭
    onClose() {
      this.triggerEvent('close');
    }
  }
});
