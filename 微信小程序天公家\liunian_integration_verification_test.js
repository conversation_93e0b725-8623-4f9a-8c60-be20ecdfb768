/**
 * 专业级流年系统集成验证测试
 * 
 * 验证内容：
 * 1. 前端集成完整性验证
 * 2. 数据流转正确性验证
 * 3. 用户体验优化验证
 * 4. 系统稳定性验证
 * 5. 与大运小运系统协调性验证
 */

const ProfessionalLiunianCalculator = require('./utils/professional_liunian_calculator.js');

class LiunianIntegrationVerificationTest {
  constructor() {
    this.calculator = new ProfessionalLiunianCalculator();
    this.testResults = [];
    console.log('🔧 流年系统集成验证测试初始化完成');
  }

  /**
   * 执行完整的集成验证测试
   */
  async runCompleteIntegrationTest() {
    console.log('\n🎯 开始执行流年系统集成验证测试...');
    console.log('=' .repeat(60));

    const testSuite = [
      { name: '前端集成完整性验证', method: 'testFrontendIntegrationCompleteness' },
      { name: '数据流转正确性验证', method: 'testDataFlowCorrectness' },
      { name: '用户体验优化验证', method: 'testUserExperienceOptimization' },
      { name: '系统稳定性验证', method: 'testSystemStability' },
      { name: '与大运小运协调性验证', method: 'testCoordinationWithOtherSystems' },
      { name: '动态计算准确性验证', method: 'testDynamicCalculationAccuracy' },
      { name: '样式和交互验证', method: 'testStyleAndInteraction' }
    ];

    for (const test of testSuite) {
      try {
        console.log(`\n📊 执行测试: ${test.name}`);
        const result = await this[test.method]();
        this.testResults.push({
          name: test.name,
          status: 'PASSED',
          result: result,
          timestamp: new Date().toISOString()
        });
        console.log(`✅ ${test.name}: 通过`);
      } catch (error) {
        console.error(`❌ ${test.name}: 失败 - ${error.message}`);
        this.testResults.push({
          name: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    this.generateIntegrationReport();
  }

  /**
   * 测试1：前端集成完整性验证
   */
  testFrontendIntegrationCompleteness() {
    console.log('🔍 验证前端集成完整性...');

    const integrationChecklist = [
      { item: 'ProfessionalLiunianCalculator模块导入', check: () => !!ProfessionalLiunianCalculator },
      { item: '流年计算方法存在', check: () => typeof this.calculator.calculateCompleteLiunianAnalysis === 'function' },
      { item: '当前流年状态方法存在', check: () => typeof this.calculator.getCurrentLiunianStatus === 'function' },
      { item: '流年干支计算方法存在', check: () => typeof this.calculator.calculateLiunianGanzhi === 'function' },
      { item: '十神分析方法存在', check: () => typeof this.calculator.analyzeTenGodsRelation === 'function' },
      { item: '神煞检测方法存在', check: () => typeof this.calculator.detectShenshaActivation === 'function' },
      { item: '运势评估方法存在', check: () => typeof this.calculator.evaluateFortuneLevel === 'function' },
      { item: '建议生成方法存在', check: () => typeof this.calculator.generateAdvice === 'function' }
    ];

    const results = integrationChecklist.map(item => {
      const passed = item.check();
      return {
        item: item.item,
        passed: passed,
        status: passed ? '✅' : '❌'
      };
    });

    const passedCount = results.filter(r => r.passed).length;
    const completeness = (passedCount / results.length * 100).toFixed(1);

    console.log('📋 前端集成检查结果:');
    results.forEach(result => {
      console.log(`   ${result.status} ${result.item}`);
    });
    console.log(`📊 集成完整性: ${completeness}%`);

    return {
      checklist: results,
      passedCount: passedCount,
      totalCount: results.length,
      completeness: parseFloat(completeness),
      fullyIntegrated: passedCount === results.length
    };
  }

  /**
   * 测试2：数据流转正确性验证
   */
  testDataFlowCorrectness() {
    console.log('🔍 验证数据流转正确性...');

    const testBazi = this.getTestBazi();
    
    // 测试完整流年分析数据流
    const liunianResult = this.calculator.calculateCompleteLiunianAnalysis(testBazi, 2024, 5);
    
    const dataFlowChecks = [
      { 
        name: '基础数据结构', 
        check: () => Array.isArray(liunianResult) && liunianResult.length === 5 
      },
      { 
        name: '年份数据正确', 
        check: () => liunianResult.every((item, index) => item.year === 2024 + index) 
      },
      { 
        name: '干支数据完整', 
        check: () => liunianResult.every(item => item.gan && item.zhi && item.ganzhi) 
      },
      { 
        name: '十神分析存在', 
        check: () => liunianResult.every(item => item.tenGod && item.tenGodAnalysis) 
      },
      { 
        name: '运势等级存在', 
        check: () => liunianResult.every(item => item.fortuneLevel && item.fortuneLevel.level) 
      },
      { 
        name: '建议内容存在', 
        check: () => liunianResult.every(item => Array.isArray(item.advice) && item.advice.length > 0) 
      },
      { 
        name: '当前年份标记', 
        check: () => liunianResult.some(item => item.isCurrent) 
      },
      { 
        name: '计算依据存在', 
        check: () => liunianResult.every(item => item.basis && item.calculation) 
      }
    ];

    const flowResults = dataFlowChecks.map(check => {
      const passed = check.check();
      return {
        name: check.name,
        passed: passed,
        status: passed ? '✅' : '❌'
      };
    });

    const flowPassedCount = flowResults.filter(r => r.passed).length;
    const flowAccuracy = (flowPassedCount / flowResults.length * 100).toFixed(1);

    console.log('🔄 数据流转检查结果:');
    flowResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 数据流转准确性: ${flowAccuracy}%`);

    return {
      dataFlowChecks: flowResults,
      passedCount: flowPassedCount,
      totalCount: flowResults.length,
      accuracy: parseFloat(flowAccuracy),
      dataFlowCorrect: flowPassedCount === flowResults.length,
      sampleData: liunianResult[0] // 提供样本数据用于检查
    };
  }

  /**
   * 测试3：用户体验优化验证
   */
  testUserExperienceOptimization() {
    console.log('🔍 验证用户体验优化...');

    const uxChecks = [
      {
        name: '计算响应时间',
        test: () => {
          const startTime = Date.now();
          this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 5);
          const responseTime = Date.now() - startTime;
          return { responseTime: responseTime, optimized: responseTime < 100 };
        }
      },
      {
        name: '数据可读性',
        test: () => {
          const result = this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 3);
          const readable = result.every(item => 
            item.tenGodAnalysis.description && 
            item.fortuneLevel.description &&
            item.advice.every(advice => advice.length > 0)
          );
          return { readable: readable, optimized: readable };
        }
      },
      {
        name: '错误处理友好性',
        test: () => {
          try {
            this.calculator.calculateCompleteLiunianAnalysis(null, 2024, 5);
            return { handled: false, optimized: false };
          } catch (error) {
            const friendly = !error.message.includes('undefined') && error.message.length > 10;
            return { handled: true, friendly: friendly, optimized: friendly };
          }
        }
      },
      {
        name: '数据量适中性',
        test: () => {
          const result = this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 5);
          const dataSize = JSON.stringify(result).length;
          const appropriate = dataSize > 1000 && dataSize < 50000; // 1KB-50KB范围
          return { dataSize: dataSize, appropriate: appropriate, optimized: appropriate };
        }
      }
    ];

    const uxResults = uxChecks.map(check => {
      const result = check.test();
      return {
        name: check.name,
        result: result,
        optimized: result.optimized,
        status: result.optimized ? '✅' : '⚠️'
      };
    });

    const uxOptimizedCount = uxResults.filter(r => r.optimized).length;
    const uxScore = (uxOptimizedCount / uxResults.length * 100).toFixed(1);

    console.log('🎨 用户体验检查结果:');
    uxResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 用户体验评分: ${uxScore}%`);

    return {
      uxChecks: uxResults,
      optimizedCount: uxOptimizedCount,
      totalCount: uxResults.length,
      uxScore: parseFloat(uxScore),
      uxOptimized: uxOptimizedCount >= uxResults.length * 0.8 // 80%以上认为优化良好
    };
  }

  /**
   * 测试4：系统稳定性验证
   */
  testSystemStability() {
    console.log('🔍 验证系统稳定性...');

    const stabilityTests = [
      {
        name: '重复计算一致性',
        test: () => {
          const bazi = this.getTestBazi();
          const result1 = this.calculator.calculateCompleteLiunianAnalysis(bazi, 2024, 3);
          const result2 = this.calculator.calculateCompleteLiunianAnalysis(bazi, 2024, 3);
          
          const consistent = result1.length === result2.length &&
            result1.every((item, index) => 
              item.ganzhi === result2[index].ganzhi &&
              item.tenGod === result2[index].tenGod &&
              item.fortuneLevel.score === result2[index].fortuneLevel.score
            );
          
          return { consistent: consistent, stable: consistent };
        }
      },
      {
        name: '边界条件处理',
        test: () => {
          const tests = [
            () => this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 1900, 1),
            () => this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2100, 1),
            () => this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 0),
            () => this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 1)
          ];
          
          let handledCount = 0;
          tests.forEach(test => {
            try {
              const result = test();
              if (result && Array.isArray(result)) handledCount++;
            } catch (error) {
              // 预期的错误也算处理了
              handledCount++;
            }
          });
          
          const stable = handledCount === tests.length;
          return { handledCount: handledCount, totalTests: tests.length, stable: stable };
        }
      },
      {
        name: '内存泄漏检测',
        test: () => {
          const initialMemory = this.getMemoryUsage();
          
          // 执行多次计算
          for (let i = 0; i < 20; i++) {
            this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 5);
          }
          
          const finalMemory = this.getMemoryUsage();
          const memoryIncrease = finalMemory - initialMemory;
          const stable = memoryIncrease < 10 * 1024 * 1024; // 小于10MB认为稳定
          
          return { memoryIncrease: memoryIncrease, stable: stable };
        }
      }
    ];

    const stabilityResults = stabilityTests.map(test => {
      const result = test.test();
      return {
        name: test.name,
        result: result,
        stable: result.stable,
        status: result.stable ? '✅' : '⚠️'
      };
    });

    const stableCount = stabilityResults.filter(r => r.stable).length;
    const stabilityScore = (stableCount / stabilityResults.length * 100).toFixed(1);

    console.log('🛡️ 系统稳定性检查结果:');
    stabilityResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 系统稳定性评分: ${stabilityScore}%`);

    return {
      stabilityTests: stabilityResults,
      stableCount: stableCount,
      totalCount: stabilityResults.length,
      stabilityScore: parseFloat(stabilityScore),
      systemStable: stableCount === stabilityResults.length
    };
  }

  /**
   * 测试5：与大运小运协调性验证
   */
  testCoordinationWithOtherSystems() {
    console.log('🔍 验证与大运小运系统协调性...');

    const coordinationTests = [
      {
        name: '大运流年交互分析',
        test: () => {
          const bazi = this.getTestBazi();
          const currentDayun = { gan: '甲', zhi: '子' };
          const liunianResult = this.calculator.calculateCompleteLiunianAnalysis(bazi, 2024, 3, currentDayun);
          
          const hasInteractions = liunianResult.some(item => 
            item.interactions && item.interactions.length > 0
          );
          
          return { hasInteractions: hasInteractions, coordinated: hasInteractions };
        }
      },
      {
        name: '数据格式兼容性',
        test: () => {
          const liunianResult = this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 3);
          
          // 检查数据格式是否与前端期望一致
          const compatible = liunianResult.every(item => 
            typeof item.year === 'number' &&
            typeof item.ganzhi === 'string' &&
            typeof item.tenGod === 'string' &&
            typeof item.fortuneLevel === 'object' &&
            Array.isArray(item.advice)
          );
          
          return { compatible: compatible, coordinated: compatible };
        }
      },
      {
        name: '时间范围协调',
        test: () => {
          const currentYear = new Date().getFullYear();
          const liunianResult = this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), currentYear, 5);
          
          const timeCoordinated = liunianResult.every(item => 
            item.year >= currentYear && item.year <= currentYear + 10
          );
          
          return { timeCoordinated: timeCoordinated, coordinated: timeCoordinated };
        }
      }
    ];

    const coordinationResults = coordinationTests.map(test => {
      const result = test.test();
      return {
        name: test.name,
        result: result,
        coordinated: result.coordinated,
        status: result.coordinated ? '✅' : '⚠️'
      };
    });

    const coordinatedCount = coordinationResults.filter(r => r.coordinated).length;
    const coordinationScore = (coordinatedCount / coordinationResults.length * 100).toFixed(1);

    console.log('🤝 系统协调性检查结果:');
    coordinationResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 系统协调性评分: ${coordinationScore}%`);

    return {
      coordinationTests: coordinationResults,
      coordinatedCount: coordinatedCount,
      totalCount: coordinationResults.length,
      coordinationScore: parseFloat(coordinationScore),
      wellCoordinated: coordinatedCount === coordinationResults.length
    };
  }

  /**
   * 测试6：动态计算准确性验证
   */
  testDynamicCalculationAccuracy() {
    console.log('🔍 验证动态计算准确性...');

    const accuracyTests = [
      {
        name: '干支计算准确性',
        test: () => {
          // 验证已知年份的干支
          const knownYears = [
            { year: 2024, expected: '甲辰' },
            { year: 2025, expected: '乙巳' },
            { year: 2026, expected: '丙午' }
          ];
          
          const accurate = knownYears.every(test => {
            const result = this.calculator.calculateLiunianGanzhi(test.year);
            return result.ganzhi === test.expected;
          });
          
          return { accurate: accurate };
        }
      },
      {
        name: '十神分析准确性',
        test: () => {
          // 测试已知的十神关系
          const tenGodTests = [
            { dayMaster: '甲', liunianGan: '甲', expected: '比肩' },
            { dayMaster: '甲', liunianGan: '乙', expected: '劫财' },
            { dayMaster: '甲', liunianGan: '丙', expected: '食神' }
          ];
          
          const accurate = tenGodTests.every(test => {
            const result = this.calculator.analyzeTenGodsRelation(test.dayMaster, test.liunianGan);
            return result.tenGod === test.expected;
          });
          
          return { accurate: accurate };
        }
      }
    ];

    const accuracyResults = accuracyTests.map(test => {
      const result = test.test();
      return {
        name: test.name,
        result: result,
        accurate: result.accurate,
        status: result.accurate ? '✅' : '❌'
      };
    });

    const accurateCount = accuracyResults.filter(r => r.accurate).length;
    const accuracyScore = (accurateCount / accuracyResults.length * 100).toFixed(1);

    console.log('🎯 动态计算准确性检查结果:');
    accuracyResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 计算准确性评分: ${accuracyScore}%`);

    return {
      accuracyTests: accuracyResults,
      accurateCount: accurateCount,
      totalCount: accuracyResults.length,
      accuracyScore: parseFloat(accuracyScore),
      calculationAccurate: accurateCount === accuracyResults.length
    };
  }

  /**
   * 测试7：样式和交互验证
   */
  testStyleAndInteraction() {
    console.log('🔍 验证样式和交互功能...');

    // 模拟前端数据转换
    const liunianResult = this.calculator.calculateCompleteLiunianAnalysis(this.getTestBazi(), 2024, 3);
    
    const styleTests = [
      {
        name: '数据转换完整性',
        test: () => {
          // 模拟前端转换逻辑
          const transformedData = liunianResult.map(item => ({
            year: `${item.year}年`,
            chars: [item.gan, item.zhi],
            title: `${item.tenGod}主导年`,
            score: `${item.fortuneLevel.score}分`,
            level: item.fortuneLevel.level
          }));
          
          const complete = transformedData.every(item => 
            item.year && item.chars.length === 2 && item.title && item.score && item.level
          );
          
          return { complete: complete };
        }
      },
      {
        name: '交互数据可用性',
        test: () => {
          const interactive = liunianResult.every(item => 
            item.advice && item.advice.length > 0 &&
            item.activatedShensha !== undefined &&
            item.interactions !== undefined
          );
          
          return { interactive: interactive };
        }
      }
    ];

    const styleResults = styleTests.map(test => {
      const result = test.test();
      const passed = Object.values(result).every(v => v === true);
      return {
        name: test.name,
        result: result,
        passed: passed,
        status: passed ? '✅' : '⚠️'
      };
    });

    const stylePassedCount = styleResults.filter(r => r.passed).length;
    const styleScore = (stylePassedCount / styleResults.length * 100).toFixed(1);

    console.log('🎨 样式交互检查结果:');
    styleResults.forEach(result => {
      console.log(`   ${result.status} ${result.name}`);
    });
    console.log(`📊 样式交互评分: ${styleScore}%`);

    return {
      styleTests: styleResults,
      passedCount: stylePassedCount,
      totalCount: styleResults.length,
      styleScore: parseFloat(styleScore),
      styleOptimized: stylePassedCount === styleResults.length
    };
  }

  /**
   * 辅助方法：获取测试八字数据
   */
  getTestBazi() {
    return {
      dayPillar: { gan: '癸', zhi: '卯' },
      yearPillar: { gan: '辛', zhi: '丑' },
      monthPillar: { gan: '甲', zhi: '午' },
      timePillar: { gan: '壬', zhi: '戌' },
      birthInfo: { year: 2021 }
    };
  }

  /**
   * 辅助方法：获取内存使用情况
   */
  getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0;
  }

  /**
   * 生成集成验证报告
   */
  generateIntegrationReport() {
    console.log('\n📋 流年系统集成验证报告');
    console.log('=' .repeat(60));

    const passedTests = this.testResults.filter(r => r.status === 'PASSED').length;
    const totalTests = this.testResults.length;
    const successRate = (passedTests / totalTests * 100).toFixed(1);

    console.log(`📊 总体集成结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);
    console.log('\n📈 详细验证结果:');

    this.testResults.forEach((result, index) => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}`);
      
      if (result.status === 'PASSED' && result.result) {
        // 显示关键指标
        if (result.result.completeness !== undefined) {
          console.log(`   完整性: ${result.result.completeness}%`);
        }
        if (result.result.accuracy !== undefined) {
          console.log(`   准确性: ${result.result.accuracy}%`);
        }
        if (result.result.uxScore !== undefined) {
          console.log(`   用户体验: ${result.result.uxScore}%`);
        }
      }
    });

    console.log('\n🎯 集成状态总结:');
    const integrationStatus = this.assessIntegrationStatus();
    console.log(`   系统集成度: ${integrationStatus.integrationLevel}`);
    console.log(`   推荐状态: ${integrationStatus.recommendation}`);

    console.log('\n✅ 流年系统集成验证完成');
    return {
      totalTests: totalTests,
      passedTests: passedTests,
      successRate: parseFloat(successRate),
      integrationStatus: integrationStatus,
      testResults: this.testResults
    };
  }

  /**
   * 评估集成状态
   */
  assessIntegrationStatus() {
    const passedTests = this.testResults.filter(r => r.status === 'PASSED').length;
    const totalTests = this.testResults.length;
    const successRate = passedTests / totalTests;

    if (successRate >= 0.95) {
      return {
        integrationLevel: '优秀 (95%+)',
        recommendation: '系统已完全集成，可以投入生产使用'
      };
    } else if (successRate >= 0.85) {
      return {
        integrationLevel: '良好 (85%+)',
        recommendation: '系统基本集成完成，建议优化少数问题后投入使用'
      };
    } else if (successRate >= 0.70) {
      return {
        integrationLevel: '一般 (70%+)',
        recommendation: '系统部分集成完成，需要解决主要问题'
      };
    } else {
      return {
        integrationLevel: '需改进 (<70%)',
        recommendation: '系统集成存在重大问题，需要全面检查和修复'
      };
    }
  }
}

// 执行测试
if (typeof module !== 'undefined' && require.main === module) {
  const test = new LiunianIntegrationVerificationTest();
  test.runCompleteIntegrationTest().catch(console.error);
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LiunianIntegrationVerificationTest;
}

console.log('📦 流年系统集成验证测试模块加载完成');
