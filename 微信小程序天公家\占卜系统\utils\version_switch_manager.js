// utils/version_switch_manager.js
// 版本切换管理器 - 管理不同版本间的切换

class VersionSwitchManager {
  constructor() {
    this.versions = new Map();
    this.currentVersion = null;
    this.switchHistory = [];
    this.initialized = false;
  }

  // 初始化管理器
  init(config = {}) {
    try {
      this.currentVersion = config.defaultVersion || 'standard';
      this.initialized = true;
      
      // 注册默认版本
      this.registerVersion('standard', {
        name: '标准版本',
        description: '标准的八字分析版本',
        features: ['basic_analysis', 'wuxing_analysis'],
        priority: 0
      });
      
      this.registerVersion('professional', {
        name: '专业版本',
        description: '专业的八字分析版本',
        features: ['basic_analysis', 'wuxing_analysis', 'professional_analysis', 'digital_analysis'],
        priority: 10
      });
      
      this.registerVersion('comprehensive', {
        name: '综合版本',
        description: '综合的八字分析版本',
        features: ['basic_analysis', 'wuxing_analysis', 'professional_analysis', 'classical_analysis'],
        priority: 20
      });
      
      console.log('✅ VersionSwitchManager 初始化成功');
      return true;
    } catch (error) {
      console.error('❌ VersionSwitchManager 初始化失败:', error);
      return false;
    }
  }

  // 注册版本
  registerVersion(versionId, config) {
    if (!this.initialized) {
      console.warn('⚠️ VersionSwitchManager 未初始化');
      return false;
    }

    try {
      this.versions.set(versionId, {
        id: versionId,
        name: config.name || versionId,
        description: config.description || '',
        features: config.features || [],
        priority: config.priority || 0,
        enabled: config.enabled !== false,
        metadata: config.metadata || {},
        registeredAt: Date.now()
      });
      
      console.log(`✅ 版本 ${versionId} 注册成功`);
      return true;
    } catch (error) {
      console.error(`❌ 版本 ${versionId} 注册失败:`, error);
      return false;
    }
  }

  // 切换版本
  switchTo(versionId, options = {}) {
    const version = this.versions.get(versionId);
    if (!version) {
      console.warn(`⚠️ 未找到版本: ${versionId}`);
      return false;
    }

    if (!version.enabled) {
      console.warn(`⚠️ 版本 ${versionId} 已禁用`);
      return false;
    }

    try {
      const previousVersion = this.currentVersion;
      this.currentVersion = versionId;
      
      // 记录切换历史
      this.switchHistory.push({
        from: previousVersion,
        to: versionId,
        timestamp: Date.now(),
        options: options,
        reason: options.reason || 'manual'
      });
      
      // 限制历史记录长度
      if (this.switchHistory.length > 50) {
        this.switchHistory = this.switchHistory.slice(-50);
      }
      
      console.log(`✅ 版本切换成功: ${previousVersion} -> ${versionId}`);
      
      // 触发切换事件
      if (options.callback && typeof options.callback === 'function') {
        options.callback(versionId, previousVersion);
      }
      
      return true;
    } catch (error) {
      console.error(`❌ 版本切换失败:`, error);
      return false;
    }
  }

  // 获取当前版本
  getCurrentVersion() {
    return this.versions.get(this.currentVersion);
  }

  // 获取当前版本ID
  getCurrentVersionId() {
    return this.currentVersion;
  }

  // 获取所有版本
  getAllVersions() {
    return Array.from(this.versions.values())
      .filter(version => version.enabled)
      .sort((a, b) => b.priority - a.priority);
  }

  // 检查功能是否可用
  hasFeature(feature, versionId = null) {
    const version = versionId ? 
      this.versions.get(versionId) : 
      this.getCurrentVersion();
    
    if (!version) {
      return false;
    }
    
    return version.features.includes(feature);
  }

  // 获取版本功能列表
  getVersionFeatures(versionId = null) {
    const version = versionId ? 
      this.versions.get(versionId) : 
      this.getCurrentVersion();
    
    return version ? version.features : [];
  }

  // 启用/禁用版本
  setVersionEnabled(versionId, enabled) {
    const version = this.versions.get(versionId);
    if (!version) {
      console.warn(`⚠️ 未找到版本: ${versionId}`);
      return false;
    }

    version.enabled = enabled;
    
    // 如果禁用的是当前版本，切换到默认版本
    if (!enabled && this.currentVersion === versionId) {
      const enabledVersions = this.getAllVersions();
      if (enabledVersions.length > 0) {
        this.switchTo(enabledVersions[0].id, { reason: 'auto_fallback' });
      }
    }
    
    console.log(`✅ 版本 ${versionId} ${enabled ? '启用' : '禁用'}成功`);
    return true;
  }

  // 获取切换历史
  getSwitchHistory(limit = 10) {
    return this.switchHistory.slice(-limit).reverse();
  }

  // 回滚到上一个版本
  rollback() {
    if (this.switchHistory.length < 1) {
      console.warn('⚠️ 没有可回滚的版本');
      return false;
    }

    const lastSwitch = this.switchHistory[this.switchHistory.length - 1];
    if (lastSwitch.from) {
      return this.switchTo(lastSwitch.from, { reason: 'rollback' });
    }

    return false;
  }

  // 获取状态
  getStatus() {
    const versionStats = {};
    for (const [id, version] of this.versions) {
      versionStats[id] = {
        enabled: version.enabled,
        featureCount: version.features.length,
        priority: version.priority,
        isCurrent: id === this.currentVersion
      };
    }

    return {
      initialized: this.initialized,
      currentVersion: this.currentVersion,
      versionCount: this.versions.size,
      switchCount: this.switchHistory.length,
      versions: versionStats
    };
  }

  // 清理管理器
  cleanup() {
    this.versions.clear();
    this.switchHistory = [];
    this.currentVersion = null;
    this.initialized = false;
    console.log('✅ VersionSwitchManager 清理完成');
  }
}

// 创建单例实例
const versionSwitchManager = new VersionSwitchManager();

module.exports = {
  VersionSwitchManager,
  default: versionSwitchManager,
  manager: versionSwitchManager
};
