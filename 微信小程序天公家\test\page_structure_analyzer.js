/**
 * 页面结构分析器
 * 专门分析每个tab页面的标签匹配情况
 */

const fs = require('fs');
const path = require('path');

class PageStructureAnalyzer {
  constructor() {
    this.pages = [];
  }

  analyzeWXMLStructure(filePath) {
    console.log('🔍 分析 WXML 页面结构');
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // 找到所有页面的开始位置
    const pageStarts = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNum = i + 1;
      
      // 检查页面开始标签
      if (line.includes('wx:if') || line.includes('wx:elif')) {
        if (line.includes('currentTab')) {
          const tabMatch = line.match(/currentTab\s*===\s*['"]([^'"]+)['"]/);
          if (tabMatch) {
            pageStarts.push({
              tab: tabMatch[1],
              line: lineNum,
              content: line.trim()
            });
          }
        }
      }
    }
    
    console.log('\n📍 找到的页面:');
    pageStarts.forEach((page, index) => {
      console.log(`${index + 1}. ${page.tab} - 第${page.line}行`);
    });
    
    // 分析每个页面的标签匹配
    console.log('\n🔍 分析每个页面的标签匹配:');
    
    for (let i = 0; i < pageStarts.length; i++) {
      const currentPage = pageStarts[i];
      const nextPage = pageStarts[i + 1];
      
      const startLine = currentPage.line - 1; // 转换为0基索引
      const endLine = nextPage ? nextPage.line - 1 : lines.length;
      
      console.log(`\n📄 分析 ${currentPage.tab} 页面 (第${currentPage.line}行 - 第${endLine}行):`);
      
      let viewDepth = 0;
      let hasError = false;
      
      for (let j = startLine; j < endLine; j++) {
        const line = lines[j];
        const lineNum = j + 1;
        
        // 计算view标签
        const openViews = (line.match(/<view[^>]*>/g) || []).length;
        const closeViews = (line.match(/<\/view>/g) || []).length;
        
        viewDepth += openViews - closeViews;
        
        // 如果是页面的最后一行，检查是否正确闭合
        if (j === endLine - 1 || (nextPage && j === nextPage.line - 2)) {
          if (viewDepth !== 0) {
            console.log(`❌ ${currentPage.tab} 页面标签不匹配！深度: ${viewDepth}`);
            console.log(`   最后几行:`);
            for (let k = Math.max(j - 3, startLine); k <= Math.min(j + 1, endLine - 1); k++) {
              const marker = k === j ? ' >>> ' : '     ';
              console.log(`${marker}${k + 1}: ${lines[k]}`);
            }
            hasError = true;
          } else {
            console.log(`✅ ${currentPage.tab} 页面标签匹配正确`);
          }
          break;
        }
      }
    }
    
    // 检查scroll-view的位置
    console.log('\n🔍 检查 scroll-view 结构:');
    
    let scrollStart = -1;
    let scrollEnd = -1;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (line.includes('<scroll-view')) {
        scrollStart = i + 1;
      }
      if (line.includes('</scroll-view>')) {
        scrollEnd = i + 1;
      }
    }
    
    console.log(`scroll-view: 第${scrollStart}行 - 第${scrollEnd}行`);
    
    // 检查最后一个页面是否在scroll-view结束之前正确闭合
    if (pageStarts.length > 0) {
      const lastPage = pageStarts[pageStarts.length - 1];
      console.log(`最后一个页面 (${lastPage.tab}) 开始于第${lastPage.line}行`);
      
      if (scrollEnd && lastPage.line < scrollEnd) {
        console.log('✅ 最后一个页面在 scroll-view 内部');
      } else {
        console.log('❌ 最后一个页面可能超出了 scroll-view 范围');
      }
    }
  }
}

// 运行分析
if (require.main === module) {
  const analyzer = new PageStructureAnalyzer();
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  analyzer.analyzeWXMLStructure(wxmlPath);
}

module.exports = PageStructureAnalyzer;
