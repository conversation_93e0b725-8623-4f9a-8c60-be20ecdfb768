/**
 * 应期分析数据流测试
 * 验证前端页面数据绑定是否正确
 */

const fs = require('fs');
const path = require('path');

function testTimingAnalysisDataFlow() {
  console.log('🔍 应期分析数据流测试\n');
  
  try {
    // 读取 WXML 文件
    const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
    const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');
    
    // 读取 JS 文件
    const jsPath = path.join(__dirname, '../pages/bazi-result/index.js');
    const jsContent = fs.readFileSync(jsPath, 'utf8');
    
    console.log('📋 数据结构一致性检查:\n');
    
    // 1. 检查病药平衡数据结构
    console.log('🔧 病药平衡法则:');
    const hasDiseaseMedicineJS = jsContent.includes('disease_analysis:') && 
                                jsContent.includes('medicine_recommendation:') &&
                                jsContent.includes('balance_score:') &&
                                jsContent.includes('balance_status:');
    
    const hasDiseaseMedicineWXML = wxmlContent.includes('disease_medicine.disease_analysis') &&
                                  wxmlContent.includes('disease_medicine.medicine_recommendation') &&
                                  wxmlContent.includes('disease_medicine.balance_score') &&
                                  wxmlContent.includes('disease_medicine.balance_status');
    
    console.log(`   ${hasDiseaseMedicineJS ? '✅' : '❌'} JS数据结构: disease_analysis, medicine_recommendation, balance_score, balance_status`);
    console.log(`   ${hasDiseaseMedicineWXML ? '✅' : '❌'} WXML数据绑定: 对应字段正确绑定`);
    
    // 2. 检查能量阈值数据结构
    console.log('\n⚡ 能量阈值模型:');
    const hasEnergyThresholdsJS = jsContent.includes('marriage_current_energy') &&
                                 jsContent.includes('marriage_required_threshold') &&
                                 jsContent.includes('marriage_met') &&
                                 jsContent.includes('marriage_estimated_year');
    
    const hasEnergyThresholdsWXML = wxmlContent.includes('energy_thresholds.marriage_current_energy') &&
                                   wxmlContent.includes('energy_thresholds.marriage_required_threshold') &&
                                   wxmlContent.includes('energy_thresholds.marriage_met') &&
                                   wxmlContent.includes('energy_thresholds.marriage_estimated_year');
    
    console.log(`   ${hasEnergyThresholdsJS ? '✅' : '❌'} JS数据结构: current_energy, required_threshold, met, estimated_year`);
    console.log(`   ${hasEnergyThresholdsWXML ? '✅' : '❌'} WXML数据绑定: 对应字段正确绑定`);
    
    // 3. 检查三重引动数据结构
    console.log('\n🔮 三重引动机制:');
    const hasTripleActivationJS = jsContent.includes('star_activation') &&
                                 jsContent.includes('palace_activation') &&
                                 jsContent.includes('shensha_activation') &&
                                 jsContent.includes('marriage_confidence');
    
    const hasTripleActivationWXML = wxmlContent.includes('triple_activation.star_activation') &&
                                   wxmlContent.includes('triple_activation.palace_activation') &&
                                   wxmlContent.includes('triple_activation.shensha_activation') &&
                                   wxmlContent.includes('triple_activation.marriage_confidence');
    
    console.log(`   ${hasTripleActivationJS ? '✅' : '❌'} JS数据结构: star_activation, palace_activation, shensha_activation, confidence`);
    console.log(`   ${hasTripleActivationWXML ? '✅' : '❌'} WXML数据绑定: 对应字段正确绑定`);
    
    // 4. 检查动态分析数据结构
    console.log('\n🌊 动态分析引擎:');
    const hasDynamicAnalysisJS = jsContent.includes('three_point_rule') &&
                                jsContent.includes('spacetime_force') &&
                                jsContent.includes('turning_points');
    
    const hasDynamicAnalysisWXML = wxmlContent.includes('dynamic_analysis.three_point_rule') &&
                                  wxmlContent.includes('dynamic_analysis.spacetime_force') &&
                                  wxmlContent.includes('dynamic_analysis.turning_points');
    
    console.log(`   ${hasDynamicAnalysisJS ? '✅' : '❌'} JS数据结构: three_point_rule, spacetime_force, turning_points`);
    console.log(`   ${hasDynamicAnalysisWXML ? '✅' : '❌'} WXML数据绑定: 对应字段正确绑定`);
    
    // 5. 检查数据设置方法
    console.log('\n📊 数据设置检查:');
    const hasSetDataCall = jsContent.includes('professionalTimingAnalysis: professionalTimingAnalysis');
    const hasLoadTimingCall = jsContent.includes('loadTimingAnalysis: function()');
    const hasCalculateMethod = jsContent.includes('calculateProfessionalTimingAnalysis: function');
    
    console.log(`   ${hasSetDataCall ? '✅' : '❌'} setData调用: professionalTimingAnalysis正确设置`);
    console.log(`   ${hasLoadTimingCall ? '✅' : '❌'} 加载方法: loadTimingAnalysis存在`);
    console.log(`   ${hasCalculateMethod ? '✅' : '❌'} 计算方法: calculateProfessionalTimingAnalysis存在`);
    
    // 6. 检查条件渲染
    console.log('\n🎯 条件渲染检查:');
    const hasConditionalRendering = wxmlContent.includes('wx:if=') &&
                                   wxmlContent.includes('wx:else') &&
                                   wxmlContent.includes('calculating');
    
    const hasLoadingStates = wxmlContent.includes('正在计算') ||
                            wxmlContent.includes('计算中') ||
                            wxmlContent.includes('正在分析');
    
    console.log(`   ${hasConditionalRendering ? '✅' : '❌'} 条件渲染: wx:if/wx:else正确使用`);
    console.log(`   ${hasLoadingStates ? '✅' : '❌'} 加载状态: 包含加载提示文本`);
    
    // 计算总体完成度
    const checks = [
      hasDiseaseMedicineJS && hasDiseaseMedicineWXML,
      hasEnergyThresholdsJS && hasEnergyThresholdsWXML,
      hasTripleActivationJS && hasTripleActivationWXML,
      hasDynamicAnalysisJS && hasDynamicAnalysisWXML,
      hasSetDataCall && hasLoadTimingCall && hasCalculateMethod,
      hasConditionalRendering && hasLoadingStates
    ];
    
    const passedChecks = checks.filter(check => check).length;
    const completionRate = (passedChecks / checks.length * 100).toFixed(1);
    
    console.log(`\n📊 测试总结:`);
    console.log(`   🎯 通过检查: ${passedChecks}/${checks.length}`);
    console.log(`   📈 数据流完整性: ${completionRate}%`);
    
    if (completionRate >= 90) {
      console.log(`   ✅ 数据流配置完成！前端应该能正确显示数据`);
    } else if (completionRate >= 70) {
      console.log(`   ⚠️ 数据流基本完成，但仍有改进空间`);
    } else {
      console.log(`   ❌ 数据流配置不完整，需要进一步修复`);
    }
    
    // 检查可能的问题
    console.log(`\n🔧 潜在问题检查:`);
    
    // 检查是否有旧的数据绑定
    const hasOldBinding = wxmlContent.includes('disease_medicine.disease') ||
                         wxmlContent.includes('disease_medicine.medicine') ||
                         wxmlContent.includes('disease_medicine.balance_effect');
    
    if (hasOldBinding) {
      console.log(`   ⚠️ 发现旧的数据绑定，可能导致显示问题`);
    } else {
      console.log(`   ✅ 没有发现旧的数据绑定`);
    }
    
    // 检查数据初始化
    const hasDataInit = jsContent.includes('professionalTimingAnalysis:') &&
                       jsContent.includes('data:');
    
    console.log(`   ${hasDataInit ? '✅' : '⚠️'} 数据初始化: ${hasDataInit ? '正确设置' : '可能需要初始化'}`);
    
    console.log(`\n🎯 修复建议:`);
    if (completionRate < 100) {
      console.log(`   1. 检查所有数据字段是否正确映射`);
      console.log(`   2. 确保 setData 调用包含所有必需字段`);
      console.log(`   3. 验证条件渲染逻辑是否完整`);
      console.log(`   4. 测试实际运行时的数据传递`);
    } else {
      console.log(`   🎉 数据流配置完美！应该能正常显示数据`);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 运行测试
testTimingAnalysisDataFlow();
