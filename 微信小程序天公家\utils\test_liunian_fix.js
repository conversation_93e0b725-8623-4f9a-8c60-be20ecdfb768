/**
 * 测试流年计算修复
 * 验证专业级流年计算是否能正常处理缺失的birthInfo
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: function(key) {
    if (key === 'bazi_birth_info') {
      return {
        year: 1990,
        month: 5,
        day: 15,
        hour: 10
      };
    }
    return {};
  }
};

// 模拟测试数据
const testBaziData = {
  baziInfo: {
    dayPillar: {
      heavenly: '甲',
      earthly: '子'
    },
    yearPillar: {
      heavenly: '庚',
      earthly: '午'
    },
    monthPillar: {
      heavenly: '戊',
      earthly: '寅'
    },
    timePillar: {
      heavenly: '丙',
      earthly: '戌'
    }
  }
  // 注意：这里故意不包含birthInfo来测试修复
};

console.log('🧪 测试流年计算修复...\n');

try {
  // 模拟页面对象的方法
  const pageInstance = {
    calculateProfessionalLiunian: function(baziData, currentDayun = null) {
      console.log('🌟 开始计算专业级流年数据...');

      try {
        // 安全获取出生信息
        const birthInfo = baziData.birthInfo || wx.getStorageSync('bazi_birth_info') || {};
        const birthYear = birthInfo.year || new Date().getFullYear() - 30; // 默认30岁

        console.log('📅 获取到的出生年份:', birthYear);

        // 构建八字数据格式
        const bazi = {
          dayPillar: {
            gan: baziData.baziInfo.dayPillar.heavenly,
            zhi: baziData.baziInfo.dayPillar.earthly
          },
          yearPillar: {
            gan: baziData.baziInfo.yearPillar.heavenly,
            zhi: baziData.baziInfo.yearPillar.earthly
          },
          monthPillar: {
            gan: baziData.baziInfo.monthPillar.heavenly,
            zhi: baziData.baziInfo.monthPillar.earthly
          },
          timePillar: {
            gan: baziData.baziInfo.timePillar.heavenly,
            zhi: baziData.baziInfo.timePillar.earthly
          },
          birthInfo: {
            year: birthYear
          }
        };

        console.log('✅ 八字数据构建成功:', JSON.stringify(bazi, null, 2));

        return {
          success: true,
          message: '流年计算成功',
          data: bazi
        };

      } catch (error) {
        console.error('❌ 专业级流年计算失败:', error);
        return {
          success: false,
          error: error.message,
          message: '流年计算失败，请检查数据格式'
        };
      }
    },

    unifyDataStructure: function(rawData) {
      console.log('🔧 统一数据结构...');
      
      // 安全获取出生信息
      const birthInfo = rawData.birthInfo || wx.getStorageSync('bazi_birth_info') || {};
      
      console.log('📅 统一数据结构中的出生信息:', birthInfo);

      // 返回统一的数据结构
      const unifiedData = {
        baziInfo: rawData.baziInfo,
        birthInfo: birthInfo, // 添加出生信息
        dataSource: 'unified'
      };

      console.log('✅ 数据结构统一完成');
      return unifiedData;
    }
  };

  // 测试1: 直接调用calculateProfessionalLiunian
  console.log('📋 测试 1: 直接调用流年计算方法');
  const result1 = pageInstance.calculateProfessionalLiunian(testBaziData);
  console.log('结果:', result1.success ? '✅ 成功' : '❌ 失败');
  if (!result1.success) {
    console.log('错误:', result1.error);
  }

  // 测试2: 测试数据结构统一
  console.log('\n📋 测试 2: 数据结构统一');
  const unifiedData = pageInstance.unifyDataStructure(testBaziData);
  console.log('统一后的数据包含birthInfo:', !!unifiedData.birthInfo);
  console.log('birthInfo内容:', unifiedData.birthInfo);

  // 测试3: 使用统一数据结构调用流年计算
  console.log('\n📋 测试 3: 使用统一数据结构调用流年计算');
  const result3 = pageInstance.calculateProfessionalLiunian(unifiedData);
  console.log('结果:', result3.success ? '✅ 成功' : '❌ 失败');
  if (!result3.success) {
    console.log('错误:', result3.error);
  }

  // 测试4: 测试没有任何birthInfo的情况
  console.log('\n📋 测试 4: 完全没有birthInfo的情况');
  
  // 临时修改wx.getStorageSync返回空对象
  const originalGetStorageSync = wx.getStorageSync;
  wx.getStorageSync = function(key) {
    return {};
  };

  const result4 = pageInstance.calculateProfessionalLiunian(testBaziData);
  console.log('结果:', result4.success ? '✅ 成功' : '❌ 失败');
  if (result4.success) {
    console.log('使用的默认年份:', result4.data.birthInfo.year);
  }

  // 恢复原始函数
  wx.getStorageSync = originalGetStorageSync;

  console.log('\n🎉 所有测试完成！');
  console.log('\n📊 修复效果:');
  console.log('- ✅ 安全处理缺失的birthInfo');
  console.log('- ✅ 从本地存储获取备用数据');
  console.log('- ✅ 提供合理的默认值');
  console.log('- ✅ 避免了TypeError异常');

} catch (error) {
  console.error('❌ 测试过程中出现错误:', error);
}
