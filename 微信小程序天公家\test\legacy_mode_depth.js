/**
 * 传统模式深度计算器
 * 专门计算传统模式的view标签深度
 */

const fs = require('fs');
const path = require('path');

function calculateLegacyModeDepth() {
  console.log('🔍 计算传统模式的 view 标签深度');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到传统模式的开始和结束
  let legacyStart = -1;
  let legacyEnd = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('wx:else') && line.includes('legacy-timing-container')) {
      legacyStart = i;
      console.log(`📍 传统模式开始: 第${i + 1}行`);
    }
    if (legacyStart > 0 && line.includes('wx:elif') && line.includes('liuqin')) {
      legacyEnd = i;
      console.log(`📍 传统模式结束: 第${i}行 (liuqin页面开始)`);
      break;
    }
  }
  
  if (legacyStart === -1 || legacyEnd === -1) {
    console.log('❌ 无法找到传统模式的范围');
    return;
  }
  
  console.log(`\n🔍 分析第${legacyStart + 1}行到第${legacyEnd}行的view标签:`);
  
  let depth = 0;
  const depthHistory = [];
  
  for (let i = legacyStart; i < legacyEnd; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 计算这一行的view标签变化
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    
    const prevDepth = depth;
    depth += openViews - closeViews;
    
    // 记录所有的深度变化
    if (openViews > 0 || closeViews > 0) {
      depthHistory.push({
        line: lineNum,
        content: line.trim(),
        openViews,
        closeViews,
        prevDepth,
        newDepth: depth,
        change: depth - prevDepth
      });
    }
  }
  
  console.log(`\n📊 传统模式统计结果:`);
  console.log(`最终深度: ${depth}`);
  
  console.log(`\n📄 所有深度变化:`);
  depthHistory.forEach(item => {
    const changeStr = item.change > 0 ? `+${item.change}` : `${item.change}`;
    console.log(`第${item.line}行: 深度 ${item.prevDepth} → ${item.newDepth} (${changeStr}) | 开始:${item.openViews} 结束:${item.closeViews}`);
    if (item.content.length > 80) {
      console.log(`    ${item.content.substring(0, 80)}...`);
    } else {
      console.log(`    ${item.content}`);
    }
  });
  
  // 分析结果
  if (depth === 0) {
    console.log(`\n✅ 传统模式的view标签匹配正确`);
  } else if (depth > 0) {
    console.log(`\n❌ 传统模式缺少 ${depth} 个结束标签`);
  } else {
    console.log(`\n❌ 传统模式有 ${Math.abs(depth)} 个多余的结束标签`);
  }
}

// 运行计算
if (require.main === module) {
  calculateLegacyModeDepth();
}

module.exports = { calculateLegacyModeDepth };
