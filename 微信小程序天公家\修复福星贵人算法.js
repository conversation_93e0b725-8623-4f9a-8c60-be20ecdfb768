/**
 * 修复福星贵人算法
 * 分析为什么福星贵人没有被发现，并修复算法
 */

console.log('🔍 修复福星贵人算法');
console.log('='.repeat(40));
console.log('');

// 测试用例：2020年8月1日 14:07 - 庚子 癸未 丙子 乙未
const testBaziData = {
  year_gan: '庚', year_zhi: '子',
  month_gan: '癸', month_zhi: '未',
  day_gan: '丙', day_zhi: '子',
  hour_gan: '乙', hour_zhi: '未'
};

console.log('📋 测试用例信息：');
console.log('='.repeat(30));
console.log(`四柱：${testBaziData.year_gan}${testBaziData.year_zhi} ${testBaziData.month_gan}${testBaziData.month_zhi} ${testBaziData.day_gan}${testBaziData.day_zhi} ${testBaziData.hour_gan}${testBaziData.hour_zhi}`);
console.log(`日干：${testBaziData.day_gan}`);
console.log('');

// 构建四柱数据
const fourPillars = [
  { gan: testBaziData.year_gan, zhi: testBaziData.year_zhi },   // 年柱
  { gan: testBaziData.month_gan, zhi: testBaziData.month_zhi }, // 月柱
  { gan: testBaziData.day_gan, zhi: testBaziData.day_zhi },     // 日柱
  { gan: testBaziData.hour_gan, zhi: testBaziData.hour_zhi }    // 时柱
];

console.log('🔍 分析福星贵人计算方法：');
console.log('='.repeat(35));

// 方法1：传统福星贵人算法
console.log('📚 方法1：传统福星贵人算法');
console.log('甲乙见寅卯，丙丁见巳午，戊己见巳午，庚辛见申酉，壬癸见亥子');

const traditionalFuxingMap = {
  '甲': ['寅', '卯'], '乙': ['寅', '卯'],
  '丙': ['巳', '午'], '丁': ['巳', '午'],
  '戊': ['巳', '午'], '己': ['巳', '午'],
  '庚': ['申', '酉'], '辛': ['申', '酉'],
  '壬': ['亥', '子'], '癸': ['亥', '子']
};

const dayGan = testBaziData.day_gan; // 丙
const traditionalTargets = traditionalFuxingMap[dayGan] || [];
console.log(`丙日干的福星贵人：${traditionalTargets.join('、')}`);

let traditionalFound = false;
fourPillars.forEach((pillar, index) => {
  if (traditionalTargets.includes(pillar.zhi)) {
    console.log(`✅ 传统方法发现福星贵人在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
    traditionalFound = true;
  }
});

if (!traditionalFound) {
  console.log('❌ 传统方法未发现福星贵人');
}

// 方法2：权威福星贵人算法（基于古籍）
console.log('\n📜 方法2：权威福星贵人算法（基于古籍）');
console.log('甲乙木人寅卯真，丙丁火人巳午寻，戊己土人巳午是，庚辛金人申酉临，壬癸水人亥子位');

// 与传统方法相同，验证是否有其他问题
console.log('权威方法与传统方法相同，问题不在算法本身');

// 方法3：检查是否有其他福星贵人算法
console.log('\n🔍 方法3：检查其他可能的福星贵人算法');

// 福星贵人的另一种算法：以年支为基准
console.log('📋 以年支为基准的福星贵人算法：');
const yearZhi = testBaziData.year_zhi; // 子
console.log(`年支：${yearZhi}`);

const yearBasedFuxingMap = {
  '子': ['申', '辰'], '丑': ['巳', '酉'], '寅': ['午', '戌'], '卯': ['未', '亥'],
  '辰': ['申', '子'], '巳': ['酉', '丑'], '午': ['戌', '寅'], '未': ['亥', '卯'],
  '申': ['子', '辰'], '酉': ['丑', '巳'], '戌': ['寅', '午'], '亥': ['卯', '未']
};

const yearBasedTargets = yearBasedFuxingMap[yearZhi] || [];
console.log(`年支${yearZhi}的福星贵人：${yearBasedTargets.join('、')}`);

let yearBasedFound = false;
fourPillars.forEach((pillar, index) => {
  if (yearBasedTargets.includes(pillar.zhi)) {
    console.log(`✅ 年支方法发现福星贵人在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
    yearBasedFound = true;
  }
});

if (!yearBasedFound) {
  console.log('❌ 年支方法未发现福星贵人');
}

// 方法4：检查"问真八字"可能使用的特殊算法
console.log('\n🎯 方法4："问真八字"特殊算法分析');

// 根据"问真八字"显示福星贵人存在，但位置未知，可能使用不同算法
console.log('分析"问真八字"可能的福星贵人算法：');

// 可能的算法1：福星贵人与天乙贵人相关
console.log('🔍 可能算法1：福星贵人与天乙贵人相关');
const tianyiMap = {
  '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'],
  '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
  '壬': ['卯', '巳'], '癸': ['卯', '巳']
};

const tianyiTargets = tianyiMap[dayGan] || [];
console.log(`丙日干的天乙贵人：${tianyiTargets.join('、')}`);

// 可能的算法2：福星贵人基于食神位置
console.log('\n🔍 可能算法2：福星贵人基于食神位置');
const shiganOrder = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dayGanIndex = shiganOrder.indexOf(dayGan);
const shishenIndex = (dayGanIndex + 2) % 10; // 食神位置
const shishen = shiganOrder[shishenIndex];
console.log(`丙日干的食神：${shishen}`);

// 食神对应的地支
const ganZhiMap = {
  '甲': '寅', '乙': '卯', '丙': '午', '丁': '巳', '戊': '辰',
  '己': '未', '庚': '申', '辛': '酉', '壬': '子', '癸': '亥'
};

const shishenZhi = ganZhiMap[shishen];
console.log(`食神${shishen}对应地支：${shishenZhi}`);

let shishenFound = false;
fourPillars.forEach((pillar, index) => {
  if (pillar.zhi === shishenZhi) {
    console.log(`✅ 食神方法发现福星贵人在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
    shishenFound = true;
  }
});

if (!shishenFound) {
  console.log('❌ 食神方法未发现福星贵人');
}

// 可能的算法3：福星贵人基于月令
console.log('\n🔍 可能算法3：福星贵人基于月令');
const monthZhi = testBaziData.month_zhi; // 未
console.log(`月支：${monthZhi}`);

// 月令福星贵人算法
const monthBasedFuxingMap = {
  '寅': ['甲', '丙'], '卯': ['乙', '丁'], '辰': ['戊', '庚'], '巳': ['己', '辛'],
  '午': ['壬', '甲'], '未': ['癸', '乙'], '申': ['丙', '戊'], '酉': ['丁', '己'],
  '戌': ['庚', '壬'], '亥': ['辛', '癸'], '子': ['戊', '庚'], '丑': ['己', '辛']
};

const monthBasedTargets = monthBasedFuxingMap[monthZhi] || [];
console.log(`月支${monthZhi}的福星贵人天干：${monthBasedTargets.join('、')}`);

let monthBasedFound = false;
fourPillars.forEach((pillar, index) => {
  if (monthBasedTargets.includes(pillar.gan)) {
    console.log(`✅ 月令方法发现福星贵人在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
    monthBasedFound = true;
  }
});

if (!monthBasedFound) {
  console.log('❌ 月令方法未发现福星贵人');
}

// 可能的算法4：福星贵人基于纳音
console.log('\n🔍 可能算法4：福星贵人基于纳音');

// 纳音福星贵人（简化版）
const nayinFuxingMap = {
  '庚子': ['甲', '戊'], '癸未': ['丁', '己'], '丙子': ['庚', '壬'], '乙未': ['己', '癸']
};

let nayinFound = false;
fourPillars.forEach((pillar, index) => {
  const pillarKey = pillar.gan + pillar.zhi;
  const nayinTargets = nayinFuxingMap[pillarKey] || [];
  if (nayinTargets.length > 0) {
    console.log(`${pillarKey}的纳音福星贵人天干：${nayinTargets.join('、')}`);
    
    fourPillars.forEach((checkPillar, checkIndex) => {
      if (nayinTargets.includes(checkPillar.gan)) {
        console.log(`✅ 纳音方法发现福星贵人：${pillarKey}在${['年柱', '月柱', '日柱', '时柱'][checkIndex]}找到${checkPillar.gan}`);
        nayinFound = true;
      }
    });
  }
});

if (!nayinFound) {
  console.log('❌ 纳音方法未发现福星贵人');
}

console.log('\n📊 福星贵人算法测试结果：');
console.log('='.repeat(35));
console.log(`传统算法：${traditionalFound ? '✅ 发现' : '❌ 未发现'}`);
console.log(`年支算法：${yearBasedFound ? '✅ 发现' : '❌ 未发现'}`);
console.log(`食神算法：${shishenFound ? '✅ 发现' : '❌ 未发现'}`);
console.log(`月令算法：${monthBasedFound ? '✅ 发现' : '❌ 未发现'}`);
console.log(`纳音算法：${nayinFound ? '✅ 发现' : '❌ 未发现'}`);

console.log('\n🎯 结论分析：');
console.log('='.repeat(15));

if (!traditionalFound && !yearBasedFound && !shishenFound && !monthBasedFound && !nayinFound) {
  console.log('❌ 所有测试的算法都未发现福星贵人');
  console.log('🤔 可能的原因：');
  console.log('   1. "问真八字"使用了我们未知的特殊算法');
  console.log('   2. 福星贵人的计算需要考虑更多因素（如时间、性别等）');
  console.log('   3. 可能存在算法版本差异');
  console.log('   4. 需要进一步研究古籍资料');
} else {
  console.log('✅ 找到了可用的福星贵人算法');
}

console.log('\n📋 下一步行动：');
console.log('='.repeat(15));
console.log('1. 研究更多古籍资料中的福星贵人算法');
console.log('2. 分析"问真八字"的具体计算逻辑');
console.log('3. 考虑实现多种福星贵人算法并进行验证');
console.log('4. 优先使用最权威的算法版本');

console.log('\n✅ 福星贵人算法分析完成！');
