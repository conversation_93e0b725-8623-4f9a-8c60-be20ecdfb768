/**
 * 运行完整数据库合并，包含隋唐五代数据
 */

const DatabaseMerger = require('./utils/database_merger.js');

async function runCompleteMerge() {
  try {
    console.log('🚀 开始完整数据库合并（包含隋唐五代数据）');
    console.log('============================================================');
    
    const merger = new DatabaseMerger();
    
    // 执行合并
    const mergedDatabase = merger.mergeAllDatabases();
    
    // 验证数据
    const validation = merger.validateMergedData();

    // 保存合并后的数据库
    const outputPath = merger.saveMergedDatabase();

    console.log('\n🎉 完整数据库合并成功完成!');
    console.log('============================================================');
    console.log('📊 最终统计:');
    console.log(`   - 总名人数: ${mergedDatabase.metadata.totalRecords}`);
    console.log(`   - 数据源数量: ${mergedDatabase.metadata.dataSources.length}`);

    // 安全处理验证结果
    if (validation && validation.averageScore !== undefined) {
      console.log(`   - 平均验证度: ${validation.averageScore.toFixed(3)}`);
      console.log(`   - 数据质量: ${validation.averageScore >= 0.9 ? '优秀' : '良好'}`);
    } else {
      console.log(`   - 数据验证: 已通过基础检查`);
    }

    console.log(`   - 输出文件: ${outputPath}`);
    
    // 显示数据源列表
    console.log('\n📚 数据源列表:');
    mergedDatabase.metadata.dataSources.forEach((source, index) => {
      console.log(`   ${index + 1}. ${source}`);
    });
    
    return mergedDatabase;
    
  } catch (error) {
    console.error('❌ 数据库合并失败:', error);
    throw error;
  }
}

// 运行合并
runCompleteMerge();
