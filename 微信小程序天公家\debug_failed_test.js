// 分析失败的测试用例
// 2000年1月1日：期望丁丑，实际丙子

console.log('🔍 分析失败的测试用例');

const tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"];
const dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"];

// 测试用例：2000年1月1日
const year = 2000;
const month = 1;
const day = 1;

console.log(`\n📅 分析日期: ${year}年${month}月${day}日`);
console.log(`期望结果: 丁丑`);

// 1. 年柱计算分析
console.log(`\n🗓️ 年柱计算分析:`);

// 立春判断
let actualYear = year;
if (month < 2 || (month === 2 && day < 4)) {
  actualYear = year - 1;
  console.log(`1月1日 < 立春(2月4日) → 使用上一年: ${actualYear}`);
} else {
  console.log(`已过立春 → 使用当年: ${actualYear}`);
}

const yearGanIndex = (actualYear - 4) % 10;
const yearZhiIndex = (actualYear - 4) % 12;
const yearGan = tiangan[yearGanIndex];
const yearZhi = dizhi[yearZhiIndex];

console.log(`实际干支年: ${actualYear}`);
console.log(`年干计算: (${actualYear} - 4) % 10 = ${yearGanIndex} → ${yearGan}`);
console.log(`年支计算: (${actualYear} - 4) % 12 = ${yearZhiIndex} → ${yearZhi}`);
console.log(`年柱: ${yearGan}${yearZhi}`);

// 2. 节气月份计算分析
console.log(`\n🌙 节气月份计算分析:`);

function getSolarMonthByNodeQi(month, day) {
  const nodeQiMap = {
    1: 6   // 小寒约1月6日
  };

  const nodeQiDay = nodeQiMap[month];
  console.log(`1月节气日: 小寒${nodeQiDay}日`);
  
  if (day >= nodeQiDay) {
    console.log(`1月${day}日 >= 小寒${nodeQiDay}日 → 进入丑月`);
    return 12; // 1月小寒后 → 丑月
  } else {
    console.log(`1月${day}日 < 小寒${nodeQiDay}日 → 仍在子月`);
    return 11; // 1月小寒前 → 子月
  }
}

const solarMonth = getSolarMonthByNodeQi(month, day);
console.log(`节气月序号: ${solarMonth}`);

// 3. 地支映射分析
console.log(`\n🔧 地支映射分析:`);
const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
const monthZhi = monthZhiMap[solarMonth - 1];

console.log(`地支映射: monthZhiMap[${solarMonth} - 1] = monthZhiMap[${solarMonth - 1}] = ${monthZhi}`);

// 4. 五虎遁计算分析
console.log(`\n🐅 五虎遁计算分析:`);
const wuhuDunMap = {
  '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
  '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
  '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
  '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
  '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
};

const monthGanStart = wuhuDunMap[yearGan];
const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
const monthGan = tiangan[monthGanIndex];

console.log(`年干: ${yearGan}`);
console.log(`五虎遁起始: ${tiangan[monthGanStart]} (索引${monthGanStart})`);
console.log(`月干计算: (${monthGanStart} + ${solarMonth} - 1) % 10 = ${monthGanIndex}`);
console.log(`月干: ${monthGan}`);

// 5. 最终结果
console.log(`\n✅ 计算结果:`);
console.log(`年柱: ${yearGan}${yearZhi}`);
console.log(`月柱: ${monthGan}${monthZhi}`);

// 6. 问题分析
console.log(`\n❌ 问题分析:`);
console.log(`期望: 丁丑`);
console.log(`实际: ${monthGan}${monthZhi}`);

if (monthGan !== '丁' || monthZhi !== '丑') {
  console.log(`\n🔍 可能的问题:`);
  
  if (monthZhi !== '丑') {
    console.log(`1. 地支错误: 得到${monthZhi}，期望丑`);
    console.log(`   - 1月1日应该在小寒前还是小寒后？`);
    console.log(`   - 小寒通常在1月5-6日，1月1日应该还在子月`);
    console.log(`   - 但期望是丑月，说明可能节气日期有误`);
  }
  
  if (monthGan !== '丁') {
    console.log(`2. 天干错误: 得到${monthGan}，期望丁`);
    console.log(`   - 可能是年干计算错误`);
    console.log(`   - 或者五虎遁起始点错误`);
  }
}

// 7. 验证正确的计算
console.log(`\n🔧 验证正确的计算:`);
console.log(`如果期望是丁丑，反推:`);

// 反推年干
const expectedMonthGan = '丁';
const expectedMonthZhi = '丑';
const expectedSolarMonth = monthZhiMap.indexOf(expectedMonthZhi) + 1;

console.log(`期望月支${expectedMonthZhi} → 节气月序号${expectedSolarMonth}`);

// 反推年干
Object.keys(wuhuDunMap).forEach(testYearGan => {
  const testMonthGanStart = wuhuDunMap[testYearGan];
  const testMonthGanIndex = (testMonthGanStart + expectedSolarMonth - 1) % 10;
  const testMonthGan = tiangan[testMonthGanIndex];
  
  if (testMonthGan === expectedMonthGan) {
    console.log(`如果年干是${testYearGan}，月干就是${testMonthGan} ✅`);
    
    // 验证这个年干是否正确
    const testYearGanIndex = tiangan.indexOf(testYearGan);
    const testYear = (testYearGanIndex + 4) % 10 === 0 ? 
                     Math.floor((actualYear - 4) / 10) * 10 + 4 + testYearGanIndex :
                     actualYear;
    console.log(`   对应的年份计算需要验证...`);
  }
});

console.log(`\n📋 结论:`);
console.log(`这个测试失败可能是因为:`);
console.log(`1. 节气日期不够精确（小寒具体日期）`);
console.log(`2. 期望值可能有误`);
console.log(`3. 需要更精确的节气计算`);
