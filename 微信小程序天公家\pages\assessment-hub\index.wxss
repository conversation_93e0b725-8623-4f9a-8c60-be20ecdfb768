/* pages/assessment-hub/index.wxss */

/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #171923; /* 默认深色背景 */
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
}

/* 主题样式 */
.ai-theme {
  background: linear-gradient(135deg, #6550e9 0%, #8370fa 50%, #6550e9 100%);
  position: relative;
}

.ai-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.student-theme {
  background: linear-gradient(135deg, #c75c1a 0%, #e87928 50%, #c75c1a 100%);
  position: relative;
}

.student-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.parent-theme {
  background: linear-gradient(135deg, #7928ca 0%, #9546e7 50%, #7928ca 100%);
  position: relative;
}

.parent-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.teacher-theme {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #16a34a 100%);
  position: relative;
}

.teacher-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 65% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 35% 65%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.doctor-theme {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #2563eb 100%);
  position: relative;
}

.doctor-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 70% 35%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 30% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

/* 占卜主题样式 */
.tarot-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
  position: relative;
}

.tarot-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.yijing-theme {
  background: linear-gradient(135deg, #4A5568 0%, #718096 50%, #4A5568 100%);
  position: relative;
}

.yijing-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.ziwei-theme {
  background: linear-gradient(135deg, #553C9A 0%, #9F7AEA 50%, #553C9A 100%);
  position: relative;
}

.ziwei-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 60%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.qimen-theme {
  background: linear-gradient(135deg, #9C7C38 0%, #D4AF37 50%, #9C7C38 100%);
  position: relative;
}

.qimen-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 40% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 60% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.bazi-theme {
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 50%, #8B4513 100%);
  position: relative;
}

.bazi-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 30%, rgba(218, 165, 32, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 70% 70%, rgba(218, 165, 32, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.liuyao-theme {
  background: linear-gradient(135deg, #2E4057 0%, #4A6741 50%, #2E4057 100%);
  position: relative;
}

.liuyao-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 50% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 50% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 25%);
  z-index: 0;
}

/* 主题类样式 */
.theme-ai {
  background: linear-gradient(135deg, #6550e9, #4b3bba);
  position: relative;
}
.theme-ai::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

.theme-student {
  background: linear-gradient(135deg, #c75c1a, #a0470f);
  position: relative;
}
.theme-student::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

.theme-parent {
  background: linear-gradient(135deg, #7928ca, #5f1da3);
  position: relative;
}
.theme-parent::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

.theme-teacher {
  background: linear-gradient(135deg, #16a34a, #0f7a36);
  position: relative;
}
.theme-teacher::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

.theme-doctor {
  background: linear-gradient(135deg, #2563eb, #1a4ab1);
  position: relative;
}
.theme-doctor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 40% 30%, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 20%);
  background-size: 120% 120%;
  z-index: 1;
}

/* 顶部导航区域 */
.header-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 30rpx 20rpx;
  margin-top: 20rpx;
  position: relative;
  z-index: 10;
}

/* 个人资料图标 */
.profile-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-icon:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.profile-icon image {
  width: 40rpx;
  height: 40rpx;
}

/* 标签选择器 */
.tab-container {
  width: 60%;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 40rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 日期显示 */
.date-display {
  padding: 20rpx 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  position: relative;
}

/* 角色切换区 */
.content-swiper {
  flex: 1;
  width: 100%;
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: visible;
  position: relative;
}

/* 设置指示点的位置和样式 */
.wx-swiper-dots {
  margin-bottom: -5px;
}

.wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 20rpx;
}

.wx-swiper-dots.wx-swiper-dots-vertical {
  right: 20rpx;
}

.swiper-item {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  overflow: visible !important;
}

.role-scroll-view {
  height: 100%;
  width: 100%;
  overflow: visible;
}

/* 角色卡片样式 - 仙风道骨设计 */
.role-card {
  width: 85%;
  padding: 40rpx 30rpx 60rpx; /* 增加底部内边距 */
  margin: 40rpx auto;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08),
              0 4rpx 16rpx rgba(0, 0, 0, 0.04),
              inset 0 1px 2px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 850rpx; /* 减少卡片高度约1.5cm */
  position: relative;
  overflow: hidden;
  margin-bottom: 60rpx; /* 减少卡片底部空间 */
  align-items: center; /* 添加居中对齐 */
  text-align: center; /* 文本居中 */
  z-index: 2; /* 确保卡片在背景图案上方 */
  backdrop-filter: blur(8rpx); /* 增强模糊效果提升仙气感 */
}

/* 塔罗占卜卡片特殊样式 */
.tarot-divination {
  background: linear-gradient(135deg, rgba(107, 91, 115, 0.15) 0%, rgba(168, 146, 109, 0.15) 100%);
  border: 1px solid rgba(168, 146, 109, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(107, 91, 115, 0.2), inset 0 1px 1px rgba(255, 215, 0, 0.08);
}

.tarot-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.04) 0%, transparent 35%),
                    radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.04) 0%, transparent 35%);
  z-index: -1;
}

/* 易经八卦卡片特殊样式 */
.yijing-divination {
  background: linear-gradient(135deg, rgba(47, 79, 79, 0.15) 0%, rgba(112, 128, 144, 0.15) 100%);
  border: 1px solid rgba(112, 128, 144, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(47, 79, 79, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.yijing-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 30%),
                    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);
  z-index: -1;
}

/* 紫微斗数卡片特殊样式 */
.ziwei-divination {
  background: linear-gradient(135deg, rgba(75, 0, 130, 0.15) 0%, rgba(138, 43, 226, 0.15) 100%);
  border: 1px solid rgba(138, 43, 226, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(75, 0, 130, 0.2), inset 0 1px 1px rgba(138, 43, 226, 0.1);
}

.ziwei-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 75%, rgba(138, 43, 226, 0.05) 0%, transparent 30%),
                    radial-gradient(circle at 75% 25%, rgba(138, 43, 226, 0.05) 0%, transparent 30%);
  z-index: -1;
}

/* 奇门遁甲卡片特殊样式 */
.qimen-divination {
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.15) 0%, rgba(218, 165, 32, 0.15) 100%);
  border: 1px solid rgba(218, 165, 32, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(184, 134, 11, 0.2), inset 0 1px 1px rgba(218, 165, 32, 0.1);
}

.qimen-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 40% 60%, rgba(218, 165, 32, 0.05) 0%, transparent 30%),
                    radial-gradient(circle at 60% 40%, rgba(218, 165, 32, 0.05) 0%, transparent 30%);
  z-index: -1;
}

/* 八字排盘卡片特殊样式 */
.bazi-divination {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.15) 0%, rgba(210, 180, 140, 0.15) 100%);
  border: 1px solid rgba(218, 165, 32, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(139, 69, 19, 0.2), inset 0 1px 1px rgba(218, 165, 32, 0.1);
}

.bazi-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 30%, rgba(218, 165, 32, 0.05) 0%, transparent 30%),
                    radial-gradient(circle at 70% 70%, rgba(218, 165, 32, 0.05) 0%, transparent 30%);
  z-index: -1;
}

/* 六爻占卜卡片特殊样式 */
.liuyao-divination {
  background: linear-gradient(135deg, rgba(46, 64, 87, 0.15) 0%, rgba(74, 103, 65, 0.15) 100%);
  border: 1px solid rgba(74, 103, 65, 0.3);
  box-shadow: 0 8rpx 30rpx rgba(46, 64, 87, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.liuyao-divination::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 50% 20%, rgba(74, 103, 65, 0.05) 0%, transparent 30%),
                    radial-gradient(circle at 50% 80%, rgba(74, 103, 65, 0.05) 0%, transparent 30%);
  z-index: -1;
}

.role-card:hover, .role-card:active {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.12),
              0 8rpx 24rpx rgba(0, 0, 0, 0.06),
              inset 0 1px 3px rgba(255, 255, 255, 0.08);
}

.role-title {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  justify-content: center;
  align-items: center; /* 居中显示 */
  margin-bottom: 30rpx;
  width: 100%;
  margin-top: 20rpx; /* 增加顶部间距 */
}

.role-title text {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx; /* 添加底部间距 */
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
}

.role-icon {
  width: 130rpx; /* 天公师兄头像尺寸调整为130rpx */
  height: 130rpx; /* 天公师兄头像尺寸调整为130rpx */
  opacity: 0.9; /* 稍微透明 */
  border-radius: 50%; /* 添加圆形边框 */
  object-fit: cover; /* 确保图片填充方式一致 */
}

/* 专门为PNG头像（天工师父、天公师娘）优化显示 */
.role-icon-png {
  width: 160rpx !important; /* 与天公师兄保持相同大尺寸 */
  height: 160rpx !important; /* 与天公师兄保持相同大尺寸 */
  border-radius: 50% !important; /* 保持圆形显示 */
  background: transparent !important; /* 透明背景 */
  border: none !important; /* 去掉边框 */
  opacity: 1 !important; /* 完全不透明，更清晰 */
}

.role-description {
  margin: 40rpx 0 60rpx; /* 增加与按钮的间距 */
  text-align: center; /* 确保文本居中 */
  width: 100%;
}

.role-description text {
  font-size: 34rpx; /* 增加字体大小 */
  color: rgba(255, 255, 255, 0.9); /* 增加透明度 */
  line-height: 1.6;
  text-align: center; /* 确保文本居中 */
}

.start-chat {
  display: flex;
  align-items: center;
  justify-content: center; /* 按钮内容居中 */
  margin-top: 20rpx;
  background-color: rgba(255, 255, 255, 0.25);
  padding: 24rpx 40rpx;
  border-radius: 100rpx; /* 更圆润的边角 */
  align-self: center; /* 按钮整体居中 */
  width: 70%; /* 设置固定宽度 */
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.start-chat:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.start-chat text {
  font-size: 32rpx;
  color: #ffffff;
  margin-right: 25rpx;
  text-align: center;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.arrow-icon {
  width: 36rpx;
  height: 36rpx;
  margin-left: 10rpx;
  position: relative;
  right: -5rpx;
}

/* 添加一个中央图标容器 */
.role-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 60rpx auto 40rpx;
}

.role-icon-container image {
  width: 140rpx;
  height: 140rpx;
  opacity: 0.8;
}

/* 分享按钮样式 */
.share-button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 40rpx;
  padding: 25rpx 0;
  text-align: center;
  margin: 60rpx auto 20rpx;
  width: 80%;
  font-size: 32rpx;
  letter-spacing: 1rpx;
}

.clock-icon {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
}

.clock-icon image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

/* 输入区域 */
.input-area {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
  align-items: center;
  position: relative;
  z-index: 10;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
  color: white;
  font-size: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.send-button, .voice-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.send-button {
  margin-right: 10rpx;
}

.send-button.active {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.send-button image, .voice-button image {
  width: 40rpx;
  height: 40rpx;
}

.button-hover {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 滑动提示效果 */
.swipe-hint {
  position: absolute;
  top: 50%;
  right: 30rpx;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 6;
}

.swipe-hint.show {
  opacity: 1;
  animation: pulseHint 2s infinite;
}

.swipe-arrow {
  width: 40rpx;
  height: 40rpx;
  border-right: 4rpx solid rgba(255, 255, 255, 0.8);
  border-bottom: 4rpx solid rgba(255, 255, 255, 0.8);
  transform: rotate(-45deg);
  margin-bottom: 10rpx;
}

.swipe-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

@keyframes pulseHint {
  0% {
    opacity: 0.7;
    transform: translateY(-50%);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) translateX(10rpx);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%);
  }
}

/* 屏幕适配调整 */
@media screen and (max-height: 700px) {
  .role-card {
    padding: 30rpx;
  }
  
  .role-title text {
    font-size: 40rpx;
  }
  
  .role-description text {
    font-size: 26rpx;
  }
}

/* 滑动提示 */
.swipe-tip {
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  padding: 10rpx 0;
  animation: pulse 1.5s infinite ease-in-out;
  position: relative;
  z-index: 5;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.start-chat-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 60rpx;
}

/* 占卜主题特定按钮样式 */
/* 塔罗占卜主题按钮 */
.tarot-theme .send-button.active,
.tarot-theme .start-chat {
  background: linear-gradient(135deg, rgba(107, 91, 115, 0.8) 0%, rgba(168, 146, 109, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(107, 91, 115, 0.3);
}

.tarot-theme .tab.active {
  background: rgba(107, 91, 115, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(107, 91, 115, 0.2);
}

/* 易经八卦主题按钮 */
.yijing-theme .send-button.active,
.yijing-theme .start-chat {
  background: linear-gradient(135deg, rgba(74, 85, 104, 0.8) 0%, rgba(113, 128, 150, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(74, 85, 104, 0.3);
}

.yijing-theme .tab.active {
  background: rgba(74, 85, 104, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(74, 85, 104, 0.2);
}

/* 紫微斗数主题按钮 */
.ziwei-theme .send-button.active,
.ziwei-theme .start-chat {
  background: linear-gradient(135deg, rgba(85, 60, 154, 0.8) 0%, rgba(159, 122, 234, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(85, 60, 154, 0.3);
}

.ziwei-theme .tab.active {
  background: rgba(85, 60, 154, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(85, 60, 154, 0.2);
}

/* 奇门遁甲主题按钮 */
.qimen-theme .send-button.active,
.qimen-theme .start-chat {
  background: linear-gradient(135deg, rgba(156, 124, 56, 0.8) 0%, rgba(212, 175, 55, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(156, 124, 56, 0.3);
}

.qimen-theme .tab.active {
  background: rgba(156, 124, 56, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(156, 124, 56, 0.2);
}

/* 八字排盘主题按钮 */
.bazi-theme .send-button.active,
.bazi-theme .start-chat {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.8) 0%, rgba(210, 180, 140, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(139, 69, 19, 0.3);
}

.bazi-theme .tab.active {
  background: rgba(139, 69, 19, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

/* 六爻占卜主题按钮 */
.liuyao-theme .send-button.active,
.liuyao-theme .start-chat {
  background: linear-gradient(135deg, rgba(46, 64, 87, 0.8) 0%, rgba(74, 103, 65, 0.8) 100%);
  box-shadow: 0 4rpx 15rpx rgba(46, 64, 87, 0.3);
}

.liuyao-theme .tab.active {
  background: rgba(46, 64, 87, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(46, 64, 87, 0.2);
}