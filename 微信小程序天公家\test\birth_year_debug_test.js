// test/birth_year_debug_test.js
// 调试年龄推算问题

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');

class BirthYearDebugTest {
  constructor() {
    this.engine = new ProfessionalTimingEngine();
  }

  runDebugTest() {
    console.log('🔍 开始年龄推算调试测试...\n');

    // 测试用例1: 2023年癸卯年（婴儿）
    console.log('👶 测试用例1: 2023年癸卯年（婴儿）');
    this.testBirthYearExtraction({
      year_pillar: { heavenly: '癸', earthly: '卯' },
      month_pillar: { heavenly: '丙', earthly: '寅' },
      day_pillar: { heavenly: '戊', earthly: '午' },
      time_pillar: { heavenly: '壬', earthly: '戌' },
      day_master: '戊'
    }, 2023, '癸卯');

    // 测试用例2: 1999年己亥年（成年人）
    console.log('\n👨 测试用例2: 1999年己亥年（成年人）');
    this.testBirthYearExtraction({
      year_pillar: { heavenly: '己', earthly: '亥' },
      month_pillar: { heavenly: '丙', earthly: '寅' },
      day_pillar: { heavenly: '戊', earthly: '午' },
      time_pillar: { heavenly: '壬', earthly: '戌' },
      day_master: '戊'
    }, 1999, '己亥');

    // 测试用例3: 2009年己酉年（少年）
    console.log('\n🧒 测试用例3: 2009年己酉年（少年）');
    this.testBirthYearExtraction({
      year_pillar: { heavenly: '己', earthly: '酉' },
      month_pillar: { heavenly: '丙', earthly: '寅' },
      day_pillar: { heavenly: '戊', earthly: '午' },
      time_pillar: { heavenly: '壬', earthly: '戌' },
      day_master: '戊'
    }, 2009, '己酉');

    console.log('\n============================================================');
    console.log('🔍 年龄推算调试总结');
    console.log('============================================================');
  }

  testBirthYearExtraction(bazi, expectedYear, expectedGanZhi) {
    console.log(`  📊 测试数据:`);
    console.log(`    期望年份: ${expectedYear}年`);
    console.log(`    期望干支: ${expectedGanZhi}`);
    console.log(`    输入干支: ${bazi.year_pillar.heavenly}${bazi.year_pillar.earthly}`);

    // 测试干支计算
    const currentYear = new Date().getFullYear();
    const calculatedGanZhi = this.engine.getYearGanZhi(expectedYear);
    console.log(`    计算干支: ${calculatedGanZhi}`);
    console.log(`    干支匹配: ${calculatedGanZhi === expectedGanZhi ? '✅ 正确' : '❌ 错误'}`);

    // 测试年龄推算
    const extractedYear = this.engine.extractBirthYear(bazi);
    const extractedAge = currentYear - extractedYear;
    const expectedAge = currentYear - expectedYear;
    
    console.log(`    推算年份: ${extractedYear}年`);
    console.log(`    推算年龄: ${extractedAge}岁`);
    console.log(`    期望年龄: ${expectedAge}岁`);
    console.log(`    年龄推算: ${extractedAge === expectedAge ? '✅ 正确' : '❌ 错误'}`);

    // 测试年龄验证
    const ageValidation = this.engine.validateAgeRequirements(bazi, 'marriage', currentYear);
    console.log(`    年龄验证: ${ageValidation.valid ? '✅ 通过' : '❌ 未通过'}`);
    if (!ageValidation.valid) {
      console.log(`    验证信息: ${ageValidation.message}`);
    }

    // 测试年龄因子
    const ageFactor = this.engine.calculateAgeEnergyFactor(extractedAge, 'marriage');
    console.log(`    年龄因子: ${ageFactor.toFixed(3)}`);

    // 验证干支推算逻辑
    console.log(`  🔧 干支推算验证:`);
    const inputGanZhi = bazi.year_pillar.heavenly + bazi.year_pillar.earthly;
    console.log(`    输入: ${inputGanZhi}`);
    
    // 手动验证推算逻辑
    const possibleYears = [];
    for (let year = currentYear; year >= currentYear - 120; year--) {
      const yearGanZhi = this.engine.getYearGanZhi(year);
      if (yearGanZhi === inputGanZhi) {
        possibleYears.push(year);
      }
    }
    
    console.log(`    可能年份: ${possibleYears.join(', ')}`);
    console.log(`    选择年份: ${possibleYears.length > 0 ? possibleYears[0] : '无'}`);
    
    if (possibleYears.includes(expectedYear)) {
      console.log(`    ✅ 期望年份在可能年份中`);
    } else {
      console.log(`    ❌ 期望年份不在可能年份中`);
    }
  }
}

// 运行调试测试
const debugTest = new BirthYearDebugTest();
debugTest.runDebugTest();
