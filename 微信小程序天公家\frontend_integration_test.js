/**
 * 前端集成测试套件
 * 验证专业级五行动态交互分析系统的前端集成完整性
 */

const UnifiedWuxingAPI = require('./utils/unified_wuxing_api.js');

class FrontendIntegrationTest {
  constructor() {
    this.api = new UnifiedWuxingAPI();
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      details: []
    };
  }

  /**
   * 执行完整的前端集成测试
   */
  async runCompleteIntegrationTest() {
    console.log('🚀 开始前端集成测试...\n');

    // 测试用例：2021年6月24日19:30 (标准案例)
    const testFourPillars = [
      { gan: '辛', zhi: '丑' }, // 年柱
      { gan: '甲', zhi: '午' }, // 月柱  
      { gan: '癸', zhi: '卯' }, // 日柱
      { gan: '壬', zhi: '戌' }  // 时柱
    ];

    // 1. 测试统一API完整分析
    await this.testUnifiedAPICompleteAnalysis(testFourPillars);

    // 2. 测试前端数据格式兼容性
    await this.testFrontendDataCompatibility(testFourPillars);

    // 3. 测试界面组件数据完整性
    await this.testUIComponentDataIntegrity(testFourPillars);

    // 4. 测试性能和响应时间
    await this.testPerformanceAndResponseTime(testFourPillars);

    // 5. 测试错误处理和降级机制
    await this.testErrorHandlingAndFallback();

    // 6. 测试缓存机制集成
    await this.testCacheIntegration(testFourPillars);

    // 输出测试结果
    this.outputTestResults();
  }

  /**
   * 测试统一API完整分析
   */
  async testUnifiedAPICompleteAnalysis(fourPillars) {
    console.log('📊 测试1: 统一API完整分析');
    
    try {
      const startTime = Date.now();
      const result = await this.api.performCompleteAnalysis(fourPillars);
      const endTime = Date.now();

      // 验证结果结构完整性
      const checks = [
        { name: '静态分析数据', condition: result.static && result.static.results },
        { name: '动态交互数据', condition: result.interactions && Object.keys(result.interactions).length > 0 },
        { name: '动态调整数据', condition: result.dynamic && result.dynamic.adjustedPowers },
        { name: '影响评估数据', condition: result.impact && result.impact.summary },
        { name: '统一结果数据', condition: result.unified && result.unified.professionalData },
        { name: '前端数据字段', condition: result.unified.professionalData.frontendData },
        { name: '元素对比数据', condition: result.unified.professionalData.frontendData.elementComparisons },
        { name: '交互详情数据', condition: result.unified.professionalData.frontendData.interactionDetails },
        { name: '建议数据', condition: result.unified.professionalData.frontendData.recommendations },
        { name: '响应时间合理', condition: (endTime - startTime) < 5000 }
      ];

      let passed = 0;
      checks.forEach(check => {
        if (check.condition) {
          console.log(`  ✅ ${check.name}`);
          passed++;
        } else {
          console.log(`  ❌ ${check.name}`);
        }
      });

      this.recordTestResult('统一API完整分析', passed, checks.length, {
        executionTime: endTime - startTime,
        dataStructure: result.unified ? '完整' : '不完整'
      });

    } catch (error) {
      console.log(`  ❌ API调用失败: ${error.message}`);
      this.recordTestResult('统一API完整分析', 0, 10, { error: error.message });
    }
  }

  /**
   * 测试前端数据格式兼容性
   */
  async testFrontendDataCompatibility(fourPillars) {
    console.log('\n🔄 测试2: 前端数据格式兼容性');

    try {
      const result = await this.api.performCompleteAnalysis(fourPillars);
      const unified = result.unified;

      // 验证基础五行数据兼容性
      const basicElementChecks = [
        { name: 'wood字段存在', condition: typeof unified.wood === 'number' },
        { name: 'fire字段存在', condition: typeof unified.fire === 'number' },
        { name: 'earth字段存在', condition: typeof unified.earth === 'number' },
        { name: 'metal字段存在', condition: typeof unified.metal === 'number' },
        { name: 'water字段存在', condition: typeof unified.water === 'number' }
      ];

      // 验证专业数据结构
      const professionalDataChecks = [
        { name: '专业数据存在', condition: unified.professionalData !== undefined },
        { name: '算法信息完整', condition: unified.professionalData.algorithm && unified.professionalData.version },
        { name: '静态分析数据', condition: unified.professionalData.staticAnalysis !== undefined },
        { name: '动态分析数据', condition: unified.professionalData.dynamicAnalysis !== undefined },
        { name: '影响评估数据', condition: unified.professionalData.impactEvaluation !== undefined }
      ];

      // 验证前端展示数据
      const frontendDataChecks = [
        { name: '前端数据存在', condition: unified.professionalData.frontendData !== undefined },
        { name: '元素对比数组', condition: Array.isArray(unified.professionalData.frontendData.elementComparisons) },
        { name: '交互详情对象', condition: unified.professionalData.frontendData.interactionDetails && unified.professionalData.frontendData.interactionDetails.all },
        { name: '建议数据存在', condition: unified.professionalData.frontendData.recommendations !== undefined },
        { name: '置信度数值', condition: typeof unified.professionalData.frontendData.confidence === 'number' }
      ];

      const allChecks = [...basicElementChecks, ...professionalDataChecks, ...frontendDataChecks];
      let passed = 0;

      allChecks.forEach(check => {
        if (check.condition) {
          console.log(`  ✅ ${check.name}`);
          passed++;
        } else {
          console.log(`  ❌ ${check.name}`);
        }
      });

      this.recordTestResult('前端数据格式兼容性', passed, allChecks.length, {
        basicElements: basicElementChecks.filter(c => c.condition).length + '/' + basicElementChecks.length,
        professionalData: professionalDataChecks.filter(c => c.condition).length + '/' + professionalDataChecks.length,
        frontendData: frontendDataChecks.filter(c => c.condition).length + '/' + frontendDataChecks.length
      });

    } catch (error) {
      console.log(`  ❌ 兼容性测试失败: ${error.message}`);
      this.recordTestResult('前端数据格式兼容性', 0, 15, { error: error.message });
    }
  }

  /**
   * 测试界面组件数据完整性
   */
  async testUIComponentDataIntegrity(fourPillars) {
    console.log('\n🎨 测试3: 界面组件数据完整性');

    try {
      const result = await this.api.performCompleteAnalysis(fourPillars);
      const frontendData = result.unified.professionalData.frontendData;

      // 验证静态vs动态对比组件数据
      const comparisonChecks = [
        { name: '元素对比数组长度', condition: frontendData.elementComparisons.length === 5 },
        { name: '对比数据结构完整', condition: frontendData.elementComparisons.every(item => 
          item.name && item.staticValue !== undefined && item.dynamicValue !== undefined && 
          item.changeDirection && item.changePercent !== undefined
        )}
      ];

      // 验证交互关系组件数据
      const interactionChecks = [
        { name: '交互详情结构', condition: frontendData.interactionDetails.all && frontendData.interactionDetails.combinations && frontendData.interactionDetails.conflicts },
        { name: '交互数据完整性', condition: frontendData.interactionDetails.all.every(item =>
          item.description && item.effect && item.strengthLevel && item.icon
        )}
      ];

      // 验证影响评估组件数据
      const impactChecks = [
        { name: '影响评估数据', condition: result.unified.professionalData.impactEvaluation !== undefined },
        { name: '评估指标完整', condition: 
          result.unified.professionalData.impactEvaluation.overallLevel &&
          result.unified.professionalData.impactEvaluation.daymasterDirection &&
          result.unified.professionalData.impactEvaluation.fortuneTrend &&
          result.unified.professionalData.impactEvaluation.confidence !== undefined
        }
      ];

      // 验证建议组件数据
      const recommendationChecks = [
        { name: '建议数据存在', condition: frontendData.recommendations !== undefined },
        { name: '主要建议内容', condition: frontendData.recommendations.primary && frontendData.recommendations.primary.length > 0 }
      ];

      const allChecks = [...comparisonChecks, ...interactionChecks, ...impactChecks, ...recommendationChecks];
      let passed = 0;

      allChecks.forEach(check => {
        if (check.condition) {
          console.log(`  ✅ ${check.name}`);
          passed++;
        } else {
          console.log(`  ❌ ${check.name}`);
        }
      });

      this.recordTestResult('界面组件数据完整性', passed, allChecks.length, {
        elementComparisons: frontendData.elementComparisons.length,
        interactions: frontendData.interactionDetails.all.length,
        hasRecommendations: frontendData.recommendations ? 'Yes' : 'No'
      });

    } catch (error) {
      console.log(`  ❌ 组件数据测试失败: ${error.message}`);
      this.recordTestResult('界面组件数据完整性', 0, 6, { error: error.message });
    }
  }

  /**
   * 测试性能和响应时间
   */
  async testPerformanceAndResponseTime(fourPillars) {
    console.log('\n⚡ 测试4: 性能和响应时间');

    const performanceTests = [];

    // 测试单次完整分析性能
    try {
      const startTime = Date.now();
      await this.api.performCompleteAnalysis(fourPillars);
      const singleAnalysisTime = Date.now() - startTime;
      performanceTests.push({
        name: '单次完整分析',
        time: singleAnalysisTime,
        passed: singleAnalysisTime < 5000
      });
    } catch (error) {
      performanceTests.push({
        name: '单次完整分析',
        time: -1,
        passed: false,
        error: error.message
      });
    }

    // 测试连续分析性能 (缓存效果)
    try {
      const startTime = Date.now();
      for (let i = 0; i < 3; i++) {
        await this.api.performCompleteAnalysis(fourPillars);
      }
      const batchAnalysisTime = Date.now() - startTime;
      const avgTime = batchAnalysisTime / 3;
      performanceTests.push({
        name: '连续分析平均时间',
        time: avgTime,
        passed: avgTime < 2000 // 缓存应该显著提升性能
      });
    } catch (error) {
      performanceTests.push({
        name: '连续分析平均时间',
        time: -1,
        passed: false,
        error: error.message
      });
    }

    // 测试快速分析性能
    try {
      const startTime = Date.now();
      await this.api.performQuickAnalysis(fourPillars);
      const quickAnalysisTime = Date.now() - startTime;
      performanceTests.push({
        name: '快速分析时间',
        time: quickAnalysisTime,
        passed: quickAnalysisTime < 1000
      });
    } catch (error) {
      performanceTests.push({
        name: '快速分析时间',
        time: -1,
        passed: false,
        error: error.message
      });
    }

    let passed = 0;
    performanceTests.forEach(test => {
      if (test.passed) {
        console.log(`  ✅ ${test.name}: ${test.time}ms`);
        passed++;
      } else {
        console.log(`  ❌ ${test.name}: ${test.error || test.time + 'ms (超时)'}`);
      }
    });

    this.recordTestResult('性能和响应时间', passed, performanceTests.length, {
      singleAnalysis: performanceTests[0]?.time + 'ms',
      batchAverage: performanceTests[1]?.time + 'ms',
      quickAnalysis: performanceTests[2]?.time + 'ms'
    });
  }

  /**
   * 测试错误处理和降级机制
   */
  async testErrorHandlingAndFallback() {
    console.log('\n🛡️ 测试5: 错误处理和降级机制');

    const errorTests = [
      {
        name: '无效四柱数据',
        input: null,
        expectError: true
      },
      {
        name: '空四柱数组',
        input: [],
        expectError: true
      },
      {
        name: '不完整四柱',
        input: [{ gan: '甲', zhi: '子' }],
        expectError: true
      },
      {
        name: '无效干支',
        input: [
          { gan: '无效', zhi: '子' },
          { gan: '甲', zhi: '无效' },
          { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }
        ],
        expectError: true
      }
    ];

    let passed = 0;
    for (const test of errorTests) {
      try {
        const result = await this.api.performCompleteAnalysis(test.input);
        if (test.expectError) {
          console.log(`  ❌ ${test.name}: 应该抛出错误但没有`);
        } else {
          console.log(`  ✅ ${test.name}: 正常处理`);
          passed++;
        }
      } catch (error) {
        if (test.expectError) {
          console.log(`  ✅ ${test.name}: 正确抛出错误`);
          passed++;
        } else {
          console.log(`  ❌ ${test.name}: 意外错误 - ${error.message}`);
        }
      }
    }

    this.recordTestResult('错误处理和降级机制', passed, errorTests.length, {
      errorHandling: '完整',
      fallbackMechanism: '已实现'
    });
  }

  /**
   * 测试缓存机制集成
   */
  async testCacheIntegration(fourPillars) {
    console.log('\n💾 测试6: 缓存机制集成');

    try {
      // 清空缓存
      this.api.staticEngine.clearCache();

      // 第一次调用 (无缓存)
      const startTime1 = Date.now();
      await this.api.performCompleteAnalysis(fourPillars);
      const time1 = Date.now() - startTime1;

      // 第二次调用 (有缓存)
      const startTime2 = Date.now();
      await this.api.performCompleteAnalysis(fourPillars);
      const time2 = Date.now() - startTime2;

      // 获取缓存统计
      const cacheStats = this.api.staticEngine.getCacheStats();

      const checks = [
        { name: '缓存命中率 > 0', condition: cacheStats.hitRate > 0 },
        { name: '第二次调用更快', condition: time2 < time1 },
        { name: '缓存统计可用', condition: cacheStats.totalRequests > 0 },
        { name: '性能提升显著', condition: time1 > time2 * 2 }
      ];

      let passed = 0;
      checks.forEach(check => {
        if (check.condition) {
          console.log(`  ✅ ${check.name}`);
          passed++;
        } else {
          console.log(`  ❌ ${check.name}`);
        }
      });

      console.log(`  📊 缓存统计: 命中率 ${(cacheStats.hitRate * 100).toFixed(1)}%, 总请求 ${cacheStats.totalRequests}`);
      console.log(`  ⚡ 性能对比: 第一次 ${time1}ms, 第二次 ${time2}ms`);

      this.recordTestResult('缓存机制集成', passed, checks.length, {
        hitRate: (cacheStats.hitRate * 100).toFixed(1) + '%',
        performanceImprovement: ((time1 - time2) / time1 * 100).toFixed(1) + '%'
      });

    } catch (error) {
      console.log(`  ❌ 缓存测试失败: ${error.message}`);
      this.recordTestResult('缓存机制集成', 0, 4, { error: error.message });
    }
  }

  /**
   * 记录测试结果
   */
  recordTestResult(testName, passed, total, details) {
    this.testResults.total += total;
    this.testResults.passed += passed;
    this.testResults.failed += (total - passed);
    this.testResults.details.push({
      name: testName,
      passed: passed,
      total: total,
      success: passed === total,
      details: details
    });
  }

  /**
   * 输出测试结果
   */
  outputTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 前端集成测试结果汇总');
    console.log('='.repeat(60));

    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    console.log(`\n🎯 总体成功率: ${successRate}% (${this.testResults.passed}/${this.testResults.total})`);
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);

    console.log('\n📊 详细结果:');
    this.testResults.details.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const rate = (result.passed / result.total * 100).toFixed(1);
      console.log(`${index + 1}. ${status} ${result.name}: ${rate}% (${result.passed}/${result.total})`);
      
      if (result.details && Object.keys(result.details).length > 0) {
        Object.entries(result.details).forEach(([key, value]) => {
          console.log(`   - ${key}: ${value}`);
        });
      }
    });

    console.log('\n🏆 集成状态评估:');
    if (successRate >= 95) {
      console.log('🟢 优秀 - 系统完全就绪，可以投入生产使用');
    } else if (successRate >= 85) {
      console.log('🟡 良好 - 系统基本就绪，建议修复少量问题后投入使用');
    } else if (successRate >= 70) {
      console.log('🟠 一般 - 系统需要进一步优化和修复');
    } else {
      console.log('🔴 需要改进 - 系统存在重大问题，需要全面检查和修复');
    }

    console.log('\n💡 建议:');
    console.log('1. 如果测试通过率 < 100%，请检查失败的测试项目');
    console.log('2. 关注性能测试结果，确保用户体验良好');
    console.log('3. 验证错误处理机制，确保系统稳定性');
    console.log('4. 检查缓存机制，确保性能优化效果');
  }
}

// 执行测试
async function runIntegrationTest() {
  const tester = new FrontendIntegrationTest();
  await tester.runCompleteIntegrationTest();
}

// 如果直接运行此文件
if (require.main === module) {
  runIntegrationTest().catch(console.error);
}

module.exports = FrontendIntegrationTest;
