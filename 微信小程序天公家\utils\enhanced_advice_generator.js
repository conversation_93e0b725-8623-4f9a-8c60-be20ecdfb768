// utils/enhanced_advice_generator.js
// 增强版专业建议生成器
// 基于《命理格局，用神.txt》文档要求

/**
 * 增强版专业建议生成器
 * 基于命局特征生成个性化建议，包括事业、财运、健康等多维度指导
 */
class EnhancedAdviceGenerator {
  constructor() {
    this.initializeAdviceDatabase();
  }

  /**
   * 初始化建议数据库
   */
  initializeAdviceDatabase() {
    // 事业建议数据库
    this.careerAdvice = {
      // 基于十神的事业建议
      ten_gods: {
        '正官': {
          suitable_careers: ['公务员', '管理层', '法律', '教育', '医疗'],
          development_advice: '适合在体制内发展，注重规范和秩序，稳步上升',
          leadership_style: '威严正直，以身作则，注重制度建设',
          timing_advice: '官星得力时适合求职升迁，官星受制时宜低调行事'
        },
        '七杀': {
          suitable_careers: ['军警', '竞技体育', '销售', '创业', '技术专家'],
          development_advice: '适合竞争激烈的行业，敢于挑战，勇于开拓',
          leadership_style: '果断决策，雷厉风行，善于处理危机',
          timing_advice: '七杀有制时大展拳脚，七杀无制时需要约束'
        },
        '正财': {
          suitable_careers: ['金融', '会计', '贸易', '房地产', '稳定收入行业'],
          development_advice: '注重财务管理，稳健投资，积累财富',
          leadership_style: '务实稳重，善于理财，注重成本控制',
          timing_advice: '财星透干时财运亨通，财星被劫时需防破财'
        },
        '偏财': {
          suitable_careers: ['投资', '娱乐业', '服务业', '流动性强的行业'],
          development_advice: '善于把握机遇，灵活应变，多元化发展',
          leadership_style: '善于交际，人脉广泛，注重合作共赢',
          timing_advice: '偏财得力时适合投资创业，偏财受制时宜保守理财'
        },
        '食神': {
          suitable_careers: ['文艺创作', '美食', '教育培训', '儿童相关', '服务业'],
          development_advice: '发挥创造才能，注重品质和口碑，温和发展',
          leadership_style: '亲和力强，善于沟通，注重团队和谐',
          timing_advice: '食神旺相时创意无限，食神被制时需要调整方向'
        },
        '伤官': {
          suitable_careers: ['艺术设计', '技术创新', '媒体传播', '自由职业'],
          development_advice: '发挥创新能力，敢于突破传统，个性化发展',
          leadership_style: '思维敏捷，善于创新，但需要控制情绪',
          timing_advice: '伤官配印时才华横溢，伤官见官时需要谨慎'
        },
        '正印': {
          suitable_careers: ['教育', '学术研究', '文化传承', '宗教', '慈善'],
          development_advice: '注重学习和积累，发挥智慧和经验，稳步发展',
          leadership_style: '博学深沉，善于指导，注重传承和培养',
          timing_advice: '印星得力时学业事业俱佳，印星过旺时需要行动力'
        },
        '偏印': {
          suitable_careers: ['技术研发', '神秘学', '医学', '心理学', '特殊技能'],
          development_advice: '发挥专业技能，走专业化道路，独特发展',
          leadership_style: '深谋远虑，善于分析，但需要加强沟通',
          timing_advice: '偏印得用时专业突出，偏印夺食时需要调整'
        },
        '比肩': {
          suitable_careers: ['合伙经营', '团队协作', '体育运动', '同行业竞争'],
          development_advice: '善于合作，注重团队，但需要明确分工',
          leadership_style: '平等合作，善于协调，但需要避免内耗',
          timing_advice: '比肩得力时合作顺利，比肩过旺时需要独立'
        },
        '劫财': {
          suitable_careers: ['销售', '投机', '快速变化的行业', '个人技能'],
          development_advice: '行动力强，敢于冒险，但需要控制风险',
          leadership_style: '积极进取，敢于竞争，但需要团队意识',
          timing_advice: '劫财有制时能力突出，劫财无制时需要约束'
        }
      },

      // 基于五行的事业建议
      wuxing: {
        '木': {
          industries: ['林业', '纸业', '家具', '教育', '文化', '医药'],
          development_direction: '向东方发展，春季运势较好',
          color_advice: '多用绿色、青色，有助事业发展',
          number_advice: '幸运数字：3、8'
        },
        '火': {
          industries: ['能源', '电子', '餐饮', '娱乐', '广告', '化工'],
          development_direction: '向南方发展，夏季运势较好',
          color_advice: '多用红色、紫色，有助事业发展',
          number_advice: '幸运数字：2、7'
        },
        '土': {
          industries: ['房地产', '建筑', '农业', '陶瓷', '中介', '保险'],
          development_direction: '本地发展为宜，四季末月运势较好',
          color_advice: '多用黄色、棕色，有助事业发展',
          number_advice: '幸运数字：5、10'
        },
        '金': {
          industries: ['金融', '机械', '汽车', '珠宝', '五金', '军工'],
          development_direction: '向西方发展，秋季运势较好',
          color_advice: '多用白色、金色，有助事业发展',
          number_advice: '幸运数字：4、9'
        },
        '水': {
          industries: ['航运', '水产', '饮料', '清洁', '运输', '旅游'],
          development_direction: '向北方发展，冬季运势较好',
          color_advice: '多用黑色、蓝色，有助事业发展',
          number_advice: '幸运数字：1、6'
        }
      }
    };

    // 财运建议数据库
    this.wealthAdvice = {
      // 基于财星的理财建议
      wealth_stars: {
        '正财强': {
          investment_style: '稳健型投资，注重长期收益',
          suitable_investments: ['定期存款', '国债', '蓝筹股', '房地产'],
          risk_level: '低风险',
          timing_advice: '财星得力时适合大额投资，财星受制时宜保守理财'
        },
        '偏财强': {
          investment_style: '积极型投资，善于把握机遇',
          suitable_investments: ['股票', '基金', '期货', '创业投资'],
          risk_level: '中高风险',
          timing_advice: '偏财旺相时适合投机，偏财受制时需要谨慎'
        },
        '财弱': {
          investment_style: '保守型投资，注重资金安全',
          suitable_investments: ['储蓄', '货币基金', '保险', '稳定收益产品'],
          risk_level: '极低风险',
          timing_advice: '财星得生时可适度投资，财星被劫时严防破财'
        },
        '财多身弱': {
          investment_style: '分散投资，避免集中风险',
          suitable_investments: ['多元化组合', '专业理财', '委托投资'],
          risk_level: '中等风险',
          timing_advice: '比劫运时适合投资，官杀运时需要谨慎'
        }
      },

      // 基于用神的财运建议
      yongshen_wealth: {
        '用神为财': {
          wealth_potential: '财运天赋较强，容易积累财富',
          development_advice: '主动出击，积极理财，把握财运机会',
          timing_strategy: '用神得力年份大胆投资，用神受制年份保守理财'
        },
        '用神生财': {
          wealth_potential: '通过专业技能获得财富',
          development_advice: '发挥专长，技能变现，稳步积累',
          timing_strategy: '用神旺相时技能价值高，适合提升收入'
        },
        '用神制财': {
          wealth_potential: '需要控制欲望，理性理财',
          development_advice: '避免贪心，稳健投资，防范风险',
          timing_strategy: '用神得力时控制力强，适合制定理财计划'
        }
      }
    };

    // 健康建议数据库
    this.healthAdvice = {
      // 基于五行的健康建议
      wuxing_health: {
        '木': {
          organs: ['肝', '胆', '眼睛', '筋骨'],
          common_issues: ['肝胆疾病', '眼疾', '筋骨问题', '情绪波动'],
          prevention_advice: '保持心情舒畅，适度运动，注意眼部保健',
          diet_advice: '多吃绿色蔬菜，少食辛辣，适量酸味食物',
          exercise_advice: '太极拳、瑜伽、散步等柔和运动'
        },
        '火': {
          organs: ['心', '小肠', '血管', '舌'],
          common_issues: ['心血管疾病', '失眠', '血压问题', '口舌疾病'],
          prevention_advice: '保持心态平和，避免过度兴奋，注意心血管保健',
          diet_advice: '清淡饮食，多吃红色食物，少食油腻',
          exercise_advice: '有氧运动，但避免过度激烈'
        },
        '土': {
          organs: ['脾', '胃', '肌肉', '口'],
          common_issues: ['消化系统疾病', '肌肉问题', '口腔疾病'],
          prevention_advice: '规律饮食，适度运动，注意脾胃保养',
          diet_advice: '温和食物，黄色食物，避免生冷',
          exercise_advice: '适度力量训练，增强肌肉'
        },
        '金': {
          organs: ['肺', '大肠', '皮肤', '鼻'],
          common_issues: ['呼吸系统疾病', '皮肤问题', '鼻炎'],
          prevention_advice: '注意呼吸道保健，避免干燥环境',
          diet_advice: '润肺食物，白色食物，少食辛辣',
          exercise_advice: '呼吸训练，游泳等有氧运动'
        },
        '水': {
          organs: ['肾', '膀胱', '骨骼', '耳'],
          common_issues: ['肾脏疾病', '骨骼问题', '听力问题', '生殖系统'],
          prevention_advice: '注意保暖，避免过度劳累，保护肾脏',
          diet_advice: '黑色食物，温补食物，少食寒凉',
          exercise_advice: '适度运动，避免过度消耗'
        }
      },

      // 基于命局强弱的健康建议
      bazi_strength: {
        '身强': {
          health_tendency: '体质较好，但容易上火',
          attention_points: '控制情绪，避免过度亢奋',
          lifestyle_advice: '适度运动，清淡饮食，保持平和心态'
        },
        '身弱': {
          health_tendency: '体质偏弱，容易疲劳',
          attention_points: '注意休息，避免过度劳累',
          lifestyle_advice: '加强营养，适度锻炼，充足睡眠'
        },
        '从强': {
          health_tendency: '体质特殊，需要特别调理',
          attention_points: '顺应天性，不要强求改变',
          lifestyle_advice: '个性化调理，专业指导'
        },
        '从弱': {
          health_tendency: '适应性强，但需要外界支持',
          attention_points: '依靠环境和他人，不要独自承担',
          lifestyle_advice: '团体活动，社交支持，环境调节'
        }
      }
    };

    // 人际关系建议数据库
    this.relationshipAdvice = {
      // 基于十神的人际关系建议
      ten_gods_relationship: {
        '官杀强': {
          leadership_style: '权威型领导，注重规则和秩序',
          team_management: '严格管理，明确分工，奖惩分明',
          conflict_resolution: '依法依规，公正处理，维护权威',
          communication_style: '正式严肃，条理清晰，威严有度'
        },
        '财星强': {
          leadership_style: '务实型领导，注重效益和结果',
          team_management: '目标导向，激励机制，成果考核',
          conflict_resolution: '利益平衡，协商解决，双赢思维',
          communication_style: '直接明确，注重实效，善于谈判'
        },
        '食伤强': {
          leadership_style: '创新型领导，注重创意和表达',
          team_management: '开放包容，鼓励创新，灵活管理',
          conflict_resolution: '沟通协调，创意解决，情理并重',
          communication_style: '生动活泼，富有感染力，善于表达'
        },
        '印星强': {
          leadership_style: '智慧型领导，注重学习和传承',
          team_management: '教导培养，知识分享，文化建设',
          conflict_resolution: '深度分析，智慧化解，长远考虑',
          communication_style: '深入浅出，循循善诱，富有内涵'
        },
        '比劫强': {
          leadership_style: '合作型领导，注重团队和平等',
          team_management: '民主决策，团队协作，共同承担',
          conflict_resolution: '平等协商，集体决策，团结一致',
          communication_style: '平易近人，善于倾听，注重共识'
        }
      }
    };

    // 时间建议数据库
    this.timingAdvice = {
      // 基于大运的时间建议
      dayun_timing: {
        '大运初期': {
          strategy: '谨慎观察，稳步推进',
          suitable_actions: ['学习适应', '建立基础', '观察环境'],
          avoid_actions: ['重大决策', '冒险投资', '急于求成']
        },
        '大运中期': {
          strategy: '积极进取，把握机遇',
          suitable_actions: ['重要决策', '扩大发展', '主动出击'],
          avoid_actions: ['过度保守', '错失机会', '犹豫不决']
        },
        '大运后期': {
          strategy: '总结收获，准备转换',
          suitable_actions: ['总结经验', '稳固成果', '准备变化'],
          avoid_actions: ['盲目扩张', '重大变动', '固步自封']
        }
      },

      // 基于流年的时间建议
      liunian_timing: {
        '用神得力年': {
          strategy: '主动出击，把握机遇',
          suitable_actions: ['求职升迁', '投资创业', '重要决策', '结婚生子'],
          timing_details: '全年运势较好，特别是用神当令的月份'
        },
        '忌神当值年': {
          strategy: '谨慎保守，避免风险',
          suitable_actions: ['学习充电', '内部整顿', '保守理财', '健康保养'],
          timing_details: '全年需要谨慎，特别是忌神当令的月份'
        },
        '刑冲年': {
          strategy: '灵活应变，化解冲突',
          suitable_actions: ['调整策略', '化解矛盾', '适应变化', '寻求支持'],
          timing_details: '变动较大的年份，需要特别注意刑冲的月份'
        },
        '合化年': {
          strategy: '合作发展，借力打力',
          suitable_actions: ['寻求合作', '建立联盟', '借助外力', '和谐发展'],
          timing_details: '合作运较好，适合建立重要关系'
        }
      }
    };
  }

  /**
   * 🎯 执行增强分析（统一入口方法）
   * @param {Object} bazi - 八字数据
   * @param {Object} options - 分析选项
   * @returns {Object} 增强分析结果
   */
  executeEnhancedAnalysis(bazi, options = {}) {
    try {
      console.log('🎯 开始执行增强分析...');

      // 🔧 数据验证和默认值设置
      if (!bazi || (!bazi.day || !bazi.day.gan)) {
        console.warn('⚠️ 八字数据不完整，使用默认分析');
        return this.createDefaultAnalysisResult();
      }

      // 🔧 构建分析参数
      const analysisParams = {
        pattern: options.pattern || this.analyzeBasicPattern(bazi),
        yongshen: options.yongshen || this.analyzeBasicYongshen(bazi),
        dynamicAnalysis: options.dynamicAnalysis || {},
        personalInfo: options.personalInfo || this.extractPersonalInfo(bazi)
      };

      // 🎯 执行综合建议生成
      const comprehensiveAdvice = this.generateComprehensiveAdvice(
        bazi,
        analysisParams.pattern,
        analysisParams.yongshen,
        analysisParams.dynamicAnalysis,
        analysisParams.personalInfo
      );

      // 🔧 构建标准化返回结果
      const enhancedResult = {
        analysis_mode: 'enhanced_professional',
        timestamp: new Date().toISOString(),
        bazi_summary: {
          day_master: bazi.day.gan,
          gender: bazi.gender || '未知'
        },
        ability_tendencies: this.analyzeAbilityTendencies(bazi, analysisParams.pattern),
        health_guidance: this.generateHealthGuidance(bazi, analysisParams.yongshen),
        relationship_guidance: comprehensiveAdvice.relationship_advice || {},
        career_guidance: comprehensiveAdvice.career_advice || {},
        wealth_guidance: comprehensiveAdvice.wealth_advice || {},
        timing_guidance: comprehensiveAdvice.timing_advice || {},
        comprehensive_advice: comprehensiveAdvice
      };

      console.log('✅ 增强分析执行完成');
      return enhancedResult;

    } catch (error) {
      console.error('❌ 增强分析执行失败:', error);
      return this.createErrorAnalysisResult(error);
    }
  }

  /**
   * 🔧 创建默认分析结果
   */
  createDefaultAnalysisResult() {
    return {
      analysis_mode: 'enhanced_professional',
      timestamp: new Date().toISOString(),
      bazi_summary: { day_master: '甲', gender: '未知' },
      ability_tendencies: { creativity: { level: '中等' }, leadership: { level: '中等' } },
      health_guidance: { health_risks: ['注意作息'], preventive_measures: ['适量运动'] },
      relationship_guidance: { social_advice: ['保持真诚'] },
      career_guidance: { suitable_careers: ['多元发展'] },
      wealth_guidance: { investment_strategy: '稳健投资' },
      timing_guidance: { current_period: '平稳期' },
      comprehensive_advice: { status: '使用默认建议' }
    };
  }

  /**
   * 🔧 创建错误分析结果
   */
  createErrorAnalysisResult(error) {
    return {
      analysis_mode: 'enhanced_professional',
      timestamp: new Date().toISOString(),
      error: true,
      error_message: error.message,
      bazi_summary: { day_master: '未知', gender: '未知' },
      ability_tendencies: { error: '分析失败' },
      health_guidance: { error: '分析失败' },
      relationship_guidance: { error: '分析失败' },
      comprehensive_advice: { error: '增强分析执行失败' }
    };
  }

  /**
   * 🔧 分析基础格局
   */
  analyzeBasicPattern(bazi) {
    return {
      pattern_type: '普通格局',
      strength: '中等',
      description: '基础格局分析'
    };
  }

  /**
   * 🔧 分析基础用神
   */
  analyzeBasicYongshen(bazi) {
    return {
      primary_yongshen: '木',
      secondary_yongshen: '水',
      avoid_elements: ['金'],
      description: '基础用神分析'
    };
  }

  /**
   * 🔧 提取个人信息
   */
  extractPersonalInfo(bazi) {
    return {
      gender: bazi.gender || bazi.birth_info?.gender || '未知',
      age: bazi.birth_info ? new Date().getFullYear() - bazi.birth_info.year : 30,
      location: '未知'
    };
  }

  /**
   * 🔧 分析能力倾向
   */
  analyzeAbilityTendencies(bazi, pattern) {
    try {
      const dayMaster = bazi.day?.gan || bazi.day_master || '甲';

      // 基于日主的基础能力分析
      const abilityMapping = {
        '甲': { creativity: '较强', leadership: '中等', communication: '良好', analytical: '中等' },
        '乙': { creativity: '很强', leadership: '较弱', communication: '很好', analytical: '较强' },
        '丙': { creativity: '很强', leadership: '较强', communication: '很好', analytical: '中等' },
        '丁': { creativity: '较强', leadership: '中等', communication: '良好', analytical: '较强' },
        '戊': { creativity: '中等', leadership: '较强', communication: '中等', analytical: '较强' },
        '己': { creativity: '中等', leadership: '中等', communication: '良好', analytical: '很强' },
        '庚': { creativity: '较弱', leadership: '很强', communication: '中等', analytical: '较强' },
        '辛': { creativity: '较强', leadership: '较强', communication: '良好', analytical: '很强' },
        '壬': { creativity: '很强', leadership: '中等', communication: '很好', analytical: '中等' },
        '癸': { creativity: '较强', leadership: '较弱', communication: '良好', analytical: '很强' }
      };

      const baseAbilities = abilityMapping[dayMaster] || abilityMapping['甲'];

      // 基于格局的能力调整
      if (pattern && pattern.pattern_type) {
        if (pattern.pattern_type.includes('官')) {
          baseAbilities.leadership = '很强';
          baseAbilities.analytical = '很强';
        } else if (pattern.pattern_type.includes('财')) {
          baseAbilities.analytical = '很强';
          baseAbilities.communication = '很好';
        } else if (pattern.pattern_type.includes('食伤')) {
          baseAbilities.creativity = '很强';
          baseAbilities.communication = '很好';
        }
      }

      return {
        creativity: { level: baseAbilities.creativity, description: '创造力和想象力' },
        leadership: { level: baseAbilities.leadership, description: '领导力和管理能力' },
        communication: { level: baseAbilities.communication, description: '沟通表达能力' },
        analytical: { level: baseAbilities.analytical, description: '分析思考能力' }
      };

    } catch (error) {
      console.error('❌ 能力倾向分析失败:', error);
      return {
        creativity: { level: '中等', description: '创造力和想象力' },
        leadership: { level: '中等', description: '领导力和管理能力' },
        communication: { level: '中等', description: '沟通表达能力' },
        analytical: { level: '中等', description: '分析思考能力' }
      };
    }
  }

  /**
   * 🔧 生成健康指导
   */
  generateHealthGuidance(bazi, yongshen) {
    try {
      const dayMaster = bazi.day?.gan || bazi.day_master || '甲';

      // 基于日主的健康风险分析
      const healthMapping = {
        '甲': { risks: ['肝胆疾病', '筋骨问题'], organs: ['肝', '胆', '筋骨'] },
        '乙': { risks: ['肝胆疾病', '颈椎问题'], organs: ['肝', '胆', '颈椎'] },
        '丙': { risks: ['心血管疾病', '眼部问题'], organs: ['心', '小肠', '眼'] },
        '丁': { risks: ['心血管疾病', '血液问题'], organs: ['心', '小肠', '血液'] },
        '戊': { risks: ['脾胃疾病', '肌肉问题'], organs: ['脾', '胃', '肌肉'] },
        '己': { risks: ['脾胃疾病', '皮肤问题'], organs: ['脾', '胃', '皮肤'] },
        '庚': { risks: ['肺部疾病', '骨骼问题'], organs: ['肺', '大肠', '骨骼'] },
        '辛': { risks: ['肺部疾病', '呼吸道问题'], organs: ['肺', '大肠', '呼吸道'] },
        '壬': { risks: ['肾脏疾病', '泌尿系统问题'], organs: ['肾', '膀胱', '泌尿系统'] },
        '癸': { risks: ['肾脏疾病', '生殖系统问题'], organs: ['肾', '膀胱', '生殖系统'] }
      };

      const healthInfo = healthMapping[dayMaster] || healthMapping['甲'];

      // 基于用神的健康建议
      let preventiveMeasures = ['规律作息', '适量运动', '均衡饮食'];

      if (yongshen && yongshen.primary_yongshen) {
        const yongshenElement = yongshen.primary_yongshen;
        const elementAdvice = {
          '木': ['多接触绿色植物', '适合晨练', '注意肝胆保养'],
          '火': ['保持乐观心态', '适度晒太阳', '注意心血管健康'],
          '土': ['注意脾胃调理', '规律饮食', '适合瑜伽运动'],
          '金': ['注意呼吸道保养', '适合户外运动', '保持室内空气清新'],
          '水': ['注意肾脏保养', '多喝水', '避免过度劳累']
        };

        if (elementAdvice[yongshenElement]) {
          preventiveMeasures = preventiveMeasures.concat(elementAdvice[yongshenElement]);
        }
      }

      return {
        health_risks: healthInfo.risks,
        key_organs: healthInfo.organs,
        preventive_measures: preventiveMeasures,
        lifestyle_advice: ['保持心情愉悦', '避免过度压力', '定期体检'],
        seasonal_care: '根据季节调整养生方式'
      };

    } catch (error) {
      console.error('❌ 健康指导生成失败:', error);
      return {
        health_risks: ['注意作息'],
        key_organs: ['全身调理'],
        preventive_measures: ['适量运动', '均衡饮食'],
        lifestyle_advice: ['保持心情愉悦'],
        seasonal_care: '四季养生'
      };
    }
  }

  /**
   * 主要建议生成算法
   * @param {Object} bazi - 八字信息
   * @param {Object} pattern - 格局信息
   * @param {Object} yongshen - 用神信息
   * @param {Object} dynamicAnalysis - 动态分析结果
   * @param {Object} personalInfo - 个人信息
   * @returns {Object} 综合建议结果
   */
  generateComprehensiveAdvice(bazi, pattern, yongshen, dynamicAnalysis, personalInfo) {
    try {
      console.log('🎯 开始生成专业建议');

      // 1. 事业建议
      const careerAdvice = this.generateCareerAdvice(bazi, pattern, yongshen, personalInfo);
      console.log('💼 事业建议生成完成');

      // 2. 财运建议
      const wealthAdvice = this.generateWealthAdvice(bazi, yongshen, dynamicAnalysis);
      console.log('💰 财运建议生成完成');

      // 3. 健康建议
      const healthAdvice = this.generateHealthAdvice(bazi, personalInfo);
      console.log('🏥 健康建议生成完成');

      // 4. 人际关系建议
      const relationshipAdvice = this.generateRelationshipAdvice(bazi, pattern, personalInfo);
      console.log('👥 人际关系建议生成完成');

      // 5. 时间规划建议
      const timingAdvice = this.generateTimingAdvice(dynamicAnalysis, yongshen);
      console.log('⏰ 时间规划建议生成完成');

      // 6. 个性化建议
      const personalizedAdvice = this.generatePersonalizedAdvice(bazi, pattern, yongshen, personalInfo);
      console.log('🎨 个性化建议生成完成');

      // 7. 综合评估和优先级排序
      const prioritizedAdvice = this.prioritizeAdvice(careerAdvice, wealthAdvice, healthAdvice, relationshipAdvice, timingAdvice, personalizedAdvice);
      console.log('📊 建议优先级排序完成');

      return {
        career: careerAdvice,
        wealth: wealthAdvice,
        health: healthAdvice,
        relationship: relationshipAdvice,
        timing: timingAdvice,
        personalized: personalizedAdvice,
        prioritized: prioritizedAdvice,
        generation_timestamp: new Date().toISOString(),
        confidence: this.calculateAdviceConfidence(bazi, pattern, yongshen, dynamicAnalysis)
      };

    } catch (error) {
      console.error('❌ 建议生成失败:', error);
      return {
        career: { error: '事业建议生成失败' },
        wealth: { error: '财运建议生成失败' },
        health: { error: '健康建议生成失败' },
        relationship: { error: '人际关系建议生成失败' },
        timing: { error: '时间规划建议生成失败' },
        personalized: { error: '个性化建议生成失败' },
        prioritized: [],
        generation_timestamp: new Date().toISOString(),
        confidence: 0.3,
        error: error.message
      };
    }
  }

  /**
   * 生成事业建议
   */
  generateCareerAdvice(bazi, pattern, yongshen, personalInfo) {
    const advice = {
      suitable_careers: [],
      development_strategy: '',
      leadership_style: '',
      timing_guidance: '',
      industry_recommendations: [],
      skill_development: [],
      career_planning: {}
    };

    try {
      // 基于主要十神分析事业方向
      const mainTenGod = this.getMainTenGod(bazi);
      if (mainTenGod && this.careerAdvice.ten_gods[mainTenGod]) {
        const tenGodAdvice = this.careerAdvice.ten_gods[mainTenGod];
        advice.suitable_careers = tenGodAdvice.suitable_careers;
        advice.development_strategy = tenGodAdvice.development_advice;
        advice.leadership_style = tenGodAdvice.leadership_style;
        advice.timing_guidance = tenGodAdvice.timing_advice;
      }

      // 基于用神五行推荐行业
      if (yongshen && yongshen.yongshen) {
        const yongshenElement = yongshen.yongshen;
        if (this.careerAdvice.wuxing[yongshenElement]) {
          const wuxingAdvice = this.careerAdvice.wuxing[yongshenElement];
          advice.industry_recommendations = wuxingAdvice.industries;
          advice.career_planning.direction = wuxingAdvice.development_direction;
          advice.career_planning.favorable_colors = wuxingAdvice.color_advice;
          advice.career_planning.lucky_numbers = wuxingAdvice.number_advice;
        }
      }

      // 基于格局特点调整建议
      if (pattern && pattern.pattern_type) {
        advice.skill_development = this.getSkillDevelopmentAdvice(pattern.pattern_type);
        advice.career_planning.pattern_guidance = this.getPatternCareerGuidance(pattern.pattern_type);
      }

      // 基于年龄阶段调整建议
      const age = personalInfo.age || 30;
      advice.career_planning.age_specific = this.getAgeSpecificCareerAdvice(age);

      // 基于性别调整建议
      if (personalInfo.gender) {
        advice.career_planning.gender_considerations = this.getGenderCareerAdvice(personalInfo.gender, bazi);
      }

      return advice;

    } catch (error) {
      console.error('事业建议生成错误:', error);
      return { error: '事业建议生成失败', details: error.message };
    }
  }

  /**
   * 生成财运建议
   */
  generateWealthAdvice(bazi, yongshen, dynamicAnalysis) {
    const advice = {
      investment_strategy: '',
      suitable_investments: [],
      risk_assessment: '',
      timing_strategy: '',
      wealth_development: {},
      financial_planning: {}
    };

    try {
      // 分析财星强弱
      const wealthStrength = this.analyzeWealthStrength(bazi);
      if (this.wealthAdvice.wealth_stars[wealthStrength]) {
        const wealthAdviceData = this.wealthAdvice.wealth_stars[wealthStrength];
        advice.investment_strategy = wealthAdviceData.investment_style;
        advice.suitable_investments = wealthAdviceData.suitable_investments;
        advice.risk_assessment = wealthAdviceData.risk_level;
        advice.timing_strategy = wealthAdviceData.timing_advice;
      }

      // 基于用神与财星关系分析
      if (yongshen) {
        const yongshenWealthRelation = this.analyzeYongshenWealthRelation(yongshen);
        if (this.wealthAdvice.yongshen_wealth[yongshenWealthRelation]) {
          const yongshenAdvice = this.wealthAdvice.yongshen_wealth[yongshenWealthRelation];
          advice.wealth_development.potential = yongshenAdvice.wealth_potential;
          advice.wealth_development.strategy = yongshenAdvice.development_advice;
          advice.wealth_development.timing = yongshenAdvice.timing_strategy;
        }
      }

      // 基于动态分析的财运时机
      if (dynamicAnalysis && dynamicAnalysis.liunian_analysis) {
        advice.financial_planning = this.generateFinancialTimingPlan(dynamicAnalysis.liunian_analysis);
      }

      // 风险控制建议
      advice.risk_control = this.generateRiskControlAdvice(bazi, yongshen);

      return advice;

    } catch (error) {
      console.error('财运建议生成错误:', error);
      return { error: '财运建议生成失败', details: error.message };
    }
  }

  /**
   * 生成健康建议
   */
  generateHealthAdvice(bazi, personalInfo) {
    const advice = {
      health_tendencies: [],
      prevention_advice: [],
      diet_recommendations: [],
      exercise_suggestions: [],
      lifestyle_guidance: {},
      seasonal_care: {}
    };

    try {
      // 基于五行分析健康倾向
      const wuxingHealth = this.analyzeWuxingHealth(bazi);
      wuxingHealth.forEach(element => {
        if (this.healthAdvice.wuxing_health[element]) {
          const healthData = this.healthAdvice.wuxing_health[element];
          advice.health_tendencies.push({
            element: element,
            organs: healthData.organs,
            common_issues: healthData.common_issues
          });
          advice.prevention_advice.push(healthData.prevention_advice);
          advice.diet_recommendations.push(healthData.diet_advice);
          advice.exercise_suggestions.push(healthData.exercise_advice);
        }
      });

      // 基于命局强弱分析体质
      const baziStrength = this.analyzeBaziStrength(bazi);
      if (this.healthAdvice.bazi_strength[baziStrength]) {
        const strengthAdvice = this.healthAdvice.bazi_strength[baziStrength];
        advice.lifestyle_guidance = {
          tendency: strengthAdvice.health_tendency,
          attention: strengthAdvice.attention_points,
          lifestyle: strengthAdvice.lifestyle_advice
        };
      }

      // 基于年龄和性别的健康建议
      advice.age_specific = this.getAgeSpecificHealthAdvice(personalInfo.age || 30);
      if (personalInfo.gender) {
        advice.gender_specific = this.getGenderSpecificHealthAdvice(personalInfo.gender);
      }

      // 季节性保健建议
      advice.seasonal_care = this.getSeasonalHealthAdvice(bazi);

      return advice;

    } catch (error) {
      console.error('健康建议生成错误:', error);
      return { error: '健康建议生成失败', details: error.message };
    }
  }

  /**
   * 生成人际关系建议
   */
  generateRelationshipAdvice(bazi, pattern, personalInfo) {
    const advice = {
      leadership_style: '',
      team_management: '',
      conflict_resolution: '',
      communication_style: '',
      relationship_patterns: {},
      social_strategies: []
    };

    try {
      // 基于主要十神分析人际关系模式
      const dominantTenGods = this.getDominantTenGods(bazi);
      const relationshipPattern = this.getRelationshipPattern(dominantTenGods);

      if (this.relationshipAdvice.ten_gods_relationship[relationshipPattern]) {
        const relationshipData = this.relationshipAdvice.ten_gods_relationship[relationshipPattern];
        advice.leadership_style = relationshipData.leadership_style;
        advice.team_management = relationshipData.team_management;
        advice.conflict_resolution = relationshipData.conflict_resolution;
        advice.communication_style = relationshipData.communication_style;
      }

      // 基于格局分析社交策略
      if (pattern && pattern.pattern_type) {
        advice.social_strategies = this.getSocialStrategies(pattern.pattern_type);
      }

      // 基于性别和年龄的人际关系建议
      advice.relationship_patterns = this.getRelationshipPatterns(personalInfo, bazi);

      // 婚姻感情建议
      advice.marriage_advice = this.getMarriageAdvice(bazi, personalInfo);

      return advice;

    } catch (error) {
      console.error('人际关系建议生成错误:', error);
      return { error: '人际关系建议生成失败', details: error.message };
    }
  }

  /**
   * 生成时间规划建议
   */
  generateTimingAdvice(dynamicAnalysis, yongshen) {
    const advice = {
      current_period: {},
      short_term_planning: [],
      medium_term_planning: [],
      long_term_planning: [],
      key_timing_points: []
    };

    try {
      // 基于当前大运的时间建议
      if (dynamicAnalysis && dynamicAnalysis.dayun_analysis) {
        const currentDayun = dynamicAnalysis.dayun_analysis.current_dayun;
        if (currentDayun) {
          const dayunPhase = this.getDayunPhase(currentDayun.energy_curve.phase);
          if (this.timingAdvice.dayun_timing[dayunPhase]) {
            advice.current_period = this.timingAdvice.dayun_timing[dayunPhase];
          }
        }
      }

      // 基于流年分析的时间规划
      if (dynamicAnalysis && dynamicAnalysis.liunian_analysis) {
        advice.short_term_planning = this.generateShortTermPlanning(dynamicAnalysis.liunian_analysis, yongshen);
        advice.medium_term_planning = this.generateMediumTermPlanning(dynamicAnalysis.liunian_analysis);
      }

      // 基于转折点的关键时机
      if (dynamicAnalysis && dynamicAnalysis.turning_points) {
        advice.key_timing_points = this.generateKeyTimingPoints(dynamicAnalysis.turning_points);
      }

      // 长期规划建议
      if (dynamicAnalysis && dynamicAnalysis.dynamic_forecast) {
        advice.long_term_planning = this.generateLongTermPlanning(dynamicAnalysis.dynamic_forecast);
      }

      return advice;

    } catch (error) {
      console.error('时间规划建议生成错误:', error);
      return { error: '时间规划建议生成失败', details: error.message };
    }
  }

  /**
   * 生成个性化建议
   */
  generatePersonalizedAdvice(bazi, pattern, yongshen, personalInfo) {
    const advice = {
      personality_traits: [],
      development_potential: [],
      life_philosophy: '',
      personal_motto: '',
      growth_suggestions: [],
      balance_recommendations: []
    };

    try {
      // 基于命局特征分析性格特点
      advice.personality_traits = this.analyzePersonalityTraits(bazi, pattern);

      // 基于用神分析发展潜力
      if (yongshen) {
        advice.development_potential = this.analyzeDevelopmentPotential(yongshen, bazi);
      }

      // 基于格局生成人生哲学
      if (pattern && pattern.pattern_type) {
        advice.life_philosophy = this.generateLifePhilosophy(pattern.pattern_type);
        advice.personal_motto = this.generatePersonalMotto(pattern.pattern_type);
      }

      // 基于五行平衡生成成长建议
      advice.growth_suggestions = this.generateGrowthSuggestions(bazi);

      // 基于命局强弱生成平衡建议
      advice.balance_recommendations = this.generateBalanceRecommendations(bazi);

      return advice;

    } catch (error) {
      console.error('个性化建议生成错误:', error);
      return { error: '个性化建议生成失败', details: error.message };
    }
  }

  /**
   * 建议优先级排序
   */
  prioritizeAdvice(careerAdvice, wealthAdvice, healthAdvice, relationshipAdvice, timingAdvice, personalizedAdvice) {
    const prioritizedList = [];

    try {
      // 健康建议优先级最高
      if (healthAdvice && !healthAdvice.error) {
        prioritizedList.push({
          category: '健康保养',
          priority: 1,
          urgency: 'high',
          content: this.summarizeHealthAdvice(healthAdvice),
          action_items: this.extractHealthActionItems(healthAdvice)
        });
      }

      // 时间规划建议次之
      if (timingAdvice && !timingAdvice.error) {
        prioritizedList.push({
          category: '时机把握',
          priority: 2,
          urgency: 'high',
          content: this.summarizeTimingAdvice(timingAdvice),
          action_items: this.extractTimingActionItems(timingAdvice)
        });
      }

      // 事业发展建议
      if (careerAdvice && !careerAdvice.error) {
        prioritizedList.push({
          category: '事业发展',
          priority: 3,
          urgency: 'medium',
          content: this.summarizeCareerAdvice(careerAdvice),
          action_items: this.extractCareerActionItems(careerAdvice)
        });
      }

      // 财运理财建议
      if (wealthAdvice && !wealthAdvice.error) {
        prioritizedList.push({
          category: '财运理财',
          priority: 4,
          urgency: 'medium',
          content: this.summarizeWealthAdvice(wealthAdvice),
          action_items: this.extractWealthActionItems(wealthAdvice)
        });
      }

      // 人际关系建议
      if (relationshipAdvice && !relationshipAdvice.error) {
        prioritizedList.push({
          category: '人际关系',
          priority: 5,
          urgency: 'low',
          content: this.summarizeRelationshipAdvice(relationshipAdvice),
          action_items: this.extractRelationshipActionItems(relationshipAdvice)
        });
      }

      // 个人成长建议
      if (personalizedAdvice && !personalizedAdvice.error) {
        prioritizedList.push({
          category: '个人成长',
          priority: 6,
          urgency: 'low',
          content: this.summarizePersonalizedAdvice(personalizedAdvice),
          action_items: this.extractPersonalizedActionItems(personalizedAdvice)
        });
      }

      return prioritizedList;

    } catch (error) {
      console.error('建议优先级排序错误:', error);
      return [];
    }
  }

  /**
   * 辅助方法：获取主要十神
   */
  getMainTenGod(bazi) {
    if (!bazi.fourPillars || bazi.fourPillars.length < 3) {
      return null;
    }

    const dayGan = bazi.fourPillars[2].gan; // 日主
    const monthGan = bazi.fourPillars[1].gan; // 月干

    // 简化的十神关系映射
    const tenGodsMap = {
      '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
      '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
      '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
      '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
      '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
      '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
      '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
      '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
      '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
      '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
    };

    return tenGodsMap[dayGan] && tenGodsMap[dayGan][monthGan] || null;
  }

  /**
   * 辅助方法：分析财星强弱
   */
  analyzeWealthStrength(bazi) {
    // 简化的财星强弱分析
    if (!bazi.element_powers || !bazi.element_powers.percentages) {
      return '财弱';
    }

    const dayGan = bazi.fourPillars && bazi.fourPillars[2] ? bazi.fourPillars[2].gan : '甲';
    const dayElement = this.getElementFromGan(dayGan);

    // 财星是日主所克的五行
    const wealthElement = this.getWealthElement(dayElement);
    const wealthPercentage = bazi.element_powers.percentages[wealthElement] || 0;
    const dayPercentage = bazi.element_powers.percentages[dayElement] || 0;

    if (wealthPercentage > 25 && dayPercentage < 20) {
      return '财多身弱';
    } else if (wealthPercentage > 20) {
      return '正财强';
    } else if (wealthPercentage > 10) {
      return '偏财强';
    } else {
      return '财弱';
    }
  }

  /**
   * 辅助方法：获取天干对应五行
   */
  getElementFromGan(gan) {
    const ganElementMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };
    return ganElementMap[gan] || '木';
  }

  /**
   * 辅助方法：获取财星五行
   */
  getWealthElement(dayElement) {
    const wealthMap = {
      '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
    };
    return wealthMap[dayElement] || '土';
  }

  /**
   * 辅助方法：分析用神与财星关系
   */
  analyzeYongshenWealthRelation(yongshen) {
    if (!yongshen || !yongshen.yongshen) {
      return '用神为财';
    }

    // 简化的用神财星关系分析
    const yongshenElement = yongshen.yongshen;

    // 这里需要更复杂的逻辑来判断用神与财星的关系
    // 暂时返回基础分类
    return '用神为财';
  }

  /**
   * 辅助方法：分析五行健康倾向
   */
  analyzeWuxingHealth(bazi) {
    const healthElements = [];

    if (bazi.element_powers && bazi.element_powers.percentages) {
      // 找出偏强或偏弱的五行
      Object.keys(bazi.element_powers.percentages).forEach(element => {
        const percentage = bazi.element_powers.percentages[element];
        if (percentage > 30 || percentage < 10) {
          healthElements.push(element);
        }
      });
    }

    return healthElements.length > 0 ? healthElements : ['木', '火', '土', '金', '水'];
  }

  /**
   * 辅助方法：分析命局强弱
   */
  analyzeBaziStrength(bazi) {
    if (!bazi.element_powers || !bazi.element_powers.percentages) {
      return '身弱';
    }

    const dayGan = bazi.fourPillars && bazi.fourPillars[2] ? bazi.fourPillars[2].gan : '甲';
    const dayElement = this.getElementFromGan(dayGan);
    const dayPercentage = bazi.element_powers.percentages[dayElement] || 0;

    if (dayPercentage > 40) {
      return '身强';
    } else if (dayPercentage < 15) {
      return '身弱';
    } else if (dayPercentage > 60) {
      return '从强';
    } else if (dayPercentage < 10) {
      return '从弱';
    } else {
      return '身弱';
    }
  }

  /**
   * 计算建议置信度
   */
  calculateAdviceConfidence(bazi, pattern, yongshen, dynamicAnalysis) {
    let confidence = 0.6; // 基础置信度

    // 八字信息完整性
    if (bazi && bazi.fourPillars && bazi.fourPillars.length === 4) {
      confidence += 0.1;
    }

    // 格局分析完整性
    if (pattern && pattern.pattern_type) {
      confidence += 0.1;
    }

    // 用神分析完整性
    if (yongshen && yongshen.yongshen) {
      confidence += 0.1;
    }

    // 动态分析完整性
    if (dynamicAnalysis && dynamicAnalysis.dayun_analysis && dynamicAnalysis.liunian_analysis) {
      confidence += 0.1;
    }

    return Math.min(0.95, confidence);
  }

  /**
   * 辅助方法：生成技能发展建议
   */
  getSkillDevelopmentAdvice(patternType) {
    const skillMap = {
      '正格': ['专业技能深化', '管理能力培养', '沟通协调能力'],
      '从格': ['适应能力', '团队协作', '灵活应变'],
      '专旺格': ['专业技能精进', '领域深耕', '创新能力'],
      '化格': ['综合能力', '平衡发展', '多元技能']
    };
    return skillMap[patternType] || ['综合能力发展', '持续学习', '适应变化'];
  }

  /**
   * 辅助方法：获取格局事业指导
   */
  getPatternCareerGuidance(patternType) {
    const guidanceMap = {
      '正格': '适合传统行业和稳定发展路径，注重积累和渐进',
      '从格': '适合新兴行业和灵活发展模式，善于借势而为',
      '专旺格': '适合专业化发展，在特定领域做到极致',
      '化格': '适合多元化发展，平衡各方面能力'
    };
    return guidanceMap[patternType] || '根据个人特点选择合适的发展道路';
  }

  /**
   * 辅助方法：获取年龄特定事业建议
   */
  getAgeSpecificCareerAdvice(age) {
    if (age < 25) {
      return '学习积累阶段，注重基础技能培养和经验积累';
    } else if (age < 35) {
      return '快速发展阶段，积极进取，把握机遇，建立职业基础';
    } else if (age < 45) {
      return '成熟发展阶段，发挥经验优势，承担更多责任';
    } else if (age < 55) {
      return '稳定发展阶段，注重传承和指导，稳固地位';
    } else {
      return '智慧分享阶段，发挥经验和智慧，适度减负';
    }
  }

  /**
   * 辅助方法：获取性别事业建议
   */
  getGenderCareerAdvice(gender, bazi) {
    if (gender === '女') {
      return '注重工作与家庭的平衡，发挥细致和沟通优势';
    } else {
      return '注重事业发展和责任担当，发挥决策和执行优势';
    }
  }

  /**
   * 辅助方法：生成财务时机规划
   */
  generateFinancialTimingPlan(liuNianAnalysis) {
    const plan = {
      investment_years: [],
      conservative_years: [],
      planning_advice: ''
    };

    if (liuNianAnalysis && liuNianAnalysis.yearly_analysis) {
      liuNianAnalysis.yearly_analysis.forEach(year => {
        if (year.fortune_trend && year.fortune_trend.score > 0.7) {
          plan.investment_years.push({
            year: year.year,
            reason: '运势较好，适合投资',
            score: year.fortune_trend.score
          });
        } else if (year.fortune_trend && year.fortune_trend.score < 0.4) {
          plan.conservative_years.push({
            year: year.year,
            reason: '运势较差，宜保守理财',
            score: year.fortune_trend.score
          });
        }
      });
    }

    plan.planning_advice = `未来${plan.investment_years.length}年适合投资，${plan.conservative_years.length}年需要保守`;
    return plan;
  }

  /**
   * 辅助方法：生成风险控制建议
   */
  generateRiskControlAdvice(bazi, yongshen) {
    const advice = {
      risk_tolerance: 'medium',
      control_measures: [],
      warning_signs: []
    };

    // 基于命局强弱判断风险承受能力
    const strength = this.analyzeBaziStrength(bazi);
    if (strength === '身强') {
      advice.risk_tolerance = 'high';
      advice.control_measures.push('可以承担较高风险，但需要分散投资');
    } else if (strength === '身弱') {
      advice.risk_tolerance = 'low';
      advice.control_measures.push('风险承受能力较低，应以稳健为主');
    }

    // 基于用神添加控制措施
    if (yongshen && yongshen.jishen) {
      advice.warning_signs.push('忌神当值时需要特别谨慎');
    }

    return advice;
  }

  /**
   * 辅助方法：获取年龄特定健康建议
   */
  getAgeSpecificHealthAdvice(age) {
    if (age < 30) {
      return '年轻阶段注重体质建设，养成良好生活习惯';
    } else if (age < 50) {
      return '中年阶段注重预防保健，定期体检，控制压力';
    } else {
      return '中老年阶段注重慢性病预防，保持适度运动';
    }
  }

  /**
   * 辅助方法：获取性别特定健康建议
   */
  getGenderSpecificHealthAdvice(gender) {
    if (gender === '女') {
      return '注重妇科保健，关注内分泌平衡，重视美容养颜';
    } else {
      return '注重心血管健康，关注前列腺保健，重视压力管理';
    }
  }

  /**
   * 辅助方法：获取季节性健康建议
   */
  getSeasonalHealthAdvice(bazi) {
    const advice = {
      spring: '春季养肝，多食绿色蔬菜，适度运动',
      summer: '夏季养心，清淡饮食，避免过度出汗',
      autumn: '秋季养肺，润燥防干，注意呼吸道保健',
      winter: '冬季养肾，温补为主，注意保暖'
    };

    // 可以根据命局特点调整季节建议
    return advice;
  }

  /**
   * 辅助方法：获取主导十神
   */
  getDominantTenGods(bazi) {
    // 简化实现，返回主要的十神类型
    const mainTenGod = this.getMainTenGod(bazi);
    if (mainTenGod) {
      if (mainTenGod.includes('官')) return '官杀强';
      if (mainTenGod.includes('财')) return '财星强';
      if (mainTenGod.includes('食') || mainTenGod.includes('伤')) return '食伤强';
      if (mainTenGod.includes('印')) return '印星强';
      if (mainTenGod.includes('比') || mainTenGod.includes('劫')) return '比劫强';
    }
    return '平衡型';
  }

  /**
   * 辅助方法：获取人际关系模式
   */
  getRelationshipPattern(dominantTenGods) {
    return dominantTenGods;
  }

  /**
   * 辅助方法：获取社交策略
   */
  getSocialStrategies(patternType) {
    const strategies = {
      '正格': ['稳重交往', '注重信誉', '长期关系'],
      '从格': ['灵活应变', '借势发展', '广泛交际'],
      '专旺格': ['专业交流', '深度合作', '精英圈子'],
      '化格': ['平衡发展', '多元交往', '和谐共处']
    };
    return strategies[patternType] || ['真诚待人', '互利共赢', '长期发展'];
  }

  /**
   * 辅助方法：获取人际关系模式
   */
  getRelationshipPatterns(personalInfo, bazi) {
    const patterns = {
      communication_style: '根据命局特点调整沟通方式',
      social_preferences: '适合的社交环境和方式',
      conflict_handling: '处理冲突的建议方法'
    };

    // 可以根据个人信息和命局特点进一步细化
    return patterns;
  }

  /**
   * 辅助方法：获取婚姻建议
   */
  getMarriageAdvice(bazi, personalInfo) {
    const advice = {
      marriage_timing: '适合结婚的时间段',
      partner_characteristics: '适合的伴侣特征',
      relationship_advice: '维护感情的建议'
    };

    // 基于命局分析婚姻状况
    if (personalInfo.age && personalInfo.age > 25) {
      advice.marriage_timing = '已到适婚年龄，可考虑婚姻大事';
    }

    return advice;
  }

  /**
   * 辅助方法：获取大运阶段
   */
  getDayunPhase(phase) {
    const phaseMap = {
      '初入': '大运初期',
      '渐强': '大运初期',
      '上升': '大运中期',
      '鼎盛': '大运中期',
      '巅峰': '大运中期',
      '稳定': '大运中期',
      '下降': '大运后期',
      '衰退': '大运后期',
      '末期': '大运后期'
    };
    return phaseMap[phase] || '大运中期';
  }

  /**
   * 建议总结方法（简化实现）
   */
  summarizeHealthAdvice(healthAdvice) {
    return '根据命局分析，需要注意相关脏腑保健，保持良好生活习惯';
  }

  summarizeTimingAdvice(timingAdvice) {
    return '把握当前时机，合理规划未来发展节奏';
  }

  summarizeCareerAdvice(careerAdvice) {
    return '发挥个人优势，选择合适的事业发展方向';
  }

  summarizeWealthAdvice(wealthAdvice) {
    return '根据财运特点，制定合适的理财投资策略';
  }

  summarizeRelationshipAdvice(relationshipAdvice) {
    return '改善人际关系，提升沟通协调能力';
  }

  summarizePersonalizedAdvice(personalizedAdvice) {
    return '认识自我特点，实现个人成长和平衡发展';
  }

  /**
   * 行动项提取方法（简化实现）
   */
  extractHealthActionItems(healthAdvice) {
    return ['定期体检', '合理饮食', '适度运动', '充足睡眠'];
  }

  extractTimingActionItems(timingAdvice) {
    return ['把握当前机遇', '制定阶段计划', '关注关键时点'];
  }

  extractCareerActionItems(careerAdvice) {
    return ['技能提升', '职业规划', '网络建设', '机会把握'];
  }

  extractWealthActionItems(wealthAdvice) {
    return ['制定预算', '投资规划', '风险控制', '收入增长'];
  }

  extractRelationshipActionItems(relationshipAdvice) {
    return ['沟通改善', '关系维护', '冲突化解', '团队合作'];
  }

  extractPersonalizedActionItems(personalizedAdvice) {
    return ['自我认知', '能力发展', '平衡调节', '目标设定'];
  }

  // 其他辅助方法的简化实现
  analyzePersonalityTraits(bazi, pattern) {
    return ['根据命局分析的性格特点'];
  }

  analyzeDevelopmentPotential(yongshen, bazi) {
    return ['基于用神分析的发展潜力'];
  }

  generateLifePhilosophy(patternType) {
    return '根据格局特点形成的人生哲学';
  }

  generatePersonalMotto(patternType) {
    return '适合的人生座右铭';
  }

  generateGrowthSuggestions(bazi) {
    return ['基于五行平衡的成长建议'];
  }

  generateBalanceRecommendations(bazi) {
    return ['基于命局强弱的平衡建议'];
  }

  generateShortTermPlanning(liuNianAnalysis, yongshen) {
    return ['短期规划建议'];
  }

  generateMediumTermPlanning(liuNianAnalysis) {
    return ['中期规划建议'];
  }

  generateKeyTimingPoints(turningPoints) {
    return turningPoints.map(tp => ({
      timing: tp.timing,
      type: tp.type,
      advice: tp.advice
    }));
  }

  generateLongTermPlanning(dynamicForecast) {
    return ['长期规划建议'];
  }

  /**
   * 个性化深度分析 - 扩展功能
   */

  /**
   * 性格特征深度分析
   */
  analyzePersonalityDepth(bazi, patternResult, personalInfo) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan || !bazi.day.zhi) {
      console.warn('⚠️ analyzePersonalityDepth: bazi.day 数据不完整，返回默认值');
      return {
        mbti_type: 'ISFJ',
        personality_traits: ['稳重', '可靠', '适应性强'],
        strengths: ['责任心强', '善于合作'],
        development_areas: ['增强自信', '提升表达能力']
      };
    }

    const dayGan = bazi.day.gan;
    const dayZhi = bazi.day.zhi;

    // MBTI性格映射
    const mbtiMapping = this.mapToMBTI(bazi, patternResult);

    // 五行性格特征
    const wuxingPersonality = this.analyzeWuxingPersonality(bazi);

    // 行为模式分析
    const behaviorPatterns = this.analyzeBehaviorPatterns(bazi, patternResult);

    // 决策风格评估
    const decisionStyle = this.assessDecisionStyle(bazi, patternResult);

    return {
      mbti_type: mbtiMapping,
      wuxing_personality: wuxingPersonality,
      behavior_patterns: behaviorPatterns,
      decision_style: decisionStyle,
      personality_summary: this.generatePersonalitySummary(mbtiMapping, wuxingPersonality),
      development_suggestions: this.generatePersonalityDevelopmentSuggestions(mbtiMapping, wuxingPersonality)
    };
  }

  /**
   * 能力倾向深度分析
   */
  analyzeAbilityTendencies(bazi, patternResult, personalInfo) {
    // 智力类型评估
    const intelligenceTypes = this.assessIntelligenceTypes(bazi, patternResult);

    // 创造力指数
    const creativityIndex = this.calculateCreativityIndex(bazi, patternResult);

    // 领导力潜质
    const leadershipPotential = this.assessLeadershipPotential(bazi, patternResult);

    // 沟通协调能力
    const communicationSkills = this.assessCommunicationSkills(bazi, patternResult);

    return {
      intelligence_types: intelligenceTypes,
      creativity_index: creativityIndex,
      leadership_potential: leadershipPotential,
      communication_skills: communicationSkills,
      ability_summary: this.generateAbilitySummary(intelligenceTypes, creativityIndex, leadershipPotential),
      enhancement_recommendations: this.generateAbilityEnhancementRecommendations(intelligenceTypes, creativityIndex)
    };
  }

  /**
   * 生活指导细化
   */
  generateDetailedLifeGuidance(bazi, patternResult, yongshenResult, personalInfo) {
    // 日常生活指导
    const dailyLifeGuidance = this.generateDailyLifeGuidance(bazi, yongshenResult);

    // 人际关系指导
    const relationshipGuidance = this.generateRelationshipGuidance(bazi, patternResult, personalInfo);

    // 健康养生建议
    const healthGuidance = this.generateHealthGuidance(bazi, yongshenResult);

    // 居住环境优化
    const environmentOptimization = this.generateEnvironmentOptimization(bazi, yongshenResult);

    return {
      daily_life: dailyLifeGuidance,
      relationships: relationshipGuidance,
      health_wellness: healthGuidance,
      environment: environmentOptimization,
      integrated_suggestions: this.generateIntegratedLifeSuggestions(dailyLifeGuidance, relationshipGuidance, healthGuidance)
    };
  }

  // MBTI映射方法
  mapToMBTI(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan) {
      console.warn('⚠️ mapToMBTI: bazi.day.gan 不存在，返回默认值');
      return 'ISFJ'; // 默认MBTI类型
    }

    const dayGan = bazi.day.gan;
    // 修复：使用正确的属性名，并添加安全检查
    const pattern = (patternResult && (patternResult.pattern || patternResult.primary_pattern || patternResult.main_pattern)) || '普通格局';

    // 基于日干和格局的MBTI映射
    const mbtiMapping = {
      '甲': { base: 'ENFP', traits: ['外向', '直觉', '情感', '感知'] },
      '乙': { base: 'ISFP', traits: ['内向', '感觉', '情感', '感知'] },
      '丙': { base: 'ESFP', traits: ['外向', '感觉', '情感', '感知'] },
      '丁': { base: 'INFP', traits: ['内向', '直觉', '情感', '感知'] },
      '戊': { base: 'ESTJ', traits: ['外向', '感觉', '思维', '判断'] },
      '己': { base: 'ISFJ', traits: ['内向', '感觉', '情感', '判断'] },
      '庚': { base: 'ENTJ', traits: ['外向', '直觉', '思维', '判断'] },
      '辛': { base: 'INTJ', traits: ['内向', '直觉', '思维', '判断'] },
      '壬': { base: 'ENTP', traits: ['外向', '直觉', '思维', '感知'] },
      '癸': { base: 'INTP', traits: ['内向', '直觉', '思维', '感知'] }
    };

    const baseType = mbtiMapping[dayGan] || mbtiMapping['戊'];

    // 根据格局调整（添加安全检查）
    if (pattern && typeof pattern === 'string' && pattern.includes('从')) {
      baseType.traits[0] = '外向'; // 从格通常更外向
    }

    return {
      type: baseType.base,
      traits: baseType.traits,
      confidence: 0.75,
      description: this.getMBTIDescription(baseType.base)
    };
  }

  // 五行性格分析
  analyzeWuxingPersonality(bazi) {
    const wuxingCount = this.calculateWuxingDistribution(bazi);
    const dominantElement = Object.keys(wuxingCount).reduce((a, b) => wuxingCount[a] > wuxingCount[b] ? a : b);

    const wuxingPersonalities = {
      '木': {
        traits: ['仁慈', '进取', '创新', '灵活'],
        strengths: ['适应性强', '富有创意', '善于成长'],
        weaknesses: ['有时过于理想化', '可能缺乏耐心']
      },
      '火': {
        traits: ['热情', '活跃', '表达力强', '领导力'],
        strengths: ['感染力强', '决策果断', '善于激励他人'],
        weaknesses: ['有时过于冲动', '可能缺乏持久力']
      },
      '土': {
        traits: ['稳重', '可靠', '务实', '包容'],
        strengths: ['执行力强', '值得信赖', '善于协调'],
        weaknesses: ['有时过于保守', '可能缺乏创新']
      },
      '金': {
        traits: ['果断', '原则性强', '追求完美', '理性'],
        strengths: ['逻辑清晰', '执行高效', '品质优秀'],
        weaknesses: ['有时过于严格', '可能缺乏灵活性']
      },
      '水': {
        traits: ['智慧', '灵活', '适应性强', '深度思考'],
        strengths: ['洞察力强', '学习能力强', '善于变通'],
        weaknesses: ['有时过于多变', '可能缺乏坚持']
      }
    };

    return {
      dominant_element: dominantElement,
      personality_profile: wuxingPersonalities[dominantElement],
      element_distribution: wuxingCount,
      balance_analysis: this.analyzeWuxingBalance(wuxingCount)
    };
  }

  // 行为模式分析
  analyzeBehaviorPatterns(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi 数据存在
    if (!bazi || !bazi.day || !bazi.day.gan || !bazi.month || !bazi.month.zhi) {
      console.warn('⚠️ analyzeBehaviorPatterns: bazi 数据不完整，返回默认值');
      return {
        decision_making: '稳健型',
        stress_response: '适应性强',
        social_interaction: '友善合作',
        work_style: '踏实认真'
      };
    }

    const dayGan = bazi.day.gan;
    const monthZhi = bazi.month.zhi;

    return {
      decision_making: this.analyzeBehaviorDecisionMaking(dayGan, patternResult),
      stress_response: this.analyzeBehaviorStressResponse(bazi, patternResult),
      social_interaction: this.analyzeBehaviorSocialInteraction(bazi),
      work_style: this.analyzeBehaviorWorkStyle(bazi, patternResult),
      learning_style: this.analyzeBehaviorLearningStyle(bazi)
    };
  }

  // 决策风格评估
  assessDecisionStyle(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan) {
      console.warn('⚠️ assessDecisionStyle: bazi.day.gan 不存在，返回默认值');
      return {
        style: '稳健型',
        speed: 'moderate',
        basis: 'balanced',
        description: '决策风格稳健，综合考虑各种因素'
      };
    }

    const dayGan = bazi.day.gan;
    const clarity = (patternResult && patternResult.clarity_score) || 0.5;

    const decisionStyles = {
      '甲': { style: '直觉型', speed: 'fast', basis: 'intuition' },
      '乙': { style: '感性型', speed: 'moderate', basis: 'emotion' },
      '丙': { style: '冲动型', speed: 'very_fast', basis: 'impulse' },
      '丁': { style: '细致型', speed: 'slow', basis: 'analysis' },
      '戊': { style: '稳健型', speed: 'moderate', basis: 'experience' },
      '己': { style: '谨慎型', speed: 'slow', basis: 'consensus' },
      '庚': { style: '果断型', speed: 'fast', basis: 'logic' },
      '辛': { style: '精确型', speed: 'moderate', basis: 'data' },
      '壬': { style: '灵活型', speed: 'variable', basis: 'situation' },
      '癸': { style: '深思型', speed: 'slow', basis: 'reflection' }
    };

    const baseStyle = decisionStyles[dayGan] || decisionStyles['戊'];

    return {
      primary_style: baseStyle.style,
      decision_speed: baseStyle.speed,
      decision_basis: baseStyle.basis,
      confidence_level: clarity,
      improvement_suggestions: this.generateDecisionStyleImprovements(baseStyle, clarity)
    };
  }

  // 智力类型评估
  assessIntelligenceTypes(bazi, patternResult) {
    const wuxingDistribution = this.calculateWuxingDistribution(bazi);

    const intelligenceMapping = {
      '木': { type: '创造智能', strength: 0.8, description: '善于创新和想象' },
      '火': { type: '社交智能', strength: 0.9, description: '善于人际交往和表达' },
      '土': { type: '实践智能', strength: 0.7, description: '善于实际操作和执行' },
      '金': { type: '逻辑智能', strength: 0.8, description: '善于分析和推理' },
      '水': { type: '学习智能', strength: 0.9, description: '善于学习和适应' }
    };

    const intelligenceTypes = [];
    Object.keys(wuxingDistribution).forEach(element => {
      if (wuxingDistribution[element] > 0.2) {
        const intelligence = intelligenceMapping[element];
        intelligenceTypes.push({
          ...intelligence,
          weight: wuxingDistribution[element]
        });
      }
    });

    return {
      primary_intelligences: intelligenceTypes.sort((a, b) => b.weight - a.weight),
      intelligence_profile: this.generateIntelligenceProfile(intelligenceTypes),
      development_potential: this.assessIntelligenceDevelopmentPotential(intelligenceTypes)
    };
  }

  // 创造力指数计算
  calculateCreativityIndex(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan) {
      console.warn('⚠️ calculateCreativityIndex: bazi.day.gan 不存在，返回默认值');
      return {
        index: 0.6,
        level: '中等',
        description: '创造力水平中等，具备一定的创新能力'
      };
    }

    const dayGan = bazi.day.gan;
    const wuxingDistribution = this.calculateWuxingDistribution(bazi);

    // 基础创造力分数
    const baseCreativity = {
      '甲': 0.8, '乙': 0.7, '丙': 0.9, '丁': 0.8, '戊': 0.5,
      '己': 0.6, '庚': 0.6, '辛': 0.7, '壬': 0.9, '癸': 0.8
    };

    let creativityScore = baseCreativity[dayGan] || 0.6;

    // 五行平衡度影响创造力
    const balance = this.calculateWuxingBalance(wuxingDistribution);
    if (balance > 0.7) creativityScore += 0.1;

    // 格局影响（添加安全检查）
    const pattern = (patternResult && (patternResult.pattern || patternResult.primary_pattern || patternResult.main_pattern)) || '普通格局';
    if (pattern && typeof pattern === 'string' && pattern.includes('从')) creativityScore += 0.1;

    return {
      creativity_score: Math.min(1.0, creativityScore),
      creativity_level: this.getCreativityLevel(creativityScore),
      creative_strengths: this.identifyCreativeStrengths(bazi, wuxingDistribution),
      enhancement_methods: this.suggestCreativityEnhancement(creativityScore, wuxingDistribution)
    };
  }

  // 领导力潜质评估
  assessLeadershipPotential(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi 数据存在
    if (!bazi || !bazi.day || !bazi.day.gan || !bazi.month || !bazi.month.zhi) {
      console.warn('⚠️ assessLeadershipPotential: bazi 数据不完整，返回默认值');
      return {
        potential: 0.6,
        level: '中等',
        leadership_style: '协作型',
        development_suggestions: ['提升沟通技巧', '增强决策能力', '培养团队意识']
      };
    }

    const dayGan = bazi.day.gan;
    const monthZhi = bazi.month.zhi;

    // 基础领导力分数
    const baseLeadership = {
      '甲': 0.8, '乙': 0.5, '丙': 0.9, '丁': 0.6, '戊': 0.8,
      '己': 0.6, '庚': 0.9, '辛': 0.7, '壬': 0.7, '癸': 0.5
    };

    let leadershipScore = baseLeadership[dayGan] || 0.6;

    // 格局影响（添加安全检查）
    const pattern = (patternResult && (patternResult.pattern || patternResult.primary_pattern || patternResult.main_pattern)) || '普通格局';
    if (pattern && typeof pattern === 'string') {
      if (pattern.includes('正官')) leadershipScore += 0.1;
      if (pattern.includes('七杀')) leadershipScore += 0.15;
    }

    return {
      leadership_score: Math.min(1.0, leadershipScore),
      leadership_style: this.identifyLeadershipStyle(dayGan, patternResult),
      leadership_strengths: this.identifyLeadershipStrengths(bazi, patternResult),
      development_areas: this.identifyLeadershipDevelopmentAreas(leadershipScore, bazi)
    };
  }

  // 沟通协调能力评估
  assessCommunicationSkills(bazi, patternResult) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan) {
      console.warn('⚠️ assessCommunicationSkills: bazi.day.gan 不存在，返回默认值');
      return {
        score: 0.6,
        level: '中等',
        description: '沟通协调能力中等，具备基本的人际交往能力'
      };
    }

    const dayGan = bazi.day.gan;
    const wuxingDistribution = this.calculateWuxingDistribution(bazi);

    // 基础沟通能力
    const baseCommunication = {
      '甲': 0.7, '乙': 0.6, '丙': 0.9, '丁': 0.7, '戊': 0.6,
      '己': 0.8, '庚': 0.6, '辛': 0.7, '壬': 0.8, '癸': 0.6
    };

    let communicationScore = baseCommunication[dayGan] || 0.6;

    // 火元素增强表达能力
    if (wuxingDistribution['火'] > 0.3) communicationScore += 0.1;

    return {
      communication_score: Math.min(1.0, communicationScore),
      communication_style: this.identifyCommunicationStyle(dayGan, wuxingDistribution),
      strengths: this.identifyCommunicationStrengths(bazi, wuxingDistribution),
      improvement_areas: this.identifyCommunicationImprovements(communicationScore, bazi)
    };
  }

  // 日常生活指导
  generateDailyLifeGuidance(bazi, yongshenResult) {
    const yongshen = yongshenResult.yongshen;

    return {
      optimal_schedule: this.generateOptimalSchedule(bazi, yongshen),
      dietary_recommendations: this.generateDietaryRecommendations(bazi, yongshen),
      exercise_suggestions: this.generateExerciseSuggestions(bazi, yongshen),
      sleep_guidance: this.generateSleepGuidance(bazi, yongshen),
      daily_habits: this.generateDailyHabits(bazi, yongshen)
    };
  }

  // 人际关系指导
  generateRelationshipGuidance(bazi, patternResult, personalInfo) {
    return {
      spouse_selection: this.generateSpouseSelectionAdvice(bazi, patternResult),
      partnership_advice: this.generatePartnershipAdvice(bazi, patternResult),
      friendship_guidance: this.generateFriendshipGuidance(bazi, personalInfo),
      family_relationships: this.generateFamilyRelationshipAdvice(bazi, patternResult),
      conflict_resolution: this.generateConflictResolutionAdvice(bazi, patternResult)
    };
  }

  // 健康养生建议
  generateHealthGuidance(bazi, yongshenResult) {
    // 🔧 安全检查：确保 bazi.day 存在
    if (!bazi || !bazi.day || !bazi.day.gan) {
      console.warn('⚠️ generateHealthGuidance: bazi.day.gan 不存在，返回默认值');
      return {
        health_risks: ['注意作息规律', '保持心情愉悦'],
        preventive_measures: ['适量运动', '均衡饮食'],
        dietary_suggestions: ['清淡饮食', '多喝水'],
        exercise_recommendations: ['散步', '太极'],
        environmental_elements: ['保持环境整洁', '适当通风']
      };
    }

    const dayGan = bazi.day.gan;
    const yongshen = yongshenResult.yongshen;

    return {
      health_risks: this.identifyHealthRisks(bazi),
      preventive_measures: this.generatePreventiveMeasures(bazi, yongshen),
      wellness_practices: this.generateWellnessPractices(bazi, yongshen),
      seasonal_adjustments: this.generateSeasonalHealthAdjustments(bazi),
      stress_management: this.generateStressManagementAdvice(bazi, yongshen)
    };
  }

  // 居住环境优化
  generateEnvironmentOptimization(bazi, yongshenResult) {
    const yongshen = yongshenResult.yongshen;

    return {
      ideal_directions: this.getIdealDirections(bazi, yongshen),
      color_recommendations: this.getColorRecommendations(bazi, yongshen),
      home_layout: this.getHomeLayoutAdvice(bazi, yongshen),
      workplace_setup: this.getWorkplaceSetupAdvice(bazi, yongshen),
      environmental_elements: this.getEnvironmentalElements(bazi, yongshen)
    };
  }

  // 辅助方法实现
  calculateWuxingDistribution(bazi) {
    // 简化的五行分布计算
    return {
      '木': 0.2,
      '火': 0.3,
      '土': 0.2,
      '金': 0.15,
      '水': 0.15
    };
  }

  calculateWuxingBalance(wuxingDistribution) {
    const values = Object.values(wuxingDistribution);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
    return 1 - Math.sqrt(variance);
  }

  getMBTIDescription(type) {
    const descriptions = {
      'ENFP': '热情洋溢的激励者',
      'ISFP': '温和的艺术家',
      'ESFP': '活泼的表演者',
      'INFP': '理想主义的治愈者',
      'ESTJ': '高效的管理者',
      'ISFJ': '贴心的保护者',
      'ENTJ': '天生的领导者',
      'INTJ': '独立的思想家',
      'ENTP': '富有创意的发明家',
      'INTP': '逻辑的思考者'
    };
    return descriptions[type] || '独特的个性';
  }

  // 更多辅助方法...
  generatePersonalitySummary(mbtiMapping, wuxingPersonality) {
    return `您的性格类型倾向于${mbtiMapping.type}（${mbtiMapping.description}），五行特质以${wuxingPersonality.dominant_element}为主导，表现为${wuxingPersonality.personality_profile.traits.join('、')}的特点。`;
  }

  generatePersonalityDevelopmentSuggestions(mbtiMapping, wuxingPersonality) {
    const suggestions = [];

    // 基于MBTI的建议
    if (mbtiMapping.traits.includes('内向')) {
      suggestions.push('适当增加社交活动，扩展人际网络');
    }

    // 基于五行的建议
    const weakElements = Object.keys(wuxingPersonality.element_distribution)
      .filter(element => wuxingPersonality.element_distribution[element] < 0.15);

    if (weakElements.length > 0) {
      suggestions.push(`注意培养${weakElements.join('、')}元素相关的品质`);
    }

    return suggestions;
  }

  generateAbilitySummary(intelligenceTypes, creativityIndex, leadershipPotential) {
    const primaryIntelligence = intelligenceTypes.primary_intelligences[0];
    return `您的主要智能类型为${primaryIntelligence.type}，创造力指数为${(creativityIndex.creativity_score * 100).toFixed(0)}%，领导力潜质为${(leadershipPotential.leadership_score * 100).toFixed(0)}%。`;
  }

  generateAbilityEnhancementRecommendations(intelligenceTypes, creativityIndex) {
    const recommendations = [];

    // 基于智能类型的建议
    intelligenceTypes.primary_intelligences.forEach(intelligence => {
      if (intelligence.weight < 0.7) {
        recommendations.push(`加强${intelligence.type}的培养和练习`);
      }
    });

    // 基于创造力的建议
    if (creativityIndex.creativity_score < 0.7) {
      recommendations.push('通过多元化学习和跨领域思考提升创造力');
    }

    return recommendations;
  }

  // 更多具体实现方法...
  generateOptimalSchedule(bazi, yongshen) {
    return {
      wake_time: '6:00-7:00',
      work_peak: '9:00-11:00',
      rest_time: '13:00-14:00',
      evening_activity: '19:00-21:00',
      sleep_time: '22:00-23:00'
    };
  }

  generateDietaryRecommendations(bazi, yongshen) {
    const elementDiet = {
      '木': ['绿色蔬菜', '酸味食物', '春季时令'],
      '火': ['红色食物', '苦味食物', '夏季时令'],
      '土': ['黄色食物', '甘味食物', '长夏时令'],
      '金': ['白色食物', '辛味食物', '秋季时令'],
      '水': ['黑色食物', '咸味食物', '冬季时令']
    };

    return {
      beneficial_foods: elementDiet[yongshen] || elementDiet['土'],
      foods_to_avoid: ['过于寒凉', '过于燥热'],
      eating_habits: ['定时定量', '细嚼慢咽', '营养均衡']
    };
  }

  generateExerciseSuggestions(bazi, yongshen) {
    const elementExercise = {
      '木': ['瑜伽', '太极', '户外运动'],
      '火': ['跑步', '球类运动', '高强度训练'],
      '土': ['力量训练', '举重', '稳定性训练'],
      '金': ['武术', '器械训练', '精准运动'],
      '水': ['游泳', '水上运动', '柔韧性训练']
    };

    return {
      recommended_exercises: elementExercise[yongshen] || elementExercise['土'],
      exercise_frequency: '每周3-4次',
      exercise_duration: '30-60分钟',
      best_time: '早晨或傍晚'
    };
  }

  // 继续实现其他方法...
  getIdealDirections(bazi, yongshen) {
    const directionMap = {
      '木': ['东方', '东南方'],
      '火': ['南方'],
      '土': ['中央', '西南方', '东北方'],
      '金': ['西方', '西北方'],
      '水': ['北方']
    };

    return {
      favorable_directions: directionMap[yongshen] || directionMap['土'],
      unfavorable_directions: this.getUnfavorableDirections(yongshen),
      application: '适用于居住、工作、出行方向选择'
    };
  }

  getColorRecommendations(bazi, yongshen) {
    const colorMap = {
      '木': ['绿色', '青色', '蓝色'],
      '火': ['红色', '紫色', '橙色'],
      '土': ['黄色', '棕色', '米色'],
      '金': ['白色', '银色', '金色'],
      '水': ['黑色', '深蓝色', '灰色']
    };

    return {
      lucky_colors: colorMap[yongshen] || colorMap['土'],
      colors_to_avoid: this.getUnfavorableColors(yongshen),
      application: '适用于服装、装饰、配饰选择'
    };
  }

  // 更多辅助方法的简化实现
  analyzeWuxingBalance(wuxingCount) {
    return '五行分布相对均衡，有利于性格的全面发展';
  }

  analyzeBehaviorDecisionMaking(dayGan, patternResult) {
    return '倾向于理性分析后做决定';
  }

  analyzeBehaviorStressResponse(bazi, patternResult) {
    return '面对压力时保持冷静，寻求解决方案';
  }

  analyzeBehaviorSocialInteraction(bazi) {
    return '在社交中表现自然，善于倾听';
  }

  analyzeBehaviorWorkStyle(bazi, patternResult) {
    return '工作中注重效率和质量的平衡';
  }

  analyzeBehaviorLearningStyle(bazi) {
    return '偏好实践与理论相结合的学习方式';
  }

  generateDecisionStyleImprovements(baseStyle, clarity) {
    return ['增强信息收集能力', '提高决策执行力'];
  }

  generateIntelligenceProfile(intelligenceTypes) {
    return '多元智能发展，具有综合优势';
  }

  assessIntelligenceDevelopmentPotential(intelligenceTypes) {
    return '具有良好的智能发展潜力';
  }

  getCreativityLevel(score) {
    if (score >= 0.8) return '高创造力';
    if (score >= 0.6) return '中等创造力';
    return '待开发创造力';
  }

  identifyCreativeStrengths(bazi, wuxingDistribution) {
    return ['想象力丰富', '思维灵活'];
  }

  suggestCreativityEnhancement(score, wuxingDistribution) {
    return ['多元化学习', '跨领域思考', '艺术熏陶'];
  }

  identifyLeadershipStyle(dayGan, patternResult) {
    return '民主型领导风格';
  }

  identifyLeadershipStrengths(bazi, patternResult) {
    return ['决策能力强', '善于激励他人'];
  }

  identifyLeadershipDevelopmentAreas(score, bazi) {
    return ['提升沟通技巧', '增强团队建设能力'];
  }

  identifyCommunicationStyle(dayGan, wuxingDistribution) {
    return '温和而有说服力的沟通风格';
  }

  identifyCommunicationStrengths(bazi, wuxingDistribution) {
    return ['表达清晰', '善于倾听'];
  }

  identifyCommunicationImprovements(score, bazi) {
    return ['增强非语言沟通', '提升公众演讲能力'];
  }

  generateIntegratedLifeSuggestions(dailyLife, relationships, health) {
    return [
      '建立规律的生活作息，有利于身心健康',
      '在人际交往中保持真诚，建立深度关系',
      '注重工作与生活的平衡，避免过度劳累'
    ];
  }

  generateSpouseSelectionAdvice(bazi, patternResult) {
    return {
      ideal_traits: ['性格互补', '价值观相近', '生活习惯协调'],
      compatibility_factors: ['五行相生', '性格平衡', '共同目标'],
      timing_advice: '适合在运势较好的年份考虑婚姻'
    };
  }

  generatePartnershipAdvice(bazi, patternResult) {
    return {
      ideal_partner_type: '能力互补型合作伙伴',
      cooperation_style: '平等协商，优势互补',
      success_factors: ['信任基础', '目标一致', '责任分工明确']
    };
  }

  generateFriendshipGuidance(bazi, personalInfo) {
    return {
      friendship_style: '深度交往型',
      friend_selection: '重质量胜过数量',
      maintenance_tips: ['定期联系', '真诚相待', '互相支持']
    };
  }

  generateFamilyRelationshipAdvice(bazi, patternResult) {
    return {
      family_role: '和谐协调者',
      relationship_tips: ['尊重长辈', '关爱晚辈', '维护家庭和睦'],
      conflict_handling: '以理服人，以情动人'
    };
  }

  generateConflictResolutionAdvice(bazi, patternResult) {
    return {
      resolution_style: '理性沟通型',
      key_principles: ['冷静分析', '寻求共识', '互利共赢'],
      avoidance_strategies: ['避免情绪化', '不做人身攻击', '保持开放心态']
    };
  }

  identifyHealthRisks(bazi) {
    return ['注意心血管健康', '预防消化系统问题'];
  }

  generatePreventiveMeasures(bazi, yongshen) {
    return ['定期体检', '均衡饮食', '适量运动', '充足睡眠'];
  }

  generateWellnessPractices(bazi, yongshen) {
    return ['冥想放松', '呼吸练习', '按摩保健', '中医调理'];
  }

  generateSeasonalHealthAdjustments(bazi) {
    return {
      spring: '注意肝脏保养，多食绿色蔬菜',
      summer: '注意心脏健康，避免过度劳累',
      autumn: '注意肺部保养，预防呼吸道疾病',
      winter: '注意肾脏保养，适当进补'
    };
  }

  generateStressManagementAdvice(bazi, yongshen) {
    return {
      stress_indicators: ['情绪波动', '睡眠质量下降', '食欲变化'],
      management_techniques: ['深呼吸', '运动释压', '音乐疗法', '社交支持'],
      prevention_strategies: ['合理安排时间', '设定现实目标', '培养兴趣爱好']
    };
  }

  getHomeLayoutAdvice(bazi, yongshen) {
    return {
      bedroom: '选择安静的方位，保持整洁',
      study: '光线充足，面向有利方向',
      living_room: '布局开阔，色彩温馨',
      kitchen: '通风良好，五行调和'
    };
  }

  getWorkplaceSetupAdvice(bazi, yongshen) {
    return {
      desk_position: '背靠实墙，面向开阔',
      lighting: '自然光为主，辅助照明',
      decoration: '简洁大方，符合五行喜忌',
      plants: '适当摆放绿植，净化空气'
    };
  }

  getEnvironmentalElements(bazi, yongshen) {
    return {
      water_features: '适当添加水元素装饰',
      plants: '选择对应五行的植物',
      crystals: '摆放有利的水晶饰品',
      artwork: '选择寓意吉祥的艺术品'
    };
  }

  getUnfavorableDirections(yongshen) {
    const oppositeMap = {
      '木': ['西方', '西南方'],
      '火': ['北方'],
      '土': ['东方'],
      '金': ['东方', '南方'],
      '水': ['南方', '西南方']
    };
    return oppositeMap[yongshen] || [];
  }

  getUnfavorableColors(yongshen) {
    const oppositeColorMap = {
      '木': ['白色', '金色'],
      '火': ['黑色', '深蓝色'],
      '土': ['绿色', '青色'],
      '金': ['红色', '紫色'],
      '水': ['黄色', '棕色']
    };
    return oppositeColorMap[yongshen] || [];
  }

  generateSleepGuidance(bazi, yongshen) {
    return {
      optimal_sleep_time: '22:00-23:00入睡',
      sleep_duration: '7-8小时',
      sleep_environment: '安静、黑暗、凉爽',
      pre_sleep_routine: '避免电子设备，进行放松活动'
    };
  }

  generateDailyHabits(bazi, yongshen) {
    return {
      morning_routine: ['早起、洗漱、简单运动、营养早餐'],
      work_habits: ['专注工作、定时休息、保持整洁'],
      evening_routine: ['总结反思、放松娱乐、准备明日'],
      weekly_habits: ['深度清洁、社交活动、学习充电']
    };
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedAdviceGenerator;
} else if (typeof window !== 'undefined') {
  window.EnhancedAdviceGenerator = EnhancedAdviceGenerator;
}
