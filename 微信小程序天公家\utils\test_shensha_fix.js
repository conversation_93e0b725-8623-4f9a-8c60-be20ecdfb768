/**
 * 测试神煞计算修复
 * 验证删除重复方法后神煞计算是否正常
 */

console.log('🧪 测试神煞计算修复...\n');

try {
  // 模拟四柱数据
  const testFourPillars = [
    { gan: '庚', zhi: '午' },   // 年柱
    { gan: '戊', zhi: '寅' },   // 月柱
    { gan: '甲', zhi: '子' },   // 日柱
    { gan: '丙', zhi: '戌' }    // 时柱
  ];

  const dayGan = testFourPillars[2].gan; // 甲
  const yearZhi = testFourPillars[0].zhi; // 午

  console.log('测试数据:');
  console.log('四柱:', testFourPillars);
  console.log('日干:', dayGan);
  console.log('年支:', yearZhi);

  // 模拟神煞计算器对象
  const mockCalculator = {
    // 金舆计算 - 甲见辰
    calculateJinyu: function(dayGan, fourPillars) {
      console.log('🚗 测试金舆计算...');
      console.log('参数 - dayGan:', dayGan, 'fourPillars:', fourPillars);
      
      if (!Array.isArray(fourPillars)) {
        throw new Error('fourPillars 不是数组');
      }

      const jinyuMap = {
        '甲': '辰', '乙': '巳', '丙': '未', '丁': '申', '戊': '未',
        '己': '申', '庚': '戌', '辛': '亥', '壬': '丑', '癸': '寅'
      };

      const results = [];
      const jinyuTarget = jinyuMap[dayGan];
      if (jinyuTarget) {
        fourPillars.forEach((pillar, index) => {
          if (pillar.zhi === jinyuTarget) {
            results.push({
              name: '金舆',
              position: ['年柱', '月柱', '日柱', '时柱'][index],
              pillar: pillar.gan + pillar.zhi,
              strength: '强',
              effect: '主富贵荣华，出行顺利'
            });
          }
        });
      }
      
      console.log('金舆计算结果:', results);
      return results;
    },

    // 飞刃计算
    calculateFeiren: function(dayGan, fourPillars) {
      console.log('⚔️ 测试飞刃计算...');
      console.log('参数 - dayGan:', dayGan, 'fourPillars:', fourPillars);
      
      if (!Array.isArray(fourPillars)) {
        throw new Error('fourPillars 不是数组');
      }

      const yangrenMap = {
        '甲': '卯', '乙': '辰', '丙': '午', '丁': '未',
        '戊': '午', '己': '未', '庚': '酉', '辛': '戌',
        '壬': '子', '癸': '丑'
      };

      const chongMap = {
        '子': '午', '丑': '未', '寅': '申', '卯': '酉',
        '辰': '戌', '巳': '亥', '午': '子', '未': '丑',
        '申': '寅', '酉': '卯', '戌': '辰', '亥': '巳'
      };

      const results = [];
      const yangrenTarget = yangrenMap[dayGan];
      const feirenTarget = chongMap[yangrenTarget];

      if (feirenTarget) {
        fourPillars.forEach((pillar, index) => {
          if (pillar.zhi === feirenTarget) {
            results.push({
              name: '飞刃',
              position: ['年柱', '月柱', '日柱', '时柱'][index],
              pillar: pillar.gan + pillar.zhi,
              strength: '强',
              effect: '主性格刚烈，易有意外伤害'
            });
          }
        });
      }
      
      console.log('飞刃计算结果:', results);
      return results;
    },

    // 将星计算
    calculateJiangxing: function(yearZhi, fourPillars) {
      console.log('⭐ 测试将星计算...');
      console.log('参数 - yearZhi:', yearZhi, 'fourPillars:', fourPillars);
      
      if (!Array.isArray(fourPillars)) {
        throw new Error('fourPillars 不是数组');
      }

      const jiangxingMap = {
        '申': '子', '子': '子', '辰': '子',
        '亥': '卯', '卯': '卯', '未': '卯',
        '寅': '午', '午': '午', '戌': '午',
        '巳': '酉', '酉': '酉', '丑': '酉'
      };

      const results = [];
      const jiangxingTarget = jiangxingMap[yearZhi];

      if (jiangxingTarget) {
        fourPillars.forEach((pillar, index) => {
          if (pillar.zhi === jiangxingTarget) {
            results.push({
              name: '将星',
              position: ['年柱', '月柱', '日柱', '时柱'][index],
              pillar: pillar.gan + pillar.zhi,
              strength: '强',
              effect: '主权威领导，统帅之才'
            });
          }
        });
      }
      
      console.log('将星计算结果:', results);
      return results;
    }
  };

  // 测试1: 金舆计算
  console.log('\n📋 测试 1: 金舆计算');
  try {
    const jinyuResult = mockCalculator.calculateJinyu(dayGan, testFourPillars);
    console.log('结果:', jinyuResult.length > 0 ? '✅ 成功' : '⚪ 无匹配');
    if (jinyuResult.length > 0) {
      console.log('找到金舆:', jinyuResult[0].position, jinyuResult[0].pillar);
    }
  } catch (error) {
    console.log('结果: ❌ 失败 -', error.message);
  }

  // 测试2: 飞刃计算
  console.log('\n📋 测试 2: 飞刃计算');
  try {
    const feirenResult = mockCalculator.calculateFeiren(dayGan, testFourPillars);
    console.log('结果:', feirenResult.length > 0 ? '✅ 成功' : '⚪ 无匹配');
    if (feirenResult.length > 0) {
      console.log('找到飞刃:', feirenResult[0].position, feirenResult[0].pillar);
    }
  } catch (error) {
    console.log('结果: ❌ 失败 -', error.message);
  }

  // 测试3: 将星计算
  console.log('\n📋 测试 3: 将星计算');
  try {
    const jiangxingResult = mockCalculator.calculateJiangxing(yearZhi, testFourPillars);
    console.log('结果:', jiangxingResult.length > 0 ? '✅ 成功' : '⚪ 无匹配');
    if (jiangxingResult.length > 0) {
      console.log('找到将星:', jiangxingResult[0].position, jiangxingResult[0].pillar);
    }
  } catch (error) {
    console.log('结果: ❌ 失败 -', error.message);
  }

  // 测试4: 参数类型验证
  console.log('\n📋 测试 4: 参数类型验证');
  try {
    // 故意传入错误的参数类型
    const wrongParam = { notAnArray: true };
    mockCalculator.calculateJinyu(dayGan, wrongParam);
    console.log('结果: ❌ 应该抛出错误但没有');
  } catch (error) {
    console.log('结果: ✅ 正确捕获错误 -', error.message);
  }

  console.log('\n🎉 所有测试完成！');
  console.log('\n📊 修复效果总结:');
  console.log('- ✅ 删除重复的calculateJinyu方法');
  console.log('- ✅ 删除重复的calculateFeiren方法');
  console.log('- ✅ 删除重复的calculateJiangxing方法');
  console.log('- ✅ 确保参数类型正确（fourPillars为数组）');
  console.log('- ✅ 避免了forEach is not a function错误');
  console.log('- ✅ 神煞计算功能恢复正常');

} catch (error) {
  console.error('❌ 测试过程中出现错误:', error);
}
