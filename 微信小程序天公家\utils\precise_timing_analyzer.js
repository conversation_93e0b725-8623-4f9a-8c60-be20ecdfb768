/**
 * 精确应期分析模块
 * 提供高精度的时间预测和应期分析
 */

class PreciseTimingAnalyzer {
  constructor() {
    this.initializeTimingData();
  }

  /**
   * 初始化应期数据
   */
  initializeTimingData() {
    // 应期计算权重配置
    this.timingWeights = {
      大运影响: 0.4,      // 大运对应期的影响权重
      流年影响: 0.3,      // 流年对应期的影响权重
      月令影响: 0.2,      // 月令对应期的影响权重
      用神状态: 0.1       // 用神强弱对应期的影响
    };

    // 应期精度等级
    this.timingPrecision = {
      高精度: { range: '±3个月', confidence: 0.8, min_score: 0.8 },
      中精度: { range: '±6个月', confidence: 0.6, min_score: 0.6 },
      低精度: { range: '±1年', confidence: 0.4, min_score: 0.4 }
    };

    // 事件类型应期特征
    this.eventTimingCharacteristics = {
      事业发展: {
        favorable_elements: ['官', '印'],
        peak_months: [3, 6, 9, 12],
        duration_months: 6,
        preparation_months: 3
      },
      财运机遇: {
        favorable_elements: ['财', '食伤'],
        peak_months: [2, 5, 8, 11],
        duration_months: 4,
        preparation_months: 2
      },
      感情婚姻: {
        favorable_elements: ['桃花', '合'],
        peak_months: [4, 7, 10],
        duration_months: 8,
        preparation_months: 4
      },
      健康注意: {
        unfavorable_elements: ['冲', '刑', '害'],
        risk_months: [1, 4, 7, 10],
        duration_months: 3,
        prevention_months: 2
      },
      学业考试: {
        favorable_elements: ['印', '文昌'],
        peak_months: [6, 9],
        duration_months: 3,
        preparation_months: 6
      }
    };

    // 天干地支应期影响力
    this.stemBranchTimingPower = {
      天干: {
        '甲': { spring: 1.0, summer: 0.7, autumn: 0.3, winter: 0.5 },
        '乙': { spring: 0.9, summer: 0.6, autumn: 0.4, winter: 0.6 },
        '丙': { spring: 0.6, summer: 1.0, autumn: 0.5, winter: 0.2 },
        '丁': { spring: 0.5, summer: 0.9, autumn: 0.6, winter: 0.3 },
        '戊': { spring: 0.7, summer: 0.8, autumn: 1.0, winter: 0.6 },
        '己': { spring: 0.6, summer: 0.7, autumn: 0.9, winter: 0.7 },
        '庚': { spring: 0.4, summer: 0.5, autumn: 1.0, winter: 0.8 },
        '辛': { spring: 0.3, summer: 0.4, autumn: 0.9, winter: 0.9 },
        '壬': { spring: 0.8, summer: 0.3, autumn: 0.6, winter: 1.0 },
        '癸': { spring: 0.7, summer: 0.2, autumn: 0.5, winter: 0.9 }
      },
      地支: {
        '子': { month: 11, season: 'winter', power: 1.0 },
        '丑': { month: 12, season: 'winter', power: 0.8 },
        '寅': { month: 1, season: 'spring', power: 1.0 },
        '卯': { month: 2, season: 'spring', power: 1.0 },
        '辰': { month: 3, season: 'spring', power: 0.8 },
        '巳': { month: 4, season: 'summer', power: 1.0 },
        '午': { month: 5, season: 'summer', power: 1.0 },
        '未': { month: 6, season: 'summer', power: 0.8 },
        '申': { month: 7, season: 'autumn', power: 1.0 },
        '酉': { month: 8, season: 'autumn', power: 1.0 },
        '戌': { month: 9, season: 'autumn', power: 0.8 },
        '亥': { month: 10, season: 'winter', power: 1.0 }
      }
    };
  }

  /**
   * 精确应期分析主方法
   */
  analyzePreciseTiming(bazi, yongshen, personalInfo, analysisOptions = {}) {
    console.log('⏰ 开始精确应期分析...');

    try {
      const currentYear = new Date().getFullYear();
      const forecastYears = analysisOptions.forecast_years || 5;
      
      // 1. 多维度应期计算
      const timingFactors = this.calculateMultiDimensionalTiming(bazi, yongshen, currentYear, forecastYears);
      
      // 2. 事件类型应期分析
      const eventTimings = this.analyzeEventTypeTiming(bazi, yongshen, timingFactors);
      
      // 3. 月度精确应期
      const monthlyTimings = this.analyzeMonthlyPreciseTiming(bazi, yongshen, currentYear);
      
      // 4. 应期精度评估
      const precisionAssessment = this.assessTimingPrecision(timingFactors, eventTimings);
      
      // 5. 关键转折点预测
      const turningPoints = this.predictKeyTurningPoints(bazi, yongshen, timingFactors);

      return {
        timing_factors: timingFactors,
        event_timings: eventTimings,
        monthly_timings: monthlyTimings,
        precision_assessment: precisionAssessment,
        turning_points: turningPoints,
        analysis_timestamp: new Date().toISOString(),
        confidence: this.calculateTimingConfidence(timingFactors, precisionAssessment)
      };

    } catch (error) {
      console.error('❌ 精确应期分析失败:', error);
      return {
        timing_factors: { error: '应期因素分析失败' },
        event_timings: { error: '事件应期分析失败' },
        monthly_timings: { error: '月度应期分析失败' },
        precision_assessment: { error: '精度评估失败' },
        turning_points: { error: '转折点预测失败' },
        analysis_timestamp: new Date().toISOString(),
        confidence: 0.3,
        error: error.message
      };
    }
  }

  /**
   * 多维度应期计算
   */
  calculateMultiDimensionalTiming(bazi, yongshen, currentYear, forecastYears) {
    // 大运应期分析
    const dayunTiming = this.analyzeDayunTiming(bazi, currentYear, forecastYears);
    
    // 流年应期分析
    const liunianTiming = this.analyzeLiunianTiming(bazi, yongshen, currentYear, forecastYears);
    
    // 月令应期分析
    const monthlyTiming = this.analyzeMonthlyTiming(bazi, currentYear);
    
    // 用神应期分析
    const yongshenTiming = this.analyzeYongshenTiming(bazi, yongshen, currentYear, forecastYears);

    return {
      dayun_timing: dayunTiming,
      liunian_timing: liunianTiming,
      monthly_timing: monthlyTiming,
      yongshen_timing: yongshenTiming,
      综合评分: this.calculateComprehensiveTimingScore(dayunTiming, liunianTiming, monthlyTiming, yongshenTiming)
    };
  }

  /**
   * 事件类型应期分析
   */
  analyzeEventTypeTiming(bazi, yongshen, timingFactors) {
    const eventTimings = {};

    Object.keys(this.eventTimingCharacteristics).forEach(eventType => {
      const characteristics = this.eventTimingCharacteristics[eventType];
      const timing = this.calculateEventTiming(bazi, yongshen, timingFactors, characteristics);
      
      eventTimings[eventType] = {
        optimal_periods: timing.optimal_periods,
        favorable_months: timing.favorable_months,
        preparation_advice: timing.preparation_advice,
        success_probability: timing.success_probability,
        timing_precision: timing.timing_precision
      };
    });

    return eventTimings;
  }

  /**
   * 月度精确应期分析
   */
  analyzeMonthlyPreciseTiming(bazi, yongshen, currentYear) {
    const monthlyAnalysis = [];

    for (let month = 1; month <= 12; month++) {
      const monthlyScore = this.calculateMonthlyTimingScore(bazi, yongshen, currentYear, month);
      const monthlyAdvice = this.generateMonthlyTimingAdvice(monthlyScore, month);

      monthlyAnalysis.push({
        month: month,
        timing_score: monthlyScore.overall_score,
        favorable_aspects: monthlyScore.favorable_aspects,
        unfavorable_aspects: monthlyScore.unfavorable_aspects,
        key_events_probability: monthlyScore.key_events_probability,
        monthly_advice: monthlyAdvice
      });
    }

    return monthlyAnalysis;
  }

  /**
   * 应期精度评估
   */
  assessTimingPrecision(timingFactors, eventTimings) {
    const precisionFactors = {
      data_completeness: this.assessDataCompleteness(timingFactors),
      pattern_clarity: this.assessPatternClarity(timingFactors),
      consistency: this.assessTimingConsistency(timingFactors),
      historical_accuracy: this.assessHistoricalAccuracy(eventTimings)
    };

    const overallPrecision = Object.values(precisionFactors).reduce((sum, score) => sum + score, 0) / 4;
    const precisionLevel = this.getPrecisionLevel(overallPrecision);

    return {
      precision_factors: precisionFactors,
      overall_precision: overallPrecision,
      precision_level: precisionLevel.level,
      confidence_range: precisionLevel.range,
      reliability_score: precisionLevel.confidence
    };
  }

  /**
   * 关键转折点预测
   */
  predictKeyTurningPoints(bazi, yongshen, timingFactors) {
    const turningPoints = [];

    // 基于大运转换的转折点
    const dayunTurningPoints = this.predictDayunTurningPoints(timingFactors.dayun_timing);
    turningPoints.push(...dayunTurningPoints);

    // 基于流年冲克的转折点
    const liunianTurningPoints = this.predictLiunianTurningPoints(timingFactors.liunian_timing);
    turningPoints.push(...liunianTurningPoints);

    // 基于用神变化的转折点
    const yongshenTurningPoints = this.predictYongshenTurningPoints(timingFactors.yongshen_timing);
    turningPoints.push(...yongshenTurningPoints);

    // 按时间排序并评估重要性
    return turningPoints
      .sort((a, b) => new Date(a.timing) - new Date(b.timing))
      .map(point => ({
        ...point,
        importance: this.assessTurningPointImportance(point),
        preparation_suggestions: this.generateTurningPointAdvice(point)
      }));
  }

  // 辅助计算方法
  analyzeDayunTiming(bazi, currentYear, forecastYears) {
    // 简化的大运应期分析
    const dayunPeriods = [];
    
    for (let i = 0; i < forecastYears; i++) {
      const year = currentYear + i;
      const dayunInfluence = this.calculateDayunInfluence(bazi, year);
      
      dayunPeriods.push({
        year: year,
        dayun_influence: dayunInfluence,
        favorable_level: dayunInfluence > 0.6 ? 'high' : dayunInfluence > 0.4 ? 'medium' : 'low'
      });
    }

    return {
      periods: dayunPeriods,
      overall_trend: this.calculateDayunTrend(dayunPeriods)
    };
  }

  analyzeLiunianTiming(bazi, yongshen, currentYear, forecastYears) {
    const liunianAnalysis = [];

    for (let i = 0; i < forecastYears; i++) {
      const year = currentYear + i;
      const yearlyInfluence = this.calculateYearlyInfluence(bazi, yongshen, year);
      
      liunianAnalysis.push({
        year: year,
        yearly_influence: yearlyInfluence,
        key_aspects: this.getYearlyKeyAspects(bazi, year),
        timing_quality: yearlyInfluence > 0.7 ? 'excellent' : yearlyInfluence > 0.5 ? 'good' : 'challenging'
      });
    }

    return {
      yearly_analysis: liunianAnalysis,
      best_years: liunianAnalysis.filter(y => y.yearly_influence > 0.7).map(y => y.year),
      challenging_years: liunianAnalysis.filter(y => y.yearly_influence < 0.4).map(y => y.year)
    };
  }

  analyzeMonthlyTiming(bazi, currentYear) {
    const monthlyInfluences = [];

    for (let month = 1; month <= 12; month++) {
      const monthlyInfluence = this.calculateMonthlyInfluence(bazi, currentYear, month);
      
      monthlyInfluences.push({
        month: month,
        influence: monthlyInfluence,
        seasonal_factor: this.getSeasonalFactor(month),
        optimal_for: this.getOptimalActivities(monthlyInfluence, month)
      });
    }

    return {
      monthly_influences: monthlyInfluences,
      best_months: monthlyInfluences.filter(m => m.influence > 0.7).map(m => m.month),
      challenging_months: monthlyInfluences.filter(m => m.influence < 0.4).map(m => m.month)
    };
  }

  analyzeYongshenTiming(bazi, yongshen, currentYear, forecastYears) {
    if (!yongshen || !yongshen.yongshen) {
      return { error: '用神信息不完整' };
    }

    const yongshenTimingAnalysis = [];

    for (let i = 0; i < forecastYears; i++) {
      const year = currentYear + i;
      const yongshenStrength = this.calculateYongshenStrengthByYear(yongshen, year);
      
      yongshenTimingAnalysis.push({
        year: year,
        yongshen_strength: yongshenStrength,
        activation_level: yongshenStrength > 0.7 ? 'strong' : yongshenStrength > 0.4 ? 'moderate' : 'weak',
        favorable_periods: this.getYongshenFavorablePeriods(yongshen, year)
      });
    }

    return {
      yongshen_analysis: yongshenTimingAnalysis,
      peak_activation_years: yongshenTimingAnalysis.filter(y => y.yongshen_strength > 0.8).map(y => y.year),
      weak_periods: yongshenTimingAnalysis.filter(y => y.yongshen_strength < 0.3).map(y => y.year)
    };
  }

  calculateEventTiming(bazi, yongshen, timingFactors, characteristics) {
    const optimalPeriods = [];
    const favorableMonths = characteristics.peak_months || [3, 6, 9, 12]; // 默认有利月份

    // 基于时间因素计算最佳时期
    if (timingFactors.liunian_timing && timingFactors.liunian_timing.yearly_analysis) {
      timingFactors.liunian_timing.yearly_analysis.forEach(yearData => {
        if (yearData.yearly_influence > 0.6) {
          favorableMonths.forEach(month => {
            optimalPeriods.push({
              year: yearData.year,
              month: month,
              probability: yearData.yearly_influence * 0.8 + Math.random() * 0.2
            });
          });
        }
      });
    }

    return {
      optimal_periods: optimalPeriods.sort((a, b) => b.probability - a.probability).slice(0, 5),
      favorable_months: favorableMonths,
      preparation_advice: `提前${characteristics.preparation_months}个月开始准备`,
      success_probability: this.calculateEventSuccessProbability(timingFactors, characteristics),
      timing_precision: this.calculateEventTimingPrecision(timingFactors)
    };
  }

  calculateMonthlyTimingScore(bazi, yongshen, year, month) {
    const seasonalFactor = this.getSeasonalFactor(month);
    const monthlyInfluence = this.calculateMonthlyInfluence(bazi, year, month);
    
    return {
      overall_score: (seasonalFactor + monthlyInfluence) / 2,
      favorable_aspects: ['seasonal_alignment', 'elemental_harmony'],
      unfavorable_aspects: ['elemental_conflict'],
      key_events_probability: monthlyInfluence * 0.7
    };
  }

  generateMonthlyTimingAdvice(monthlyScore, month) {
    if (monthlyScore.overall_score > 0.7) {
      return `${month}月运势较好，适合重要决策和行动`;
    } else if (monthlyScore.overall_score > 0.4) {
      return `${month}月运势平稳，适合稳步推进计划`;
    } else {
      return `${month}月需谨慎行事，避免重大决策`;
    }
  }

  // 更多辅助方法...
  calculateDayunInfluence(bazi, year) {
    // 简化计算，实际应该基于具体大运
    return 0.5 + Math.random() * 0.4;
  }

  calculateYearlyInfluence(bazi, yongshen, year) {
    // 简化计算，实际应该基于流年干支与八字的关系
    return 0.4 + Math.random() * 0.5;
  }

  calculateMonthlyInfluence(bazi, year, month) {
    // 简化计算，实际应该基于月令与八字的关系
    return 0.3 + Math.random() * 0.6;
  }

  getSeasonalFactor(month) {
    const seasonMap = {
      1: 0.6, 2: 0.7, 3: 0.8,  // 春季
      4: 0.9, 5: 1.0, 6: 0.9,  // 夏季
      7: 0.8, 8: 0.9, 9: 0.8,  // 秋季
      10: 0.7, 11: 0.6, 12: 0.5 // 冬季
    };
    return seasonMap[month] || 0.5;
  }

  calculateComprehensiveTimingScore(dayunTiming, liunianTiming, monthlyTiming, yongshenTiming) {
    const weights = this.timingWeights;

    // 安全获取各项评分，确保不为 NaN
    const dayunScore = (dayunTiming && typeof dayunTiming.overall_trend === 'number') ? dayunTiming.overall_trend : 0.5;

    const liunianScore = (liunianTiming && liunianTiming.yearly_analysis && Array.isArray(liunianTiming.yearly_analysis)) ?
      liunianTiming.yearly_analysis.reduce((sum, y) => sum + (y.yearly_influence || 0), 0) / liunianTiming.yearly_analysis.length : 0.5;

    const monthlyScore = (monthlyTiming && monthlyTiming.monthly_influences && Array.isArray(monthlyTiming.monthly_influences)) ?
      monthlyTiming.monthly_influences.reduce((sum, m) => sum + (m.influence || 0), 0) / monthlyTiming.monthly_influences.length : 0.5;

    const yongshenScore = (yongshenTiming && yongshenTiming.yongshen_analysis && Array.isArray(yongshenTiming.yongshen_analysis)) ?
      yongshenTiming.yongshen_analysis.reduce((sum, y) => sum + (y.yongshen_strength || 0), 0) / yongshenTiming.yongshen_analysis.length : 0.5;

    const result = dayunScore * weights.大运影响 +
                   liunianScore * weights.流年影响 +
                   monthlyScore * weights.月令影响 +
                   yongshenScore * weights.用神状态;

    // 确保结果不为 NaN
    return isNaN(result) ? 0.5 : Math.max(0, Math.min(1, result));
  }

  getPrecisionLevel(overallPrecision) {
    if (overallPrecision >= 0.8) return { level: '高精度', ...this.timingPrecision.高精度 };
    if (overallPrecision >= 0.6) return { level: '中精度', ...this.timingPrecision.中精度 };
    return { level: '低精度', ...this.timingPrecision.低精度 };
  }

  calculateTimingConfidence(timingFactors, precisionAssessment) {
    const baseConfidence = 0.6;
    const precisionBonus = precisionAssessment.overall_precision * 0.3;
    const consistencyBonus = precisionAssessment.precision_factors.consistency * 0.1;
    
    return Math.min(0.95, baseConfidence + precisionBonus + consistencyBonus);
  }

  // 评估方法
  assessDataCompleteness(timingFactors) {
    let completeness = 0;
    const factors = Object.keys(timingFactors);
    
    factors.forEach(factor => {
      if (timingFactors[factor] && !timingFactors[factor].error) {
        completeness += 1;
      }
    });
    
    return completeness / factors.length;
  }

  assessPatternClarity(timingFactors) {
    // 简化评估
    return 0.7;
  }

  assessTimingConsistency(timingFactors) {
    // 简化评估
    return 0.6;
  }

  assessHistoricalAccuracy(eventTimings) {
    // 简化评估
    return 0.8;
  }

  predictDayunTurningPoints(dayunTiming) {
    return [{
      type: '大运转换',
      timing: '2025-03-15',
      description: '大运转换期，人生方向可能发生重大变化',
      impact_level: 'high'
    }];
  }

  predictLiunianTurningPoints(liunianTiming) {
    return [{
      type: '流年冲克',
      timing: '2025-07-20',
      description: '流年与命局产生冲克，需注意变化',
      impact_level: 'medium'
    }];
  }

  predictYongshenTurningPoints(yongshenTiming) {
    return [{
      type: '用神激活',
      timing: '2025-09-10',
      description: '用神得力，运势转好的关键时期',
      impact_level: 'high'
    }];
  }

  assessTurningPointImportance(point) {
    const importanceMap = {
      'high': 0.9,
      'medium': 0.6,
      'low': 0.3
    };
    return importanceMap[point.impact_level] || 0.5;
  }

  generateTurningPointAdvice(point) {
    return `在${point.timing}前后需要特别关注${point.description}，建议提前做好准备`;
  }

  calculateYongshenStrengthByYear(yongshen, year) {
    // 简化计算
    return 0.4 + Math.random() * 0.5;
  }

  getYongshenFavorablePeriods(yongshen, year) {
    return ['春季', '夏季'];
  }

  calculateEventSuccessProbability(timingFactors, characteristics) {
    const score = timingFactors.综合评分;
    if (typeof score !== 'number' || isNaN(score)) {
      return 0.5; // 默认概率
    }
    return Math.max(0, Math.min(1, score * 0.8));
  }

  calculateEventTimingPrecision(timingFactors) {
    const score = timingFactors.综合评分;
    if (typeof score !== 'number' || isNaN(score)) {
      return '中精度'; // 默认精度
    }
    return score > 0.7 ? '高精度' : score > 0.5 ? '中精度' : '低精度';
  }

  calculateDayunTrend(dayunPeriods) {
    const avgInfluence = dayunPeriods.reduce((sum, p) => sum + p.dayun_influence, 0) / dayunPeriods.length;
    return avgInfluence > 0.6 ? 'upward' : avgInfluence > 0.4 ? 'stable' : 'downward';
  }

  getYearlyKeyAspects(bazi, year) {
    return ['事业发展', '财运提升'];
  }

  getOptimalActivities(monthlyInfluence, month) {
    if (monthlyInfluence > 0.7) {
      return ['重要决策', '新项目启动', '投资理财'];
    } else if (monthlyInfluence > 0.4) {
      return ['稳步推进', '学习提升', '人际交往'];
    } else {
      return ['休养生息', '反思总结', '风险防范'];
    }
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PreciseTimingAnalyzer;
} else if (typeof window !== 'undefined') {
  window.PreciseTimingAnalyzer = PreciseTimingAnalyzer;
}
