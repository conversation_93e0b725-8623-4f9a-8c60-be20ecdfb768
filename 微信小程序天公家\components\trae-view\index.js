// components/trae-view/index.js
const { transformQuestion, getTemplateTypeByGrade, transformOptions } = require('../../utils/grade_conversation_transformer');

Component({
  properties: {
    questions: {
      type: Array,
      value: []
    },
    currentQuestionIndex: {
      type: Number,
      value: 0
    },
    grade: {
      type: String,
      value: ''
    },
    gradeCategory: {
      type: String,
      value: ''
    },
    useConversationalFormat: {
      type: Boolean,
      value: true
    },
    isDebugMode: {
      type: Boolean,
      value: false
    }
  },
  
  lifetimes: {
    attached: function() {
      // 组件初始化时记录开始时间
      this.setData({
        answerStartTime: Date.now()
      });
      
      // 检查问题数据格式并输出调试信息
      this.checkQuestionData();
      
      // 转换问题为对话形式
      this.transformQuestionsToConversational();
    }
  },
  
  data: {
    selectedOption: -1,
    answerStartTime: 0,
    isSpeedAnswer: false,
    speedAnswerThreshold: 2000, // 2秒阈值，用于标记速答题
    conversationalQuestions: [], // 存储转换后的会话式问题
  },
  
  methods: {
    // 检查问题数据格式
    checkQuestionData: function() {
      const questions = this.properties.questions;
      const grade = this.properties.grade;
      const gradeCategory = this.properties.gradeCategory;
      
      console.log('trae-view组件初始化:', {
        grade: grade,
        gradeCategory: gradeCategory,
        questionCount: questions ? questions.length : 0,
        currentIndex: this.properties.currentQuestionIndex
      });
      
      if (questions && questions.length > 0) {
        const firstQuestion = questions[0];
        console.log('问题数据格式检查:', {
          hasText: !!firstQuestion.text,
          hasContent: !!firstQuestion.content,
          optionsType: typeof firstQuestion.options,
          hasOptions: !!(firstQuestion.options && firstQuestion.options.length),
          optionsFormat: firstQuestion.options && firstQuestion.options[0] && firstQuestion.options[0].text ? 'object' : 'string'
        });
      } else {
        console.log('问题数据为空或不存在');
      }
    },
    
    // 转换问题为对话形式
    transformQuestionsToConversational: function() {
      // 完全禁用会话转换功能，直接使用原始题目数据
      console.log('已禁用会话式转换功能，直接使用原始题目数据');
      
      // 直接使用原始问题，不进行任何转换
        this.setData({
          conversationalQuestions: this.properties.questions
        });
    },
    
    // 内部简单问题转换函数，作为备用
    _internalTransformQuestion: function(question, gradeNumber) {
      // 简单的问题转换逻辑
      if (!question || (!question.text && !question.content)) {
        return '问题数据不完整';
      }
      
      const content = question.text || question.content;
      
      // 根据年级选择不同的问题前缀
      let prefix = '';
      
      if (gradeNumber <= 3) {
        // 低年级小学生
        prefix = ['小朋友，', '告诉我，', '你觉得，'][Math.floor(Math.random() * 3)];
      } else if (gradeNumber <= 6) {
        // 高年级小学生
        prefix = ['你会', '你觉得', '你认为'][Math.floor(Math.random() * 3)];
      } else if (gradeNumber <= 9) {
        // 初中生
        prefix = ['你是否', '你会', '你觉得'][Math.floor(Math.random() * 3)];
      } else {
        // 高中生
        prefix = ['你会', '你认为', ''][Math.floor(Math.random() * 3)];
      }
      
      // 移除问题末尾的问号和格式性词语
      let cleanContent = content
        .replace(/[？?]$/g, '')
        .replace(/是否|请问|您|请您回答/g, '')
        .trim();
      
      // 检查问题是否已经是会话式
      const conversationalStarters = [
        '你会', '你有', '你是', '你觉得', '你认为', '你喜欢'
      ];
      
      const alreadyConversational = conversationalStarters.some(starter => 
        cleanContent.startsWith(starter)
      );
      
      // 如果已经是会话式，保持原样，否则添加前缀
      let transformedContent = alreadyConversational ? cleanContent : (prefix + cleanContent);
      
      // 确保问题以问号结尾
      if (!transformedContent.endsWith('？') && !transformedContent.endsWith('?')) {
        transformedContent += '？';
      }
      
      return transformedContent;
    },
    
    // 内部选项转换函数
    _internalTransformOptions: function(options, gradeNumber) {
      if (!options || !Array.isArray(options)) {
        return [];
      }
      
      // 确定年级类型
      const isElementary = gradeNumber <= 6;
      const isLowerElementary = gradeNumber <= 3;
      
      // 返回转换后的选项
      return options.map(option => {
        // 防御性检查，确保option存在
        if (!option) return { text: '选项缺失', value: '' };
        
        // 处理字符串或对象形式的选项
        let optionText = '';
        let optionValue = '';
        
        if (typeof option === 'string') {
          optionText = option;
          optionValue = option;
        } else if (typeof option === 'object') {
          optionText = option.text || option.value || '';
          if (typeof optionText !== 'string') {
            optionText = String(optionText || '');
          }
          optionValue = option.value || option.text || '';
        } else {
          optionText = String(option || '');
          optionValue = option;
        }
        
        // 为小学生添加表情符号
        if (isElementary) {
          // 根据选项内容添加合适的表情
          if (typeof optionText === 'string') {
            if (optionText.includes('从不') || optionText.includes('完全不')) {
              return { text: `${optionText} 😊`, value: optionValue };
            } else if (optionText.includes('很少') || optionText.includes('偶尔')) {
              return { text: `${optionText} 🙂`, value: optionValue };
            } else if (optionText.includes('有时')) {
              return { text: `${optionText} 😐`, value: optionValue };
            } else if (optionText.includes('经常')) {
              return { text: `${optionText} 😔`, value: optionValue };
            } else if (optionText.includes('总是') || optionText.includes('几乎总是')) {
              return { text: `${optionText} 😢`, value: optionValue };
            }
          }
        }
        
        // 默认情况下直接返回原始文本和值
        return { text: optionText, value: optionValue };
      });
    },
    
    /**
     * 选择选项
     * @param {Object} e - 事件对象
     */
    selectOption(e) {
      // 原始方法已有，接收conversational-option组件的选择事件
      const index = e.currentTarget ? e.currentTarget.dataset.index : e.detail.index;
      
      // 移除现有选中状态
      this.setData({
        selectedOption: index
      });
      
      // 延迟发送答案，以便用户看到选项反馈
      setTimeout(() => {
        this.sendAnswer(index);
      }, 300);
    },

    /**
     * 根据年级获取对应的样式类型
     * @param {String} grade - 年级
     * @returns {String} 样式类型
     */
    getGradeStyle(grade) {
      if (!grade) return 'default';
      
      const gradeMap = {
        '四年级': 'elementary',
        '五年级': 'elementary',
        '六年级': 'elementary',
        '初一': 'junior',
        '初二': 'junior',
        '初三': 'junior',
        '高一': 'senior',
        '高二': 'senior',
        '高三': 'senior'
      };
      
      return gradeMap[grade] || 'default';
    },
    
    /**
     * 检测选项是否包含表情符号
     * @param {String} text - 选项文本
     * @returns {Boolean} 是否包含表情符号
     */
    hasEmoji(text) {
      if (!text) return false;
      
      // 检测常见表情符号范围
      const emojiRegex = /[\u{1F300}-\u{1F6FF}\u{2600}-\u{26FF}]/u;
      return emojiRegex.test(text);
    },
    
    // 当问题变化时重置状态
    resetState: function() {
      this.setData({
        selectedOption: -1,
        answerStartTime: Date.now(),
        isSpeedAnswer: false
      });
    },
    
    // 获取当前显示的问题（原始问题或转换后的问题）
    getCurrentDisplayQuestion: function() {
      const index = this.properties.currentQuestionIndex;
      
      // 1. 检查索引是否有效
      if (index === undefined || index === null || index < 0) {
        console.error('getCurrentDisplayQuestion: 无效的问题索引');
        return { 
          text: '无效的问题索引', 
          content: '无效的问题索引',
          options: []
        };
      }
      
      // 2. 优先使用会话式问题
      if (this.properties.useConversationalFormat && 
          this.data.conversationalQuestions && 
          this.data.conversationalQuestions.length > index) {
        const conversationalQuestion = this.data.conversationalQuestions[index];
        // 确保返回的会话式问题有效
        if (conversationalQuestion) {
          return conversationalQuestion;
        }
      }
      
      // 3. 如果没有有效的会话式问题，使用原始问题
      if (this.properties.questions && this.properties.questions.length > index) {
        const originalQuestion = this.properties.questions[index];
        if (originalQuestion) {
          return originalQuestion;
        }
      }
      
      // 4. 如果两者都无效，返回一个默认问题对象
      console.error('getCurrentDisplayQuestion: 无法获取有效问题数据');
      return {
        text: '问题数据缺失',
        content: '问题数据缺失',
        options: []
      };
    },

    /**
     * 发送答案
     * @param {Number} optionIndex - 选项索引
     */
    sendAnswer(optionIndex) {
      try {
        const answerTime = Date.now() - this.data.answerStartTime;
        const isSpeedAnswer = answerTime < this.data.speedAnswerThreshold;
        
        // 更新速答状态
        if (this.data.isSpeedAnswer !== isSpeedAnswer) {
          this.setData({
            isSpeedAnswer: isSpeedAnswer
          });
        }
        
        // 获取当前问题数据
        const currentQuestion = this.properties.questions[this.properties.currentQuestionIndex];
        
        // 获取选项值（支持两种数据结构）
        let optionValue;
        if (currentQuestion.options[optionIndex] && typeof currentQuestion.options[optionIndex] === 'object') {
          optionValue = currentQuestion.options[optionIndex].value || optionIndex;
        } else {
          optionValue = optionIndex;
        }
        
        // 触发回答事件，传递选中的选项、答题时间和是否为速答
        this.triggerEvent('answer', {
          questionIndex: this.properties.currentQuestionIndex,
          optionIndex: optionIndex,
          optionValue: optionValue,
          answerTime: answerTime,
          isSpeedAnswer: isSpeedAnswer
        });

        // 如果是速答，触发speedAnswer事件
        if (isSpeedAnswer) {
          this.triggerEvent('speedAnswer', {
            questionIndex: this.properties.currentQuestionIndex,
            answerTime: answerTime
          });
        }

        // 如果答题时间超过阈值，触发犹豫事件
        if (answerTime > 10000) { // 10秒阈值
          this.triggerEvent('hesitation', {
            questionIndex: this.properties.currentQuestionIndex,
            answerTime: answerTime
          });
        }
        
        // 触发nextQuestion事件，通知父组件进入下一题
        this.triggerEvent('nextQuestion', {
          currentQuestionIndex: this.properties.currentQuestionIndex
        });
      } catch (error) {
        console.error('处理答案时发生错误:', error);
      }
    }
  },
  
  // 监听属性变化
  observers: {
    'questions': function(newQuestions) {
      // 当问题数据变化时，检查数据格式并转换为对话形式
      if (newQuestions && newQuestions.length > 0) {
        this.checkQuestionData();
        this.transformQuestionsToConversational();
      }
    },
    'currentQuestionIndex': function(newIndex) {
      // 当问题索引变化时，重置状态
      this.resetState();
    },
    'grade': function(newGrade) {
      // 当年级变化时，重新转换问题
      if (this.properties.questions && this.properties.questions.length > 0) {
        this.transformQuestionsToConversational();
      }
    },
    'useConversationalFormat': function(newValue) {
      // 当转换配置变化时，重新转换问题
      if (newValue && this.properties.questions && this.properties.questions.length > 0) {
        this.transformQuestionsToConversational();
      }
    }
  }
});