// test_frontend_display.js
// 测试前端显示问题的简化脚本

console.log('🧪 开始测试前端显示问题...');

// 模拟页面数据结构
const mockPageData = {
  professionalLiunianData: {
    success: true,
    currentLiunian: {
      year: 2025,
      gan: '乙',
      zhi: '巳',
      fortuneLevel: {
        level: '中吉',
        score: 78,
        levelClass: 'good'
      }
    },
    liunianList: [
      {
        year: 2025,
        age: 0,
        gan: '乙',
        zhi: '巳',
        ganzhi: '乙巳',
        tenGod: '比肩',
        tenGodAnalysis: {
          description: '比肩主导，同类相助，运势平稳'
        },
        fortuneLevel: {
          level: '中吉',
          score: 78
        },
        advice: ['保持稳定', '谨慎投资'],
        activatedShensha: [{ name: '天德贵人' }],
        interactions: [{ description: '五行平衡' }]
      }
    ],
    summary: {
      totalYears: 1,
      averageScore: 78,
      averageScore_display: 78,
      bestYear: {
        year: 2025,
        fortuneLevel: { score: 78 }
      },
      worstYear: {
        year: 2025,
        fortuneLevel: { score: 78 }
      }
    },
    basis: '《三命通会·流年章》黄帝纪元法'
  },
  liunianData: [
    {
      year: '2025年',
      age: '0岁',
      chars: ['乙', '巳'],
      ganzhi: '乙巳',
      title: '比肩主导年',
      desc: '比肩主导，同类相助，运势平稳',
      score: '78分',
      level: '中吉',
      levelClass: 'good',
      levelColor: '#4ecdc4',
      tenGod: '比肩',
      tenGodIcon: '👥',
      advice: '保持稳定；谨慎投资',
      activatedShensha: '天德贵人',
      interactions: '五行平衡',
      current: true
    }
  ],
  loadingStates: {
    liunian: false
  }
};

// 测试 WXML 条件渲染逻辑
function testWXMLConditions(data) {
  console.log('\n📋 测试 WXML 条件渲染逻辑:');
  
  const conditions = {
    // 专业流年分析模块 - 应该总是显示
    showProfessionalModule: true,
    
    // 数据状态指示器
    showStatusIndicator: !!data.professionalLiunianData,
    statusSuccess: data.professionalLiunianData && data.professionalLiunianData.success,
    
    // 当前流年概览
    showCurrentOverview: !!(data.professionalLiunianData && data.professionalLiunianData.currentLiunian),
    
    // 流年列表
    showLiunianList: Array.isArray(data.liunianData) && data.liunianData.length > 0,
    
    // 加载状态
    showLoading: data.loadingStates && data.loadingStates.liunian,
    
    // 流年统计摘要 - 关键条件
    showSummary: !!(data.professionalLiunianData && 
                   data.professionalLiunianData.summary && 
                   (!data.loadingStates || !data.loadingStates.liunian))
  };
  
  console.log('条件检查结果:', conditions);
  
  // 检查关键问题
  const issues = [];
  
  if (!conditions.showStatusIndicator) {
    issues.push('❌ 数据状态指示器不显示');
  }
  
  if (!conditions.statusSuccess) {
    issues.push('❌ 数据状态显示为失败');
  }
  
  if (!conditions.showCurrentOverview) {
    issues.push('❌ 当前流年概览不显示');
  }
  
  if (!conditions.showLiunianList) {
    issues.push('❌ 流年列表不显示');
  }
  
  if (conditions.showLoading) {
    issues.push('⚠️ 仍在加载状态');
  }
  
  if (!conditions.showSummary) {
    issues.push('❌ 流年统计摘要不显示');
  }
  
  if (issues.length === 0) {
    console.log('✅ 所有条件都满足，应该正常显示');
  } else {
    console.log('❌ 发现问题:');
    issues.forEach(issue => console.log(`  ${issue}`));
  }
  
  return { conditions, issues };
}

// 测试数据绑定
function testDataBinding(data) {
  console.log('\n📋 测试数据绑定:');
  
  const bindings = {
    // 专业流年分析模块的数据绑定
    professionalLiunianDataExists: !!data.professionalLiunianData,
    professionalLiunianDataSuccess: data.professionalLiunianData && data.professionalLiunianData.success,
    professionalLiunianDataBasis: data.professionalLiunianData && data.professionalLiunianData.basis,
    
    // 当前流年概览的数据绑定
    currentLiunianExists: data.professionalLiunianData && !!data.professionalLiunianData.currentLiunian,
    currentLiunianYear: data.professionalLiunianData && data.professionalLiunianData.currentLiunian && data.professionalLiunianData.currentLiunian.year,
    currentLiunianGan: data.professionalLiunianData && data.professionalLiunianData.currentLiunian && data.professionalLiunianData.currentLiunian.gan,
    currentLiunianZhi: data.professionalLiunianData && data.professionalLiunianData.currentLiunian && data.professionalLiunianData.currentLiunian.zhi,
    
    // 流年列表的数据绑定
    liunianDataExists: !!data.liunianData,
    liunianDataIsArray: Array.isArray(data.liunianData),
    liunianDataLength: data.liunianData ? data.liunianData.length : 0,
    
    // 流年统计摘要的数据绑定
    summaryExists: data.professionalLiunianData && !!data.professionalLiunianData.summary,
    summaryAverageScore: data.professionalLiunianData && data.professionalLiunianData.summary && data.professionalLiunianData.summary.averageScore_display,
    summaryBestYear: data.professionalLiunianData && data.professionalLiunianData.summary && data.professionalLiunianData.summary.bestYear,
    summaryWorstYear: data.professionalLiunianData && data.professionalLiunianData.summary && data.professionalLiunianData.summary.worstYear
  };
  
  console.log('数据绑定检查:', bindings);
  
  return bindings;
}

// 生成调试报告
function generateDebugReport(data) {
  console.log('\n📊 生成调试报告:');
  
  const wxmlTest = testWXMLConditions(data);
  const bindingTest = testDataBinding(data);
  
  const report = {
    timestamp: new Date().toLocaleString(),
    dataStructure: {
      professionalLiunianData: !!data.professionalLiunianData,
      liunianData: !!data.liunianData,
      loadingStates: !!data.loadingStates
    },
    wxmlConditions: wxmlTest.conditions,
    dataBindings: bindingTest,
    issues: wxmlTest.issues,
    recommendations: []
  };
  
  // 生成建议
  if (wxmlTest.issues.length === 0) {
    report.recommendations.push('✅ 数据结构正常，检查微信小程序缓存和编译');
    report.recommendations.push('🔧 在微信开发者工具中：工具 -> 清除缓存 -> 清除所有');
    report.recommendations.push('🔄 重新编译项目');
    report.recommendations.push('📱 检查真机调试是否正常');
  } else {
    report.recommendations.push('❌ 数据结构存在问题，需要修复数据设置逻辑');
    report.recommendations.push('🔍 检查 setData 调用是否正确执行');
    report.recommendations.push('📋 验证数据计算过程是否有错误');
  }
  
  console.log('调试报告:', report);
  
  return report;
}

// 运行测试
console.log('🎯 使用模拟数据运行测试...');
const debugReport = generateDebugReport(mockPageData);

console.log('\n🚀 测试结论:');
if (debugReport.issues.length === 0) {
  console.log('✅ 模拟数据测试通过，问题可能在于:');
  console.log('  1. 微信小程序缓存问题');
  console.log('  2. 代码编译问题');
  console.log('  3. 实际运行时数据与模拟数据不符');
  console.log('  4. CSS样式被覆盖或未正确加载');
} else {
  console.log('❌ 模拟数据测试发现问题:');
  debugReport.issues.forEach(issue => console.log(`  ${issue}`));
}

console.log('\n📋 建议的下一步操作:');
console.log('1. 在微信开发者工具中查看实际的页面数据');
console.log('2. 检查控制台是否有JavaScript错误');
console.log('3. 验证调试信息是否正确显示');
console.log('4. 清除缓存并重新编译');

module.exports = {
  testWXMLConditions,
  testDataBinding,
  generateDebugReport
};
