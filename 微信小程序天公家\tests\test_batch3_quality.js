/**
 * 第三批次数据质量测试
 * 验证先秦、魏晋南北朝时期名人数据的质量和准确性
 */

const batch3Data = require('../data/batch3_complete_celebrities');
const completeDatabase = require('../data/complete_celebrities_database_v3');

class Batch3QualityTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      issues: []
    };
  }

  /**
   * 测试数据结构完整性
   */
  testDataStructure() {
    console.log('🔍 测试数据结构完整性...');
    
    const requiredFields = [
      'id', 'basicInfo', 'bazi', 'pattern', 'lifeEvents', 'verification'
    ];
    
    const basicInfoFields = ['name', 'birthYear', 'deathYear', 'dynasty', 'occupation'];
    const baziFields = ['year', 'month', 'day', 'hour', 'fullBazi'];
    const patternFields = ['mainPattern', 'dayMaster', 'yongshen', 'confidence'];
    const verificationFields = ['algorithmMatch', 'ancientTextEvidence', 'expertValidation'];
    
    batch3Data.celebrities.forEach((celebrity, index) => {
      this.testResults.totalTests++;
      
      // 检查顶级字段
      const missingFields = requiredFields.filter(field => !celebrity[field]);
      if (missingFields.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo?.name || `第${index+1}位`}: 缺少字段 ${missingFields.join(', ')}`);
        return;
      }
      
      // 检查basicInfo字段
      const missingBasicInfo = basicInfoFields.filter(field => !celebrity.basicInfo[field]);
      if (missingBasicInfo.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: basicInfo缺少字段 ${missingBasicInfo.join(', ')}`);
        return;
      }
      
      // 检查bazi字段
      const missingBazi = baziFields.filter(field => !celebrity.bazi[field]);
      if (missingBazi.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: bazi缺少字段 ${missingBazi.join(', ')}`);
        return;
      }
      
      // 检查pattern字段
      const missingPattern = patternFields.filter(field => !celebrity.pattern[field]);
      if (missingPattern.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: pattern缺少字段 ${missingPattern.join(', ')}`);
        return;
      }
      
      // 检查verification字段
      const missingVerification = verificationFields.filter(field => !celebrity.verification[field]);
      if (missingVerification.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: verification缺少字段 ${missingVerification.join(', ')}`);
        return;
      }
      
      this.testResults.passedTests++;
    });
    
    console.log(`✅ 数据结构测试完成: ${this.testResults.passedTests}/${this.testResults.totalTests} 通过`);
  }

  /**
   * 测试历史准确性
   */
  testHistoricalAccuracy() {
    console.log('🔍 测试历史准确性...');
    
    const historicalChecks = [
      {
        name: '孔子',
        expectedDynasty: '春秋',
        expectedBirthYear: -551,
        expectedPattern: '正印格'
      },
      {
        name: '孟子',
        expectedDynasty: '战国',
        expectedBirthYear: -372,
        expectedPattern: '正印格'
      },
      {
        name: '曹操',
        expectedDynasty: '三国',
        expectedBirthYear: 155,
        expectedPattern: '七杀格'
      },
      {
        name: '王羲之',
        expectedDynasty: '东晋',
        expectedBirthYear: 303,
        expectedPattern: '正印格'
      },
      {
        name: '陶渊明',
        expectedDynasty: '东晋',
        expectedBirthYear: 365,
        expectedPattern: '正财格'
      }
    ];
    
    historicalChecks.forEach(check => {
      this.testResults.totalTests++;
      
      const celebrity = batch3Data.celebrities.find(c => c.basicInfo.name === check.name);
      
      if (!celebrity) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`未找到历史人物: ${check.name}`);
        return;
      }
      
      let hasError = false;
      
      if (celebrity.basicInfo.dynasty !== check.expectedDynasty) {
        hasError = true;
        this.testResults.issues.push(`${check.name}: 朝代错误，期望${check.expectedDynasty}，实际${celebrity.basicInfo.dynasty}`);
      }
      
      if (celebrity.basicInfo.birthYear !== check.expectedBirthYear) {
        hasError = true;
        this.testResults.issues.push(`${check.name}: 出生年份错误，期望${check.expectedBirthYear}，实际${celebrity.basicInfo.birthYear}`);
      }
      
      if (celebrity.pattern.mainPattern !== check.expectedPattern) {
        hasError = true;
        this.testResults.issues.push(`${check.name}: 格局错误，期望${check.expectedPattern}，实际${celebrity.pattern.mainPattern}`);
      }
      
      if (hasError) {
        this.testResults.failedTests++;
      } else {
        this.testResults.passedTests++;
      }
    });
    
    console.log(`✅ 历史准确性测试完成: ${this.testResults.passedTests - this.testResults.failedTests}/${historicalChecks.length} 通过`);
  }

  /**
   * 测试古籍文献引用
   */
  testAncientTextEvidence() {
    console.log('🔍 测试古籍文献引用...');

    const validSources = [
      '《史记》', '《汉书》', '《三国志》', '《晋书》', '《南史》', '《北史》',
      '《论语》', '《孟子》', '《庄子》', '《韩非子》', '《荀子》', '《管子》',
      '《滴天髓》', '《子平真诊》', '《兰亭序》', '《归园田居》', '《道德经》',
      '《晏子春秋》', '《左传》', '《国语》', '《战国策》', '《吕氏春秋》',
      '《墨子》', '《商君书》', '《韩非子》', '《孙子兵法》', '《六韬》'
    ];

    batch3Data.celebrities.forEach(celebrity => {
      this.testResults.totalTests++;

      if (!celebrity.verification.ancientTextEvidence || !Array.isArray(celebrity.verification.ancientTextEvidence)) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 缺少古籍文献引用`);
        return;
      }

      let hasValidSource = false;
      celebrity.verification.ancientTextEvidence.forEach(evidence => {
        // 检查是否包含任何有效的古籍来源（支持部分匹配，如《史记·孔子世家》包含《史记》）
        if (validSources.some(source => evidence.includes(source.replace('《', '').replace('》', '')))) {
          hasValidSource = true;
        }
      });

      if (!hasValidSource) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 古籍文献引用不规范 - ${celebrity.verification.ancientTextEvidence.join('; ')}`);
      } else {
        this.testResults.passedTests++;
      }
    });

    console.log(`✅ 古籍文献引用测试完成`);
  }

  /**
   * 测试朝代分布
   */
  testDynastyDistribution() {
    console.log('🔍 测试朝代分布...');
    
    const dynastyStats = {};
    batch3Data.celebrities.forEach(celebrity => {
      const dynasty = celebrity.basicInfo.dynasty;
      dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
    });
    
    const expectedDistribution = {
      '春秋': { min: 5, max: 8 },
      '战国': { min: 10, max: 15 },
      '三国': { min: 4, max: 8 },
      '东晋': { min: 4, max: 8 },
      '南朝': { min: 2, max: 5 },
      '北朝': { min: 2, max: 5 }
    };
    
    Object.entries(expectedDistribution).forEach(([dynasty, range]) => {
      this.testResults.totalTests++;
      
      const actualCount = dynastyStats[dynasty] || 0;
      
      if (actualCount >= range.min && actualCount <= range.max) {
        this.testResults.passedTests++;
      } else {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${dynasty}朝代分布异常: 期望${range.min}-${range.max}位，实际${actualCount}位`);
      }
    });
    
    console.log('📊 朝代分布统计:');
    Object.entries(dynastyStats)
      .sort((a, b) => b[1] - a[1])
      .forEach(([dynasty, count]) => {
        console.log(`   - ${dynasty}: ${count}位`);
      });
    
    console.log(`✅ 朝代分布测试完成`);
  }

  /**
   * 测试验证分数质量
   */
  testVerificationScores() {
    console.log('🔍 测试验证分数质量...');
    
    let totalScore = 0;
    let lowScoreCount = 0;
    const lowScoreThreshold = 0.85;
    
    batch3Data.celebrities.forEach(celebrity => {
      this.testResults.totalTests++;
      
      const score = celebrity.verification.algorithmMatch;
      
      if (typeof score !== 'number' || score < 0 || score > 1) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 验证分数格式错误 (${score})`);
        return;
      }
      
      totalScore += score;
      
      if (score < lowScoreThreshold) {
        lowScoreCount++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 验证分数偏低 (${score})`);
      }
      
      this.testResults.passedTests++;
    });
    
    const averageScore = totalScore / batch3Data.celebrities.length;
    
    console.log(`📊 验证分数统计:`);
    console.log(`   - 平均分数: ${averageScore.toFixed(3)}`);
    console.log(`   - 低分数量: ${lowScoreCount}位 (< ${lowScoreThreshold})`);
    console.log(`   - 分数范围: ${Math.min(...batch3Data.celebrities.map(c => c.verification.algorithmMatch)).toFixed(3)} - ${Math.max(...batch3Data.celebrities.map(c => c.verification.algorithmMatch)).toFixed(3)}`);
    
    console.log(`✅ 验证分数测试完成`);
  }

  /**
   * 执行所有测试
   */
  runAllTests() {
    console.log('🚀 开始第三批次数据质量测试');
    console.log('============================================================');
    
    try {
      // 重置测试结果
      this.testResults = {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        issues: []
      };
      
      // 执行各项测试
      this.testDataStructure();
      this.testHistoricalAccuracy();
      this.testAncientTextEvidence();
      this.testDynastyDistribution();
      this.testVerificationScores();
      
      // 计算总体结果
      const successRate = (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1);
      const qualityGrade = successRate >= 95 ? '优秀' : successRate >= 85 ? '良好' : '需改进';
      
      console.log('============================================================');
      console.log('🎉 第三批次数据质量测试完成！');
      console.log(`📊 测试总数: ${this.testResults.totalTests}`);
      console.log(`✅ 通过测试: ${this.testResults.passedTests}`);
      console.log(`❌ 失败测试: ${this.testResults.failedTests}`);
      console.log(`📈 成功率: ${successRate}%`);
      console.log(`🏆 质量等级: ${qualityGrade}`);
      
      if (this.testResults.issues.length > 0) {
        console.log(`⚠️  发现问题 (前5个):`);
        this.testResults.issues.slice(0, 5).forEach(issue => {
          console.log(`   - ${issue}`);
        });
        if (this.testResults.issues.length > 5) {
          console.log(`   ... 还有 ${this.testResults.issues.length - 5} 个问题`);
        }
      }
      
      return {
        success: true,
        totalTests: this.testResults.totalTests,
        passedTests: this.testResults.passedTests,
        failedTests: this.testResults.failedTests,
        successRate: parseFloat(successRate),
        qualityGrade: qualityGrade,
        issues: this.testResults.issues
      };
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new Batch3QualityTester();
  tester.runAllTests();
}

module.exports = Batch3QualityTester;
