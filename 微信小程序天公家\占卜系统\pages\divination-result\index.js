// pages/divination-result/index.js
// 天公师兄占卜结果展示页面

const DivinationCalculator = require('../../utils/divination_calculator');
const NavigationColor = require('../../utils/navigation_color');
const PosterGenerator = require('../../utils/poster_generator');

Page({
  data: {
    // 主题相关
    themeClass: 'tarot-theme',

    // 占卜结果
    result: null,
    analysis: null,

    // 滚动相关
    scrollTop: 0,
    scrollEnabled: true,
    
    // 问题类型映射
    questionTypeMap: {
      'lost': '失物寻找',
      'wealth': '求财问事',
      'travel': '出行安全',
      'love': '感情问题',
      'study': '学习考试',
      'career': '工作事业',
      'health': '健康状况',
      'weather': '天气预测',
      'disease': '疾病医疗',
      'food': '饮食宜忌',
      'other': '其他事务'
    },
    questionTypeText: '',
    
    // 界面控制
    showCalculation: true,
    showCalculationDetail: false,
    showShareModal: false
  },

  onLoad(options) {
    console.log('✅ 占卜结果页面加载，海报已优化（删除二维码和副标题）', options);

    // 处理__route__未定义问题
    try {
      if (typeof __route__ === 'undefined' && typeof getApp()._fixRouteIssue === 'function') {
        getApp()._fixRouteIssue('pages/divination-result/index');
        console.log('已尝试修复路由问题');
      } else if (typeof __route__ === 'undefined') {
        console.log('__route__未定义，页面路径手动设置为:"pages/divination-result/index"');
      }
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }

    try {
      // 显示加载状态
      wx.showLoading({
        title: '正在解析占卜结果...',
        mask: true
      });

      // 设置导航栏颜色
      NavigationColor.setNavigationBarColorByRole('tarot');

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '洞察先机'
      });

      // 解析占卜数据
      if (options.data) {
        try {
          const divinationData = JSON.parse(decodeURIComponent(options.data));
          this.processDivinationData(divinationData);
        } catch (error) {
          console.error('解析占卜数据失败:', error);
          wx.hideLoading();
          this.showError('数据解析失败，请重新占卜');
        }
      } else {
        wx.hideLoading();
        this.showError('缺少占卜数据，请重新开始');
      }

    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.hideLoading();
      this.showError('页面加载失败，请重试');
    }
  },

  /**
   * 处理占卜数据
   */
  processDivinationData(data) {
    console.log('处理占卜数据:', data);
    
    try {
      // 使用占卜计算器计算结果
      const result = DivinationCalculator.calculate(data);
      console.log('占卜计算结果:', result);
      
      // 生成详细分析
      const analysis = DivinationCalculator.generateAnalysis(result);
      console.log('占卜分析结果:', analysis);
      console.log('智能指引:', analysis.specific.title);
      console.log('针对性建议:', analysis.specific.details);
      
      // 获取问题类型文本
      const questionTypeText = this.data.questionTypeMap[result.questionType] || '其他事务';
      
      this.setData({
        result: result,
        analysis: analysis,
        questionTypeText: questionTypeText
      });

      // 保存到本地存储（用于历史记录）
      this.saveToHistory(result, analysis);

      // 隐藏加载状态
      wx.hideLoading();

    } catch (error) {
      console.error('处理占卜数据失败:', error);
      wx.hideLoading();
      this.showError('占卜计算失败，请重新尝试');
    }
  },

  /**
   * 保存到历史记录
   */
  saveToHistory(result, analysis) {
    try {
      const historyKey = 'divination_history';
      const history = wx.getStorageSync(historyKey) || [];
      
      const record = {
        id: Date.now().toString(),
        timestamp: result.timestamp,
        questionType: result.questionType,
        questionText: result.questionText,
        godName: result.god.name,
        fortune: result.god.fortune,
        method: result.method,
        date: new Date().toLocaleDateString()
      };
      
      // 添加到历史记录开头
      history.unshift(record);
      
      // 只保留最近20条记录
      if (history.length > 20) {
        history.splice(20);
      }
      
      wx.setStorageSync(historyKey, history);
      console.log('占卜记录已保存到历史');
      
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    // 2秒后返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  /**
   * 切换计算过程显示
   */
  toggleCalculation() {
    this.setData({
      showCalculationDetail: !this.data.showCalculationDetail
    });
  },

  /**
   * 分享结果
   */
  shareResult() {
    this.setData({
      showShareModal: true
    });
  },

  /**
   * 隐藏分享弹窗
   */
  hideShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  /**
   * 分享给朋友
   */
  shareToFriend() {
    const result = this.data.result;
    const shareContent = this.generateShareContent(result);
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 触发分享
    wx.shareAppMessage({
      title: shareContent.title,
      desc: shareContent.desc,
      path: `/pages/divination-result/index?shared=true`,
      success: () => {
        console.log('分享成功');
        this.hideShareModal();
      },
      fail: (error) => {
        console.error('分享失败:', error);
        wx.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 保存到相册
   */
  async saveToAlbum() {
    try {
      wx.showLoading({
        title: '生成海报中...',
        mask: true
      });

      console.log('✅ 生成海报（已删除二维码和副标题）');

      // 生成海报
      const posterPath = await PosterGenerator.generatePoster(this.data.result, this.data.analysis);

      // 保存到相册
      await PosterGenerator.saveToAlbum(posterPath);

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      this.hideShareModal();

    } catch (error) {
      console.error('保存海报失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成分享内容
   */
  generateShareContent(result) {
    const god = result.god;
    const title = `天公师兄n/遇事不决  可问天公：${god.name}`;
    const desc = `${god.description} - ${god.advice}`;

    return { title, desc };
  },

  /**
   * 返回首页
   */
  goHome() {
    console.log('✅ 返回首页按钮点击');
    wx.reLaunch({
      url: '/pages/assessment-hub/index'
    });
  },

  /**
   * 再次占卜
   */
  newDivination() {
    // 检查是否同一天已经占卜过相同问题
    const today = new Date().toDateString();
    const lastDivinationDate = wx.getStorageSync('last_divination_date');
    const lastQuestion = wx.getStorageSync('last_question');
    
    if (lastDivinationDate === today && lastQuestion === this.data.result.questionText) {
      wx.showModal({
        title: '提醒',
        content: '同一问题一日内不宜重复占卜，再占不验。是否继续？',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.goToNewDivination();
          }
        }
      });
    } else {
      this.goToNewDivination();
    }
  },

  /**
   * 跳转到新占卜
   */
  goToNewDivination() {
    // 保存当前占卜信息
    const today = new Date().toDateString();
    wx.setStorageSync('last_divination_date', today);
    wx.setStorageSync('last_question', this.data.result.questionText);
    
    // 跳转到信息收集页面
    wx.redirectTo({
      url: '/pages/divination-input/index'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const result = this.data.result;
    if (result) {
      const shareContent = this.generateShareContent(result);
      return {
        title: shareContent.title,
        desc: shareContent.desc,
        path: `/pages/assessment-hub/index`
      };
    }
    
    return {
      title: '天公师兄占卜',
      desc: '遇事不决，可问天公',
      path: `/pages/assessment-hub/index`
    };
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline() {
    const result = this.data.result;
    if (result) {
      const shareContent = this.generateShareContent(result);
      return {
        title: shareContent.title,
        query: 'shared=true'
      };
    }
    
    return {
      title: '天公师兄·李淳风六壬时课 - 无事不占，一事一占'
    };
  },

  /**
   * 滚动事件处理
   */
  onScroll(e) {
    this.setData({
      scrollTop: e.detail.scrollTop
    });
  },

  /**
   * 滚动到顶部
   */
  scrollToTop() {
    this.setData({
      scrollTop: 0
    });
  },

  /**
   * 滚动到指定位置
   */
  scrollToPosition(scrollTop) {
    this.setData({
      scrollTop: scrollTop
    });
  }
});
