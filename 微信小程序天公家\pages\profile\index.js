// pages/profile/index.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLogin: false,
    hasUserInfo: false,
    hasPhoneNumber: false,
    isAdminUser: false,
    avatarList: [
      { url: '/assets/avatars/pet_1.svg', type: 'pet', id: 'pet_1' },
      { url: '/assets/avatars/mecha_1.svg', type: 'mecha', id: 'mecha_1' },
      { url: '/assets/avatars/healing_1.svg', type: 'healing', id: 'healing_1' }
    ],
    currentAvatarIndex: 0,
    assessmentRecords: [],
    walletInfo: {
      balance: 0,
      coupons: [],
      packages: [],
      autoRenew: false
    },
    // 修改昵称相关
    showNicknameModal: false,
    newNickname: '',
    nicknameEditLimit: 3,
    // 充值相关
    showRechargeModal: false,
    rechargePackages: [
      { id: 'basic', name: '心灵呵护', price: 39.9, description: '3次专业测评+个性化建议' },
      { id: 'standard', name: '心灵守护', price: 99.9, description: '10次专业测评+个性化建议+1次专家咨询' },
      { id: 'premium', name: '心灵伙伴', price: 199.9, description: '无限测评+个性化建议+3次专家咨询' }
    ],
    selectedPackage: -1,
    // 头像预览
    showAvatarPreview: false,
    previewAvatarUrl: '',
    offlineMode: false,
    syncStatus: {
      pendingCount: 0,
      lastSyncTime: null,
      isSyncing: false
    },
    assessmentCount: 0,
    lastAssessmentDate: null,
  },

  onLoad: function(options) {
    this.checkLoginStatus();
    this.getAssessmentRecords();
    this.getWalletInfo();
    this.getNicknameEditLimit();
    this.checkAdminStatus();
    this.getUserInfo();
    this.getAssessmentHistory();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        isLogin: true,
        hasUserInfo: true,
        hasPhoneNumber: !!app.globalData.userInfo.phoneNumber
      });
    }
  },

  // 获取用户信息
  getUserInfo: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },

  // 获取用户授权
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo;
        wx.setStorageSync('userInfo', userInfo);
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });
      }
    });
  },

  // 获取手机号
  getPhoneNumber: function(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      wx.showLoading({
        title: '验证中...'
      });

      // 模拟验证成功
      setTimeout(() => {
        const app = getApp();
        const mockPhoneNumber = '138****8888'; // 模拟手机号
        if (app.globalData.userInfo) {
          app.globalData.userInfo.phoneNumber = mockPhoneNumber;
        }
        this.setData({
          hasPhoneNumber: true,
          'userInfo.phoneNumber': mockPhoneNumber
        });
        wx.hideLoading();
        wx.showToast({
          title: '验证成功',
          icon: 'success'
        });
        // 完成注册后刷新页面数据
        this.getAssessmentRecords();
        this.getWalletInfo();
      }, 1000);
    } else {
      wx.showToast({
        title: '需要授权手机号才能继续使用',
        icon: 'none'
      });
    }
  },

  onShow: function() {
    // 每次页面显示时刷新数据
    this.getAssessmentRecords();
    this.getWalletInfo();
    this.getAssessmentHistory();
    
    // 更新离线模式状态
    this.updateOfflineStatus();
  },

  // 获取测评记录
  getAssessmentRecords: function() {
    // 模拟从服务器获取测评记录
    const mockRecords = [
      {
        id: 'rec001',
        title: '心理健康初筛',
        description: '完成度: 100%',
        date: '2023-10-15 14:30',
        counselorAvatar: '/assets/icons/assessment.png'
      },
      {
        id: 'rec002',
        title: '情绪管理能力测评',
        description: '完成度: 100%',
        date: '2023-10-10 09:15',
        counselorAvatar: '/assets/icons/assessment.png'
      },
      {
        id: 'rec003',
        title: '学习压力评估',
        description: '完成度: 100%',
        date: '2023-09-28 16:45',
        counselorAvatar: '/assets/icons/assessment.png'
      }
    ];

    this.setData({
      assessmentRecords: mockRecords
    });

    // 实际应用中应该从云数据库获取
    // wx.cloud.callFunction({
    //   name: 'getAssessmentRecords',
    //   success: res => {
    //     this.setData({
    //       assessmentRecords: res.result.data
    //     });
    //   },
    //   fail: err => {
    //     console.error('获取测评记录失败', err);
    //     wx.showToast({
    //       title: '获取记录失败',
    //       icon: 'none'
    //     });
    //   }
    // });
  },

  // 获取钱包信息
  getWalletInfo: function() {
    // 模拟从服务器获取钱包信息
    const mockWalletInfo = {
      balance: 128.5,
      coupons: [
        { id: 'c001', amount: 10, description: '新用户专享券', expireDate: '2023-12-31' },
        { id: 'c002', amount: 20, description: '生日特惠券', expireDate: '2023-11-30' },
        { id: 'c003', amount: 5, description: '周末专享券', expireDate: '2023-11-15' }
      ],
      packages: [
        { id: 'p001', name: '心灵守护', description: '10次专业测评+个性化建议', daysLeft: 45 },
        { id: 'p002', name: '专家咨询', description: '3次专家一对一咨询', daysLeft: 2 }
      ],
      autoRenew: true
    };

    this.setData({
      walletInfo: mockWalletInfo
    });

    // 实际应用中应该从云数据库获取
    // wx.cloud.callFunction({
    //   name: 'getWalletInfo',
    //   success: res => {
    //     this.setData({
    //       walletInfo: res.result.data
    //     });
    //   },
    //   fail: err => {
    //     console.error('获取钱包信息失败', err);
    //     wx.showToast({
    //       title: '获取钱包信息失败',
    //       icon: 'none'
    //     });
    //   }
    // });
  },

  // 获取昵称修改次数限制
  getNicknameEditLimit: function() {
    // 模拟从服务器获取昵称修改次数限制
    this.setData({
      nicknameEditLimit: 3
    });

    // 实际应用中应该从云数据库获取
    // wx.cloud.callFunction({
    //   name: 'getNicknameEditLimit',
    //   success: res => {
    //     this.setData({
    //       nicknameEditLimit: res.result.limit
    //     });
    //   },
    //   fail: err => {
    //     console.error('获取昵称修改次数限制失败', err);
    //   }
    // });
  },

  // 头像滑动切换事件
  onAvatarSwiperChange: function(e) {
    this.setData({
      currentAvatarIndex: e.detail.current
    });
  },

  // 预览动态头像
  previewDynamicAvatar: function(e) {
    const currentAvatar = this.data.avatarList[this.data.currentAvatarIndex];
    this.setData({
      showAvatarPreview: true,
      previewAvatarUrl: currentAvatar.url
    });

    // 实际应用中应该加载Lottie动画
    // 这里只是模拟
    wx.showToast({
      title: '长按预览动态效果',
      icon: 'none'
    });
  },

  // 关闭头像预览
  closeAvatarPreview: function() {
    this.setData({
      showAvatarPreview: false
    });
  },

  // 显示昵称修改弹窗
  showNicknameEdit: function() {
    if (this.data.nicknameEditLimit <= 0) {
      wx.showToast({
        title: '本周修改次数已用完',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showNicknameModal: true,
      newNickname: this.data.userInfo.name
    });
  },

  // 昵称输入事件
  onNicknameInput: function(e) {
    this.setData({
      newNickname: e.detail.value
    });
  },

  // 取消昵称修改
  cancelNicknameEdit: function() {
    this.setData({
      showNicknameModal: false
    });
  },

  // 确认昵称修改
  confirmNicknameEdit: function() {
    if (!this.data.newNickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    // 更新用户昵称
    const app = getApp();
    app.globalData.userInfo.name = this.data.newNickname;

    this.setData({
      'userInfo.name': this.data.newNickname,
      showNicknameModal: false,
      nicknameEditLimit: this.data.nicknameEditLimit - 1
    });

    wx.showToast({
      title: '昵称修改成功',
      icon: 'success'
    });

    // 实际应用中应该更新到云数据库
    // wx.cloud.callFunction({
    //   name: 'updateUserNickname',
    //   data: {
    //     nickname: this.data.newNickname
    //   },
    //   success: res => {
    //     this.setData({
    //       'userInfo.name': this.data.newNickname,
    //       showNicknameModal: false,
    //       nicknameEditLimit: this.data.nicknameEditLimit - 1
    //     });

    //     wx.showToast({
    //       title: '昵称修改成功',
    //       icon: 'success'
    //     });
    //   },
    //   fail: err => {
    //     console.error('修改昵称失败', err);
    //     wx.showToast({
    //       title: '修改失败，请重试',
    //       icon: 'none'
    //     });
    //   }
    // });
  },

  // 查看所有测评记录
  viewAllRecords: function() {
    wx.showToast({
      title: '查看所有记录功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到记录列表页面
    // wx.navigateTo({
    //   url: '/pages/profile/records/index'
    // });
  },

  // 查看测评详情
  viewAssessmentDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '查看详情功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到详情页面
    // wx.navigateTo({
    //   url: `/pages/assessment/result_enhanced?id=${id}`
    // });
  },

  // 显示删除测评记录选项
  showDeleteOption: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showActionSheet({
      itemList: ['删除记录'],
      itemColor: '#FF6B6B',
      success: res => {
        if (res.tapIndex === 0) {
          this.deleteAssessmentRecord(id);
        }
      }
    });
  },

  // 删除测评记录
  deleteAssessmentRecord: function(id) {
    // 生物验证
    wx.showModal({
      title: '安全验证',
      content: '删除记录需要进行生物识别验证',
      confirmText: '验证',
      success: res => {
        if (res.confirm) {
          // 模拟生物验证
          wx.startSoterAuthentication({
            requestAuthModes: ['fingerPrint', 'facial'],
            challenge: 'challenge',
            authContent: '请验证身份以删除记录',
            success: () => {
              // 验证成功后删除记录
              const newRecords = this.data.assessmentRecords.filter(item => item.id !== id);
              this.setData({
                assessmentRecords: newRecords
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 实际应用中应该从云数据库删除
              // wx.cloud.callFunction({
              //   name: 'deleteAssessmentRecord',
              //   data: { id },
              //   success: res => {
              //     const newRecords = this.data.assessmentRecords.filter(item => item.id !== id);
              //     this.setData({
              //       assessmentRecords: newRecords
              //     });

              //     wx.showToast({
              //       title: '删除成功',
              //       icon: 'success'
              //     });
              //   },
              //   fail: err => {
              //     console.error('删除记录失败', err);
              //     wx.showToast({
              //       title: '删除失败，请重试',
              //       icon: 'none'
              //     });
              //   }
              // });
            },
            fail: err => {
              console.error('生物验证失败', err);
              wx.showToast({
                title: '验证失败，无法删除',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 查看钱包明细
  viewWalletDetail: function() {
    wx.showToast({
      title: '钱包明细功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到钱包明细页面
    // wx.navigateTo({
    //   url: '/pages/profile/wallet/detail'
    // });
  },

  // 显示充值选项
  showRechargeOptions: function() {
    // 检查用户年龄，如果小于14岁则不显示充值入口
    const app = getApp();
    const userAge = app.globalData.userInfo.age;
    
    if (userAge < 14) {
      wx.showModal({
        title: '温馨提示',
        content: '未满14岁用户暂不支持充值功能，请在家长陪同下使用',
        showCancel: false
      });
      return;
    }
    
    this.setData({
      showRechargeModal: true,
      selectedPackage: -1
    });
  },

  // 选择充值套餐
  selectPackage: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedPackage: index
    });
  },

  // 取消充值
  cancelRecharge: function() {
    this.setData({
      showRechargeModal: false
    });
  },

  // 确认充值
  confirmRecharge: function() {
    if (this.data.selectedPackage === -1) {
      wx.showToast({
        title: '请选择套餐',
        icon: 'none'
      });
      return;
    }

    const selectedPackage = this.data.rechargePackages[this.data.selectedPackage];
    
    // 模拟支付流程
    wx.showLoading({
      title: '支付处理中'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      
      this.setData({
        showRechargeModal: false
      });
      
      wx.showToast({
        title: '充值成功',
        icon: 'success'
      });
      
      // 更新钱包余额
      this.setData({
        'walletInfo.balance': this.data.walletInfo.balance + selectedPackage.price
      });
      
      // 实际应用中应该调用支付接口
      // wx.requestPayment({
      //   timeStamp: '',
      //   nonceStr: '',
      //   package: '',
      //   signType: 'MD5',
      //   paySign: '',
      //   success: res => {
      //     this.setData({
      //       showRechargeModal: false
      //     });
      //     
      //     wx.showToast({
      //       title: '充值成功',
      //       icon: 'success'
      //     });
      //     
      //     // 刷新钱包信息
      //     this.getWalletInfo();
      //   },
      //   fail: err => {
      //     console.error('支付失败', err);
      //     wx.showToast({
      //       title: '支付失败，请重试',
      //       icon: 'none'
      //     });
      //   }
      // });
    }, 1500);
  },

  // 使用优惠券
  useCoupon: function(e) {
    const id = e.currentTarget.dataset.id;
    const coupon = this.data.walletInfo.coupons.find(item => item.id === id);
    
    wx.showModal({
      title: '使用优惠券',
      content: `确定使用${coupon.amount}元${coupon.description}吗？`,
      success: res => {
        if (res.confirm) {
          wx.showToast({
            title: '优惠券使用功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 切换自动续费
  toggleAutoRenew: function(e) {
    const autoRenew = e.detail.value;
    
    if (!autoRenew) {
      // 关闭自动续费需要确认
      wx.showModal({
        title: '关闭自动续费',
        content: '关闭后将放弃剩余3次服务，确定要关闭吗？',
        success: res => {
          if (res.confirm) {
            this.setData({
              'walletInfo.autoRenew': false
            });
            
            wx.showToast({
              title: '已关闭自动续费',
              icon: 'success'
            });
            
            // 实际应用中应该更新到云数据库
            // wx.cloud.callFunction({
            //   name: 'updateAutoRenew',
            //   data: { autoRenew: false },
            //   success: res => {
            //     wx.showToast({
            //       title: '已关闭自动续费',
            //       icon: 'success'
            //     });
            //   },
            //   fail: err => {
            //     console.error('更新自动续费设置失败', err);
            //     // 恢复开关状态
            //     this.setData({
            //       'walletInfo.autoRenew': true
            //     });
            //   }
            // });
          } else {
            // 用户取消，恢复开关状态
            this.setData({
              'walletInfo.autoRenew': true
            });
          }
        }
      });
    } else {
      this.setData({
        'walletInfo.autoRenew': true
      });
      
      wx.showToast({
        title: '已开启自动续费',
        icon: 'success'
      });
      
      // 实际应用中应该更新到云数据库
      // wx.cloud.callFunction({
      //   name: 'updateAutoRenew',
      //   data: { autoRenew: true },
      //   success: res => {
      //     wx.showToast({
      //       title: '已开启自动续费',
      //       icon: 'success'
      //     });
      //   },
      //   fail: err => {
      //     console.error('更新自动续费设置失败', err);
      //     // 恢复开关状态
      //     this.setData({
      //       'walletInfo.autoRenew': false
      //     });
      //   }
      // });
    }
  },

  // 导航到隐私设置页面
  navigateToPrivacySettings: function() {
    wx.showToast({
      title: '隐私设置功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到隐私设置页面
    // wx.navigateTo({
    //   url: '/pages/profile/privacy/index'
    // });
  },

  // 导航到账号安全页面
  navigateToAccountSecurity: function() {
    wx.showToast({
      title: '账号安全功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到账号安全页面
    // wx.navigateTo({
    //   url: '/pages/profile/security/index'
    // });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: res => {
        if (res.confirm) {
          // 清除登录状态
          const app = getApp();
          app.globalData.isAuthorized = false;
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 跳转到登录页面
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      }
    });
  },

  // 检查管理员权限
  checkAdminStatus: function() {
    // 实际应用中，应该从服务器获取用户权限信息
    // 这里为了演示，使用模拟数据
    if (app.globalData.userInfo && app.globalData.userInfo.userId) {
      // 这里可以调用云函数检查用户权限
      // 暂时使用模拟数据
      this.setData({
        isAdminUser: true // 为了演示，设置为 true
      });
    }
  },

  // 跳转到常模设置页面
  navigateToNormSettings: function() {
    wx.navigateTo({
      url: '/pages/assessment/norm-settings'
    });
  },

  // 导航到测评设置页面
  navigateToSettings() {
    wx.navigateTo({
      url: '/pages/assessment/settings'
    });
  },

  /**
   * 更新离线模式状态
   */
  updateOfflineStatus: function() {
    this.setData({
      offlineMode: app.globalData.offlineMode || false,
      syncStatus: app.globalData.syncStatus || {
        pendingCount: 0,
        lastSyncTime: null,
        isSyncing: false
      }
    });
  },
  
  /**
   * 切换离线模式
   */
  toggleOfflineMode: function() {
    const newMode = app.toggleOfflineMode(!this.data.offlineMode);
    
    this.setData({
      offlineMode: newMode
    });
    
    wx.showToast({
      title: newMode ? '已切换到离线模式' : '已切换到在线模式',
      icon: 'none',
      duration: 2000
    });
  },
  
  /**
   * 手动触发数据同步
   */
  manualSyncData: function() {
    if (this.data.syncStatus.isSyncing) {
      wx.showToast({
        title: '同步操作正在进行中',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 显示同步中提示
    this.setData({
      'syncStatus.isSyncing': true
    });
    
    wx.showLoading({
      title: '正在同步数据...',
      mask: true
    });
    
    app.manualSyncData()
      .then(result => {
        wx.hideLoading();
        
        if (result.success) {
          wx.showToast({
            title: '数据同步成功',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showModal({
            title: '同步部分失败',
            content: `总计${result.results.total}项，成功${result.results.success}项，失败${result.results.failed}项`,
            showCancel: false
          });
        }
        
        // 更新同步状态
        this.updateOfflineStatus();
      })
      .catch(error => {
        wx.hideLoading();
        wx.showModal({
          title: '同步失败',
          content: error.message || '未知错误',
          showCancel: false
        });
      })
      .finally(() => {
        this.setData({
          'syncStatus.isSyncing': false
        });
      });
  },
  
  /**
   * 格式化日期时间
   */
  formatDateTime: function(date) {
    if (!date) return '未同步';
    
    const dt = new Date(date);
    if (isNaN(dt.getTime())) return '未知时间';
    
    return `${dt.getFullYear()}-${(dt.getMonth() + 1).toString().padStart(2, '0')}-${dt.getDate().toString().padStart(2, '0')} ${dt.getHours().toString().padStart(2, '0')}:${dt.getMinutes().toString().padStart(2, '0')}`;
  },

  // 获取评估历史记录
  getAssessmentHistory: function() {
    const history = wx.getStorageSync('assessment_history') || [];
    
    this.setData({
      assessmentCount: history.length,
      lastAssessmentDate: history.length > 0 ? this.formatDate(new Date(history[0].timestamp)) : null
    });
  },
  
  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  },
  
  // 导航到历史记录页面
  navigateToHistory: function() {
    wx.navigateTo({
      url: '/pages/assessment-history/index'
    });
  },
  
  // 联系客服
  contactService: function() {
    wx.showModal({
      title: '联系客服',
      content: '如有问题请联系客服：<EMAIL>',
      showCancel: false
    });
  },
  
  // 关于我们
  aboutUs: function() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  }
})