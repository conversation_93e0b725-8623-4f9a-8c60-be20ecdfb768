#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def multiline_check(file_path):
    """处理跨行标签的结构检查"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 使用栈来跟踪标签嵌套
    tag_stack = []
    
    # 查找所有标签（包括跨行的）
    tag_pattern = r'<(/?)([a-zA-Z-]+)(?:\s[^>]*)?>'
    matches = re.finditer(tag_pattern, content, re.DOTALL)
    
    # 计算行号
    line_starts = [0]
    for i, char in enumerate(content):
        if char == '\n':
            line_starts.append(i + 1)
    
    def get_line_number(pos):
        for i, start in enumerate(line_starts):
            if i == len(line_starts) - 1 or pos < line_starts[i + 1]:
                return i + 1
        return len(line_starts)
    
    for match in matches:
        is_closing = match.group(1) == '/'
        tag_name = match.group(2)
        pos = match.start()
        line_num = get_line_number(pos)
        
        if is_closing:  # 结束标签
            if not tag_stack:
                print(f"❌ 第{line_num}行：意外的结束标签 </{tag_name}>，没有对应的开始标签")
                return False
            
            expected_tag = tag_stack[-1][1]
            if tag_name != expected_tag:
                print(f"❌ 第{line_num}行：标签不匹配")
                print(f"   期望: </{expected_tag}>")
                print(f"   实际: </{tag_name}>")
                print(f"   开始标签位置: 第{tag_stack[-1][0]}行")
                return False
            
            tag_stack.pop()
        else:  # 开始标签
            tag_stack.append((line_num, tag_name))
    
    if tag_stack:
        print(f"❌ 有 {len(tag_stack)} 个未闭合的标签:")
        for line_num, tag_name in tag_stack:
            print(f"   第{line_num}行: <{tag_name}>")
        return False
    
    print("✅ WXML结构检查通过！所有标签都正确匹配。")
    return True

if __name__ == "__main__":
    multiline_check("pages/bazi-result/index.wxml")
