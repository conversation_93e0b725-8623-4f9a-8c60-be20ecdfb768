
### **八字五行综合计算引擎开发文档 (V4.0 - 集成版)**

**1. 概述 (Overview)**

本文档定义了一套完整的、专业级的八字命理分析引擎的开发方案。本方案集成了两大核心模块：

1.  [cite\_start]**高精度五行静态力量量化模块**：采用三层权重模型 [cite: 21]，精确计算八字中各五行的基础力量。
2.  **动态交互分析模块**：模拟命盘中干支的“合、会、刑、冲”等化学反应，对静态力量进行动态修正。

最终目标是输出一个准确、可靠的数据模型，为上层的“日主强弱判断”、“喜用神分析”及“格局论断”等应用提供坚实基础。

-----

### **Part 1: 基础框架与排盘算法 (Foundational Framework & Chart Generation)**

在一切计算开始前，必须根据公历生日精确排出八字。

#### **1.1 核心数据结构 (Core Data Structures)**

  * **天干 (Heavenly Stems)**: `甲, 乙, 丙, 丁, 戊, 己, 庚, 辛, 壬, 癸`
      * **属性**: 五行 (Wuxing), 阴阳 (Yin/Yang)
  * **地支 (Earthly Branches)**: `子, 丑, 寅, 卯, 辰, 巳, 午, 未, 申, 酉, 戌, 亥`
      * **属性**: 五行 (Wuxing), 阴阳 (Yin/Yang), 藏干 (Hidden Stems)

#### **1.2 排盘算法 (Charting Algorithm)**

**输入**: 公历年、月、日、时、分。
**输出**: 四柱八字（年柱、月柱、日柱、时柱）。

1.  **年柱 (Year Pillar)**:
      * **关键点**: 以当年的 **“立春”** 节气为分界线。出生在立春前，计为上一年；立春后，计为当年。
2.  **月柱 (Month Pillar)**:
      * **关键点**: 以每个月的 **“节”** (立春、惊蛰、清明...) 为分界线，而非农历初一。
      * 月干通过“年上起月法”或查表得出（例如：甲己之年丙作首）。
3.  **日柱 (Day Pillar)**:
      * 通过万年历数据库或Goffert's Universal Algorithm等历法转换算法计算，确保公历到干支的准确转换。
4.  **时柱 (Hour Pillar)**:
      * **关键点**: 以 **23:00** 作为一天的分界点（子时的开始）。23:00-23:59 计为当日的“晚子时”，00:00-01:00 计为次日的“早子时”，日柱需相应变更。
      * 时干通过“日上起时法”或查表得出（例如：甲己还加甲）。

-----

### **Part 2: 核心计算引擎：五行静态力量量化 (Core Engine: Static Power Quantification)**

[cite\_start]此部分完整采用您V3.0方案的精确算法 [cite: 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]，以月令为总纲，精确计算八字中每个五行的基础旺衰。

#### [cite\_start]**Step 1: 初始化五行力量容器 [cite: 22]**

```javascript
let elementPowers = { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 };
```

#### [cite\_start]**Step 2: 计算天干的基础力量 [cite: 23]**

为每个天干设定一个基础分值（如：10分），并根据其五行进行累加。

  * [cite\_start]**数据**: `const HEAVENLY_STEM_WUXING = {'甲': '木', ...};` [cite: 23]
  * [cite\_start]**算法**: 遍历四天干，为对应的五行在 `elementPowers` 中累加分数 [cite: 24, 25]。

#### [cite\_start]**Step 3: 计算地支藏干的精确力量 [cite: 25]**

根据地支藏干的“主气、中气、余气”权威力量配比，计算地支的五行力量。

  * [cite\_start]**数据**: `const BASE_SCORE_BRANCH = 10;` [cite: 29]
  * **数据结构 (权威配比)**:
    ```javascript
    const HIDDEN_STEM_STRENGTH = {
        '寅': { '木': 0.6, '火': 0.3, '土': 0.1 }, // 主气木，中气火，余气土
        '卯': { '木': 1.0 },
        [cite_start]'辰': { '土': 0.6, '木': 0.3, '水': 0.1 }, [cite: 26]
        '巳': { '火': 0.6, '金': 0.3, '土': 0.1 },
        '午': { '火': 0.7, '土': 0.3 },
        [cite_start]'未': { '土': 0.6, '火': 0.3, '木': 0.1 }, [cite: 26]
        [cite_start]'申': { '金': 0.6, '水': 0.3, '土': 0.1 }, [cite: 27]
        '酉': { '金': 1.0 },
        [cite_start]'戌': { '土': 0.6, '金': 0.3, '火': 0.1 }, [cite: 27]
        '亥': { '水': 0.7, '木': 0.3 },
        '子': { '水': 1.0 },
        [cite_start]'丑': { '土': 0.6, '水': 0.3, '金': 0.1 } [cite: 28]
    };
    ```
  * [cite\_start]**算法**: 遍历四地支，根据上述配比表和地支基础分，将计算出的力量累加到 `elementPowers` 中 [cite: 30, 31]。

#### [cite\_start]**Step 4: 应用月令（季节）旺衰修正 [cite: 31]**

根据出生月份地支（月令），对**全局所有已计算出的五行力量**进行修正，体现“得令者旺”的最高原则。

  * **数据结构 (季节乘数)**:
    ```javascript
    const SEASONAL_MULTIPLIER = {
        '春': { '木': 1.5, '火': 1.2, '水': 0.8, '金': 0.6, '土': 0.4 }, // 木旺，火相...
        [cite_start]'夏': { '火': 1.5, '土': 1.2, '木': 0.8, '水': 0.6, '金': 0.4 }, [cite: 4]
        '秋': { '金': 1.5, '水': 1.2, '土': 0.8, '火': 0.6, '木': 0.4 }, // 金旺，水相...
        '冬': { '水': 1.5, '木': 1.2, '金': 0.8, '土': 0.6, '火': 0.4 }  // 水旺，木相...
    };
    ```
  * **算法**:
    1.  [cite\_start]确定月支属于哪个季节 [cite: 33, 35]。
    2.  [cite\_start]获取对应的季节乘数 [cite: 35]。
    3.  [cite\_start]将 `elementPowers` 中每个五行的当前分数乘以其对应的季节乘数 [cite: 36, 37]。

**至此，我们得到了一个高精度的“五行静态力量”快照。**

-----

### **Part 3: 动态交互分析模块 (Dynamic Interaction Analysis)**

此模块处理干支间的“化学反应”，对静态力量进行二次修正。其优先级高于月令，因为合会成功会改变五行的基本盘。

#### **3.1 算法流程**

1.  **检测与标记**: 遍历八字地支，检测是否存在“三会”、“三合”、“六合”；遍历天干，检测“五合”；遍历地支，检测“六冲”、“相刑”等。
2.  **应用修正**: 根据优先级（会 \> 合 \> 冲）应用修正。

#### **3.2 动态修正细则**

  * **三会方 (Directional Combinations)**: `寅卯辰`会木、`巳午未`会火...

      * **逻辑**: 力量最强，能改变全局五行格局。
      * **算法**: 若三会成立，**极大幅度**增加所会五行的力量（如乘以系数 `3.0` 或更高），并**显著削弱**参与地支原有的其他藏干五行力量。例如 `寅卯辰` 会木，`寅` 中丙火和戊土的力量应被大幅削减或忽略。

  * **三合局 (Harmony Combinations)**: `申子辰`合水、`亥卯未`合木...

      * **逻辑**: 力量仅次于三会。
      * **算法**: 若三合成立，**大幅度**增加所合五行的力量（如乘以系数 `2.5`），并**中度削弱**参与地支的非主气藏干力量。

  * **六合/五合 (6 & 5 Combinations)**: `子丑`合土、`甲己`合土...

      * **逻辑**: 合化有严格条件（如月令支持），若合化不成功，则称为“合绊”，双方力量均被削弱。
      * **算法**:
          * **简化模型**: 对相合的双方力量均乘以一个削弱系数（如 `0.8`），表示双方因贪合而无法完全发挥作用。
          * **高级模型**: 判断月令是否支持合化后的五行。若支持，则增加合化五行的力量，削弱原五行；若不支持，则按“合绊”处理。

  * **六冲 (Clashes)**: `子午`冲、`卯酉`冲...

      * **逻辑**: 两败俱伤，旺者胜，衰者拔根。
      * **算法**: 对相冲的双方五行力量均乘以一个削弱系数（如 `0.6`）。近冲（月日、日时）影响大于远冲（年时）。

-----

### **Part 4: 应用层：分析与输出 (Application Layer: Analysis & Output)**

在完成动态修正后，最终的 `elementPowers` 可用于上层分析。

#### **4.1 日主强弱判断 (Day Master Strength)**

1.  **确定日主**: 日柱天干即为日主。
2.  **划分阵营**:
      * **我方 (Self-Camp)**: 与日主五行相同（比肩、劫财）和生助日主五行（正印、偏印）的力量总和。
      * **敌方 (Opposing-Camp)**: 克制（官杀）、消耗（财星）、泄耗（食伤）日主五行的力量总和。
3.  **对比分析**: 比较“我方”和“敌方”的力量总分，结合月令判断（日主是否得令），得出“身强”、“身弱”、“从强”、“从弱”等结论。

#### **4.2 喜用神判断 (Favorable Elements)**

  * **身强**: 喜用神为“敌方”阵营的五行（官杀、财星、食伤），以求平衡。
  * **身弱**: 喜用神为“我方”阵营的五行（印星、比劫），以求生扶。
  * **从格**: 喜用神为格局所从的五行。
  * **调候**: 对于出生在过寒（冬）、过燥（夏）季节的八字，优先考虑能调节气候的五行（水、火）作为用神，此为更高阶逻辑。

#### **4.3 最终输出 (Final Output)**

建议将所有计算结果封装在一个JSON对象中，方便前端调用或进一步分析。

```javascript
{
    "baziChart": { "year": [...], "month": [...], ... },
    "analysisResults": {
        "staticPowers": { "金": 120.5, "木": 80.0, ... }, // Step 4的结果
        "finalPowers": { "金": 110.2, "木": 250.8, ... },  // Part 3动态修正后的结果
        "dayMasterStrength": "身弱", // Weak
        "favorableElements": ["木", "火"] // Wood, Fire
    }
}
```

**免责声明**: 本文档提供的算法模型，旨在将传统命理逻辑进行现代化和技术化的实现，仅供学术研究和软件开发参考。计算结果不构成任何实际的命理建议。