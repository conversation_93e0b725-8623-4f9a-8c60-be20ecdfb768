/**
 * 近现代人物数据合并工具
 * 将4个近现代数据文件合并为完整的近现代人物数据库
 */

const modernEraPart1 = require('../data/modern_era_part1');
const modernEraPart2 = require('../data/modern_era_part2');
const modernEraPart3 = require('../data/modern_era_part3');
const modernEraPart4 = require('../data/modern_era_part4');

function mergeModernEraData() {
  console.log('开始合并近现代人物数据...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...modernEraPart1.celebrities,
    ...modernEraPart2.celebrities,
    ...modernEraPart3.celebrities,
    ...modernEraPart4.celebrities
  ];

  // 统计时期分布
  const periodStats = {
    "洋务维新": modernEraPart1.celebrities.length,
    "革命先驱": modernEraPart2.celebrities.length,
    "军阀抗战": modernEraPart3.celebrities.length,
    "科学文化": modernEraPart4.celebrities.length
  };

  // 统计职业分布
  const occupationStats = {};
  allCelebrities.forEach(celebrity => {
    celebrity.basicInfo.occupation.forEach(occupation => {
      occupationStats[occupation] = (occupationStats[occupation] || 0) + 1;
    });
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建合并后的数据结构
  const mergedData = {
    metadata: {
      title: "近现代人物数据库",
      description: "第四批次近现代人物数据，包含1840-1949年重要历史人物",
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      timeRange: "1840-1949AD",
      dataSource: "《清史稿》《康有为全集》《梁启超全集》《孙中山全集》《鲁迅全集》等",
      verificationStandard: "专家交叉校验+古籍依据双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      periodDistribution: periodStats,
      occupationDistribution: occupationStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按时期分类的统计信息
    periodAnalysis: {
      洋务维新: {
        timeRange: "1840-1900",
        count: periodStats["洋务维新"],
        representatives: ["林则徐", "魏源", "康有为", "梁启超", "谭嗣同", "严复"],
        characteristics: "开眼看世界、师夷长技、维新变法、思想启蒙"
      },
      革命先驱: {
        timeRange: "1866-1925",
        count: periodStats["革命先驱"],
        representatives: ["孙中山", "黄兴", "宋教仁", "章太炎", "秋瑾", "蔡锷", "刘永福", "邓世昌"],
        characteristics: "推翻帝制、共和革命、民主思想、民族英雄"
      },
      军阀抗战: {
        timeRange: "1876-1949",
        count: periodStats["军阀抗战"],
        representatives: ["蒋介石", "张学良", "冯玉祥", "阎锡山", "李宗仁", "白崇禧"],
        characteristics: "军阀混战、抗日救国、政治斗争、民族统一"
      },
      科学文化: {
        timeRange: "1881-1936",
        count: periodStats["科学文化"],
        representatives: ["鲁迅", "胡适", "蔡元培", "陈独秀", "梁漱溟"],
        characteristics: "新文化运动、白话文学、教育改革、思想解放"
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 近现代人物数据合并完成 ===');
  console.log(`总计名人数量: ${mergedData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${mergedData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${mergedData.metadata.qualityGrade}`);
  console.log('\n时期分布:');
  Object.entries(periodStats).forEach(([period, count]) => {
    console.log(`  ${period}: ${count}位`);
  });
  console.log('\n主要职业分布:');
  Object.entries(occupationStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([occupation, count]) => {
      console.log(`  ${occupation}: ${count}位`);
    });

  return mergedData;
}

// 数据质量检查
function validateModernEraData(data) {
  console.log('\n开始数据质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查验证分数范围
  celebrities.forEach((celebrity, index) => {
    const score = celebrity.verification?.algorithmMatch;
    if (score && (score < 0 || score > 1)) {
      issues.push(`验证分数超出范围: ${celebrity.basicInfo?.name || index} (${score})`);
    }
  });

  // 检查时间范围
  celebrities.forEach((celebrity, index) => {
    const birthYear = celebrity.basicInfo?.birthYear;
    const deathYear = celebrity.basicInfo?.deathYear;
    if (birthYear && (birthYear < 1785 || birthYear > 1949)) {
      issues.push(`出生年份超出近现代范围: ${celebrity.basicInfo?.name || index} (${birthYear})`);
    }
    if (deathYear && birthYear && deathYear <= birthYear) {
      issues.push(`死亡年份早于出生年份: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查古籍依据格式
  celebrities.forEach((celebrity, index) => {
    const evidence = celebrity.verification?.ancientTextEvidence;
    if (!evidence || !Array.isArray(evidence) || evidence.length === 0) {
      issues.push(`缺少古籍依据: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`数据质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const mergedData = mergeModernEraData();
    const validation = validateModernEraData(mergedData);
    
    if (validation.isValid) {
      console.log('\n✅ 数据质量检查通过！');
      
      // 保存合并后的数据
      const fs = require('fs');
      const outputPath = 'data/modern_era_complete.js';
      const fileContent = `/**
 * 近现代人物完整数据库
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 4个近现代数据文件合并
 */

const modernEraComplete = ${JSON.stringify(mergedData, null, 2)};

module.exports = modernEraComplete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 合并数据已保存到: ${outputPath}`);
    } else {
      console.log('\n❌ 数据质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeModernEraData,
  validateModernEraData
};
