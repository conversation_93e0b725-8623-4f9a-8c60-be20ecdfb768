/**
 * 八字相似度匹配算法
 * 实现用户八字与历史名人八字的精确相似度计算
 * 包括多维度相似度分析和智能匹配算法
 */

class BaziSimilarityMatcher {
  constructor() {
    // 五行相生相克关系
    this.wuxingRelations = {
      '木': { sheng: '火', ke: '土', shengBy: '水', keBy: '金' },
      '火': { sheng: '土', ke: '金', shengBy: '木', keBy: '水' },
      '土': { sheng: '金', ke: '水', shengBy: '火', keBy: '木' },
      '金': { sheng: '水', ke: '木', shengBy: '土', keBy: '火' },
      '水': { sheng: '木', ke: '火', shengBy: '金', keBy: '土' }
    };

    // 天干五行属性
    this.ganWuxing = {
      '甲': '木', '乙': '木',
      '丙': '火', '丁': '火',
      '戊': '土', '己': '土',
      '庚': '金', '辛': '金',
      '壬': '水', '癸': '水'
    };

    // 地支五行属性
    this.zhiWuxing = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木',
      '辰': '土', '巳': '火', '午': '火', '未': '土',
      '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };

    // 格局权重配置
    this.patternWeights = {
      mainPattern: 0.4,    // 主格局权重
      subPattern: 0.2,     // 副格局权重
      dayMaster: 0.2,      // 日主权重
      yongshen: 0.2        // 用神权重
    };

    // 八字柱权重配置
    this.pillarWeights = {
      year: 0.2,   // 年柱权重
      month: 0.3,  // 月柱权重
      day: 0.4,    // 日柱权重
      hour: 0.1    // 时柱权重
    };
  }

  /**
   * 计算综合相似度
   * @param {Object} userBazi - 用户八字信息
   * @param {Object} celebrityBazi - 名人八字信息
   * @param {Object} options - 计算选项
   * @returns {Object} 相似度结果
   */
  calculateOverallSimilarity(userBazi, celebrityBazi, options = {}) {
    const {
      includeBazi = true,
      includePattern = true,
      includeWuxing = true,
      includeStructure = true
    } = options;

    const results = {
      overall: 0,
      details: {}
    };

    let totalWeight = 0;

    // 1. 八字直接相似度
    if (includeBazi) {
      const baziSimilarity = this.calculateBaziDirectSimilarity(userBazi, celebrityBazi);
      results.details.bazi = baziSimilarity;
      results.overall += baziSimilarity.score * 0.3;
      totalWeight += 0.3;
    }

    // 2. 格局相似度
    if (includePattern && userBazi.pattern && celebrityBazi.pattern) {
      const patternSimilarity = this.calculatePatternSimilarity(userBazi.pattern, celebrityBazi.pattern);
      results.details.pattern = patternSimilarity;
      results.overall += patternSimilarity.score * 0.3;
      totalWeight += 0.3;
    }

    // 3. 五行结构相似度
    if (includeWuxing) {
      const wuxingSimilarity = this.calculateWuxingSimilarity(userBazi, celebrityBazi);
      results.details.wuxing = wuxingSimilarity;
      results.overall += wuxingSimilarity.score * 0.25;
      totalWeight += 0.25;
    }

    // 4. 命理结构相似度
    if (includeStructure) {
      const structureSimilarity = this.calculateStructureSimilarity(userBazi, celebrityBazi);
      results.details.structure = structureSimilarity;
      results.overall += structureSimilarity.score * 0.15;
      totalWeight += 0.15;
    }

    // 标准化总分
    if (totalWeight > 0) {
      results.overall = results.overall / totalWeight;
    }

    // 添加匹配等级
    results.level = this.getSimilarityLevel(results.overall);
    
    return results;
  }

  /**
   * 计算八字直接相似度
   */
  calculateBaziDirectSimilarity(userBazi, celebrityBazi) {
    const result = {
      score: 0,
      details: {
        year: 0,
        month: 0,
        day: 0,
        hour: 0
      }
    };

    // 计算各柱相似度
    ['year', 'month', 'day', 'hour'].forEach(pillar => {
      const userPillar = userBazi[pillar];
      const celebrityPillar = celebrityBazi[pillar];
      
      let pillarScore = 0;
      
      // 天干匹配 (权重0.5)
      if (userPillar.gan === celebrityPillar.gan) {
        pillarScore += 0.5;
      }
      
      // 地支匹配 (权重0.5)
      if (userPillar.zhi === celebrityPillar.zhi) {
        pillarScore += 0.5;
      }
      
      result.details[pillar] = pillarScore;
      result.score += pillarScore * this.pillarWeights[pillar];
    });

    return result;
  }

  /**
   * 计算格局相似度
   */
  calculatePatternSimilarity(userPattern, celebrityPattern) {
    const result = {
      score: 0,
      details: {}
    };

    // 主格局匹配
    if (userPattern.mainPattern === celebrityPattern.mainPattern) {
      result.details.mainPattern = 1;
      result.score += this.patternWeights.mainPattern;
    } else {
      result.details.mainPattern = 0;
    }

    // 副格局匹配
    if (userPattern.subPattern && celebrityPattern.subPattern) {
      if (userPattern.subPattern === celebrityPattern.subPattern) {
        result.details.subPattern = 1;
        result.score += this.patternWeights.subPattern;
      } else {
        result.details.subPattern = 0;
      }
    }

    // 日主匹配
    if (userPattern.dayMaster === celebrityPattern.dayMaster) {
      result.details.dayMaster = 1;
      result.score += this.patternWeights.dayMaster;
    } else {
      result.details.dayMaster = 0;
    }

    // 用神匹配
    if (userPattern.yongshen === celebrityPattern.yongshen) {
      result.details.yongshen = 1;
      result.score += this.patternWeights.yongshen;
    } else {
      result.details.yongshen = 0;
    }

    return result;
  }

  /**
   * 计算五行结构相似度
   */
  calculateWuxingSimilarity(userBazi, celebrityBazi) {
    const userWuxing = this.analyzeWuxingStructure(userBazi);
    const celebrityWuxing = this.analyzeWuxingStructure(celebrityBazi);

    const result = {
      score: 0,
      details: {
        distribution: 0,
        strength: 0,
        balance: 0
      }
    };

    // 五行分布相似度
    result.details.distribution = this.compareWuxingDistribution(userWuxing, celebrityWuxing);
    
    // 五行强弱相似度
    result.details.strength = this.compareWuxingStrength(userWuxing, celebrityWuxing);
    
    // 五行平衡相似度
    result.details.balance = this.compareWuxingBalance(userWuxing, celebrityWuxing);

    // 综合评分
    result.score = (result.details.distribution * 0.4 + 
                   result.details.strength * 0.4 + 
                   result.details.balance * 0.2);

    return result;
  }

  /**
   * 分析五行结构
   */
  analyzeWuxingStructure(bazi) {
    const wuxingCount = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };
    const wuxingStrength = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };

    // 统计天干五行
    ['year', 'month', 'day', 'hour'].forEach(pillar => {
      const ganWuxing = this.ganWuxing[bazi[pillar].gan];
      const zhiWuxing = this.zhiWuxing[bazi[pillar].zhi];
      
      wuxingCount[ganWuxing]++;
      wuxingCount[zhiWuxing]++;
      
      // 计算强度 (月令权重最大)
      const weight = pillar === 'month' ? 3 : (pillar === 'day' ? 2 : 1);
      wuxingStrength[ganWuxing] += weight;
      wuxingStrength[zhiWuxing] += weight;
    });

    // 计算平衡度
    const total = Object.values(wuxingCount).reduce((sum, count) => sum + count, 0);
    const balance = this.calculateBalance(wuxingCount, total);

    return {
      count: wuxingCount,
      strength: wuxingStrength,
      balance: balance,
      dominant: this.findDominantWuxing(wuxingStrength),
      weak: this.findWeakWuxing(wuxingStrength)
    };
  }

  /**
   * 比较五行分布
   */
  compareWuxingDistribution(user, celebrity) {
    let similarity = 0;
    const wuxings = ['木', '火', '土', '金', '水'];
    
    wuxings.forEach(wuxing => {
      const userRatio = user.count[wuxing] / 8;
      const celebrityRatio = celebrity.count[wuxing] / 8;
      const diff = Math.abs(userRatio - celebrityRatio);
      similarity += (1 - diff) / 5;
    });

    return Math.max(0, similarity);
  }

  /**
   * 比较五行强弱
   */
  compareWuxingStrength(user, celebrity) {
    // 比较主导五行
    const dominantMatch = user.dominant === celebrity.dominant ? 0.5 : 0;
    
    // 比较弱势五行
    const weakMatch = user.weak === celebrity.weak ? 0.3 : 0;
    
    // 比较整体强度分布
    let strengthSimilarity = 0;
    const wuxings = ['木', '火', '土', '金', '水'];
    const userTotal = Object.values(user.strength).reduce((sum, s) => sum + s, 0);
    const celebrityTotal = Object.values(celebrity.strength).reduce((sum, s) => sum + s, 0);
    
    wuxings.forEach(wuxing => {
      const userRatio = user.strength[wuxing] / userTotal;
      const celebrityRatio = celebrity.strength[wuxing] / celebrityTotal;
      const diff = Math.abs(userRatio - celebrityRatio);
      strengthSimilarity += (1 - diff) / 5;
    });
    
    return dominantMatch + weakMatch + strengthSimilarity * 0.2;
  }

  /**
   * 比较五行平衡
   */
  compareWuxingBalance(user, celebrity) {
    const balanceDiff = Math.abs(user.balance - celebrity.balance);
    return Math.max(0, 1 - balanceDiff);
  }

  /**
   * 计算结构相似度
   */
  calculateStructureSimilarity(userBazi, celebrityBazi) {
    const result = {
      score: 0,
      details: {
        seasonMatch: 0,
        dayMasterMatch: 0,
        monthOrderMatch: 0
      }
    };

    // 季节匹配 (月支相同或相近)
    const userMonth = userBazi.month.zhi;
    const celebrityMonth = celebrityBazi.month.zhi;
    result.details.seasonMatch = this.calculateSeasonSimilarity(userMonth, celebrityMonth);

    // 日主匹配
    result.details.dayMasterMatch = userBazi.day.gan === celebrityBazi.day.gan ? 1 : 0;

    // 月令匹配
    result.details.monthOrderMatch = userBazi.month.gan === celebrityBazi.month.gan ? 1 : 0;

    // 综合评分
    result.score = (result.details.seasonMatch * 0.4 + 
                   result.details.dayMasterMatch * 0.4 + 
                   result.details.monthOrderMatch * 0.2);

    return result;
  }

  /**
   * 计算季节相似度
   */
  calculateSeasonSimilarity(userMonth, celebrityMonth) {
    const seasons = {
      '寅': '春', '卯': '春', '辰': '春',
      '巳': '夏', '午': '夏', '未': '夏',
      '申': '秋', '酉': '秋', '戌': '秋',
      '亥': '冬', '子': '冬', '丑': '冬'
    };

    if (userMonth === celebrityMonth) return 1;
    if (seasons[userMonth] === seasons[celebrityMonth]) return 0.7;
    return 0;
  }

  /**
   * 计算平衡度
   */
  calculateBalance(wuxingCount, total) {
    const ideal = total / 5;
    let variance = 0;
    
    Object.values(wuxingCount).forEach(count => {
      variance += Math.pow(count - ideal, 2);
    });
    
    const standardDeviation = Math.sqrt(variance / 5);
    return Math.max(0, 1 - standardDeviation / ideal);
  }

  /**
   * 找到主导五行
   */
  findDominantWuxing(wuxingStrength) {
    let maxStrength = 0;
    let dominant = null;
    
    Object.entries(wuxingStrength).forEach(([wuxing, strength]) => {
      if (strength > maxStrength) {
        maxStrength = strength;
        dominant = wuxing;
      }
    });
    
    return dominant;
  }

  /**
   * 找到弱势五行
   */
  findWeakWuxing(wuxingStrength) {
    let minStrength = Infinity;
    let weak = null;
    
    Object.entries(wuxingStrength).forEach(([wuxing, strength]) => {
      if (strength < minStrength) {
        minStrength = strength;
        weak = wuxing;
      }
    });
    
    return weak;
  }

  /**
   * 获取相似度等级
   */
  getSimilarityLevel(score) {
    if (score >= 0.9) return '极高相似';
    if (score >= 0.8) return '高度相似';
    if (score >= 0.7) return '较高相似';
    if (score >= 0.6) return '中等相似';
    if (score >= 0.5) return '一般相似';
    if (score >= 0.3) return '轻微相似';
    return '相似度较低';
  }

  /**
   * 批量计算相似度
   * @param {Object} userBazi - 用户八字
   * @param {Array} celebrities - 名人列表
   * @param {Object} options - 选项
   * @returns {Array} 相似度结果列表
   */
  batchCalculateSimilarity(userBazi, celebrities, options = {}) {
    const { limit = 10, minSimilarity = 0.3 } = options;
    
    const results = celebrities.map(celebrity => {
      const similarity = this.calculateOverallSimilarity(userBazi, celebrity.bazi, options);
      return {
        celebrity,
        similarity: similarity.overall,
        details: similarity.details,
        level: similarity.level
      };
    })
    .filter(result => result.similarity >= minSimilarity)
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit);

    return results;
  }
}

module.exports = BaziSimilarityMatcher;
