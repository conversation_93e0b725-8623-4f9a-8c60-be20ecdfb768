#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json

def detailed_module_verification():
    """详细验证每个模块的功能完整性"""
    
    print("🔍 八字结果页面模块功能详细验证")
    print("=" * 60)
    
    # 读取WXML文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        wxml_content = f.read()
    
    # 定义每个模块应该包含的关键功能
    module_requirements = {
        'basic': {
            'name': '基本信息页面',
            'required_cards': [
                '用户基本信息卡片',
                '四柱八字卡片', 
                '五行分析卡片',
                '出生天体图卡片'
            ],
            'required_data': [
                'baziData.name', 'baziData.gender', 'baziData.birthDate',
                'baziData.year_gan', 'baziData.month_gan', 'baziData.day_gan', 'baziData.hour_gan',
                'baziData.fiveElements'
            ]
        },
        'paipan': {
            'name': '四柱排盘页面',
            'required_cards': [
                '四柱排盘主卡片',
                '十神分析卡片',
                '大运预览卡片'
            ],
            'required_data': [
                'baziData.year_gan', 'baziData.year_zhi',
                'baziData.shishen_analysis', 'baziData.dayun_info'
            ]
        },
        'advanced': {
            'name': '神煞五行页面',
            'required_cards': [
                '五行强弱卡片',
                '吉星神煞卡片',
                '凶星神煞卡片',
                '用神喜忌卡片'
            ],
            'required_data': [
                'baziData.wuxing_strength', 'baziData.balanceIndex',
                'baziData.yongshen'
            ]
        },
        'fortune': {
            'name': '大运流年页面',
            'required_cards': [
                '当前大运卡片',
                '流年运势卡片',
                '运势总览卡片'
            ],
            'required_data': [
                'baziData.currentDayun', 'baziData.liunianList'
            ]
        },
        'professional': {
            'name': '格局用神页面',
            'required_cards': [
                '格局分析卡片',
                '用神分析卡片',
                '强弱分析卡片',
                '专业建议卡片'
            ],
            'required_data': [
                'professionalAnalysis.layeredResults',
                'professionalAnalysis.enhancedRules',
                'professionalAnalysis.yongshen'
            ]
        },
        'classical': {
            'name': '古籍分析页面',
            'required_cards': [
                '古籍引用卡片',
                '性格判断卡片',
                '事业判断卡片',
                '综合评价卡片'
            ],
            'required_data': [
                'classicalAnalysis.quotes',
                'classicalAnalysis.personality_judgment',
                'classicalAnalysis.career_judgment'
            ]
        },
        'timing': {
            'name': '时运分析页面',
            'required_cards': [
                '事业运势卡片',
                '财运分析卡片',
                '婚姻运势卡片',
                '健康运势卡片'
            ],
            'required_data': [
                'timingAnalysis.career',
                'timingAnalysis.wealth',
                'timingAnalysis.marriage',
                'timingAnalysis.health'
            ]
        },
        'liuqin': {
            'name': '六亲分析页面',
            'required_cards': [
                '配偶分析卡片',
                '子女分析卡片',
                '父母分析卡片',
                '兄弟姐妹卡片',
                '综合关系卡片'
            ],
            'required_data': [
                'liuqinAnalysis.spouse',
                'liuqinAnalysis.children',
                'liuqinAnalysis.parents'
            ]
        }
    }
    
    verification_results = {}
    
    for module_name, requirements in module_requirements.items():
        print(f"\n🔍 验证 {requirements['name']} ({module_name})")
        result = verify_module_functionality(wxml_content, module_name, requirements)
        verification_results[module_name] = result
        
        # 显示验证结果
        status = "✅" if result['all_passed'] else "⚠️"
        print(f"{status} 卡片完整性: {result['cards_found']}/{result['cards_required']}")
        print(f"{status} 数据绑定: {result['data_bindings_found']}/{result['data_bindings_required']}")
        
        if result['missing_cards']:
            print(f"   ❌ 缺失卡片: {', '.join(result['missing_cards'])}")
        
        if result['missing_data']:
            print(f"   ❌ 缺失数据绑定: {', '.join(result['missing_data'])}")
    
    # 生成总体报告
    generate_module_report(verification_results)
    
    return verification_results

def verify_module_functionality(wxml_content, module_name, requirements):
    """验证单个模块的功能完整性"""
    
    # 提取模块内容
    module_pattern = rf"wx:(?:if|elif)=\"{{{{currentTab === '{module_name}'}}}}\".*?(?=<view wx:(?:elif|if)=\"{{{{currentTab === '|</view>\s*</scroll-view>)"
    module_match = re.search(module_pattern, wxml_content, re.DOTALL)
    
    if not module_match:
        return {
            'found': False,
            'all_passed': False,
            'cards_found': 0,
            'cards_required': len(requirements['required_cards']),
            'data_bindings_found': 0,
            'data_bindings_required': len(requirements['required_data']),
            'missing_cards': requirements['required_cards'],
            'missing_data': requirements['required_data']
        }
    
    module_content = module_match.group(0)
    
    # 检查卡片完整性
    missing_cards = []
    cards_found = 0
    
    for card_name in requirements['required_cards']:
        # 简化的卡片检查 - 查找相关的class或注释
        card_keywords = card_name.replace('卡片', '').replace('分析', '').replace('信息', '')
        if card_keywords in module_content or any(keyword in module_content for keyword in card_keywords.split()):
            cards_found += 1
        else:
            missing_cards.append(card_name)
    
    # 检查数据绑定完整性
    missing_data = []
    data_bindings_found = 0
    
    for data_binding in requirements['required_data']:
        # 检查数据绑定是否存在（包括容错版本）
        if check_data_binding_exists(module_content, data_binding):
            data_bindings_found += 1
        else:
            missing_data.append(data_binding)
    
    all_passed = len(missing_cards) == 0 and len(missing_data) == 0
    
    return {
        'found': True,
        'all_passed': all_passed,
        'cards_found': cards_found,
        'cards_required': len(requirements['required_cards']),
        'data_bindings_found': data_bindings_found,
        'data_bindings_required': len(requirements['required_data']),
        'missing_cards': missing_cards,
        'missing_data': missing_data
    }

def check_data_binding_exists(content, data_binding):
    """检查数据绑定是否存在（包括容错版本）"""
    
    # 直接检查
    if data_binding in content:
        return True
    
    # 检查容错版本（使用 || 操作符的绑定）
    base_binding = data_binding.split('.')[0]
    field_name = data_binding.split('.')[-1]
    
    # 构建可能的容错模式
    fallback_patterns = [
        f"{data_binding} ||",
        f"|| {data_binding}",
        f"{base_binding}.*{field_name}",
        f"{field_name}"
    ]
    
    for pattern in fallback_patterns:
        if re.search(pattern, content):
            return True
    
    return False

def generate_module_report(verification_results):
    """生成模块验证报告"""
    
    print(f"\n📋 模块功能验证总结")
    print("=" * 50)
    
    total_modules = len(verification_results)
    passed_modules = sum(1 for result in verification_results.values() if result['all_passed'])
    
    print(f"总模块数: {total_modules}")
    print(f"完全通过: {passed_modules}")
    print(f"需要修复: {total_modules - passed_modules}")
    
    if passed_modules == total_modules:
        print(f"\n🎉 所有模块功能验证通过！")
        print(f"📋 建议进行的测试:")
        print(f"  1. 编译项目并检查控制台错误")
        print(f"  2. 逐个测试每个tab页面的数据显示")
        print(f"  3. 验证用户交互功能（tab切换、滚动等）")
        print(f"  4. 测试加载状态和错误处理")
        print(f"  5. 验证数据为空时的默认显示")
    else:
        print(f"\n⚠️ 需要修复的模块:")
        for module_name, result in verification_results.items():
            if not result['all_passed']:
                print(f"  - {module_name}: 缺失 {len(result['missing_cards'])} 个卡片, {len(result['missing_data'])} 个数据绑定")

if __name__ == "__main__":
    results = detailed_module_verification()
