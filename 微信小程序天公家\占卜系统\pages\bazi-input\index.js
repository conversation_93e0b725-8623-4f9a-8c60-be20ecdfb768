// pages/bazi-input/index.js
// 八字排盘信息输入页面

const app = getApp();

Page({
  data: {
    // 角色身份信息
    role: 'bazi', // 角色类型
    master: '天工师父', // 师父名称
    question: '', // 用户问题

    // 出生信息
    birthInfo: {
      year: '',
      month: '',
      day: '',
      hour: '',
      minute: '',
      gender: '男',
      location: '北京'
    },
    
    // 选择器数据
    years: [],
    months: [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ],
    days: [],
    hours: [],
    minutes: [],
    genders: ['男', '女'],
    
    // 选择器索引
    yearIndex: 0,
    monthIndex: 0,
    dayIndex: 0,
    hourIndex: 0,
    minuteIndex: 0,
    genderIndex: 0,
    
    // 分析模式
    analysisModes: [
      { key: 'basic', name: '基础模式', desc: '基本排盘信息和五行分析' },
      { key: 'professional', name: '专业模式', desc: '专业细盘分析，包含强弱、格局、用神' },
      { key: 'classical', name: '古籍模式', desc: '基于六部古籍的传统理论分析' },
      { key: 'comprehensive', name: '综合模式', desc: '全面综合分析，推荐使用' }
    ],
    selectedModeIndex: 3, // 默认选择综合模式
    
    // 界面状态
    loading: false,
    showLocationInput: false,
    customLocation: ''
  },

  onLoad: function (options) {
    console.log('八字排盘页面加载', options);

    // 处理传入的角色和师父信息
    const role = options.role || 'bazi';
    const master = options.master || '天工师父';
    const question = options.question ? decodeURIComponent(options.question) : '';

    this.setData({
      role: role,
      master: master,
      question: question
    });

    console.log('🔮 八字排盘页面 - 角色信息:', { role, master, question });

    this.initPickerData();
    this.setDefaultValues();
  },

  onShow: function () {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '八字排盘'
    });
  },

  // 初始化选择器数据
  initPickerData: function() {
    const currentYear = new Date().getFullYear();
    const years = [];
    
    // 生成年份选项 (1900-2030)
    for (let i = currentYear; i >= 1900; i--) {
      years.push(i + '年');
    }
    
    // 生成日期选项 (1-31)
    const days = [];
    for (let i = 1; i <= 31; i++) {
      days.push(i + '日');
    }
    
    // 生成小时选项 (0-23)
    const hours = [];
    for (let i = 0; i < 24; i++) {
      const hour = i.toString().padStart(2, '0');
      hours.push(hour + '时');
    }
    
    // 生成分钟选项 (0-59)
    const minutes = [];
    for (let i = 0; i < 60; i++) {
      const minute = i.toString().padStart(2, '0');
      minutes.push(minute + '分');
    }
    
    this.setData({
      years: years,
      days: days,
      hours: hours,
      minutes: minutes
    });
  },

  // 设置默认值
  setDefaultValues: function() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentDay = now.getDate();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // 设置默认为当前时间
    this.setData({
      yearIndex: 0, // 当前年份
      monthIndex: currentMonth,
      dayIndex: currentDay - 1,
      hourIndex: currentHour,
      minuteIndex: currentMinute,
      'birthInfo.year': currentYear,
      'birthInfo.month': currentMonth + 1,
      'birthInfo.day': currentDay,
      'birthInfo.hour': currentHour,
      'birthInfo.minute': currentMinute
    });
  },

  // 年份选择
  onYearChange: function(e) {
    const index = e.detail.value;
    const year = parseInt(this.data.years[index]);
    this.setData({
      yearIndex: index,
      'birthInfo.year': year
    });
  },

  // 月份选择
  onMonthChange: function(e) {
    const index = e.detail.value;
    this.setData({
      monthIndex: index,
      'birthInfo.month': index + 1
    });
    this.updateDaysInMonth();
  },

  // 日期选择
  onDayChange: function(e) {
    const index = e.detail.value;
    this.setData({
      dayIndex: index,
      'birthInfo.day': index + 1
    });
  },

  // 小时选择
  onHourChange: function(e) {
    const index = e.detail.value;
    this.setData({
      hourIndex: index,
      'birthInfo.hour': index
    });
  },

  // 分钟选择
  onMinuteChange: function(e) {
    const index = e.detail.value;
    this.setData({
      minuteIndex: index,
      'birthInfo.minute': index
    });
  },

  // 性别选择
  onGenderChange: function(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      'birthInfo.gender': this.data.genders[index]
    });
  },

  // 分析模式选择
  onModeChange: function(e) {
    const index = e.detail.value;
    this.setData({
      selectedModeIndex: index
    });
  },

  // 更新月份对应的天数
  updateDaysInMonth: function() {
    const year = this.data.birthInfo.year;
    const month = this.data.birthInfo.month;
    const daysInMonth = new Date(year, month, 0).getDate();
    
    const days = [];
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i + '日');
    }
    
    // 如果当前选择的日期超过了该月的天数，调整到最后一天
    let dayIndex = this.data.dayIndex;
    if (dayIndex >= daysInMonth) {
      dayIndex = daysInMonth - 1;
    }
    
    this.setData({
      days: days,
      dayIndex: dayIndex,
      'birthInfo.day': dayIndex + 1
    });
  },

  // 显示地点输入框
  showLocationInput: function() {
    this.setData({
      showLocationInput: true,
      customLocation: this.data.birthInfo.location
    });
  },

  // 地点输入
  onLocationInput: function(e) {
    this.setData({
      customLocation: e.detail.value
    });
  },

  // 确认地点
  confirmLocation: function() {
    const location = this.data.customLocation.trim();
    if (location) {
      this.setData({
        'birthInfo.location': location,
        showLocationInput: false
      });
    } else {
      wx.showToast({
        title: '请输入出生地点',
        icon: 'none'
      });
    }
  },

  // 取消地点输入
  cancelLocation: function() {
    this.setData({
      showLocationInput: false,
      customLocation: this.data.birthInfo.location
    });
  },

  // 验证输入数据
  validateInput: function() {
    const { year, month, day, hour, minute, gender } = this.data.birthInfo;
    
    if (!year || !month || !day || hour === '' || minute === '' || !gender) {
      wx.showToast({
        title: '请完善出生信息',
        icon: 'none'
      });
      return false;
    }
    
    // 验证日期有效性
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
      wx.showToast({
        title: '请输入有效日期',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 开始排盘
  startPaipan: function() {
    if (!this.validateInput()) {
      return;
    }
    
    const birthInfo = this.data.birthInfo;
    const selectedMode = this.data.analysisModes[this.data.selectedModeIndex];
    
    // 显示加载状态
    this.setData({ loading: true });
    
    wx.showLoading({
      title: '正在排盘...',
      mask: true
    });
    
    // 调用八字排盘API
    wx.request({
      url: 'http://localhost:8000/api/bazi/paipan',
      method: 'POST',
      header: {
        'content-type': 'application/json'
      },
      data: {
        year: parseInt(birthInfo.year),
        month: parseInt(birthInfo.month),
        day: parseInt(birthInfo.day),
        hour: parseInt(birthInfo.hour),
        minute: parseInt(birthInfo.minute),
        gender: birthInfo.gender,
        location: birthInfo.location,
        analysis_mode: selectedMode.key
      },
      success: (res) => {
        console.log('八字排盘API响应:', res);
        
        if (res.data && res.data.success) {
          // 保存结果ID到本地存储
          const resultId = res.data.data.id;
          wx.setStorageSync('bazi_result_id', resultId);
          wx.setStorageSync('bazi_birth_info', birthInfo);
          wx.setStorageSync('bazi_analysis_mode', selectedMode);
          
          // 跳转到结果页面
          wx.navigateTo({
            url: '/pages/bazi-result/index?id=' + resultId
          });
        } else {
          wx.showToast({
            title: res.data?.error || '排盘失败',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (err) => {
        console.error('八字排盘API调用失败:', err);
        wx.showToast({
          title: '网络连接失败，请检查API服务',
          icon: 'none',
          duration: 3000
        });
      },
      complete: () => {
        wx.hideLoading();
        this.setData({ loading: false });
      }
    });
  },

  // 返回首页
  goBack: function() {
    wx.navigateBack();
  },

  // 查看帮助
  showHelp: function() {
    wx.showModal({
      title: '八字排盘说明',
      content: '八字排盘是根据出生年月日时推算命理的传统方法。请准确填写出生信息，时间越精确，分析结果越准确。\n\n• 基础模式：快速排盘\n• 专业模式：详细分析\n• 古籍模式：传统理论\n• 综合模式：全面分析（推荐）',
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
