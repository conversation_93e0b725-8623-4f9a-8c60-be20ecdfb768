// utils/navigation_color.js
// 导航栏颜色工具类 - 支持占卜主题

/**
 * 角色颜色配置
 */
const ROLE_COLORS = {
  // 心理评估角色
  ai: {
    primary: '#6550e9',
    secondary: 'rgba(101, 80, 233, 0.8)',
    light: '#8370fa'
  },
  student: {
    primary: '#c75c1a',
    secondary: 'rgba(199, 92, 26, 0.8)',
    light: '#e87928'
  },
  parent: {
    primary: '#7928ca',
    secondary: 'rgba(121, 40, 202, 0.8)',
    light: '#9546e7'
  },
  teacher: {
    primary: '#16a34a',
    secondary: 'rgba(22, 163, 74, 0.8)',
    light: '#22c55e'
  },
  doctor: {
    primary: '#2563eb',
    secondary: 'rgba(37, 99, 235, 0.8)',
    light: '#3b82f6'
  },
  companion: {
    primary: '#6550e9',
    secondary: 'rgba(101, 80, 233, 0.8)',
    light: '#8370fa'
  },
  
  // 占卜角色 - 仙风道骨配色
  tarot: {
    primary: '#6B5B73',
    secondary: 'rgba(107, 91, 115, 0.8)',
    light: '#A8926D',
    accent: '#D4AF37'
  },
  bazi: {
    primary: '#8B4513',
    secondary: 'rgba(139, 69, 19, 0.8)',
    light: '#D2B48C',
    accent: '#DAA520'
  },
  yijing: {
    primary: '#4A5568',
    secondary: 'rgba(74, 85, 104, 0.8)',
    light: '#718096',
    accent: '#A0AEC0'
  },
  ziwei: {
    primary: '#553C9A',
    secondary: 'rgba(85, 60, 154, 0.8)',
    light: '#9F7AEA',
    accent: '#B794F6'
  },
  qimen: {
    primary: '#9C7C38',
    secondary: 'rgba(156, 124, 56, 0.8)',
    light: '#D4AF37',
    accent: '#F7E98E'
  },
  liuyao: {
    primary: '#2E4057',
    secondary: 'rgba(46, 64, 87, 0.8)',
    light: '#4A6741',
    accent: '#68D391'
  }
};

/**
 * 获取角色对应的颜色配置
 * @param {string} role - 角色类型
 * @returns {object} 颜色配置对象
 */
function getRoleColors(role) {
  const colors = ROLE_COLORS[role];
  if (!colors) {
    console.warn(`未找到角色 ${role} 的颜色配置，使用默认配置`);
    return ROLE_COLORS.ai; // 默认使用AI伴侣的颜色
  }
  return colors;
}

/**
 * 根据角色设置导航栏颜色
 * @param {string} role - 角色类型
 * @returns {object} 颜色配置对象
 */
function setNavigationBarColorByRole(role) {
  const colors = getRoleColors(role);
  
  try {
    // 设置导航栏颜色
    wx.setNavigationBarColor({
      backgroundColor: colors.primary,
      frontColor: '#ffffff',
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    });
    
    console.log(`已设置 ${role} 角色的导航栏颜色:`, colors.primary);
  } catch (error) {
    console.error('设置导航栏颜色失败:', error);
  }
  
  return colors;
}

/**
 * 获取角色主题类名
 * @param {string} role - 角色类型
 * @returns {string} 主题类名
 */
function getRoleThemeClass(role) {
  return `${role}-theme`;
}

/**
 * 获取角色显示名称
 * @param {string} role - 角色类型
 * @returns {string} 显示名称
 */
function getRoleDisplayName(role) {
  const displayNames = {
    ai: 'AI伴侣',
    student: '学生自评',
    parent: '家长评估',
    teacher: '教师评估',
    doctor: '医生评估',
    companion: 'AI伴侣',
    tarot: '天公师兄·六壬时课',
    bazi: '天工师父·八字排盘',
    yijing: '易经八卦',
    ziwei: '紫微斗数',
    qimen: '奇门遁甲',
    liuyao: '六爻占卜'
  };

  return displayNames[role] || role;
}

/**
 * 根据角色获取渐变背景样式
 * @param {string} role - 角色类型
 * @returns {string} CSS渐变样式
 */
function getRoleGradientStyle(role) {
  const colors = getRoleColors(role);
  
  if (colors.accent) {
    // 占卜角色使用三色渐变
    return `linear-gradient(135deg, ${colors.primary} 0%, ${colors.light} 50%, ${colors.primary} 100%)`;
  } else {
    // 心理评估角色使用双色渐变
    return `linear-gradient(135deg, ${colors.primary} 0%, ${colors.light} 100%)`;
  }
}

/**
 * 检查是否为占卜角色
 * @param {string} role - 角色类型
 * @returns {boolean} 是否为占卜角色
 */
function isDivinationRole(role) {
  const divinationRoles = ['tarot', 'bazi', 'yijing', 'ziwei', 'qimen', 'liuyao'];
  return divinationRoles.includes(role);
}

/**
 * 检查是否为心理评估角色
 * @param {string} role - 角色类型
 * @returns {boolean} 是否为心理评估角色
 */
function isAssessmentRole(role) {
  const assessmentRoles = ['ai', 'student', 'parent', 'teacher', 'doctor', 'companion'];
  return assessmentRoles.includes(role);
}

module.exports = {
  getRoleColors,
  setNavigationBarColorByRole,
  getRoleThemeClass,
  getRoleDisplayName,
  getRoleGradientStyle,
  isDivinationRole,
  isAssessmentRole,
  ROLE_COLORS
};
