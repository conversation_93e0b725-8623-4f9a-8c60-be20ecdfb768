/**
 * View标签深度计算器
 * 专门计算timing页面的view标签深度
 */

const fs = require('fs');
const path = require('path');

function calculateViewDepth() {
  console.log('🔍 计算 timing 页面的 view 标签深度');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到timing页面的开始和结束
  let timingStart = -1;
  let timingEnd = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('wx:elif') && line.includes('timing')) {
      timingStart = i;
      console.log(`📍 timing页面开始: 第${i + 1}行`);
    }
    if (line.includes('wx:elif') && line.includes('liuqin')) {
      timingEnd = i;
      console.log(`📍 timing页面结束: 第${i}行 (liuqin页面开始)`);
      break;
    }
  }
  
  if (timingStart === -1 || timingEnd === -1) {
    console.log('❌ 无法找到timing页面的范围');
    return;
  }
  
  console.log(`\n🔍 分析第${timingStart + 1}行到第${timingEnd}行的view标签:`);
  
  let depth = 0;
  let maxDepth = 0;
  const depthHistory = [];
  
  for (let i = timingStart; i < timingEnd; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 计算这一行的view标签变化
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    
    const prevDepth = depth;
    depth += openViews - closeViews;
    maxDepth = Math.max(maxDepth, depth);
    
    // 记录重要的深度变化
    if (openViews > 0 || closeViews > 0) {
      depthHistory.push({
        line: lineNum,
        content: line.trim(),
        openViews,
        closeViews,
        prevDepth,
        newDepth: depth,
        change: depth - prevDepth
      });
    }
  }
  
  console.log(`\n📊 统计结果:`);
  console.log(`最大深度: ${maxDepth}`);
  console.log(`最终深度: ${depth}`);
  console.log(`需要添加的结束标签数量: ${depth > 0 ? depth : 0}`);
  
  console.log(`\n📄 深度变化历史 (最后20个变化):`);
  const recentChanges = depthHistory.slice(-20);
  recentChanges.forEach(item => {
    const changeStr = item.change > 0 ? `+${item.change}` : `${item.change}`;
    console.log(`第${item.line}行: 深度 ${item.prevDepth} → ${item.newDepth} (${changeStr}) | 开始:${item.openViews} 结束:${item.closeViews}`);
    if (item.content.length > 80) {
      console.log(`    ${item.content.substring(0, 80)}...`);
    } else {
      console.log(`    ${item.content}`);
    }
  });
  
  // 如果深度不为0，显示需要添加的标签
  if (depth > 0) {
    console.log(`\n🔧 修复建议:`);
    console.log(`在第${timingEnd}行之前添加 ${depth} 个 </view> 标签`);
    
    let fixContent = '';
    for (let i = 0; i < depth; i++) {
      fixContent += '        </view>\n';
    }
    console.log(`\n修复内容:\n${fixContent}`);
  } else if (depth < 0) {
    console.log(`\n⚠️ 警告: 深度为负数 (${depth})，说明有多余的结束标签`);
  } else {
    console.log(`\n✅ timing页面的view标签匹配正确`);
  }
}

// 运行计算
if (require.main === module) {
  calculateViewDepth();
}

module.exports = { calculateViewDepth };
