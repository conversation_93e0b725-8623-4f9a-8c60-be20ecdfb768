#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量对比分析工具
对比原始数据、基础清理数据和高级清理数据的质量差异
"""

import json
import os
from datetime import datetime

class DataQualityComparison:
    def __init__(self):
        self.files = {
            "原始完整数据": "classical_rules_complete.json",
            "基础清理数据": "classical_rules_complete_cleaned.json", 
            "高级清理数据": "classical_rules_advanced_complete.json",
            "高级清理示例": "classical_rules_advanced_cleaned.json"
        }
    
    def load_data(self, filename: str) -> dict:
        """加载JSON数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"文件不存在: {filename}")
            return {}
        except Exception as e:
            print(f"加载文件 {filename} 时出错: {e}")
            return {}
    
    def analyze_data_quality(self, data: dict, data_name: str) -> dict:
        """分析数据质量"""
        if not data or 'rules' not in data:
            return {"error": "无效数据"}
        
        rules = data['rules']
        metadata = data.get('metadata', {})
        
        analysis = {
            "数据名称": data_name,
            "规则总数": len(rules),
            "数据来源": metadata.get('source_books', []),
            "分类数量": len(set(rule.get('category', '未分类') for rule in rules)),
            "平均文本长度": sum(len(rule.get('original_text', '')) for rule in rules) / len(rules) if rules else 0,
            "高级清理标记": sum(1 for rule in rules if rule.get('advanced_cleaned', False)),
            "置信度分布": {},
            "文本质量评估": self._assess_text_quality(rules),
            "结构完整性": self._assess_structure_completeness(rules)
        }
        
        # 分析置信度分布
        confidence_ranges = {"0.9-1.0": 0, "0.8-0.9": 0, "0.7-0.8": 0, "0.6-0.7": 0, "低于0.6": 0}
        for rule in rules:
            confidence = rule.get('confidence', 0)
            if confidence >= 0.9:
                confidence_ranges["0.9-1.0"] += 1
            elif confidence >= 0.8:
                confidence_ranges["0.8-0.9"] += 1
            elif confidence >= 0.7:
                confidence_ranges["0.7-0.8"] += 1
            elif confidence >= 0.6:
                confidence_ranges["0.6-0.7"] += 1
            else:
                confidence_ranges["低于0.6"] += 1
        
        analysis["置信度分布"] = confidence_ranges
        
        return analysis
    
    def _assess_text_quality(self, rules: list) -> dict:
        """评估文本质量"""
        if not rules:
            return {"error": "无规则数据"}
        
        quality_metrics = {
            "包含OCR错误": 0,
            "文本截断": 0,
            "格式混乱": 0,
            "内容清晰": 0,
            "解释完整": 0
        }
        
        ocr_error_patterns = ['氺', '灬', '釒', '本', '士']
        
        for rule in rules:
            text = rule.get('original_text', '')
            interpretations = rule.get('interpretations', '')
            
            # 检查OCR错误
            if any(pattern in text for pattern in ocr_error_patterns):
                quality_metrics["包含OCR错误"] += 1
            
            # 检查文本截断
            if text.endswith('...') or len(text) < 50:
                quality_metrics["文本截断"] += 1
            
            # 检查格式混乱
            if '沂水易士注' in text or '例如：' in text or text.count('。') < 2:
                quality_metrics["格式混乱"] += 1
            
            # 检查内容清晰度
            if len(text) > 100 and '。' in text and not any(pattern in text for pattern in ocr_error_patterns):
                quality_metrics["内容清晰"] += 1
            
            # 检查解释完整性
            if interpretations and len(interpretations) > 20:
                quality_metrics["解释完整"] += 1
        
        return quality_metrics
    
    def _assess_structure_completeness(self, rules: list) -> dict:
        """评估结构完整性"""
        if not rules:
            return {"error": "无规则数据"}
        
        completeness = {
            "有规则ID": sum(1 for rule in rules if rule.get('rule_id')),
            "有格局名称": sum(1 for rule in rules if rule.get('pattern_name')),
            "有分类": sum(1 for rule in rules if rule.get('category')),
            "有来源": sum(1 for rule in rules if rule.get('book_source')),
            "有原始文本": sum(1 for rule in rules if rule.get('original_text')),
            "有解释": sum(1 for rule in rules if rule.get('interpretations')),
            "有条件": sum(1 for rule in rules if rule.get('conditions')),
            "有置信度": sum(1 for rule in rules if rule.get('confidence'))
        }
        
        total_rules = len(rules)
        completeness_percentage = {k: f"{(v/total_rules)*100:.1f}%" for k, v in completeness.items()}
        
        return {
            "绝对数量": completeness,
            "完整度百分比": completeness_percentage
        }
    
    def generate_comparison_report(self) -> str:
        """生成对比报告"""
        report = []
        report.append("=" * 80)
        report.append("古籍八字命理规则数据质量对比分析报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        analyses = {}
        
        # 分析每个数据文件
        for data_name, filename in self.files.items():
            if os.path.exists(filename):
                data = self.load_data(filename)
                analysis = self.analyze_data_quality(data, data_name)
                analyses[data_name] = analysis
                
                report.append(f"## {data_name} ({filename})")
                report.append("-" * 50)
                
                if "error" in analysis:
                    report.append(f"错误: {analysis['error']}")
                    report.append("")
                    continue
                
                report.append(f"规则总数: {analysis['规则总数']:,}")
                report.append(f"平均文本长度: {analysis['平均文本长度']:.1f} 字符")
                report.append(f"高级清理标记: {analysis['高级清理标记']}")
                report.append(f"分类数量: {analysis['分类数量']}")
                
                # 置信度分布
                report.append("\n置信度分布:")
                for range_name, count in analysis['置信度分布'].items():
                    percentage = (count / analysis['规则总数']) * 100 if analysis['规则总数'] > 0 else 0
                    report.append(f"  {range_name}: {count} ({percentage:.1f}%)")
                
                # 文本质量
                report.append("\n文本质量评估:")
                for metric, count in analysis['文本质量评估'].items():
                    if metric != "error":
                        percentage = (count / analysis['规则总数']) * 100 if analysis['规则总数'] > 0 else 0
                        report.append(f"  {metric}: {count} ({percentage:.1f}%)")
                
                # 结构完整性
                report.append("\n结构完整性:")
                completeness = analysis['结构完整性']['完整度百分比']
                for field, percentage in completeness.items():
                    report.append(f"  {field}: {percentage}")
                
                report.append("")
        
        # 生成对比总结
        report.append("## 数据质量对比总结")
        report.append("-" * 50)
        
        if len(analyses) >= 2:
            # 对比规则数量
            rule_counts = {name: analysis.get('规则总数', 0) for name, analysis in analyses.items() if 'error' not in analysis}
            if rule_counts:
                report.append("规则数量对比:")
                for name, count in sorted(rule_counts.items(), key=lambda x: x[1], reverse=True):
                    report.append(f"  {name}: {count:,} 条")
                report.append("")
            
            # 对比文本质量
            report.append("文本质量对比:")
            for data_name, analysis in analyses.items():
                if 'error' not in analysis:
                    clear_content = analysis['文本质量评估'].get('内容清晰', 0)
                    total_rules = analysis['规则总数']
                    clarity_rate = (clear_content / total_rules * 100) if total_rules > 0 else 0
                    report.append(f"  {data_name}: 内容清晰率 {clarity_rate:.1f}%")
            report.append("")
            
            # 推荐使用
            report.append("## 推荐使用")
            report.append("-" * 30)
            report.append("基于质量分析，推荐使用顺序:")
            report.append("1. 高级清理数据 (classical_rules_advanced_complete.json)")
            report.append("   - 最高质量，从原始古籍重新提取")
            report.append("   - 文本清晰，结构完整，置信度高")
            report.append("   - 适合生产环境使用")
            report.append("")
            report.append("2. 高级清理示例 (classical_rules_advanced_cleaned.json)")
            report.append("   - 高质量示例数据")
            report.append("   - 适合开发测试使用")
            report.append("")
            report.append("3. 基础清理数据 (classical_rules_complete_cleaned.json)")
            report.append("   - 基础清理，仍有质量问题")
            report.append("   - 仅作为备用参考")
            report.append("")
            report.append("4. 原始完整数据 (classical_rules_complete.json)")
            report.append("   - 原始数据，质量最差")
            report.append("   - 仅作为历史记录保存")
        
        return "\n".join(report)
    
    def save_report(self, report: str, filename: str = "data_quality_comparison_report.txt"):
        """保存报告到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"质量对比报告已保存到: {filename}")

def main():
    """主函数"""
    comparator = DataQualityComparison()
    report = comparator.generate_comparison_report()
    
    # 打印报告
    print(report)
    
    # 保存报告
    comparator.save_report(report)

if __name__ == "__main__":
    main()
