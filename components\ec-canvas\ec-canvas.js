Component({
  properties: {
    ec: {
      type: Object,
      value: {
        lazyLoad: false
      }
    },
    canvasId: {
      type: String,
      value: 'ec-canvas'
    }
  },

  ready: function () {
    try {
      console.log('ec-canvas组件已准备就绪');
      if (!this.data.ec) {
        console.error('组件ec属性不存在');
        return;
      }
      
      if (!this.data.ec.lazyLoad) {
        this.init();
      } else {
        console.log('启用了惰性加载，等待调用init方法');
      }
    } catch (e) {
      console.error('ec-canvas组件初始化过程中出错', e);
    }
  },

  methods: {
    init: function (callback) {
      try {
        console.log('开始初始化ec-canvas');
        const version = wx.version.version.split('.').map(n => parseInt(n, 10));
        const isValid = version[0] > 1 || (version[0] === 1 && version[1] >= 9)
          || (version[0] === 1 && version[1] === 9 && version[2] >= 91);
        if (!isValid) {
          console.error('微信版本过低，需要基础库1.9.91+');
          return;
        }

        const ctx = wx.createCanvasContext(this.data.canvasId, this);
        if (!ctx) {
          console.error('无法创建Canvas上下文', this.data.canvasId);
          return;
        }

        const canvas = new WxCanvas(ctx, this.data.canvasId, false);
        if (!canvas) {
          console.error('无法创建WxCanvas实例');
          return;
        }

        echarts.setCanvasCreator(() => {
          return canvas;
        });

        var query = wx.createSelectorQuery().in(this);
        query.select('.ec-canvas')
          .boundingClientRect(res => {
            if (!res) {
              console.error('找不到组件，无法获取尺寸信息');
              // 给一个默认尺寸，避免出错
              this.chart = callback(canvas, 300, 200);
              return;
            }
            const { width, height } = res;
            console.log('Canvas尺寸', width, height);
            if (!width || !height) {
              console.warn('Canvas尺寸异常，宽/高为0');
            }
            this.chart = callback(canvas, width, height);
          })
          .exec();
      } catch (e) {
        console.error('初始化ec-canvas过程中发生错误:', e);
      }
    },
    
    canvasToTempFilePath(opts) {
      try {
        if (!this.chart) {
          console.error('图表未初始化');
          return;
        }
        wx.canvasToTempFilePath({
          canvasId: this.data.canvasId,
          ...opts
        }, this);
      } catch (e) {
        console.error('导出图表为图片过程中发生错误:', e);
      }
    },
    
    touchStart(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        var handler = this.chart.getZr().handler;
        handler.dispatch('mousedown', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.dispatch('mousemove', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'start');
      }
    },
    
    touchMove(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        var handler = this.chart.getZr().handler;
        handler.dispatch('mousemove', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'change');
      }
    },
    
    touchEnd(e) {
      if (this.chart) {
        const touch = e.changedTouches ? e.changedTouches[0] : {};
        var handler = this.chart.getZr().handler;
        handler.dispatch('mouseup', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.dispatch('click', {
          zrX: touch.x,
          zrY: touch.y
        });
        handler.processGesture(wrapTouch(e), 'end');
      }
    }
  }
});

// 工具函数 - 包装触摸事件
function wrapTouch(event) {
  try {
    for (let i = 0; i < event.touches.length; ++i) {
      const touch = event.touches[i];
      touch.offsetX = touch.x;
      touch.offsetY = touch.y;
    }
    return event;
  } catch (e) {
    console.error('处理触摸事件过程中发生错误:', e);
    return event;
  }
} 