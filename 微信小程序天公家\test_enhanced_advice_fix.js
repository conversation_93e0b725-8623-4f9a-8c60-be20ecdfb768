// test_enhanced_advice_fix.js
// 测试增强建议生成器的修复

console.log('🧪 开始测试增强建议生成器修复');

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null
};

// 加载模块
const EnhancedAdviceGenerator = require('./utils/enhanced_advice_generator.js');

// 创建实例
const generator = new EnhancedAdviceGenerator();

console.log('✅ 增强建议生成器实例创建成功');

// 测试用例1：正常的八字数据
console.log('\n📋 测试用例1: 正常的八字数据');
const normalBazi = {
  year: { heavenly: '甲', earthly: '子' },
  month: { heavenly: '丙', earthly: '寅' },
  day: { heavenly: '戊', earthly: '午' },
  hour: { heavenly: '庚', earthly: '申' }
};

try {
  const creativityResult = generator.calculateCreativityIndex(normalBazi, {});
  console.log('✅ 创造力指数计算成功:', creativityResult);
  
  const communicationResult = generator.assessCommunicationSkills(normalBazi, {});
  console.log('✅ 沟通能力评估成功:', communicationResult);
} catch (error) {
  console.error('❌ 正常数据测试失败:', error.message);
}

// 测试用例2：缺少 day 字段的八字数据
console.log('\n📋 测试用例2: 缺少 day 字段的八字数据');
const incompleteBazi1 = {
  year: { heavenly: '甲', earthly: '子' },
  month: { heavenly: '丙', earthly: '寅' },
  // day 字段缺失
  hour: { heavenly: '庚', earthly: '申' }
};

try {
  const creativityResult = generator.calculateCreativityIndex(incompleteBazi1, {});
  console.log('✅ 创造力指数计算（缺少day）成功:', creativityResult);
  
  const communicationResult = generator.assessCommunicationSkills(incompleteBazi1, {});
  console.log('✅ 沟通能力评估（缺少day）成功:', communicationResult);
} catch (error) {
  console.error('❌ 缺少day字段测试失败:', error.message);
}

// 测试用例3：day 字段存在但缺少 heavenly 的八字数据
console.log('\n📋 测试用例3: day 字段存在但缺少 heavenly');
const incompleteBazi2 = {
  year: { heavenly: '甲', earthly: '子' },
  month: { heavenly: '丙', earthly: '寅' },
  day: { earthly: '午' }, // 缺少 heavenly
  hour: { heavenly: '庚', earthly: '申' }
};

try {
  const creativityResult = generator.calculateCreativityIndex(incompleteBazi2, {});
  console.log('✅ 创造力指数计算（缺少heavenly）成功:', creativityResult);
  
  const communicationResult = generator.assessCommunicationSkills(incompleteBazi2, {});
  console.log('✅ 沟通能力评估（缺少heavenly）成功:', communicationResult);
} catch (error) {
  console.error('❌ 缺少heavenly字段测试失败:', error.message);
}

// 测试用例4：完全空的八字数据
console.log('\n📋 测试用例4: 完全空的八字数据');
const emptyBazi = null;

try {
  const creativityResult = generator.calculateCreativityIndex(emptyBazi, {});
  console.log('✅ 创造力指数计算（空数据）成功:', creativityResult);
  
  const communicationResult = generator.assessCommunicationSkills(emptyBazi, {});
  console.log('✅ 沟通能力评估（空数据）成功:', communicationResult);
} catch (error) {
  console.error('❌ 空数据测试失败:', error.message);
}

// 测试用例5：undefined 八字数据
console.log('\n📋 测试用例5: undefined 八字数据');
const undefinedBazi = undefined;

try {
  const creativityResult = generator.calculateCreativityIndex(undefinedBazi, {});
  console.log('✅ 创造力指数计算（undefined）成功:', creativityResult);
  
  const communicationResult = generator.assessCommunicationSkills(undefinedBazi, {});
  console.log('✅ 沟通能力评估（undefined）成功:', communicationResult);
} catch (error) {
  console.error('❌ undefined数据测试失败:', error.message);
}

console.log('\n🎉 所有测试完成！');
console.log('\n💡 修复总结:');
console.log('1. ✅ 添加了对 bazi.day 存在性的检查');
console.log('2. ✅ 添加了对 bazi.day.heavenly 存在性的检查');
console.log('3. ✅ 提供了合理的默认返回值');
console.log('4. ✅ 添加了警告日志以便调试');
console.log('\n🔧 这应该解决了日志中看到的 "Cannot read property \'heavenly\' of undefined" 错误');

module.exports = { EnhancedAdviceGenerator };
