/**
 * 历史名人数据库管理面板
 * 提供数据库管理、维护、导入导出等功能
 */

const CelebrityDatabaseManager = require('./celebrity_database_manager.js');
const fs = require('fs');
const path = require('path');

class DatabaseAdminPanel {
  constructor() {
    this.databaseManager = new CelebrityDatabaseManager();
  }

  /**
   * 显示数据库概览
   */
  showDatabaseOverview() {
    console.log('📊 历史名人数据库概览');
    console.log('============================================================');
    
    const statistics = this.databaseManager.getPatternStatistics();
    const metadata = this.databaseManager.getMetadata();
    
    console.log(`📚 数据库版本: ${metadata.version}`);
    console.log(`📅 最后更新: ${metadata.lastUpdated}`);
    console.log(`👥 总名人数: ${statistics.totalCelebrities}`);
    console.log(`⭐ 平均验证度: ${statistics.averageVerificationScore}`);
    console.log(`📖 数据来源: ${metadata.dataSources.length} 个古籍文献`);
    
    console.log('\n🏛️ 朝代分布:');
    Object.entries(statistics.dynastyDistribution).forEach(([dynasty, count]) => {
      const percentage = ((count / statistics.totalCelebrities) * 100).toFixed(1);
      console.log(`   ${dynasty}: ${count} 人 (${percentage}%)`);
    });
    
    console.log('\n🎭 格局分布:');
    Object.entries(statistics.patternDistribution).forEach(([pattern, count]) => {
      const percentage = ((count / statistics.totalCelebrities) * 100).toFixed(1);
      console.log(`   ${pattern}: ${count} 人 (${percentage}%)`);
    });
    
    console.log('\n👤 职业分布 (前10):');
    const sortedOccupations = Object.entries(statistics.occupationDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    sortedOccupations.forEach(([occupation, count]) => {
      const percentage = ((count / statistics.totalCelebrities) * 100).toFixed(1);
      console.log(`   ${occupation}: ${count} 人 (${percentage}%)`);
    });
  }

  /**
   * 数据质量检查
   */
  performDataQualityCheck() {
    console.log('\n🔍 数据质量检查');
    console.log('============================================================');
    
    const celebrities = this.databaseManager.celebrities;
    const issues = [];
    
    celebrities.forEach((celebrity, index) => {
      const validation = this.databaseManager.validateCelebrityData(celebrity);
      
      if (!validation.isValid) {
        issues.push({
          index: index,
          name: celebrity.basicInfo?.name || '未知',
          id: celebrity.id,
          errors: validation.errors
        });
      }
      
      // 检查验证度
      if (celebrity.verification?.algorithmMatch < 0.8) {
        issues.push({
          index: index,
          name: celebrity.basicInfo?.name || '未知',
          id: celebrity.id,
          errors: [`验证度过低: ${celebrity.verification.algorithmMatch}`]
        });
      }
      
      // 检查古籍依据
      if (!celebrity.verification?.ancientTextEvidence || 
          celebrity.verification.ancientTextEvidence.length === 0) {
        issues.push({
          index: index,
          name: celebrity.basicInfo?.name || '未知',
          id: celebrity.id,
          errors: ['缺少古籍依据']
        });
      }
    });
    
    if (issues.length === 0) {
      console.log('✅ 数据质量检查通过，未发现问题');
    } else {
      console.log(`❌ 发现 ${issues.length} 个数据质量问题:`);
      issues.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.name} (ID: ${issue.id})`);
        issue.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      });
    }
    
    return issues;
  }

  /**
   * 生成数据报告
   */
  generateDataReport() {
    console.log('\n📋 生成详细数据报告');
    console.log('============================================================');
    
    const statistics = this.databaseManager.getPatternStatistics();
    const celebrities = this.databaseManager.celebrities;
    
    const report = {
      generatedAt: new Date().toISOString(),
      summary: {
        totalCelebrities: statistics.totalCelebrities,
        averageVerificationScore: parseFloat(statistics.averageVerificationScore),
        dataQuality: this.calculateDataQuality(),
        completeness: this.calculateDataCompleteness()
      },
      distributions: {
        dynasty: statistics.dynastyDistribution,
        pattern: statistics.patternDistribution,
        occupation: statistics.occupationDistribution
      },
      topCelebrities: this.getTopCelebritiesByVerification(10),
      dataGaps: this.identifyDataGaps(),
      recommendations: this.generateRecommendations()
    };
    
    // 保存报告到文件
    const reportPath = path.join(__dirname, '../reports/database_report.json');
    this.ensureDirectoryExists(path.dirname(reportPath));
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📄 报告已生成: ${reportPath}`);
    console.log(`📊 数据质量评分: ${report.summary.dataQuality.toFixed(1)}/10`);
    console.log(`📈 数据完整度: ${report.summary.completeness.toFixed(1)}%`);
    
    return report;
  }

  /**
   * 计算数据质量评分
   */
  calculateDataQuality() {
    const celebrities = this.databaseManager.celebrities;
    let totalScore = 0;
    
    celebrities.forEach(celebrity => {
      let score = 0;
      
      // 基础信息完整性 (30%)
      if (celebrity.basicInfo?.name) score += 1;
      if (celebrity.basicInfo?.birthYear && celebrity.basicInfo?.deathYear) score += 1;
      if (celebrity.basicInfo?.birthplace) score += 1;
      
      // 命理信息完整性 (30%)
      if (celebrity.bazi?.fullBazi) score += 1;
      if (celebrity.pattern?.mainPattern) score += 1;
      if (celebrity.pattern?.yongshen) score += 1;
      
      // 验证信息质量 (40%)
      if (celebrity.verification?.algorithmMatch >= 0.9) score += 2;
      else if (celebrity.verification?.algorithmMatch >= 0.8) score += 1;
      
      if (celebrity.verification?.ancientTextEvidence?.length > 0) score += 1;
      if (celebrity.verification?.expertValidation?.score >= 0.9) score += 1;
      
      totalScore += score;
    });
    
    return (totalScore / (celebrities.length * 10)) * 10;
  }

  /**
   * 计算数据完整度
   */
  calculateDataCompleteness() {
    const celebrities = this.databaseManager.celebrities;
    let completenessSum = 0;
    
    celebrities.forEach(celebrity => {
      let fields = 0;
      let filledFields = 0;
      
      // 检查各个字段
      const fieldsToCheck = [
        'basicInfo.name',
        'basicInfo.courtesy',
        'basicInfo.birthYear',
        'basicInfo.deathYear',
        'basicInfo.birthplace',
        'basicInfo.occupation',
        'bazi.fullBazi',
        'pattern.mainPattern',
        'pattern.yongshen',
        'lifeEvents',
        'verification.algorithmMatch',
        'verification.ancientTextEvidence'
      ];
      
      fieldsToCheck.forEach(fieldPath => {
        fields++;
        if (this.getNestedValue(celebrity, fieldPath)) {
          filledFields++;
        }
      });
      
      completenessSum += (filledFields / fields) * 100;
    });
    
    return completenessSum / celebrities.length;
  }

  /**
   * 获取嵌套对象值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * 获取验证度最高的名人
   */
  getTopCelebritiesByVerification(count = 10) {
    return this.databaseManager.celebrities
      .sort((a, b) => b.verification.algorithmMatch - a.verification.algorithmMatch)
      .slice(0, count)
      .map(celebrity => ({
        name: celebrity.basicInfo.name,
        dynasty: celebrity.basicInfo.dynasty,
        pattern: celebrity.pattern.mainPattern,
        verificationScore: celebrity.verification.algorithmMatch
      }));
  }

  /**
   * 识别数据缺口
   */
  identifyDataGaps() {
    const gaps = [];
    const statistics = this.databaseManager.getPatternStatistics();
    
    // 检查朝代覆盖
    const majorDynasties = ['秦朝', '汉朝', '唐朝', '宋朝', '元朝', '明朝', '清朝'];
    majorDynasties.forEach(dynasty => {
      if (!statistics.dynastyDistribution[dynasty]) {
        gaps.push({
          type: 'dynasty',
          missing: dynasty,
          priority: 'high'
        });
      }
    });
    
    // 检查格局覆盖
    const majorPatterns = ['正官格', '七杀格', '正财格', '偏财格', '食神格', '伤官格', '正印格', '偏印格'];
    majorPatterns.forEach(pattern => {
      if (!statistics.patternDistribution[pattern] || statistics.patternDistribution[pattern] < 2) {
        gaps.push({
          type: 'pattern',
          missing: pattern,
          priority: 'medium'
        });
      }
    });
    
    return gaps;
  }

  /**
   * 生成改进建议
   */
  generateRecommendations() {
    const recommendations = [];
    const dataQuality = this.calculateDataQuality();
    const completeness = this.calculateDataCompleteness();
    
    if (dataQuality < 8) {
      recommendations.push({
        type: 'quality',
        priority: 'high',
        description: '提高数据验证标准，增加古籍依据和专家验证'
      });
    }
    
    if (completeness < 90) {
      recommendations.push({
        type: 'completeness',
        priority: 'medium',
        description: '补充缺失的基础信息和命理数据'
      });
    }
    
    const gaps = this.identifyDataGaps();
    if (gaps.length > 0) {
      recommendations.push({
        type: 'coverage',
        priority: 'medium',
        description: `扩展数据覆盖范围，补充 ${gaps.length} 个缺失的朝代/格局`
      });
    }
    
    return recommendations;
  }

  /**
   * 确保目录存在
   */
  ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  /**
   * 导出数据库备份
   */
  exportBackup(format = 'json') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, '../backups');
    this.ensureDirectoryExists(backupDir);
    
    if (format === 'json') {
      const backupPath = path.join(backupDir, `celebrity_database_backup_${timestamp}.json`);
      const data = this.databaseManager.exportToJSON();
      fs.writeFileSync(backupPath, data);
      console.log(`✅ 数据库备份已创建: ${backupPath}`);
      return backupPath;
    }
    
    throw new Error(`不支持的导出格式: ${format}`);
  }

  /**
   * 运行完整的管理面板
   */
  runAdminPanel() {
    console.log('🎛️ 历史名人数据库管理面板');
    console.log('============================================================\n');
    
    // 显示概览
    this.showDatabaseOverview();
    
    // 数据质量检查
    this.performDataQualityCheck();
    
    // 生成报告
    this.generateDataReport();
    
    // 创建备份
    this.exportBackup();
    
    console.log('\n🎉 管理面板运行完成!');
  }
}

module.exports = DatabaseAdminPanel;
