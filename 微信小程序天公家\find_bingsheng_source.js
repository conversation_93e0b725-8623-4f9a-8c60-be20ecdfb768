// find_bingsheng_source.js
// 寻找产生"丙申"的错误源

console.log('🔍 寻找产生"丙申"的错误源');
console.log('='.repeat(60));

// 基础数据
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 获取时辰地支
function getHourZhi(hour) {
  if (hour >= 23 || hour < 1) return '子';
  if (hour >= 1 && hour < 3) return '丑';
  if (hour >= 3 && hour < 5) return '寅';
  if (hour >= 5 && hour < 7) return '卯';
  if (hour >= 7 && hour < 9) return '辰';
  if (hour >= 9 && hour < 11) return '巳';
  if (hour >= 11 && hour < 13) return '午';
  if (hour >= 13 && hour < 15) return '未';
  if (hour >= 15 && hour < 17) return '申';
  if (hour >= 17 && hour < 19) return '酉';
  if (hour >= 19 && hour < 21) return '戌';
  if (hour >= 21 && hour < 23) return '亥';
  return '子';
}

// 情况1：时辰判断错误 - 17点被判断为申时
function testScenario1() {
  console.log('\n🧪 情况1：时辰判断错误 - 17点被判断为申时');
  
  function getHourZhiWrong(hour) {
    if (hour >= 23 || hour < 1) return '子';
    if (hour >= 1 && hour < 3) return '丑';
    if (hour >= 3 && hour < 5) return '寅';
    if (hour >= 5 && hour < 7) return '卯';
    if (hour >= 7 && hour < 9) return '辰';
    if (hour >= 9 && hour < 11) return '巳';
    if (hour >= 11 && hour < 13) return '午';
    if (hour >= 13 && hour < 15) return '未';
    if (hour >= 15 && hour < 19) return '申';  // 错误：15-19点都是申时
    if (hour >= 19 && hour < 21) return '戌';
    if (hour >= 21 && hour < 23) return '亥';
    return '子';
  }
  
  const dayGan = '辛';
  const hour = 17;
  const hourZhi = getHourZhiWrong(hour);
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  // 正确的五鼠遁
  const wushuDunMap = {
    '甲': '甲', '己': '甲',
    '乙': '丙', '庚': '丙',
    '丙': '戊', '辛': '戊',
    '丁': '庚', '壬': '庚',
    '戊': '壬', '癸': '壬'
  };
  
  const baseGan = wushuDunMap[dayGan];
  const baseGanIndex = tiangan.indexOf(baseGan);
  const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
  
  const result = tiangan[hourGanIndex] + hourZhi;
  
  console.log(`日干: ${dayGan}, 时间: ${hour}:00`);
  console.log(`错误时支: ${hourZhi} (应该是酉)`);
  console.log(`计算结果: ${result}`);
  
  if (result === '丙申') {
    console.log('❌ 找到了！时辰判断错误会产生丙申');
    return true;
  }
  
  return false;
}

// 情况2：日干传递错误
function testScenario2() {
  console.log('\n🧪 情况2：日干传递错误');
  
  const hour = 17;
  const hourZhi = getHourZhi(hour); // 酉
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  // 正确的五鼠遁
  const wushuDunMap = {
    '甲': '甲', '己': '甲',
    '乙': '丙', '庚': '丙',
    '丙': '戊', '辛': '戊',
    '丁': '庚', '壬': '庚',
    '戊': '壬', '癸': '壬'
  };
  
  console.log('测试不同日干在17点酉时的结果:');
  
  tiangan.forEach(dayGan => {
    const baseGan = wushuDunMap[dayGan];
    const baseGanIndex = tiangan.indexOf(baseGan);
    const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
    const result = tiangan[hourGanIndex] + hourZhi;
    
    console.log(`${dayGan}日17点 -> ${result}`);
    
    if (result === '丙申') {
      console.log('❌ 找到了！这个日干会产生丙申');
      return true;
    }
  });
  
  return false;
}

// 情况3：五鼠遁表错误
function testScenario3() {
  console.log('\n🧪 情况3：五鼠遁表错误');
  
  const dayGan = '辛';
  const hour = 17;
  const hourZhi = getHourZhi(hour); // 酉
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  // 可能的错误五鼠遁表
  const wrongMaps = [
    {
      name: '错误1：直接映射日干',
      map: {
        '甲': '甲', '己': '己',
        '乙': '乙', '庚': '庚',
        '丙': '丙', '辛': '辛',
        '丁': '丁', '壬': '壬',
        '戊': '戊', '癸': '癸'
      }
    },
    {
      name: '错误2：偏移错误',
      map: {
        '甲': '乙', '己': '乙',
        '乙': '丁', '庚': '丁',
        '丙': '己', '辛': '己',
        '丁': '辛', '壬': '辛',
        '戊': '癸', '癸': '癸'
      }
    },
    {
      name: '错误3：完全错误的映射',
      map: {
        '甲': '丙', '己': '丙',
        '乙': '戊', '庚': '戊',
        '丙': '庚', '辛': '庚',
        '丁': '壬', '壬': '壬',
        '戊': '甲', '癸': '甲'
      }
    }
  ];
  
  wrongMaps.forEach(wrongMap => {
    const baseGan = wrongMap.map[dayGan];
    const baseGanIndex = tiangan.indexOf(baseGan);
    const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
    const result = tiangan[hourGanIndex] + hourZhi;
    
    console.log(`${wrongMap.name}: ${dayGan}日17点 -> ${result}`);
    
    if (result === '丙申') {
      console.log('❌ 找到了！这个错误映射会产生丙申');
      return true;
    }
  });
  
  return false;
}

// 情况4：申时+错误日干的组合
function testScenario4() {
  console.log('\n🧪 情况4：申时+错误日干的组合');
  
  const hour = 15; // 申时
  const hourZhi = getHourZhi(hour); // 申
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  // 正确的五鼠遁
  const wushuDunMap = {
    '甲': '甲', '己': '甲',
    '乙': '丙', '庚': '丙',
    '丙': '戊', '辛': '戊',
    '丁': '庚', '壬': '庚',
    '戊': '壬', '癸': '壬'
  };
  
  console.log('测试不同日干在申时的结果:');
  
  tiangan.forEach(dayGan => {
    const baseGan = wushuDunMap[dayGan];
    const baseGanIndex = tiangan.indexOf(baseGan);
    const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
    const result = tiangan[hourGanIndex] + hourZhi;
    
    console.log(`${dayGan}日申时 -> ${result}`);
    
    if (result === '丙申') {
      console.log('❌ 找到了！这个日干在申时会产生丙申');
      return true;
    }
  });
  
  return false;
}

// 执行所有测试
console.log('开始寻找产生"丙申"的错误源...');

const found1 = testScenario1();
const found2 = testScenario2();
const found3 = testScenario3();
const found4 = testScenario4();

console.log('\n📋 总结:');
if (found1 || found2 || found3 || found4) {
  console.log('✅ 找到了可能产生"丙申"的错误源');
} else {
  console.log('❌ 没有找到产生"丙申"的明显错误源');
  console.log('可能的其他原因：');
  console.log('1. 前端使用了不同的算法文件');
  console.log('2. 数据传递过程中被修改');
  console.log('3. 缓存问题');
  console.log('4. 真太阳时计算影响了时辰判断');
}

console.log('\n✅ 分析完成');
