/**
 * 语义搜索适配器 - 小程序兼容版本
 * 将现有的JavaScript语义搜索引擎适配到新的统一数据访问层
 * 注意：小程序环境不支持 child_process，使用本地逻辑替代
 */

// 小程序环境不支持 child_process，注释掉相关引用
// const { spawn } = require('child_process');
// const path = require('path');

class SemanticSearchAdapter {
  
  /**
   * 使用统一数据访问层进行语义搜索
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @returns {Promise<object>} 搜索结果
   */
  static async searchClassicalText(questionText, godName) {
    console.log('🔍 小程序环境：直接使用本地语义搜索');
    console.log('问题:', questionText, '六神:', godName);

    try {
      // 小程序环境不支持调用Python脚本，直接使用本地逻辑
      console.log('⚠️ 小程序环境限制，使用本地逻辑');
      return this.fallbackToOriginalLogic(questionText, godName);

    } catch (error) {
      console.error('❌ 本地语义搜索失败:', error);
      // 返回基本结果
      return this.getBasicFallbackResult();
    }
  }
  
  /**
   * 调用Python统一数据访问层 - 小程序环境不支持
   * @param {object} params - 查询参数
   * @returns {Promise<object>} 查询结果
   */
  static callUnifiedDataAccess(params) {
    // 小程序环境不支持 child_process，直接返回失败
    return new Promise((resolve, reject) => {
      console.log('⚠️ 小程序环境不支持Python脚本调用');
      reject(new Error('小程序环境不支持Python脚本调用'));
    });
  }
  
  /**
   * 将统一数据访问层结果转换为原有格式
   * @param {object} result - 统一数据访问层结果
   * @returns {object} 原有格式结果
   */
  static convertToLegacyFormat(result) {
    const interpretation = result.interpretation;
    
    return {
      domain: result.domain || 'general',
      scenario: interpretation.scenario || 'general',
      classical: {
        original: interpretation.original_text || '',
        meaning: interpretation.description || '',
        fortune: this.mapLuckLevelToFortune(interpretation.luck_level)
      },
      modern: {
        explanation: interpretation.interpretation || interpretation.description || '',
        advice: interpretation.modern_advice || interpretation.interpretation || '',
        keywords: this.parseKeywords(interpretation.keywords)
      },
      confidence: result.confidence || 0.5,
      source: 'unified_data_access',
      alternatives: result.alternatives || []
    };
  }
  
  /**
   * 将吉凶等级映射为运势
   * @param {string} luckLevel - 吉凶等级
   * @returns {string} 运势描述
   */
  static mapLuckLevelToFortune(luckLevel) {
    const mapping = {
      'very_auspicious': '大吉',
      'auspicious': '吉',
      'neutral': '平',
      'inauspicious': '凶',
      'very_inauspicious': '大凶',
      'unknown': '待定'
    };
    return mapping[luckLevel] || '平';
  }
  
  /**
   * 解析关键词
   * @param {string|array} keywords - 关键词
   * @returns {array} 关键词数组
   */
  static parseKeywords(keywords) {
    if (Array.isArray(keywords)) {
      return keywords;
    }
    if (typeof keywords === 'string') {
      try {
        return JSON.parse(keywords);
      } catch {
        return keywords.split(',').map(k => k.trim()).filter(k => k);
      }
    }
    return [];
  }
  
  /**
   * 回退到原有逻辑
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @returns {object} 原有格式结果
   */
  static fallbackToOriginalLogic(questionText, godName) {
    console.log('🔄 使用本地语义搜索逻辑');

    try {
      // 尝试使用本地的智能匹配器
      const intelligentMatcher = require('./intelligent_matcher');
      const analysis = intelligentMatcher.analyzeQuestion(questionText);

      // 基于分析结果生成占卜解读
      return this.generateLocalInterpretation(questionText, godName, analysis);

    } catch (error) {
      console.error('❌ 本地逻辑也失败了:', error);

      // 最终回退：返回基本结果
      return this.getBasicFallbackResult();
    }
  }

  /**
   * 生成本地占卜解读
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @param {object} analysis - 智能分析结果
   * @returns {object} 占卜解读
   */
  static generateLocalInterpretation(questionText, godName, analysis) {
    // 基于六神的基本解读
    const godInterpretations = {
      '大安': {
        fortune: '大吉',
        meaning: '事情安稳顺利，可以放心进行',
        advice: '保持现状，稳步前进'
      },
      '留连': {
        fortune: '小凶',
        meaning: '事情有阻碍，需要耐心等待',
        advice: '暂缓行动，等待时机'
      },
      '速喜': {
        fortune: '中吉',
        meaning: '好消息即将到来，事情会有转机',
        advice: '积极行动，把握机会'
      },
      '赤口': {
        fortune: '大凶',
        meaning: '需要谨慎小心，避免冲突',
        advice: '三思而后行，避免争执'
      },
      '小吉': {
        fortune: '小吉',
        meaning: '小有收获，但不宜过于期待',
        advice: '适度努力，保持平常心'
      },
      '空亡': {
        fortune: '中凶',
        meaning: '事情可能落空，需要另寻他法',
        advice: '重新规划，寻找新的方向'
      }
    };

    const godInfo = godInterpretations[godName] || godInterpretations['大安'];

    return {
      domain: analysis.questionType || 'general',
      scenario: 'local_analysis',
      classical: {
        original: `${godName}神示：${godInfo.meaning}`,
        meaning: godInfo.meaning,
        fortune: godInfo.fortune
      },
      modern: {
        explanation: `根据您的问题"${questionText}"和${godName}神的指示，${godInfo.meaning}`,
        advice: godInfo.advice,
        keywords: analysis.keywords || ['占卜', '指导']
      },
      confidence: Math.min(analysis.confidence || 0.7, 0.9),
      source: 'local_semantic'
    };
  }
  
  /**
   * 混合搜索
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @param {string} keyword - 关键词
   * @returns {Promise<object>} 搜索结果
   */
  static async hybridSearch(questionText, godName, keyword) {
    console.log('🔄 使用统一数据访问层进行混合搜索');
    
    try {
      const result = await this.callUnifiedDataAccess({
        type: 'hybrid',
        questionText: questionText,
        godName: godName,
        keyword: keyword
      });
      
      if (result.success && result.interpretation) {
        return this.convertToLegacyFormat(result);
      } else {
        return this.fallbackToOriginalLogic(questionText, godName);
      }
      
    } catch (error) {
      console.error('❌ 混合搜索失败:', error);
      return this.fallbackToOriginalLogic(questionText, godName);
    }
  }
  
  /**
   * 关键词搜索
   * @param {string} keyword - 关键词
   * @param {object} filters - 过滤条件
   * @returns {Promise<object>} 搜索结果
   */
  static async keywordSearch(keyword, filters = {}) {
    console.log('🔍 使用统一数据访问层进行关键词搜索');
    
    try {
      const result = await this.callUnifiedDataAccess({
        type: 'keyword',
        keyword: keyword,
        filters: filters
      });
      
      return result;
      
    } catch (error) {
      console.error('❌ 关键词搜索失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 获取基本回退结果
   * @returns {object} 基本结果
   */
  static getBasicFallbackResult() {
    return {
      domain: 'general',
      scenario: 'general',
      classical: {
        original: '占卜结果暂时无法获取，请稍后再试',
        meaning: '系统正在维护中，请稍后再试',
        fortune: '平'
      },
      modern: {
        explanation: '系统正在维护中，请稍后再试',
        advice: '建议稍后再次尝试占卜',
        keywords: ['维护', '稍后再试']
      },
      confidence: 0.1,
      source: 'basic_fallback'
    };
  }
}

module.exports = SemanticSearchAdapter;
