/* pages/divination/index.wxss */
.divination-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部导航 */
.header {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
}

.nav-left {
  flex: 1;
}

.nav-back {
  color: #667eea;
  font-size: 32rpx;
  font-weight: bold;
}

.nav-title {
  flex: 2;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.nav-right {
  flex: 1;
}

/* API连接状态 */
.api-status {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.status-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.status-tip {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 搜索区域 */
.search-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 50rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background: #667eea;
  color: white;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: none;
  margin-left: 20rpx;
}

.search-btn[disabled] {
  background: #ccc;
}

.search-actions {
  text-align: center;
}

.clear-btn {
  background: #ff6b6b;
  color: white;
  border-radius: 20rpx;
  padding: 10rpx 30rpx;
  font-size: 24rpx;
  border: none;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-count {
  font-size: 24rpx;
  color: #666;
}

.refresh-btn {
  background: #667eea;
  color: white;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  border: none;
}

/* 占卜卡片 */
.divination-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 20rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.divination-card:active {
  transform: scale(0.98);
}

.random-card {
  border-left: 6rpx solid #667eea;
}

.result-card {
  border-left: 6rpx solid #52c41a;
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.card-title {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.luck-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  white-space: nowrap;
}

.luck-very_auspicious {
  background: #e8f5e8;
  color: #2e7d32;
}

.luck-auspicious {
  background: #e3f2fd;
  color: #1976d2;
}

.luck-neutral {
  background: #f5f5f5;
  color: #666;
}

.luck-inauspicious {
  background: #fff3e0;
  color: #f57c00;
}

.luck-very_inauspicious {
  background: #ffebee;
  color: #d32f2f;
}

.luck-unknown {
  background: #f0f0f0;
  color: #999;
}

.card-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

/* 关键词标签 */
.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.keyword-tag {
  background: #f0f8ff;
  color: #1890ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.card-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.meta-text {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
}

/* 加载状态 */
.load-more, .loading-more, .no-more {
  text-align: center;
  padding: 30rpx;
}

.load-more-text, .loading-text, .no-more-text {
  font-size: 24rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.empty-tip {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 统计信息 */
.stats-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 使用说明 */
.help-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.help-content {
  margin-top: 20rpx;
}

.help-item {
  margin-bottom: 25rpx;
}

.help-title {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.help-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
}
