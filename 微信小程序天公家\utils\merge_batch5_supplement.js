/**
 * 第五批次补充数据合并工具
 * 将三个部分合并为完整的第五批次补充数据
 */

const batch5Part1 = require('../data/batch5_supplement');
const batch5Part2 = require('../data/batch5_supplement_part2');
const batch5Part3 = require('../data/batch5_supplement_part3');

function mergeBatch5SupplementData() {
  console.log('开始合并第五批次补充数据...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...batch5Part1.celebrities,
    ...batch5Part2.celebrities,
    ...batch5Part3.celebrities
  ];

  // 统计时期分布
  const periodStats = {
    "春秋战国": 0,
    "秦汉": 0,
    "三国": 0,
    "魏晋南北朝": 0,
    "明清": 0,
    "近现代": 0
  };

  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    if (dynasty.includes('春秋') || dynasty.includes('战国')) {
      periodStats["春秋战国"]++;
    } else if (dynasty.includes('秦') || dynasty.includes('汉')) {
      periodStats["秦汉"]++;
    } else if (dynasty.includes('三国')) {
      periodStats["三国"]++;
    } else if (dynasty.includes('晋') || dynasty.includes('南北朝')) {
      periodStats["魏晋南北朝"]++;
    } else if (dynasty.includes('明') || dynasty.includes('清')) {
      periodStats["明清"]++;
    } else if (dynasty.includes('民国') || dynasty.includes('清末')) {
      periodStats["近现代"]++;
    }
  });

  // 统计朝代分布
  const dynastyStats = {};
  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
  });

  // 统计职业分布
  const occupationStats = {};
  allCelebrities.forEach(celebrity => {
    celebrity.basicInfo.occupation.forEach(occupation => {
      occupationStats[occupation] = (occupationStats[occupation] || 0) + 1;
    });
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建合并后的数据结构
  const mergedData = {
    metadata: {
      title: "历史名人数据库 - 第五批次补充版",
      description: "第五批次补充历史名人数据，包含37位重要历史人物以达到200位目标",
      batchNumber: 5,
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      timeRange: "前725年-1901年AD",
      dataSource: "《史记》《汉书》《三国志》《晋书》《明史》《清史稿》等权威史料",
      verificationStandard: "专家交叉校验+古籍依据双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      periodDistribution: periodStats,
      dynastyDistribution: dynastyStats,
      occupationDistribution: occupationStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按时期分类的详细统计信息
    periodAnalysis: {
      春秋战国: {
        timeRange: "前725年-前221年",
        count: periodStats["春秋战国"],
        characteristics: "百家争鸣、政治变革、军事创新",
        representatives: ["管仲", "晏婴", "孙武", "吴起", "商鞅", "苏秦", "张仪", "廉颇"]
      },
      秦汉: {
        timeRange: "前221年-220年",
        count: periodStats["秦汉"],
        characteristics: "统一天下、制度建立、文化融合、医学发展",
        representatives: ["韩信", "萧何", "张骞", "董仲舒", "班固", "华佗", "张仲景"]
      },
      三国: {
        timeRange: "220年-280年",
        count: periodStats["三国"],
        characteristics: "三足鼎立、英雄辈出、文武并重",
        representatives: ["曹操", "孙权"]
      },
      魏晋南北朝: {
        timeRange: "220年-589年",
        count: periodStats["魏晋南北朝"],
        characteristics: "政权分立、文化繁荣、道教兴起",
        representatives: ["谢玄", "葛玄"]
      },
      明清: {
        timeRange: "1368年-1912年",
        count: periodStats["明清"],
        characteristics: "理学发展、文学繁荣、科技进步",
        representatives: ["王阳明", "汤显祖", "宋应星", "蒲松龄", "吴敬梓", "顾炎武"]
      },
      近现代: {
        timeRange: "1811年-1901年",
        count: periodStats["近现代"],
        characteristics: "洋务运动、维新变法、救亡图存",
        representatives: ["曾国藩", "李鸿章", "左宗棠"]
      }
    },

    // 质量分析
    qualityAnalysis: {
      totalCelebrities: allCelebrities.length,
      averageVerificationScore: averageVerificationScore,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格",
      scoreDistribution: {
        excellent: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.95).length,
        good: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.90 && c.verification.algorithmMatch < 0.95).length,
        acceptable: allCelebrities.filter(c => c.verification.algorithmMatch < 0.90).length
      },
      dataIntegrity: {
        completeBasicInfo: allCelebrities.filter(c => c.basicInfo?.name && c.basicInfo?.birthYear).length,
        completeBazi: allCelebrities.filter(c => c.bazi?.fullBazi).length,
        completePattern: allCelebrities.filter(c => c.pattern?.mainPattern).length,
        completeVerification: allCelebrities.filter(c => c.verification?.algorithmMatch).length
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 第五批次补充数据合并完成 ===');
  console.log(`总计名人数量: ${mergedData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${mergedData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${mergedData.metadata.qualityGrade}`);
  console.log('\n时期分布:');
  Object.entries(periodStats).forEach(([period, count]) => {
    console.log(`  ${period}: ${count}位`);
  });
  console.log('\n主要朝代分布:');
  Object.entries(dynastyStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([dynasty, count]) => {
      console.log(`  ${dynasty}: ${count}位`);
    });
  console.log('\n主要职业分布:');
  Object.entries(occupationStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .forEach(([occupation, count]) => {
      console.log(`  ${occupation}: ${count}位`);
    });

  return mergedData;
}

// 数据质量检查
function validateBatch5SupplementData(data) {
  console.log('\n开始第五批次补充数据质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段完整性
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`第五批次补充数据质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const mergedData = mergeBatch5SupplementData();
    const validation = validateBatch5SupplementData(mergedData);
    
    if (validation.isValid) {
      console.log('\n✅ 第五批次补充数据质量检查通过！');
      
      // 保存合并后的数据
      const fs = require('fs');
      const outputPath = 'data/batch5_supplement_complete.js';
      const fileContent = `/**
 * 历史名人数据库 - 第五批次补充完整版
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 三个部分数据合并
 * 总计: ${mergedData.metadata.totalRecords}位历史名人
 * 平均验证分数: ${mergedData.metadata.averageVerificationScore}
 */

const batch5SupplementComplete = ${JSON.stringify(mergedData, null, 2)};

module.exports = batch5SupplementComplete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 第五批次补充完整数据已保存到: ${outputPath}`);
    } else {
      console.log('\n❌ 第五批次补充数据质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('第五批次补充数据合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeBatch5SupplementData,
  validateBatch5SupplementData
};
