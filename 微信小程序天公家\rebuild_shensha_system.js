// rebuild_shensha_system.js
// 基于"问真八字"标准重建神煞计算系统

console.log('🔧 重建神煞计算系统');
console.log('='.repeat(80));

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  birthMonth: 6,
  birthInfo: {
    year: 2021,
    month: 6,
    day: 24,
    hour: 19,
    minute: 30,
    gender: '男'
  }
};

// "问真八字"标准结果
const wenZhenStandard = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

// 权威神煞查表系统
const authoritativeShenshaTable = {
  // 天乙贵人表
  tianyi_guiren: {
    '甲': ['丑', '未'], '戊': ['丑', '未'],
    '乙': ['子', '申'], '己': ['子', '申'],
    '丙': ['亥', '酉'], '丁': ['亥', '酉'],
    '庚': ['丑', '未'], '辛': ['寅', '午'],
    '壬': ['卯', '巳'], '癸': ['卯', '巳']
  },
  
  // 文昌贵人表
  wenchang_guiren: {
    '甲': '巳', '乙': '午', '丙': '申', '丁': '酉',
    '戊': '申', '己': '酉', '庚': '亥', '辛': '子',
    '壬': '寅', '癸': '卯'
  },
  
  // 桃花表（咸池）
  taohua: {
    '申子辰': '酉', '寅午戌': '卯', '巳酉丑': '午', '亥卯未': '子'
  },
  
  // 福星贵人表（基于年支）
  fuxing_guiren: {
    '子': ['申', '辰'], '丑': ['巳', '酉'], '寅': ['午', '戌'],
    '卯': ['未', '亥'], '辰': ['申', '子'], '巳': ['酉', '丑'],
    '午': ['戌', '寅'], '未': ['亥', '卯'], '申': ['子', '辰'],
    '酉': ['丑', '巳'], '戌': ['寅', '午'], '亥': ['卯', '未']
  },
  
  // 灾煞表（基于年支）
  zaisha: {
    '子': '午', '丑': '未', '寅': '申', '卯': '酉',
    '辰': '戌', '巳': '亥', '午': '子', '未': '丑',
    '申': '寅', '酉': '卯', '戌': '辰', '亥': '巳'
  },
  
  // 丧门表（基于年支）
  sangmen: {
    '子': '寅', '丑': '卯', '寅': '辰', '卯': '巳',
    '辰': '午', '巳': '未', '午': '申', '未': '酉',
    '申': '戌', '酉': '亥', '戌': '子', '亥': '丑'
  },
  
  // 寡宿表（基于年支）
  guasu: {
    '申子辰': '戌', '寅午戌': '丑', '巳酉丑': '辰', '亥卯未': '未'
  }
};

// 重建的神煞计算函数
class AuthoritativeShenshaCalculator {
  
  // 计算天乙贵人
  calculateTianyiGuiren(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const expectedZhi = authoritativeShenshaTable.tianyi_guiren[dayGan] || [];
    
    const results = [];
    fourPillars.forEach((pillar, index) => {
      if (expectedZhi.includes(pillar.zhi)) {
        const positions = ['年柱', '月柱', '日柱', '时柱'];
        results.push({
          name: '天乙贵人',
          position: positions[index],
          pillar: pillar.gan + pillar.zhi,
          type: 'auspicious'
        });
      }
    });
    
    return results;
  }
  
  // 计算文昌贵人
  calculateWenchangGuiren(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const expectedZhi = authoritativeShenshaTable.wenchang_guiren[dayGan];
    
    const results = [];
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === expectedZhi) {
        const positions = ['年柱', '月柱', '日柱', '时柱'];
        results.push({
          name: '文昌贵人',
          position: positions[index],
          pillar: pillar.gan + pillar.zhi,
          type: 'auspicious'
        });
      }
    });
    
    return results;
  }
  
  // 计算桃花
  calculateTaohua(fourPillars) {
    const dayZhi = fourPillars[2].zhi;
    
    let expectedTaohua = null;
    for (const [group, taohua] of Object.entries(authoritativeShenshaTable.taohua)) {
      if (group.includes(dayZhi)) {
        expectedTaohua = taohua;
        break;
      }
    }
    
    const results = [];
    if (expectedTaohua) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === expectedTaohua) {
          const positions = ['年柱', '月柱', '日柱', '时柱'];
          results.push({
            name: '桃花',
            position: positions[index],
            pillar: pillar.gan + pillar.zhi,
            type: 'special'
          });
        }
      });
    }
    
    return results;
  }
  
  // 计算福星贵人
  calculateFuxingGuiren(fourPillars) {
    const yearZhi = fourPillars[0].zhi;
    const expectedZhi = authoritativeShenshaTable.fuxing_guiren[yearZhi] || [];
    
    const results = [];
    fourPillars.forEach((pillar, index) => {
      if (expectedZhi.includes(pillar.zhi)) {
        const positions = ['年柱', '月柱', '日柱', '时柱'];
        results.push({
          name: '福星贵人',
          position: positions[index],
          pillar: pillar.gan + pillar.zhi,
          type: 'auspicious'
        });
      }
    });
    
    return results;
  }
  
  // 计算灾煞
  calculateZaisha(fourPillars) {
    const yearZhi = fourPillars[0].zhi;
    const expectedZhi = authoritativeShenshaTable.zaisha[yearZhi];
    
    const results = [];
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === expectedZhi) {
        const positions = ['年柱', '月柱', '日柱', '时柱'];
        results.push({
          name: '灾煞',
          position: positions[index],
          pillar: pillar.gan + pillar.zhi,
          type: 'inauspicious'
        });
      }
    });
    
    return results;
  }
  
  // 计算丧门
  calculateSangmen(fourPillars) {
    const yearZhi = fourPillars[0].zhi;
    const expectedZhi = authoritativeShenshaTable.sangmen[yearZhi];
    
    const results = [];
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === expectedZhi) {
        const positions = ['年柱', '月柱', '日柱', '时柱'];
        results.push({
          name: '丧门',
          position: positions[index],
          pillar: pillar.gan + pillar.zhi,
          type: 'inauspicious'
        });
      }
    });
    
    return results;
  }
  
  // 计算寡宿
  calculateGuasu(fourPillars) {
    const yearZhi = fourPillars[0].zhi;
    
    let expectedZhi = null;
    for (const [group, guasu] of Object.entries(authoritativeShenshaTable.guasu)) {
      if (group.includes(yearZhi)) {
        expectedZhi = guasu;
        break;
      }
    }
    
    const results = [];
    if (expectedZhi) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === expectedZhi) {
          const positions = ['年柱', '月柱', '日柱', '时柱'];
          results.push({
            name: '寡宿',
            position: positions[index],
            pillar: pillar.gan + pillar.zhi,
            type: 'inauspicious'
          });
        }
      });
    }
    
    return results;
  }
  
  // 计算所有神煞
  calculateAllShensha(fourPillars) {
    const allResults = [];
    
    // 计算各种神煞
    allResults.push(...this.calculateTianyiGuiren(fourPillars));
    allResults.push(...this.calculateWenchangGuiren(fourPillars));
    allResults.push(...this.calculateTaohua(fourPillars));
    allResults.push(...this.calculateFuxingGuiren(fourPillars));
    allResults.push(...this.calculateZaisha(fourPillars));
    allResults.push(...this.calculateSangmen(fourPillars));
    allResults.push(...this.calculateGuasu(fourPillars));
    
    return allResults;
  }
}

// 验证重建的神煞系统
function verifyRebuiltSystem() {
  console.log('\n🔍 验证重建的神煞系统:');
  console.log('='.repeat(50));
  
  const calculator = new AuthoritativeShenshaCalculator();
  const results = calculator.calculateAllShensha(testData.fourPillars);
  
  console.log('计算结果:');
  results.forEach(result => {
    console.log(`${result.position}: ${result.name} (${result.pillar})`);
  });
  
  // 按柱位分组
  const byPillar = {
    year: [], month: [], day: [], hour: []
  };
  
  results.forEach(result => {
    const pillarMap = { '年柱': 'year', '月柱': 'month', '日柱': 'day', '时柱': 'hour' };
    const pillar = pillarMap[result.position];
    if (pillar) {
      byPillar[pillar].push(result.name);
    }
  });
  
  console.log('\n按柱位分组:');
  console.log(`年柱: ${byPillar.year.join(', ')}`);
  console.log(`月柱: ${byPillar.month.join(', ')}`);
  console.log(`日柱: ${byPillar.day.join(', ')}`);
  console.log(`时柱: ${byPillar.hour.join(', ')}`);
  
  console.log('\n与"问真八字"对比:');
  console.log(`年柱期望: ${wenZhenStandard.year.join(', ')}`);
  console.log(`月柱期望: ${wenZhenStandard.month.join(', ')}`);
  console.log(`日柱期望: ${wenZhenStandard.day.join(', ')}`);
  console.log(`时柱期望: ${wenZhenStandard.hour.join(', ')}`);
  
  return { results, byPillar };
}

// 执行验证
console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('参考标准: "问真八字"权威软件');

const verification = verifyRebuiltSystem();

console.log('\n📊 重建结果分析:');
console.log('='.repeat(40));
console.log(`✅ 已实现神煞: ${verification.results.length}个`);
console.log(`❌ 仍需实现: 月德合、天厨贵人、德秀贵人、童子煞、血刃、元辰、披麻`);

console.log('\n🚀 下一步计划:');
console.log('1. 继续实现缺失的神煞计算');
console.log('2. 应用重建的神煞系统到前端');
console.log('3. 验证所有神煞计算准确性');
console.log('4. 优化神煞显示格式');
