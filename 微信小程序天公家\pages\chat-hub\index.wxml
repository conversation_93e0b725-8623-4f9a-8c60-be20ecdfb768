<view class="chat-container">
  <!-- 顶部导航栏 -->
  <view class="custom-nav-bar">
    <view class="nav-bar-title">心理评估小程序</view>
    <view class="nav-bar-right">
      <view class="menu-icon">
        <text>...</text>
      </view>
      <view class="settings-icon" bindtap="goToSettings" hover-class="icon-hover">
        <image src="/assets/icons/settings.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 角色选择器 -->
  <view class="role-tabs">
    <view class="role-tab {{currentRole === 'student' ? 'active' : ''}}" 
      bindtap="switchRole" data-role="student" data-index="0">
      <text class="role-icon-text" style="color: #3b82f6;">学</text>
      <text>学生</text>
    </view>
    <view class="role-tab {{currentRole === 'parent' ? 'active' : ''}}" 
      bindtap="switchRole" data-role="parent" data-index="1">
      <text class="role-icon-text" style="color: #f59e0b;">家</text>
      <text>家长</text>
    </view>
    <view class="role-tab {{currentRole === 'teacher' ? 'active' : ''}}" 
      bindtap="switchRole" data-role="teacher" data-index="2">
      <text class="role-icon-text" style="color: #05cb6d;">师</text>
      <text>教师</text>
    </view>
    <view class="role-tab {{currentRole === 'doctor' ? 'active' : ''}}" 
      bindtap="switchRole" data-role="doctor" data-index="3">
      <text class="role-icon-text" style="color: #0ea5e9;">医</text>
      <text>医生</text>
    </view>
  </view>

  <!-- 日期显示 -->
  <view class="date-display">
    <text>{{currentDate}}</text>
    <text class="date-dot">·</text>
    <text>{{roleTexts[currentRole].title}}对话</text>
  </view>

  <!-- 底部边框指示器，显示当前角色的颜色 -->
  <view class="tab-indicator" style="background:{{roleColors[currentRole]}}"></view>

  <!-- 滑动内容区域 - 修改为垂直滑动 -->
  <swiper class="chat-swiper" current="{{currentIndex}}" bindchange="onSwiperChange" duration="300" vertical="{{vertical}}">
    <!-- 学生对话界面 -->
    <swiper-item class="swiper-item">
      <view class="role-content student-content">
        <!-- 欢迎消息区域，仅在没有消息时显示 -->
        <block wx:if="{{!studentMessages || studentMessages.length === 0}}">
          <view class="welcome-message student-welcome">
            <text class="welcome-title">开始学生评估</text>
            <text class="welcome-subtitle">通过对话了解你的学习情况、情绪状态和校园生活</text>
            <view class="welcome-start-button" bindtap="startStudentAssessment">
              <text>开始对话</text>
              <text class="iconfont">→</text>
            </view>
          </view>
        </block>

        <!-- 消息区域 -->
        <scroll-view wx:else scroll-y class="message-area" scroll-into-view="{{studentScrollId}}" scroll-with-animation="{{true}}">
          <block wx:for="{{studentMessages}}" wx:key="id">
            <!-- AI消息 -->
            <view class="message-wrapper ai-wrapper" wx:if="{{item.type === 'ai'}}" id="student-msg-{{item.id}}">
              <view class="avatar-container">
                <text class="role-icon-text" style="color: #3b82f6;">AI</text>
              </view>
              <view class="message ai-message student-ai-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
                
                <!-- 如果有选项列表 -->
                <view class="options-message" wx:if="{{item.options && item.options.length > 0}}">
                  <view class="options-list">
                    <view class="option-item student-option" 
                      wx:for="{{item.options}}" wx:for-item="option" wx:key="index" 
                      bindtap="selectOption" data-role="student" data-option="{{option}}">
                      <text class="option-content">{{option}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 用户消息 -->
            <view class="message-wrapper user-wrapper" wx:if="{{item.type === 'user'}}" id="student-msg-{{item.id}}">
              <view class="message user-message student-user-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 正在输入指示器 -->
          <view class="typing-wrapper" wx:if="{{studentTyping}}">
            <view class="avatar-container">
              <text class="role-icon-text" style="color: #3b82f6;">AI</text>
            </view>
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </scroll-view>
      </view>
    </swiper-item>

    <!-- 家长对话界面 -->
    <swiper-item class="swiper-item">
      <view class="role-content parent-content">
        <!-- 欢迎消息区域，仅在没有消息时显示 -->
        <block wx:if="{{!parentMessages || parentMessages.length === 0}}">
          <view class="welcome-message parent-welcome">
            <text class="welcome-title">开始家长评估</text>
            <text class="welcome-subtitle">了解孩子在家庭环境中的表现和行为习惯</text>
            <view class="welcome-start-button" bindtap="startParentAssessment">
              <text>开始对话</text>
              <text class="iconfont">→</text>
            </view>
          </view>
        </block>

        <!-- 消息区域 -->
        <scroll-view wx:else scroll-y class="message-area" scroll-into-view="{{parentScrollId}}" scroll-with-animation="{{true}}">
          <block wx:for="{{parentMessages}}" wx:key="id">
            <!-- AI消息 -->
            <view class="message-wrapper ai-wrapper" wx:if="{{item.type === 'ai'}}" id="parent-msg-{{item.id}}">
              <view class="avatar-container">
                <text class="role-icon-text" style="color: #f59e0b;">AI</text>
              </view>
              <view class="message ai-message parent-ai-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
                
                <!-- 如果有选项列表 -->
                <view class="options-message" wx:if="{{item.options && item.options.length > 0}}">
                  <view class="options-list">
                    <view class="option-item parent-option" 
                      wx:for="{{item.options}}" wx:for-item="option" wx:key="index" 
                      bindtap="selectOption" data-role="parent" data-option="{{option}}">
                      <text class="option-content">{{option}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 用户消息 -->
            <view class="message-wrapper user-wrapper" wx:if="{{item.type === 'user'}}" id="parent-msg-{{item.id}}">
              <view class="message user-message parent-user-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 正在输入指示器 -->
          <view class="typing-wrapper" wx:if="{{parentTyping}}">
            <view class="avatar-container">
              <text class="role-icon-text" style="color: #f59e0b;">AI</text>
            </view>
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </scroll-view>
      </view>
    </swiper-item>

    <!-- 教师对话界面 -->
    <swiper-item class="swiper-item">
      <view class="role-content teacher-content">
        <!-- 欢迎消息区域，仅在没有消息时显示 -->
        <block wx:if="{{!teacherMessages || teacherMessages.length === 0}}">
          <view class="welcome-message teacher-welcome">
            <text class="welcome-title">开始教师评估</text>
            <text class="welcome-subtitle">评估学生在学校环境中的表现和学习状况</text>
            <view class="welcome-start-button" bindtap="startTeacherAssessment">
              <text>开始对话</text>
              <text class="iconfont">→</text>
            </view>
          </view>
        </block>

        <!-- 消息区域 -->
        <scroll-view wx:else scroll-y class="message-area" scroll-into-view="{{teacherScrollId}}" scroll-with-animation="{{true}}">
          <block wx:for="{{teacherMessages}}" wx:key="id">
            <!-- AI消息 -->
            <view class="message-wrapper ai-wrapper" wx:if="{{item.type === 'ai'}}" id="teacher-msg-{{item.id}}">
              <view class="avatar-container">
                <text class="role-icon-text" style="color: #05cb6d;">AI</text>
              </view>
              <view class="message ai-message teacher-ai-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
                
                <!-- 如果有选项列表 -->
                <view class="options-message" wx:if="{{item.options && item.options.length > 0}}">
                  <view class="options-list">
                    <view class="option-item teacher-option" 
                      wx:for="{{item.options}}" wx:for-item="option" wx:key="index" 
                      bindtap="selectOption" data-role="teacher" data-option="{{option}}">
                      <text class="option-content">{{option}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 用户消息 -->
            <view class="message-wrapper user-wrapper" wx:if="{{item.type === 'user'}}" id="teacher-msg-{{item.id}}">
              <view class="message user-message teacher-user-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 正在输入指示器 -->
          <view class="typing-wrapper" wx:if="{{teacherTyping}}">
            <view class="avatar-container">
              <text class="role-icon-text" style="color: #05cb6d;">AI</text>
            </view>
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </scroll-view>
      </view>
    </swiper-item>

    <!-- 医生对话界面 -->
    <swiper-item class="swiper-item">
      <view class="role-content doctor-content">
        <!-- 欢迎消息区域，仅在没有消息时显示 -->
        <block wx:if="{{!doctorMessages || doctorMessages.length === 0}}">
          <view class="welcome-message doctor-welcome">
            <text class="welcome-title">开始医生评估</text>
            <text class="welcome-subtitle">专业医学视角评估儿童健康和发展状况</text>
            <view class="welcome-start-button" bindtap="startDoctorAssessment">
              <text>开始对话</text>
              <text class="iconfont">→</text>
            </view>
          </view>
        </block>

        <!-- 消息区域 -->
        <scroll-view wx:else scroll-y class="message-area" scroll-into-view="{{doctorScrollId}}" scroll-with-animation="{{true}}">
          <block wx:for="{{doctorMessages}}" wx:key="id">
            <!-- AI消息 -->
            <view class="message-wrapper ai-wrapper" wx:if="{{item.type === 'ai'}}" id="doctor-msg-{{item.id}}">
              <view class="avatar-container">
                <text class="role-icon-text" style="color: #0ea5e9;">AI</text>
              </view>
              <view class="message ai-message doctor-ai-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
                
                <!-- 如果有选项列表 -->
                <view class="options-message" wx:if="{{item.options && item.options.length > 0}}">
                  <view class="options-list">
                    <view class="option-item doctor-option" 
                      wx:for="{{item.options}}" wx:for-item="option" wx:key="index" 
                      bindtap="selectOption" data-role="doctor" data-option="{{option}}">
                      <text class="option-content">{{option}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 用户消息 -->
            <view class="message-wrapper user-wrapper" wx:if="{{item.type === 'user'}}" id="doctor-msg-{{item.id}}">
              <view class="message user-message doctor-user-message">
                <view class="message-content">
                  <text>{{item.content}}</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 正在输入指示器 -->
          <view class="typing-wrapper" wx:if="{{doctorTyping}}">
            <view class="avatar-container">
              <text class="role-icon-text" style="color: #0ea5e9;">AI</text>
            </view>
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </scroll-view>
      </view>
    </swiper-item>
  </swiper>

  <!-- 底部输入区域 -->
  <view class="input-area" style="border-top-color:{{roleColors[currentRole]}}">
    <input class="message-input" type="text" value="{{inputValue}}" bindinput="onInputChange" 
      placeholder="{{roleTexts[currentRole].placeholder}}" placeholder-style="color: rgba(255, 255, 255, 0.5);" 
      disabled="{{sending}}" confirm-type="send" bindconfirm="sendMessage" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage" hover-class="button-hover"
      style="background: {{inputValue ? roleColors[currentRole] : 'rgba(255, 255, 255, 0.1)'}}">
      <text class="iconfont send-icon">发</text>
    </view>
    <view class="voice-button" bindtap="activateVoiceInput" hover-class="button-hover">
      <text class="iconfont mic-icon">麦</text>
    </view>
  </view>

  <!-- 滑动手势提示 当切换到新角色时短暂显示 -->
  <view class="swipe-hint {{showSwipeHint ? 'show' : ''}}" style="background: {{roleColors[currentRole]}}">
    <text class="iconfont swipe-icon">↕</text>
    <text>上下滑动切换角色</text>
  </view>
</view> 