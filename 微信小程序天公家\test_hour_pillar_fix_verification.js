// test_hour_pillar_fix_verification.js
// 验证时柱计算修复效果

console.log('🔍 验证时柱计算修复效果');
console.log('='.repeat(60));

// 基础数据
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 修复后的精确时辰判断方法
function getHourZhiPrecise(hour, minute = 0) {
  // 将时间转换为分钟总数，便于精确判断
  const totalMinutes = hour * 60 + minute;
  
  // 时辰边界（以分钟为单位）
  if (totalMinutes >= 23 * 60 || totalMinutes < 1 * 60) return '子';
  if (totalMinutes >= 1 * 60 && totalMinutes < 3 * 60) return '丑';
  if (totalMinutes >= 3 * 60 && totalMinutes < 5 * 60) return '寅';
  if (totalMinutes >= 5 * 60 && totalMinutes < 7 * 60) return '卯';
  if (totalMinutes >= 7 * 60 && totalMinutes < 9 * 60) return '辰';
  if (totalMinutes >= 9 * 60 && totalMinutes < 11 * 60) return '巳';
  if (totalMinutes >= 11 * 60 && totalMinutes < 13 * 60) return '午';
  if (totalMinutes >= 13 * 60 && totalMinutes < 15 * 60) return '未';
  if (totalMinutes >= 15 * 60 && totalMinutes < 17 * 60) return '申';
  if (totalMinutes >= 17 * 60 && totalMinutes < 19 * 60) return '酉';
  if (totalMinutes >= 19 * 60 && totalMinutes < 21 * 60) return '戌';
  if (totalMinutes >= 21 * 60 && totalMinutes < 23 * 60) return '亥';
  return '子';
}

// 旧版本的时辰判断方法（只考虑小时）
function getHourZhiOld(hour) {
  if (hour >= 23 || hour < 1) return '子';
  if (hour >= 1 && hour < 3) return '丑';
  if (hour >= 3 && hour < 5) return '寅';
  if (hour >= 5 && hour < 7) return '卯';
  if (hour >= 7 && hour < 9) return '辰';
  if (hour >= 9 && hour < 11) return '巳';
  if (hour >= 11 && hour < 13) return '午';
  if (hour >= 13 && hour < 15) return '未';
  if (hour >= 15 && hour < 17) return '申';
  if (hour >= 17 && hour < 19) return '酉';
  if (hour >= 19 && hour < 21) return '戌';
  if (hour >= 21 && hour < 23) return '亥';
  return '子';
}

// 修复后的时柱计算方法
function calculateHourPillarFixed(hour, minute, dayGan) {
  const hourZhi = getHourZhiPrecise(hour, minute);
  const hourZhiIndex = dizhi.indexOf(hourZhi);

  const wushuDunTimeMap = {
    '甲': 0, '己': 0, // 甲己还加甲
    '乙': 2, '庚': 2, // 乙庚丙作初
    '丙': 4, '辛': 4, // 丙辛从戊起
    '丁': 6, '壬': 6, // 丁壬庚子居
    '戊': 8, '癸': 8  // 戊癸何方发，壬子是真途
  };

  const hourGanStart = wushuDunTimeMap[dayGan];
  const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;

  return {
    gan: tiangan[hourGanIndex],
    zhi: hourZhi,
    method: '修复后'
  };
}

// 旧版本的时柱计算方法
function calculateHourPillarOld(hour, dayGan) {
  const hourZhi = getHourZhiOld(hour);
  const hourZhiIndex = dizhi.indexOf(hourZhi);

  const wushuDunTimeMap = {
    '甲': 0, '己': 0,
    '乙': 2, '庚': 2,
    '丙': 4, '辛': 4,
    '丁': 6, '壬': 6,
    '戊': 8, '癸': 8
  };

  const hourGanStart = wushuDunTimeMap[dayGan];
  const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;

  return {
    gan: tiangan[hourGanIndex],
    zhi: hourZhi,
    method: '修复前'
  };
}

// 测试关键时间点
console.log('\n🧪 测试关键时间点:');

const testCases = [
  { hour: 16, minute: 54, desc: '真太阳时16:54（用户问题时间）' },
  { hour: 16, minute: 59, desc: '边界测试16:59' },
  { hour: 17, minute: 0, desc: '边界测试17:00' },
  { hour: 17, minute: 15, desc: '用户输入时间17:15' },
  { hour: 15, minute: 30, desc: '申时中间15:30' },
  { hour: 18, minute: 30, desc: '酉时中间18:30' }
];

const dayGan = '辛'; // 2025年7月31日的日干

console.log('时间\t\t修复前\t修复后\t正确性');
console.log('-'.repeat(50));

testCases.forEach(testCase => {
  const oldResult = calculateHourPillarOld(testCase.hour, dayGan);
  const newResult = calculateHourPillarFixed(testCase.hour, testCase.minute, dayGan);
  
  const timeStr = `${testCase.hour}:${testCase.minute.toString().padStart(2, '0')}`;
  const oldPillar = oldResult.gan + oldResult.zhi;
  const newPillar = newResult.gan + newResult.zhi;
  
  // 判断正确性
  let correctness = '';
  if (testCase.hour === 16 && testCase.minute === 54) {
    // 16:54应该是酉时，结果应该是丁酉
    correctness = newPillar === '丁酉' ? '✅ 修复成功' : '❌ 仍有问题';
  } else if (testCase.hour >= 17) {
    // 17点以后应该是酉时
    correctness = newResult.zhi === '酉' ? '✅' : '❌';
  } else if (testCase.hour < 17) {
    // 17点以前应该是申时
    correctness = newResult.zhi === '申' ? '✅' : '❌';
  }
  
  console.log(`${timeStr}\t\t${oldPillar}\t${newPillar}\t${correctness}`);
});

// 重点验证用户问题
console.log('\n🎯 重点验证用户问题:');
console.log('用户输入: 2025年7月31日 17:15');
console.log('真太阳时: 16:54');
console.log('日柱: 辛丑');
console.log('期望时柱: 丁酉');

const userProblemOld = calculateHourPillarOld(16, '辛'); // 旧版本：16:54 -> 16
const userProblemNew = calculateHourPillarFixed(16, 54, '辛'); // 新版本：16:54

console.log('\n结果对比:');
console.log(`修复前: ${userProblemOld.gan}${userProblemOld.zhi} (16:54被截断为16点)`);
console.log(`修复后: ${userProblemNew.gan}${userProblemNew.zhi} (16:54精确判断)`);

if (userProblemNew.gan + userProblemNew.zhi === '丁酉') {
  console.log('✅ 修复成功！现在16:54正确显示为丁酉');
} else {
  console.log('❌ 修复失败，仍需进一步调试');
}

// 测试时辰边界
console.log('\n⏰ 测试时辰边界精确性:');
const boundaryTests = [
  { hour: 16, minute: 59, expected: '申' },
  { hour: 17, minute: 0, expected: '酉' },
  { hour: 17, minute: 1, expected: '酉' },
  { hour: 18, minute: 59, expected: '酉' },
  { hour: 19, minute: 0, expected: '戌' }
];

boundaryTests.forEach(test => {
  const result = getHourZhiPrecise(test.hour, test.minute);
  const status = result === test.expected ? '✅' : '❌';
  console.log(`${test.hour}:${test.minute.toString().padStart(2, '0')} -> ${result} (期望${test.expected}) ${status}`);
});

console.log('\n📋 总结:');
console.log('修复内容:');
console.log('1. 使用getHourZhiPrecise()方法，考虑分钟数');
console.log('2. 16:54正确判断为酉时，而不是申时');
console.log('3. 辛日酉时 = 丁酉，而不是丙申');

console.log('\n✅ 验证完成');
