const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer');

// 创建分析器实例
const analyzer = new EnhancedPatternAnalyzer();

// 从格测试案例（修正版）
const testCases = [
  {
    name: '从财格案例',
    bazi: '壬申 壬子 丁酉 壬子', // 日主丁火极弱无根，水财星过旺，金泄火
    fourPillars: [
      { gan: '壬', zhi: '申' },  // 年柱
      { gan: '壬', zhi: '子' },  // 月柱
      { gan: '丁', zhi: '酉' },  // 日柱
      { gan: '壬', zhi: '子' }   // 时柱
    ]
  },
  {
    name: '从官格案例（问题案例）',
    bazi: '甲寅 甲寅 己酉 甲寅', // 日主己土极弱无根，甲木官杀过旺，金泄土
    fourPillars: [
      { gan: '甲', zhi: '寅' },  // 年柱
      { gan: '甲', zhi: '寅' },  // 月柱
      { gan: '己', zhi: '酉' },  // 日柱
      { gan: '甲', zhi: '寅' }   // 时柱
    ]
  }
];

console.log('🔍 从格案例分析');
console.log('='.repeat(60));

testCases.forEach((testCase, index) => {
  console.log(`\n📊 ${testCase.name}: ${testCase.bazi}`);
  console.log('-'.repeat(40));
  
  try {
    // 获取月令主气
    const birthDateTime = new Date(2000, 1, 15); // 假设日期
    const monthMainQi = analyzer.getMonthQi(birthDateTime, testCase.fourPillars[1].zhi);
    console.log('📅 月令主气:', monthMainQi);
    
    // 计算五行力量
    const elementPowers = analyzer.calculateElementPowers(testCase.fourPillars, monthMainQi);
    console.log('⚡ 五行力量:', elementPowers);
    
    // 获取日主五行
    const dayGan = testCase.fourPillars[2].gan;
    const dayElement = analyzer.wuxingMap[dayGan];
    const dayPowerPercentage = elementPowers.percentages[dayElement] || 0;
    
    console.log(`🎯 日主: ${dayGan}(${dayElement}), 力量占比: ${dayPowerPercentage.toFixed(1)}%`);
    
    // 计算克泄五行力量
    const drainElements = analyzer.getDrainElements(dayElement);
    let drainPower = 0;
    drainElements.forEach(element => {
      drainPower += elementPowers.percentages[element] || 0;
    });
    
    console.log(`🌊 克泄五行(${drainElements.join(', ')}): ${drainPower.toFixed(1)}%`);
    
    // 从格条件判定
    const isFromPattern = dayPowerPercentage < 10 && drainPower > 60;
    console.log(`📋 从格条件: 日主<10% (${dayPowerPercentage.toFixed(1)}%) && 克泄>60% (${drainPower.toFixed(1)}%)`);
    console.log(`🎯 符合从格: ${isFromPattern ? '✅ 是' : '❌ 否'}`);
    
    // 检查特殊格局判定
    const isSpecial = analyzer.isSpecialPattern(elementPowers, testCase.fourPillars);
    console.log(`🔥 特殊格局判定: ${isSpecial ? '✅ 是' : '❌ 否'}`);
    
    if (isSpecial) {
      const specialResult = analyzer.classifySpecialPattern(elementPowers, testCase.fourPillars);
      console.log('🎭 特殊格局结果:', specialResult);
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
});

console.log('\n🎉 从格案例分析完成');
