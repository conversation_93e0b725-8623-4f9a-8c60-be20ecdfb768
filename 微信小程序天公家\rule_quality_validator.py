#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则质量验证器
验证提取的规则是否符合质量标准
"""

import json
from typing import Dict, List, Tuple

class RuleQualityValidator:
    def __init__(self):
        self.quality_standards = {
            "最小文本长度": 30,
            "最低置信度": 0.85,
            "必需字段": ["rule_id", "pattern_name", "category", "original_text", "interpretations"],
            "文本质量检查": True,
            "重复检查": True
        }
    
    def validate_rules(self, rules: List[Dict]) -> Tuple[List[Dict], List[str]]:
        """验证规则质量"""
        valid_rules = []
        issues = []
        
        for i, rule in enumerate(rules):
            rule_issues = self.validate_single_rule(rule, i)
            
            if not rule_issues:
                valid_rules.append(rule)
            else:
                issues.extend(rule_issues)
        
        return valid_rules, issues
    
    def validate_single_rule(self, rule: Dict, index: int) -> List[str]:
        """验证单条规则"""
        issues = []
        
        # 检查必需字段
        for field in self.quality_standards["必需字段"]:
            if field not in rule or not rule[field]:
                issues.append(f"规则{index}: 缺少必需字段 {field}")
        
        # 检查文本长度
        original_text = rule.get("original_text", "")
        if len(original_text) < self.quality_standards["最小文本长度"]:
            issues.append(f"规则{index}: 文本长度不足 ({len(original_text)}字符)")
        
        # 检查置信度
        confidence = rule.get("confidence", 0)
        if confidence < self.quality_standards["最低置信度"]:
            issues.append(f"规则{index}: 置信度过低 ({confidence})")
        
        # 检查文本质量
        if self.quality_standards["文本质量检查"]:
            text_issues = self.check_text_quality(original_text)
            if text_issues:
                issues.extend([f"规则{index}: {issue}" for issue in text_issues])
        
        return issues
    
    def check_text_quality(self, text: str) -> List[str]:
        """检查文本质量"""
        issues = []
        
        # 检查OCR错误
        ocr_errors = ['氺', '灬', '釒', '本', '士']
        if any(error in text for error in ocr_errors):
            issues.append("包含OCR错误字符")
        
        # 检查文本完整性
        if text.endswith('...') or '...' in text:
            issues.append("文本可能不完整")
        
        # 检查标点符号
        if text.count('。') < 1:
            issues.append("缺少句号，可能不是完整句子")
        
        return issues
    
    def generate_quality_report(self, rules: List[Dict], issues: List[str]) -> str:
        """生成质量报告"""
        total_rules = len(rules) + len([i for i in issues if "规则" in i])
        valid_rules = len(rules)
        
        report = f"""
规则质量验证报告
================
总规则数: {total_rules}
有效规则数: {valid_rules}
通过率: {valid_rules/total_rules*100:.1f}%

质量问题:
"""
        for issue in issues[:10]:  # 只显示前10个问题
            report += f"- {issue}\n"
        
        if len(issues) > 10:
            report += f"... 还有 {len(issues)-10} 个问题\n"
        
        return report

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python rule_quality_validator.py <规则文件.json>")
        return
    
    filename = sys.argv[1]
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        validator = RuleQualityValidator()
        valid_rules, issues = validator.validate_rules(data.get('rules', []))
        
        # 更新数据
        data['rules'] = valid_rules
        data['metadata']['validated'] = True
        data['metadata']['validation_issues'] = len(issues)
        
        # 保存验证后的数据
        validated_filename = filename.replace('.json', '_validated.json')
        with open(validated_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 生成报告
        report = validator.generate_quality_report(valid_rules, issues)
        print(report)
        
        print(f"验证后的规则已保存到: {validated_filename}")
        
    except Exception as e:
        print(f"验证失败: {e}")

if __name__ == "__main__":
    main()
