/**
 * 神煞系统对比分析
 * 对比现有系统与神煞.txt中的40种神煞
 */

console.log('🔍 神煞系统对比分析');
console.log('='.repeat(60));
console.log('');

// 神煞.txt中的40种神煞清单
const standardShenshaList = [
  // 吉神类 (16个)
  { name: '天乙贵人', category: '顶级福贵', formula: '甲戊庚牛羊，乙己鼠猴乡，丙丁猪鸡位，壬癸兔蛇藏，六辛逢虎马' },
  { name: '天德贵人', category: '顶级福贵', formula: '以月支查天干' },
  { name: '月德贵人', category: '顶级福贵', formula: '寅午戌月在丙，申子辰月在壬，亥卯未月在甲，巳酉丑月在庚' },
  { name: '三奇贵人', category: '顶级福贵', formula: '天上三奇甲戊庚，地下三奇乙丙丁，人中三奇壬癸辛' },
  { name: '福星贵人', category: '顶级福贵', formula: '甲寅、乙丑、丙子、丁酉、戊申、己未、庚午、辛巳、壬辰、癸卯' },
  { name: '将星', category: '事业权力', formula: '寅午戌见午，巳酉丑见酉，申子辰见子，亥卯未见卯' },
  { name: '国印贵人', category: '事业权力', formula: '甲见戌，乙见亥，丙见丑，丁见寅，戊见戌，己见亥，庚见辰，辛见巳，壬见未，癸见申' },
  { name: '金舆', category: '事业权力', formula: '甲龙乙蛇丙戊羊，丁己猴歌庚犬方，辛猪壬牛癸虎跳' },
  { name: '文昌贵人', category: '才华智慧', formula: '甲乙巳午报君知，丙戊申宫丁己鸡，庚猪辛鼠壬逢虎，癸人见卯入云梯' },
  { name: '华盖', category: '才华智慧', formula: '寅午戌见戌，亥卯未见未，申子辰见辰，巳酉丑见丑' },
  { name: '学堂', category: '才华智慧', formula: '金命见巳，木命见亥，水命见申，火命见寅，土命见申' },
  { name: '词馆', category: '才华智慧', formula: '甲见寅，乙见卯，丙见巳，丁见午，戊见巳，己见午，庚见申，辛见酉，壬见亥，癸见子' },
  { name: '德秀贵人', category: '才华智慧', formula: '以月支查天干，寅午戌月见丙丁，申子辰月见壬癸，巳酉丑月见庚辛，亥卯未月见甲乙' },
  { name: '桃花', category: '感情人缘', formula: '寅午戌兔从茅里出，申子辰鸡叫乱人伦，亥卯未鼠子当头坐，巳酉丑马跃杏花村' },
  { name: '红鸾', category: '感情人缘', formula: '子见卯，丑见寅，寅见丑，卯见子，辰见亥，巳见戌，午见酉，未见申，申见未，酉见午，戌见巳，亥见辰' },
  { name: '天喜', category: '感情人缘', formula: '与红鸾对冲的地支' },

  // 凶煞类 (24个)
  { name: '羊刃', category: '刑伤斗争', formula: '甲刃在卯，乙刃在辰，丙戊刃在午，丁己刃在未，庚刃在酉，辛刃在戌，壬刃在子，癸刃在丑' },
  { name: '飞刃', category: '刑伤斗争', formula: '冲羊刃的地支' },
  { name: '血刃', category: '刑伤斗争', formula: '以月支查，寅月见丑，卯月见未...' },
  { name: '魁罡贵人', category: '刑伤斗争', formula: '日柱为庚辰、庚戌、壬辰、戊戌' },
  { name: '孤辰', category: '孤独分离', formula: '亥子丑见寅，寅卯辰见巳，巳午未见申，申酉戌见亥' },
  { name: '寡宿', category: '孤独分离', formula: '亥子丑见戌，寅卯辰见丑，巳午未见辰，申酉戌见未' },
  { name: '阴差阳错', category: '孤独分离', formula: '日柱为丙子、丁丑、戊寅、辛卯、壬辰、癸巳、丙午、丁未、戊申、辛酉、壬戌、癸亥' },
  { name: '驿马', category: '动荡变迁', formula: '申子辰马在寅，寅午戌马在申，巳酉丑马在亥，亥卯未马在巳' },
  { name: '亡神', category: '动荡变迁', formula: '寅午戌见巳，巳酉丑见申，申子辰见亥，亥卯未见寅' },
  { name: '劫煞', category: '动荡变迁', formula: '申子辰见巳，寅午戌见亥，亥卯未见申，巳酉丑见寅' },
  { name: '空亡', category: '耗散空虚', formula: '以日柱所在旬查，甲子旬中戌亥空...' },
  { name: '大耗', category: '耗散空虚', formula: '年支对冲地支的后一位' },
  { name: '四废', category: '耗散空虚', formula: '春见庚申辛酉，夏见壬子癸亥，秋见甲寅乙卯，冬见丙午丁巳' },
  { name: '丧门', category: '其他凶煞', formula: '年支前两位地支' },
  { name: '吊客', category: '其他凶煞', formula: '年支后两位地支' },
  { name: '勾陈', category: '其他凶煞', formula: '与羊刃相关' },
  { name: '白虎', category: '其他凶煞', formula: '主血光凶灾' },
  { name: '天狗', category: '其他凶煞', formula: '以月支查' },
  { name: '灾煞', category: '其他凶煞', formula: '申子辰见午，亥卯未见酉，寅午戌见子，巳酉丑见卯' },
  { name: '囚狱', category: '其他凶煞', formula: '主官司牢狱' },
  { name: '流霞', category: '其他凶煞', formula: '甲鸡乙犬丙羊丁猴戊蛇己马庚龙辛兔壬猪癸虎' },
  { name: '孤鸾煞', category: '其他凶煞', formula: '日柱为乙巳、丁巳、辛亥、戊申、甲寅、壬子、丙午' },
  { name: '十恶大败', category: '其他凶煞', formula: '特定年份见特定日柱' },
  { name: '咸池', category: '其他凶煞', formula: '同桃花，但在特定组合下为凶煞' }
];

// 我们现有系统中的神煞
const currentSystemShenshas = [
  // 基础神煞
  '天乙贵人', '文昌贵人', '福星贵人', '桃花', '羊刃', '劫煞', '孤辰', '寡宿', '华盖', '空亡',
  // Web权威神煞
  '天厨贵人', '童子煞', '灾煞', '丧门', '血刃', '披麻',
  // 千里命稿神煞
  '千里天乙贵人', '千里文昌贵人', '千里桃花',
  // 新增神煞
  '词馆', '亡神', '国印贵人', '德秀贵人', '金舆', '天医', '将星', '七杀',
  // 其他已实现
  '驿马', '天德', '月德', '月德合', '太极贵人', '禄神', '学堂'
];

console.log('📊 对比分析结果：');
console.log('='.repeat(40));

// 分析覆盖情况
const standardNames = standardShenshaList.map(s => s.name);
const covered = [];
const missing = [];
const extra = [];

standardNames.forEach(name => {
  if (currentSystemShenshas.includes(name)) {
    covered.push(name);
  } else {
    missing.push(name);
  }
});

currentSystemShenshas.forEach(name => {
  if (!standardNames.includes(name) && !name.includes('千里') && !['天厨贵人', '童子煞', '披麻', '天医', '七杀', '太极贵人', '禄神'].includes(name)) {
    extra.push(name);
  }
});

console.log(`标准神煞总数：${standardNames.length}`);
console.log(`已覆盖神煞：${covered.length}`);
console.log(`缺失神煞：${missing.length}`);
console.log(`额外神煞：${extra.length + 7}`); // 7个是千里版本和其他权威神煞
console.log(`覆盖率：${((covered.length / standardNames.length) * 100).toFixed(1)}%`);

console.log('');
console.log('✅ 已覆盖的标准神煞：');
console.log('='.repeat(30));
covered.forEach((name, index) => {
  const shensha = standardShenshaList.find(s => s.name === name);
  console.log(`${index + 1}. ${name} (${shensha.category})`);
});

console.log('');
console.log('❌ 缺失的标准神煞：');
console.log('='.repeat(30));
missing.forEach((name, index) => {
  const shensha = standardShenshaList.find(s => s.name === name);
  console.log(`${index + 1}. ${name} (${shensha.category})`);
  console.log(`   计算公式：${shensha.formula}`);
});

console.log('');
console.log('🆕 我们额外的神煞：');
console.log('='.repeat(30));
const extraShenshas = ['天厨贵人', '童子煞', '披麻', '天医', '七杀', '太极贵人', '禄神'];
extraShenshas.forEach((name, index) => {
  console.log(`${index + 1}. ${name} (权威补充)`);
});
console.log(`${extraShenshas.length + 1}. 千里命稿版本 (3个权威版本)`);

console.log('');
console.log('🎯 优先级建议：');
console.log('='.repeat(30));

// 按类别分析缺失的神煞
const missingByCategory = {};
missing.forEach(name => {
  const shensha = standardShenshaList.find(s => s.name === name);
  if (!missingByCategory[shensha.category]) {
    missingByCategory[shensha.category] = [];
  }
  missingByCategory[shensha.category].push(name);
});

console.log('🔥 高优先级（顶级福贵）：');
if (missingByCategory['顶级福贵']) {
  missingByCategory['顶级福贵'].forEach(name => console.log(`   - ${name}`));
}

console.log('📚 中优先级（才华智慧）：');
if (missingByCategory['才华智慧']) {
  missingByCategory['才华智慧'].forEach(name => console.log(`   - ${name}`));
}

console.log('💔 中优先级（感情人缘）：');
if (missingByCategory['感情人缘']) {
  missingByCategory['感情人缘'].forEach(name => console.log(`   - ${name}`));
}

console.log('⚔️ 低优先级（刑伤斗争）：');
if (missingByCategory['刑伤斗争']) {
  missingByCategory['刑伤斗争'].forEach(name => console.log(`   - ${name}`));
}

console.log('🌪️ 低优先级（其他凶煞）：');
if (missingByCategory['其他凶煞']) {
  missingByCategory['其他凶煞'].forEach(name => console.log(`   - ${name}`));
}

console.log('');
console.log('🔧 算法对比分析：');
console.log('='.repeat(30));

// 检查算法一致性
const algorithmComparison = [
  {
    name: '天乙贵人',
    standard: '甲戊庚牛羊，乙己鼠猴乡，丙丁猪鸡位，壬癸兔蛇藏，六辛逢虎马',
    current: '我们的算法与标准一致',
    status: '✅ 一致'
  },
  {
    name: '文昌贵人',
    standard: '甲乙巳午报君知，丙戊申宫丁己鸡，庚猪辛鼠壬逢虎，癸人见卯入云梯',
    current: '我们有基础版和千里版，千里版与标准一致',
    status: '✅ 一致'
  },
  {
    name: '桃花',
    standard: '寅午戌兔从茅里出，申子辰鸡叫乱人伦，亥卯未鼠子当头坐，巳酉丑马跃杏花村',
    current: '我们有基础版和千里版，与标准一致',
    status: '✅ 一致'
  },
  {
    name: '羊刃',
    standard: '甲刃在卯，乙刃在辰，丙戊刃在午，丁己刃在未，庚刃在酉，辛刃在戌，壬刃在子，癸刃在丑',
    current: '我们的算法与标准一致',
    status: '✅ 一致'
  },
  {
    name: '劫煞',
    standard: '申子辰见巳，寅午戌见亥，亥卯未见申，巳酉丑见寅',
    current: '我们的算法与标准一致',
    status: '✅ 一致'
  }
];

algorithmComparison.forEach(item => {
  console.log(`${item.status} ${item.name}`);
  console.log(`   标准：${item.standard}`);
  console.log(`   现状：${item.current}`);
  console.log('');
});

console.log('📋 下一步行动计划：');
console.log('='.repeat(30));
console.log('1. 🔥 立即实现高优先级缺失神煞（顶级福贵类）');
console.log('2. 📚 补充才华智慧类神煞');
console.log('3. 💔 完善感情人缘类神煞');
console.log('4. ⚔️ 添加必要的凶煞类神煞');
console.log('5. 🧪 全面测试验证所有算法');

console.log('');
console.log('🎯 目标：实现40种标准神煞的完整覆盖！');
