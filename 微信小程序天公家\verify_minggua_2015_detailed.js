/**
 * 详细验证2015年11月20日14:00的命卦计算
 * 对比多种算法，确定正确结果
 */

// 方法1：标准九宫八卦命卦算法
function calculateMingguaMethod1(year, gender = '男') {
  console.log('🔍 方法1：标准九宫八卦命卦算法');
  console.log('年份:', year, '性别:', gender);
  
  const yearLastTwo = year % 100;
  console.log('年份后两位:', yearLastTwo);
  
  let remainder;
  if (gender === '男') {
    // 男性：(100 - 年份后两位) ÷ 9 取余
    remainder = (100 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
    console.log('男性公式: (100 - 年份后两位) ÷ 9 取余');
    console.log('计算过程: (100 -', yearLastTwo, ') % 9 =', remainder);
  } else {
    // 女性：(年份后两位 + 5) ÷ 9 取余
    remainder = (yearLastTwo + 5) % 9;
    if (remainder === 0) remainder = 9;
    console.log('女性公式: (年份后两位 + 5) ÷ 9 取余');
    console.log('计算过程: (', yearLastTwo, '+ 5) % 9 =', remainder);
  }
  
  // 九宫八卦对应表
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  
  // 处理中宫（5）
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
    console.log('余数为5（中宫），', gender === '男' ? '男性取坤' : '女性取艮');
  }
  
  // 东四命/西四命
  const dongsiMing = ['震', '巽', '离', '坎'];
  const mingType = dongsiMing.includes(gua) ? '东四命' : '西四命';
  
  console.log('余数:', remainder, '→ 命卦:', gua, '→ 属性:', mingType);
  
  return {
    method: '标准九宫八卦',
    remainder: remainder,
    gua: gua,
    type: mingType,
    result: `${gua}卦 (${mingType})`
  };
}

// 方法2：简化年份取余算法
function calculateMingguaMethod2(year) {
  console.log('\n🔍 方法2：简化年份取余算法');
  console.log('年份:', year);
  
  const remainder = year % 8;
  console.log('计算过程:', year, '% 8 =', remainder);
  
  const guaMap = {
    0: '坤', 1: '震', 2: '巽', 3: '离', 4: '坤', 5: '兑', 6: '乾', 7: '坎'
  };
  
  const gua = guaMap[remainder];
  console.log('余数:', remainder, '→ 命卦:', gua);
  
  return {
    method: '简化年份取余',
    remainder: remainder,
    gua: gua,
    result: `${gua}卦`
  };
}

// 方法3：传统命理书籍算法
function calculateMingguaMethod3(year, gender = '男') {
  console.log('\n🔍 方法3：传统命理书籍算法');
  console.log('年份:', year, '性别:', gender);
  
  // 2000年前后算法可能不同
  const isAfter2000 = year >= 2000;
  console.log('是否2000年后:', isAfter2000);
  
  const yearLastTwo = year % 100;
  let remainder;
  
  if (gender === '男') {
    if (isAfter2000) {
      // 21世纪男性：(100 - 年份后两位) ÷ 9 取余
      remainder = (100 - yearLastTwo) % 9;
    } else {
      // 20世纪男性：(99 - 年份后两位) ÷ 9 取余
      remainder = (99 - yearLastTwo) % 9;
    }
    if (remainder === 0) remainder = 9;
    console.log('男性计算过程:', isAfter2000 ? `(100-${yearLastTwo})%9=${remainder}` : `(99-${yearLastTwo})%9=${remainder}`);
  } else {
    if (isAfter2000) {
      // 21世纪女性：(年份后两位 + 4) ÷ 9 取余
      remainder = (yearLastTwo + 4) % 9;
    } else {
      // 20世纪女性：(年份后两位 + 6) ÷ 9 取余
      remainder = (yearLastTwo + 6) % 9;
    }
    if (remainder === 0) remainder = 9;
    console.log('女性计算过程:', isAfter2000 ? `(${yearLastTwo}+4)%9=${remainder}` : `(${yearLastTwo}+6)%9=${remainder}`);
  }
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }
  
  const dongsiMing = ['震', '巽', '离', '坎'];
  const mingType = dongsiMing.includes(gua) ? '东四命' : '西四命';
  
  console.log('余数:', remainder, '→ 命卦:', gua, '→ 属性:', mingType);
  
  return {
    method: '传统命理书籍',
    remainder: remainder,
    gua: gua,
    type: mingType,
    result: `${gua}卦 (${mingType})`
  };
}

// 方法4：手工验证（逐步计算）
function calculateMingguaMethod4(year, gender = '男') {
  console.log('\n🔍 方法4：手工验证（逐步计算）');
  console.log('验证2015年男性命卦');
  
  console.log('步骤1：取年份后两位');
  const yearLastTwo = year % 100;
  console.log('2015 % 100 =', yearLastTwo);
  
  console.log('步骤2：应用男性公式');
  console.log('公式：(100 - 年份后两位) ÷ 9 取余数');
  const calculation = 100 - yearLastTwo;
  console.log('100 -', yearLastTwo, '=', calculation);
  
  const remainder = calculation % 9;
  console.log(calculation, '÷ 9 = 余数', remainder);
  
  console.log('步骤3：查找对应的卦');
  console.log('1=坎 2=坤 3=震 4=巽 5=中宫 6=乾 7=兑 8=艮 9=离');
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  console.log('余数', remainder, '对应:', gua);
  
  if (remainder === 5) {
    gua = '坤';
    console.log('中宫（5）男性取坤卦');
  }
  
  console.log('步骤4：确定东西四命');
  const dongsiMing = ['震', '巽', '离', '坎'];
  const mingType = dongsiMing.includes(gua) ? '东四命' : '西四命';
  console.log('震巽离坎=东四命，乾坤艮兑=西四命');
  console.log(gua, '属于', mingType);
  
  return {
    method: '手工验证',
    yearLastTwo: yearLastTwo,
    calculation: calculation,
    remainder: remainder,
    gua: gua,
    type: mingType,
    result: `${gua}卦 (${mingType})`
  };
}

// 主验证函数
function verifyMinggua2015() {
  console.log('🔮 详细验证2015年11月20日14:00的命卦');
  console.log('='.repeat(60));
  
  const year = 2015;
  const gender = '男'; // 假设性别
  
  console.log('📊 基本信息:');
  console.log('阳历日期: 2015年11月20日14:00');
  console.log('性别:', gender);
  console.log('');
  
  // 测试各种方法
  const method1 = calculateMingguaMethod1(year, gender);
  const method2 = calculateMingguaMethod2(year);
  const method3 = calculateMingguaMethod3(year, gender);
  const method4 = calculateMingguaMethod4(year, gender);
  
  // 汇总结果
  console.log('\n📊 各方法结果汇总:');
  console.log('='.repeat(50));
  console.log('方法1（标准九宫八卦）:', method1.result);
  console.log('方法2（简化年份取余）:', method2.result);
  console.log('方法3（传统命理书籍）:', method3.result);
  console.log('方法4（手工验证）:', method4.result);
  
  // 分析一致性
  console.log('\n🔍 结果分析:');
  const results = [method1.gua, method3.gua, method4.gua];
  const method2Result = method2.gua;
  
  const standardResult = method1.gua; // 以标准算法为准
  const isMethod2Consistent = method2Result === standardResult;
  
  console.log('标准算法组（方法1,3,4）结果:', results[0]);
  console.log('简化算法（方法2）结果:', method2Result);
  console.log('算法一致性:', isMethod2Consistent ? '✅ 一致' : '❌ 不一致');
  
  if (!isMethod2Consistent) {
    console.log('\n差异原因分析:');
    console.log('标准算法: (100-15)%9=4 → 巽卦');
    console.log('简化算法: 2015%8=7 → 坎卦');
    console.log('结论: 简化算法不正确');
  }
  
  return {
    standardResult: standardResult,
    method2Result: method2Result,
    consistent: isMethod2Consistent,
    correctAnswer: method1.result
  };
}

// 验证您提到的震卦
function checkZhenGua() {
  console.log('\n🔍 验证您提到的震卦:');
  console.log('='.repeat(40));
  
  console.log('如果命卦是震卦，对应的余数应该是3');
  console.log('让我们反推计算:');
  
  console.log('\n男性公式反推:');
  console.log('如果余数=3，则 (100-年份后两位)%9=3');
  console.log('可能的年份后两位: 7, 16, 25, 34, 43, 52, 61, 70, 79, 88, 97');
  console.log('2015年后两位是15，不在此列');
  
  console.log('\n女性公式反推:');
  console.log('如果余数=3，则 (年份后两位+5)%9=3');
  console.log('可能的年份后两位: 7, 16, 25, 34, 43, 52, 61, 70, 79, 88, 97');
  console.log('需要年份后两位为: -2, 7, 16, 25, 34, 43, 52, 61, 70, 79, 88, 97');
  console.log('2015年后两位是15，不在此列');
  
  console.log('\n结论: 2015年无论男女都不应该是震卦');
}

// 运行验证
const result = verifyMinggua2015();
checkZhenGua();

console.log('\n🎯 最终结论:');
console.log('2015年11月20日14:00的命卦（男性）:');
console.log('正确答案:', result.correctAnswer);
console.log('');
console.log('如果您的资料显示是震卦，可能的原因:');
console.log('1. 性别判断不同（女性）');
console.log('2. 使用了不同的计算公式');
console.log('3. 考虑了农历或其他因素');
console.log('4. 资料中的算法可能有误');
console.log('');
console.log('建议: 请确认性别和具体的计算方法');
