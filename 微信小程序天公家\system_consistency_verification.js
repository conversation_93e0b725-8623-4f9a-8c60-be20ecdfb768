/**
 * 系统一致性验证脚本
 * 验证四柱计算系统统一后的一致性和功能完整性
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 四柱计算系统一致性验证');
console.log('=' * 50);

// 1. 验证前端精确计算系统存在且完整
function verifyFrontendCalculationSystem() {
  console.log('\n📋 1. 验证前端精确计算系统...');
  
  try {
    const baziInputPath = 'pages/bazi-input/index.js';
    const content = fs.readFileSync(baziInputPath, 'utf8');
    
    // 检查关键方法存在
    const requiredMethods = [
      'calculatePreciseFourPillars',
      'calculatePreciseYearPillar',
      'calculatePreciseMonthPillar', 
      'calculatePreciseDayPillar',
      'calculatePreciseHourPillar'
    ];
    
    let methodsFound = 0;
    requiredMethods.forEach(method => {
      if (content.includes(method)) {
        console.log(`   ✅ ${method} - 存在`);
        methodsFound++;
      } else {
        console.log(`   ❌ ${method} - 缺失`);
      }
    });
    
    const completeness = (methodsFound / requiredMethods.length * 100).toFixed(1);
    console.log(`   📊 前端计算系统完整度: ${completeness}%`);
    
    return methodsFound === requiredMethods.length;
    
  } catch (error) {
    console.log(`   ❌ 验证失败: ${error.message}`);
    return false;
  }
}

// 2. 验证后端系统已删除
function verifyBackendSystemsRemoved() {
  console.log('\n📋 2. 验证后端计算系统已删除...');

  const deletedFiles = [
    'py/玉匣记八字排盘主系统.py',
    'py/完整八字分析系统.py',
    'py/精确四柱算法.py',
    'py/终极四柱查询系统.py',
    'py/八字命理系统集成方案.py',
    'utils/bazi_calculator.js',
    'yujiaji_web_api.py'
  ];
  
  let deletedCount = 0;
  deletedFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      console.log(`   ✅ ${file} - 已删除`);
      deletedCount++;
    } else {
      console.log(`   ❌ ${file} - 仍存在`);
    }
  });
  
  const deletionRate = (deletedCount / deletedFiles.length * 100).toFixed(1);
  console.log(`   📊 后端系统清理完成度: ${deletionRate}%`);
  
  return deletedCount === deletedFiles.length;
}

// 3. 验证引用一致性
function verifyReferenceConsistency() {
  console.log('\n📋 3. 验证引用一致性...');
  
  try {
    const baziInputPath = 'pages/bazi-input/index.js';
    const content = fs.readFileSync(baziInputPath, 'utf8');
    
    // 检查是否有对已删除文件的引用
    const badReferences = [
      'bazi_calculator.js',
      '玉匣记八字排盘主系统',
      'yujiaji_web_api'
    ];
    
    let badRefCount = 0;
    badReferences.forEach(ref => {
      if (content.includes(ref)) {
        console.log(`   ❌ 发现对已删除系统的引用: ${ref}`);
        badRefCount++;
      } else {
        console.log(`   ✅ 无引用: ${ref}`);
      }
    });
    
    // 检查统一数据源注释
    const hasUnifiedComments = content.includes('统一数据源') || content.includes('唯一数据源');
    console.log(`   ${hasUnifiedComments ? '✅' : '❌'} 统一数据源注释: ${hasUnifiedComments ? '已添加' : '缺失'}`);
    
    return badRefCount === 0 && hasUnifiedComments;
    
  } catch (error) {
    console.log(`   ❌ 验证失败: ${error.message}`);
    return false;
  }
}

// 4. 验证占卜页面修复
function verifyDivinationPageFix() {
  console.log('\n📋 4. 验证占卜页面修复...');

  try {
    // 检查简化版占卜计算器是否存在
    const divinationCalculatorExists = fs.existsSync('utils/divination_calculator.js');
    console.log(`   ${divinationCalculatorExists ? '✅' : '❌'} 简化版占卜计算器: ${divinationCalculatorExists ? '已创建' : '缺失'}`);

    if (divinationCalculatorExists) {
      // 检查占卜页面是否能正确引用
      const divinationPagePath = 'pages/divination-input/index.js';
      if (fs.existsSync(divinationPagePath)) {
        const content = fs.readFileSync(divinationPagePath, 'utf8');
        const hasCorrectRequire = content.includes("require('../../utils/divination_calculator')");
        console.log(`   ${hasCorrectRequire ? '✅' : '❌'} 占卜页面引用: ${hasCorrectRequire ? '正确' : '错误'}`);

        return divinationCalculatorExists && hasCorrectRequire;
      } else {
        console.log('   ❌ 占卜页面文件不存在');
        return false;
      }
    }

    return false;

  } catch (error) {
    console.log(`   ❌ 验证失败: ${error.message}`);
    return false;
  }
}

// 5. 验证核心功能完整性
function verifyCoreFeatures() {
  console.log('\n📋 4. 验证核心功能完整性...');
  
  try {
    const baziInputPath = 'pages/bazi-input/index.js';
    const content = fs.readFileSync(baziInputPath, 'utf8');
    
    // 检查核心功能
    const coreFeatures = [
      { name: '真太阳时校正', pattern: 'trueSolarTime' },
      { name: '四柱计算', pattern: 'calculatePreciseFourPillars' },
      { name: '农历转换', pattern: 'AuthoritativeLunarConverter' },
      { name: '五行分析', pattern: 'wuxing' },
      { name: '神煞计算', pattern: 'shensha' }
    ];
    
    let featuresFound = 0;
    coreFeatures.forEach(feature => {
      if (content.includes(feature.pattern)) {
        console.log(`   ✅ ${feature.name} - 功能存在`);
        featuresFound++;
      } else {
        console.log(`   ❌ ${feature.name} - 功能缺失`);
      }
    });
    
    const featureCompleteness = (featuresFound / coreFeatures.length * 100).toFixed(1);
    console.log(`   📊 核心功能完整度: ${featureCompleteness}%`);
    
    return featuresFound >= coreFeatures.length * 0.8; // 80%以上认为通过
    
  } catch (error) {
    console.log(`   ❌ 验证失败: ${error.message}`);
    return false;
  }
}

// 主验证函数
function runSystemConsistencyVerification() {
  console.log('🚀 开始系统一致性验证...\n');
  
  const results = {
    frontendSystem: verifyFrontendCalculationSystem(),
    backendRemoval: verifyBackendSystemsRemoved(),
    referenceConsistency: verifyReferenceConsistency(),
    divinationPageFix: verifyDivinationPageFix(),
    coreFeatures: verifyCoreFeatures()
  };
  
  console.log('\n📊 验证结果汇总:');
  console.log('=' * 30);
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    const testName = {
      frontendSystem: '前端精确计算系统',
      backendRemoval: '后端系统清理',
      referenceConsistency: '引用一致性',
      divinationPageFix: '占卜页面修复',
      coreFeatures: '核心功能完整性'
    }[test];
    
    console.log(`   ${status} ${testName}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const overallScore = (passedTests / totalTests * 100).toFixed(1);

  console.log(`\n🎯 总体评分: ${overallScore}% (${passedTests}/${totalTests})`);

  if (overallScore >= 100) {
    console.log('🎉 系统一致性验证完全通过！占卜页面错误已修复！');
  } else if (overallScore >= 80) {
    console.log('✅ 系统一致性验证基本通过，有少量问题需要修复');
  } else {
    console.log('⚠️ 系统一致性验证未通过，需要进一步修复');
  }
  
  return results;
}

// 执行验证
if (require.main === module) {
  runSystemConsistencyVerification();
}

module.exports = { runSystemConsistencyVerification };
