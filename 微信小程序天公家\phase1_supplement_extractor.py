#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段补充提取器
补充格局理论、调候理论、藏干理论等缺失的239条规则
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List, Tuple
from collections import defaultdict

class Phase1SupplementExtractor:
    def __init__(self):
        # 需要补充的理论类别
        self.supplement_targets = {
            "格局理论": {
                "current": 121,
                "target": 200,
                "needed": 79,
                "keywords": ["格局", "正格", "外格", "变格", "成格", "破格", "建禄格", "月刃格", "从格"],
                "ancient_books": ["千里命稿.txt", "渊海子平.docx"],
                "search_sections": ["格局篇", "八格", "正格", "外格"]
            },
            "调候理论": {
                "current": 119,
                "target": 200,
                "needed": 81,
                "keywords": ["调候", "寒暖", "燥湿", "春夏秋冬", "季节", "月令", "丙火", "壬水", "甲木"],
                "ancient_books": ["千里命稿.txt", "穷通宝鉴.txt"],
                "search_sections": ["调候篇", "寒暖", "燥湿", "四季"]
            },
            "藏干理论": {
                "current": 21,
                "target": 100,
                "needed": 79,
                "keywords": ["藏干", "本气", "中气", "余气", "地支藏干", "子藏", "丑藏", "寅藏"],
                "ancient_books": ["千里命稿.txt", "五行精纪.docx"],
                "search_sections": ["藏干", "地支", "本气"]
            }
        }
        
        # 质量标准
        self.quality_standards = {
            "min_confidence": 0.88,
            "min_text_length": 40,
            "max_text_length": 800
        }
        
        self.rule_id_counter = 1762  # 从第一阶段结束后继续编号
        
    def load_existing_rules(self, filename: str = "classical_rules_phase1_foundation_theory.json") -> List[Dict]:
        """加载已有的第一阶段规则"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            existing_rules = data.get('rules', [])
            print(f"✅ 加载已有规则: {len(existing_rules)}条")
            return existing_rules
            
        except Exception as e:
            print(f"❌ 加载已有规则失败: {e}")
            return []
    
    def extract_from_original_rules(self, theory_name: str, theory_config: Dict) -> List[Dict]:
        """从原始规则中提取补充规则"""
        print(f"🔍 从原始规则中补充{theory_name}...")
        
        try:
            with open("classical_rules_complete.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            original_rules = data.get('rules', [])
            keywords = theory_config["keywords"]
            needed = theory_config["needed"]
            
            # 筛选相关规则
            candidate_rules = []
            for rule in original_rules:
                text = rule.get('original_text', '')
                confidence = rule.get('confidence', 0)
                
                # 检查关键词匹配
                keyword_matches = sum(1 for keyword in keywords if keyword in text)
                
                # 质量筛选
                if (keyword_matches >= 2 and 
                    confidence >= self.quality_standards["min_confidence"] and
                    len(text) >= self.quality_standards["min_text_length"] and
                    len(text) <= self.quality_standards["max_text_length"]):
                    
                    # 计算相关性分数
                    relevance_score = keyword_matches * confidence
                    rule['relevance_score'] = relevance_score
                    candidate_rules.append(rule)
            
            # 按相关性排序并选择
            candidate_rules.sort(key=lambda x: x['relevance_score'], reverse=True)
            selected_rules = candidate_rules[:needed]
            
            # 增强规则
            enhanced_rules = []
            for rule in selected_rules:
                enhanced_rule = self._enhance_rule(rule, theory_name)
                enhanced_rules.append(enhanced_rule)
            
            print(f"  从原始规则补充了 {len(enhanced_rules)} 条{theory_name}规则")
            return enhanced_rules
            
        except Exception as e:
            print(f"❌ 从原始规则提取{theory_name}失败: {e}")
            return []
    
    def extract_from_ancient_books(self, theory_name: str, theory_config: Dict) -> List[Dict]:
        """从古籍中提取补充规则"""
        print(f"📚 从古籍中补充{theory_name}...")
        
        extracted_rules = []
        books_dir = "古籍资料"
        
        for book_file in theory_config["ancient_books"]:
            book_path = os.path.join(books_dir, book_file)
            
            if not os.path.exists(book_path):
                continue
            
            try:
                content = self._load_book_content(book_path)
                if content:
                    book_rules = self._extract_rules_from_content(
                        content, theory_name, theory_config, book_file
                    )
                    extracted_rules.extend(book_rules)
                    
            except Exception as e:
                print(f"  ⚠️ 处理{book_file}时出错: {e}")
        
        print(f"  从古籍补充了 {len(extracted_rules)} 条{theory_name}规则")
        return extracted_rules
    
    def _load_book_content(self, file_path: str) -> str:
        """加载古籍内容"""
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_path.endswith('.docx'):
                try:
                    from docx import Document
                    doc = Document(file_path)
                    return '\n'.join([p.text for p in doc.paragraphs])
                except ImportError:
                    print(f"  ⚠️ 缺少docx库，跳过{file_path}")
                    return ""
            else:
                return ""
        except Exception as e:
            print(f"  ❌ 加载{file_path}失败: {e}")
            return ""
    
    def _extract_rules_from_content(self, content: str, theory_name: str, 
                                   theory_config: Dict, book_file: str) -> List[Dict]:
        """从内容中提取规则"""
        rules = []
        keywords = theory_config["keywords"]
        search_sections = theory_config["search_sections"]
        
        # 按章节分割内容
        sections = self._split_content_by_sections(content, search_sections)
        
        for section_name, section_content in sections.items():
            # 在相关章节中查找规则
            sentences = re.split(r'[。；！？]', section_content)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 30:
                    continue
                
                # 检查关键词匹配
                keyword_matches = sum(1 for keyword in keywords if keyword in sentence)
                
                if keyword_matches >= 1 and len(sentence) >= 40:
                    rule = self._create_rule_from_sentence(
                        sentence, theory_name, book_file, section_name
                    )
                    rules.append(rule)
                    
                    # 限制每个理论的古籍提取数量
                    if len(rules) >= 20:
                        break
            
            if len(rules) >= 20:
                break
        
        return rules
    
    def _split_content_by_sections(self, content: str, search_sections: List[str]) -> Dict[str, str]:
        """按章节分割内容"""
        sections = {}
        
        for section_name in search_sections:
            # 查找包含章节名的段落
            pattern = rf'[^。]*{section_name}[^。]*。[^第]*?(?=第|$)'
            matches = re.findall(pattern, content, re.DOTALL)
            
            if matches:
                sections[section_name] = '\n'.join(matches)
        
        # 如果没有找到特定章节，使用全文
        if not sections:
            sections["全文"] = content
        
        return sections
    
    def _create_rule_from_sentence(self, sentence: str, theory_name: str, 
                                  book_file: str, section_name: str) -> Dict:
        """从句子创建规则"""
        book_name = book_file.split('.')[0]
        
        rule = {
            "rule_id": f"{theory_name}_补充_{self.rule_id_counter:03d}",
            "pattern_name": f"{theory_name}补充规则",
            "category": self._get_category_from_theory(theory_name),
            "book_source": book_name,
            "original_text": self._clean_text(sentence),
            "interpretations": self._generate_interpretation(theory_name, sentence),
            "confidence": 0.90,  # 古籍提取的置信度稍低
            "conditions": f"{theory_name}的应用条件",
            "advanced_cleaned": True,
            "advanced_cleaned_at": datetime.now().isoformat(),
            "extraction_phase": "第一阶段补充",
            "theory_category": theory_name,
            "source_section": section_name,
            "supplement_extraction": True
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _enhance_rule(self, original_rule: Dict, theory_name: str) -> Dict:
        """增强原始规则"""
        enhanced_rule = original_rule.copy()
        
        # 更新规则ID
        enhanced_rule["rule_id"] = f"{theory_name}_补充_{self.rule_id_counter:03d}"
        
        # 添加补充标记
        enhanced_rule["theory_category"] = theory_name
        enhanced_rule["supplement_extraction"] = True
        enhanced_rule["extraction_phase"] = "第一阶段补充"
        enhanced_rule["enhanced_at"] = datetime.now().isoformat()
        
        # 清理文本
        if "original_text" in enhanced_rule:
            enhanced_rule["original_text"] = self._clean_text(enhanced_rule["original_text"])
        
        # 生成解释
        if not enhanced_rule.get("interpretations"):
            enhanced_rule["interpretations"] = self._generate_interpretation(
                theory_name, enhanced_rule.get("original_text", "")
            )
        
        self.rule_id_counter += 1
        return enhanced_rule
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        
        # 移除常见的OCR错误
        ocr_fixes = {
            '氺': '水', '灬': '火', '釒': '金', '本': '木', '士': '土'
        }
        for old, new in ocr_fixes.items():
            text = text.replace(old, new)
        
        # 标准化标点
        text = text.replace('，', '，').replace('。', '。')
        
        return text.strip()
    
    def _generate_interpretation(self, theory_name: str, text: str) -> str:
        """生成解释"""
        interpretations = {
            "格局理论": "格局的成立条件、特征和用神选择方法",
            "调候理论": "根据季节寒暖燥湿调节命局平衡的理论",
            "藏干理论": "地支藏干的作用机制和影响分析"
        }
        
        base_interpretation = interpretations.get(theory_name, f"{theory_name}的理论和应用")
        
        # 根据文本内容添加具体说明
        if "春" in text or "夏" in text or "秋" in text or "冬" in text:
            base_interpretation += "，特别适用于相应季节的命局分析"
        
        return base_interpretation
    
    def _get_category_from_theory(self, theory_name: str) -> str:
        """根据理论名称获取分类"""
        category_mapping = {
            "格局理论": "正格",
            "调候理论": "调候格局",
            "藏干理论": "强弱判断"
        }
        return category_mapping.get(theory_name, "综合理论")
    
    def execute_supplement(self) -> Dict:
        """执行补充提取"""
        print("🚀 启动第一阶段补充提取...")
        
        # 加载已有规则
        existing_rules = self.load_existing_rules()
        
        all_supplement_rules = []
        supplement_summary = {}
        
        # 为每个需要补充的理论提取规则
        for theory_name, theory_config in self.supplement_targets.items():
            print(f"\n📋 补充{theory_name} (需要{theory_config['needed']}条)")
            
            # 从原始规则提取
            original_rules = self.extract_from_original_rules(theory_name, theory_config)
            
            # 从古籍提取
            ancient_rules = self.extract_from_ancient_books(theory_name, theory_config)
            
            # 合并规则
            theory_rules = original_rules + ancient_rules
            
            # 限制数量
            needed = theory_config["needed"]
            if len(theory_rules) > needed:
                # 按质量排序选择最好的
                theory_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
                theory_rules = theory_rules[:needed]
            
            all_supplement_rules.extend(theory_rules)
            supplement_summary[theory_name] = {
                "needed": needed,
                "extracted": len(theory_rules),
                "from_original": len(original_rules),
                "from_ancient": len(ancient_rules)
            }
        
        # 合并所有规则
        total_rules = existing_rules + all_supplement_rules
        
        # 生成输出数据
        output_data = {
            "metadata": {
                "phase": "第一阶段：基础理论层建设（完整版）",
                "supplement_date": datetime.now().isoformat(),
                "original_count": len(existing_rules),
                "supplement_count": len(all_supplement_rules),
                "total_count": len(total_rules),
                "target_achieved": len(total_rules) >= 2000,
                "supplement_summary": supplement_summary
            },
            "rules": total_rules
        }
        
        return {
            "success": True,
            "data": output_data,
            "summary": {
                "原有规则": len(existing_rules),
                "补充规则": len(all_supplement_rules),
                "总规则数": len(total_rules),
                "目标完成": f"{len(total_rules)}/2000",
                "完成率": f"{len(total_rules)/2000*100:.1f}%"
            }
        }

def main():
    """主函数"""
    extractor = Phase1SupplementExtractor()
    
    # 执行补充提取
    result = extractor.execute_supplement()
    
    if result.get("success"):
        # 保存完整的第一阶段结果
        output_filename = "classical_rules_phase1_complete.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        # 打印结果
        print("\n" + "="*80)
        print("第一阶段补充提取完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        # 详细补充统计
        supplement_summary = result["data"]["metadata"]["supplement_summary"]
        print(f"\n📊 补充详情:")
        for theory, stats in supplement_summary.items():
            print(f"  {theory}: {stats['extracted']}/{stats['needed']} "
                  f"(原始:{stats['from_original']}, 古籍:{stats['from_ancient']})")
        
        print(f"\n✅ 第一阶段完整数据已保存到: {output_filename}")
        
        # 检查是否达到目标
        total_count = summary["总规则数"]
        if total_count >= 2000:
            print(f"🎉 第一阶段目标达成！准备启动第二阶段...")
        else:
            remaining = 2000 - total_count
            print(f"⚠️ 距离目标还差 {remaining} 条规则，需要进一步补充")
        
    else:
        print(f"❌ 补充提取失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
