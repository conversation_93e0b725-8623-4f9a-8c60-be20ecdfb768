/* pages/divination-input/index.wxss */

/* 滚动容器 - 强制启用滚动 */
.scroll-container {
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
  /* 隐藏所有滚动指示器 */
  -webkit-scrollbar: none;
  scrollbar-width: none;
  /* 禁用滚动指示器 */
  overflow-scrolling: touch;
}

/* 隐藏微信小程序的滚动指示器 */
.scroll-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* 隐藏可能的分页指示器 */
.scroll-container::after,
.scroll-container::before {
  display: none !important;
}

/* 页面根容器 - 确保滚动正常 */
page {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.container {
  min-height: 100vh;
  background: transparent; /* 背景移到scroll-container */
  position: relative;
  padding: 40rpx 30rpx 120rpx;
  box-sizing: border-box;
  /* 确保内容可以正常显示 */
  width: 100%;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.04) 0%, transparent 35%),
    radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.04) 0%, transparent 35%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.02) 0%, transparent 30%);
  z-index: 0;
  /* 确保背景装饰不影响滚动 */
  pointer-events: none;
}

/* 顶部标题区域 */
.header-section {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 60rpx;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.master-avatar {
  width: 120rpx; /* 统一头像尺寸 */
  height: 120rpx; /* 统一头像尺寸 */
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3px solid rgba(139, 69, 19, 0.6);
  object-fit: cover; /* 确保图片填充方式一致 */
  box-shadow: 0 4rpx 15rpx rgba(139, 69, 19, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.title-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.sub-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
}

.description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 32rpx;
  line-height: 1.6;
  text-align: center;
}

/* 占卜方式选择 */
.method-selection {
  position: relative;
  z-index: 1;
  margin-bottom: 50rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 30rpx;
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.method-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.method-card.selected {
  background: rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.5);
  transform: scale(1.02);
}

.method-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.method-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}

.method-badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #333;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: bold;
}

/* 基本信息收集区域 */
.basic-info-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.info-input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.required-mark {
  color: #ff6b6b;
  margin-left: 5rpx;
  font-weight: bold;
}



/* 真太阳时显示区域 */
.solar-time-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.solar-time-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
  font-weight: bold;
}

.solar-time-info {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 4rpx solid #ff6b35;
  backdrop-filter: blur(10rpx);
}

.solar-time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.solar-time-item:last-child {
  margin-bottom: 0;
}

.solar-time-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.solar-time-value {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.solar-time-value.corrected {
  color: #ff6b35;
}

.solar-time-value.diff {
  color: #f7931e;
}

/* 位置获取失败区域 */
.location-failed-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.location-failed {
  display: flex;
  align-items: center;
  background: rgba(255, 193, 7, 0.2);
  border: 2px solid rgba(255, 193, 7, 0.4);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.failed-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.failed-text {
  flex: 1;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

.retry-btn {
  font-size: 24rpx;
  color: #007bff;
  padding: 8rpx 15rpx;
  border: 2rpx solid #007bff;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.9);
}

/* 问题输入区域 */
.question-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.question-types {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.type-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.type-tag.selected {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.6);
  color: #FFFFFF;
}

.question-input {
  width: 100%;
  min-height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  padding: 25rpx;
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 1.5;
  box-sizing: border-box;
}

.question-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input-counter {
  text-align: right;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 数字输入区域 */
.number-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.number-inputs {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.number-input {
  flex: 1;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.number-hint {
  text-align: center;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 时间信息区域 */
.time-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.time-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.time-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.location-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.location-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}

.time-note {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

/* 时间选择器样式 */
.time-picker-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.picker-item:last-child {
  margin-bottom: 0;
}

.picker-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
  margin-right: 20rpx;
}

.picker-input {
  flex: 1;
}

.picker-display {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.picker-display:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.time-hint {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20rpx;
  line-height: 1.4;
}

/* 底部操作区域 */
.action-section {
  position: relative;
  z-index: 1;
  margin-top: 60rpx;
}

.divination-rules {
  text-align: center;
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.start-divination-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(107, 91, 115, 0.8) 0%, rgba(168, 146, 109, 0.8) 100%);
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.start-divination-btn.active {
  background: linear-gradient(135deg, rgba(107, 91, 115, 1) 0%, rgba(168, 146, 109, 1) 100%);
  box-shadow: 0 8rpx 25rpx rgba(107, 91, 115, 0.4);
}

.start-divination-btn.disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
}

.btn-decoration {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.start-divination-btn.active:active .btn-decoration {
  left: 100%;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: #FFFFFF;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #FFFFFF;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主题适配 */
.tarot-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
}

/* GPS授权说明样式 */
.gps-notice {
  margin-top: 25rpx;
  padding: 25rpx;
  background: rgba(116, 185, 255, 0.08);
  border-radius: 15rpx;
  border: 1px solid rgba(116, 185, 255, 0.2);
  display: flex;
  align-items: flex-start;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  margin-top: 5rpx;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.notice-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 8rpx;
  display: block;
}

.notice-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 10rpx;
  display: block;
}

.notice-formula {
  font-size: 22rpx;
  color: rgba(116, 185, 255, 0.9);
  font-family: 'Courier New', monospace;
  background: rgba(116, 185, 255, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  display: block;
}

/* 全局隐藏滚动指示器 */
page {
  /* 隐藏页面级别的滚动指示器 */
  -webkit-scrollbar: none;
  scrollbar-width: none;
}

/* 隐藏任何可能的滚动点指示器 */
.scroll-indicator,
.scroll-dots,
.pagination-dots,
[class*="indicator"],
[class*="dot"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 隐藏微信小程序可能的内置滚动指示器 */
scroll-view::after,
scroll-view::before,
.scroll-container::after,
.scroll-container::before {
  content: none !important;
  display: none !important;
}

/* 隐藏右侧可能出现的滚动指示器点 */
.scroll-container {
  position: relative;
}

.scroll-container::after {
  display: none !important;
}

/* 隐藏滚动指示器 */
.scroll-indicator,
.scroll-bar,
.indicator {
  display: none !important;
  visibility: hidden !important;
}

/* 隐藏可能的垂直滚动指示器 */
.wx-scroll-view-indicator,
.wx-scroll-indicator,
.scroll-view-indicator {
  display: none !important;
  opacity: 0 !important;
}
