#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
古籍全文智能处理器
专门处理《渊海子平》《三命通会》《穷通宝鉴》三本古籍
"""

import re
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
import hashlib

@dataclass
class ExtractedRule:
    """提取的规则数据结构"""
    rule_id: str
    pattern_name: str
    category: str
    original_text: str
    book_source: str
    chapter: str
    conditions: List[Dict]
    interpretations: Dict
    confidence: float
    created_at: str

class AdvancedTextProcessor:
    """高级文本处理器"""
    
    def __init__(self):
        self.pattern_keywords = {
            "格局名称": [
                "正官格", "偏官格", "七杀格", "正财格", "偏财格", "正印格", "偏印格",
                "食神格", "伤官格", "比肩格", "劫财格", "建禄格", "羊刃格",
                "从财格", "从官格", "从儿格", "从杀格", "化气格", "炎上格", "润下格",
                "稼穑格", "金白水清", "木火通明", "水火既济", "金水相涵",
                "伤官配印", "财官双美", "杀印相生", "食神制杀", "羊刃驾杀"
            ],
            "十神": ["正官", "偏官", "七杀", "正财", "偏财", "正印", "偏印", "食神", "伤官", "比肩", "劫财"],
            "五行": ["金", "木", "水", "火", "土"],
            "天干": ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"],
            "地支": ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"],
            "月份": ["正月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "冬月", "腊月"],
            "强弱": ["强", "弱", "旺", "衰", "休", "囚", "死"],
            "吉凶": ["吉", "凶", "贵", "富", "贫", "夭", "寿", "福", "祸"]
        }
        
        self.book_configs = {
            "渊海子平": {
                "chapter_pattern": r"(卷[一二三四五六七八九十]+|第[一二三四五六七八九十]+卷)",
                "priority": 1,
                "specialties": ["格局判断", "正格理论", "基础八字"]
            },
            "三命通会": {
                "chapter_pattern": r"(卷第[一二三四五六七八九十]+|第[一二三四五六七八九十]+章)",
                "priority": 2,
                "specialties": ["全面理论", "变格分析", "神煞应用"]
            },
            "穷通宝鉴": {
                "chapter_pattern": r"([正二三四五六七八九十冬腊]+月|春夏秋冬)",
                "priority": 1,
                "specialties": ["用神取用", "调候理论", "月令分析"]
            }
        }
    
    def process_book(self, book_name: str, full_text: str) -> List[ExtractedRule]:
        """处理单本古籍的完整文本"""
        print(f"📚 开始处理《{book_name}》全文...")
        print(f"📄 文本长度: {len(full_text)} 字符")
        
        # 1. 文本预处理
        cleaned_text = self._preprocess_full_text(full_text)
        print(f"✅ 文本预处理完成，清理后长度: {len(cleaned_text)} 字符")
        
        # 2. 智能章节分割
        chapters = self._intelligent_chapter_split(cleaned_text, book_name)
        print(f"📑 识别章节数量: {len(chapters)}")
        
        # 3. 批量规则提取
        all_rules = []
        for i, (chapter_title, chapter_content) in enumerate(chapters):
            print(f"🔍 处理章节 {i+1}/{len(chapters)}: {chapter_title}")
            rules = self._extract_rules_from_chapter(chapter_content, book_name, chapter_title)
            all_rules.extend(rules)
            print(f"   提取规则: {len(rules)} 条")
        
        # 4. 数据去重和验证
        validated_rules = self._validate_and_deduplicate(all_rules)
        print(f"✅ 《{book_name}》处理完成，有效规则: {len(validated_rules)} 条")
        
        return validated_rules
    
    def _preprocess_full_text(self, text: str) -> str:
        """全文预处理"""
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 去除页眉页脚
        text = re.sub(r'第\s*\d+\s*页', '', text)
        text = re.sub(r'页码：\d+', '', text)
        
        # 标准化标点符号
        punctuation_map = {
            '，': ',', '。': '.', '；': ';', '：': ':', '？': '?', '！': '!',
            '"': '"', '"': '"', ''': "'", ''': "'", '（': '(', '）': ')'
        }
        for old, new in punctuation_map.items():
            text = text.replace(old, new)
        
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    
    def _intelligent_chapter_split(self, text: str, book_name: str) -> List[Tuple[str, str]]:
        """智能章节分割"""
        config = self.book_configs.get(book_name, {})
        chapter_pattern = config.get("chapter_pattern", r"(第[一二三四五六七八九十]+章)")
        
        # 查找章节标题
        chapter_matches = list(re.finditer(chapter_pattern, text))
        
        if not chapter_matches:
            # 如果没有找到标准章节，尝试其他分割方式
            return self._fallback_chapter_split(text, book_name)
        
        chapters = []
        for i, match in enumerate(chapter_matches):
            chapter_title = match.group(1)
            start_pos = match.end()
            
            # 确定章节结束位置
            if i + 1 < len(chapter_matches):
                end_pos = chapter_matches[i + 1].start()
            else:
                end_pos = len(text)
            
            chapter_content = text[start_pos:end_pos].strip()
            
            if len(chapter_content) > 50:  # 过滤太短的章节
                chapters.append((chapter_title, chapter_content))
        
        return chapters
    
    def _fallback_chapter_split(self, text: str, book_name: str) -> List[Tuple[str, str]]:
        """备用章节分割方法"""
        # 按段落分割
        paragraphs = text.split('\n\n')
        chapters = []
        
        current_chapter = ""
        chapter_count = 1
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if len(paragraph) > 20:
                current_chapter += paragraph + "\n\n"
                
                # 每1000字符作为一个章节
                if len(current_chapter) > 1000:
                    chapters.append((f"第{chapter_count}部分", current_chapter.strip()))
                    current_chapter = ""
                    chapter_count += 1
        
        # 处理最后一个章节
        if current_chapter.strip():
            chapters.append((f"第{chapter_count}部分", current_chapter.strip()))
        
        return chapters
    
    def _extract_rules_from_chapter(self, chapter_content: str, book_name: str, chapter_title: str) -> List[ExtractedRule]:
        """从章节中提取规则"""
        rules = []
        
        # 按句子分割
        sentences = self._split_into_sentences(chapter_content)
        
        for sentence in sentences:
            if len(sentence) < 10:  # 过滤太短的句子
                continue
            
            # 检查是否包含格局相关内容
            if self._contains_pattern_info(sentence):
                rule = self._parse_sentence_to_rule(sentence, book_name, chapter_title)
                if rule:
                    rules.append(rule)
        
        return rules
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子"""
        # 按标点符号分割
        sentences = re.split(r'[.。;；!！?？]', text)
        
        # 清理和过滤
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 5:
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def _contains_pattern_info(self, sentence: str) -> bool:
        """检查句子是否包含格局信息"""
        # 检查格局关键词
        for pattern_name in self.pattern_keywords["格局名称"]:
            if pattern_name in sentence:
                return True
        
        # 检查十神组合
        shishen_count = sum(1 for shishen in self.pattern_keywords["十神"] if shishen in sentence)
        if shishen_count >= 2:
            return True
        
        # 检查五行相关
        wuxing_count = sum(1 for wuxing in self.pattern_keywords["五行"] if wuxing in sentence)
        if wuxing_count >= 2:
            return True
        
        # 检查吉凶判断
        if any(word in sentence for word in self.pattern_keywords["吉凶"]):
            if any(word in sentence for word in self.pattern_keywords["十神"]):
                return True
        
        return False
    
    def _parse_sentence_to_rule(self, sentence: str, book_name: str, chapter_title: str) -> Optional[ExtractedRule]:
        """将句子解析为规则"""
        try:
            # 提取格局名称
            pattern_name = self._extract_pattern_name(sentence)
            if not pattern_name:
                pattern_name = "未分类格局"
            
            # 生成规则ID
            rule_id = self._generate_rule_id(sentence, book_name)
            
            # 提取条件
            conditions = self._extract_conditions_advanced(sentence)
            
            # 提取解释
            interpretations = self._extract_interpretations_advanced(sentence)
            
            # 计算置信度
            confidence = self._calculate_confidence(sentence, conditions, interpretations)
            
            # 分类格局
            category = self._classify_pattern_advanced(pattern_name, sentence)
            
            rule = ExtractedRule(
                rule_id=rule_id,
                pattern_name=pattern_name,
                category=category,
                original_text=sentence,
                book_source=book_name,
                chapter=chapter_title,
                conditions=conditions,
                interpretations=interpretations,
                confidence=confidence,
                created_at=datetime.now().isoformat()
            )
            
            return rule
            
        except Exception as e:
            print(f"⚠️ 解析句子失败: {e}")
            return None
    
    def _extract_pattern_name(self, sentence: str) -> Optional[str]:
        """提取格局名称"""
        # 优先匹配已知格局名称
        for pattern_name in self.pattern_keywords["格局名称"]:
            if pattern_name in sentence:
                return pattern_name
        
        # 尝试提取格局模式
        pattern_matches = re.findall(r'([一-龥]{2,6}格)', sentence)
        if pattern_matches:
            return pattern_matches[0]
        
        # 尝试提取十神组合
        shishen_in_sentence = [s for s in self.pattern_keywords["十神"] if s in sentence]
        if len(shishen_in_sentence) >= 2:
            return "".join(shishen_in_sentence[:2])
        
        return None
    
    def _extract_conditions_advanced(self, sentence: str) -> List[Dict]:
        """高级条件提取"""
        conditions = []
        
        # 提取日主强弱
        if "日主" in sentence:
            for strength in self.pattern_keywords["强弱"]:
                if strength in sentence:
                    conditions.append({
                        "type": "日主强弱",
                        "condition": f"日主{strength}",
                        "weight": 0.4,
                        "source": "原文直接描述"
                    })
        
        # 提取十神条件
        for shishen in self.pattern_keywords["十神"]:
            if shishen in sentence:
                # 检查十神状态
                if "透" in sentence:
                    conditions.append({
                        "type": "十神状态",
                        "condition": f"{shishen}透干",
                        "weight": 0.3,
                        "source": "透干分析"
                    })
                elif "藏" in sentence:
                    conditions.append({
                        "type": "十神状态", 
                        "condition": f"{shishen}藏支",
                        "weight": 0.2,
                        "source": "藏支分析"
                    })
        
        # 提取月令条件
        for month in self.pattern_keywords["月份"]:
            if month in sentence:
                conditions.append({
                    "type": "月令条件",
                    "condition": f"{month}生人",
                    "weight": 0.3,
                    "source": "月令分析"
                })
        
        # 提取五行条件
        wuxing_in_sentence = [w for w in self.pattern_keywords["五行"] if w in sentence]
        if len(wuxing_in_sentence) >= 2:
            conditions.append({
                "type": "五行关系",
                "condition": f"{''.join(wuxing_in_sentence)}相关",
                "weight": 0.25,
                "source": "五行分析"
            })
        
        return conditions
    
    def _extract_interpretations_advanced(self, sentence: str) -> Dict:
        """高级解释提取"""
        interpretations = {
            "original": sentence,
            "personality": "",
            "career": "",
            "wealth": "",
            "relationships": "",
            "health": "",
            "general": ""
        }
        
        # 性格特质关键词
        personality_keywords = {
            "聪明": "聪明智慧", "智慧": "智慧过人", "机敏": "机敏灵活",
            "稳重": "性格稳重", "温和": "性情温和", "刚强": "性格刚强"
        }
        
        # 事业关键词
        career_keywords = {
            "官": "适合从政或管理", "贵": "有贵人相助", "文": "适合文化工作",
            "武": "适合武职或体力工作", "技": "有技术专长", "艺": "有艺术天赋"
        }
        
        # 财运关键词
        wealth_keywords = {
            "富": "财运亨通", "财": "有聚财能力", "贫": "财运不佳",
            "破": "有破财之象", "得": "能够获得财富"
        }
        
        # 提取各方面解释
        for keyword, meaning in personality_keywords.items():
            if keyword in sentence:
                interpretations["personality"] += meaning + ";"
        
        for keyword, meaning in career_keywords.items():
            if keyword in sentence:
                interpretations["career"] += meaning + ";"
        
        for keyword, meaning in wealth_keywords.items():
            if keyword in sentence:
                interpretations["wealth"] += meaning + ";"
        
        # 生成通用解释
        if any(word in sentence for word in ["吉", "贵", "福"]):
            interpretations["general"] = "整体运势较好，多有吉象"
        elif any(word in sentence for word in ["凶", "祸", "灾"]):
            interpretations["general"] = "需要注意防范，谨慎行事"
        else:
            interpretations["general"] = sentence[:50] + "..." if len(sentence) > 50 else sentence
        
        return interpretations
    
    def _calculate_confidence(self, sentence: str, conditions: List[Dict], interpretations: Dict) -> float:
        """计算置信度"""
        confidence = 0.5  # 基础置信度
        
        # 根据条件数量调整
        confidence += len(conditions) * 0.1
        
        # 根据解释完整性调整
        non_empty_interpretations = sum(1 for v in interpretations.values() if v.strip())
        confidence += non_empty_interpretations * 0.05
        
        # 根据句子长度调整
        if len(sentence) > 20:
            confidence += 0.1
        if len(sentence) > 50:
            confidence += 0.1
        
        # 根据关键词密度调整
        total_keywords = sum(len([k for k in keywords if k in sentence]) 
                           for keywords in self.pattern_keywords.values())
        confidence += min(total_keywords * 0.02, 0.2)
        
        return min(confidence, 1.0)
    
    def _classify_pattern_advanced(self, pattern_name: str, sentence: str) -> str:
        """高级格局分类"""
        if not pattern_name:
            return "其他"
        
        # 正格判断
        zhengge_keywords = ["正官", "偏官", "正财", "偏财", "正印", "偏印", "食神", "伤官"]
        if any(keyword in pattern_name for keyword in zhengge_keywords):
            return "正格"
        
        # 变格判断
        biange_keywords = ["从", "化", "专", "润下", "炎上", "稼穑"]
        if any(keyword in pattern_name for keyword in biange_keywords):
            return "变格"
        
        # 特殊格局判断
        if "格" in pattern_name:
            return "特殊格局"
        
        # 根据句子内容判断
        if "格" in sentence:
            return "格局相关"
        elif any(word in sentence for word in ["用神", "忌神"]):
            return "用神理论"
        elif any(word in sentence for word in ["大运", "流年"]):
            return "运程分析"
        
        return "其他"
    
    def _generate_rule_id(self, sentence: str, book_name: str) -> str:
        """生成规则ID"""
        # 使用句子内容和书名生成唯一ID
        content = f"{book_name}_{sentence}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:12]
    
    def _validate_and_deduplicate(self, rules: List[ExtractedRule]) -> List[ExtractedRule]:
        """验证和去重"""
        # 按置信度排序
        rules.sort(key=lambda x: x.confidence, reverse=True)
        
        # 去重（基于相似内容）
        unique_rules = []
        seen_patterns = set()
        
        for rule in rules:
            # 创建去重键
            dedup_key = f"{rule.pattern_name}_{rule.category}"
            
            if dedup_key not in seen_patterns:
                # 验证规则有效性
                if self._validate_rule(rule):
                    unique_rules.append(rule)
                    seen_patterns.add(dedup_key)
        
        return unique_rules
    
    def _validate_rule(self, rule: ExtractedRule) -> bool:
        """验证规则有效性"""
        # 检查必要字段
        if not rule.pattern_name or not rule.original_text:
            return False
        
        # 检查置信度
        if rule.confidence < 0.3:
            return False
        
        # 检查内容长度
        if len(rule.original_text) < 10:
            return False
        
        return True

class DatabaseBuilder:
    """数据库构建器"""
    
    def __init__(self, db_path: str = "占卜系统/data/bazi_classical_complete.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建完整的规则表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS classical_rules (
                rule_id TEXT PRIMARY KEY,
                pattern_name TEXT NOT NULL,
                category TEXT,
                original_text TEXT,
                book_source TEXT,
                chapter TEXT,
                conditions TEXT,
                interpretations TEXT,
                confidence REAL,
                created_at TEXT
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pattern_name ON classical_rules(pattern_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON classical_rules(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_book_source ON classical_rules(book_source)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_confidence ON classical_rules(confidence)')
        
        # 创建统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processing_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                book_name TEXT,
                total_rules INTEGER,
                avg_confidence REAL,
                processing_time TEXT,
                created_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_rules(self, rules: List[ExtractedRule], book_name: str) -> Dict:
        """保存规则到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        saved_count = 0
        start_time = datetime.now()
        
        for rule in rules:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO classical_rules 
                    (rule_id, pattern_name, category, original_text, book_source, 
                     chapter, conditions, interpretations, confidence, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    rule.rule_id,
                    rule.pattern_name,
                    rule.category,
                    rule.original_text,
                    rule.book_source,
                    rule.chapter,
                    json.dumps(rule.conditions, ensure_ascii=False),
                    json.dumps(rule.interpretations, ensure_ascii=False),
                    rule.confidence,
                    rule.created_at
                ))
                saved_count += 1
            except Exception as e:
                print(f"⚠️ 保存规则失败: {e}")
        
        # 保存统计信息
        processing_time = (datetime.now() - start_time).total_seconds()
        avg_confidence = sum(r.confidence for r in rules) / len(rules) if rules else 0
        
        cursor.execute('''
            INSERT INTO processing_stats 
            (book_name, total_rules, avg_confidence, processing_time, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            book_name,
            saved_count,
            avg_confidence,
            f"{processing_time:.2f}秒",
            datetime.now().isoformat()
        ))
        
        conn.commit()
        conn.close()
        
        return {
            "saved_count": saved_count,
            "avg_confidence": avg_confidence,
            "processing_time": processing_time
        }
    
    def get_statistics(self) -> Dict:
        """获取处理统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 总体统计
        cursor.execute('SELECT COUNT(*) FROM classical_rules')
        total_rules = cursor.fetchone()[0]
        
        cursor.execute('SELECT AVG(confidence) FROM classical_rules')
        avg_confidence = cursor.fetchone()[0] or 0
        
        # 按书籍统计
        cursor.execute('''
            SELECT book_source, COUNT(*), AVG(confidence) 
            FROM classical_rules 
            GROUP BY book_source
        ''')
        book_stats = cursor.fetchall()
        
        # 按类别统计
        cursor.execute('''
            SELECT category, COUNT(*) 
            FROM classical_rules 
            GROUP BY category
        ''')
        category_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            "total_rules": total_rules,
            "avg_confidence": round(avg_confidence, 3),
            "book_stats": book_stats,
            "category_stats": category_stats
        }

def main():
    """主处理函数"""
    print("🚀 古籍全文智能处理器启动")
    print("=" * 60)
    
    processor = AdvancedTextProcessor()
    db_builder = DatabaseBuilder()
    
    print("✅ 处理器初始化完成")
    print("📋 准备处理以下古籍:")
    print("   1. 《渊海子平》- 格局判断权威")
    print("   2. 《三命通会》- 全面理论集成")
    print("   3. 《穷通宝鉴》- 用神取用专著")
    
    print("\n🎯 处理能力展示:")
    print("   ✅ 智能章节分割")
    print("   ✅ 格局规则提取")
    print("   ✅ 条件自动识别")
    print("   ✅ 现代化解读")
    print("   ✅ 数据去重验证")
    print("   ✅ 数据库自动构建")
    
    print("\n📤 请上传古籍文件，我将立即开始处理！")

if __name__ == "__main__":
    main()
