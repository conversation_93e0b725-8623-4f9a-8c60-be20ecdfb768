/**
 * 合并隋唐五代第一批次名人数据
 */

const suiTangPart1 = require('../data/sui_tang_celebrities_batch1.js');
const suiTangPart2 = require('../data/sui_tang_celebrities_batch1_part2.js');
const suiTangSupplement = require('../data/sui_tang_celebrities_supplement.js');
const suiTangFinal3 = require('../data/sui_tang_final_3.js');
const fs = require('fs');
const path = require('path');

class SuiTangBatch1Merger {
  constructor() {
    this.mergedData = null;
  }

  /**
   * 合并两个部分的数据
   */
  mergeData() {
    console.log('🔄 开始合并隋唐五代第一批次数据...');
    
    // 基础结构来自第一部分
    this.mergedData = {
      metadata: {
        ...suiTangPart1.metadata,
        totalRecords: suiTangPart1.celebrities.length + suiTangPart2.celebrities.length + suiTangSupplement.celebrities.length + suiTangFinal3.celebrities.length,
        description: "隋唐五代时期帝王名臣命理数据库第一批次完整版"
      },
      celebrities: [
        ...suiTangPart1.celebrities,
        ...suiTangPart2.celebrities,
        ...suiTangSupplement.celebrities,
        ...suiTangFinal3.celebrities
      ]
    };

    console.log(`✅ 合并完成:`);
    console.log(`   - 第一部分: ${suiTangPart1.celebrities.length} 位名人`);
    console.log(`   - 第二部分: ${suiTangPart2.celebrities.length} 位名人`);
    console.log(`   - 补充部分: ${suiTangSupplement.celebrities.length} 位名人`);
    console.log(`   - 最后3位: ${suiTangFinal3.celebrities.length} 位名人`);
    console.log(`   - 总计: ${this.mergedData.celebrities.length} 位名人`);

    return this.mergedData;
  }

  /**
   * 检查是否达到目标数量
   */
  checkTargetCount() {
    const currentCount = this.mergedData.celebrities.length;
    const targetCount = 25;

    if (currentCount >= targetCount) {
      console.log(`✅ 已达到目标数量 ${targetCount} 位 (实际: ${currentCount} 位)`);
      return true;
    } else {
      console.log(`📝 当前 ${currentCount} 位，距离目标 ${targetCount} 位还差 ${targetCount - currentCount} 位`);
      return false;
    }
  }

  /**
   * 验证数据质量
   */
  validateData() {
    console.log('\n🔍 验证数据质量...');
    
    const celebrities = this.mergedData.celebrities;
    let totalVerificationScore = 0;
    let issueCount = 0;

    celebrities.forEach((celebrity, index) => {
      // 检查必要字段
      if (!celebrity.id || !celebrity.basicInfo || !celebrity.bazi || !celebrity.pattern) {
        console.log(`❌ 第${index + 1}位名人缺少必要字段: ${celebrity.basicInfo?.name || '未知'}`);
        issueCount++;
      }

      // 检查验证分数
      if (celebrity.verification && celebrity.verification.algorithmMatch) {
        totalVerificationScore += celebrity.verification.algorithmMatch;
      }
    });

    const averageScore = totalVerificationScore / celebrities.length;
    
    console.log(`📊 数据质量报告:`);
    console.log(`   - 总名人数: ${celebrities.length}`);
    console.log(`   - 平均验证度: ${averageScore.toFixed(3)}`);
    console.log(`   - 发现问题: ${issueCount} 个`);
    console.log(`   - 质量等级: ${averageScore >= 0.9 ? '优秀' : averageScore >= 0.8 ? '良好' : '需改进'}`);

    return { averageScore, issueCount };
  }

  /**
   * 保存合并后的数据
   */
  saveData() {
    const outputPath = path.join(__dirname, '../data/sui_tang_celebrities_batch1_complete.js');
    
    const content = `/**
 * 隋唐五代历史名人数据库 - 第一批次完整版
 * 包含25位隋唐五代时期的帝王名臣
 * 基于真实古籍文献记录
 */

const suiTangCelebritiesBatch1Complete = ${JSON.stringify(this.mergedData, null, 2)};

module.exports = suiTangCelebritiesBatch1Complete;
`;

    fs.writeFileSync(outputPath, content, 'utf8');
    console.log(`💾 数据已保存到: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 执行完整的合并流程
   */
  async execute() {
    console.log('🚀 开始隋唐五代第一批次数据合并流程');
    console.log('============================================================');
    
    try {
      // 1. 合并数据
      this.mergeData();

      // 2. 检查目标数量
      this.checkTargetCount();

      // 3. 验证数据质量
      const validation = this.validateData();
      
      // 4. 保存数据
      const outputPath = this.saveData();
      
      console.log('\n🎉 合并流程完成!');
      console.log('============================================================');
      console.log('📊 最终统计:');
      console.log(`   - 总名人数: ${this.mergedData.celebrities.length}`);
      console.log(`   - 平均验证度: ${validation.averageScore.toFixed(3)}`);
      console.log(`   - 数据质量: ${validation.averageScore >= 0.9 ? '优秀' : '良好'}`);
      console.log(`   - 输出文件: ${outputPath}`);
      
      return this.mergedData;
      
    } catch (error) {
      console.error('❌ 合并流程失败:', error);
      throw error;
    }
  }
}

module.exports = SuiTangBatch1Merger;
