/**
 * 宋元明清历史名人数据库第二批次合并工具
 * 合并宋朝、元朝、明朝、清朝数据
 */

const songDynasty = require('../data/song_dynasty_celebrities.js');
const songDynastyPart2 = require('../data/song_dynasty_celebrities_part2.js');
const yuanDynasty = require('../data/yuan_dynasty_celebrities.js');
const mingDynasty = require('../data/ming_dynasty_celebrities.js');
const qingDynasty = require('../data/qing_dynasty_celebrities.js');
const fs = require('fs');
const path = require('path');

class SongYuanMingQingBatch2Merger {
  constructor() {
    this.mergedData = {
      metadata: {
        batchNumber: 2,
        dynasties: ["宋朝", "元朝", "明朝", "清朝"],
        period: "960-1912年",
        totalRecords: 0,
        description: "宋元明清时期历史名人命理数据库第二批次完整版",
        dataSources: [],
        creationDate: new Date().toISOString().split('T')[0],
        verificationStandard: "专家交叉校验+古籍依据双重认证"
      },
      celebrities: []
    };
  }

  /**
   * 合并所有朝代数据
   */
  mergeData() {
    console.log('🔄 开始合并宋元明清第二批次数据...');
    
    // 合并宋朝数据（两部分）
    const allSongCelebrities = [
      ...songDynasty.celebrities,
      ...songDynastyPart2.celebrities
    ];
    
    // 基础结构
    this.mergedData = {
      metadata: {
        ...this.mergedData.metadata,
        totalRecords: allSongCelebrities.length + yuanDynasty.celebrities.length + 
                     mingDynasty.celebrities.length + qingDynasty.celebrities.length,
        dataSources: [
          ...songDynasty.metadata.dataSource.split(' '),
          ...yuanDynasty.metadata.dataSource.split(' '),
          ...mingDynasty.metadata.dataSource.split(' '),
          ...qingDynasty.metadata.dataSource.split(' ')
        ].filter((source, index, arr) => arr.indexOf(source) === index) // 去重
      },
      celebrities: [
        ...allSongCelebrities,
        ...yuanDynasty.celebrities,
        ...mingDynasty.celebrities,
        ...qingDynasty.celebrities
      ]
    };

    console.log(`✅ 合并完成:`);
    console.log(`   - 宋朝: ${allSongCelebrities.length} 位名人`);
    console.log(`   - 元朝: ${yuanDynasty.celebrities.length} 位名人`);
    console.log(`   - 明朝: ${mingDynasty.celebrities.length} 位名人`);
    console.log(`   - 清朝: ${qingDynasty.celebrities.length} 位名人`);
    console.log(`   - 总计: ${this.mergedData.celebrities.length} 位名人`);
  }

  /**
   * 验证数据质量
   */
  validateData() {
    console.log('🔍 验证数据质量...');
    
    let totalVerification = 0;
    let verificationCount = 0;
    let issues = [];
    
    this.mergedData.celebrities.forEach((celebrity, index) => {
      // 检查必要字段
      if (!celebrity.basicInfo || !celebrity.basicInfo.name) {
        issues.push(`第${index + 1}位名人缺少基本信息`);
      }
      
      if (!celebrity.bazi || !celebrity.bazi.fullBazi) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少八字信息`);
      }
      
      if (!celebrity.pattern || !celebrity.pattern.mainPattern) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少格局信息`);
      }
      
      if (!celebrity.verification || !celebrity.verification.algorithmMatch) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少验证信息`);
      } else {
        totalVerification += celebrity.verification.algorithmMatch;
        verificationCount++;
      }
    });
    
    const averageVerification = verificationCount > 0 ? totalVerification / verificationCount : 0;
    const qualityLevel = averageVerification >= 0.9 ? '优秀' : '良好';
    
    console.log(`📊 数据质量报告:`);
    console.log(`   - 总名人数: ${this.mergedData.celebrities.length}`);
    console.log(`   - 平均验证度: ${averageVerification.toFixed(3)}`);
    console.log(`   - 发现问题: ${issues.length} 个`);
    console.log(`   - 质量等级: ${qualityLevel}`);
    
    if (issues.length > 0) {
      console.log(`⚠️  问题详情:`);
      issues.forEach(issue => console.log(`     - ${issue}`));
    }
    
    return {
      averageScore: averageVerification,
      issues: issues,
      qualityLevel: qualityLevel
    };
  }

  /**
   * 保存合并后的数据
   */
  saveData() {
    const outputPath = path.join(__dirname, '../data/song_yuan_ming_qing_batch2_complete.js');
    
    const fileContent = `/**
 * 宋元明清历史名人数据库第二批次完整版
 * 自动生成于 ${new Date().toISOString().split('T')[0]}
 * 包含25位宋元明清时期重要历史人物
 */

const songYuanMingQingBatch2Complete = ${JSON.stringify(this.mergedData, null, 2)};

module.exports = songYuanMingQingBatch2Complete;`;

    fs.writeFileSync(outputPath, fileContent, 'utf8');
    console.log(`💾 数据已保存到: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 执行完整的合并流程
   */
  async execute() {
    try {
      console.log('🚀 开始宋元明清第二批次数据合并流程');
      console.log('============================================================');
      
      // 1. 合并数据
      this.mergeData();
      
      // 2. 验证数据质量
      const validation = this.validateData();
      
      // 3. 保存数据
      const outputPath = this.saveData();
      
      console.log('\n🎉 合并流程完成!');
      console.log('============================================================');
      console.log('📊 最终统计:');
      console.log(`   - 总名人数: ${this.mergedData.celebrities.length}`);
      console.log(`   - 平均验证度: ${validation.averageScore.toFixed(3)}`);
      console.log(`   - 数据质量: ${validation.qualityLevel}`);
      console.log(`   - 输出文件: ${outputPath}`);
      
      return {
        success: true,
        data: this.mergedData,
        validation: validation,
        outputPath: outputPath
      };
      
    } catch (error) {
      console.error('❌ 合并流程失败:', error);
      throw error;
    }
  }
}

module.exports = SongYuanMingQingBatch2Merger;
