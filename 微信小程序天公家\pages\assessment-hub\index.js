const app = getApp()
const config = require('../../utils/config.js')

Page({
  data: {
    currentIndex: 0, // 当前选中的角色方式下标
    roles: ['天公师兄', '天工师父', '紫微斗数', '奇门遁甲', '六爻占卜'], // 角色方式显示名称
    roleTypes: ['tarot', 'bazi', 'ziwei', 'qimen', 'liuyao'], // 占卜方式编码
    tabs: ['今天', '历史'], // 标签页名称
    activeTab: 0, // 当前选中的标签页
    currentDate: '', // 当前日期
    currentRole: 'tarot', // 当前选中的占卜方式
    themeClass: 'tarot-theme', // 当前主题类名
    inputValue: '', // 输入框的值

    // 天公师父集成相关数据
    tianggongshifuServiceAvailable: false, // 天公师父服务是否可用
    systemStatistics: null, // 系统统计信息
    todayDivination: null, // 今日占卜
  },

  onLoad() {
    console.log('✅ 占卜中心页面加载，开始集成玉匣记功能');

    // 设置当前日期
    this.setCurrentDate();

    // 默认第一个角色的主题
    this.updateThemeClass(0);

    // 初始化天公师父服务
    this.initTianggongshifuService();
  },

  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const month = now.getMonth() + 1;
    const day = now.getDate();

    this.setData({
      currentDate: `${month}月${day}日`
    });
  },

  // 初始化天公师父服务（优化版）
  async initTianggongshifuService() {
    try {
      console.log('🔮 检查天公师父综合服务(端口8000)...');

      // 静默检查天公师父服务可用性
      const isAvailable = await this.checkTianggongshifuService();

      if (isAvailable) {
        console.log('🎉 天公师父综合服务(端口8000)已连接，加载增强功能...');

        // 获取系统信息
        const systemInfo = await this.getTianggongshifuSystemInfo();

        // 生成今日占卜（基于当前日期）
        const todayDivination = this.generateTodayDivination();

        this.setData({
          tianggongshifuServiceAvailable: true,
          systemStatistics: systemInfo,
          todayDivination: todayDivination
        });

        console.log('✅ 天公师父综合服务增强功能加载完成');

      } else {
        console.log('📱 使用本地功能模式（天公师父服务不可用）');
        this.setData({
          tianggongshifuServiceAvailable: false,
          todayDivination: this.generateTodayDivination() // 本地占卜功能
        });
      }

    } catch (error) {
      console.log('ℹ️ 天公师父综合服务(端口8000)初始化异常，使用本地功能:', error.message);
      this.setData({
        tianggongshifuServiceAvailable: false,
        todayDivination: this.generateTodayDivination() // 本地占卜功能
      });
    }
  },

  // 检查天公师父服务可用性（静默检查，带回退机制）
  async checkTianggongshifuService() {
    return new Promise((resolve) => {
      // 首先尝试主要地址
      console.log('🔍 尝试连接天公师父服务:', config.API_BASE_URL);

      wx.request({
        url: config.API_BASE_URL + '/',
        method: 'GET',
        timeout: 3000,
        success: (res) => {
          console.log('📡 天公师父服务响应:', res.statusCode, res.data?.name);
          const isAvailable = res.statusCode === 200 && res.data?.name?.includes('天公师父');
          if (isAvailable) {
            console.log('✅ 天公师父综合服务(端口8000)可用 - 主地址');
          }
          resolve(isAvailable);
        },
        fail: (error) => {
          console.log('⚠️ 主地址连接失败，尝试备用地址:', error.errMsg);

          // 尝试备用地址
          wx.request({
            url: config.API_BASE_URL_FALLBACK + '/',
            method: 'GET',
            timeout: 3000,
            success: (res) => {
              console.log('📡 天公师父服务备用地址响应:', res.statusCode, res.data?.name);
              const isAvailable = res.statusCode === 200 && res.data?.name?.includes('天公师父');
              if (isAvailable) {
                console.log('✅ 天公师父综合服务(端口8000)可用 - 备用地址');
              }
              resolve(isAvailable);
            },
            fail: (fallbackError) => {
              console.log('ℹ️ 天公师父综合服务(端口8000)不可用，使用本地功能');
              console.log('🔍 连接错误详情:', {
                主地址错误: error.errMsg,
                备用地址错误: fallbackError.errMsg
              });
              resolve(false);
            }
          });
        }
      });
    });
  },

  // 获取天公师父系统信息
  async getTianggongshifuSystemInfo() {
    return new Promise((resolve) => {
      wx.request({
        url: config.API_BASE_URL + '/',
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          if (res.statusCode === 200 && res.data) {
            resolve(res.data);
          } else {
            resolve(null);
          }
        },
        fail: () => {
          resolve(null);
        }
      });
    });
  },

  // 生成今日占卜
  generateTodayDivination() {
    const today = new Date();
    const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 86400000);

    // 基于日期生成占卜内容
    const divinations = [
      {
        title: "今日宜静心修身",
        content: "天道酬勤，静心致远。今日宜内省修身，不宜急躁冒进。",
        advice: "保持内心平静，专注当下事务。",
        lucky_time: "午时（11:00-13:00）"
      },
      {
        title: "今日宜积极进取",
        content: "天行健，君子以自强不息。今日运势上扬，宜积极行动。",
        advice: "把握机会，勇敢前行，但需谨慎决策。",
        lucky_time: "辰时（07:00-09:00）"
      },
      {
        title: "今日宜谨慎行事",
        content: "小心驶得万年船，今日宜谨慎处事，避免冲动决定。",
        advice: "三思而后行，多听取他人意见。",
        lucky_time: "酉时（17:00-19:00）"
      }
    ];

    return divinations[dayOfYear % divinations.length];
  },

  // 监听滑块切换
  onSwiperChange(e) {
    const current = e.detail.current;
    console.log('当前滑块索引:', current);
    
    this.setData({
      currentIndex: current,
      currentRole: this.data.roleTypes[current]
    });
    
    // 更新页面主题
    this.updateThemeClass(current);
    
    // 显示提示
    wx.showToast({
      title: `当前角色: ${this.data.roles[current]}`,
      icon: 'none',
      duration: 1000
    });
  },

  // 更新主题样式class
  updateThemeClass(index) {
    const role = this.data.roleTypes[index];
    const themeClass = `${role}-theme`;
    console.log('更新主题类名为:', themeClass);
    
    this.setData({
      themeClass: themeClass
    });
  },

  // 切换角色
  switchRole(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    
    this.setData({
      currentIndex: index,
      currentRole: this.data.roleTypes[index]
    });
    
    // 更新页面主题
    this.updateThemeClass(index);
  },

  // 切换标签页
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      activeTab: index
    });
  },

  // 导航到个人设置
  navigateToProfile() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息或开始占卜
  sendMessage(e) {
    // 获取点击的占卜方式
    const role = e.currentTarget.dataset.role || this.data.currentRole;

    // 阻止事件冒泡，避免重复触发（当点击按钮时）
    if(e && e.type === 'tap' && e.target && e.target.dataset && e.target.dataset.catchevent) {
      return;
    }

    // 获取输入的问题
    const question = this.data.inputValue.trim();

    // 清空输入框
    this.setData({
      inputValue: ''
    });

    // 根据角色类型直接跳转到对应的占卜信息收集页面
    if (role === 'tarot') {
      // 天公师兄 - 直接跳转到李淳风六壬时课占卜页面
      this.startTianggongshixiongDivination(question);
    } else if (role === 'bazi') {
      // 天工师父 - 直接跳转到八字排盘页面
      this.startBaziDivination(question);
    } else if (role === 'ziwei') {
      // 紫微斗数 - 直接跳转到紫微斗数页面
      this.startZiweiDivination(question);
    } else if (role === 'qimen') {
      // 奇门遁甲 - 直接跳转到奇门遁甲页面
      this.startQimenDivination(question);
    } else if (role === 'liuyao') {
      // 六爻占卜 - 直接跳转到六爻占卜页面
      this.startLiuyaoDivination(question);
    } else {
      // 其他未实现的占卜方式，显示提示
      wx.showToast({
        title: '该占卜方式正在开发中',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 开始天公师兄占卜流程（李淳风六壬时课）
  startTianggongshixiongDivination(question) {
    console.log('🔮 开始天公师兄占卜流程:', question);

    // 导航到占卜输入页面，传递天公师兄标识
    wx.navigateTo({
      url: `/pages/divination-input/index?useTianggongshifu=true&role=tarot&master=天公师兄&question=${encodeURIComponent(question || '')}`
    });
  },

  // 开始八字排盘流程（天工师父 - 玉匣记八字分析）
  startBaziDivination(question) {
    console.log('🔮 开始八字排盘流程（天工师父）:', question);

    // 导航到八字输入页面
    wx.navigateTo({
      url: `/pages/bazi-input/index?role=bazi&master=天工师父&question=${encodeURIComponent(question || '')}`
    });
  },

  // 开始紫微斗数流程
  startZiweiDivination(question) {
    console.log('🔮 开始紫微斗数流程:', question);

    // 暂时显示开发中提示，后续可以添加具体页面
    wx.showToast({
      title: '紫微斗数功能开发中',
      icon: 'none',
      duration: 2000
    });

    // 未来可以跳转到紫微斗数页面
    // wx.navigateTo({
    //   url: `/pages/ziwei-input/index?role=ziwei&master=紫微斗数&question=${encodeURIComponent(question || '')}`
    // });
  },

  // 开始奇门遁甲流程
  startQimenDivination(question) {
    console.log('🔮 开始奇门遁甲流程:', question);

    // 暂时显示开发中提示，后续可以添加具体页面
    wx.showToast({
      title: '奇门遁甲功能开发中',
      icon: 'none',
      duration: 2000
    });

    // 未来可以跳转到奇门遁甲页面
    // wx.navigateTo({
    //   url: `/pages/qimen-input/index?role=qimen&master=奇门遁甲&question=${encodeURIComponent(question || '')}`
    // });
  },

  // 开始六爻占卜流程
  startLiuyaoDivination(question) {
    console.log('🔮 开始六爻占卜流程:', question);

    // 暂时显示开发中提示，后续可以添加具体页面
    wx.showToast({
      title: '六爻占卜功能开发中',
      icon: 'none',
      duration: 2000
    });

    // 未来可以跳转到六爻占卜页面
    // wx.navigateTo({
    //   url: `/pages/liuyao-input/index?role=liuyao&master=六爻占卜&question=${encodeURIComponent(question || '')}`
    // });
  },

  // 跳转到语音输入
  activateVoiceInput() {
    wx.showToast({
      title: '语音输入功能开发中',
      icon: 'none'
    });
  },

  // 查看今日占卜详情
  viewTodayDivination() {
    if (this.data.todayDivination) {
      const divinationData = JSON.stringify(this.data.todayDivination);
      wx.navigateTo({
        url: `/pages/divination-result/index?data=${encodeURIComponent(divinationData)}&source=tianggongshifu`
      });
    } else {
      wx.showToast({
        title: '今日占卜数据加载中',
        icon: 'none'
      });
    }
  },

  // 刷新天公师父数据
  async refreshTianggongshifuData() {
    try {
      wx.showLoading({
        title: '刷新中...',
        mask: true
      });

      // 重新生成今日占卜
      const todayDivination = this.generateTodayDivination();

      this.setData({
        todayDivination: todayDivination
      });

      wx.hideLoading();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('🔮 刷新天公师父数据失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    }
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '天公师兄占卜算命 - 天公师父古法占卜',
      path: '/pages/assessment-hub/index'
    };
  }
}) 