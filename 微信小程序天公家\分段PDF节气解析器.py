#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分段PDF节气解析器
基于之前成功的方法，分段解析PDF中的节气数据
"""

import re
import json
import pdfplumber
from typing import Dict, List, Tuple
import os

class SegmentedPDFJieqiParser:
    """分段PDF节气解析器"""
    
    def __init__(self):
        # 加载已有的完整数据作为基准
        self.load_existing_data()
        
        # 节气名称列表
        self.jieqi_names = [
            '小寒', '大寒', '立春', '雨水', '惊蛰', '春分',
            '清明', '谷雨', '立夏', '小满', '芒种', '夏至',
            '小暑', '大暑', '立秋', '处暑', '白露', '秋分',
            '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
        ]
        
        # 节气月份对应关系
        self.jieqi_month_map = {
            '小寒': 1, '大寒': 1, '立春': 2, '雨水': 2,
            '惊蛰': 3, '春分': 3, '清明': 4, '谷雨': 4,
            '立夏': 5, '小满': 5, '芒种': 6, '夏至': 6,
            '小暑': 7, '大暑': 7, '立秋': 8, '处暑': 8,
            '白露': 9, '秋分': 9, '寒露': 10, '霜降': 10,
            '立冬': 11, '小雪': 11, '大雪': 12, '冬至': 12
        }
        
        self.pdf_extracted_data = {}
    
    def load_existing_data(self):
        """加载已有的完整数据"""
        try:
            with open("权威节气数据_1900-2025_完整版.json", 'r', encoding='utf-8') as f:
                self.existing_data = json.load(f)
            print(f"✅ 加载已有数据: {len(self.existing_data)} 年")
        except FileNotFoundError:
            print("⚠️ 未找到已有数据文件，将从头开始")
            self.existing_data = {}
    
    def parse_pdf_by_segments(self, pdf_path: str, pages_per_segment: int = 20) -> Dict:
        """分段解析PDF"""
        print(f"🔍 开始分段解析PDF: {pdf_path}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return {}
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                print(f"📊 PDF总页数: {total_pages}")
                
                # 分段处理
                segments = []
                for start_page in range(0, total_pages, pages_per_segment):
                    end_page = min(start_page + pages_per_segment, total_pages)
                    segments.append((start_page, end_page))
                
                print(f"📦 分为 {len(segments)} 个段落，每段 {pages_per_segment} 页")
                
                all_extracted_data = {}
                
                for i, (start_page, end_page) in enumerate(segments):
                    print(f"\n🔄 处理段落 {i+1}/{len(segments)}: 页面 {start_page+1}-{end_page}")
                    
                    segment_data = self._parse_segment(pdf, start_page, end_page)
                    
                    if segment_data:
                        all_extracted_data.update(segment_data)
                        print(f"   ✅ 提取到 {len(segment_data)} 年的数据")
                    else:
                        print(f"   ⚠️ 未提取到数据")
                
                return all_extracted_data
                
        except Exception as e:
            print(f"❌ PDF解析错误: {e}")
            return {}
    
    def _parse_segment(self, pdf, start_page: int, end_page: int) -> Dict:
        """解析PDF段落"""
        segment_text = ""
        segment_tables = []
        
        # 提取段落内容
        for page_num in range(start_page, end_page):
            try:
                page = pdf.pages[page_num]
                
                # 提取文本
                page_text = page.extract_text()
                if page_text:
                    segment_text += page_text + "\n"
                
                # 提取表格
                tables = page.extract_tables()
                if tables:
                    segment_tables.extend(tables)
                    
            except Exception as e:
                print(f"     ⚠️ 页面 {page_num+1} 解析失败: {e}")
                continue
        
        # 解析段落数据
        text_data = self._parse_text_for_jieqi(segment_text)
        table_data = self._parse_tables_for_jieqi(segment_tables)
        
        # 合并数据
        merged_data = {}
        for year, year_data in text_data.items():
            if year not in merged_data:
                merged_data[year] = {}
            merged_data[year].update(year_data)
        
        for year, year_data in table_data.items():
            if year not in merged_data:
                merged_data[year] = {}
            merged_data[year].update(year_data)
        
        return merged_data
    
    def _parse_text_for_jieqi(self, text: str) -> Dict:
        """从文本中解析节气数据"""
        data = {}
        
        # 查找年份
        years = self._find_years_in_text(text)
        
        for year in years:
            if year < 1900 or year > 2100:
                continue
            
            # 查找该年份的节气数据
            year_data = self._extract_year_jieqi_from_text(text, year)
            
            if len(year_data) >= 5:  # 至少要有一些节气数据
                data[year] = year_data
        
        return data
    
    def _parse_tables_for_jieqi(self, tables: List[List[List]]) -> Dict:
        """从表格中解析节气数据"""
        data = {}
        
        for table in tables:
            if not table or len(table) < 2:
                continue
            
            # 尝试解析表格中的节气数据
            table_data = self._extract_jieqi_from_table(table)
            
            if table_data:
                for year, year_data in table_data.items():
                    if year not in data:
                        data[year] = {}
                    data[year].update(year_data)
        
        return data
    
    def _find_years_in_text(self, text: str) -> List[int]:
        """在文本中查找年份"""
        year_pattern = r'(19\d{2}|20\d{2})'
        years = [int(y) for y in re.findall(year_pattern, text)]
        return sorted(list(set(years)))
    
    def _extract_year_jieqi_from_text(self, text: str, year: int) -> Dict:
        """从文本中提取指定年份的节气数据"""
        year_data = {}
        
        # 查找包含该年份的文本段落
        year_sections = self._find_year_sections(text, year)
        
        for section in year_sections:
            # 使用多种模式匹配节气数据
            patterns = [
                # 模式1: 节气名 月 日 时 分
                r'(小寒|大寒|立春|雨水|惊蛰|春分|清明|谷雨|立夏|小满|芒种|夏至|小暑|大暑|立秋|处暑|白露|秋分|寒露|霜降|立冬|小雪|大雪|冬至)\s*(\d{1,2})\s*(\d{1,2})\s*(\d{1,2})\s*(\d{1,2})',
                
                # 模式2: 考虑可能的分隔符
                r'(小寒|大寒|立春|雨水|惊蛰|春分|清明|谷雨|立夏|小满|芒种|夏至|小暑|大暑|立秋|处暑|白露|秋分|寒露|霜降|立冬|小雪|大雪|冬至)[\s\|\-\t]*(\d{1,2})[\s\|\-\t]*(\d{1,2})[\s\|\-\t]*(\d{1,2})[\s\|\-\t]*(\d{1,2})',
                
                # 模式3: 更宽松的匹配
                r'(小寒|大寒|立春|雨水|惊蛰|春分|清明|谷雨|立夏|小满|芒种|夏至|小暑|大暑|立秋|处暑|白露|秋分|寒露|霜降|立冬|小雪|大雪|冬至).*?(\d{1,2}).*?(\d{1,2}).*?(\d{1,2}).*?(\d{1,2})',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, section, re.DOTALL)
                
                for match in matches:
                    if len(match) == 5:
                        jieqi_name, month, day, hour, minute = match
                        
                        if self._validate_jieqi_data(jieqi_name, month, day, hour, minute):
                            year_data[jieqi_name] = {
                                'month': int(month),
                                'day': int(day),
                                'hour': int(hour),
                                'minute': int(minute)
                            }
        
        return year_data
    
    def _find_year_sections(self, text: str, year: int) -> List[str]:
        """查找包含指定年份的文本段落"""
        sections = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            if str(year) in line:
                # 提取前后各20行作为上下文
                start = max(0, i - 20)
                end = min(len(lines), i + 20)
                section = '\n'.join(lines[start:end])
                sections.append(section)
        
        return sections
    
    def _extract_jieqi_from_table(self, table: List[List]) -> Dict:
        """从表格中提取节气数据"""
        data = {}
        
        # 查找年份
        year = None
        for row in table:
            if row:
                for cell in row:
                    if cell and isinstance(cell, str):
                        year_match = re.search(r'(19\d{2}|20\d{2})', cell)
                        if year_match:
                            year = int(year_match.group(1))
                            break
                if year:
                    break
        
        if not year or year < 1900 or year > 2100:
            return data
        
        # 查找节气数据
        for row in table:
            if not row or len(row) < 2:
                continue
            
            # 检查是否包含节气名称
            jieqi_name = None
            for cell in row:
                if cell and isinstance(cell, str) and cell.strip() in self.jieqi_names:
                    jieqi_name = cell.strip()
                    break
            
            if jieqi_name:
                # 提取时间数据
                numbers = []
                for cell in row:
                    if cell and isinstance(cell, str):
                        nums = re.findall(r'\d+', cell)
                        numbers.extend([int(n) for n in nums])
                
                if len(numbers) >= 4:
                    month, day, hour, minute = numbers[:4]
                    
                    if self._validate_jieqi_data(jieqi_name, str(month), str(day), str(hour), str(minute)):
                        if year not in data:
                            data[year] = {}
                        
                        data[year][jieqi_name] = {
                            'month': month,
                            'day': day,
                            'hour': hour,
                            'minute': minute
                        }
        
        return data
    
    def _validate_jieqi_data(self, jieqi_name: str, month: str, day: str, hour: str, minute: str) -> bool:
        """验证节气数据的合理性"""
        try:
            month_int = int(month)
            day_int = int(day)
            hour_int = int(hour)
            minute_int = int(minute)
            
            # 基本范围检查
            if not (1 <= month_int <= 12 and 1 <= day_int <= 31 and 0 <= hour_int <= 23 and 0 <= minute_int <= 59):
                return False
            
            # 检查月份是否符合节气规律
            expected_month = self.jieqi_month_map.get(jieqi_name)
            if expected_month and month_int != expected_month:
                return False
            
            return True
            
        except ValueError:
            return False
    
    def merge_with_existing_data(self, pdf_data: Dict) -> Dict:
        """将PDF数据与已有数据合并"""
        merged_data = self.existing_data.copy()
        
        for year, year_data in pdf_data.items():
            year_str = str(year)
            
            if year_str in merged_data:
                # 如果已有数据，比较并选择更完整的
                existing_count = len(merged_data[year_str])
                pdf_count = len(year_data)
                
                if pdf_count > existing_count:
                    print(f"   🔄 {year}年: PDF数据更完整 ({pdf_count} vs {existing_count})，使用PDF数据")
                    merged_data[year_str] = year_data
                else:
                    print(f"   ✅ {year}年: 保持已有数据 ({existing_count} vs {pdf_count})")
            else:
                # 新年份数据
                print(f"   ➕ {year}年: 新增PDF数据 ({len(year_data)} 个节气)")
                merged_data[year_str] = year_data
        
        return merged_data
    
    def save_enhanced_data(self, data: Dict, base_filename: str):
        """保存增强后的数据"""
        # 保存完整版
        with open(f"{base_filename}_增强版.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 生成压缩版
        compressed = self._create_compressed_format(data)
        with open(f"{base_filename}_增强压缩版.json", 'w', encoding='utf-8') as f:
            json.dump(compressed, f, ensure_ascii=False, separators=(',', ':'))
        
        # 生成JavaScript版本
        js_content = self._create_javascript_format(data)
        with open(f"{base_filename}_增强版.js", 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ 增强数据已保存:")
        print(f"   - 增强版: {base_filename}_增强版.json")
        print(f"   - 压缩版: {base_filename}_增强压缩版.json")
        print(f"   - JS版本: {base_filename}_增强版.js")
    
    def _create_compressed_format(self, data: Dict) -> Dict:
        """创建压缩格式"""
        compressed = {
            'meta': {
                'version': '4.0',
                'description': 'PDF分段解析增强节气数据',
                'jieqi_order': self.jieqi_names,
                'year_range': [min([int(y) for y in data.keys()]), max([int(y) for y in data.keys()])],
                'total_years': len(data),
                'total_records': sum(len(year_data) for year_data in data.values())
            },
            'data': {}
        }
        
        for year_str, year_data in data.items():
            year_array = []
            for jieqi in self.jieqi_names:
                if jieqi in year_data:
                    info = year_data[jieqi]
                    year_array.append([info['month'], info['day'], info['hour'], info['minute']])
                else:
                    year_array.append([0, 0, 0, 0])
            
            compressed['data'][year_str] = year_array
        
        return compressed
    
    def _create_javascript_format(self, data: Dict) -> str:
        """创建JavaScript格式"""
        years = [int(y) for y in data.keys()]
        js_code = f'''/**
 * 权威节气数据表 - PDF分段解析增强版
 * 数据来源: PDF分段解析 + 天文算法生成
 * 年份范围: {min(years)}-{max(years)} ({len(data)}年)
 * 节气总数: {sum(len(year_data) for year_data in data.values())}条
 */

const authoritativeJieqiData = {json.dumps(data, ensure_ascii=False, indent=2)};

/**
 * 获取权威节气数据
 * @param {{number}} year - 年份
 * @returns {{Object|null}} 该年份的节气数据
 */
function getAuthoritativeJieqiData(year) {{
  const data = authoritativeJieqiData[year] || null;
  if (data) {{
    console.log(`🌸 使用PDF增强的权威节气数据 - ${{year}}年 (包含${{Object.keys(data).length}}个节气)`);
  }} else {{
    console.log(`⚠️ ${{year}}年无权威节气数据`);
  }}
  return data;
}}

/**
 * 获取支持的年份范围
 * @returns {{Array<number>}} [startYear, endYear]
 */
function getSupportedYearRange() {{
  const years = Object.keys(authoritativeJieqiData).map(y => parseInt(y));
  return [Math.min(...years), Math.max(...years)];
}}

/**
 * 获取所有支持的年份
 * @returns {{Array<number>}} 支持的年份数组
 */
function getAllSupportedYears() {{
  return Object.keys(authoritativeJieqiData).map(y => parseInt(y)).sort();
}}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {{
  module.exports = {{
    authoritativeJieqiData,
    getAuthoritativeJieqiData,
    getSupportedYearRange,
    getAllSupportedYears
  }};
}} else if (typeof window !== 'undefined') {{
  window.authoritativeJieqiData = authoritativeJieqiData;
  window.getAuthoritativeJieqiData = getAuthoritativeJieqiData;
  window.getSupportedYearRange = getSupportedYearRange;
  window.getAllSupportedYears = getAllSupportedYears;
}}
'''
        return js_code

def main():
    """主函数"""
    parser = SegmentedPDFJieqiParser()
    
    pdf_path = "节气.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return
    
    print("🚀 开始分段PDF节气数据解析...")
    
    # 分段解析PDF
    pdf_data = parser.parse_pdf_by_segments(pdf_path, pages_per_segment=30)
    
    if pdf_data:
        print(f"\n🎉 PDF解析完成！")
        print(f"📊 PDF提取统计:")
        print(f"   - 提取年份数: {len(pdf_data)}")
        
        if pdf_data:
            years = sorted(pdf_data.keys())
            print(f"   - 年份范围: {years[0]}-{years[-1]}")
            print(f"   - 节气总数: {sum(len(year_data) for year_data in pdf_data.values())}")
        
        # 与已有数据合并
        print(f"\n🔄 合并数据...")
        enhanced_data = parser.merge_with_existing_data(pdf_data)
        
        print(f"\n📊 最终统计:")
        print(f"   - 总年份数: {len(enhanced_data)}")
        years = sorted([int(y) for y in enhanced_data.keys()])
        print(f"   - 年份范围: {years[0]}-{years[-1]}")
        print(f"   - 总节气数: {sum(len(year_data) for year_data in enhanced_data.values())}")
        
        # 保存增强数据
        parser.save_enhanced_data(enhanced_data, "权威节气数据")
        
        # 显示详细统计
        print(f"\n📅 数据完整性统计:")
        complete_years = 0
        for year_str, year_data in enhanced_data.items():
            count = len(year_data)
            if count == 24:
                complete_years += 1
            elif count >= 20:
                print(f"     {year_str}年: {count}/24 个节气 (基本完整)")
            elif count >= 10:
                print(f"     {year_str}年: {count}/24 个节气 (部分数据)")
            else:
                print(f"     {year_str}年: {count}/24 个节气 (数据不足)")
        
        print(f"   - 完整年份 (24个节气): {complete_years} 年")
        print(f"   - 数据覆盖率: {complete_years/len(enhanced_data)*100:.1f}%")
    
    else:
        print("❌ PDF解析失败，使用已有数据")
        parser.save_enhanced_data(parser.existing_data, "权威节气数据")

if __name__ == "__main__":
    main()
