/**
 * 测试隋唐五代数据集成
 */

const completeDatabase = require('./data/complete_celebrities_database.js');

function testSuiTangIntegration() {
  console.log('🧪 测试隋唐五代数据集成');
  console.log('============================================================');
  
  // 基础统计
  const totalCelebrities = completeDatabase.celebrities.length;
  console.log(`📊 数据库总览:`);
  console.log(`   - 总名人数: ${totalCelebrities}`);
  console.log(`   - 数据源数: ${completeDatabase.metadata.dataSources.length}`);
  
  // 按朝代分类统计
  const dynastyStats = {};
  completeDatabase.celebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
  });
  
  console.log('\n📈 朝代分布:');
  Object.entries(dynastyStats).sort((a, b) => b[1] - a[1]).forEach(([dynasty, count]) => {
    console.log(`   - ${dynasty}: ${count} 位`);
  });
  
  // 隋唐五代名人验证
  const suiTangCelebrities = completeDatabase.celebrities.filter(celebrity => 
    ['隋朝', '唐朝'].includes(celebrity.basicInfo.dynasty)
  );
  
  console.log(`\n🏛️ 隋唐五代名人详情:`);
  console.log(`   - 隋唐名人总数: ${suiTangCelebrities.length}`);
  
  // 验证度统计
  let totalVerification = 0;
  let verificationCount = 0;
  
  suiTangCelebrities.forEach(celebrity => {
    if (celebrity.verification && celebrity.verification.algorithmMatch) {
      totalVerification += celebrity.verification.algorithmMatch;
      verificationCount++;
    }
  });
  
  const averageVerification = verificationCount > 0 ? totalVerification / verificationCount : 0;
  console.log(`   - 平均验证度: ${averageVerification.toFixed(3)}`);
  console.log(`   - 质量等级: ${averageVerification >= 0.9 ? '优秀' : '良好'}`);
  
  // 显示部分隋唐名人
  console.log('\n👑 隋唐五代代表人物:');
  const representativeFigures = suiTangCelebrities.slice(0, 10);
  representativeFigures.forEach((celebrity, index) => {
    const verification = celebrity.verification?.algorithmMatch || 0;
    console.log(`   ${index + 1}. ${celebrity.basicInfo.name} (${celebrity.basicInfo.dynasty}) - 验证度: ${verification.toFixed(3)}`);
  });
  
  // 格局分析统计
  const patternStats = {};
  suiTangCelebrities.forEach(celebrity => {
    if (celebrity.pattern && celebrity.pattern.mainPattern) {
      const pattern = celebrity.pattern.mainPattern;
      patternStats[pattern] = (patternStats[pattern] || 0) + 1;
    }
  });
  
  console.log('\n🔮 命理格局分布:');
  Object.entries(patternStats).sort((a, b) => b[1] - a[1]).forEach(([pattern, count]) => {
    console.log(`   - ${pattern}: ${count} 位`);
  });
  
  // 数据完整性检查
  console.log('\n🔍 数据完整性检查:');
  let completeRecords = 0;
  let incompleteRecords = 0;
  
  suiTangCelebrities.forEach(celebrity => {
    const hasBasicInfo = celebrity.basicInfo && celebrity.basicInfo.name;
    const hasBazi = celebrity.bazi && celebrity.bazi.fullBazi;
    const hasPattern = celebrity.pattern && celebrity.pattern.mainPattern;
    const hasVerification = celebrity.verification && celebrity.verification.algorithmMatch;
    
    if (hasBasicInfo && hasBazi && hasPattern && hasVerification) {
      completeRecords++;
    } else {
      incompleteRecords++;
    }
  });
  
  console.log(`   - 完整记录: ${completeRecords} 位`);
  console.log(`   - 不完整记录: ${incompleteRecords} 位`);
  console.log(`   - 完整率: ${((completeRecords / suiTangCelebrities.length) * 100).toFixed(1)}%`);
  
  // 古籍来源验证
  console.log('\n📚 古籍来源验证:');
  const suiTangSources = new Set();
  suiTangCelebrities.forEach(celebrity => {
    if (celebrity.verification && celebrity.verification.ancientTextEvidence) {
      celebrity.verification.ancientTextEvidence.forEach(evidence => {
        // 提取古籍名称
        const match = evidence.match(/《([^》]+)》/);
        if (match) {
          suiTangSources.add(match[1]);
        }
      });
    }
  });
  
  console.log(`   - 引用古籍数量: ${suiTangSources.size} 部`);
  console.log(`   - 主要古籍: ${Array.from(suiTangSources).slice(0, 5).join('、')}`);
  
  console.log('\n✅ 隋唐五代数据集成测试完成!');
  console.log('============================================================');
  
  return {
    totalCelebrities,
    suiTangCount: suiTangCelebrities.length,
    averageVerification,
    completeRecords,
    dynastyStats,
    patternStats
  };
}

// 运行测试
const results = testSuiTangIntegration();

// 输出测试结果摘要
console.log('\n📋 测试结果摘要:');
console.log(`✅ 成功集成 ${results.suiTangCount} 位隋唐五代名人`);
console.log(`✅ 数据库总规模达到 ${results.totalCelebrities} 位历史名人`);
console.log(`✅ 平均验证度 ${results.averageVerification.toFixed(3)} (${results.averageVerification >= 0.9 ? '优秀' : '良好'})`);
console.log(`✅ 数据完整率 ${((results.completeRecords / results.suiTangCount) * 100).toFixed(1)}%`);
console.log('🎉 隋唐五代第一批次扩展计划圆满完成!');
