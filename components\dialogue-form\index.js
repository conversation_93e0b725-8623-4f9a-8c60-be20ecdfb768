// components/dialogue-form/index.js
Component({
    properties: {
      formType: {
        type: String,
        value: 'user-info'
      }
    },
    
    data: {
      userInfo: {
        name: '',
        age: '',
        gender: '男',
        occupation: '学生'
      },
      genderOptions: ['男', '女', '其他'],
      occupationOptions: ['学生', '教师', '医生', '工程师', '其他']
    },
    
    methods: {
      onInputChange(e) {
        const { field } = e.currentTarget.dataset;
        const { value } = e.detail;
        
        this.setData({
          [`userInfo.${field}`]: value
        });
      },
      
      onPickerChange(e) {
        const { field } = e.currentTarget.dataset;
        const { value } = e.detail;
        
        if (field === 'gender') {
          this.setData({
            'userInfo.gender': this.data.genderOptions[value]
          });
        } else if (field === 'occupation') {
          this.setData({
            'userInfo.occupation': this.data.occupationOptions[value]
          });
        }
      },
      
      submitForm() {
        const { userInfo } = this.data;
        
        // 基本验证
        if (!userInfo.name.trim()) {
          wx.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
          return;
        }
        
        if (userInfo.age && isNaN(Number(userInfo.age))) {
          wx.showToast({
            title: '年龄必须是数字',
            icon: 'none'
          });
          return;
        }
        
        // 触发提交事件
        this.triggerEvent('submit', userInfo);
      }
    }
  });
