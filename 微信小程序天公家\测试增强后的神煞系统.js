/**
 * 测试增强后的神煞系统
 * 验证新增的6个神煞功能
 */

console.log('🚀 测试增强后的神煞系统');
console.log('='.repeat(60));
console.log('');

// 测试用例：辛丑 甲午 癸卯 壬戌
const testFourPillars = [
  { gan: '辛', zhi: '丑' }, // 年柱
  { gan: '甲', zhi: '午' }, // 月柱  
  { gan: '癸', zhi: '卯' }, // 日柱
  { gan: '壬', zhi: '戌' }  // 时柱
];

const dayGan = testFourPillars[2].gan; // 癸
const yearZhi = testFourPillars[0].zhi; // 丑

console.log('📋 测试用例信息：');
console.log('='.repeat(30));
console.log(`四柱：${testFourPillars.map(p => p.gan + p.zhi).join(' ')}`);
console.log(`日干：${dayGan}`);
console.log(`年支：${yearZhi}`);
console.log('');

// 模拟增强后的内置计算器（包含新增的6个神煞函数）
const enhancedCalculator = {
  // 原有的基础神煞（已验证工作正常）
  calculateTianyiGuiren: function(dayGan, fourPillars) {
    const tianyiMap = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['酉', '亥'], '丁': ['酉', '亥'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };
    const results = [];
    const tianyiTargets = tianyiMap[dayGan] || [];
    fourPillars.forEach((pillar, index) => {
      if (tianyiTargets.includes(pillar.zhi)) {
        results.push({
          name: '天乙贵人',
          position: ['年柱', '月柱', '日柱', '时柱'][index],
          pillar: pillar.gan + pillar.zhi
        });
      }
    });
    return results;
  },

  calculateWenchangGuiren: function(dayGan, fourPillars) {
    const wenchangMap = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉', '戊': '申',
      '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
    };
    const results = [];
    const wenchangTarget = wenchangMap[dayGan];
    if (wenchangTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === wenchangTarget) {
          results.push({
            name: '文昌贵人',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  // 🚀 新增的6个神煞函数
  calculateGuoyinGuiren: function(dayGan, fourPillars) {
    // 国印贵人：甲见戌，乙见亥，丙见丑，丁见寅，戊见丑，己见寅，庚见辰，辛见巳，壬见未，癸见申
    const guoyinMap = {
      '甲': '戌', '乙': '亥', '丙': '丑', '丁': '寅', '戊': '丑',
      '己': '寅', '庚': '辰', '辛': '巳', '壬': '未', '癸': '申'
    };

    const results = [];
    const guoyinTarget = guoyinMap[dayGan];
    if (guoyinTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === guoyinTarget) {
          results.push({
            name: '国印贵人',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  calculateDexiuGuiren: function(dayGan, fourPillars) {
    // 德秀贵人：甲见丁，乙见申，丙见亥，丁见寅，戊见申，己见酉，庚见寅，辛见巳，壬见申，癸见卯
    const dexiuMap = {
      '甲': '丁', '乙': '申', '丙': '亥', '丁': '寅', '戊': '申',
      '己': '酉', '庚': '寅', '辛': '巳', '壬': '申', '癸': '卯'
    };

    const results = [];
    const dexiuTarget = dexiuMap[dayGan];
    if (dexiuTarget) {
      fourPillars.forEach((pillar, index) => {
        // 检查天干或地支匹配
        if (pillar.gan === dexiuTarget || pillar.zhi === dexiuTarget) {
          results.push({
            name: '德秀贵人',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  calculateJinyu: function(dayGan, fourPillars) {
    // 金舆：甲见辰，乙见巳，丙见未，丁见申，戊见未，己见申，庚见戌，辛见亥，壬见丑，癸见寅
    const jinyuMap = {
      '甲': '辰', '乙': '巳', '丙': '未', '丁': '申', '戊': '未',
      '己': '申', '庚': '戌', '辛': '亥', '壬': '丑', '癸': '寅'
    };

    const results = [];
    const jinyuTarget = jinyuMap[dayGan];
    if (jinyuTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === jinyuTarget) {
          results.push({
            name: '金舆',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  calculateTianyi: function(dayGan, fourPillars) {
    // 天医：甲见丑，乙见子，丙见亥，丁见戌，戊见酉，己见申，庚见未，辛见午，壬见巳，癸见辰
    const tianyiMap = {
      '甲': '丑', '乙': '子', '丙': '亥', '丁': '戌', '戊': '酉',
      '己': '申', '庚': '未', '辛': '午', '壬': '巳', '癸': '辰'
    };

    const results = [];
    const tianyiTarget = tianyiMap[dayGan];
    if (tianyiTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === tianyiTarget) {
          results.push({
            name: '天医',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  calculateJiangxing: function(yearZhi, fourPillars) {
    // 将星：申子辰见子，亥卯未见卯，寅午戌见午，巳酉丑见酉
    const jiangxingMap = {
      '申': '子', '子': '子', '辰': '子',
      '亥': '卯', '卯': '卯', '未': '卯',
      '寅': '午', '午': '午', '戌': '午',
      '巳': '酉', '酉': '酉', '丑': '酉'
    };

    const results = [];
    const jiangxingTarget = jiangxingMap[yearZhi];
    if (jiangxingTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === jiangxingTarget) {
          results.push({
            name: '将星',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  },

  calculateQisha: function(dayGan, fourPillars) {
    // 七杀：甲见庚，乙见辛，丙见壬，丁见癸，戊见甲，己见乙，庚见丙，辛见丁，壬见戊，癸见己
    const qishaMap = {
      '甲': '庚', '乙': '辛', '丙': '壬', '丁': '癸', '戊': '甲',
      '己': '乙', '庚': '丙', '辛': '丁', '壬': '戊', '癸': '己'
    };

    const results = [];
    const qishaTarget = qishaMap[dayGan];
    if (qishaTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === qishaTarget) {
          results.push({
            name: '七杀',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi
          });
        }
      });
    }
    return results;
  }
};

console.log('🧪 测试新增的6个神煞功能：');
console.log('='.repeat(40));

let totalResults = [];
let successCount = 0;

// 测试新增的6个神煞函数
const newFunctions = [
  { name: 'calculateGuoyinGuiren', params: [dayGan, testFourPillars] },
  { name: 'calculateDexiuGuiren', params: [dayGan, testFourPillars] },
  { name: 'calculateJinyu', params: [dayGan, testFourPillars] },
  { name: 'calculateTianyi', params: [dayGan, testFourPillars] },
  { name: 'calculateJiangxing', params: [yearZhi, testFourPillars] },
  { name: 'calculateQisha', params: [dayGan, testFourPillars] }
];

newFunctions.forEach((func, index) => {
  console.log(`${index + 1}. 测试 ${func.name}:`);
  
  try {
    const result = enhancedCalculator[func.name](...func.params);
    console.log(`   结果：${result.length} 个神煞`);
    
    if (result.length > 0) {
      successCount++;
      result.forEach(shensha => {
        console.log(`   ✅ ${shensha.name} - ${shensha.position} (${shensha.pillar})`);
        totalResults.push(shensha);
      });
    } else {
      console.log(`   ❌ 无匹配`);
    }
  } catch (error) {
    console.log(`   🚨 错误：${error.message}`);
  }
  
  console.log('');
});

console.log('📊 新增神煞测试结果：');
console.log('='.repeat(30));
console.log(`测试功能数量：${newFunctions.length}`);
console.log(`成功匹配功能：${successCount}`);
console.log(`新增神煞数量：${totalResults.length}`);
console.log(`成功率：${(successCount / newFunctions.length * 100).toFixed(1)}%`);

console.log('');
console.log('🎯 新发现的神煞：');
console.log('='.repeat(20));
if (totalResults.length > 0) {
  totalResults.forEach((shensha, index) => {
    console.log(`${index + 1}. ${shensha.name} - ${shensha.position} (${shensha.pillar})`);
  });
} else {
  console.log('❌ 没有发现新的神煞');
}

console.log('');
console.log('🚀 系统增强效果：');
console.log('='.repeat(30));
console.log('1. 修复前：3个神煞');
console.log('2. 第一次修复后：6个神煞');
console.log(`3. 增强后：6 + ${totalResults.length} = ${6 + totalResults.length}个神煞`);
console.log(`4. 总提升：${((6 + totalResults.length - 3) / 3 * 100).toFixed(1)}%`);

console.log('');
console.log('💡 下一步优化建议：');
console.log('='.repeat(30));
console.log('1. 继续添加更多神煞类型');
console.log('2. 验证计算公式的准确性');
console.log('3. 对比"问真八字"标准');
console.log('4. 优化神煞分类和显示');

console.log('');
console.log('✅ 增强后神煞系统测试完成！');
console.log(`🎯 成果：神煞数量从3个提升到${6 + totalResults.length}个`);
