/**
 * 检查函数调用问题
 * 分析为什么能计算出5个神煞但只显示3个
 */

console.log('🔍 检查函数调用问题');
console.log('='.repeat(60));
console.log('');

// 模拟 calculateAllShenshas 函数的调用逻辑
console.log('📋 分析 calculateAllShenshas 函数调用：');
console.log('='.repeat(50));

const testFourPillars = [
  { gan: '辛', zhi: '丑' }, // 年柱
  { gan: '甲', zhi: '午' }, // 月柱  
  { gan: '癸', zhi: '卯' }, // 日柱
  { gan: '壬', zhi: '戌' }  // 时柱
];

const dayGan = testFourPillars[2].gan; // 癸
const yearZhi = testFourPillars[0].zhi; // 丑

// 模拟内置计算器
const mockCalculator = {
  calculateShensha: function() { return []; }, // 简化版，返回空数组
  calculateTianyiGuiren: function() { return [{ name: '天乙贵人', position: '日柱' }]; },
  calculateWenchangGuiren: function() { return [{ name: '文昌贵人', position: '日柱' }]; },
  calculateFuxingGuiren: function() { return []; },
  calculateTaohua: function() { return [{ name: '桃花', position: '月柱' }]; },
  calculateHuagai: function() { return [{ name: '华盖', position: '年柱' }]; },
  calculateKongWang: function() { return [{ name: '空亡', position: '月柱' }]; },
  calculateYangRen: function() { return []; },
  calculateJiesha: function() { return []; },
  calculateGuchenGuasu: function() { return []; }
};

// 模拟 calculateAllShenshas 函数
function simulateCalculateAllShenshas(fourPillars, calculator) {
  const allShenshas = [];
  const dayGan = fourPillars[2].gan;
  const yearZhi = fourPillars[0].zhi;

  console.log('🚀 开始调用各个神煞计算函数...');
  console.log('');

  // 1. 主计算函数
  console.log('1. 调用 calculateShensha:');
  const mainResults = calculator.calculateShensha(fourPillars);
  console.log(`   结果：${mainResults.length} 个神煞`);
  mainResults.forEach(s => allShenshas.push(s));

  // 2. 天乙贵人
  console.log('2. 调用 calculateTianyiGuiren:');
  if (typeof calculator.calculateTianyiGuiren === 'function') {
    const tianyiResults = calculator.calculateTianyiGuiren(dayGan, fourPillars);
    console.log(`   结果：${tianyiResults.length} 个神煞`);
    tianyiResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 3. 文昌贵人
  console.log('3. 调用 calculateWenchangGuiren:');
  if (typeof calculator.calculateWenchangGuiren === 'function') {
    const wenchangResults = calculator.calculateWenchangGuiren(dayGan, fourPillars);
    console.log(`   结果：${wenchangResults.length} 个神煞`);
    wenchangResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 4. 福星贵人
  console.log('4. 调用 calculateFuxingGuiren:');
  if (typeof calculator.calculateFuxingGuiren === 'function') {
    const fuxingResults = calculator.calculateFuxingGuiren(dayGan, fourPillars);
    console.log(`   结果：${fuxingResults.length} 个神煞`);
    fuxingResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 5. 桃花
  console.log('5. 调用 calculateTaohua:');
  if (typeof calculator.calculateTaohua === 'function') {
    const taohuaResults = calculator.calculateTaohua(yearZhi, fourPillars);
    console.log(`   结果：${taohuaResults.length} 个神煞`);
    taohuaResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 6. 华盖
  console.log('6. 调用 calculateHuagai:');
  if (typeof calculator.calculateHuagai === 'function') {
    const huagaiResults = calculator.calculateHuagai(yearZhi, fourPillars);
    console.log(`   结果：${huagaiResults.length} 个神煞`);
    huagaiResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 7. 空亡
  console.log('7. 调用 calculateKongWang:');
  if (typeof calculator.calculateKongWang === 'function') {
    const kongwangResults = calculator.calculateKongWang(dayGan, fourPillars);
    console.log(`   结果：${kongwangResults.length} 个神煞`);
    kongwangResults.forEach(s => allShenshas.push(s));
  } else {
    console.log('   ❌ 函数不存在');
  }

  // 8-19. 其他函数（可能不被调用）
  console.log('8. 其他函数调用情况：');
  const otherFunctions = [
    'calculateYangRen', 'calculateJiesha', 'calculateGuchenGuasu',
    'calculateWebTianchuGuiren', 'calculateWebTongzisha', 'calculateWebZaisha',
    'calculateWebSangmen', 'calculateWebXueren', 'calculateWebPima'
  ];

  otherFunctions.forEach(funcName => {
    if (typeof calculator[funcName] === 'function') {
      console.log(`   ✅ ${funcName} 存在但可能未被调用`);
    } else {
      console.log(`   ❌ ${funcName} 不存在`);
    }
  });

  console.log('');
  console.log(`📊 总计收集到：${allShenshas.length} 个神煞`);
  
  return allShenshas;
}

// 执行模拟
const simulatedResults = simulateCalculateAllShenshas(testFourPillars, mockCalculator);

console.log('');
console.log('🎯 模拟结果分析：');
console.log('='.repeat(30));
console.log(`模拟收集到：${simulatedResults.length} 个神煞`);
simulatedResults.forEach((shensha, index) => {
  console.log(`${index + 1}. ${shensha.name} - ${shensha.position}`);
});

console.log('');
console.log('🚨 发现的问题：');
console.log('='.repeat(30));
console.log('1. 主计算函数返回空数组（避免重复）');
console.log('2. 只有前7个函数被调用');
console.log('3. 后12个函数可能存在但未被调用');
console.log('4. 这解释了为什么只显示部分神煞');

console.log('');
console.log('💡 解决方案：');
console.log('='.repeat(30));
console.log('1. 确保所有19个函数都被调用');
console.log('2. 检查函数存在性判断');
console.log('3. 修复函数调用逻辑');
console.log('4. 添加调试日志');

console.log('');
console.log('🔍 需要检查的具体问题：');
console.log('='.repeat(40));
console.log('1. calculateAllShenshas 函数是否调用了所有19个函数？');
console.log('2. 内置计算器是否包含所有19个函数？');
console.log('3. 函数调用顺序是否正确？');
console.log('4. 是否存在函数名拼写错误？');

console.log('');
console.log('🚀 下一步行动：');
console.log('='.repeat(30));
console.log('1. 检查 calculateAllShenshas 的完整实现');
console.log('2. 确保内置计算器包含所有函数');
console.log('3. 添加所有19个函数的调用');
console.log('4. 测试修复后的结果');

console.log('');
console.log('✅ 函数调用问题检查完成！');
console.log('🎯 关键：需要修复函数调用逻辑，确保所有19个函数都被调用');
