/* 动画效果样式文件 */

/* 基础过渡动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
  from { transform: translateX(-20rpx); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
  from { transform: translateX(20rpx); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scale {
  from { transform: scale(0.95); }
  to { transform: scale(1); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 交互反馈动画 */
@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5rpx); }
  75% { transform: translateX(5rpx); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

/* 过渡类名 */
.fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down {
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left {
  animation: slideLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right {
  animation: slideRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale {
  animation: scale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 交互反馈类名 */
.ripple {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.shake {
  animation: shake 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce {
  animation: bounce 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 动画修饰器 */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.duration-700 {
  animation-duration: 700ms;
}

/* 动画曲线 */
.ease-in {
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}