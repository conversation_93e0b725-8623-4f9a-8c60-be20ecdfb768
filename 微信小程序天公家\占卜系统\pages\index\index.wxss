/* pages/index/index.wxss */

/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #171923; /* 默认深色背景 */
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
}

/* 主题样式 */
.divination-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
  position: relative;
}

.divination-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.chat-theme {
  background: linear-gradient(135deg, #6550e9 0%, #8370fa 50%, #6550e9 100%);
  position: relative;
}

.chat-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.assessment-theme {
  background: linear-gradient(135deg, #7928ca 0%, #9546e7 50%, #7928ca 100%);
  position: relative;
}

.assessment-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.profile-theme {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #16a34a 100%);
  position: relative;
}

.profile-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 65% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 35% 65%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

/* 顶部导航区域 */
.header-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 30rpx 20rpx;
  margin-top: 20rpx;
  position: relative;
  z-index: 10;
}

/* 个人资料图标 */
.profile-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-icon:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.profile-icon image {
  width: 40rpx;
  height: 40rpx;
}

/* 标签选择器 */
.tab-container {
  width: 60%;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 40rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 日期显示 */
.date-display {
  padding: 20rpx 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  position: relative;
}

/* 滑动提示 */
.swipe-tip {
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  padding: 10rpx 0;
  animation: pulse 1.5s infinite ease-in-out;
  position: relative;
  z-index: 5;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 角色切换区 */
.content-swiper {
  flex: 1;
  width: 100%;
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: visible;
  position: relative;
}

.swiper-item {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  overflow: visible !important;
}

.role-scroll-view {
  height: 100%;
  width: 100%;
  overflow: visible;
}

/* 角色卡片样式 - 仙风道骨设计 */
.role-card {
  width: 85%;
  padding: 40rpx 30rpx 60rpx;
  margin: 40rpx auto;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08),
              0 4rpx 16rpx rgba(0, 0, 0, 0.04),
              inset 0 1px 2px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 850rpx;
  position: relative;
  overflow: hidden;
  margin-bottom: 60rpx;
  align-items: center;
  text-align: center;
  z-index: 2;
  backdrop-filter: blur(8rpx);
}

/* 快捷功能 */
.quick-section {
  padding: 0 20rpx 30rpx;
}

.quick-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15rpx;
}

.quick-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx 15rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.quick-item:active {
  transform: scale(0.95);
}

.quick-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
}

.quick-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

/* 系统信息 */
.info-section {
  padding: 0 20rpx 30rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部说明 */
.footer-section {
  text-align: center;
  padding: 40rpx 20rpx 60rpx;
}

.footer-text {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.footer-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 吉凶等级样式 */
.luck-very_auspicious {
  background: #e8f5e8;
  color: #2e7d32;
}

.luck-auspicious {
  background: #e3f2fd;
  color: #1976d2;
}

.luck-neutral {
  background: #f5f5f5;
  color: #666;
}

.luck-inauspicious {
  background: #fff3e0;
  color: #f57c00;
}

.luck-very_inauspicious {
  background: #ffebee;
  color: #d32f2f;
}

/* 占卜选项样式 */
.divination-options {
  margin: 30rpx 0;
  padding: 0 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  margin-bottom: 15rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 15rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.12);
}

.option-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 5rpx;
}

.option-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.option-arrow {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.4);
  margin-left: 15rpx;
}