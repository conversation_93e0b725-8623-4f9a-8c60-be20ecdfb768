/**
 * 简化的缓存性能测试
 */

const ProfessionalWuxingEngine = require('./utils/professional_wuxing_engine.js');

console.log('🚀 开始缓存性能测试');

const engine = new ProfessionalWuxingEngine();

const testCase = [
  { gan: '甲', zhi: '子' },
  { gan: '乙', zhi: '丑' },
  { gan: '丙', zhi: '寅' },
  { gan: '丁', zhi: '卯' }
];

console.log('\n📋 测试缓存正确性...');

// 清空缓存
engine.clearCache();

// 第一次计算（缓存未命中）
console.log('第一次计算:');
const result1 = engine.generateDetailedReport(testCase);

// 第二次计算（缓存命中）
console.log('\n第二次计算:');
const result2 = engine.generateDetailedReport(testCase);

// 检查结果一致性
const isCorrect = JSON.stringify(result1.results) === JSON.stringify(result2.results);
console.log(`\n✅ 结果一致性: ${isCorrect ? '正确' : '错误'}`);

// 显示缓存统计
const stats = engine.getCacheStats();
console.log('\n📊 缓存统计:');
console.log(`  总请求数: ${stats.totalRequests}`);
console.log(`  缓存命中: ${stats.hits}`);
console.log(`  缓存未命中: ${stats.misses}`);
console.log(`  命中率: ${stats.hitRate}`);
console.log(`  缓存大小: ${stats.cacheSize}/${stats.maxSize}`);

console.log('\n📋 测试性能提升...');

// 测试无缓存性能
engine.cacheConfig.enableCache = false;
engine.clearCache();

const iterations = 50;
console.log(`\n🔄 无缓存测试 (${iterations}次):`);
const startTime1 = Date.now();
for (let i = 0; i < iterations; i++) {
  engine.generateDetailedReport(testCase);
}
const endTime1 = Date.now();
const noCacheTime = endTime1 - startTime1;
const noCacheAvg = noCacheTime / iterations;

console.log(`  总耗时: ${noCacheTime}ms`);
console.log(`  平均耗时: ${noCacheAvg.toFixed(2)}ms/次`);

// 测试有缓存性能
engine.cacheConfig.enableCache = true;
engine.clearCache();

console.log(`\n🚀 有缓存测试 (${iterations}次):`);
const startTime2 = Date.now();
for (let i = 0; i < iterations; i++) {
  engine.generateDetailedReport(testCase);
}
const endTime2 = Date.now();
const cacheTime = endTime2 - startTime2;
const cacheAvg = cacheTime / iterations;

console.log(`  总耗时: ${cacheTime}ms`);
console.log(`  平均耗时: ${cacheAvg.toFixed(2)}ms/次`);

const finalStats = engine.getCacheStats();
console.log(`  缓存命中率: ${finalStats.hitRate}`);

// 性能提升分析
const speedImprovement = ((noCacheAvg - cacheAvg) / noCacheAvg * 100);
console.log(`\n🎯 性能提升分析:`);
console.log(`  响应时间提升: ${speedImprovement.toFixed(1)}%`);
console.log(`  缓存优化效果: ${speedImprovement >= 50 ? '优秀' : speedImprovement >= 30 ? '良好' : '一般'}`);

console.log('\n🎉 缓存测试完成！');
