<!-- 增强版五行分析组件 -->
<!-- 用于集成到主页面的专业级五行分析展示 -->

<!-- 静态vs动态力量对比组件 -->
<template name="static-dynamic-comparison">
  <view class="static-dynamic-comparison" wx:if="{{staticDynamicComparison && staticDynamicComparison.length > 0}}">
    <view class="comparison-header" bindtap="toggleStaticDynamicComparison">
      <text class="section-icon">📊</text>
      <text class="section-title">静态vs动态力量对比</text>
      <text class="section-subtitle">展示交互前后的力量变化</text>
      <text class="toggle-icon">{{showStaticDynamicComparison ? '▼' : '▶'}}</text>
    </view>
    
    <view class="comparison-content" wx:if="{{showStaticDynamicComparison}}">
      <view class="comparison-grid">
        <view class="element-comparison" wx:for="{{staticDynamicComparison}}" wx:key="element">
          <view class="element-header">
            <text class="element-name">{{item.name}}</text>
            <view class="change-indicator change-{{item.changeLevel}}">
              <text class="change-arrow">{{item.changeDirection}}</text>
              <text class="change-percent">{{item.changePercent}}</text>
            </view>
          </view>
          
          <!-- 静态力量 -->
          <view class="power-row">
            <text class="power-label">静态</text>
            <view class="power-bar">
              <view class="power-fill static" style="width: {{item.staticPercent}}%"></view>
            </view>
            <text class="power-value">{{item.staticValue}}</text>
          </view>
          
          <!-- 动态力量 -->
          <view class="power-row">
            <text class="power-label">动态</text>
            <view class="power-bar">
              <view class="power-fill dynamic" style="width: {{item.dynamicPercent}}%"></view>
            </view>
            <text class="power-value">{{item.dynamicValue}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<!-- 动态交互关系详情组件 -->
<template name="interaction-details">
  <view class="interaction-details" wx:if="{{interactionDetails && interactionDetails.all.length > 0}}">
    <view class="interaction-header" bindtap="toggleInteractionDetails">
      <text class="section-icon">🔄</text>
      <text class="section-title">动态交互关系</text>
      <text class="section-subtitle">合会冲刑等干支交互分析</text>
      <text class="toggle-icon">{{showInteractionDetails ? '▼' : '▶'}}</text>
    </view>
    
    <view class="interaction-content" wx:if="{{showInteractionDetails}}">
      <!-- 合局类交互 -->
      <view class="interaction-category" wx:if="{{interactionDetails.combinations.length > 0}}">
        <view class="category-header">
          <text class="category-icon">🤝</text>
          <text class="category-title">合局关系</text>
          <text class="category-count">{{interactionDetails.combinations.length}}个</text>
        </view>
        <view class="interaction-list">
          <view class="interaction-item" wx:for="{{interactionDetails.combinations}}" wx:key="index">
            <view class="interaction-info">
              <text class="interaction-icon">{{item.icon}}</text>
              <text class="interaction-desc">{{item.description}}</text>
            </view>
            <text class="interaction-effect">{{item.effect}}</text>
            <view class="strength-indicator strength-{{item.strengthLevel}}"></view>
          </view>
        </view>
      </view>
      
      <!-- 冲刑类交互 -->
      <view class="interaction-category" wx:if="{{interactionDetails.conflicts.length > 0}}">
        <view class="category-header">
          <text class="category-icon">⚡</text>
          <text class="category-title">冲刑关系</text>
          <text class="category-count">{{interactionDetails.conflicts.length}}个</text>
        </view>
        <view class="interaction-list">
          <view class="interaction-item" wx:for="{{interactionDetails.conflicts}}" wx:key="index">
            <view class="interaction-info">
              <text class="interaction-icon">{{item.icon}}</text>
              <text class="interaction-desc">{{item.description}}</text>
            </view>
            <text class="interaction-effect">{{item.effect}}</text>
            <view class="strength-indicator strength-{{item.strengthLevel}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<!-- 交互影响评估结果组件 -->
<template name="impact-evaluation">
  <view class="impact-evaluation" wx:if="{{impactEvaluation}}">
    <view class="evaluation-header" bindtap="toggleImpactEvaluation">
      <text class="section-icon">📈</text>
      <text class="section-title">交互影响评估</text>
      <text class="section-subtitle">综合分析动态交互的整体影响</text>
      <text class="toggle-icon">{{showImpactEvaluation ? '▼' : '▶'}}</text>
    </view>
    
    <view class="evaluation-content" wx:if="{{showImpactEvaluation}}">
      <!-- 核心指标卡片 -->
      <view class="impact-metrics">
        <!-- 整体影响等级 -->
        <view class="metric-card">
          <text class="metric-icon">📊</text>
          <text class="metric-label">整体影响</text>
          <text class="metric-value impact-{{impactEvaluation.overallLevel}}">
            {{impactEvaluation.overallLevelText}}
          </text>
          <view class="metric-bar">
            <view class="metric-fill" style="width: {{impactEvaluation.overallPercent}}%"></view>
          </view>
        </view>
        
        <!-- 日主影响方向 -->
        <view class="metric-card">
          <text class="metric-icon">🎯</text>
          <text class="metric-label">日主影响</text>
          <text class="metric-value direction-{{impactEvaluation.daymasterDirection}}">
            {{impactEvaluation.daymasterDirectionText}}
          </text>
          <text class="metric-detail">强度变化: {{impactEvaluation.daymasterChange}}</text>
        </view>
        
        <!-- 吉凶趋势 -->
        <view class="metric-card">
          <text class="metric-icon">🔮</text>
          <text class="metric-label">吉凶趋势</text>
          <text class="metric-value trend-{{impactEvaluation.fortuneTrend}}">
            {{impactEvaluation.fortuneTrendText}}
          </text>
          <text class="metric-detail">置信度: {{impactEvaluation.confidence}}%</text>
        </view>
      </view>
      
      <!-- 详细分析指标 -->
      <view class="detailed-metrics">
        <view class="detail-item">
          <text class="detail-label">五行平衡</text>
          <text class="detail-value balance-{{impactEvaluation.balanceImprovement ? 'improved' : 'declined'}}">
            {{impactEvaluation.balanceImprovement ? '改善' : '恶化'}}
          </text>
        </view>
        <view class="detail-item">
          <text class="detail-label">交互强度</text>
          <text class="detail-value">{{impactEvaluation.interactionStrength}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">格局稳定性</text>
          <text class="detail-value stability-{{impactEvaluation.patternStability}}">
            {{impactEvaluation.patternStability === 'stable' ? '稳定' : 
              impactEvaluation.patternStability === 'affected' ? '受影响' : '受损'}}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<!-- 个性化建议组件 -->
<template name="personalized-recommendations">
  <view class="personalized-recommendations" wx:if="{{wuxingRecommendations}}">
    <view class="recommendations-header" bindtap="toggleRecommendations">
      <text class="section-icon">💡</text>
      <text class="section-title">个性化建议</text>
      <text class="section-subtitle">基于专业分析的针对性指导</text>
      <text class="toggle-icon">{{showRecommendations ? '▼' : '▶'}}</text>
    </view>
    
    <view class="recommendations-content" wx:if="{{showRecommendations}}">
      <!-- 主要建议 -->
      <view class="primary-recommendation">
        <view class="recommendation-card primary">
          <text class="recommendation-icon">🎯</text>
          <text class="recommendation-title">主要建议</text>
          <text class="recommendation-content">{{wuxingRecommendations.primary}}</text>
          <view class="confidence-indicator">
            <text class="confidence-label">置信度</text>
            <view class="confidence-bar">
              <view class="confidence-fill" style="width: {{impactEvaluation.confidence}}%"></view>
            </view>
            <text class="confidence-value">{{impactEvaluation.confidence}}%</text>
          </view>
        </view>
      </view>
      
      <!-- 次要建议 -->
      <view class="secondary-recommendations" wx:if="{{wuxingRecommendations.secondary && wuxingRecommendations.secondary.length > 0}}">
        <text class="recommendations-subtitle">补充建议</text>
        <view class="recommendation-list">
          <view class="recommendation-item" wx:for="{{wuxingRecommendations.secondary}}" wx:key="index">
            <text class="item-icon">💡</text>
            <text class="item-content">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<!-- 操作按钮组件 -->
<template name="wuxing-actions">
  <view class="wuxing-actions">
    <view class="action-buttons">
      <button class="action-btn refresh-btn" bindtap="refreshWuxingAnalysis" disabled="{{refreshing}}">
        <text class="btn-icon">{{refreshing ? '🔄' : '🔄'}}</text>
        <text class="btn-text">{{refreshing ? '刷新中...' : '刷新分析'}}</text>
      </button>
      
      <button class="action-btn share-btn" bindtap="shareWuxingAnalysis">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享结果</text>
      </button>
    </view>
  </view>
</template>

<!-- 数据状态指示器 -->
<template name="data-status-indicator">
  <view class="data-status-indicator" wx:if="{{completeWuxingAnalysis}}">
    <view class="status-item">
      <text class="status-icon">✅</text>
      <text class="status-text">专业级分析</text>
    </view>
    <view class="status-item">
      <text class="status-icon">🔄</text>
      <text class="status-text">动态交互</text>
    </view>
    <view class="status-item">
      <text class="status-icon">📈</text>
      <text class="status-text">影响评估</text>
    </view>
    <view class="status-item">
      <text class="status-icon">💡</text>
      <text class="status-text">智能建议</text>
    </view>
  </view>
</template>
