const app = getApp();
const navColorUtil = require('../../utils/navigation_color');

Page({
  data: {
    messages: [], // 对话消息记录
    inputValue: '', // 输入框内容
    sending: false, // 是否正在发送消息
    typing: false, // AI是否正在输入
    assessmentComplete: false, // 评估是否完成
    currentQuestionIndex: 0, // 当前问题索引
    scores: {}, // 评估得分
    dimensions: {}, // 维度得分
    activeTab: 'today', // 当前选中的标签
    currentDate: '', // 当前日期
    lastMessageId: '', // 最后一条消息ID
    welcomeTitle: '欢迎进行教师测评', // 欢迎标题
    welcomeSubtitle: '这里的测评将帮助了解学生在学校环境中的表现和行为', // 欢迎副标题
    questions: [], // 评估问题列表
    lastEmotionType: null, // 最近一次情绪类型
    assessmentProgress: 0, // 测评进度百分比
    totalQuestions: 10, // 总问题数
    lastInputTime: 0, // 上次输入时间
    inputPaused: false, // 输入是否暂停
    inputTimer: null, // 输入计时器
    showSmartTip: false, // 是否显示智能提示
    smartTipContent: '', // 智能提示内容
    waitingForReadyConfirmation: true, // 是否等待用户准备确认
    userReady: false, // 用户是否准备好开始测评
    askingForStudentInfo: false, // 是否正在询问学生信息
    studentInfo: null, // 学生信息
  },

  onLoad() {
    // 尝试处理__route__未定义问题
    try {
      if (typeof __route__ === 'undefined' && typeof getApp()._fixRouteIssue === 'function') {
        getApp()._fixRouteIssue('pages/teacher-assessment/index');
      } else if (typeof __route__ === 'undefined') {
        console.log('__route__未定义，页面路径手动设置为:"pages/teacher-assessment/index"');
      }
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }
    
    // 设置当前日期
    this.setCurrentDate();
    
    // 初始化评估问题
    this.initQuestions();
    
    // 设置数据初始化
    this.setData({
      totalQuestions: 10,
      currentQuestionIndex: 0,
      assessmentProgress: 0,
      waitingForReadyConfirmation: true,
      userReady: false
    });
    
    // 设置导航栏颜色
    navColorUtil.setNavigationBarColorByRole('teacher');
    
    // 发送欢迎消息
    setTimeout(() => {
      this.addMessage("AI", "您好！我是教师评估助手。通过系统化的评估问题，帮助教师更客观地评价学生状况。您准备好开始了吗？", "greeting");
    }, 500);
  },
  
  // 当页面显示完成后处理已有消息中的选项题
  onReady() {
    // 处理已有消息中的选项题
    setTimeout(() => {
      const { messages } = this.data;
      const updatedMessages = messages.map(message => {
        // 处理包含选项的消息
        if (message.role === 'AI' && message.content && 
            message.content.includes('A.') && message.content.includes('B.') && 
            !message.parsedOptions) {
          message.parsedOptions = this.parseMessageOptions(message.content);
        }
        return message;
      });
      
      if (updatedMessages.length > 0) {
        this.setData({ messages: updatedMessages });
      }
    }, 300);
  },
  
  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
    
    const weekday = weekdays[now.getDay()];
    const month = months[now.getMonth()];
    const day = now.getDate();
    
    const formattedDate = `${weekday}，${month} ${day}`;
    this.setData({
      currentDate: formattedDate.toUpperCase()
    });
  },

  // 初始化评估问题
  initQuestions() {
    const questions = [
      {
        id: "academic_performance",
        text: "这位学生的学习成绩如何？在您的课堂上表现得怎么样？",
        dimension: "academic",
        followUp: "学生的优势和薄弱学科分别是什么？"
      },
      {
        id: "classroom_behavior",
        text: "学生在课堂上的行为表现如何？是否积极参与课堂活动和讨论？",
        dimension: "behavior",
        followUp: "学生在不同类型的课堂活动(如小组讨论、独立作业)中表现有何不同？"
      },
      {
        id: "attention_focus",
        text: "学生的注意力集中情况如何？能够专注于课堂任务的时间大概有多长？",
        dimension: "attention",
        followUp: "您观察到什么因素会影响学生的注意力？"
      },
      {
        id: "peer_relationships",
        text: "学生与同学之间的关系如何？在小组合作中扮演什么角色？",
        dimension: "social",
        followUp: "学生是否有固定的朋友圈？与其他同学的互动模式是怎样的？"
      },
      {
        id: "teacher_relationship",
        text: "学生与您及其他老师的互动情况如何？是否乐于寻求帮助或指导？",
        dimension: "relationship",
        followUp: "学生对不同老师的态度有差异吗？可能的原因是什么？"
      },
      {
        id: "emotional_regulation",
        text: "学生在面对挫折、冲突或困难时的情绪反应和处理方式如何？",
        dimension: "emotion",
        followUp: "您有观察到学生情绪变化的特定触发因素吗？"
      },
      {
        id: "learning_motivation",
        text: "学生的学习动机和积极性如何？对什么类型的学习活动最有兴趣？",
        dimension: "motivation",
        followUp: "什么样的奖励或反馈方式对激励这位学生最有效？"
      },
      {
        id: "problem_solving",
        text: "学生面对学术挑战或问题时的解决策略和能力如何？",
        dimension: "cognitive",
        followUp: "学生是否表现出特定的思维模式或解决问题的方法？"
      },
      {
        id: "recent_changes",
        text: "最近一段时间内，您是否观察到学生在行为、学业或情绪方面的明显变化？",
        dimension: "changes",
        followUp: "这些变化可能与什么事件或因素有关？"
      },
      {
        id: "support_needs",
        text: "根据您的观察，这位学生可能需要哪些方面的额外支持或干预？",
        dimension: "support",
        followUp: "您认为学校或家庭可以如何更好地支持这位学生？"
      }
    ];

    this.setData({
      questions
    });
  },

  // 添加消息到对话列表
  addMessage(role, content, type = "normal", emotionType = null) {
    const { messages } = this.data;
    const id = Date.now();
    
    // 创建消息对象
    const messageObj = {
      id,
      role,
      content,
      type,
      emotionType,
      time: new Date().toLocaleTimeString()
    };
    
    // 如果是AI消息且包含选项题格式，预处理选项
    if (role === "AI" && content.includes("A.") && content.includes("B.")) {
      // 解析选项内容
      const parsedOptions = this.parseMessageOptions(content);
      messageObj.parsedOptions = parsedOptions;
    }
    
    messages.push(messageObj);

    this.setData({
      messages,
      typing: role === "AI"
    });

    // 设置最后一条消息ID，用于滚动到底部
    this.setData({
      lastMessageId: `msg-${id}`
    });

    // 如果是AI消息，模拟打字效果
    if (role === "AI") {
      setTimeout(() => {
        this.setData({
          typing: false
        });
      }, 1000);
    }
  },
  
  // 解析选项消息的选项部分
  parseMessageOptions(content) {
    try {
      // 将内容按行分割，取第2行开始的部分（选项部分）
      const lines = content.split('\n');
      const optionLines = lines.slice(2); // 跳过标题和空行
      
      // 过滤并清理选项行
      const parsedOptions = optionLines
        .map(line => line.trim())
        .filter(line => line.length > 0);
      
      return parsedOptions;
    } catch (error) {
      console.error('解析选项出错:', error);
      return []; // 出错时返回空数组
    }
  },

  // 输入框内容变化
  onInputChange(e) {
    const now = Date.now();
    this.setData({
      inputValue: e.detail.value,
      lastInputTime: now,
      inputPaused: false
    });
    
    // 清除之前的计时器
    if (this.data.inputTimer) {
      clearTimeout(this.data.inputTimer);
    }
    
    // 如果有内容，开始监测输入暂停
    if (e.detail.value) {
      const timer = setTimeout(() => {
        this.checkInputPause();
      }, 5000); // 5秒无输入则检查
      
      this.setData({
        inputTimer: timer
      });
    }
  },
  
  // 检查输入暂停
  checkInputPause() {
    const now = Date.now();
    const timeSinceLastInput = now - this.data.lastInputTime;
    
    // 如果超过5秒没有新输入
    if (timeSinceLastInput >= 5000 && this.data.inputValue && !this.data.inputPaused) {
          this.setData({
        inputPaused: true
      });
      
      // 显示智能提示
      this.showSmartTip();
    }
  },
  
  // 显示智能提示
  showSmartTip() {
    // 根据当前问题生成相关提示
    const { currentQuestionIndex, questions } = this.data;
    
    if (currentQuestionIndex < questions.length) {
      const currentQuestion = questions[currentQuestionIndex];
      let tipContent = "";
      
      // 根据不同问题维度提供不同提示
      switch (currentQuestion.dimension) {
        case "academic":
          tipContent = "可以从作业完成情况、课堂参与度或考试成绩等方面描述";
          break;
        case "behavior":
          tipContent = "考虑学生是否遵守课堂规则、参与度和与同学互动情况";
          break;
        case "social":
          tipContent = "您是否观察到学生在不同社交场合的表现有何不同？";
          break;
        default:
          tipContent = "可以结合具体事例来描述，这有助于更准确的评估";
      }
      
      this.setData({
        showSmartTip: true,
        smartTipContent: tipContent
      });
      
      // 3秒后隐藏提示
      setTimeout(() => {
        this.setData({
          showSmartTip: false
        });
      }, 3000);
    }
  },

  // 发送消息
  sendMessage() {
    const { inputValue, sending, assessmentComplete, currentQuestionIndex, questions, waitingForReadyConfirmation, askingForStudentInfo } = this.data;
    
    // 如果正在发送或评估已完成，不执行操作
    if (sending || assessmentComplete) return;
    
    // 如果是空消息且没有欢迎消息，触发欢迎流程
    if (!inputValue && this.data.messages.length === 0) {
      this.addMessage("AI", "您好！我是教师评估助手。通过系统化的评估问题，帮助教师更客观地评价学生状况。您准备好开始了吗？", "greeting");
      return;
    }
    
    // 如果是空消息，不发送
    if (!inputValue) return;
    
    // 标记为正在发送
    this.setData({
      sending: true
    });
    
    // 添加用户消息
    this.addMessage("User", inputValue);
    
    // 清空输入框
    this.setData({
      inputValue: ''
    });
    
    // 处理用户回复
    if (waitingForReadyConfirmation) {
      // 检查用户是否准备好
      const readyStatus = this.checkIfUserReady(inputValue);
      if (readyStatus === 'ready') {
        this.handleUserReady();
      } else {
        this.handleUserNotReady();
      }
    } else if (askingForStudentInfo) {
      // 处理学生信息输入
      this.handleStudentInfoInput(inputValue);
    } else {
      // 处理常规评估问题回答
      this.processUserResponse(inputValue, currentQuestionIndex);
    }
    
    // 重置发送状态
    setTimeout(() => {
      this.setData({
        sending: false
      });
    }, 500);
  },
  
  // 检查用户是否准备好开始评测
  checkIfUserReady(response) {
    if (!response) return 'not_ready';
    
    // 表示准备好的关键词
    const readyKeywords = ['准备好', '可以开始', '开始吧', '好的', '可以', '是的', '好啊', '嗯', '好', '开始', '没问题', '行', '确定', 'ok', '准备', '我准备好了', '开始测评', '测评'];
    
    // 表示没准备好的关键词
    const notReadyKeywords = ['还没', '不', '等等', '等一下', '稍等', '不行', '不可以', '不好', '不是', '没准备好', '不开始', '不要'];
    
    // 检查用户回答中是否包含准备好的关键词
    for (const keyword of readyKeywords) {
      if (response.toLowerCase().includes(keyword.toLowerCase())) {
        return 'ready';
      }
    }
    
    // 检查用户回答中是否包含没准备好的关键词
    for (const keyword of notReadyKeywords) {
      if (response.toLowerCase().includes(keyword.toLowerCase())) {
        return 'not_ready';
      }
    }
    
    // 如果用户的回复很短（少于4个字符），且不包含否定词，通常可以视为简单的肯定回答
    if (response.length < 4 && !response.includes('不')) {
      return 'ready';
    }
    
    // 无法确定用户是否准备好，默认当作已准备好处理
    return 'ready';
  },
  
  // 处理用户准备好的情况
  handleUserReady() {
    setTimeout(() => {
      this.addMessage("AI", "太好了！让我们开始吧。为了更好地评估学生的情况，请先提供一些基本信息。");
      
      setTimeout(() => {
        this.addMessage("AI", "请告诉我您要评估的学生姓名、年龄和年级。例如：\"小明，10岁，四年级\"。");
        
        // 设置用户已准备好状态
        this.setData({
          userReady: true,
          waitingForReadyConfirmation: false,
          askingForStudentInfo: true
        });
      }, 1000);
    }, 1000);
  },
  
  // 处理用户未准备好的情况
  handleUserNotReady() {
    console.log('处理用户未准备好的情况');
    
    // 生成随机回复，增加对话的多样性
    const responses = [
      "没关系，我们可以稍后再开始。当您准备好了，随时告诉我。",
      "我理解，有时候需要一点时间做好准备。准备好了告诉我就行。",
      "好的，不着急。您可以随时告诉我您准备好了，我们再开始测评。"
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    setTimeout(() => {
      this.addMessage("AI", randomResponse);
      
      // 设置用户未准备好状态，但保持等待用户自主发起准备好的状态
      this.setData({
        userReady: false,
        waitingForReadyConfirmation: true
      });
    }, 1000);
  },
  
  // 处理学生信息输入
  handleStudentInfoInput(input) {
    // 解析学生信息
    const studentInfo = this.parseStudentInfo(input);
    
    if (studentInfo && (studentInfo.name || studentInfo.age || studentInfo.grade)) {
      setTimeout(() => {
        let infoMessage = "谢谢您提供的信息！我了解到您要评估的学生";
        if (studentInfo.name) {
          infoMessage += "叫" + studentInfo.name;
        }
        if (studentInfo.age) {
          infoMessage += "，今年" + studentInfo.age + "岁";
        }
        if (studentInfo.grade) {
          infoMessage += "，目前在读" + studentInfo.grade;
        }
        infoMessage += "。";
        
        this.addMessage("AI", infoMessage);
        
        setTimeout(() => {
          this.addMessage("AI", "现在我们开始正式的评估。请根据您在学校环境中对该学生的观察回答以下问题，这将帮助我更好地了解学生的情况。");
          
          // 设置状态
          this.setData({
            studentInfo: studentInfo,
            askingForStudentInfo: false
          });
          
          // 开始提问第一个问题
          setTimeout(() => {
            const firstQuestion = this.data.questions[0];
            this.addMessage("AI", firstQuestion.text);
          }, 1000);
        }, 1000);
      }, 1000);
    } else {
      // 无法解析学生信息，请求更明确的信息
      setTimeout(() => {
        this.addMessage("AI", "抱歉，我可能没有完全理解您提供的信息。请明确告诉我学生的姓名、年龄和年级，例如\"小明，10岁，四年级\"。");
      }, 1000);
    }
  },
  
  // 解析学生信息
  parseStudentInfo(text) {
    if (!text) return null;
    
    // 改进的信息提取正则表达式
    const nameRegex = /([a-zA-Z\u4e00-\u9fa5]{1,10})(?:[，,\s]|$)/;
    const ageRegex = /(\d{1,2})(?:[\s]*[岁歲]|周岁|岁半)?/;
    const gradeRegex = /([一二三四五六七八九十\d]{1,2}年级|[一二三四五六]年级|小\d|初[一二三]|高[一二三])/;
    
    let name = null;
    let age = null;
    let grade = null;
    
    // 尝试提取姓名
    const nameMatch = text.match(nameRegex);
    if (nameMatch && nameMatch[1]) {
      name = nameMatch[1];
    }
    
    // 尝试提取年龄
    const ageMatch = text.match(ageRegex);
    if (ageMatch && ageMatch[1]) {
      age = ageMatch[1];
    }
    
    // 尝试提取年级
    const gradeMatch = text.match(gradeRegex);
    if (gradeMatch && gradeMatch[1]) {
      grade = gradeMatch[1];
    }
    
    // 如果只有名字或年龄被单独提及，也尝试识别
    if (text.length < 8 && (text.includes("岁") || /^\d+$/.test(text))) {
      const simpleAgeMatch = text.match(/(\d+)/);
      if (simpleAgeMatch) {
        age = simpleAgeMatch[1];
      }
    }
    
    if (text.length < 10 && !name && !age && !grade) {
      // 如果输入很短且没有识别到结构化信息，可能整个文本就是名字
      name = text.trim();
    }
    
    return { name, age, grade };
  },
  
  // 处理用户响应
  processUserResponse(response, questionIndex) {
    const { questions, assessmentComplete, totalQuestions } = this.data;
    
    // 如果评估已完成，不处理
    if (assessmentComplete) return;
    
    // 如果当前索引超出问题范围，完成评估
    if (questionIndex >= questions.length) {
      this.generateAssessmentResult();
      return;
    }
    
    // 当前问题
    const currentQuestion = questions[questionIndex];
    
    // 更新进度
    const progress = Math.min(Math.round(((questionIndex + 1) / totalQuestions) * 100), 100);
          this.setData({
      assessmentProgress: progress
    });
    
    // 处理答案并生成跟进问题或下一问题
    setTimeout(() => {
      // 模拟处理用户回答并给出跟进问题或下一问题
      if (questionIndex < questions.length - 1) {
        // 如果不是最后一个问题，继续提问下一个问题
        const nextQuestion = questions[questionIndex + 1];
        this.addMessage("AI", nextQuestion.text);
        
        this.setData({
          currentQuestionIndex: questionIndex + 1
        });
      } else {
        // 如果是最后一个问题，完成评估
        this.addMessage("AI", "感谢您的回答！我已经完成了对该学生情况的初步评估。正在生成评估报告...");
        
        // 延迟生成结果，模拟处理时间
        setTimeout(() => {
          this.generateAssessmentResult();
        }, 2000);
      }
    }, 1500);
  },
  
  // 生成评估结果
  generateAssessmentResult() {
    // 模拟生成评估结果
    const dimensions = {
      academic: Math.random() * 100,
      behavior: Math.random() * 100,
      attention: Math.random() * 100,
      social: Math.random() * 100,
      relationship: Math.random() * 100,
      emotion: Math.random() * 100,
      motivation: Math.random() * 100,
      cognitive: Math.random() * 100
    };
    
    // 计算总分
    const scores = Object.values(dimensions);
    const totalScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    
    // 生成结果对象
    const result = {
      totalScore,
      dimensions,
      timestamp: new Date().toISOString(),
      type: "teacher-assessment"
    };
    
    // 更新状态
    this.setData({
      assessmentComplete: true,
      dimensions,
      scores: { total: totalScore }
    });
    
    // 呈现结果概要
    this.presentResultSummary(result);
    
    // 保存结果
    this.saveAssessmentResult(result);
  },
  
  // 呈现结果概要
  presentResultSummary(result) {
    // 确定风险等级
    const riskLevel = this.determineRiskLevel(result.totalScore);
    
    // 生成摘要消息
    let summaryMessage = `✅ 教师评估完成!\n\n`;
    
    summaryMessage += `📊 学生整体状况评分: ${Math.round(result.totalScore)}分\n`;
    summaryMessage += `🔍 详细分析:\n`;
    
    // 添加维度得分
    Object.entries(result.dimensions).forEach(([key, score]) => {
      const dimensionName = this.getDimensionName(key);
      const roundedScore = Math.round(score);
      summaryMessage += `• ${dimensionName}: ${roundedScore}分 ${this.generateAlertLevel(score)}\n`;
    });
    
    // 添加总体评价
    summaryMessage += `\n💡 整体评价:\n`;
    if (riskLevel === 'low') {
      summaryMessage += "该学生整体表现良好，在学校环境中适应情况佳。建议继续提供现有的支持和指导。";
    } else if (riskLevel === 'medium') {
      summaryMessage += "该学生在部分方面表现正常，但也存在一些需要关注的领域。建议针对性地提供额外支持。";
    } else {
      summaryMessage += "该学生在多个方面显示出需要关注的信号，建议制定个性化的支持计划并考虑与家长和相关专业人士合作。";
    }
    
    // 添加消息
    this.addMessage("AI", summaryMessage, "result");
  },
  
  // 获取维度名称
  getDimensionName(dimensionKey) {
    const dimensionNames = {
      academic: "学业表现",
      behavior: "课堂行为",
      attention: "注意力集中",
      social: "同伴关系",
      relationship: "师生互动",
      emotion: "情绪调节",
      motivation: "学习动机",
      cognitive: "认知能力",
      changes: "近期变化",
      support: "支持需求"
    };
    
    return dimensionNames[dimensionKey] || dimensionKey;
  },
  
  // 确定风险等级
  determineRiskLevel(score) {
    if (score >= 70) return 'low';
    if (score >= 50) return 'medium';
    return 'high';
  },
  
  // 根据分数获取颜色
  getColorForScore(score) {
    if (score >= 70) return '#4CAF50'; // 绿色
    if (score >= 50) return '#FFC107'; // 黄色
    return '#F44336'; // 红色
  },
  
  // 生成警报级别
  generateAlertLevel(score) {
    if (score >= 80) return '🟢'; // 绿色圆圈
    if (score >= 60) return '🟡'; // 黄色圆圈
    if (score >= 40) return '🟠'; // 橙色圆圈
    return '🔴'; // 红色圆圈
  },
  
  // 保存评估结果
  saveAssessmentResult(result) {
    // 存储到本地
    this.saveResultToLocal(result);
    
    // 可以在这里添加保存到后端的逻辑
    console.log('保存评估结果:', result);
  },
  
  // 将结果保存到本地
  saveResultToLocal(result) {
    // 获取已有的历史记录
    const historyKey = 'assessment_history';
    const history = wx.getStorageSync(historyKey) || [];
    
    // 添加新结果
    history.unshift(result);
    
    // 保存回本地
    wx.setStorageSync(historyKey, history);
  },
  
  // 重新开始评估
  restartAssessment() {
    // 重置状态
    this.setData({
      messages: [],
      assessmentComplete: false,
      currentQuestionIndex: 0,
      assessmentProgress: 0,
      scores: {},
      dimensions: {},
      waitingForReadyConfirmation: true,
      userReady: false,
      askingForStudentInfo: false,
      studentInfo: null
    });
    
    // 发送欢迎消息
    setTimeout(() => {
      this.addMessage("AI", "您好！我是教师评估助手。通过系统化的评估问题，帮助教师更客观地评价学生状况。您准备好开始了吗？", "greeting");
    }, 500);
  },
  
  // 导航到个人资料
  navigateToProfile() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  },
  
  // 导航到历史记录
  navigateToHistory() {
    wx.navigateTo({
      url: '/pages/assessment-history/index'
    });
  },
  
  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    
    this.setData({
      activeTab: tab
    });
  },
  
  // 查看完整报告
  viewFullReport() {
          wx.navigateTo({
      url: `/pages/assessment-result/index?type=teacher&score=${Math.round(this.data.scores.total)}`
    });
  },
  
  // 分享小程序
  onShareAppMessage() {
    return {
      title: '教师心理评估助手',
      path: '/pages/teacher-assessment/index'
    };
  }
}); 