// debug_real_environment.js
// 在真实微信小程序环境中调试数据流

/**
 * 添加到页面的调试方法
 * 将这些方法添加到 pages/bazi-result/index.js 中用于调试
 */

const debugMethods = {
  /**
   * 🔧 调试：检查存储数据的真实结构
   */
  debugStorageData: function() {
    console.log('🔍 开始检查存储数据的真实结构');
    
    const frontendResult = wx.getStorageSync('bazi_frontend_result');
    const birthInfo = wx.getStorageSync('bazi_birth_info');
    const analysisMode = wx.getStorageSync('bazi_analysis_mode');
    
    console.log('📊 存储数据详细检查:');
    console.log('1. frontendResult 类型:', typeof frontendResult);
    console.log('1. frontendResult 内容:', frontendResult);
    console.log('2. birthInfo 类型:', typeof birthInfo);
    console.log('2. birthInfo 内容:', birthInfo);
    console.log('3. analysisMode:', analysisMode);
    
    // 检查 frontendResult 的关键字段
    if (frontendResult) {
      console.log('📋 frontendResult 关键字段检查:');
      console.log('  - bazi:', frontendResult.bazi);
      console.log('  - five_elements:', frontendResult.five_elements);
      console.log('  - nayin:', frontendResult.nayin);
      console.log('  - lunar_date:', frontendResult.lunar_date);
      console.log('  - basicInfo:', frontendResult.basicInfo);
      
      if (frontendResult.bazi) {
        console.log('📋 bazi 详细结构:');
        console.log('  - year:', frontendResult.bazi.year);
        console.log('  - month:', frontendResult.bazi.month);
        console.log('  - day:', frontendResult.bazi.day);
        console.log('  - hour:', frontendResult.bazi.hour);
      }
    }
    
    // 检查 birthInfo 的关键字段
    if (birthInfo) {
      console.log('📋 birthInfo 关键字段检查:');
      console.log('  - name:', birthInfo.name);
      console.log('  - year:', birthInfo.year);
      console.log('  - month:', birthInfo.month);
      console.log('  - day:', birthInfo.day);
      console.log('  - hour:', birthInfo.hour);
      console.log('  - minute:', birthInfo.minute);
    }
    
    return { frontendResult, birthInfo, analysisMode };
  },

  /**
   * 🔧 调试：逐步验证数据转换过程
   */
  debugDataConversion: function() {
    console.log('🔍 开始逐步验证数据转换过程');
    
    const storageData = this.debugStorageData();
    const { frontendResult, birthInfo, analysisMode } = storageData;
    
    if (!frontendResult || !birthInfo) {
      console.error('❌ 存储数据缺失，无法进行转换测试');
      return { success: false, error: '存储数据缺失' };
    }
    
    console.log('📋 步骤1: 开始数据转换');
    try {
      const convertedData = this.convertFrontendDataToDisplayFormat(frontendResult, birthInfo, analysisMode);
      console.log('✅ 数据转换成功:', convertedData);
      
      console.log('📋 步骤2: 开始数据统一');
      const unifiedData = this.unifyDataStructure(convertedData);
      console.log('✅ 数据统一成功:', unifiedData);
      
      console.log('📋 步骤3: 验证关键字段');
      const validation = {
        hasUserInfo: !!unifiedData.userInfo,
        hasBaziInfo: !!unifiedData.baziInfo,
        hasBirthInfo: !!unifiedData.birthInfo,
        baziComplete: !!(unifiedData.baziInfo?.yearPillar && unifiedData.baziInfo?.monthPillar && 
                        unifiedData.baziInfo?.dayPillar && unifiedData.baziInfo?.timePillar),
        birthInfoComplete: !!(unifiedData.birthInfo?.year && unifiedData.birthInfo?.month && 
                             unifiedData.birthInfo?.day && unifiedData.birthInfo?.hour)
      };
      
      console.log('📊 验证结果:', validation);
      
      return {
        success: true,
        convertedData,
        unifiedData,
        validation
      };
      
    } catch (error) {
      console.error('❌ 数据转换过程出错:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * 🔧 调试：测试流年计算过程
   */
  debugLiunianCalculation: function() {
    console.log('🔍 开始测试流年计算过程');
    
    const conversionResult = this.debugDataConversion();
    if (!conversionResult.success) {
      console.error('❌ 数据转换失败，无法进行流年计算测试');
      return conversionResult;
    }
    
    const { unifiedData } = conversionResult;
    
    console.log('📋 开始流年计算测试');
    try {
      // 检查 ProfessionalLiunianCalculator 是否可用
      if (typeof ProfessionalLiunianCalculator === 'undefined') {
        console.error('❌ ProfessionalLiunianCalculator 未定义');
        return { success: false, error: 'ProfessionalLiunianCalculator 未定义' };
      }
      
      const result = this.calculateProfessionalLiunian(unifiedData);
      console.log('✅ 流年计算完成:', result);
      
      // 验证计算结果
      const resultValidation = {
        hasSuccess: result.success,
        hasSummary: !!result.summary,
        hasCurrentLiunian: !!result.currentLiunian,
        hasLiunianList: Array.isArray(result.liunianList) && result.liunianList.length > 0
      };
      
      console.log('📊 计算结果验证:', resultValidation);
      
      if (result.summary) {
        console.log('📊 summary 详细内容:', result.summary);
      }
      
      return {
        success: true,
        calculationResult: result,
        validation: resultValidation
      };
      
    } catch (error) {
      console.error('❌ 流年计算过程出错:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * 🔧 调试：完整的数据流测试
   */
  debugCompleteDataFlow: function() {
    console.log('🚀 开始完整的数据流调试测试');
    console.log('测试时间:', new Date().toLocaleString());
    
    const results = {
      storageCheck: null,
      dataConversion: null,
      liunianCalculation: null,
      pageDataSet: null
    };
    
    // 1. 存储数据检查
    console.log('\n📋 阶段1: 存储数据检查');
    results.storageCheck = this.debugStorageData();
    
    // 2. 数据转换测试
    console.log('\n📋 阶段2: 数据转换测试');
    results.dataConversion = this.debugDataConversion();
    
    // 3. 流年计算测试
    console.log('\n📋 阶段3: 流年计算测试');
    results.liunianCalculation = this.debugLiunianCalculation();
    
    // 4. 页面数据设置测试
    console.log('\n📋 阶段4: 页面数据设置测试');
    if (results.liunianCalculation.success) {
      try {
        const liunianData = results.liunianCalculation.calculationResult;
        
        console.log('📊 设置数据到页面...');
        this.setData({
          professionalLiunianData: liunianData,
          'loadingStates.liunian': false
        });
        
        // 验证页面数据
        setTimeout(() => {
          const pageValidation = {
            hasData: !!this.data.professionalLiunianData,
            hasSuccess: this.data.professionalLiunianData?.success,
            hasSummary: !!this.data.professionalLiunianData?.summary,
            summaryContent: this.data.professionalLiunianData?.summary
          };
          
          console.log('📊 页面数据验证:', pageValidation);
          results.pageDataSet = { success: true, validation: pageValidation };
          
          // 输出最终报告
          this.outputDebugReport(results);
        }, 100);
        
      } catch (error) {
        console.error('❌ 页面数据设置失败:', error);
        results.pageDataSet = { success: false, error: error.message };
        this.outputDebugReport(results);
      }
    } else {
      results.pageDataSet = { success: false, error: '流年计算失败' };
      this.outputDebugReport(results);
    }
    
    return results;
  },

  /**
   * 🔧 输出调试报告
   */
  outputDebugReport: function(results) {
    console.log('\n🎉 调试测试完成！');
    console.log('\n📊 调试报告:');
    console.log('=' * 50);
    
    // 存储数据检查
    console.log('1. 存储数据检查:', results.storageCheck ? '✅ 有数据' : '❌ 无数据');
    
    // 数据转换
    console.log('2. 数据转换:', results.dataConversion?.success ? '✅ 成功' : '❌ 失败');
    if (!results.dataConversion?.success) {
      console.log('   错误:', results.dataConversion?.error);
    }
    
    // 流年计算
    console.log('3. 流年计算:', results.liunianCalculation?.success ? '✅ 成功' : '❌ 失败');
    if (!results.liunianCalculation?.success) {
      console.log('   错误:', results.liunianCalculation?.error);
    }
    
    // 页面数据设置
    console.log('4. 页面数据设置:', results.pageDataSet?.success ? '✅ 成功' : '❌ 失败');
    if (!results.pageDataSet?.success) {
      console.log('   错误:', results.pageDataSet?.error);
    }
    
    // 最终状态
    if (results.pageDataSet?.validation) {
      console.log('\n📋 最终页面状态:');
      const v = results.pageDataSet.validation;
      console.log(`  - 有数据: ${v.hasData ? '✅' : '❌'}`);
      console.log(`  - 计算成功: ${v.hasSuccess ? '✅' : '❌'}`);
      console.log(`  - 有摘要: ${v.hasSummary ? '✅' : '❌'}`);
      
      if (v.summaryContent) {
        console.log('  - 摘要内容:');
        console.log(`    平均分: ${v.summaryContent.averageScore_display || v.summaryContent.averageScore}`);
        console.log(`    总年数: ${v.summaryContent.totalYears}`);
        console.log(`    最佳年: ${v.summaryContent.bestYear?.year}`);
        console.log(`    最差年: ${v.summaryContent.worstYear?.year}`);
      }
    }
    
    console.log('\n💡 问题定位建议:');
    if (!results.storageCheck) {
      console.log('- 检查八字排盘是否正确完成');
      console.log('- 检查数据是否正确保存到本地存储');
    } else if (!results.dataConversion?.success) {
      console.log('- 检查存储数据格式是否符合预期');
      console.log('- 检查数据转换逻辑');
    } else if (!results.liunianCalculation?.success) {
      console.log('- 检查 ProfessionalLiunianCalculator 模块是否正确加载');
      console.log('- 检查流年计算的输入参数');
    } else if (!results.pageDataSet?.success) {
      console.log('- 检查页面 setData 调用');
      console.log('- 检查页面数据绑定');
    } else {
      console.log('- 所有步骤都成功，问题可能在前端模板或CSS');
    }
  }
};

// 导出调试方法，可以复制到页面文件中使用
module.exports = debugMethods;
