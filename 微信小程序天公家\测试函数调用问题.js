/**
 * 测试函数调用问题
 * 模拟微信小程序环境，测试Web神煞函数是否可以正确调用
 */

console.log('🔧 测试函数调用问题');
console.log('='.repeat(40));
console.log('');

// 模拟微信小程序Page对象结构
const mockPageObject = {
  data: {
    // 页面数据
  },

  // 模拟其他函数
  generateResultId: function() {
    return 'frontend_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  },

  // 模拟神煞计算函数
  calculateShensha: function(fourPillars, birthMonth, birthInfo) {
    console.log('🌟 开始测试神煞计算函数调用...');
    
    const dayGan = fourPillars[2].gan;
    const yearZhi = fourPillars[0].zhi;
    const monthZhi = fourPillars[1].zhi;
    
    console.log('📊 测试数据:', { dayGan, yearZhi, monthZhi });
    
    // 测试调用Web神煞函数
    try {
      console.log('🔮 测试调用 calculateWebTianchuGuiren...');
      
      // 检查函数是否存在
      if (typeof this.calculateWebTianchuGuiren === 'function') {
        console.log('   ✅ calculateWebTianchuGuiren 函数存在');
        const result = this.calculateWebTianchuGuiren(dayGan, fourPillars);
        console.log('   ✅ 调用成功，结果:', result);
      } else {
        console.log('   ❌ calculateWebTianchuGuiren 函数不存在');
        console.log('   🔍 this对象的属性:', Object.keys(this));
      }
      
    } catch (error) {
      console.log('   ❌ 调用失败:', error.message);
    }
    
    return {
      auspicious_stars: [],
      inauspicious_stars: [],
      overall_effect: '测试完成'
    };
  },

  // Web神煞函数定义
  calculateWebTianchuGuiren: function(dayGan, fourPillars) {
    console.log('🔮 执行天厨贵人计算:', { dayGan, fourPillars });
    
    const tianchuMap = {
      '甲': '巳', '乙': '午', '丙': '巳', '丁': '午', '戊': '申',
      '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
    };

    const tianchuZhi = tianchuMap[dayGan];
    if (!tianchuZhi) return [];

    const result = [];
    const pillars = [fourPillars[0], fourPillars[1], fourPillars[2], fourPillars[3]];
    const pillarNames = ['年', '月', '日', '时'];

    pillars.forEach((pillar, index) => {
      if (pillar.zhi === tianchuZhi) {
        result.push({
          name: '天厨贵人',
          position: `${pillarNames[index]}柱`,
          pillar: pillar.gan + pillar.zhi,
          strength: '强',
          effect: '主衣食丰足，生活富裕'
        });
      }
    });

    return result;
  }
};

// 测试数据
const testFourPillars = [
  { gan: '辛', zhi: '丑' }, // 年柱
  { gan: '甲', zhi: '午' }, // 月柱
  { gan: '癸', zhi: '卯' }, // 日柱
  { gan: '壬', zhi: '戌' }  // 时柱
];

console.log('🚀 开始测试...');
console.log('');

// 执行测试
const result = mockPageObject.calculateShensha(testFourPillars, 6, null);

console.log('');
console.log('📊 测试结果:', result);

console.log('');
console.log('🔍 问题分析：');
console.log('   1. 检查函数定义位置是否正确');
console.log('   2. 检查对象结构是否完整');
console.log('   3. 检查函数调用上下文（this指向）');
console.log('   4. 检查是否有语法错误影响对象构建');

console.log('');
console.log('💡 可能的解决方案：');
console.log('   1. 确保Web神煞函数在主Page对象内定义');
console.log('   2. 检查函数定义前后的逗号分隔符');
console.log('   3. 验证对象结构的完整性');
console.log('   4. 在微信开发者工具中检查编译错误');

console.log('');
console.log('✅ 函数调用测试完成！');
