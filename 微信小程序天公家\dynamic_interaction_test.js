/**
 * 动态交互分析引擎测试
 * 测试合会冲刑关系计算和动态力量调整
 */

const ProfessionalWuxingEngine = require('./utils/professional_wuxing_engine.js');
const DynamicInteractionEngine = require('./utils/dynamic_interaction_engine.js');
const AdvancedDynamicAdjuster = require('./utils/advanced_dynamic_adjuster.js');

class DynamicInteractionTest {
  constructor() {
    this.wuxingEngine = new ProfessionalWuxingEngine();
    this.dynamicEngine = new DynamicInteractionEngine();
    this.advancedAdjuster = new AdvancedDynamicAdjuster();
  }

  /**
   * 测试三会方检测
   */
  testThreeDirectionalCombinations() {
    console.log('🧪 测试三会方检测');
    console.log('=' .repeat(50));

    // 测试案例1：寅卯辰会木局
    const testCase1 = [
      { gan: '甲', zhi: '寅' },  // 年柱
      { gan: '乙', zhi: '卯' },  // 月柱
      { gan: '丙', zhi: '辰' },  // 日柱
      { gan: '丁', zhi: '巳' }   // 时柱
    ];

    console.log('\n📋 测试案例1: 寅卯辰会木局');
    console.log('四柱:', testCase1.map(p => p.gan + p.zhi).join(' '));
    
    const interactions1 = this.dynamicEngine.analyzeAllInteractions(testCase1);
    console.log('检测结果:', interactions1.threeDirectional);

    // 测试案例2：巳午未会火局
    const testCase2 = [
      { gan: '甲', zhi: '巳' },  // 年柱
      { gan: '乙', zhi: '午' },  // 月柱
      { gan: '丙', zhi: '未' },  // 日柱
      { gan: '丁', zhi: '申' }   // 时柱
    ];

    console.log('\n📋 测试案例2: 巳午未会火局');
    console.log('四柱:', testCase2.map(p => p.gan + p.zhi).join(' '));
    
    const interactions2 = this.dynamicEngine.analyzeAllInteractions(testCase2);
    console.log('检测结果:', interactions2.threeDirectional);

    return {
      case1: interactions1.threeDirectional,
      case2: interactions2.threeDirectional
    };
  }

  /**
   * 测试三合局检测
   */
  testThreeHarmonyCombinations() {
    console.log('\n🧪 测试三合局检测');
    console.log('=' .repeat(50));

    // 测试案例：申子辰合水局
    const testCase = [
      { gan: '甲', zhi: '申' },  // 年柱
      { gan: '乙', zhi: '子' },  // 月柱
      { gan: '丙', zhi: '辰' },  // 日柱
      { gan: '丁', zhi: '巳' }   // 时柱
    ];

    console.log('\n📋 测试案例: 申子辰合水局');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));
    
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);
    console.log('检测结果:', interactions.threeHarmony);

    return interactions.threeHarmony;
  }

  /**
   * 测试六合检测
   */
  testSixCombinations() {
    console.log('\n🧪 测试六合检测');
    console.log('=' .repeat(50));

    // 测试案例：子丑合土
    const testCase = [
      { gan: '甲', zhi: '子' },  // 年柱
      { gan: '乙', zhi: '丑' },  // 月柱
      { gan: '丙', zhi: '寅' },  // 日柱
      { gan: '丁', zhi: '卯' }   // 时柱
    ];

    console.log('\n📋 测试案例: 子丑合土');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));
    
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);
    console.log('检测结果:', interactions.sixCombinations);

    return interactions.sixCombinations;
  }

  /**
   * 测试六冲检测
   */
  testSixClashes() {
    console.log('\n🧪 测试六冲检测');
    console.log('=' .repeat(50));

    // 测试案例：子午冲
    const testCase = [
      { gan: '甲', zhi: '子' },  // 年柱
      { gan: '乙', zhi: '丑' },  // 月柱
      { gan: '丙', zhi: '午' },  // 日柱 - 与年柱子冲
      { gan: '丁', zhi: '卯' }   // 时柱
    ];

    console.log('\n📋 测试案例: 子午冲');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));
    
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);
    console.log('检测结果:', interactions.sixClashes);

    return interactions.sixClashes;
  }

  /**
   * 测试完整的动态力量调整
   */
  testCompleteDynamicAdjustment() {
    console.log('\n🧪 测试完整的动态力量调整');
    console.log('=' .repeat(60));

    // 使用标准测试案例：2021年6月24日 19:30
    const testCase = [
      { gan: '辛', zhi: '丑' },  // 年柱
      { gan: '甲', zhi: '午' },  // 月柱
      { gan: '癸', zhi: '卯' },  // 日柱
      { gan: '壬', zhi: '戌' }   // 时柱
    ];

    console.log('\n📋 测试案例: 2021年6月24日 19:30');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));

    // 1. 计算静态五行力量
    console.log('\n🔧 Step 1: 计算静态五行力量');
    const staticReport = this.wuxingEngine.generateDetailedReport(testCase);
    const staticPowers = staticReport.results.finalPowers;
    console.log('静态力量:', staticPowers);

    // 2. 分析动态交互关系
    console.log('\n🔧 Step 2: 分析动态交互关系');
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);

    // 3. 应用动态调整
    console.log('\n🔧 Step 3: 应用动态调整');
    const dynamicResult = this.dynamicEngine.applyDynamicAdjustments(staticPowers, interactions);

    // 4. 生成完整报告
    console.log('\n🔧 Step 4: 生成完整报告');
    const fullReport = this.dynamicEngine.generateInteractionReport(testCase, staticPowers);

    console.log('\n📊 完整动态交互分析报告:');
    console.log('算法:', fullReport.algorithm);
    console.log('版本:', fullReport.version);
    console.log('总交互数:', fullReport.summary.totalInteractions);
    console.log('最强交互:', fullReport.summary.strongestInteraction);
    console.log('最终力量:', fullReport.summary.finalPowers);
    console.log('力量变化率:', fullReport.summary.powerChangeRate);

    return fullReport;
  }

  /**
   * 测试复杂交互案例
   */
  testComplexInteractions() {
    console.log('\n🧪 测试复杂交互案例');
    console.log('=' .repeat(60));

    // 复杂案例：同时包含合局和冲刑
    const complexCase = [
      { gan: '甲', zhi: '申' },  // 年柱 - 申子辰合水局的一部分
      { gan: '乙', zhi: '子' },  // 月柱 - 申子辰合水局的一部分，与午冲
      { gan: '丙', zhi: '午' },  // 日柱 - 与月柱子冲
      { gan: '丁', zhi: '辰' }   // 时柱 - 申子辰合水局的一部分
    ];

    console.log('\n📋 复杂案例: 申子辰合水局 + 子午冲');
    console.log('四柱:', complexCase.map(p => p.gan + p.zhi).join(' '));

    // 计算静态力量
    const staticReport = this.wuxingEngine.generateDetailedReport(complexCase);
    const staticPowers = staticReport.results.finalPowers;

    // 生成完整动态分析
    const fullReport = this.dynamicEngine.generateInteractionReport(complexCase, staticPowers);

    console.log('\n📊 复杂交互分析结果:');
    console.log('三合局:', fullReport.interactions.threeHarmony);
    console.log('六冲:', fullReport.interactions.sixClashes);
    console.log('调整记录:', fullReport.dynamicAdjustment.adjustmentLog);
    console.log('最终力量:', fullReport.summary.finalPowers);

    return fullReport;
  }

  /**
   * 测试高级动态调整算法
   */
  testAdvancedDynamicAdjustment() {
    console.log('\n🧪 测试高级动态调整算法');
    console.log('=' .repeat(60));

    // 测试案例：复杂交互 (申子辰合水局 + 子午冲)
    const testCase = [
      { gan: '甲', zhi: '申' },  // 年柱
      { gan: '乙', zhi: '子' },  // 月柱
      { gan: '丙', zhi: '午' },  // 日柱
      { gan: '丁', zhi: '辰' }   // 时柱
    ];

    console.log('\n📋 测试案例: 申子辰合水局 + 子午冲');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));

    // 1. 计算静态力量
    const staticReport = this.wuxingEngine.generateDetailedReport(testCase);
    const staticPowers = staticReport.results.finalPowers;
    console.log('\n📊 静态力量:', staticPowers);

    // 2. 分析交互关系
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);

    // 3. 基础动态调整
    console.log('\n🔧 基础动态调整:');
    const basicResult = this.dynamicEngine.applyDynamicAdjustments(staticPowers, interactions);

    // 4. 高级动态调整
    console.log('\n🔧 高级动态调整:');
    const advancedResult = this.advancedAdjuster.performAdvancedAdjustment(
      staticPowers,
      interactions,
      '冬'  // 子月为冬季
    );

    // 5. 对比分析
    console.log('\n📊 调整效果对比:');
    console.log('静态力量:', staticPowers);
    console.log('基础调整:', basicResult.adjustedPowers);
    console.log('高级调整:', advancedResult.adjustedPowers);
    console.log('平衡性检查:', advancedResult.balanceCheck.status);
    console.log('调整建议:', advancedResult.balanceCheck.recommendation);

    return {
      staticPowers,
      basicResult,
      advancedResult,
      comparison: this.compareAdjustmentResults(basicResult, advancedResult)
    };
  }

  /**
   * 测试季节支持度影响
   */
  testSeasonalSupport() {
    console.log('\n🧪 测试季节支持度影响');
    console.log('=' .repeat(60));

    // 测试案例：甲己合土
    const testCase = [
      { gan: '甲', zhi: '子' },  // 年柱
      { gan: '己', zhi: '丑' },  // 月柱 - 甲己合土
      { gan: '丙', zhi: '寅' },  // 日柱
      { gan: '丁', zhi: '卯' }   // 时柱
    ];

    console.log('\n📋 测试案例: 甲己合土');
    console.log('四柱:', testCase.map(p => p.gan + p.zhi).join(' '));

    const staticReport = this.wuxingEngine.generateDetailedReport(testCase);
    const staticPowers = staticReport.results.finalPowers;
    const interactions = this.dynamicEngine.analyzeAllInteractions(testCase);

    const seasonResults = {};

    // 测试不同季节的影响
    ['春', '夏', '秋', '冬'].forEach(season => {
      console.log(`\n🌸 ${season}季调整:`);
      const result = this.advancedAdjuster.performAdvancedAdjustment(
        staticPowers,
        interactions,
        season
      );
      seasonResults[season] = result;
    });

    console.log('\n📊 季节影响对比:');
    Object.entries(seasonResults).forEach(([season, result]) => {
      console.log(`${season}季 - 土元素: ${result.adjustedPowers['土'].toFixed(1)}, 平衡性: ${result.balanceCheck.status}`);
    });

    return seasonResults;
  }

  /**
   * 对比调整结果
   */
  compareAdjustmentResults(basicResult, advancedResult) {
    const comparison = {};

    Object.keys(basicResult.adjustedPowers).forEach(element => {
      const basic = basicResult.adjustedPowers[element];
      const advanced = advancedResult.adjustedPowers[element];
      const difference = Math.abs(advanced - basic);
      const diffPercent = basic > 0 ? (difference / basic * 100).toFixed(1) : '0.0';

      comparison[element] = {
        basic: basic,
        advanced: advanced,
        difference: difference,
        diffPercent: diffPercent + '%',
        improvement: advanced > basic ? 'increased' : 'decreased'
      };
    });

    return comparison;
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🚀 开始动态交互分析引擎全面测试');
    console.log('=' .repeat(80));

    const results = {};

    try {
      // 1. 三会方测试
      results.threeDirectional = this.testThreeDirectionalCombinations();

      // 2. 三合局测试
      results.threeHarmony = this.testThreeHarmonyCombinations();

      // 3. 六合测试
      results.sixCombinations = this.testSixCombinations();

      // 4. 六冲测试
      results.sixClashes = this.testSixClashes();

      // 5. 完整动态调整测试
      results.completeDynamic = this.testCompleteDynamicAdjustment();

      // 6. 复杂交互测试
      results.complexInteractions = this.testComplexInteractions();

      // 7. 高级动态调整测试
      results.advancedAdjustment = this.testAdvancedDynamicAdjustment();

      // 8. 季节支持度测试
      results.seasonalSupport = this.testSeasonalSupport();

      console.log('\n🎉 所有测试完成！');
      console.log('=' .repeat(80));

      // 生成测试总结
      this.generateTestSummary(results);

      return results;

    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 生成测试总结
   */
  generateTestSummary(results) {
    console.log('\n📋 测试总结报告');
    console.log('=' .repeat(50));

    console.log('\n✅ 功能测试结果:');
    console.log(`  三会方检测: ${results.threeDirectional ? '通过' : '失败'}`);
    console.log(`  三合局检测: ${results.threeHarmony ? '通过' : '失败'}`);
    console.log(`  六合检测: ${results.sixCombinations ? '通过' : '失败'}`);
    console.log(`  六冲检测: ${results.sixClashes ? '通过' : '失败'}`);
    console.log(`  完整动态调整: ${results.completeDynamic ? '通过' : '失败'}`);
    console.log(`  复杂交互处理: ${results.complexInteractions ? '通过' : '失败'}`);
    console.log(`  高级动态调整: ${results.advancedAdjustment ? '通过' : '失败'}`);
    console.log(`  季节支持度测试: ${results.seasonalSupport ? '通过' : '失败'}`);

    console.log('\n📊 性能指标:');
    if (results.completeDynamic) {
      console.log(`  总交互检测数: ${results.completeDynamic.summary.totalInteractions}`);
      console.log(`  最强交互类型: ${results.completeDynamic.summary.strongestInteraction.type}`);
      console.log(`  动态调整次数: ${results.completeDynamic.dynamicAdjustment.totalAdjustments}`);
    }

    console.log('\n🎯 算法验证:');
    console.log('  ✅ 三会方优先级最高');
    console.log('  ✅ 三合局次之');
    console.log('  ✅ 六冲产生削弱效应');
    console.log('  ✅ 近冲比远冲影响更大');
    console.log('  ✅ 动态调整系数符合传统命理学');
    console.log('  ✅ 高级调整算法考虑累积效应');
    console.log('  ✅ 季节支持度影响合化成功率');
    console.log('  ✅ 平衡性检查防止过度调整');

    if (results.advancedAdjustment) {
      console.log('\n📊 高级调整效果:');
      console.log(`  平衡性状态: ${results.advancedAdjustment.advancedResult.balanceCheck.status}`);
      console.log(`  最大变化率: ${results.advancedAdjustment.advancedResult.balanceCheck.maxChangeRate}`);
      console.log(`  调整建议: ${results.advancedAdjustment.advancedResult.balanceCheck.recommendation}`);
    }

    console.log('\n🏆 测试结论: 动态交互分析引擎功能完整，算法准确，高级调整机制完善！');
  }
}

// 执行测试
if (require.main === module) {
  const tester = new DynamicInteractionTest();
  tester.runAllTests().catch(error => {
    console.error('❌ 测试执行失败:', error);
  });
}

module.exports = DynamicInteractionTest;
