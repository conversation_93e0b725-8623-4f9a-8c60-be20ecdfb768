/**
 * 正确的身宫计算方法
 * 发现：身宫地支 = 时支，天干用五虎遁（寅基索引）
 */

// 正确的身宫计算函数
function calculateShengongCorrect(monthZhi, hourZhi, yearGan) {
  console.log('🏛️ 正确的身宫计算方法');
  console.log('月支:', monthZhi);
  console.log('时支:', hourZhi);
  console.log('年干:', yearGan);
  
  // 身宫地支 = 时支
  const shengong<PERSON>hi = hourZhi;
  console.log('身宫地支 = 时支 =', shengongZhi);
  
  // 计算身宫天干（五虎遁法 - 寅基索引）
  const wuhuDun = {
    '甲': ['丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁'], // 甲年正月丙寅
    '己': ['丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁'],
    '乙': ['戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'], // 乙年正月戊寅
    '庚': ['戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'],
    '丙': ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛'], // 丙年正月庚寅
    '辛': ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛'],
    '丁': ['壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'], // 丁年正月壬寅
    '壬': ['壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
    '戊': ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙'], // 戊年正月甲寅
    '癸': ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙']
  };
  
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const shengongIndex = zhiOrder.indexOf(shengongZhi);
  
  // 从寅开始的索引（寅=0, 卯=1, ..., 亥=9, 子=10, 丑=11）
  const yinBasedIndex = (shengongIndex - 2 + 12) % 12;
  const ganArray = wuhuDun[yearGan];
  const shengongGan = ganArray ? ganArray[yinBasedIndex] : '未知';
  
  console.log('身宫地支索引:', shengongIndex);
  console.log('寅基索引:', yinBasedIndex);
  console.log('五虎遁数组:', ganArray);
  console.log('身宫天干:', shengongGan);
  
  const shengongGanzhi = shengongGan + shengongZhi;
  
  // 纳音
  const nayin = {
    '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火',
    '戊辰': '大林木', '己巳': '大林木', '庚午': '路旁土', '辛未': '路旁土',
    '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
    '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城墙土', '己卯': '城墙土',
    '庚辰': '白蜡金', '辛巳': '白蜡金', '壬午': '杨柳木', '癸未': '杨柳木',
    '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
    '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木',
    '壬辰': '长流水', '癸巳': '长流水', '甲午': '砂中金', '乙未': '砂中金',
    '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
    '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金',
    '甲辰': '覆灯火', '乙巳': '覆灯火', '丙午': '天河水', '丁未': '天河水',
    '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
    '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水',
    '丙辰': '沙中土', '丁巳': '沙中土', '戊午': '天上火', '己未': '天上火',
    '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
  };
  
  const shengongNayin = nayin[shengongGanzhi] || '未知纳音';
  
  return {
    ganzhi: shengongGanzhi,
    nayin: shengongNayin,
    gan: shengongGan,
    zhi: shengongZhi
  };
}

// 测试所有案例
function testAllCases() {
  console.log('🔮 测试所有身宫计算案例');
  console.log('=' * 60);
  
  const testCases = [
    {
      name: '2015年11月20日14:00',
      year: '乙',
      month: '亥',
      hour: '未',
      expected: '癸未',
      description: '新案例'
    },
    {
      name: '1996年10月13日15:45',
      year: '丙',
      month: '戌',
      hour: '申',
      expected: '?',
      description: '验证案例1'
    },
    {
      name: '2013年8月17日11:00',
      year: '癸',
      month: '申',
      hour: '午',
      expected: '?',
      description: '验证案例2'
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n📋 ${testCase.name} (${testCase.description}):`);
    console.log('输入:', `${testCase.year}年${testCase.month}月${testCase.hour}时`);
    
    const result = calculateShengongCorrect(testCase.month, testCase.hour, testCase.year);
    
    console.log('计算结果:', result.ganzhi, '(' + result.nayin + ')');
    
    if (testCase.expected && testCase.expected !== '?') {
      const isCorrect = result.ganzhi === testCase.expected;
      console.log('期望结果:', testCase.expected);
      console.log('验证结果:', isCorrect ? '✅ 正确' : '❌ 错误');
    } else {
      console.log('期望结果: 待验证');
    }
  });
}

// 总结身宫计算规律
function summarizeShengongRule() {
  console.log('\n📊 身宫计算规律总结');
  console.log('=' * 50);
  
  console.log('🎯 身宫计算公式:');
  console.log('1. 身宫地支 = 时支');
  console.log('2. 身宫天干 = 五虎遁[年干][寅基索引]');
  console.log('3. 寅基索引 = (时支索引 - 2 + 12) % 12');
  
  console.log('\n🔍 与命宫的区别:');
  console.log('命宫: 复杂的传统起法（我们还没完全掌握）');
  console.log('身宫: 简单明确 = 时支 + 五虎遁天干');
  
  console.log('\n✅ 验证结果:');
  console.log('2015年11月20日14:00: 癸未（杨柳木）✅');
}

// 运行所有测试
testAllCases();
summarizeShengongRule();
