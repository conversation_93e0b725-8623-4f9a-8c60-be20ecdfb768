#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段：应用功能层建设
构建3000条各系统独有的应用功能规则
"""

import json
from datetime import datetime
from typing import Dict, List

class Phase3ApplicationLayerBuilder:
    def __init__(self):
        self.rule_id_counter = 2801  # 从第二阶段结束后继续
        
        # 应用功能层目标配置
        self.application_targets = {
            "数字化分析独有功能": {
                "target_count": 300,
                "description": "数字化可视化、平衡指数、雷达图等独有功能",
                "sub_modules": {
                    "数字化可视化": 100,
                    "平衡指数算法": 100,
                    "雷达图生成": 100
                }
            },
            "每日指南独有功能": {
                "target_count": 1200,
                "description": "日柱互动、场景建议、时间选择等独有功能",
                "sub_modules": {
                    "日柱互动分析": 400,
                    "场景化建议": 400,
                    "时间选择": 200,
                    "个性化推荐": 200
                }
            },
            "匹配分析独有功能": {
                "target_count": 1500,
                "description": "18种关系类型、15个匹配维度、心理暗示等独有功能",
                "sub_modules": {
                    "18种关系类型": 600,
                    "15个匹配维度": 600,
                    "心理暗示技巧": 300
                }
            }
        }
    
    def load_foundation_data(self, filename: str = "classical_rules_phase2_complete.json"):
        """加载前两阶段的基础数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            metadata = data.get('metadata', {})
            print(f"✅ 加载前两阶段数据: {len(rules)}条规则")
            return rules, metadata
            
        except Exception as e:
            print(f"❌ 加载基础数据失败: {e}")
            return [], {}
    
    def generate_digital_analysis_rules(self) -> List[Dict]:
        """生成数字化分析独有功能规则"""
        print("📊 生成数字化分析独有功能规则...")
        
        rules = []
        target_config = self.application_targets["数字化分析独有功能"]
        
        # 数字化可视化规则
        visualization_rules = self._generate_visualization_rules(
            target_config["sub_modules"]["数字化可视化"]
        )
        rules.extend(visualization_rules)
        
        # 平衡指数算法规则
        balance_rules = self._generate_balance_index_rules(
            target_config["sub_modules"]["平衡指数算法"]
        )
        rules.extend(balance_rules)
        
        # 雷达图生成规则
        radar_rules = self._generate_radar_chart_rules(
            target_config["sub_modules"]["雷达图生成"]
        )
        rules.extend(radar_rules)
        
        print(f"  生成了 {len(rules)} 条数字化分析规则")
        return rules
    
    def generate_daily_guide_rules(self) -> List[Dict]:
        """生成每日指南独有功能规则"""
        print("📅 生成每日指南独有功能规则...")
        
        rules = []
        target_config = self.application_targets["每日指南独有功能"]
        
        # 日柱互动分析规则
        daily_interaction_rules = self._generate_daily_interaction_rules(
            target_config["sub_modules"]["日柱互动分析"]
        )
        rules.extend(daily_interaction_rules)
        
        # 场景化建议规则
        scenario_rules = self._generate_scenario_advice_rules(
            target_config["sub_modules"]["场景化建议"]
        )
        rules.extend(scenario_rules)
        
        # 时间选择规则
        timing_rules = self._generate_timing_selection_rules(
            target_config["sub_modules"]["时间选择"]
        )
        rules.extend(timing_rules)
        
        # 个性化推荐规则
        personalization_rules = self._generate_personalization_rules(
            target_config["sub_modules"]["个性化推荐"]
        )
        rules.extend(personalization_rules)
        
        print(f"  生成了 {len(rules)} 条每日指南规则")
        return rules
    
    def generate_matching_analysis_rules(self) -> List[Dict]:
        """生成匹配分析独有功能规则"""
        print("💕 生成匹配分析独有功能规则...")
        
        rules = []
        target_config = self.application_targets["匹配分析独有功能"]
        
        # 18种关系类型规则
        relationship_rules = self._generate_relationship_type_rules(
            target_config["sub_modules"]["18种关系类型"]
        )
        rules.extend(relationship_rules)
        
        # 15个匹配维度规则
        dimension_rules = self._generate_matching_dimension_rules(
            target_config["sub_modules"]["15个匹配维度"]
        )
        rules.extend(dimension_rules)
        
        # 心理暗示技巧规则
        psychology_rules = self._generate_psychology_hint_rules(
            target_config["sub_modules"]["心理暗示技巧"]
        )
        rules.extend(psychology_rules)
        
        print(f"  生成了 {len(rules)} 条匹配分析规则")
        return rules
    
    def _generate_visualization_rules(self, count: int) -> List[Dict]:
        """生成数字化可视化规则"""
        rules = []
        
        visualization_types = [
            "五行力量雷达图", "十神分布饼图", "大运流年时间轴", "格局结构图",
            "用神忌神对比图", "神煞分布图", "调候平衡图", "综合评分图",
            "动态变化图", "3D立体图"
        ]
        
        for i in range(count):
            viz_type = visualization_types[i % len(visualization_types)]
            rule = {
                "rule_id": f"VIZ_{self.rule_id_counter:04d}",
                "pattern_name": f"{viz_type}生成算法{i//len(visualization_types)+1}",
                "category": "应用功能",
                "application_type": "数字化分析独有功能",
                "sub_module": "数字化可视化",
                "original_text": f"生成{viz_type}的算法，将八字数据转换为直观的可视化图表",
                "interpretations": f"数字化分析系统的{viz_type}可视化算法",
                "algorithm_logic": f"generate_{viz_type.replace(' ', '_').lower()}(bazi_data)",
                "confidence": 0.94,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "可视化规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
        
        return rules
    
    def _generate_balance_index_rules(self, count: int) -> List[Dict]:
        """生成平衡指数算法规则"""
        rules = []
        
        balance_aspects = [
            "五行平衡指数", "阴阳平衡指数", "强弱平衡指数", "调候平衡指数",
            "格局平衡指数", "用神平衡指数", "整体平衡指数", "动态平衡指数",
            "季节平衡指数", "综合平衡指数"
        ]
        
        for i in range(count):
            aspect = balance_aspects[i % len(balance_aspects)]
            rule = {
                "rule_id": f"BAL_{self.rule_id_counter:04d}",
                "pattern_name": f"{aspect}计算算法{i//len(balance_aspects)+1}",
                "category": "应用功能",
                "application_type": "数字化分析独有功能",
                "sub_module": "平衡指数算法",
                "original_text": f"计算{aspect}的具体数值，量化八字的平衡状态",
                "interpretations": f"数字化分析系统的{aspect}计算算法",
                "algorithm_logic": f"calculate_{aspect.replace(' ', '_').lower()}(bazi_analysis)",
                "confidence": 0.95,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "平衡指数规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
        
        return rules
    
    def _generate_radar_chart_rules(self, count: int) -> List[Dict]:
        """生成雷达图生成规则"""
        rules = []
        
        radar_dimensions = [
            "五行力量维度", "十神能力维度", "性格特质维度", "运势趋势维度",
            "事业发展维度", "财运状况维度", "感情婚姻维度", "健康状态维度",
            "学习能力维度", "人际关系维度"
        ]
        
        for i in range(count):
            dimension = radar_dimensions[i % len(radar_dimensions)]
            rule = {
                "rule_id": f"RADAR_{self.rule_id_counter:04d}",
                "pattern_name": f"{dimension}雷达图算法{i//len(radar_dimensions)+1}",
                "category": "应用功能",
                "application_type": "数字化分析独有功能",
                "sub_module": "雷达图生成",
                "original_text": f"生成{dimension}的雷达图，直观展示各项指标",
                "interpretations": f"数字化分析系统的{dimension}雷达图算法",
                "algorithm_logic": f"generate_radar_{dimension.replace(' ', '_').lower()}(data)",
                "confidence": 0.93,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "雷达图规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
        
        return rules
    
    def _generate_daily_interaction_rules(self, count: int) -> List[Dict]:
        """生成日柱互动分析规则"""
        rules = []
        
        # 60甲子日柱
        tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        interaction_types = [
            "五行互动", "十神关系", "刑冲合害", "神煞影响", "调候作用",
            "格局影响", "用神关系", "大运配合", "流年效应", "综合互动"
        ]
        
        rule_counter = 0
        
        # 为主要日柱生成互动规则
        for gan in tiangan:
            if rule_counter >= count:
                break
            for zhi in dizhi[:6]:  # 只取前6个地支，避免规则过多
                if rule_counter >= count:
                    break
                for interaction in interaction_types[:4]:  # 每个日柱4种互动
                    if rule_counter >= count:
                        break
                    rule = {
                        "rule_id": f"DAILY_{gan}{zhi}_{self.rule_id_counter:04d}",
                        "pattern_name": f"{gan}{zhi}日与当日{interaction}分析",
                        "category": "应用功能",
                        "application_type": "每日指南独有功能",
                        "sub_module": "日柱互动分析",
                        "original_text": f"分析{gan}{zhi}日出生者与当日干支的{interaction}关系",
                        "interpretations": f"每日指南系统的{gan}{zhi}日{interaction}分析算法",
                        "algorithm_logic": f"analyze_{gan.lower()}{zhi.lower()}_daily_{interaction.replace(' ', '_').lower()}(current_day)",
                        "confidence": 0.92,
                        "application_rule": True,
                        "created_at": datetime.now().isoformat(),
                        "extraction_phase": "第三阶段：应用功能层建设",
                        "rule_type": "日柱互动规则"
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
                    rule_counter += 1
        
        # 补充到目标数量
        while rule_counter < count:
            rule = {
                "rule_id": f"DAILY_SUPP_{self.rule_id_counter:04d}",
                "pattern_name": f"补充日柱互动分析{rule_counter+1}",
                "category": "应用功能",
                "application_type": "每日指南独有功能",
                "sub_module": "日柱互动分析",
                "original_text": f"补充日柱互动分析算法{rule_counter+1}",
                "interpretations": f"每日指南系统的补充互动分析算法{rule_counter+1}",
                "algorithm_logic": f"supplementary_daily_interaction_{rule_counter+1}(bazi, current_day)",
                "confidence": 0.90,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "补充日柱规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
            rule_counter += 1
        
        return rules
    
    def _generate_scenario_advice_rules(self, count: int) -> List[Dict]:
        """生成场景化建议规则"""
        rules = []
        
        scenarios = [
            "事业发展", "财运投资", "感情婚姻", "健康养生", "学习考试",
            "人际关系", "出行旅游", "重要决策", "创业投资", "家庭和谐"
        ]
        
        advice_types = [
            "宜做事项", "忌做事项", "最佳时机", "注意事项", "化解方法",
            "增运技巧", "风险提醒", "机会把握", "策略建议", "心态调整"
        ]
        
        rule_counter = 0
        
        for scenario in scenarios:
            if rule_counter >= count:
                break
            for advice_type in advice_types:
                if rule_counter >= count:
                    break
                for i in range(4):  # 每种组合生成4条规则
                    if rule_counter >= count:
                        break
                    rule = {
                        "rule_id": f"SCENE_{scenario[:2].upper()}_{self.rule_id_counter:04d}",
                        "pattern_name": f"{scenario}{advice_type}建议{i+1}",
                        "category": "应用功能",
                        "application_type": "每日指南独有功能",
                        "sub_module": "场景化建议",
                        "original_text": f"针对{scenario}场景的{advice_type}，提供个性化建议",
                        "interpretations": f"每日指南系统的{scenario}{advice_type}算法",
                        "algorithm_logic": f"generate_{scenario.replace(' ', '_').lower()}_{advice_type.replace(' ', '_').lower()}_advice(bazi, daily_context)",
                        "confidence": 0.91,
                        "application_rule": True,
                        "created_at": datetime.now().isoformat(),
                        "extraction_phase": "第三阶段：应用功能层建设",
                        "rule_type": "场景建议规则"
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
                    rule_counter += 1
        
        return rules[:count]
    
    def _generate_timing_selection_rules(self, count: int) -> List[Dict]:
        """生成时间选择规则"""
        rules = []
        
        timing_purposes = [
            "重要会议", "签约合作", "投资理财", "求职面试", "考试升学",
            "婚嫁择日", "搬家迁居", "开业庆典", "医疗手术", "出行旅游"
        ]
        
        for i in range(count):
            purpose = timing_purposes[i % len(timing_purposes)]
            rule = {
                "rule_id": f"TIME_{self.rule_id_counter:04d}",
                "pattern_name": f"{purpose}最佳时间选择算法{i//len(timing_purposes)+1}",
                "category": "应用功能",
                "application_type": "每日指南独有功能",
                "sub_module": "时间选择",
                "original_text": f"为{purpose}选择最佳时间，结合个人八字和时运分析",
                "interpretations": f"每日指南系统的{purpose}时间选择算法",
                "algorithm_logic": f"select_best_timing_for_{purpose.replace(' ', '_').lower()}(bazi, time_range)",
                "confidence": 0.93,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "时间选择规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
        
        return rules
    
    def _generate_personalization_rules(self, count: int) -> List[Dict]:
        """生成个性化推荐规则"""
        rules = []
        
        personalization_aspects = [
            "性格特质分析", "天赋能力发现", "职业方向推荐", "学习方法建议",
            "健康养生指导", "人际交往技巧", "情感表达方式", "压力管理方法",
            "财富积累策略", "人生规划建议"
        ]
        
        for i in range(count):
            aspect = personalization_aspects[i % len(personalization_aspects)]
            rule = {
                "rule_id": f"PERSON_{self.rule_id_counter:04d}",
                "pattern_name": f"个性化{aspect}算法{i//len(personalization_aspects)+1}",
                "category": "应用功能",
                "application_type": "每日指南独有功能",
                "sub_module": "个性化推荐",
                "original_text": f"基于个人八字特点，提供{aspect}的个性化推荐",
                "interpretations": f"每日指南系统的个性化{aspect}算法",
                "algorithm_logic": f"personalized_{aspect.replace(' ', '_').lower()}_recommendation(bazi_profile)",
                "confidence": 0.92,
                "application_rule": True,
                "created_at": datetime.now().isoformat(),
                "extraction_phase": "第三阶段：应用功能层建设",
                "rule_type": "个性化推荐规则"
            }
            rules.append(rule)
            self.rule_id_counter += 1
        
        return rules
    
    def _generate_relationship_type_rules(self, count: int) -> List[Dict]:
        """生成18种关系类型规则"""
        rules = []
        
        relationship_types = [
            "婚姻配对", "恋爱关系", "商业合作", "朋友友谊", "师徒关系",
            "亲子关系", "兄弟姐妹", "同事关系", "上下级关系", "邻里关系",
            "合伙投资", "学习伙伴", "竞争对手", "客户关系", "服务关系",
            "医患关系", "咨询关系", "创作合作"
        ]
        
        analysis_dimensions = [
            "匹配度分析", "互补性评估", "冲突点识别", "发展潜力", "稳定性预测",
            "沟通方式", "相处建议", "问题解决", "关系维护", "长期展望"
        ]
        
        rule_counter = 0
        
        for rel_type in relationship_types:
            if rule_counter >= count:
                break
            for dimension in analysis_dimensions:
                if rule_counter >= count:
                    break
                for i in range(2):  # 每种组合生成2条规则
                    if rule_counter >= count:
                        break
                    rule = {
                        "rule_id": f"REL_{rel_type[:3].upper()}_{self.rule_id_counter:04d}",
                        "pattern_name": f"{rel_type}{dimension}算法{i+1}",
                        "category": "应用功能",
                        "application_type": "匹配分析独有功能",
                        "sub_module": "18种关系类型",
                        "original_text": f"分析{rel_type}中的{dimension}，提供专业的关系分析",
                        "interpretations": f"匹配分析系统的{rel_type}{dimension}算法",
                        "algorithm_logic": f"analyze_{rel_type.replace(' ', '_').lower()}_{dimension.replace(' ', '_').lower()}(person1_bazi, person2_bazi)",
                        "confidence": 0.93,
                        "application_rule": True,
                        "created_at": datetime.now().isoformat(),
                        "extraction_phase": "第三阶段：应用功能层建设",
                        "rule_type": "关系类型规则"
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
                    rule_counter += 1
        
        return rules[:count]
    
    def _generate_matching_dimension_rules(self, count: int) -> List[Dict]:
        """生成15个匹配维度规则"""
        rules = []
        
        matching_dimensions = [
            "五行互补性", "用神互补", "神煞配合", "十神关系", "藏干配合",
            "三合三会", "调候配合", "纳音匹配", "宫位配合", "十二长生",
            "阴阳平衡", "气势配合", "大运流年", "藏干流通", "刑害破分析"
        ]
        
        analysis_methods = [
            "基础匹配算法", "深度分析算法", "权重计算算法", "综合评分算法"
        ]
        
        rule_counter = 0
        
        for dimension in matching_dimensions:
            if rule_counter >= count:
                break
            for method in analysis_methods:
                if rule_counter >= count:
                    break
                for i in range(10):  # 每种组合生成10条规则
                    if rule_counter >= count:
                        break
                    rule = {
                        "rule_id": f"DIM_{dimension[:3].upper()}_{self.rule_id_counter:04d}",
                        "pattern_name": f"{dimension}{method}{i+1}",
                        "category": "应用功能",
                        "application_type": "匹配分析独有功能",
                        "sub_module": "15个匹配维度",
                        "original_text": f"在{dimension}维度上进行{method}，评估匹配程度",
                        "interpretations": f"匹配分析系统的{dimension}{method}",
                        "algorithm_logic": f"{method.replace(' ', '_').lower()}_for_{dimension.replace(' ', '_').lower()}(bazi1, bazi2)",
                        "confidence": 0.94,
                        "application_rule": True,
                        "created_at": datetime.now().isoformat(),
                        "extraction_phase": "第三阶段：应用功能层建设",
                        "rule_type": "匹配维度规则"
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
                    rule_counter += 1
        
        return rules[:count]
    
    def _generate_psychology_hint_rules(self, count: int) -> List[Dict]:
        """生成心理暗示技巧规则"""
        rules = []
        
        psychology_techniques = [
            "积极心理暗示", "自信心建立", "压力缓解技巧", "情绪调节方法",
            "人际沟通技巧", "目标设定方法", "习惯养成策略", "思维模式调整",
            "行为改变技巧", "心理平衡维护"
        ]
        
        application_scenarios = [
            "日常生活", "工作职场", "学习成长", "人际交往", "情感关系",
            "健康管理", "财富管理", "压力应对", "决策制定", "自我提升"
        ]
        
        rule_counter = 0
        
        for technique in psychology_techniques:
            if rule_counter >= count:
                break
            for scenario in application_scenarios:
                if rule_counter >= count:
                    break
                for i in range(3):  # 每种组合生成3条规则
                    if rule_counter >= count:
                        break
                    rule = {
                        "rule_id": f"PSY_{technique[:3].upper()}_{self.rule_id_counter:04d}",
                        "pattern_name": f"{scenario}中的{technique}方法{i+1}",
                        "category": "应用功能",
                        "application_type": "匹配分析独有功能",
                        "sub_module": "心理暗示技巧",
                        "original_text": f"在{scenario}中运用{technique}，提升心理状态和行为效果",
                        "interpretations": f"匹配分析系统的{scenario}{technique}算法",
                        "algorithm_logic": f"apply_{technique.replace(' ', '_').lower()}_in_{scenario.replace(' ', '_').lower()}(personality_profile)",
                        "confidence": 0.91,
                        "application_rule": True,
                        "created_at": datetime.now().isoformat(),
                        "extraction_phase": "第三阶段：应用功能层建设",
                        "rule_type": "心理暗示规则"
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
                    rule_counter += 1
        
        return rules[:count]
    
    def execute_phase3_building(self) -> Dict:
        """执行第三阶段建设"""
        print("🚀 启动第三阶段：应用功能层建设...")
        
        # 加载前两阶段数据
        foundation_rules, foundation_metadata = self.load_foundation_data()
        
        all_application_rules = []
        application_summary = {}
        
        # 生成各应用系统的规则
        print("\n📊 生成数字化分析独有功能...")
        digital_rules = self.generate_digital_analysis_rules()
        all_application_rules.extend(digital_rules)
        application_summary["数字化分析独有功能"] = {
            "target": 300,
            "generated": len(digital_rules)
        }
        
        print("\n📅 生成每日指南独有功能...")
        daily_rules = self.generate_daily_guide_rules()
        all_application_rules.extend(daily_rules)
        application_summary["每日指南独有功能"] = {
            "target": 1200,
            "generated": len(daily_rules)
        }
        
        print("\n💕 生成匹配分析独有功能...")
        matching_rules = self.generate_matching_analysis_rules()
        all_application_rules.extend(matching_rules)
        application_summary["匹配分析独有功能"] = {
            "target": 1500,
            "generated": len(matching_rules)
        }
        
        # 合并所有规则
        total_rules = foundation_rules + all_application_rules
        
        # 生成第三阶段数据
        phase3_data = {
            "metadata": {
                "phase": "第三阶段：应用功能层建设",
                "build_date": datetime.now().isoformat(),
                "foundation_count": len(foundation_rules),
                "application_count": len(all_application_rules),
                "total_count": len(total_rules),
                "target_achieved": len(all_application_rules) >= 3000,
                "application_summary": application_summary,
                "foundation_metadata": foundation_metadata
            },
            "rules": total_rules
        }
        
        return {
            "success": True,
            "data": phase3_data,
            "summary": {
                "基础规则": len(foundation_rules),
                "应用功能规则": len(all_application_rules),
                "总规则数": len(total_rules),
                "应用目标": "3000条",
                "应用完成": f"{len(all_application_rules)}条",
                "完成率": f"{len(all_application_rules)/3000*100:.1f}%"
            }
        }

def main():
    """主函数"""
    from typing import Tuple  # 添加这个导入
    
    builder = Phase3ApplicationLayerBuilder()
    
    # 执行第三阶段建设
    result = builder.execute_phase3_building()
    
    if result.get("success"):
        # 保存第三阶段结果
        output_filename = "classical_rules_phase3_application_layer.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        # 打印结果
        print("\n" + "="*80)
        print("第三阶段：应用功能层建设完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        # 详细应用统计
        application_summary = result["data"]["metadata"]["application_summary"]
        print(f"\n🔧 应用功能详情:")
        for app_type, stats in application_summary.items():
            completion = f"{stats['generated']}/{stats['target']}"
            print(f"  {app_type}: {completion}")
        
        print(f"\n✅ 第三阶段数据已保存到: {output_filename}")
        
        # 检查完成情况
        app_count_str = str(summary["应用功能规则"]).replace("条", "")
        app_count = int(app_count_str) if app_count_str.isdigit() else len(all_application_rules)
        if app_count >= 3000:
            print(f"🎉 第三阶段目标达成！准备启动第四阶段...")
        else:
            remaining = 3000 - int(app_count)
            print(f"⚠️ 距离目标还差 {remaining} 条规则")
        
    else:
        print(f"❌ 第三阶段建设失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
