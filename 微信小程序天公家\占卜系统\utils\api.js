// utils/api.js - API调用封装
const config = require('./config');

class ApiService {
  constructor() {
    this.baseUrl = config.apiBaseUrl;
    this.timeout = config.timeout;
    this.debug = config.debug;
    
    if (this.debug) {
      console.log('ApiService初始化:', {
        baseUrl: this.baseUrl,
        timeout: this.timeout
      });
    }
  }
  
  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const url = `${this.baseUrl}${options.url}`;
      
      if (this.debug) {
        console.log('发起API请求:', {
          url: url,
          method: options.method || 'GET',
          data: options.data
        });
      }
      
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
      
      wx.request({
        url: url,
        method: options.method || 'GET',
        data: options.data || {},
        timeout: this.timeout,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          wx.hideLoading();
          
          if (this.debug) {
            console.log('API响应:', res);
          }
          
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            const errorMsg = res.data?.detail || `HTTP ${res.statusCode}`;
            reject(new Error(errorMsg));
          }
        },
        fail: (err) => {
          wx.hideLoading();
          
          if (this.debug) {
            console.error('API请求失败:', err);
          }
          
          let errorMsg = '网络请求失败';
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMsg = '请求超时，请检查网络';
            } else if (err.errMsg.includes('fail')) {
              errorMsg = '无法连接到服务器';
            }
          }
          
          reject(new Error(errorMsg));
        }
      });
    });
  }
  
  // 健康检查
  healthCheck() {
    return this.request({
      url: '/health'
    });
  }
  
  // 搜索占卜
  searchDivination(params = {}) {
    return this.request({
      url: '/api/v1/divination/search',
      data: {
        keyword: params.keyword || '',
        content_type: params.content_type || '',
        luck_level: params.luck_level || '',
        category: params.category || '',
        page: params.page || 1,
        page_size: params.page_size || 20
      }
    });
  }
  
  // 获取随机占卜
  getRandomDivination(params = {}) {
    return this.request({
      url: '/api/v1/divination/random',
      data: params
    });
  }
  
  // 根据日期查询占卜
  getDivinationByDate(heavenlyStem, earthlyBranch, page = 1, pageSize = 20) {
    return this.request({
      url: '/api/v1/divination/by-date',
      data: {
        heavenly_stem: heavenlyStem,
        earthly_branch: earthlyBranch,
        page: page,
        page_size: pageSize
      }
    });
  }
  
  // 获取分类信息
  getCategories() {
    return this.request({
      url: '/api/v1/categories'
    });
  }
  
  // 获取统计信息
  getStatistics() {
    return this.request({
      url: '/api/v1/statistics'
    });
  }

  // 增强占卜结果（新的API）
  enhanceDivinationResult(data) {
    return this.request({
      url: '/api/v1/divination/enhance',
      method: 'POST',
      data: data
    });
  }
  
  // 显示错误提示
  showError(message) {
    wx.showToast({
      title: message || '操作失败',
      icon: 'none',
      duration: 2000
    });
  }
  
  // 显示成功提示
  showSuccess(message) {
    wx.showToast({
      title: message || '操作成功',
      icon: 'success',
      duration: 1500
    });
  }
}

// 创建API实例
const apiService = new ApiService();

module.exports = apiService;
