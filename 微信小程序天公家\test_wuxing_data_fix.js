// test_wuxing_data_fix.js
// 测试五行数据修复

console.log('🧪 开始测试五行数据修复');

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null
};

// 测试专业五行计算结果结构
const mockProfessionalWuxingResult = {
  // 顶层五行数据（这是我们需要的）
  wood: 2.5,
  fire: 3.2,
  earth: 1.8,
  metal: 2.1,
  water: 0.4,
  
  // 专业级扩展数据
  professionalData: {
    algorithm: '专业级五行动态交互分析系统',
    version: 'V2.0 - 完整版',
    isProfessional: true,
    staticAnalysis: {
      powers: { '木': 2.5, '火': 3.2, '土': 1.8, '金': 2.1, '水': 0.4 }
    }
  }
};

console.log('📊 模拟专业五行计算结果:', mockProfessionalWuxingResult);

// 测试数据提取逻辑
function testFiveElementsExtraction(professionalWuxingResult) {
  console.log('\n🔧 测试五行数据提取逻辑:');
  
  let finalFiveElements;
  if (professionalWuxingResult && professionalWuxingResult.wood !== undefined) {
    // 从专业计算结果的顶层提取五行数据
    finalFiveElements = {
      wood: professionalWuxingResult.wood || 0,
      fire: professionalWuxingResult.fire || 0,
      earth: professionalWuxingResult.earth || 0,
      metal: professionalWuxingResult.metal || 0,
      water: professionalWuxingResult.water || 0
    };
    console.log('✅ 使用专业级五行计算结果:', finalFiveElements);
  } else {
    // 使用备用数据
    const defaultElements = { wood: 2, fire: 3, earth: 2, metal: 1, water: 0 };
    finalFiveElements = defaultElements;
    console.log('⚠️ 专业级计算失败，使用备用五行数据:', finalFiveElements);
  }
  
  return finalFiveElements;
}

// 测试1: 正常的专业五行结果
console.log('\n📋 测试1: 正常的专业五行结果');
const result1 = testFiveElementsExtraction(mockProfessionalWuxingResult);
console.log('结果1:', result1);

// 测试2: 空的专业五行结果
console.log('\n📋 测试2: 空的专业五行结果');
const result2 = testFiveElementsExtraction(null);
console.log('结果2:', result2);

// 测试3: 不完整的专业五行结果
console.log('\n📋 测试3: 不完整的专业五行结果');
const incompleteResult = { professionalData: { isProfessional: true } };
const result3 = testFiveElementsExtraction(incompleteResult);
console.log('结果3:', result3);

// 测试4: 只有部分五行数据的结果
console.log('\n📋 测试4: 只有部分五行数据的结果');
const partialResult = { wood: 1.5, fire: 2.0 }; // 缺少 earth, metal, water
const result4 = testFiveElementsExtraction(partialResult);
console.log('结果4:', result4);

// 验证结果
console.log('\n📊 验证结果:');
const allResults = [result1, result2, result3, result4];
let allValid = true;

allResults.forEach((result, index) => {
  const hasAllElements = ['wood', 'fire', 'earth', 'metal', 'water'].every(
    element => typeof result[element] === 'number' && result[element] >= 0
  );
  
  if (hasAllElements) {
    console.log(`✅ 结果${index + 1}有效: 包含所有五行元素`);
  } else {
    console.log(`❌ 结果${index + 1}无效: 缺少五行元素`);
    allValid = false;
  }
});

if (allValid) {
  console.log('\n🎉 所有测试通过！五行数据提取逻辑修复成功。');
  console.log('\n💡 修复要点:');
  console.log('1. ✅ 从专业五行结果的顶层提取数据（不是从 professionalData 中）');
  console.log('2. ✅ 检查 wood 属性存在性来判断数据有效性');
  console.log('3. ✅ 为缺失的元素提供默认值 0');
  console.log('4. ✅ 在所有情况下都返回完整的五行对象');
  console.log('\n🔧 这应该解决日志中看到的 "五行数据验证完成: {wood: 0, fire: 0, earth: 0, metal: 0, water: 0}" 问题');
} else {
  console.log('\n⚠️ 部分测试失败，需要进一步检查');
}

// 额外测试：验证数据类型
console.log('\n🔍 额外测试：验证数据类型');
const typeTest = testFiveElementsExtraction(mockProfessionalWuxingResult);
Object.entries(typeTest).forEach(([element, value]) => {
  if (typeof value === 'number' && value >= 0) {
    console.log(`✅ ${element}: ${value} (数字类型，有效)`);
  } else {
    console.log(`❌ ${element}: ${value} (类型: ${typeof value}，无效)`);
  }
});

module.exports = { testFiveElementsExtraction };
