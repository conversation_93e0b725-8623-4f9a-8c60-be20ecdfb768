/**
 * 验证月德合计算
 * 测试数据：2021年6月24日 19:30 (辛丑 甲午 癸卯 壬戌)
 */

// 测试数据
const testFourPillars = [
  { gan: '辛', zhi: '丑' },  // 年柱
  { gan: '甲', zhi: '午' },  // 月柱
  { gan: '癸', zhi: '卯' },  // 日柱
  { gan: '壬', zhi: '戌' }   // 时柱
];

const yearZhi = '丑';  // 年支

console.log('🧪 验证月德合计算');
console.log('📅 测试数据: 2021年6月24日 19:30 (辛丑 甲午 癸卯 壬戌)');
console.log(`年支: ${yearZhi}`);
console.log('');

// 月德计算（基于年支三合局）
const qianliYuedeMap = {
  '寅': '丙', '午': '丙', '戌': '丙',  // 寅午戌年逢丙
  '申': '壬', '子': '壬', '辰': '壬',  // 申子辰年逢壬
  '亥': '甲', '卯': '甲', '未': '甲',  // 亥卯未年逢甲
  '巳': '庚', '酉': '庚', '丑': '庚'   // 巳酉丑年逢庚
};

const yuede = qianliYuedeMap[yearZhi];
console.log(`年支${yearZhi}对应的月德: ${yuede}`);

// 天干合化关系
const ganHeMap = {
  '甲': '己', '己': '甲',
  '乙': '庚', '庚': '乙',
  '丙': '辛', '辛': '丙',
  '丁': '壬', '壬': '丁',
  '戊': '癸', '癸': '戊'
};

const yuehe = yuede ? ganHeMap[yuede] : null;
console.log(`月德${yuede}对应的月德合: ${yuehe}`);

// 检查四柱中是否有月德合
const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
let foundYuehe = false;

console.log('');
console.log('🔍 检查四柱中的月德合:');
testFourPillars.forEach((pillar, index) => {
  console.log(`${pillarNames[index]}: ${pillar.gan}${pillar.zhi}`);
  if (pillar.gan === yuehe) {
    console.log(`✅ 找到月德合！${pillarNames[index]}的天干${pillar.gan}匹配月德合${yuehe}`);
    foundYuehe = true;
  }
});

if (!foundYuehe) {
  console.log(`❌ 未找到月德合${yuehe}`);
}

console.log('');
console.log('🎯 与"问真八字"标准对比:');
console.log('期望结果: 年柱应该有月德合');
console.log('实际结果:', foundYuehe ? '✅ 找到月德合' : '❌ 未找到月德合');

// 详细分析
console.log('');
console.log('📊 详细分析:');
console.log('1. 年支丑属于巳酉丑三合金局');
console.log('2. 巳酉丑年的月德是庚');
console.log('3. 庚的合化天干是乙');
console.log('4. 所以月德合应该是乙');
console.log('5. 检查四柱中是否有天干乙...');

testFourPillars.forEach((pillar, index) => {
  if (pillar.gan === '乙') {
    console.log(`✅ 年柱${pillar.gan}${pillar.zhi}中找到天干乙！`);
  }
});

console.log('');
console.log('🤔 问题分析:');
console.log('我们的四柱中没有天干乙，所以按照计算规则，确实不应该有月德合。');
console.log('但"问真八字"显示年柱有月德合，这可能意味着:');
console.log('1. 我们的月德合计算规则不正确');
console.log('2. 或者"问真八字"使用了不同的计算方法');
console.log('3. 或者需要考虑其他因素（如地支藏干等）');

// 检查地支藏干
console.log('');
console.log('🔍 检查地支藏干中的月德合:');
const cangganMap = {
  '子': ['癸'],
  '丑': ['己', '癸', '辛'],
  '寅': ['甲', '丙', '戊'],
  '卯': ['乙'],
  '辰': ['戊', '乙', '癸'],
  '巳': ['丙', '庚', '戊'],
  '午': ['丁', '己'],
  '未': ['己', '丁', '乙'],
  '申': ['庚', '壬', '戊'],
  '酉': ['辛'],
  '戌': ['戊', '辛', '丁'],
  '亥': ['壬', '甲']
};

testFourPillars.forEach((pillar, index) => {
  const canggan = cangganMap[pillar.zhi] || [];
  console.log(`${pillarNames[index]} ${pillar.gan}${pillar.zhi} 藏干: [${canggan.join(', ')}]`);
  
  if (canggan.includes('乙')) {
    console.log(`✅ ${pillarNames[index]}的地支${pillar.zhi}藏干中有乙！`);
  }
});

console.log('');
console.log('💡 发现: 卯的藏干是乙，未的藏干也包含乙');
console.log('如果月德合计算需要考虑地支藏干，那么日柱癸卯的藏干乙可能就是月德合的来源');
console.log('但这样的话，月德合应该在日柱，而不是年柱');
console.log('');
console.log('🔍 需要进一步研究月德合的准确计算方法...');
