#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版缺口填补提取工具
专门针对数字化分析(55条)和匹配分析(50条)的缺口进行深度解析
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class EnhancedGapFillingExtractor:
    def __init__(self):
        self.rule_id_counter = 1000000
        
        # 精确的缺口目标
        self.gap_targets = {
            "数字化分析": 55,
            "匹配分析": 50
        }
        
        # 增强的古籍解析配置
        self.enhanced_config = {
            "滴天髓": {
                "file": "滴天髓.pdf",
                "focus_areas": {
                    "数字化分析": {
                        "ultra_patterns": [
                            # 精微程度分析
                            r'[^。]*?[精微|奥妙|深浅|轻重|强弱|高低][^。]*?[程度|等级|分寸|火候][^。]*?。',
                            r'[^。]*?[一分|二分|三分|半分|几分][^。]*?[力|气|势|意][^。]*?。',
                            r'[^。]*?[太过|不及|中和|偏枯][^。]*?[如何|怎样][^。]*?[分辨|判断|衡量][^。]*?。',
                            r'[^。]*?[旺衰|强弱][^。]*?[细分|详分|精分|微分][^。]*?。',
                            r'[^。]*?[量|度|数|级][^。]*?[五行|阴阳|干支|格局][^。]*?。'
                        ],
                        "target": 25
                    },
                    "匹配分析": {
                        "ultra_patterns": [
                            # 深层配偶理论
                            r'[^。]*?[夫妻|配偶|阴阳|男女][^。]*?[配合|相配|匹配|和谐|协调][^。]*?。',
                            r'[^。]*?[夫星|妻星|夫宫|妻宫][^。]*?[得失|有无|强弱|清浊][^。]*?。',
                            r'[^。]*?[婚姻|感情|情缘][^。]*?[深层|本质|根本|内在][^。]*?[机理|道理|原理][^。]*?。'
                        ],
                        "target": 20
                    }
                }
            },
            "三命通会": {
                "file": "《三命通会》完整白话版  .pdf",
                "focus_areas": {
                    "数字化分析": {
                        "ultra_patterns": [
                            # 神煞力量分析
                            r'[^。]*?[神煞|贵人|凶神][^。]*?[力量|威力|影响|作用][^。]*?[大小|强弱|轻重][^。]*?。',
                            r'[^。]*?[几重|几分|几等|几级][^。]*?[贵|凶|吉|害][^。]*?。',
                            r'[^。]*?[轻重|深浅|厚薄|大小][^。]*?[如何|怎样][^。]*?[区分|分别|判断][^。]*?。'
                        ],
                        "target": 15
                    },
                    "匹配分析": {
                        "ultra_patterns": [
                            # 神煞婚姻配合
                            r'[^。]*?[桃花|红鸾|天喜|咸池|孤辰|寡宿][^。]*?[夫妻|配偶|婚姻][^。]*?。',
                            r'[^。]*?[夫妻|配偶][^。]*?[神煞|星曜|贵人][^。]*?[配合|相克|相生][^。]*?。',
                            r'[^。]*?[男女|阴阳][^。]*?[命格|格局][^。]*?[配合|匹配|相配][^。]*?。'
                        ],
                        "target": 15
                    }
                }
            },
            "渊海子平": {
                "file": "渊海子平.docx",
                "focus_areas": {
                    "数字化分析": {
                        "ultra_patterns": [
                            # 十神力量精算
                            r'[^。]*?[正官|偏官|正财|偏财|正印|偏印|食神|伤官|比肩|劫财][^。]*?[力量|强度|影响][^。]*?[如何|怎样][^。]*?[计算|评估|衡量][^。]*?。',
                            r'[^。]*?[用神|喜神|忌神][^。]*?[力量|强弱][^。]*?[分析|判断|评定][^。]*?。',
                            r'[^。]*?[格局][^。]*?[高低|优劣|等级|品第][^。]*?[如何|怎样][^。]*?[分辨|区别][^。]*?。'
                        ],
                        "target": 10
                    },
                    "匹配分析": {
                        "ultra_patterns": [
                            # 子平配偶详论
                            r'[^。]*?[夫妻|配偶][^。]*?[十神|六神][^。]*?[配置|组合|搭配][^。]*?。',
                            r'[^。]*?[日主|日元][^。]*?[配偶|夫妻][^。]*?[关系|相处|配合][^。]*?[分析|论断][^。]*?。',
                            r'[^。]*?[夫妻宫|配偶宫][^。]*?[十神|神煞][^。]*?[吉凶|好坏|利害][^。]*?。'
                        ],
                        "target": 10
                    }
                }
            },
            "千里命稿": {
                "file": "千里命稿.txt",
                "focus_areas": {
                    "数字化分析": {
                        "ultra_patterns": [
                            # 实战量化技法
                            r'[^。]*?[几成|几分|几等|几级][^。]*?[力|气|势|功][^。]*?。',
                            r'[^。]*?[强弱|轻重|深浅][^。]*?[实际|具体|准确][^。]*?[判断|分析|评估][^。]*?[方法|技巧|诀窍][^。]*?。',
                            r'[^。]*?[量化|数值化|具体化][^。]*?[分析|判断|评估][^。]*?。'
                        ],
                        "target": 5
                    },
                    "匹配分析": {
                        "ultra_patterns": [
                            # 实用配偶技法
                            r'[^。]*?[配偶|夫妻][^。]*?[实际|具体|准确][^。]*?[分析|判断|推断][^。]*?[方法|技法|诀窍][^。]*?。',
                            r'[^。]*?[夫星|妻星][^。]*?[实用|实际|具体][^。]*?[看法|论法|断法][^。]*?。'
                        ],
                        "target": 5
                    }
                }
            }
        }
    
    def load_ancient_book(self, book_name: str) -> str:
        """加载古籍内容"""
        if book_name not in self.enhanced_config:
            return ""
        
        filename = self.enhanced_config[book_name]["file"]
        file_path = os.path.join("古籍资料", filename)
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在: {filename}")
            return ""
        
        try:
            if filename.endswith('.txt'):
                return self._load_txt_content(file_path)
            elif filename.endswith('.docx'):
                return self._load_docx_content(file_path)
            elif filename.endswith('.pdf'):
                return self._load_pdf_content(file_path)
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
            return ""
        
        return ""
    
    def _load_txt_content(self, file_path: str) -> str:
        """加载TXT内容"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    print(f"  ✅ TXT加载成功: {len(content):,} 字符")
                    return content
            except UnicodeDecodeError:
                continue
        return ""
    
    def _load_docx_content(self, file_path: str) -> str:
        """加载DOCX内容"""
        try:
            from docx import Document
            doc = Document(file_path)
            content = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            print(f"  ✅ DOCX加载成功: {len(content):,} 字符")
            return content
        except ImportError:
            print("  需要安装python-docx库")
            return ""
        except Exception:
            return ""
    
    def _load_pdf_content(self, file_path: str) -> str:
        """加载PDF内容"""
        try:
            import PyPDF2
            content_parts = []
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                # 处理更多页面以获得更全面的内容
                max_pages = min(1000, total_pages)
                
                for i in range(max_pages):
                    try:
                        page_text = pdf_reader.pages[i].extract_text()
                        if page_text and len(page_text.strip()) > 20:
                            cleaned_text = re.sub(r'\s+', ' ', page_text).strip()
                            content_parts.append(cleaned_text)
                    except:
                        continue
                
                content = '\n'.join(content_parts)
                print(f"  ✅ PDF加载成功: {max_pages}/{total_pages}页, {len(content):,} 字符")
                return content
        except ImportError:
            print("  需要安装PyPDF2库")
            return ""
        except Exception:
            return ""
    
    def extract_gap_filling_rules(self, content: str, book_name: str, 
                                dimension: str, config: Dict) -> List[Dict]:
        """提取缺口填补规则"""
        if not content:
            return []
        
        patterns = config["ultra_patterns"]
        target = config["target"]
        
        all_extracted = []
        
        print(f"  🎯 深度提取{dimension}规则 (目标: {target}条)...")
        
        # 1. 超精准模式匹配
        for i, pattern in enumerate(patterns):
            try:
                matches = re.findall(pattern, content)
                print(f"    超精准模式{i+1}: {len(matches)}条")
                
                for match in matches:
                    cleaned_text = self._ultra_clean_text(match)
                    if self._ultra_validate_rule(cleaned_text, dimension):
                        rule = self._create_gap_filling_rule(
                            cleaned_text, book_name, dimension, f"超精准模式{i+1}"
                        )
                        all_extracted.append(rule)
            except Exception as e:
                continue
        
        # 2. 深度上下文提取
        context_rules = self._extract_deep_context(content, book_name, dimension)
        all_extracted.extend(context_rules)
        
        # 3. 语义相关性提取
        semantic_rules = self._extract_semantic_related(content, book_name, dimension)
        all_extracted.extend(semantic_rules)
        
        # 4. 精确去重和质量筛选
        unique_rules = self._ultra_deduplicate(all_extracted)
        
        # 5. 按质量排序并精确控制数量
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        final_rules = unique_rules[:target]
        
        print(f"  ✅ 深度提取完成: {len(final_rules)}条高质量规则")
        return final_rules
    
    def _extract_deep_context(self, content: str, book_name: str, dimension: str) -> List[Dict]:
        """深度上下文提取"""
        rules = []
        
        # 根据维度选择核心关键词
        if dimension == "数字化分析":
            core_keywords = ["强弱", "程度", "等级", "分析", "量化", "评估", "计算"]
        else:  # 匹配分析
            core_keywords = ["夫妻", "配偶", "婚姻", "配合", "匹配", "和谐"]
        
        # 扩大上下文窗口
        for keyword in core_keywords:
            for match in re.finditer(keyword, content):
                start = max(0, match.start() - 150)
                end = min(len(content), match.end() + 150)
                context = content[start:end]
                
                # 提取完整的理论句子
                sentences = re.split(r'[。；]', context)
                for sentence in sentences:
                    if keyword in sentence and 40 <= len(sentence) <= 400:
                        cleaned_text = self._ultra_clean_text(sentence)
                        if self._ultra_validate_rule(cleaned_text, dimension):
                            rule = self._create_gap_filling_rule(
                                cleaned_text, book_name, dimension, "深度上下文"
                            )
                            rules.append(rule)
        
        return rules
    
    def _extract_semantic_related(self, content: str, book_name: str, dimension: str) -> List[Dict]:
        """语义相关性提取"""
        rules = []
        
        # 语义相关模式
        if dimension == "数字化分析":
            semantic_patterns = [
                r'[^。]*?[一二三四五六七八九十][^。]*?[分|等|级|度|重|轻][^。]*?。',
                r'[^。]*?[上中下|高中低|强中弱|大中小][^。]*?[等|级|品|类][^。]*?。',
                r'[^。]*?[最|极|甚|很|较|稍|更|尤][^。]*?[强|弱|重|轻|深|浅][^。]*?。'
            ]
        else:  # 匹配分析
            semantic_patterns = [
                r'[^。]*?[男女|阴阳|夫妇][^。]*?[相|互|彼此][^。]*?[配|合|和|谐|克|冲][^。]*?。',
                r'[^。]*?[配偶|伴侣|夫妻][^。]*?[性情|品格|相貌|才能][^。]*?。',
                r'[^。]*?[婚配|合婚|择偶][^。]*?[宜|忌|利|害|吉|凶][^。]*?。'
            ]
        
        for pattern in semantic_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                cleaned_text = self._ultra_clean_text(match)
                if self._ultra_validate_rule(cleaned_text, dimension):
                    rule = self._create_gap_filling_rule(
                        cleaned_text, book_name, dimension, "语义相关"
                    )
                    rules.append(rule)
        
        return rules
    
    def _ultra_clean_text(self, text: str) -> str:
        """超级文本清理"""
        if not text:
            return ""
        
        # 基础清理
        text = re.sub(r'\s+', ' ', text).strip()
        
        # OCR错误修复
        ocr_fixes = {
            '氺': '水', '灬': '火', '釒': '金', '本': '木', '士': '土',
            '沂水易士注': '', '例如：': '', '注：': '', '按：': '',
            '又云：': '', '古云：': '', '书云：': '', '经云：': ''
        }
        
        for old, new in ocr_fixes.items():
            text = text.replace(old, new)
        
        # 标点标准化
        text = text.replace('，', '，').replace('。', '。')
        text = re.sub(r'([，。；：？！])\1+', r'\1', text)
        
        return text.strip()
    
    def _ultra_validate_rule(self, text: str, dimension: str) -> bool:
        """超级规则验证"""
        if not text or len(text) < 25 or len(text) > 600:
            return False
        
        # 维度特定验证
        if dimension == "数字化分析":
            required_indicators = ["强弱", "程度", "等级", "分析", "计算", "评估", "量化", "数值"]
            theory_indicators = ["格局", "用神", "五行", "十神", "旺衰", "命理", "八字"]
        else:  # 匹配分析
            required_indicators = ["夫妻", "配偶", "婚姻", "配合", "匹配", "和谐", "感情"]
            theory_indicators = ["关系", "相处", "交往", "性格", "品德", "相貌", "才能"]
        
        has_required = any(indicator in text for indicator in required_indicators)
        has_theory = any(indicator in text for indicator in theory_indicators)
        
        return has_required and has_theory
    
    def _create_gap_filling_rule(self, text: str, book_name: str, 
                               dimension: str, method: str) -> Dict:
        """创建缺口填补规则"""
        # 高质量置信度
        confidence = 0.92 + (len(text) / 1000) * 0.03
        confidence = min(0.98, confidence)
        
        rule = {
            "rule_id": f"GAP_{dimension[:3].upper()}_{self.rule_id_counter:06d}",
            "pattern_name": f"《{book_name}》·{dimension}缺口填补规则",
            "category": dimension,
            "dimension_type": dimension,
            "book_source": book_name,
            "extraction_method": method,
            "original_text": text,
            "interpretations": f"出自《{book_name}》的{dimension}深度理论，专门填补数据缺口",
            "confidence": confidence,
            "gap_filling_rule": True,
            "extracted_at": datetime.now().isoformat(),
            "extraction_phase": "缺口填补专项提取",
            "rule_type": f"{dimension}缺口填补规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _ultra_deduplicate(self, rules: List[Dict]) -> List[Dict]:
        """超级去重"""
        seen_texts = set()
        unique_rules = []
        
        for rule in rules:
            text = rule.get('original_text', '')
            # 更严格的去重标准
            simplified = re.sub(r'[\s\W]', '', text)[:30]
            
            if simplified not in seen_texts and len(simplified) > 15:
                seen_texts.add(simplified)
                unique_rules.append(rule)
        
        return unique_rules
    
    def execute_gap_filling(self) -> Dict:
        """执行缺口填补"""
        print("🚀 开始缺口填补专项提取...")
        print(f"🎯 目标缺口: {self.gap_targets}")
        
        all_gap_rules = {}
        total_extracted = 0
        
        for book_name, book_config in self.enhanced_config.items():
            print(f"\n📚 深度解析《{book_name}》...")
            
            content = self.load_ancient_book(book_name)
            if not content:
                continue
            
            focus_areas = book_config["focus_areas"]
            for dimension, area_config in focus_areas.items():
                if dimension in self.gap_targets:
                    rules = self.extract_gap_filling_rules(content, book_name, dimension, area_config)
                    
                    if dimension not in all_gap_rules:
                        all_gap_rules[dimension] = []
                    
                    all_gap_rules[dimension].extend(rules)
                    total_extracted += len(rules)
        
        # 生成结果数据
        result_data = {
            "metadata": {
                "extraction_type": "缺口填补专项提取",
                "extraction_date": datetime.now().isoformat(),
                "target_gaps": self.gap_targets,
                "total_extracted": total_extracted,
                "books_processed": len(self.enhanced_config),
                "gap_filling_results": {
                    dimension: {
                        "target": self.gap_targets[dimension],
                        "extracted": len(rules),
                        "completion_rate": f"{len(rules)/self.gap_targets[dimension]*100:.1f}%"
                    }
                    for dimension, rules in all_gap_rules.items()
                }
            },
            "gap_filling_rules": all_gap_rules
        }
        
        return {
            "success": True,
            "data": result_data,
            "summary": {
                "总提取规则": total_extracted,
                "数字化分析": len(all_gap_rules.get("数字化分析", [])),
                "匹配分析": len(all_gap_rules.get("匹配分析", []))
            }
        }

def main():
    """主函数"""
    extractor = EnhancedGapFillingExtractor()
    
    result = extractor.execute_gap_filling()
    
    if result.get("success"):
        output_filename = f"gap_filling_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        print("\n" + "="*80)
        print("🎉 缺口填补专项提取完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        gap_results = result["data"]["metadata"]["gap_filling_results"]
        print(f"\n📊 缺口填补情况:")
        for dimension, stats in gap_results.items():
            print(f"  {dimension}: {stats['extracted']}/{stats['target']} ({stats['completion_rate']})")
        
        print(f"\n✅ 缺口填补结果已保存到: {output_filename}")
        
    else:
        print(f"❌ 缺口填补失败")

if __name__ == "__main__":
    main()
