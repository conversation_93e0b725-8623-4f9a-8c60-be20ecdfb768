/**
 * 验证所有路径引用是否正确
 * 确保所有require语句指向存在的文件
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证路径引用...');

// 需要检查的文件
const filesToCheck = [
  'pages/bazi-input/index.js',
  'pages/bazi-result/index.js'
];

// 提取require语句的正则表达式
const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;

let totalIssues = 0;

filesToCheck.forEach(filePath => {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ 文件不存在: ${filePath}`);
    return;
  }

  console.log(`\n📋 检查文件: ${filePath}`);
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let match;
  requireRegex.lastIndex = 0; // 重置正则表达式
  
  while ((match = requireRegex.exec(content)) !== null) {
    const requiredPath = match[1];
    
    // 跳过内置模块
    if (!requiredPath.startsWith('.') && !requiredPath.startsWith('/')) {
      continue;
    }
    
    // 计算实际文件路径
    const baseDir = path.dirname(filePath);
    let actualPath = path.resolve(baseDir, requiredPath);
    
    // 尝试不同的扩展名
    const possiblePaths = [
      actualPath,
      actualPath + '.js',
      actualPath + '/index.js'
    ];
    
    let fileExists = false;
    let existingPath = '';
    
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        fileExists = true;
        existingPath = testPath;
        break;
      }
    }
    
    // 找到包含这个require的行号
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    if (fileExists) {
      console.log(`   ✅ 第${lineNumber}行: ${requiredPath} -> ${path.relative('.', existingPath)}`);
    } else {
      console.log(`   ❌ 第${lineNumber}行: ${requiredPath} -> 文件不存在`);
      console.log(`      尝试过的路径:`);
      possiblePaths.forEach(p => {
        console.log(`        - ${path.relative('.', p)}`);
      });
      totalIssues++;
    }
  }
});

// 检查utils目录中的文件是否存在
console.log('\n📁 检查utils目录中的关键文件...');

const utilsFiles = [
  'utils/authoritative_lunar_data.js',
  'utils/true_solar_time_engine.js',
  'utils/city_coordinates.js',
  'utils/config.js',
  'utils/unified_wuxing_calculator_safe.js',
  'utils/enhanced_pattern_analyzer.js',
  'utils/enhanced_yongshen_calculator.js',
  'utils/enhanced_dynamic_analyzer.js',
  'utils/enhanced_advice_generator.js',
  'utils/celebrity_database_api.js',
  'utils/bazi_similarity_matcher.js',
  'utils/shensha_architecture_manager.js',
  'utils/unified_architecture_manager.js',
  'utils/unified_basic_info_calculator.js'
];

utilsFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`   ✅ ${filePath}`);
  } else {
    console.log(`   ❌ ${filePath} - 文件不存在`);
    totalIssues++;
  }
});

// 检查特殊文件
console.log('\n📁 检查特殊文件...');
const specialFiles = [
  '权威节气数据_前端就绪版.js'
];

specialFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`   ✅ ${filePath}`);
  } else {
    console.log(`   ❌ ${filePath} - 文件不存在`);
    totalIssues++;
  }
});

// 总结
console.log('\n📊 验证结果总结:');
console.log('='.repeat(40));

if (totalIssues === 0) {
  console.log('🎉 所有路径引用都正确！');
  console.log('✅ 没有发现路径引用问题');
} else {
  console.log(`⚠️ 发现 ${totalIssues} 个路径引用问题`);
  console.log('💡 建议修复这些问题以避免编译错误');
}

// 提供修复建议
if (totalIssues > 0) {
  console.log('\n🔧 修复建议:');
  console.log('1. 检查文件是否存在于正确的位置');
  console.log('2. 确认文件名拼写正确');
  console.log('3. 验证相对路径是否正确');
  console.log('4. 考虑移除 .js 扩展名（Node.js会自动添加）');
}

console.log('\n🏁 路径引用验证完成');
