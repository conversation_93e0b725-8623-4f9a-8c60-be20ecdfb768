/**
 * 神煞系统完整度评估
 * 分析当前神煞系统的覆盖情况和是否需要添加更多神煞
 */

// 当前已实现的神煞（基于"问真八字"标准）
const CURRENT_IMPLEMENTED = {
  // 重要贵人类（8个）
  guiren: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人'],
  tiande: ['月德合'],
  taohua: ['桃花'],
  
  // 重要凶煞类（8个）
  xiongsha: ['童子煞', '灾煞', '血刃', '寡宿', '披麻', '元辰', '丧门']
};

// 常见重要神煞完整列表（基于古籍和现代命理）
const COMMON_IMPORTANT_SHENSHA = {
  // 第一级：最重要的神煞（我们已有）
  level1: {
    auspicious: ['天乙贵人', '文昌贵人', '福星贵人', '天德', '月德', '月德合', '德秀贵人'],
    inauspicious: ['羊刃', '劫煞', '灾煞', '血刃', '元辰', '孤辰', '寡宿']
  },
  
  // 第二级：常用重要神煞
  level2: {
    auspicious: ['太极贵人', '国印贵人', '学堂', '词馆', '金舆', '禄神', '驿马', '华盖'],
    inauspicious: ['亡神', '劫煞', '空亡', '咸池', '红艳', '孤鸾煞', '阴差阳错', '十恶大败']
  },
  
  // 第三级：专业命理常用神煞
  level3: {
    auspicious: ['三奇贵人', '天医', '解神', '天赦', '红鸾', '天喜', '将星'],
    inauspicious: ['勾绞煞', '天罗地网', '飞刃', '伏吟', '反吟', '魁罡', '日德']
  },
  
  // 第四级：特殊情况神煞
  level4: {
    auspicious: ['月空', '日贵', '时贵', '拱贵', '夹贵'],
    inauspicious: ['六厄', '大耗', '小耗', '病符', '死符', '官符', '五鬼']
  }
};

// 用户补充的神煞类型（之前提到的）
const USER_MENTIONED = {
  monthZhi: ['天德', '天德合', '月德', '月德合'],
  yearZhi: ['将星', '驿马', '华盖', '劫煞', '灾煞', '桃花', '红鸾', '天喜', '孤辰', '寡宿'],
  dayGan: ['贵人', '文昌', '沐浴', '干禄', '羊刃', '红艳', '金舆']
};

// 分析函数
const ShenshaAnalysis = {
  // 统计当前覆盖情况
  analyzeCoverage: function() {
    const implemented = Object.values(CURRENT_IMPLEMENTED).flat();
    const level1All = Object.values(COMMON_IMPORTANT_SHENSHA.level1).flat();
    const level2All = Object.values(COMMON_IMPORTANT_SHENSHA.level2).flat();
    const userMentioned = Object.values(USER_MENTIONED).flat();
    
    console.log('=== 神煞系统完整度评估报告 ===');
    console.log('');
    
    console.log('📊 当前已实现神煞统计：');
    console.log(`• 总数：${implemented.length}个`);
    console.log(`• 贵人类：${CURRENT_IMPLEMENTED.guiren.length + CURRENT_IMPLEMENTED.tiande.length}个`);
    console.log(`• 凶煞类：${CURRENT_IMPLEMENTED.xiongsha.length}个`);
    console.log(`• 特殊类：${CURRENT_IMPLEMENTED.taohua.length}个`);
    console.log('');
    
    // 分析第一级覆盖情况
    console.log('🎯 第一级重要神煞覆盖分析：');
    const level1Missing = level1All.filter(s => !implemented.some(i => i.includes(s.replace('天德', '').replace('月德', ''))));
    console.log(`• 第一级总数：${level1All.length}个`);
    console.log(`• 已覆盖：${level1All.length - level1Missing.length}个`);
    console.log(`• 覆盖率：${((level1All.length - level1Missing.length) / level1All.length * 100).toFixed(1)}%`);
    if (level1Missing.length > 0) {
      console.log(`• 缺失：${level1Missing.join('、')}`);
    }
    console.log('');
    
    // 分析第二级覆盖情况
    console.log('📋 第二级常用神煞覆盖分析：');
    const level2Missing = level2All.filter(s => !implemented.some(i => i.includes(s)) && !userMentioned.some(u => u.includes(s)));
    console.log(`• 第二级总数：${level2All.length}个`);
    console.log(`• 已覆盖或提及：${level2All.length - level2Missing.length}个`);
    console.log(`• 覆盖率：${((level2All.length - level2Missing.length) / level2All.length * 100).toFixed(1)}%`);
    if (level2Missing.length > 0) {
      console.log(`• 缺失：${level2Missing.join('、')}`);
    }
    console.log('');
    
    return {
      implemented,
      level1Missing,
      level2Missing,
      coverage: {
        level1: ((level1All.length - level1Missing.length) / level1All.length * 100).toFixed(1),
        level2: ((level2All.length - level2Missing.length) / level2All.length * 100).toFixed(1)
      }
    };
  },
  
  // 推荐需要添加的神煞
  recommendAdditions: function() {
    console.log('💡 推荐添加的神煞优先级：');
    console.log('');
    
    console.log('🔥 高优先级（第一级重要神煞）：');
    console.log('1. 羊刃 - 最重要的凶煞之一，影响性格和运势');
    console.log('2. 天德 - 重要吉神，化解灾难能力强');
    console.log('3. 月德 - 重要吉神，与月德合配套');
    console.log('4. 劫煞 - 重要凶煞，影响财运和人际关系');
    console.log('5. 孤辰 - 重要凶煞，影响婚姻和人际关系');
    console.log('');
    
    console.log('🔶 中优先级（第二级常用神煞）：');
    console.log('1. 太极贵人 - 重要贵人星，主聪明智慧');
    console.log('2. 驿马 - 主动变迁，现代社会很重要');
    console.log('3. 华盖 - 主艺术才华，孤高清雅');
    console.log('4. 空亡 - 重要凶煞，主虚耗不实');
    console.log('5. 禄神 - 重要吉神，主财禄丰厚');
    console.log('6. 亡神 - 重要凶煞，主意外灾祸');
    console.log('');
    
    console.log('🔷 低优先级（专业命理神煞）：');
    console.log('1. 国印贵人 - 主权威地位');
    console.log('2. 学堂词馆 - 主文化学识');
    console.log('3. 三奇贵人 - 主特殊才能');
    console.log('4. 红鸾天喜 - 主婚姻感情');
    console.log('5. 勾绞煞 - 主是非纠纷');
    console.log('');
  },
  
  // 评估系统完整度
  evaluateCompleteness: function() {
    const analysis = this.analyzeCoverage();
    
    console.log('🏆 神煞系统完整度评估：');
    console.log('');
    
    if (parseFloat(analysis.coverage.level1) >= 85) {
      console.log('✅ 第一级重要神煞覆盖率优秀（≥85%）');
      console.log('✅ 已具备专业八字分析的核心神煞体系');
      console.log('✅ 能够满足大部分用户的神煞分析需求');
    } else {
      console.log('⚠️ 第一级重要神煞覆盖率需要提升');
      console.log('📋 建议优先补充第一级缺失的神煞');
    }
    
    console.log('');
    
    if (parseFloat(analysis.coverage.level2) >= 70) {
      console.log('✅ 第二级常用神煞覆盖率良好（≥70%）');
      console.log('✅ 系统已具备较高的专业性和完整性');
    } else {
      console.log('📋 第二级常用神煞还有提升空间');
      console.log('💡 可以根据用户需求逐步添加');
    }
    
    console.log('');
    console.log('🎯 总体评估结论：');
    
    if (parseFloat(analysis.coverage.level1) >= 85 && parseFloat(analysis.coverage.level2) >= 70) {
      console.log('🌟 当前神煞系统已达到专业级水准！');
      console.log('🌟 覆盖了绝大部分重要和常用神煞');
      console.log('🌟 可以满足专业八字分析的需求');
      console.log('');
      console.log('💡 建议策略：');
      console.log('• 当前系统已经非常完整，可以投入使用');
      console.log('• 后续可以根据用户反馈和实际需求逐步添加');
      console.log('• 重点关注系统稳定性和计算准确性');
      console.log('• 考虑添加神煞的详细解释和应用指导');
    } else {
      console.log('📋 建议继续完善重要神煞的覆盖');
      console.log('🎯 优先添加第一级重要神煞');
      console.log('📈 逐步提升系统的专业性和完整性');
    }
    
    return analysis;
  }
};

// 执行分析
ShenshaAnalysis.evaluateCompleteness();
ShenshaAnalysis.recommendAdditions();
