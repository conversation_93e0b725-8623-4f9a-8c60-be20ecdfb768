#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星运长生系统
实现十二长生状态的详细显示和分析，对标"问真八字"的星运功能
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ChangshengState(Enum):
    """十二长生状态枚举"""
    CHANGSHENG = "长生"
    MUYU = "沐浴"
    GUANDAI = "冠带"
    LINGUAN = "临官"
    DIWANG = "帝旺"
    SHUAI = "衰"
    BING = "病"
    SI = "死"
    MU = "墓"
    JUE = "绝"
    TAI = "胎"
    YANG = "养"

@dataclass
class ChangshengInfo:
    """长生状态信息"""
    pillar_name: str                # 柱位名称
    gan: str                       # 天干
    zhi: str                       # 地支
    changsheng_state: ChangshengState  # 长生状态
    state_strength: str            # 状态强度
    life_energy: float             # 生命能量值
    development_phase: str         # 发展阶段
    fortune_tendency: str          # 运势倾向
    suitable_activities: List[str] # 适宜活动
    avoid_activities: List[str]    # 避免活动
    energy_characteristics: str    # 能量特征
    timing_advice: str             # 时机建议

@dataclass
class ComprehensiveChangshengResult:
    """完整长生分析结果"""
    pillar_changsheng: List[ChangshengInfo]  # 各柱长生状态
    energy_distribution: Dict[str, float]    # 能量分布
    life_cycle_pattern: str                  # 生命周期模式
    overall_energy_level: str               # 整体能量水平
    development_rhythm: str                  # 发展节奏
    critical_periods: List[str]             # 关键时期
    energy_flow_analysis: Dict              # 能量流动分析
    strategic_recommendations: List[str]     # 策略建议

class ComprehensiveChangshengAnalyzer:
    """完整的星运长生分析系统"""
    
    def __init__(self):
        """初始化长生分析器"""
        # 十二长生序列（阳干）- 权威修正版，完全对标"问真八字"
        self.yang_changsheng_sequence = {
            "甲": ["亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌"],  # 午=绝
            "丙": ["寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑"],
            "戊": ["寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑"],
            "庚": ["巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑", "寅", "卯", "辰"],
            "壬": ["申", "酉", "戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未"]  # 戌=衰
        }

        # 十二长生序列（阴干）- 权威修正版，完全对标"问真八字"
        self.yin_changsheng_sequence = {
            "乙": ["午", "巳", "辰", "卯", "寅", "丑", "子", "亥", "戌", "酉", "申", "未"],
            "丁": ["酉", "申", "未", "午", "巳", "辰", "卯", "寅", "丑", "子", "亥", "戌"],
            "己": ["酉", "申", "未", "午", "巳", "辰", "卯", "寅", "丑", "子", "亥", "戌"],
            "辛": ["子", "亥", "戌", "酉", "申", "未", "午", "巳", "辰", "卯", "寅", "丑"],  # 丑=冠带
            "癸": ["卯", "寅", "丑", "子", "亥", "戌", "酉", "午", "未", "申", "巳", "辰"]  # 丑=养,午=死,戌=冠带
        }
        
        # 长生状态名称
        self.changsheng_names = [
            ChangshengState.CHANGSHENG, ChangshengState.MUYU, ChangshengState.GUANDAI,
            ChangshengState.LINGUAN, ChangshengState.DIWANG, ChangshengState.SHUAI,
            ChangshengState.BING, ChangshengState.SI, ChangshengState.MU,
            ChangshengState.JUE, ChangshengState.TAI, ChangshengState.YANG
        ]
        
        # 长生状态详细信息数据库
        self.changsheng_database = self._init_changsheng_database()
    
    def _init_changsheng_database(self) -> Dict:
        """初始化长生状态数据库"""
        return {
            ChangshengState.CHANGSHENG: {
                "strength": "极强",
                "energy": 1.0,
                "phase": "新生期",
                "tendency": "蓬勃向上",
                "suitable": ["开始新项目", "学习新技能", "建立基础", "积极进取"],
                "avoid": ["急于求成", "过度消耗", "忽视基础"],
                "characteristics": "生机勃勃，充满希望和潜力",
                "timing": "最佳起步时机，宜积极行动"
            },
            ChangshengState.MUYU: {
                "strength": "弱",
                "energy": 0.3,
                "phase": "净化期",
                "tendency": "不稳定",
                "suitable": ["学习充电", "调整方向", "接受指导", "保持谦逊"],
                "avoid": ["独断专行", "过于张扬", "重大决策"],
                "characteristics": "容易受外界影响，需要保护和指导",
                "timing": "调整期，宜低调行事"
            },
            ChangshengState.GUANDAI: {
                "strength": "中",
                "energy": 0.6,
                "phase": "成长期",
                "tendency": "稳步上升",
                "suitable": ["建立规范", "学习技能", "承担责任", "稳步发展"],
                "avoid": ["急躁冒进", "违反规则", "逃避责任"],
                "characteristics": "开始承担责任，建立自己的规范",
                "timing": "成长期，宜按部就班"
            },
            ChangshengState.LINGUAN: {
                "strength": "强",
                "energy": 0.9,
                "phase": "发展期",
                "tendency": "积极向上",
                "suitable": ["展现才能", "承担重任", "积极进取", "建立威信"],
                "avoid": ["骄傲自满", "忽视他人", "过度扩张"],
                "characteristics": "能力强盛，有权威和影响力",
                "timing": "发展期，宜积极进取"
            },
            ChangshengState.DIWANG: {
                "strength": "极强",
                "energy": 1.0,
                "phase": "巅峰期",
                "tendency": "达到顶峰",
                "suitable": ["发挥优势", "创造辉煌", "领导他人", "实现理想"],
                "avoid": ["过度自信", "忽视危机", "不思进取"],
                "characteristics": "达到人生巅峰，实力和影响力最强",
                "timing": "巅峰期，宜把握机遇"
            },
            ChangshengState.SHUAI: {
                "strength": "中",
                "energy": 0.5,
                "phase": "衰退期",
                "tendency": "开始下降",
                "suitable": ["调整策略", "保持状态", "传承经验", "稳中求进"],
                "avoid": ["固执己见", "拒绝变化", "过度消耗"],
                "characteristics": "力量开始减弱，需要调整和适应",
                "timing": "调整期，宜稳中求进"
            },
            ChangshengState.BING: {
                "strength": "弱",
                "energy": 0.2,
                "phase": "困难期",
                "tendency": "遇到阻碍",
                "suitable": ["寻求帮助", "调养身心", "反思总结", "耐心等待"],
                "avoid": ["强行推进", "过度劳累", "孤军奋战"],
                "characteristics": "遇到困难和阻碍，需要治疗和调养",
                "timing": "困难期，宜调养生息"
            },
            ChangshengState.SI: {
                "strength": "极弱",
                "energy": 0.1,
                "phase": "低谷期",
                "tendency": "停滞不前",
                "suitable": ["接受现实", "寻求转机", "内在修养", "等待时机"],
                "avoid": ["强求结果", "消极绝望", "盲目行动"],
                "characteristics": "生机全无，处于最低谷状态",
                "timing": "低谷期，宜等待转机"
            },
            ChangshengState.MU: {
                "strength": "弱",
                "energy": 0.3,
                "phase": "蛰伏期",
                "tendency": "深藏不露",
                "suitable": ["积蓄力量", "内在修养", "等待时机", "深度学习"],
                "avoid": ["急于表现", "浪费资源", "盲目行动"],
                "characteristics": "深藏不露，在暗中积蓄力量",
                "timing": "蛰伏期，宜积蓄力量"
            },
            ChangshengState.JUE: {
                "strength": "极弱",
                "energy": 0.1,
                "phase": "绝境期",
                "tendency": "断绝重生",
                "suitable": ["放下包袱", "重新开始", "寻找新方向", "破而后立"],
                "avoid": ["执着过去", "消极等待", "自暴自弃"],
                "characteristics": "彻底断绝，但也是重新开始的契机",
                "timing": "绝境期，宜破而后立"
            },
            ChangshengState.TAI: {
                "strength": "弱",
                "energy": 0.4,
                "phase": "孕育期",
                "tendency": "孕育新生",
                "suitable": ["耐心等待", "积累能量", "制定计划", "准备行动"],
                "avoid": ["急于求成", "过早行动", "缺乏耐心"],
                "characteristics": "在孕育新的生机，充满希望",
                "timing": "孕育期，宜耐心准备"
            },
            ChangshengState.YANG: {
                "strength": "中",
                "energy": 0.7,
                "phase": "养育期",
                "tendency": "逐渐成长",
                "suitable": ["稳步发展", "打好基础", "接受滋养", "循序渐进"],
                "avoid": ["急躁冒进", "忽视基础", "过度消耗"],
                "characteristics": "受到滋养，逐渐成长壮大",
                "timing": "养育期，宜稳步发展"
            }
        }
    
    def analyze_changsheng(self, four_pillars: List[Tuple[str, str]]) -> ComprehensiveChangshengResult:
        """分析四柱长生状态"""
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        pillar_changsheng = []
        energy_distribution = {}
        
        # 分析各柱长生状态
        for i, (gan, zhi) in enumerate(four_pillars):
            changsheng_info = self._analyze_single_pillar_changsheng(
                pillar_names[i], gan, zhi
            )
            pillar_changsheng.append(changsheng_info)
            
            # 累计能量分布
            state_name = changsheng_info.changsheng_state.value
            energy_distribution[state_name] = (
                energy_distribution.get(state_name, 0) + changsheng_info.life_energy
            )
        
        # 生命周期模式分析
        life_cycle_pattern = self._analyze_life_cycle_pattern(pillar_changsheng)
        
        # 整体能量水平
        overall_energy_level = self._calculate_overall_energy_level(pillar_changsheng)
        
        # 发展节奏
        development_rhythm = self._analyze_development_rhythm(pillar_changsheng)
        
        # 关键时期识别
        critical_periods = self._identify_critical_periods(pillar_changsheng)
        
        # 能量流动分析
        energy_flow_analysis = self._analyze_energy_flow(pillar_changsheng)
        
        # 策略建议
        strategic_recommendations = self._generate_strategic_recommendations(
            pillar_changsheng, life_cycle_pattern, overall_energy_level
        )
        
        return ComprehensiveChangshengResult(
            pillar_changsheng=pillar_changsheng,
            energy_distribution=energy_distribution,
            life_cycle_pattern=life_cycle_pattern,
            overall_energy_level=overall_energy_level,
            development_rhythm=development_rhythm,
            critical_periods=critical_periods,
            energy_flow_analysis=energy_flow_analysis,
            strategic_recommendations=strategic_recommendations
        )
    
    def _analyze_single_pillar_changsheng(self, pillar_name: str, gan: str, zhi: str) -> ChangshengInfo:
        """分析单个柱位的长生状态"""
        # 确定长生状态
        changsheng_state = self._get_changsheng_state(gan, zhi)
        
        # 获取状态信息
        state_data = self.changsheng_database[changsheng_state]
        
        # 计算状态强度
        state_strength = state_data["strength"]
        
        # 生命能量值
        life_energy = state_data["energy"]
        
        return ChangshengInfo(
            pillar_name=pillar_name,
            gan=gan,
            zhi=zhi,
            changsheng_state=changsheng_state,
            state_strength=state_strength,
            life_energy=life_energy,
            development_phase=state_data["phase"],
            fortune_tendency=state_data["tendency"],
            suitable_activities=state_data["suitable"],
            avoid_activities=state_data["avoid"],
            energy_characteristics=state_data["characteristics"],
            timing_advice=state_data["timing"]
        )
    
    def _get_changsheng_state(self, gan: str, zhi: str) -> ChangshengState:
        """获取天干在地支的长生状态"""
        # 阳干
        if gan in self.yang_changsheng_sequence:
            sequence = self.yang_changsheng_sequence[gan]
            if zhi in sequence:
                index = sequence.index(zhi)
                return self.changsheng_names[index]
        
        # 阴干
        if gan in self.yin_changsheng_sequence:
            sequence = self.yin_changsheng_sequence[gan]
            if zhi in sequence:
                index = sequence.index(zhi)
                return self.changsheng_names[index]
        
        # 默认返回长生
        return ChangshengState.CHANGSHENG
    
    def _analyze_life_cycle_pattern(self, pillar_changsheng: List[ChangshengInfo]) -> str:
        """分析生命周期模式"""
        # 统计各阶段的数量
        phase_count = {}
        for info in pillar_changsheng:
            phase = info.development_phase
            phase_count[phase] = phase_count.get(phase, 0) + 1
        
        # 分析模式
        if len(phase_count) == 1:
            dominant_phase = list(phase_count.keys())[0]
            return f"单一{dominant_phase}模式 - 发展阶段集中"
        elif "巅峰期" in phase_count and phase_count["巅峰期"] >= 2:
            return "巅峰主导模式 - 人生多个高峰期"
        elif "新生期" in phase_count and "发展期" in phase_count:
            return "成长发展模式 - 持续向上发展"
        elif "困难期" in phase_count or "低谷期" in phase_count:
            return "波折起伏模式 - 人生多有起伏"
        else:
            return "平衡发展模式 - 各阶段相对均衡"
    
    def _calculate_overall_energy_level(self, pillar_changsheng: List[ChangshengInfo]) -> str:
        """计算整体能量水平"""
        total_energy = sum(info.life_energy for info in pillar_changsheng)
        avg_energy = total_energy / len(pillar_changsheng)
        
        if avg_energy >= 0.8:
            return "极强 - 生命力旺盛，发展潜力巨大"
        elif avg_energy >= 0.6:
            return "强 - 能量充沛，发展势头良好"
        elif avg_energy >= 0.4:
            return "中 - 能量平稳，需要适当调节"
        elif avg_energy >= 0.2:
            return "弱 - 能量不足，需要积极调养"
        else:
            return "极弱 - 能量匮乏，需要重点关注"
    
    def _analyze_development_rhythm(self, pillar_changsheng: List[ChangshengInfo]) -> str:
        """分析发展节奏"""
        energy_sequence = [info.life_energy for info in pillar_changsheng]
        
        # 分析能量变化趋势
        if len(energy_sequence) < 2:
            return "稳定节奏"
        
        # 计算变化趋势
        increasing = 0
        decreasing = 0
        
        for i in range(1, len(energy_sequence)):
            if energy_sequence[i] > energy_sequence[i-1]:
                increasing += 1
            elif energy_sequence[i] < energy_sequence[i-1]:
                decreasing += 1
        
        if increasing > decreasing:
            return "上升节奏 - 能量逐步提升，发展向好"
        elif decreasing > increasing:
            return "下降节奏 - 能量逐步减弱，需要调整"
        else:
            return "波动节奏 - 能量起伏变化，需要把握节奏"
    
    def _identify_critical_periods(self, pillar_changsheng: List[ChangshengInfo]) -> List[str]:
        """识别关键时期"""
        critical_periods = []
        
        for info in pillar_changsheng:
            if info.changsheng_state in [ChangshengState.DIWANG, ChangshengState.CHANGSHENG]:
                critical_periods.append(f"{info.pillar_name}({info.changsheng_state.value}) - 重要发展机遇期")
            elif info.changsheng_state in [ChangshengState.SI, ChangshengState.JUE]:
                critical_periods.append(f"{info.pillar_name}({info.changsheng_state.value}) - 重要转折调整期")
            elif info.changsheng_state == ChangshengState.LINGUAN:
                critical_periods.append(f"{info.pillar_name}({info.changsheng_state.value}) - 重要发展期")
        
        return critical_periods
    
    def _analyze_energy_flow(self, pillar_changsheng: List[ChangshengInfo]) -> Dict:
        """分析能量流动"""
        energy_flow = {
            "能量起点": "",
            "能量终点": "",
            "能量峰值": "",
            "能量低谷": "",
            "流动特征": ""
        }
        
        if pillar_changsheng:
            # 能量起点和终点
            energy_flow["能量起点"] = f"{pillar_changsheng[0].pillar_name}({pillar_changsheng[0].changsheng_state.value})"
            energy_flow["能量终点"] = f"{pillar_changsheng[-1].pillar_name}({pillar_changsheng[-1].changsheng_state.value})"
            
            # 找到能量峰值和低谷
            max_energy_info = max(pillar_changsheng, key=lambda x: x.life_energy)
            min_energy_info = min(pillar_changsheng, key=lambda x: x.life_energy)
            
            energy_flow["能量峰值"] = f"{max_energy_info.pillar_name}({max_energy_info.changsheng_state.value})"
            energy_flow["能量低谷"] = f"{min_energy_info.pillar_name}({min_energy_info.changsheng_state.value})"
            
            # 流动特征
            start_energy = pillar_changsheng[0].life_energy
            end_energy = pillar_changsheng[-1].life_energy
            
            if end_energy > start_energy:
                energy_flow["流动特征"] = "整体上升趋势，发展向好"
            elif end_energy < start_energy:
                energy_flow["流动特征"] = "整体下降趋势，需要调整"
            else:
                energy_flow["流动特征"] = "整体平稳，发展稳定"
        
        return energy_flow
    
    def _generate_strategic_recommendations(self, pillar_changsheng: List[ChangshengInfo],
                                          life_cycle_pattern: str, overall_energy_level: str) -> List[str]:
        """生成策略建议"""
        recommendations = []
        
        # 基于整体能量水平的建议
        if "极强" in overall_energy_level:
            recommendations.append("整体能量强盛，宜积极进取，把握机遇创造辉煌")
        elif "强" in overall_energy_level:
            recommendations.append("能量充沛，宜稳步发展，持续提升实力")
        elif "弱" in overall_energy_level:
            recommendations.append("能量不足，宜调养生息，积蓄力量等待时机")
        
        # 基于生命周期模式的建议
        if "巅峰主导" in life_cycle_pattern:
            recommendations.append("多个巅峰期，宜把握每个高峰机会，创造最大价值")
        elif "成长发展" in life_cycle_pattern:
            recommendations.append("持续成长模式，宜循序渐进，稳步提升")
        elif "波折起伏" in life_cycle_pattern:
            recommendations.append("人生多起伏，宜增强韧性，在困难中寻找机遇")
        
        # 基于关键时期的建议
        strong_periods = [info for info in pillar_changsheng 
                         if info.changsheng_state in [ChangshengState.DIWANG, ChangshengState.LINGUAN]]
        if strong_periods:
            recommendations.append(f"重点把握{strong_periods[0].pillar_name}的强势期，全力发展")
        
        # 基于弱势期的建议
        weak_periods = [info for info in pillar_changsheng 
                       if info.changsheng_state in [ChangshengState.SI, ChangshengState.BING]]
        if weak_periods:
            recommendations.append(f"注意{weak_periods[0].pillar_name}的困难期，提前做好准备")
        
        return recommendations[:5]  # 返回前5个建议


# 测试和使用示例
def main():
    """测试星运长生系统"""
    print("🌟 星运长生系统测试")
    print("=" * 50)
    
    # 创建长生分析器
    analyzer = ComprehensiveChangshengAnalyzer()
    
    # 测试数据 - 四柱
    test_four_pillars = [
        ("乙", "巳"),  # 年柱
        ("癸", "未"),  # 月柱
        ("丁", "丑"),  # 日柱
        ("丁", "未")   # 时柱
    ]
    
    # 进行长生分析
    result = analyzer.analyze_changsheng(test_four_pillars)
    
    # 显示各柱长生状态
    print("🏛️ 各柱长生状态:")
    for info in result.pillar_changsheng:
        print(f"\n   {info.pillar_name} {info.gan}{info.zhi}:")
        print(f"      长生状态: {info.changsheng_state.value}")
        print(f"      状态强度: {info.state_strength}")
        print(f"      生命能量: {info.life_energy:.1f}")
        print(f"      发展阶段: {info.development_phase}")
        print(f"      运势倾向: {info.fortune_tendency}")
        print(f"      能量特征: {info.energy_characteristics}")
        print(f"      时机建议: {info.timing_advice}")
        print(f"      适宜活动: {', '.join(info.suitable_activities[:3])}")
        if info.avoid_activities:
            print(f"      避免活动: {', '.join(info.avoid_activities[:2])}")
    
    # 显示能量分布
    print("\n📊 能量分布统计:")
    for state, energy in result.energy_distribution.items():
        print(f"   {state}: {energy:.1f}")
    
    # 显示整体分析
    print(f"\n🎭 生命周期模式: {result.life_cycle_pattern}")
    print(f"⚡ 整体能量水平: {result.overall_energy_level}")
    print(f"🎵 发展节奏: {result.development_rhythm}")
    
    # 显示关键时期
    if result.critical_periods:
        print("\n⏰ 关键时期:")
        for period in result.critical_periods:
            print(f"   • {period}")
    
    # 显示能量流动分析
    print("\n🌊 能量流动分析:")
    for key, value in result.energy_flow_analysis.items():
        print(f"   {key}: {value}")
    
    # 显示策略建议
    print("\n💡 策略建议:")
    for i, recommendation in enumerate(result.strategic_recommendations, 1):
        print(f"   {i}. {recommendation}")
    
    print("\n✅ 星运长生系统测试完成！")


if __name__ == "__main__":
    main()
