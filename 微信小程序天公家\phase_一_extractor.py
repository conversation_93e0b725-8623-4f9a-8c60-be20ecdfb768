#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段专项规则提取器
从38条扩展到88条规则 (新增50条)
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class Phase一Extractor:
    def __init__(self):
        self.rule_id_counter = 1
        self.target_tasks = [
        {
                "任务": "五行力量计算规则",
                "来源": "千里命稿强弱篇、五行精纪",
                "目标数量": 14,
                "关键词": [
                        "五行生克",
                        "旺相休囚死",
                        "月令",
                        "藏干"
                ],
                "提取重点": "五行力量计算的具体方法和判断标准"
        },
        {
                "任务": "五行平衡指数规则",
                "来源": "千里命稿调候篇、穷通宝鉴",
                "目标数量": 9,
                "关键词": [
                        "调候",
                        "平衡",
                        "中和",
                        "偏枯"
                ],
                "提取重点": "五行平衡状态的量化标准"
        },
        {
                "任务": "十神关系规则",
                "来源": "千里命稿十神篇、渊海子平",
                "目标数量": 15,
                "关键词": [
                        "十神",
                        "生克",
                        "关系",
                        "作用"
                ],
                "提取重点": "十神之间的相互作用关系"
        },
        {
                "任务": "日柱互动分析规则",
                "来源": "千里命稿日主篇",
                "目标数量": 12,
                "关键词": [
                        "日主",
                        "日柱",
                        "互动",
                        "影响"
                ],
                "提取重点": "日柱与其他柱位的互动关系"
        }
]
        
    def extract_phase_rules(self, books_dir: str = "古籍资料") -> Dict:
        """提取第一阶段所需规则"""
        all_rules = []
        
        for task in self.target_tasks:
            print(f"正在提取: {task['任务']}")
            task_rules = self.extract_task_rules(task, books_dir)
            all_rules.extend(task_rules)
            print(f"提取了 {len(task_rules)} 条规则")
        
        metadata = {
            "phase": "第一阶段",
            "export_date": datetime.now().isoformat(),
            "total_rules": len(all_rules),
            "target_count": 50,
            "completion_rate": f"{len(all_rules)/sum(task['目标数量'] for task in self.target_tasks)*100:.1f}%"
        }
        
        return {
            "metadata": metadata,
            "rules": all_rules
        }
    
    def extract_task_rules(self, task: Dict, books_dir: str) -> List[Dict]:
        """提取单个任务的规则"""
        rules = []
        keywords = task["关键词"]
        target_count = task["目标数量"]
        
        # 根据来源确定要处理的文件
        source_files = self.get_source_files(task["来源"], books_dir)
        
        for file_path in source_files:
            if not os.path.exists(file_path):
                continue
                
            content = self.load_file_content(file_path)
            file_rules = self.extract_rules_from_content(
                content, keywords, task, file_path
            )
            rules.extend(file_rules)
            
            if len(rules) >= target_count:
                break
        
        return rules[:target_count]  # 限制数量
    
    def get_source_files(self, sources: str, books_dir: str) -> List[str]:
        """根据来源获取文件路径"""
        files = []
        source_mapping = {
            "千里命稿": "千里命稿.txt",
            "三命通会": "《三命通会》完整白话版  .pdf",
            "五行精纪": "五行精纪.docx",
            "渊海子平": "渊海子平.docx",
            "滴天髓": "滴天髓.txt",
            "穷通宝鉴": "穷通宝鉴.txt"
        }
        
        for source in sources.split("、"):
            source = source.strip()
            for key, filename in source_mapping.items():
                if key in source:
                    files.append(os.path.join(books_dir, filename))
        
        return files
    
    def load_file_content(self, file_path: str) -> str:
        """加载文件内容"""
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_path.endswith('.docx'):
                # 需要docx库
                try:
                    from docx import Document
                    doc = Document(file_path)
                    return "\n".join([p.text for p in doc.paragraphs])
                except ImportError:
                    print(f"跳过 {file_path} - 缺少docx库")
                    return ""
            else:
                return ""
        except Exception as e:
            print(f"加载文件失败 {file_path}: {e}")
            return ""
    
    def extract_rules_from_content(self, content: str, keywords: List[str], 
                                 task: Dict, file_path: str) -> List[Dict]:
        """从内容中提取规则"""
        rules = []
        
        # 按关键词搜索相关段落
        for keyword in keywords:
            pattern = rf'[^。]*{keyword}[^。]*。'
            matches = re.findall(pattern, content)
            
            for match in matches:
                if len(match.strip()) > 20:  # 确保有足够内容
                    rule = {
                        "rule_id": f"{task['任务']}_{self.rule_id_counter:03d}",
                        "pattern_name": task["任务"],
                        "category": self.get_category_from_task(task["任务"]),
                        "book_source": os.path.basename(file_path).split('.')[0],
                        "original_text": self.clean_text(match),
                        "interpretations": task["提取重点"],
                        "confidence": 0.92,
                        "conditions": f"与{keyword}相关的条件",
                        "advanced_cleaned": True,
                        "advanced_cleaned_at": datetime.now().isoformat(),
                        "extraction_phase": "第一阶段",
                        "target_keyword": keyword
                    }
                    rules.append(rule)
                    self.rule_id_counter += 1
        
        return rules
    
    def get_category_from_task(self, task_name: str) -> str:
        """根据任务名称确定分类"""
        category_mapping = {
            "五行": "强弱判断",
            "十神": "用神理论", 
            "格局": "正格",
            "神煞": "神煞格局",
            "匹配": "六亲关系",
            "运程": "大运流年",
            "调候": "调候格局"
        }
        
        for key, category in category_mapping.items():
            if key in task_name:
                return category
        
        return "综合理论"
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        text = re.sub(r'\s+', ' ', text)
        text = text.replace('，', '，').replace('。', '。')
        return text.strip()
    
    def save_rules(self, data: Dict, filename: str):
        """保存规则到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"{data['metadata']['phase']}规则已保存到: {filename}")

def main():
    """主函数"""
    extractor = Phase一Extractor()
    
    # 提取规则
    data = extractor.extract_phase_rules()
    
    # 保存结果
    filename = f"classical_rules_phase_一.json"
    extractor.save_rules(data, filename)

if __name__ == "__main__":
    main()
