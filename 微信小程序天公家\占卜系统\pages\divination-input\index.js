// pages/divination-input/index.js
// 🔮 李淳风六壬时课专业系统 - 独立占卜页面
// 用途：专业的李淳风六壬时课占卜工具
// 用户：专业用户，纯占卜功能需求
// 部署：独立服务，可单独部署
// 维护：算法研发分支，专业功能优化

const DivinationCalculator = require('../../utils/divination_calculator');
const NavigationColor = require('../../utils/navigation_color');
// ✅ 天公师兄系统使用权威农历转换器
const AuthoritativeLunarConverter = require('../../utils/authoritative_lunar_data');
const TrueSolarTimeEngine = require('../../utils/true_solar_time_engine');

Page({
  data: {
    // 主题相关
    themeClass: 'tarot-theme',
    
    // 占卜方式
    selectedMethod: 'time', // 'time' 或 'number'
    
    // 问题相关
    questionTypes: [
      { label: '失物寻找', value: 'lost' },
      { label: '求财问事', value: 'wealth' },
      { label: '出行安全', value: 'travel' },
      { label: '感情问题', value: 'love' },
      { label: '学习考试', value: 'study' },
      { label: '工作事业', value: 'career' },
      { label: '健康状况', value: 'health' },
      { label: '天气预测', value: 'weather' },
      { label: '疾病医疗', value: 'disease' },
      { label: '饮食宜忌', value: 'food' },
      { label: '其他事务', value: 'other' }
    ],
    selectedType: '',
    questionText: '',
    
    // 数字占卜
    numbers: ['', '', ''],
    
    // 事件发生时间占卜
    eventDate: '',           // 事件发生日期（阳历）
    eventTime: '',           // 事件发生时间
    eventDateTime: null,     // 完整的事件发生时间对象
    eventLunarTime: '',      // 事件农历时间
    eventShichen: '',        // 事件发生时辰
    eventTrueSolarTime: '',  // 事件真太阳时
    locationInfo: '',        // 定位信息
    longitude: 116.4074,     // 经度（默认北京）
    latitude: 39.9042,       // 纬度（默认北京）
    fullTimeInfo: null,      // 完整时间信息
    
    // 状态控制
    canStartDivination: false,
    isCalculating: false,

    // API增强功能
    useApiEnhancement: false
  },

  onLoad(options) {
    console.log('占卜信息收集页面加载', options);

    // 测试新增问题类型功能
    this.testNewQuestionTypes();

    // 端到端功能测试
    this.testEndToEndFlow();

    // 测试万事皆可问功能
    this.testUniversalQuestions();

    // 处理__route__未定义问题
    try {
      if (typeof __route__ === 'undefined' && typeof getApp()._fixRouteIssue === 'function') {
        getApp()._fixRouteIssue('pages/divination-input/index');
        console.log('已尝试修复路由问题');
      } else if (typeof __route__ === 'undefined') {
        console.log('__route__未定义，页面路径手动设置为:"pages/divination-input/index"');
      }
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }

    // 延迟初始化，避免jsbridge调用过早
    setTimeout(() => {
      this.initializePage();
    }, 100);
  },

  /**
   * 初始化页面（延迟执行）
   */
  initializePage() {
    console.log('开始初始化占卜页面');

    // 显示页面加载状态
    wx.showLoading({
      title: '正在准备占卜环境...',
      mask: true
    });

    try {
      // 设置导航栏颜色
      NavigationColor.setNavigationBarColorByRole('tarot');

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '起盘问卦'
      });

      // 获取位置信息（用于真太阳时计算）
      this.getLocationInfo();

      // 初始化默认事件时间为当前时间
      this.initDefaultEventTime();

      console.log('页面初始化完成');

    } catch (error) {
      console.error('页面初始化失败:', error);
    }

    // 确保loading状态被隐藏
    setTimeout(() => {
      wx.hideLoading();
      console.log('隐藏loading状态');

      // 初始化完成后检查占卜状态
      this.checkCanStartDivination();
    }, 1500);
  },

  onShow() {
    console.log('占卜信息收集页面显示');
    // 检查是否可以开始占卜
    this.checkCanStartDivination();
  },

  /**
   * 初始化默认事件时间
   */
  initDefaultEventTime() {
    const now = new Date();

    // 设置默认的事件发生时间为当前时间
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    this.setData({
      eventDate: `${year}-${month}-${day}`,
      eventTime: `${hour}:${minute}`
    });

    // 计算事件时间信息
    this.calculateEventTimeInfo();
  },

  /**
   * 计算事件时间信息
   */
  calculateEventTimeInfo() {
    const { eventDate, eventTime, longitude, latitude } = this.data;

    if (!eventDate || !eventTime) {
      return;
    }

    try {
      // 构建完整的事件发生时间
      const eventDateTime = new Date(`${eventDate} ${eventTime}`);

      console.log('🕐 开始计算事件时间信息:', {
        eventDate,
        eventTime,
        eventDateTime: eventDateTime.toString(),
        longitude,
        latitude
      });

      // 使用AuthoritativeLunarConverter进行农历转换
      const lunarInfo = AuthoritativeLunarConverter.solarToLunar(eventDateTime);

      // 使用TrueSolarTimeCorrector计算真太阳时
      const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
      const corrector = new TrueSolarTimeCorrector();
      const trueSolarResult = corrector.calculateTrueSolarTime(eventDateTime, longitude);

      // 从真太阳时计算时辰
      const trueSolarTime = new Date(trueSolarResult.result.trueSolarTime);
      const shichen = this.getShichen(trueSolarTime);

      // 格式化显示信息
      const formattedInfo = {
        solar: eventDateTime.toLocaleString('zh-CN'),
        lunar: `农历${lunarInfo.year}年${lunarInfo.monthName}${lunarInfo.dayName}`,
        trueSolar: trueSolarTime.toLocaleString('zh-CN'),
        shichen: shichen
      };

      this.setData({
        eventDateTime: formattedInfo.solar,
        eventLunarTime: formattedInfo.lunar,
        eventShichen: formattedInfo.shichen,
        eventTrueSolarTime: formattedInfo.trueSolar,
        fullTimeInfo: {
          solar: eventDateTime,
          lunar: lunarInfo,
          trueSolar: trueSolarTime,
          shichen: shichen,
          formatted: formattedInfo
        }
      });

      console.log('✅ 事件时间信息计算完成:', {
        eventDateTime: formattedInfo.solar,
        eventLunarTime: formattedInfo.lunar,
        eventShichen: formattedInfo.shichen,
        eventTrueSolarTime: formattedInfo.trueSolar
      });

      // 重新检查是否可以开始占卜
      this.checkCanStartDivination();

    } catch (error) {
      console.error('❌ 计算事件时间信息失败:', error);
      wx.showToast({
        title: '时间计算失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 事件日期选择
   */
  onEventDateChange(e) {
    console.log('事件日期选择:', e.detail.value);
    this.setData({
      eventDate: e.detail.value
    });
    this.calculateEventTimeInfo();
  },

  /**
   * 事件时间选择
   */
  onEventTimeChange(e) {
    console.log('事件时间选择:', e.detail.value);
    this.setData({
      eventTime: e.detail.value
    });
    this.calculateEventTimeInfo();
  },

  /**
   * 根据时间计算时辰
   */
  getShichen(date) {
    const hour = date.getHours();
    const shichenMap = {
      23: '子时', 0: '子时', 1: '丑时', 2: '丑时',
      3: '寅时', 4: '寅时', 5: '卯时', 6: '卯时',
      7: '辰时', 8: '辰时', 9: '巳时', 10: '巳时',
      11: '午时', 12: '午时', 13: '未时', 14: '未时',
      15: '申时', 16: '申时', 17: '酉时', 18: '酉时',
      19: '戌时', 20: '戌时', 21: '亥时', 22: '亥时'
    };
    return shichenMap[hour] || '未知';
  },

  // 旧的时间处理方法已移至 LunarCalendar 工具类

  /**
   * 获取位置信息
   */
  getLocationInfo() {
    console.log('开始获取位置信息 - GPS定位初始化');

    // 首先检查位置权限
    wx.getSetting({
      success: (res) => {
        console.log('当前权限设置:', res.authSetting);
        console.log('位置权限状态:', res.authSetting['scope.userLocation']);

        if (res.authSetting['scope.userLocation'] === false) {
          // 用户之前拒绝了位置权限
          console.log('用户之前拒绝了位置权限，显示引导');
          this.showLocationPermissionGuide();
          return;
        }

        // 尝试获取位置
        console.log('权限检查通过，开始请求位置');
        this.requestLocation();
      },
      fail: (err) => {
        console.log('获取权限设置失败:', err);
        console.log('直接尝试定位');
        this.requestLocation();
      }
    });
  },

  /**
   * 请求位置信息
   */
  requestLocation() {
    wx.getLocation({
      type: 'gcj02',
      altitude: false,
      success: (res) => {
        console.log('获取位置成功:', res);

        // 更新位置信息
        this.setData({
          locationInfo: `经度: ${res.longitude.toFixed(2)}°, 纬度: ${res.latitude.toFixed(2)}°`,
          longitude: res.longitude,
          latitude: res.latitude
        });

        // 使用真实位置重新计算事件时间信息
        this.calculateEventTimeInfo();

        // 显示定位成功提示
        wx.showToast({
          title: '定位成功，时间已校正',
          icon: 'success',
          duration: 2000
        });
      },
      fail: (err) => {
        console.log('获取位置失败:', err);
        this.handleLocationError(err);
      }
    });
  },

  /**
   * 处理定位错误
   */
  handleLocationError(err) {
    let errorMsg = '定位失败';
    let detailMsg = '';

    console.log('GPS定位错误详情:', err);

    // 根据错误类型提供具体说明
    if (err.errMsg.includes('auth deny')) {
      errorMsg = '位置权限被拒绝';
      detailMsg = '请在小程序设置中开启位置权限';
    } else if (err.errMsg.includes('location fail')) {
      errorMsg = 'GPS定位失败';
      detailMsg = '请检查手机GPS功能是否开启，或移动到信号较好的位置';
    } else if (err.errMsg.includes('requiredPrivateInfos')) {
      errorMsg = 'API配置错误';
      detailMsg = '位置API配置问题，请联系开发者';
      console.error('需要在app.json中配置requiredPrivateInfos字段');
    } else if (err.errMsg.includes('need to be declared')) {
      errorMsg = 'API声明错误';
      detailMsg = '位置API需要声明，正在尝试修复...';
      console.error('位置API未正确声明:', err.errMsg);
    }

    wx.showModal({
      title: errorMsg,
      content: `${detailMsg}\n\n将使用北京时间进行占卜计算。\n\n真太阳时校正对占卜精度有重要影响，建议开启位置权限以获得更准确的结果。`,
      confirmText: '继续占卜',
      cancelText: '重新定位',
      success: (res) => {
        if (res.cancel) {
          // 用户选择重新定位
          this.getLocationInfo();
        }
      }
    });

    // 使用北京时间作为默认值
    this.setData({
      locationInfo: '使用北京时间（建议开启定位获得更准确结果）',
      longitude: 116.4074,
      latitude: 39.9042
    });
  },

  /**
   * 显示位置权限引导
   */
  showLocationPermissionGuide() {
    wx.showModal({
      title: '需要位置权限',
      content: '李淳风六壬时课需要您的位置信息来计算真太阳时，确保占卜结果的准确性。\n\n请点击"去设置"开启位置权限。',
      confirmText: '去设置',
      cancelText: '使用北京时间',
      success: (res) => {
        if (res.confirm) {
          // 打开设置页面
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userLocation']) {
                // 用户开启了位置权限，重新获取位置
                this.requestLocation();
              }
            }
          });
        } else {
          // 用户选择使用北京时间
          this.setData({
            locationInfo: '使用北京时间（建议开启定位获得更准确结果）',
            longitude: 116.4074,
            latitude: 39.9042
          });
        }
      }
    });
  },

  /**
   * 选择占卜方式
   */
  selectMethod(e) {
    const method = e.currentTarget.dataset.method;
    console.log('选择占卜方式:', method);

    this.setData({
      selectedMethod: method
    });

    // 给用户反馈
    wx.showToast({
      title: method === 'time' ? '已选择时间占卜' : '已选择数字占卜',
      icon: 'success',
      duration: 1000
    });

    this.checkCanStartDivination();
  },

  /**
   * 选择问题类型
   */
  selectQuestionType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    });
    this.checkCanStartDivination();
  },

  /**
   * 问题输入
   */
  onQuestionInput(e) {
    this.setData({
      questionText: e.detail.value
    });
    this.checkCanStartDivination();
  },

  /**
   * 数字输入
   */
  onNumberInput(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const numbers = [...this.data.numbers];
    numbers[index] = value;
    
    this.setData({
      numbers
    });
    this.checkCanStartDivination();
  },

  /**
   * 检查是否可以开始占卜
   */
  checkCanStartDivination() {
    const { selectedMethod, questionText, numbers, eventDate, eventTime, eventDateTime } = this.data;

    let canStart = false;

    if (selectedMethod === 'time') {
      // 时间占卜：需要有问题描述和事件发生时间
      const hasQuestion = questionText.trim().length >= 5;
      const hasEventTime = eventDate && eventTime && eventDateTime;
      canStart = hasQuestion && hasEventTime;
      console.log('时间占卜验证:', {
        method: selectedMethod,
        questionLength: questionText.trim().length,
        hasQuestion: hasQuestion,
        hasEventTime: hasEventTime,
        eventDate: eventDate,
        eventTime: eventTime,
        canStart: canStart
      });
    } else if (selectedMethod === 'number') {
      // 数字占卜：需要有问题描述和三个数字
      const validNumbers = numbers.every(num => num && num >= 1 && num <= 9);
      canStart = questionText.trim().length >= 5 && validNumbers;
      console.log('数字占卜验证:', {
        method: selectedMethod,
        questionLength: questionText.trim().length,
        numbers: numbers,
        validNumbers: validNumbers,
        canStart: canStart
      });
    }

    console.log('占卜验证结果:', { selectedMethod, canStart });

    this.setData({
      canStartDivination: canStart
    });
  },

  /**
   * 切换API增强功能
   */
  toggleApiEnhancement() {
    const newState = !this.data.useApiEnhancement;
    this.setData({
      useApiEnhancement: newState
    });

    wx.showToast({
      title: newState ? '已启用AI增强' : '使用传统占卜',
      icon: 'success',
      duration: 1500
    });

    console.log('API增强模式:', newState ? '启用' : '关闭');
  },

  /**
   * 详细表单验证
   */
  validateFormDetailed() {
    const { selectedMethod, questionText, numbers } = this.data;

    // 验证问题输入
    if (!questionText.trim()) {
      wx.showModal({
        title: '温馨提示',
        content: '请输入您要占卜的问题。\n\n根据李淳风六壬时课传统：\n• 无事不占，一事一占\n• 问题要具体明确\n• 诚心为要',
        confirmText: '我知道了',
        showCancel: false
      });
      return false;
    }

    // 验证问题长度
    if (questionText.trim().length < 5) {
      wx.showToast({
        title: '问题描述过于简单，请详细描述',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 数字占卜特殊验证
    if (selectedMethod === 'number') {
      // 检查数字有效性
      for (let i = 0; i < numbers.length; i++) {
        const num = numbers[i];
        if (!num || num < 1 || num > 9) {
          wx.showToast({
            title: `请输入第${i + 1}个数字(1-9)`,
            icon: 'none'
          });
          return false;
        }
      }
    }

    return true;
  },

  /**
   * 开始占卜（集成API版本）
   */
  async startDivination() {
    // 详细表单验证
    if (!this.validateFormDetailed()) {
      return;
    }

    // 显示专业的占卜加载提示
    wx.showLoading({
      title: '天公师兄正在为您占卜...',
      mask: true
    });

    this.setData({
      isCalculating: true
    });

    try {
      // 智能识别问题类型（如果用户没有选择）
      let questionType = this.data.selectedType;
      if (!questionType) {
        questionType = this.detectQuestionType(this.data.questionText);
        console.log('智能识别问题类型:', questionType);
      }

      // 准备占卜数据
      const divinationData = {
        method: this.data.selectedMethod,
        questionType: questionType,
        questionText: this.data.questionText,
        timestamp: Date.now()
      };

      if (this.data.selectedMethod === 'time') {
        // 事件发生时间占卜数据
        divinationData.timeInfo = {
          eventDateTime: this.data.eventDateTime,
          eventLunarTime: this.data.eventLunarTime,
          eventShichen: this.data.eventShichen,
          eventTrueSolarTime: this.data.eventTrueSolarTime,
          longitude: this.data.longitude,
          latitude: this.data.latitude,
          fullTimeInfo: this.data.fullTimeInfo
        };
      } else {
        // 数字占卜数据
        divinationData.numbers = this.data.numbers.map(n => parseInt(n));
      }

      // 第一步：本地计算基础占卜结果
      console.log('🔮 开始本地占卜计算');
      const DivinationCalculator = require('../../utils/divination_calculator');

      // 使用本地计算器进行占卜
      const localResult = DivinationCalculator.calculate(divinationData);
      console.log('✅ 本地占卜结果:', localResult);

      // 将本地结果添加到占卜数据中
      divinationData.localResult = localResult;
      divinationData.god = localResult.god;
      divinationData.calculation = localResult.calculation;

      // 第二步：根据用户选择决定是否调用增强API
      let enhancedResult = null;
      if (this.data.useApiEnhancement) {
        try {
          console.log('🤖 尝试调用AI增强API');
          const api = require('../../utils/api');

          // 构建增强API请求数据
          const enhanceRequest = {
            god_name: localResult.god.name,
            question_type: questionType,
            question_text: this.data.questionText
          };

          console.log('调用增强API:', enhanceRequest);
          enhancedResult = await api.enhanceDivinationResult(enhanceRequest);
          console.log('✅ AI增强结果:', enhancedResult);

          // 将增强结果添加到占卜数据中
          divinationData.enhancedResult = enhancedResult;
          divinationData.enhancementUsed = true;

        } catch (apiError) {
          console.warn('⚠️ AI增强API调用失败:', apiError);
          wx.showToast({
            title: 'AI增强暂不可用，使用基础占卜',
            icon: 'none',
            duration: 2000
          });
          // API失败时继续使用本地结果
          divinationData.enhancementUsed = false;
        }
      } else {
        console.log('📖 用户选择基础占卜方法');
        divinationData.enhancementUsed = false;
      }

      // 模拟计算过程（给用户一些时间感受占卜的神秘感）
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      this.setData({
        isCalculating: false
      });

      // 跳转到结果页面
      wx.navigateTo({
        url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(divinationData))}`,
        success: () => {
          console.log('成功跳转到占卜结果页面');
        },
        fail: (err) => {
          console.error('跳转到结果页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    }, 2000);
  },

  /**
   * 智能检测问题类型（升级版语义理解系统）
   */
  detectQuestionType(questionText) {
    console.log('🤖 启动智能问题类型检测，输入文本:', questionText);

    // 首先尝试使用新的智能语义理解系统
    const DivinationCalculator = require('../../utils/divination_calculator');
    const intelligentResult = DivinationCalculator.intelligentQuestionClassification(questionText);

    if (intelligentResult.questionType && intelligentResult.confidence > 5) {
      console.log('✅ 智能语义识别成功:', intelligentResult.questionType, '置信度:', intelligentResult.confidence);
      return intelligentResult.questionType;
    }

    // 如果智能识别失败，回退到关键词匹配系统
    console.log('🔄 回退到关键词匹配系统');
    const text = questionText.toLowerCase();

    // 失物寻找关键词（优先级最高）- 扩展版
    const lostKeywords = [
      // 动作词（标准表达）
      '丢了', '丢失', '找不到', '不见了', '遗失', '失踪', '掉了', '弄丢',
      // 动作词（口语化）
      '搞丢了', '弄没了', '搞不见了', '弄掉了', '搞掉了', '弄丢了',
      // 动作词（方言）
      '搞丢', '弄丢', '搞没', '弄没', '搞不见', '弄不见',
      // 疑问表达
      '哪去了', '哪里去了', '在哪里', '在哪儿', '去哪了', '跑哪了',
      // 物品词（常见）
      '钥匙', '手机', '钱包', '包', '东西', '物品', '失物', '证件', '首饰',
      '戒指', '项链', '手表', '眼镜', '文件', '资料', '卡片', '银行卡',
      // 物品词（扩展）
      '身份证', '驾驶证', '护照', '学生证', '工作证', '会员卡', '充电器',
      '耳机', '背包', '书包', '公文包', '化妆品', '口红', '香水', '药品',
      '伞', '雨伞', '帽子', '围巾', '手套', '袜子', '内衣', '衣服',
      // 组合词
      '找东西', '寻物', '寻找', '找回', '寻回', '追回', '找寻',
      // 状态描述
      '不知道放哪了', '忘记放哪了', '记不起来了', '想不起来了'
    ];

    // 使用加权匹配，多个关键词匹配得分更高
    let lostScore = 0;
    lostKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        lostScore += keyword.length > 2 ? 2 : 1; // 长关键词权重更高
      }
    });

    if (lostScore >= 1) {
      console.log('检测为失物寻找类型，匹配得分:', lostScore);
      return 'lost';
    }

    // 求财问事关键词 - 扩展版
    const wealthKeywords = [
      // 赚钱相关（标准）
      '赚钱', '发财', '投资', '生意', '财运', '收入', '工资', '奖金', '中奖',
      '股票', '基金', '理财', '买卖', '经商', '盈利', '财富', '金钱', '资金',
      // 赚钱相关（口语化）
      '挣钱', '搞钱', '弄钱', '来钱', '进账', '入账', '到账', '回本',
      // 投资理财
      '炒股', '买股票', '买基金', '买房', '房产', '房子', '楼盘', '地产',
      '期货', '外汇', '黄金', '白银', '数字货币', '比特币', '虚拟币',
      // 生意经营
      '开店', '开公司', '创业', '做生意', '做买卖', '摆摊', '开铺',
      '合伙', '入股', '股份', '分红', '利润', '营业额', '销售额',
      // 工作收入
      '薪水', '薪资', '月薪', '年薪', '提成', '佣金', '绩效', '津贴',
      '补贴', '福利', '红包', '年终奖', '季度奖', '业绩奖',
      // 意外之财
      '彩票', '中彩', '中奖', '刮刮乐', '双色球', '大乐透', '福彩',
      '偏财', '横财', '意外收入', '额外收入',
      // 财务状况
      '财务', '资产', '存款', '储蓄', '积蓄', '家底', '身家', '净值'
    ];
    let wealthScore = 0;
    wealthKeywords.forEach(keyword => {
      if (text.includes(keyword)) wealthScore += 1;
    });
    if (wealthScore >= 1) {
      console.log('检测为求财问事类型，匹配得分:', wealthScore);
      return 'wealth';
    }

    // 学习考试关键词（高优先级，避免与事业混淆）
    const studyKeywords = [
      // 考试相关
      '考试', '考研', '高考', '中考', '期末考试', '期中考试', '模拟考试', '测试',
      '考级', '资格考试', '证书考试', '驾考', '公务员考试', '教师资格证',
      // 学习相关
      '学习', '学业', '成绩', '分数', '及格', '复习', '备考', '刷题',
      '上课', '听课', '作业', '论文', '毕业', '升学', '入学', '录取',
      // 学校相关
      '学校', '大学', '高中', '初中', '小学', '班级', '同学', '老师', '教授',
      '校园', '宿舍', '图书馆', '实验室', '课程', '专业', '学科'
    ];
    let studyScore = 0;
    studyKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        // 考试相关词汇权重更高
        studyScore += (keyword.includes('考试') || keyword.includes('考研') || keyword.includes('高考')) ? 3 : 1;
      }
    });

    // 检查是否存在职场关键词冲突（如"面试"+"通过"的情况）
    const hasCareerContext = text.includes('面试') || text.includes('工作') || text.includes('职位') ||
                            text.includes('公司') || text.includes('升职') || text.includes('跳槽');

    if (studyScore >= 1 && !hasCareerContext) {
      console.log('检测为学习考试类型，匹配得分:', studyScore);
      return 'study';
    }

    // 感情问题关键词（优先级较高）- 扩展版
    const loveKeywords = [
      // 感情状态（标准）
      '感情', '恋爱', '结婚', '分手', '复合', '喜欢', '爱情', '男友', '女友',
      '老公', '老婆', '对象', '表白', '求婚', '婚姻', '相亲', '约会', '暗恋',
      // 感情状态（口语化）
      '谈恋爱', '处对象', '找对象', '脱单', '单身', '恋人', '情侣',
      '男朋友', '女朋友', '另一半', '伴侣', '爱人', '心上人', '意中人',
      // 感情发展
      '追求', '追女孩', '追男孩', '被追', '告白', '表白', '求爱',
      '牵手', '接吻', '拥抱', '同居', '订婚', '领证', '办婚礼',
      // 感情问题
      '吵架', '冷战', '误会', '矛盾', '争执', '闹别扭', '生气',
      '分居', '离婚', '出轨', '背叛', '第三者', '小三', '劈腿',
      // 感情挽回
      '挽回', '和好', '重新开始', '破镜重圆', '旧情复燃',
      // 婚恋相关
      '结婚证', '婚礼', '婚纱', '蜜月', '新婚', '夫妻', '家庭',
      '丈夫', '妻子', '老伴', '配偶', '枕边人',
      // 单身相关
      '单身狗', '光棍', '剩女', '剩男', '母胎单身', '万年单身',
      // 网络用语
      'cp', '官宣', '秀恩爱', '撒狗粮', '吃狗粮', '柠檬精'
    ];
    let loveScore = 0;
    loveKeywords.forEach(keyword => {
      if (text.includes(keyword)) loveScore += 1;
    });
    if (loveScore >= 1) {
      console.log('检测为感情问题类型，匹配得分:', loveScore);
      return 'love';
    }

    // 工作事业关键词 - 扩展版
    const careerKeywords = [
      // 工作核心词汇（标准）
      '工作', '事业', '职业', '职位', '岗位', '上班', '下班', '加班',
      // 工作核心词汇（口语化）
      '打工', '搬砖', '上班族', '社畜', '996', '007', '朝九晚五',
      // 职场发展
      '升职', '晋升', '提拔', '跳槽', '面试', '求职', '辞职', '转行', '调动',
      '入职', '离职', '试用期', '转正', '实习', '兼职', '全职', '临时工',
      // 职位类型
      '经理', '主管', '总监', '总裁', '董事', '员工', '助理', '秘书',
      '销售', '客服', '技术', '研发', '设计', '运营', '市场', '财务',
      // 商业经营
      '创业', '生意', '经商', '开店', '合作', '投资', '项目', '业绩', '销售',
      '开公司', '注册公司', '营业执照', '商标', '专利', '融资', '上市',
      // 职场环境
      '公司', '企业', '单位', '部门', '团队', '老板', '领导', '同事', '客户',
      '集团', '分公司', '子公司', '总部', '分部', '办事处', '工厂', '车间',
      // 职场相关
      '职场', '办公室', '会议', '出差', '培训', '绩效', '薪水', '奖金',
      '考勤', '请假', '休假', '年假', '病假', '调休', '值班', '轮班',
      // 合同商务
      '合同', '签约', '签署', '协议', '谈判', '商务', '交易', '订单', '签字',
      '签合同', '签协议', '合作协议', '劳动合同', '保密协议', '竞业协议',
      // 工作成果
      '业绩', '成果', '成绩', '表现', '评价', '考核', '评估', '汇报',
      '方案', '计划', '报告', '总结', '提案', '建议', '意见', '反馈',
      // 工作问题
      '压力', '困难', '挑战', '问题', '麻烦', '冲突', '矛盾', '竞争'
    ];
    let careerScore = 0;
    careerKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        // 核心职场词汇、面试相关和合同相关词汇权重更高
        careerScore += (keyword === '工作' || keyword === '事业' || keyword === '升职' ||
                       keyword === '面试' || keyword === '求职' || keyword === '入职' ||
                       keyword === '合同' || keyword === '签约' || keyword === '签署') ? 3 : 1;
      }
    });

    // 特别处理面试相关问题，确保优先识别为career类型
    if (text.includes('面试')) {
      careerScore += 5; // 给面试额外高权重
      console.log('✅ 检测到面试关键词，增加career权重，确保正确分类');
    }

    if (careerScore >= 1) {
      console.log('检测为工作事业类型，匹配得分:', careerScore);
      return 'career';
    }

    // 出行安全关键词 - 扩展版
    const travelKeywords = [
      // 出行动作（标准）
      '出行', '旅游', '搬家', '出门', '路上', '旅行', '外出', '远行',
      '回家', '探亲', '度假', '出游', '自驾', '坐车', '坐飞机', '坐火车',
      // 出行动作（口语化）
      '出去玩', '去玩', '去旅游', '去度假', '出差', '出门办事',
      '走亲戚', '串门', '拜访', '看望', '回老家', '回娘家',
      // 交通工具
      '开车', '骑车', '走路', '步行', '打车', '叫车', '网约车',
      '公交', '地铁', '高铁', '动车', '绿皮车', '客车', '大巴',
      '飞机', '航班', '船', '轮船', '游轮', '摩托车', '电动车',
      // 出行目的
      '旅游', '观光', '游玩', '散心', '放松', '休闲', '娱乐',
      '出差', '公务', '办事', '谈业务', '开会', '培训',
      '搬家', '搬迁', '迁移', '乔迁', '换房', '搬办公室',
      // 出行地点
      '景点', '景区', '名胜', '古迹', '公园', '海边', '山区',
      '国外', '出国', '境外', '海外', '异地', '外地', '远方',
      // 出行安全
      '平安', '安全', '路况', '交通', '堵车', '事故', '意外'
    ];
    let travelScore = 0;
    travelKeywords.forEach(keyword => {
      if (text.includes(keyword)) travelScore += 1;
    });
    if (travelScore >= 1) {
      console.log('检测为出行安全类型，匹配得分:', travelScore);
      return 'travel';
    }

    // 健康状况关键词 - 扩展版
    const healthKeywords = [
      // 健康状态（标准）
      '健康', '身体', '生病', '医院', '治疗', '康复', '病情', '手术', '检查',
      '疾病', '痊愈', '养病', '调理', '体检', '药物', '医生',
      // 健康状态（口语化）
      '身体好', '身体差', '不舒服', '难受', '疼痛', '痛苦', '虚弱',
      '精神', '体力', '元气', '气色', '脸色', '状态', '感觉',
      // 疾病症状
      '头痛', '头晕', '发烧', '感冒', '咳嗽', '流鼻涕', '打喷嚏',
      '胃痛', '肚子痛', '腹痛', '腰痛', '背痛', '关节痛', '肌肉痛',
      '失眠', '睡不着', '多梦', '疲劳', '乏力', '没精神', '犯困',
      // 医疗相关
      '看病', '就医', '挂号', '门诊', '急诊', '住院', '出院',
      '诊断', '化验', 'ct', 'b超', 'x光', '核磁', '胃镜', '肠镜',
      '吃药', '打针', '输液', '开药', '配药', '中药', '西药',
      // 医疗机构
      '医院', '诊所', '卫生院', '社区医院', '三甲医院', '专科医院',
      '中医院', '西医', '中医', '医生', '大夫', '护士', '专家',
      // 健康管理
      '养生', '保健', '锻炼', '运动', '健身', '跑步', '散步',
      '饮食', '营养', '补品', '保健品', '维生素', '钙片',
      // 心理健康
      '心理', '情绪', '压力', '焦虑', '抑郁', '烦躁', '紧张',
      '心情', '开心', '难过', '伤心', '愤怒', '恐惧', '担心'
    ];
    let healthScore = 0;
    healthKeywords.forEach(keyword => {
      if (text.includes(keyword)) healthScore += 1;
    });
    if (healthScore >= 1) {
      console.log('检测为健康状况类型，匹配得分:', healthScore);
      return 'health';
    }

    // 默认返回其他事务
    console.log('未匹配到特定类型，返回其他事务');
    return 'other';
  },

  /**
   * 测试新增问题类型的识别和建议生成
   */
  testNewQuestionTypes() {
    console.log('\n🧪 开始测试新增问题类型识别和建议生成...');

    const testCases = [
      // 天气预测测试
      {
        question: '明天会下雨吗？',
        expectedType: 'weather',
        description: '天气预测-降雨'
      },
      {
        question: '今天天气怎么样？',
        expectedType: 'weather',
        description: '天气预测-一般'
      },
      {
        question: '这几天会刮风吗？',
        expectedType: 'weather',
        description: '天气预测-风力'
      },
      // 疾病医疗测试
      {
        question: '我的感冒什么时候好？',
        expectedType: 'disease',
        description: '疾病医疗-感冒'
      },
      {
        question: '头疼会不会严重？',
        expectedType: 'disease',
        description: '疾病医疗-头疼'
      },
      {
        question: '这个病能治好吗？',
        expectedType: 'disease',
        description: '疾病医疗-一般'
      },
      // 饮食宜忌测试
      {
        question: '这个药能吃吗？',
        expectedType: 'food',
        description: '饮食宜忌-药物'
      },
      {
        question: '海鲜适合我吃吗？',
        expectedType: 'food',
        description: '饮食宜忌-海鲜'
      },
      {
        question: '这个食物好不好？',
        expectedType: 'food',
        description: '饮食宜忌-一般'
      }
    ];

    testCases.forEach((testCase, index) => {
      console.log(`\n--- 测试用例 ${index + 1}: ${testCase.description} ---`);
      console.log('问题:', testCase.question);

      // 测试智能语义识别
      const DivinationCalculator = require('../../utils/divination_calculator');
      const intelligentResult = DivinationCalculator.intelligentQuestionClassification(testCase.question);

      console.log('智能识别结果:', intelligentResult);
      console.log('预期类型:', testCase.expectedType);
      console.log('实际类型:', intelligentResult.questionType);
      console.log('置信度:', intelligentResult.confidence);

      // 验证识别准确性
      if (intelligentResult.questionType === testCase.expectedType) {
        console.log('✅ 识别正确');
      } else {
        console.log('❌ 识别错误');
      }

      // 测试回退到关键词匹配
      const fallbackType = this.detectQuestionType(testCase.question);
      console.log('关键词匹配结果:', fallbackType);
    });

    console.log('\n🎉 新增问题类型测试完成！');
  },

  /**
   * 完整的端到端测试 - 从问题识别到建议生成
   */
  testEndToEndFlow() {
    console.log('\n🚀 开始端到端功能测试...');

    const testQuestion = '明天会下雨吗？';
    console.log('测试问题:', testQuestion);

    // 步骤1: 智能问题类型识别
    const DivinationCalculator = require('../../utils/divination_calculator');
    const intelligentResult = DivinationCalculator.intelligentQuestionClassification(testQuestion);
    console.log('步骤1 - 智能识别结果:', intelligentResult);

    // 步骤2: 模拟占卜计算
    const mockResult = {
      god: {
        name: '速喜',
        fortune: '大吉',
        color: '#FF6B6B'
      },
      questionType: intelligentResult.questionType || 'weather',
      questionText: testQuestion,
      timestamp: new Date().toISOString()
    };

    console.log('步骤2 - 模拟占卜结果:', mockResult);

    // 步骤3: 生成详细分析
    try {
      const analysis = DivinationCalculator.generateAnalysis(mockResult);
      console.log('步骤3 - 分析生成成功:');
      console.log('- 总体运势:', analysis.overall.description);
      console.log('- 具体建议:', analysis.specific.title);
      console.log('- 详细指导:', analysis.specific.details);
      console.log('- 注意事项:', analysis.precautions);

      console.log('✅ 端到端测试成功！新增问题类型功能正常工作');
    } catch (error) {
      console.error('❌ 端到端测试失败:', error);
    }
  },

  /**
   * 测试万事皆可问功能
   */
  testUniversalQuestions() {
    console.log('=== 开始测试万事皆可问功能 ===');

    const testQuestions = [
      '今天晚上的火锅好不好吃',
      '明天买这件衣服合适吗',
      '这个电影值得看吗',
      '今天去公园散步怎么样',
      '这个游戏好玩吗',
      '明天的聚会会开心吗'
    ];

    testQuestions.forEach((question, index) => {
      console.log(`\n--- 测试问题 ${index + 1}: ${question} ---`);

      // 测试问题类型检测
      const questionType = this.detectQuestionType(question);
      console.log('检测到的问题类型:', questionType);

      // 验证是否正确识别为'other'类型
      if (questionType === 'other') {
        console.log('✅ 正确识别为万能问题类型');
      } else {
        console.log('❌ 未正确识别为万能问题类型，实际类型:', questionType);
      }
    });

    console.log('=== 万事皆可问功能测试完成 ===');
  }
});
