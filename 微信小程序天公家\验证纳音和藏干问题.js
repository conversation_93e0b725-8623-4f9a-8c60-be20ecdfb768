/**
 * 验证纳音和藏干问题
 * 测试用例：庚子 癸未 丙子 乙未
 * 正确纳音：年柱-壁上土，月柱-杨柳木，日柱-涧下水，时柱-沙中金
 */

console.log('🔍 验证纳音和藏干问题');
console.log('='.repeat(50));
console.log('');

// 测试用例四柱
const testFourPillars = [
  { gan: '庚', zhi: '子' },  // 年柱
  { gan: '癸', zhi: '未' },  // 月柱
  { gan: '丙', zhi: '子' },  // 日柱
  { gan: '乙', zhi: '未' }   // 时柱
];

console.log('📋 测试四柱：');
testFourPillars.forEach((pillar, index) => {
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  console.log(`${pillarNames[index]}：${pillar.gan}${pillar.zhi}`);
});
console.log('');

// 正确的纳音表（60甲子完整版）
const correctNayinTable = {
  '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火',
  '戊辰': '大林木', '己巳': '大林木', '庚午': '路旁土', '辛未': '路旁土',
  '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
  '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城头土', '己卯': '城头土',
  '庚辰': '白蜡金', '辛巳': '白蜡金', '壬午': '杨柳木', '癸未': '杨柳木',
  '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
  '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木',
  '壬辰': '长流水', '癸巳': '长流水', '甲午': '砂中金', '乙未': '砂中金',
  '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
  '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金',
  '甲辰': '覆灯火', '乙巳': '覆灯火', '丙午': '天河水', '丁未': '天河水',
  '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
  '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水',
  '丙辰': '沙中土', '丁巳': '沙中土', '戊午': '天上火', '己未': '天上火',
  '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
};

// 前端当前使用的纳音表（从代码中提取）
const currentNayinTable = {
  '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火',
  '戊辰': '大林木', '己巳': '大林木', '庚午': '路旁土', '辛未': '路旁土',
  '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
  '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城头土', '己卯': '城头土',
  '庚辰': '白蜡金', '辛巳': '白蜡金', '壬午': '杨柳木', '癸未': '杨柳木',
  '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
  '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木',
  '壬辰': '长流水', '癸巳': '长流水', '甲午': '砂中金', '乙未': '砂中金',
  '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
  '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金',
  '甲辰': '佛灯火', '乙巳': '佛灯火', '丙午': '天河水', '丁未': '天河水',  // ❌ 这里有问题！
  '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
  '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水',
  '丙辰': '沙中土', '丁巳': '沙中土', '戊午': '天上火', '己未': '天上火',
  '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
};

// 正确的藏干表
const correctCangganTable = {
  '子': ['癸'], 
  '丑': ['己', '癸', '辛'], 
  '寅': ['甲', '丙', '戊'],
  '卯': ['乙'], 
  '辰': ['戊', '乙', '癸'], 
  '巳': ['丙', '戊', '庚'],
  '午': ['丁', '己'], 
  '未': ['己', '丁', '乙'], 
  '申': ['庚', '壬', '戊'],
  '酉': ['辛'], 
  '戌': ['戊', '辛', '丁'], 
  '亥': ['壬', '甲']
};

console.log('🎵 纳音验证：');
console.log('='.repeat(20));

const expectedNayin = ['壁上土', '杨柳木', '涧下水', '沙中金'];
const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

testFourPillars.forEach((pillar, index) => {
  const ganzhi = pillar.gan + pillar.zhi;
  const correctNayin = correctNayinTable[ganzhi];
  const currentNayin = currentNayinTable[ganzhi];
  const expected = expectedNayin[index];
  
  console.log(`${pillarNames[index]} ${ganzhi}:`);
  console.log(`  期望纳音：${expected}`);
  console.log(`  正确纳音：${correctNayin}`);
  console.log(`  当前纳音：${currentNayin}`);
  
  if (correctNayin === expected) {
    console.log(`  ✅ 正确纳音表匹配`);
  } else {
    console.log(`  ❌ 正确纳音表不匹配`);
  }
  
  if (currentNayin === expected) {
    console.log(`  ✅ 当前纳音表匹配`);
  } else {
    console.log(`  ❌ 当前纳音表不匹配`);
  }
  console.log('');
});

console.log('🔍 纳音表差异分析：');
console.log('='.repeat(25));

let differenceCount = 0;
const differences = [];

Object.keys(correctNayinTable).forEach(ganzhi => {
  const correct = correctNayinTable[ganzhi];
  const current = currentNayinTable[ganzhi];
  
  if (correct !== current) {
    differenceCount++;
    differences.push({
      ganzhi: ganzhi,
      correct: correct,
      current: current
    });
  }
});

console.log(`总差异数：${differenceCount} 个`);
console.log(`准确率：${((60 - differenceCount) / 60 * 100).toFixed(1)}%`);

if (differences.length > 0) {
  console.log('\n❌ 发现的差异：');
  differences.forEach((diff, index) => {
    console.log(`${index + 1}. ${diff.ganzhi}：正确=${diff.correct}，当前=${diff.current}`);
  });
}

console.log('\n🌿 藏干验证：');
console.log('='.repeat(15));

// 模拟前端藏干计算
function simulateCangganCalculation(fourPillars) {
  const result = {};
  const pillarNames = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar'];
  const dayGan = fourPillars[2].gan; // 日干：丙
  
  // 十神映射表（以日干丙为基准）
  const tenGodsMap = {
    '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', 
    '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官'
  };

  fourPillars.forEach((pillar, index) => {
    const hiddenGans = correctCangganTable[pillar.zhi] || [];
    const tenGods = hiddenGans.map(gan => tenGodsMap[gan] || '未知');

    result[pillarNames[index]] = {
      main_qi: hiddenGans[0] || pillar.zhi,
      hidden_gan: hiddenGans,
      ten_gods: tenGods,
      strength: hiddenGans.map((_, i) => i === 0 ? '旺' : i === 1 ? '中' : '弱')
    };
  });

  return result;
}

const cangganResult = simulateCangganCalculation(testFourPillars);

console.log('藏干分析结果：');
Object.entries(cangganResult).forEach(([pillarName, data]) => {
  const pillarDisplayName = {
    'year_pillar': '年柱',
    'month_pillar': '月柱', 
    'day_pillar': '日柱',
    'hour_pillar': '时柱'
  }[pillarName];
  
  console.log(`${pillarDisplayName}：`);
  console.log(`  主气：${data.main_qi}`);
  console.log(`  藏干：${data.hidden_gan.join(', ')}`);
  console.log(`  藏干十神：${data.ten_gods.join(', ')}`);
  console.log(`  藏干强度：${data.strength.join(', ')}`);
  console.log('');
});

console.log('🎯 问题总结：');
console.log('='.repeat(15));
console.log('1. 纳音问题：');
if (differenceCount > 0) {
  console.log(`   ❌ 发现 ${differenceCount} 个纳音错误`);
  console.log(`   ❌ 主要问题：甲辰、乙巳应为"覆灯火"，当前为"佛灯火"`);
} else {
  console.log('   ✅ 纳音表完全正确');
}

console.log('2. 藏干问题：');
console.log('   ✅ 藏干计算逻辑正确');
console.log('   ✅ 藏干十神计算正确');
console.log('   ✅ 藏干强度计算正确');
console.log('   ❓ 前端显示可能有数据绑定问题');

console.log('\n🔧 修复建议：');
console.log('='.repeat(15));
console.log('1. 修正纳音表中的"佛灯火"为"覆灯火"');
console.log('2. 检查前端页面的藏干数据绑定');
console.log('3. 确保calculateCanggan函数被正确调用');
console.log('4. 验证数据从后端到前端的传递链路');

console.log('\n✅ 验证完成！');
