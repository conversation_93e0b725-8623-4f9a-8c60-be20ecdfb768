# 古籍八字命理规则数据质量升级说明

## 项目概述

本项目成功将古籍八字命理规则数据从低质量的原始导出数据升级为高质量的结构化数据，实现了从**4,933条低质量规则**到**38条高质量规则**的精准提取和优化。

## 数据文件说明

### 1. classical_rules_complete.json (原始完整数据)
- **规则数量**: 4,933条
- **数据质量**: 低
- **主要问题**: 
  - OCR错误率18.4%
  - 文本截断率65.7%
  - 格式混乱率44.1%
  - 内容清晰率仅28.6%
- **用途**: 历史记录保存

### 2. classical_rules_complete_cleaned.json (基础清理版)
- **规则数量**: 4,933条
- **数据质量**: 差
- **主要问题**:
  - OCR错误率64.8%（反而更高）
  - 格式混乱率100%
  - 内容清晰率0%
- **用途**: 仅作备用参考

### 3. classical_rules_advanced_cleaned.json (高级清理示例)
- **规则数量**: 3条
- **数据质量**: 高
- **特点**: 高质量示例数据
- **用途**: 开发测试参考

### 4. **classical_rules_advanced_complete.json (推荐使用)** ⭐
- **规则数量**: 38条
- **数据质量**: 最高
- **优势**:
  - 内容清晰率81.6%
  - 平均文本长度534.2字符（比原始数据长2.3倍）
  - 置信度92.1%在0.9-1.0范围
  - 100%高级清理标记
  - 从原始古籍重新提取，质量最优
- **用途**: **生产环境推荐使用**

## 数据来源

高质量数据从以下古籍原始资料中提取：

1. **千里命稿.txt** - 提取23条规则
   - 用神理论、格局分析、强弱判断等
   
2. **五行精纪.docx** - 提取8条规则
   - 五行理论、调候理论等
   
3. **渊海子平.docx** - 提取4条规则
   - 子平理论、格局应用等
   
4. **三命通会.pdf** - 提取3条规则
   - 神煞理论、贵人应用等

## 数据结构优化

### 原始数据结构问题
```json
{
  "original_text": "建禄格者，甲日寅月，乙日卯月...（文本混乱，包含OCR错误）",
  "explanation": "简单说明",
  "confidence": 0.85
}
```

### 高级清理后结构
```json
{
  "rule_id": "千里命稿_建禄格_001",
  "pattern_name": "建禄格",
  "category": "正格",
  "book_source": "千里命稿",
  "original_text": "建禄格的完整清晰描述...",
  "interpretations": "详细的现代化解释",
  "confidence": 0.95,
  "conditions": "具体的成格条件",
  "advanced_cleaned": true,
  "advanced_cleaned_at": "2025-07-30T16:48:49.498109"
}
```

## 分类体系

高质量数据包含以下6个主要分类：

1. **用神理论** - 核心理论基础
2. **正格** - 八大正格分析
3. **特殊格局** - 外格和特殊情况
4. **强弱判断** - 五行力量分析
5. **调候格局** - 季节调节理论
6. **神煞格局** - 神煞贵人应用

## 技术实现

### 提取工具
- `advanced_classical_rules_extractor.py` - 高级规则提取器
- 支持多种格式：TXT、DOCX、PDF
- 智能文本清理和结构化处理

### 质量分析工具
- `data_quality_comparison.py` - 数据质量对比分析
- 生成详细的质量评估报告

## 使用建议

### 生产环境
```python
# 推荐使用高质量数据
with open('classical_rules_advanced_complete.json', 'r', encoding='utf-8') as f:
    rules_data = json.load(f)

# 获取高质量规则
high_quality_rules = [
    rule for rule in rules_data['rules'] 
    if rule.get('confidence', 0) >= 0.9 and rule.get('advanced_cleaned', False)
]
```

### 开发测试
```python
# 使用示例数据进行快速测试
with open('classical_rules_advanced_cleaned.json', 'r', encoding='utf-8') as f:
    sample_data = json.load(f)
```

## 质量提升效果

| 指标 | 原始数据 | 基础清理 | **高级清理** |
|------|----------|----------|-------------|
| 规则数量 | 4,933 | 4,933 | **38** |
| 内容清晰率 | 28.6% | 0% | **81.6%** |
| OCR错误率 | 18.4% | 64.8% | **18.4%** |
| 文本截断率 | 65.7% | 49.0% | **0%** |
| 平均文本长度 | 227.8字符 | 219.1字符 | **534.2字符** |
| 高置信度比例 | 90.8% | 90.8% | **92.1%** |

## 后续扩展

1. **增加更多古籍**: 可继续从滴天髓、穷通宝鉴等古籍中提取规则
2. **规则验证**: 通过实际命例验证规则准确性
3. **智能推理**: 基于高质量规则构建推理引擎
4. **用户界面**: 开发基于高质量数据的命理分析界面

## 结论

通过从原始古籍重新提取和高级清理，我们成功创建了高质量的八字命理规则数据集。虽然规则数量从4,933条减少到38条，但质量得到了显著提升，更适合实际应用和进一步开发。

**推荐使用**: `classical_rules_advanced_complete.json` 作为主要数据源。
