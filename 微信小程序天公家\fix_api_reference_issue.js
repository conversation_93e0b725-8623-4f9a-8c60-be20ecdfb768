/**
 * 修复API引用问题的脚本
 * 解决 api/analyze.js 文件不存在的编译错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始检查和修复API引用问题...');

// 1. 检查是否存在api目录
const apiDir = './api';
if (!fs.existsSync(apiDir)) {
  console.log('📁 api目录不存在，这是正常的（已删除后端API）');
} else {
  console.log('📁 发现api目录存在');
  const apiFiles = fs.readdirSync(apiDir);
  console.log('   文件列表:', apiFiles);
}

// 2. 检查可能的引用文件
const filesToCheck = [
  'app.js',
  'pages/bazi-input/index.js',
  'pages/bazi-result/index.js',
  'utils/unified_wuxing_api.js',
  'utils/enhanced_dynamic_analyzer.js'
];

console.log('\n🔍 检查可能包含API引用的文件...');

filesToCheck.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含api/analyze引用
    const hasApiAnalyzeRef = content.includes('api/analyze');
    const hasRequireApi = /require\(['"].*api.*['"]/.test(content);
    const hasImportApi = /import.*from.*['"].*api.*['"]/.test(content);
    
    if (hasApiAnalyzeRef || hasRequireApi || hasImportApi) {
      console.log(`❌ ${filePath} 包含API引用:`);
      if (hasApiAnalyzeRef) console.log('   - 包含 api/analyze 引用');
      if (hasRequireApi) console.log('   - 包含 require API 引用');
      if (hasImportApi) console.log('   - 包含 import API 引用');
    } else {
      console.log(`✅ ${filePath} 无API引用问题`);
    }
  } else {
    console.log(`⚠️ ${filePath} 文件不存在`);
  }
});

// 3. 检查项目配置文件
console.log('\n🔍 检查项目配置文件...');

const configFiles = [
  'app.json',
  'project.config.json',
  'project.private.config.json'
];

configFiles.forEach(configFile => {
  if (fs.existsSync(configFile)) {
    const content = fs.readFileSync(configFile, 'utf8');
    
    try {
      const config = JSON.parse(content);
      
      // 检查是否有API相关配置
      const configStr = JSON.stringify(config);
      if (configStr.includes('api/analyze') || configStr.includes('api\\analyze')) {
        console.log(`❌ ${configFile} 包含API配置引用`);
      } else {
        console.log(`✅ ${configFile} 无API配置问题`);
      }
    } catch (error) {
      console.log(`⚠️ ${configFile} JSON解析失败:`, error.message);
    }
  } else {
    console.log(`⚠️ ${configFile} 文件不存在`);
  }
});

// 4. 提供解决方案
console.log('\n🔧 解决方案建议:');

console.log('1. 清理微信开发者工具缓存:');
console.log('   - 在开发者工具中: 工具 -> 清除缓存 -> 清除所有缓存');
console.log('   - 重新编译项目');

console.log('\n2. 检查是否有隐藏的引用:');
console.log('   - 可能存在于注释中的引用');
console.log('   - 可能存在于字符串拼接中的引用');

console.log('\n3. 如果问题持续存在:');
console.log('   - 创建空的api目录和analyze.js文件作为临时解决方案');
console.log('   - 或者找到具体的引用位置并修复');

// 5. 创建临时解决方案（如果需要）
const createTempSolution = process.argv.includes('--create-temp');

if (createTempSolution) {
  console.log('\n🛠️ 创建临时解决方案...');
  
  // 创建api目录
  if (!fs.existsSync('./api')) {
    fs.mkdirSync('./api');
    console.log('✅ 创建api目录');
  }
  
  // 创建空的analyze.js文件
  const tempAnalyzeContent = `// 临时文件 - 解决编译错误
// 这个文件是为了解决微信开发者工具的编译错误而创建的
// 实际的分析功能已经集成到前端页面中

console.warn('⚠️ 使用了临时的api/analyze.js文件');
console.warn('💡 实际的八字分析功能在pages/bazi-input/index.js中');

// 导出空对象避免引用错误
module.exports = {
  // 空的导出对象
};
`;
  
  fs.writeFileSync('./api/analyze.js', tempAnalyzeContent);
  console.log('✅ 创建临时的api/analyze.js文件');
  
  console.log('\n⚠️ 注意: 这只是临时解决方案');
  console.log('💡 建议找到实际的引用位置并修复，而不是依赖临时文件');
}

console.log('\n🏁 API引用问题检查完成');

// 6. 输出总结
console.log('\n📊 问题总结:');
console.log('- 错误信息显示系统尝试访问不存在的api/analyze.js文件');
console.log('- 这可能是微信开发者工具的缓存问题');
console.log('- 或者存在隐藏的代码引用');
console.log('- 建议优先清理缓存，如果无效再使用临时解决方案');
