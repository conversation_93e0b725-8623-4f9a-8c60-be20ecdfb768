// utils/poster_generator.js
// 占卜结果海报生成工具

/**
 * 海报生成器
 */
class PosterGenerator {
  
  /**
   * 生成占卜结果海报
   * @param {object} result - 占卜结果
   * @param {object} analysis - 分析结果
   * @returns {Promise<string>} 海报图片路径
   */
  static async generatePoster(result, analysis) {
    return new Promise((resolve, reject) => {
      try {
        // 创建canvas上下文
        const query = wx.createSelectorQuery();
        query.select('#poster-canvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res[0]) {
              reject(new Error('Canvas节点未找到'));
              return;
            }
            
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸
            const windowInfo = wx.getWindowInfo();
            const dpr = windowInfo.pixelRatio;
            canvas.width = 750 * dpr;
            canvas.height = 1334 * dpr;
            ctx.scale(dpr, dpr);
            
            // 绘制海报
            this.drawPoster(ctx, result, analysis)
              .then(() => {
                // 导出图片
                wx.canvasToTempFilePath({
                  canvas: canvas,
                  success: (res) => {
                    resolve(res.tempFilePath);
                  },
                  fail: reject
                });
              })
              .catch(reject);
          });
      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * 绘制海报内容
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {object} result - 占卜结果
   * @param {object} analysis - 分析结果
   */
  static async drawPoster(ctx, result, analysis) {
    const width = 750;
    const height = 1334;
    
    // 绘制背景渐变
    await this.drawBackground(ctx, width, height, result.god.isAuspicious);
    
    // 绘制标题
    await this.drawTitle(ctx, width);
    
    // 绘制六神结果
    await this.drawGodResult(ctx, width, result);
    
    // 绘制诗句
    await this.drawPoetry(ctx, width, result);
    
    // 绘制建议
    await this.drawAdvice(ctx, width, analysis);

    // 绘制底部信息
    await this.drawFooter(ctx, width, height);
  }
  
  /**
   * 绘制背景
   */
  static async drawBackground(ctx, width, height, isAuspicious) {
    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    
    if (isAuspicious) {
      // 吉卦 - 绿色系背景
      gradient.addColorStop(0, '#6B5B73');
      gradient.addColorStop(0.5, '#48BB78');
      gradient.addColorStop(1, '#A8926D');
    } else {
      // 凶卦 - 保持原有仙风道骨色调，但稍微偏暖
      gradient.addColorStop(0, '#6B5B73');
      gradient.addColorStop(0.5, '#8B7355');
      gradient.addColorStop(1, '#A8926D');
    }
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // 添加装饰图案
    await this.drawDecorations(ctx, width, height);
  }
  
  /**
   * 绘制装饰图案
   */
  static async drawDecorations(ctx, width, height) {
    ctx.save();
    
    // 绘制圆形装饰
    const circles = [
      { x: width * 0.2, y: height * 0.15, radius: 60, opacity: 0.1 },
      { x: width * 0.8, y: height * 0.25, radius: 40, opacity: 0.08 },
      { x: width * 0.15, y: height * 0.7, radius: 50, opacity: 0.06 },
      { x: width * 0.85, y: height * 0.8, radius: 35, opacity: 0.1 }
    ];
    
    circles.forEach(circle => {
      ctx.beginPath();
      ctx.arc(circle.x, circle.y, circle.radius, 0, 2 * Math.PI);
      ctx.fillStyle = `rgba(255, 215, 0, ${circle.opacity})`;
      ctx.fill();
    });
    
    ctx.restore();
  }
  
  /**
   * 绘制标题
   */
  static async drawTitle(ctx, width) {
    ctx.save();
    
    // 主标题
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 48px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('天公师兄·六壬时课', width / 2, 120);
    
    ctx.restore();
  }
  
  /**
   * 绘制六神结果
   */
  static async drawGodResult(ctx, width, result) {
    ctx.save();
    
    const centerX = width / 2;
    const centerY = 280;
    
    // 绘制六神名称背景圆
    ctx.beginPath();
    ctx.arc(centerX, centerY, 80, 0, 2 * Math.PI);
    ctx.fillStyle = result.god.color;
    ctx.fill();
    
    // 绘制六神名称
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 36px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(result.god.name, centerX, centerY + 12);
    
    // 绘制吉凶判断
    ctx.fillStyle = result.god.color;
    ctx.font = 'bold 32px sans-serif';
    ctx.fillText(result.god.fortune, centerX, centerY + 120);
    
    // 绘制五行神兽
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = '24px sans-serif';
    ctx.fillText(`${result.god.element}行 · ${result.god.beast}`, centerX, centerY + 160);
    
    ctx.restore();
  }
  
  /**
   * 绘制诗句
   */
  static async drawPoetry(ctx, width, result) {
    ctx.save();
    
    const centerX = width / 2;
    let currentY = 480;
    
    // 绘制诗句背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.fillRect(60, currentY - 40, width - 120, 120);
    
    // 绘制诗句内容
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '28px sans-serif';
    ctx.textAlign = 'center';
    
    const description = result.god.description;
    const lines = this.wrapText(description, 20);
    
    lines.forEach((line, index) => {
      ctx.fillText(line, centerX, currentY + index * 40);
    });
    
    ctx.restore();
  }
  
  /**
   * 绘制建议
   */
  static async drawAdvice(ctx, width, analysis) {
    ctx.save();
    
    const centerX = width / 2;
    let currentY = 680;
    
    // 标题
    ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
    ctx.font = 'bold 24px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('天公指引', centerX, currentY);
    
    currentY += 50;
    
    // 建议内容
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = '22px sans-serif';
    
    if (analysis && analysis.specific) {
      const advice = analysis.specific.title;
      const lines = this.wrapText(advice, 24);
      
      lines.forEach((line, index) => {
        ctx.fillText(line, centerX, currentY + index * 35);
      });
    }
    
    ctx.restore();
  }

  /**
   * 绘制底部信息
   */
  static async drawFooter(ctx, width, height) {
    ctx.save();
    
    const footerY = height - 60;
    
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.font = '18px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('无事不占 · 一事一占 · 诚心为要', width / 2, footerY);
    
    ctx.restore();
  }
  
  /**
   * 文本换行处理
   * @param {string} text - 文本内容
   * @param {number} maxLength - 每行最大字符数
   * @returns {array} 分行后的文本数组
   */
  static wrapText(text, maxLength) {
    if (!text) return [''];
    
    const lines = [];
    let currentLine = '';
    
    for (let i = 0; i < text.length; i++) {
      currentLine += text[i];
      
      if (currentLine.length >= maxLength || text[i] === '\n') {
        lines.push(currentLine.replace('\n', ''));
        currentLine = '';
      }
    }
    
    if (currentLine) {
      lines.push(currentLine);
    }
    
    return lines;
  }
  
  /**
   * 保存海报到相册
   * @param {string} tempFilePath - 临时文件路径
   * @returns {Promise} 保存结果
   */
  static async saveToAlbum(tempFilePath) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: resolve,
        fail: reject
      });
    });
  }
}

module.exports = PosterGenerator;
