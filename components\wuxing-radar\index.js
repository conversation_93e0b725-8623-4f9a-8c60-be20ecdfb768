// components/wuxing-radar/index.js
// 五行雷达图组件
Component({
  properties: {
    // 五行分数数据
    wuxingScores: {
      type: Object,
      value: {
        wood: 50,
        fire: 50,
        earth: 50,
        metal: 50,
        water: 50
      },
      observer: 'onWuxingScoresChange'
    },
    
    // 是否显示详细信息
    showDetails: {
      type: Boolean,
      value: true
    },
    
    // 是否启用动画
    animation: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 五行基础配置
    wuxingConfig: {
      wood: { name: '木', symbol: '🌿', color: '#4CAF50' },
      fire: { name: '火', symbol: '🔥', color: '#FF5722' },
      earth: { name: '土', symbol: '🏔️', color: '#795548' },
      metal: { name: '金', symbol: '🔸', color: '#9E9E9E' },
      water: { name: '水', symbol: '💧', color: '#2196F3' }
    },
    
    // 处理后的五行列表
    wuxingList: [],
    
    // 总分
    totalScore: 0,
    
    // Canvas上下文
    canvasContext: null,
    
    // 画布尺寸
    canvasWidth: 300,
    canvasHeight: 300,
    
    // 雷达图参数
    centerX: 150,
    centerY: 150,
    radius: 100,
    
    // 动画参数
    animationProgress: 0,
    animationTimer: null
  },

  lifetimes: {
    attached() {
      this.initCanvas();
    },
    
    detached() {
      if (this.data.animationTimer) {
        clearInterval(this.data.animationTimer);
      }
    }
  },

  methods: {
    // 初始化Canvas
    initCanvas() {
      const query = wx.createSelectorQuery().in(this);
      query.select('#wuxingRadarCanvas').boundingClientRect().exec((res) => {
        if (res[0]) {
          const rect = res[0];
          this.setData({
            canvasWidth: rect.width,
            canvasHeight: rect.height,
            centerX: rect.width / 2,
            centerY: rect.height / 2,
            radius: Math.min(rect.width, rect.height) * 0.3
          });
          
          // 获取Canvas上下文
          const ctx = wx.createCanvasContext('wuxingRadarCanvas', this);
          this.setData({ canvasContext: ctx });
          
          // 绘制雷达图
          this.drawRadarChart();
        }
      });
    },

    // 五行分数变化处理
    onWuxingScoresChange(newScores) {
      this.processWuxingData(newScores);
      if (this.data.canvasContext) {
        this.drawRadarChart();
      }
    },

    // 处理五行数据
    processWuxingData(scores) {
      const config = this.data.wuxingConfig;
      const wuxingList = [];
      let totalScore = 0;

      Object.keys(config).forEach(element => {
        const score = scores[element] || 0;
        const percentage = Math.min(score, 100);
        totalScore += score;
        
        wuxingList.push({
          element: element,
          name: config[element].name,
          symbol: config[element].symbol,
          color: config[element].color,
          score: score,
          percentage: percentage,
          strength: this.getStrengthLevel(score),
          description: this.getElementDescription(element, score)
        });
      });

      this.setData({
        wuxingList: wuxingList,
        totalScore: Math.round(totalScore / 5)
      });
    },

    // 获取强度等级
    getStrengthLevel(score) {
      if (score >= 80) return '极强';
      if (score >= 60) return '较强';
      if (score >= 40) return '中等';
      if (score >= 20) return '较弱';
      return '极弱';
    },

    // 获取元素描述
    getElementDescription(element, score) {
      const descriptions = {
        wood: score >= 60 ? '生机勃勃，创造力强' : '需要培养创新思维',
        fire: score >= 60 ? '热情洋溢，行动力强' : '需要增强执行力',
        earth: score >= 60 ? '稳重踏实，包容性强' : '需要提升稳定性',
        metal: score >= 60 ? '意志坚定，原则性强' : '需要加强决断力',
        water: score >= 60 ? '智慧灵活，适应性强' : '需要提高应变能力'
      };
      return descriptions[element] || '需要平衡发展';
    },

    // 绘制雷达图
    drawRadarChart() {
      const ctx = this.data.canvasContext;
      if (!ctx) return;

      const { centerX, centerY, radius, wuxingList } = this.data;
      
      // 清空画布
      ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 绘制背景网格
      this.drawBackground(ctx, centerX, centerY, radius);
      
      // 绘制数据区域
      this.drawDataArea(ctx, centerX, centerY, radius, wuxingList);
      
      // 绘制轴线和标签
      this.drawAxesAndLabels(ctx, centerX, centerY, radius, wuxingList);
      
      // 绘制数据点
      this.drawDataPoints(ctx, centerX, centerY, radius, wuxingList);
      
      ctx.draw();
    },

    // 绘制背景网格
    drawBackground(ctx, centerX, centerY, radius) {
      ctx.setStrokeStyle('#E0E0E0');
      ctx.setLineWidth(1);
      
      // 绘制同心圆
      for (let i = 1; i <= 5; i++) {
        const r = radius * i / 5;
        ctx.beginPath();
        ctx.arc(centerX, centerY, r, 0, Math.PI * 2);
        ctx.stroke();
      }
      
      // 绘制轴线
      const angleStep = Math.PI * 2 / 5;
      for (let i = 0; i < 5; i++) {
        const angle = i * angleStep - Math.PI / 2;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(x, y);
        ctx.stroke();
      }
    },

    // 绘制数据区域
    drawDataArea(ctx, centerX, centerY, radius, wuxingList) {
      if (wuxingList.length === 0) return;
      
      ctx.setFillStyle('rgba(33, 150, 243, 0.2)');
      ctx.setStrokeStyle('#2196F3');
      ctx.setLineWidth(2);
      
      ctx.beginPath();
      const angleStep = Math.PI * 2 / 5;
      
      wuxingList.forEach((item, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const distance = radius * (item.score / 100);
        const x = centerX + distance * Math.cos(angle);
        const y = centerY + distance * Math.sin(angle);
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },

    // 绘制轴线和标签
    drawAxesAndLabels(ctx, centerX, centerY, radius, wuxingList) {
      ctx.setFillStyle('#333333');
      ctx.setFontSize(12);
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      
      const angleStep = Math.PI * 2 / 5;
      
      wuxingList.forEach((item, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const labelX = centerX + (radius + 20) * Math.cos(angle);
        const labelY = centerY + (radius + 20) * Math.sin(angle);
        
        ctx.fillText(item.name, labelX, labelY);
      });
    },

    // 绘制数据点
    drawDataPoints(ctx, centerX, centerY, radius, wuxingList) {
      const angleStep = Math.PI * 2 / 5;
      
      wuxingList.forEach((item, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const distance = radius * (item.score / 100);
        const x = centerX + distance * Math.cos(angle);
        const y = centerY + distance * Math.sin(angle);
        
        ctx.setFillStyle(item.color);
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      });
    },

    // Canvas触摸事件
    onCanvasTouch(e) {
      // 可以在这里添加交互逻辑
      console.log('Canvas touched:', e.type);
    }
  }
});
