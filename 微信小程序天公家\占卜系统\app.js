// app.js - 玉匣记占卜小程序入口文件
const api = require('./utils/api');

App({
  globalData: {
    userInfo: null,
    apiConnected: false,
    statistics: null,
    categories: null
  },

  onLaunch() {
    console.log('《玉匣记》占卜小程序启动');

    // 获取系统信息
    this.getSystemInfo();

    // 测试API连接
    this.testApiConnection();
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      // 使用新的分离式API替代废弃的wx.getSystemInfoSync
      const deviceInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      // 合并系统信息
      const systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
      };

      console.log('系统信息:', systemInfo);
      this.globalData.systemInfo = systemInfo;
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },

  // 测试API连接
  async testApiConnection() {
    console.log('开始测试API连接...');

    try {
      const result = await api.healthCheck();
      console.log('API连接测试成功:', result);

      this.globalData.apiConnected = true;

      wx.showToast({
        title: 'API连接成功',
        icon: 'success',
        duration: 2000
      });

      // 获取基础数据
      this.loadBasicData();

    } catch (error) {
      console.error('API连接测试失败:', error);

      this.globalData.apiConnected = false;

      wx.showModal({
        title: 'API连接失败',
        content: `无法连接到服务器：${error.message}\n\n请确保FastAPI服务正在运行在localhost:8000`,
        showCancel: false,
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 延迟重试
            setTimeout(() => {
              this.testApiConnection();
            }, 2000);
          }
        }
      });
    }
  },

  // 加载基础数据
  async loadBasicData() {
    try {
      // 获取统计信息
      const stats = await api.getStatistics();
      console.log('系统统计信息:', stats);
      this.globalData.statistics = stats;

      // 获取分类信息
      const categories = await api.getCategories();
      console.log('分类信息:', categories);
      this.globalData.categories = categories;

    } catch (error) {
      console.error('加载基础数据失败:', error);
    }
  }
});