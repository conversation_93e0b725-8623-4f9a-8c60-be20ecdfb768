#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def validate_wxml_structure(file_path):
    """验证WXML文件的标签结构"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 简单的标签匹配
    view_stack = []
    scroll_view_stack = []
    
    for line_num, line in enumerate(lines, 1):
        # 查找view标签
        view_opens = re.findall(r'<view(?:\s[^>]*)?(?<!/)>', line)
        view_closes = re.findall(r'</view>', line)
        
        # 查找scroll-view标签
        scroll_opens = re.findall(r'<scroll-view(?:\s[^>]*)?(?<!/)>', line)
        scroll_closes = re.findall(r'</scroll-view>', line)
        
        # 处理view标签
        for _ in view_opens:
            view_stack.append(line_num)
        
        for _ in view_closes:
            if view_stack:
                view_stack.pop()
            else:
                print(f"❌ 第{line_num}行：多余的 </view> 标签")
        
        # 处理scroll-view标签
        for _ in scroll_opens:
            scroll_view_stack.append(line_num)
        
        for _ in scroll_closes:
            if scroll_view_stack:
                scroll_view_stack.pop()
            else:
                print(f"❌ 第{line_num}行：多余的 </scroll-view> 标签")
    
    # 检查未闭合的标签
    if view_stack:
        print(f"❌ 有 {len(view_stack)} 个未闭合的 <view> 标签，开始行：{view_stack}")
    
    if scroll_view_stack:
        print(f"❌ 有 {len(scroll_view_stack)} 个未闭合的 <scroll-view> 标签，开始行：{scroll_view_stack}")
    
    if not view_stack and not scroll_view_stack:
        print("✅ WXML文件结构验证通过！所有标签都正确匹配。")
    
    return len(view_stack) == 0 and len(scroll_view_stack) == 0

if __name__ == "__main__":
    validate_wxml_structure("pages/bazi-result/index.wxml")
