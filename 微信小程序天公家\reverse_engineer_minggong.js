/**
 * 反推命宫计算的正确公式
 * 已知：1996年10月13日15:45（丙年戌月申时）命宫是"己亥"
 */

// 反推分析
function reverseEngineerMingGong() {
  console.log('🔍 反推命宫计算公式');
  console.log('已知条件:');
  console.log('- 年干: 丙');
  console.log('- 月支: 戌');
  console.log('- 时支: 申');
  console.log('- 命宫: 己亥');
  console.log('=' * 50);
  
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const monthIndex = zhiOrder.indexOf('戌'); // 10
  const hourIndex = zhiOrder.indexOf('申');  // 8
  const mingGongIndex = zhiOrder.indexOf('亥'); // 11
  
  console.log('地支索引:');
  console.log('- 戌月索引:', monthIndex);
  console.log('- 申时索引:', hourIndex);
  console.log('- 亥宫索引:', mingGongIndex);
  
  // 尝试各种可能的公式
  console.log('\n🔧 尝试反推公式:');
  
  // 公式1: (某个基数 - 月支索引 - 时支索引) % 12 = 命宫索引
  for (let base = 1; base <= 30; base++) {
    let result = (base - monthIndex - hourIndex) % 12;
    if (result < 0) result += 12;
    if (result === mingGongIndex) {
      console.log(`✅ 找到公式: (${base} - 月支索引 - 时支索引) % 12`);
      console.log(`验证: (${base} - ${monthIndex} - ${hourIndex}) % 12 = ${result} = 亥宫索引`);
    }
  }
  
  // 公式2: (某个基数 + 月支索引 - 时支索引) % 12 = 命宫索引
  for (let base = 1; base <= 30; base++) {
    let result = (base + monthIndex - hourIndex) % 12;
    if (result < 0) result += 12;
    if (result === mingGongIndex) {
      console.log(`✅ 找到公式: (${base} + 月支索引 - 时支索引) % 12`);
      console.log(`验证: (${base} + ${monthIndex} - ${hourIndex}) % 12 = ${result} = 亥宫索引`);
    }
  }
  
  // 公式3: (某个基数 - 月支索引 + 时支索引) % 12 = 命宫索引
  for (let base = 1; base <= 30; base++) {
    let result = (base - monthIndex + hourIndex) % 12;
    if (result < 0) result += 12;
    if (result === mingGongIndex) {
      console.log(`✅ 找到公式: (${base} - 月支索引 + 时支索引) % 12`);
      console.log(`验证: (${base} - ${monthIndex} + ${hourIndex}) % 12 = ${result} = 亥宫索引`);
    }
  }
  
  // 公式4: 使用地支数值（子1、丑2...亥12）
  const zhiNum = {
    '子': 1, '丑': 2, '寅': 3, '卯': 4, '辰': 5, '巳': 6,
    '午': 7, '未': 8, '申': 9, '酉': 10, '戌': 11, '亥': 12
  };
  
  const monthNum = zhiNum['戌']; // 11
  const hourNum = zhiNum['申'];  // 9
  const mingGongNum = zhiNum['亥']; // 12
  
  console.log('\n使用地支数值（子1...亥12）:');
  console.log('- 戌月数值:', monthNum);
  console.log('- 申时数值:', hourNum);
  console.log('- 亥宫数值:', mingGongNum);
  
  for (let base = 1; base <= 30; base++) {
    let result = base - monthNum - hourNum;
    while (result <= 0) result += 12;
    while (result > 12) result -= 12;
    
    if (result === mingGongNum) {
      console.log(`✅ 找到公式: ${base} - 月数 - 时数 = 命宫数`);
      console.log(`验证: ${base} - ${monthNum} - ${hourNum} = ${base - monthNum - hourNum} → ${result} = 亥宫数值`);
    }
  }
  
  // 特殊检查：可能是农历月份的问题
  console.log('\n🌙 检查农历月份:');
  console.log('1996年10月13日对应农历九月初二');
  console.log('如果使用农历九月（酉月）而不是阳历10月（戌月）:');
  
  const lunarMonthZhi = '酉'; // 农历九月
  const lunarMonthIndex = zhiOrder.indexOf(lunarMonthZhi); // 9
  const lunarMonthNum = zhiNum[lunarMonthZhi]; // 10
  
  console.log('- 农历酉月索引:', lunarMonthIndex);
  console.log('- 农历酉月数值:', lunarMonthNum);
  
  // 重新用农历月份测试
  for (let base = 1; base <= 30; base++) {
    let result = base - lunarMonthNum - hourNum;
    while (result <= 0) result += 12;
    while (result > 12) result -= 12;
    
    if (result === mingGongNum) {
      console.log(`✅ 农历公式: ${base} - 农历月数 - 时数 = 命宫数`);
      console.log(`验证: ${base} - ${lunarMonthNum} - ${hourNum} = ${base - lunarMonthNum - hourNum} → ${result} = 亥宫数值`);
    }
  }
}

// 运行反推分析
reverseEngineerMingGong();
