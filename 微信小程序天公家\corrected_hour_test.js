/**
 * 修正后的时柱计算测试
 * 使用正确的时辰判断标准
 */

console.log('🕐 修正后的时柱计算测试');
console.log('='.repeat(60));

// 天干地支数组
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 五鼠遁时干公式
const wushuDunTimeMap = {
  '甲': 0, '己': 0, // 甲己还加甲
  '乙': 2, '庚': 2, // 乙庚丙作初
  '丙': 4, '辛': 4, // 丙辛从戊起
  '丁': 6, '壬': 6, // 丁壬庚子居
  '戊': 8, '癸': 8  // 戊癸何方发，壬子是真途
};

// 正确的时辰判断函数
function getHourZhi(hour, minute = 0) {
  const totalMinutes = hour * 60 + minute;
  
  // 正确的时辰边界：
  // 23:00-01:00 子时, 01:00-03:00 丑时, 03:00-05:00 寅时, 05:00-07:00 卯时
  // 07:00-09:00 辰时, 09:00-11:00 巳时, 11:00-13:00 午时, 13:00-15:00 未时
  // 15:00-17:00 申时, 17:00-19:00 酉时, 19:00-21:00 戌时, 21:00-23:00 亥时
  
  if (totalMinutes >= 23 * 60 || totalMinutes < 1 * 60) return '子';
  if (totalMinutes >= 1 * 60 && totalMinutes < 3 * 60) return '丑';
  if (totalMinutes >= 3 * 60 && totalMinutes < 5 * 60) return '寅';
  if (totalMinutes >= 5 * 60 && totalMinutes < 7 * 60) return '卯';
  if (totalMinutes >= 7 * 60 && totalMinutes < 9 * 60) return '辰';
  if (totalMinutes >= 9 * 60 && totalMinutes < 11 * 60) return '巳';
  if (totalMinutes >= 11 * 60 && totalMinutes < 13 * 60) return '午';
  if (totalMinutes >= 13 * 60 && totalMinutes < 15 * 60) return '未';
  if (totalMinutes >= 15 * 60 && totalMinutes < 17 * 60) return '申';
  if (totalMinutes >= 17 * 60 && totalMinutes < 19 * 60) return '酉';
  if (totalMinutes >= 19 * 60 && totalMinutes < 21 * 60) return '戌';
  if (totalMinutes >= 21 * 60 && totalMinutes < 23 * 60) return '亥';
  return '子';
}

// 时柱计算函数
function calculateHourPillar(hour, minute, dayGan) {
  const hourZhi = getHourZhi(hour, minute);
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  const hourGanStart = wushuDunTimeMap[dayGan];
  const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;
  
  return {
    gan: tiangan[hourGanIndex],
    zhi: hourZhi,
    ganzhi: tiangan[hourGanIndex] + hourZhi
  };
}

// 修正后的测试用例（使用正确的时辰判断）
const correctedTestCases = [
  // 2006年7月23日 癸丑日 - 实际测试数据
  { date: '2006-07-23', dayGan: '癸', time: '14:30', expected: '己未', description: '癸日未时(14:30)' },
  { date: '2006-07-23', dayGan: '癸', time: '19:30', expected: '壬戌', description: '癸日戌时(19:30)' },
  
  // 各日干的子时测试（最重要的基准）
  { date: '2024-01-01', dayGan: '甲', time: '23:30', expected: '甲子', description: '甲日子时' },
  { date: '2024-01-02', dayGan: '乙', time: '23:30', expected: '丙子', description: '乙日子时' },
  { date: '2024-01-03', dayGan: '丙', time: '23:30', expected: '戊子', description: '丙日子时' },
  { date: '2024-01-04', dayGan: '丁', time: '23:30', expected: '庚子', description: '丁日子时' },
  { date: '2024-01-05', dayGan: '戊', time: '23:30', expected: '壬子', description: '戊日子时' },
  { date: '2024-01-06', dayGan: '己', time: '23:30', expected: '甲子', description: '己日子时' },
  { date: '2024-01-07', dayGan: '庚', time: '23:30', expected: '丙子', description: '庚日子时' },
  { date: '2024-01-08', dayGan: '辛', time: '23:30', expected: '戊子', description: '辛日子时' },
  { date: '2024-01-09', dayGan: '壬', time: '23:30', expected: '庚子', description: '壬日子时' },
  { date: '2024-01-10', dayGan: '癸', time: '23:30', expected: '壬子', description: '癸日子时' },
  
  // 午时测试（11:00-13:00）
  { date: '2024-01-01', dayGan: '甲', time: '12:30', expected: '庚午', description: '甲日午时(12:30)' },
  { date: '2024-01-02', dayGan: '乙', time: '12:30', expected: '壬午', description: '乙日午时(12:30)' },
  { date: '2024-01-03', dayGan: '丙', time: '12:30', expected: '甲午', description: '丙日午时(12:30)' },
  
  // 未时测试（13:00-15:00）
  { date: '2024-01-01', dayGan: '甲', time: '14:30', expected: '辛未', description: '甲日未时(14:30)' },
  { date: '2024-01-02', dayGan: '乙', time: '14:30', expected: '癸未', description: '乙日未时(14:30)' },
  { date: '2024-01-03', dayGan: '丙', time: '14:30', expected: '乙未', description: '丙日未时(14:30)' },
  
  // 亥时测试（21:00-23:00）
  { date: '2024-01-01', dayGan: '甲', time: '22:30', expected: '癸亥', description: '甲日亥时(22:30)' },
  { date: '2024-01-02', dayGan: '乙', time: '22:30', expected: '乙亥', description: '乙日亥时(22:30)' },
  
  // 边界时间测试
  { date: '2024-01-10', dayGan: '甲', time: '00:59', expected: '甲子', description: '子时结束前(00:59)' },
  { date: '2024-01-10', dayGan: '甲', time: '01:00', expected: '乙丑', description: '丑时开始(01:00)' },
  { date: '2024-01-10', dayGan: '甲', time: '12:59', expected: '庚午', description: '午时结束前(12:59)' },
  { date: '2024-01-10', dayGan: '甲', time: '13:00', expected: '辛未', description: '未时开始(13:00)' },
  { date: '2024-01-10', dayGan: '甲', time: '22:59', expected: '癸亥', description: '亥时结束前(22:59)' },
  { date: '2024-01-10', dayGan: '甲', time: '23:00', expected: '甲子', description: '子时开始(23:00)' }
];

console.log('\n📋 开始修正后的测试:');
console.log('-'.repeat(40));

let totalTests = 0;
let passedTests = 0;
let failedTests = [];

correctedTestCases.forEach(test => {
  totalTests++;
  
  const [hour, minute] = test.time.split(':').map(Number);
  const calculated = calculateHourPillar(hour, minute, test.dayGan);
  const isCorrect = calculated.ganzhi === test.expected;
  
  if (isCorrect) {
    passedTests++;
    console.log(`✅ ${test.date} ${test.time} ${test.dayGan}日 → ${calculated.ganzhi} (${test.description})`);
  } else {
    failedTests.push(test);
    console.log(`❌ ${test.date} ${test.time} ${test.dayGan}日 → ${calculated.ganzhi} (期望: ${test.expected}) - ${test.description}`);
    
    // 详细分析
    const hourZhi = getHourZhi(hour, minute);
    const hourZhiIndex = dizhi.indexOf(hourZhi);
    const hourGanStart = wushuDunTimeMap[test.dayGan];
    const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;
    
    console.log(`     详细分析: 时支=${hourZhi}(${hourZhiIndex}), 起始干=${hourGanStart}, 时干索引=${hourGanIndex}, 时干=${tiangan[hourGanIndex]}`);
  }
});

console.log('\n📊 修正后测试结果:');
console.log('-'.repeat(40));
console.log(`总测试数: ${totalTests}`);
console.log(`通过数: ${passedTests}`);
console.log(`失败数: ${totalTests - passedTests}`);
console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

// 时辰边界验证
console.log('\n⏰ 时辰边界详细验证:');
console.log('-'.repeat(40));

const timeBoundaries = [
  { time: '23:00', expected: '子', description: '子时开始' },
  { time: '00:59', expected: '子', description: '子时结束前' },
  { time: '01:00', expected: '丑', description: '丑时开始' },
  { time: '11:00', expected: '午', description: '午时开始' },
  { time: '12:59', expected: '午', description: '午时结束前' },
  { time: '13:00', expected: '未', description: '未时开始' },
  { time: '14:59', expected: '未', description: '未时结束前' },
  { time: '15:00', expected: '申', description: '申时开始' },
  { time: '21:00', expected: '亥', description: '亥时开始' },
  { time: '22:59', expected: '亥', description: '亥时结束前' }
];

timeBoundaries.forEach(test => {
  const [hour, minute] = test.time.split(':').map(Number);
  const calculated = getHourZhi(hour, minute);
  const isCorrect = calculated === test.expected;
  const status = isCorrect ? '✅' : '❌';
  
  console.log(`${status} ${test.time} → ${calculated} (${test.description})`);
});

console.log('\n🎯 最终结论:');
console.log('-'.repeat(40));

if (passedTests === totalTests) {
  console.log('🎉 时柱计算算法完全正确！');
  console.log('✅ 所有测试用例都通过');
  console.log('✅ 五鼠遁公式实现正确');
  console.log('✅ 时辰边界判断准确');
  console.log('✅ 算法实现无误');
} else {
  console.log('⚠️ 时柱计算算法仍有问题');
  console.log(`❌ ${totalTests - passedTests}个测试用例失败`);
  if (failedTests.length > 0) {
    console.log('\n失败的测试用例:');
    failedTests.forEach(test => {
      console.log(`   ${test.date} ${test.time} ${test.dayGan}日 期望: ${test.expected}`);
    });
  }
}

console.log('\n🏁 修正测试完成');

// 导出测试结果
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    passedTests, 
    totalTests, 
    passRate: (passedTests / totalTests) * 100,
    failedTests,
    calculateHourPillar,
    getHourZhi
  };
}
