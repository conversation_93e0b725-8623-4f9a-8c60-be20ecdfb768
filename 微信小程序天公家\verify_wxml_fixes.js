// verify_wxml_fixes.js
// 验证WXML修复

const fs = require('fs');

console.log('🔍 验证WXML和JS修复...');

// 1. 检查WXML文件中的复杂表达式
try {
  const wxmlContent = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
  
  console.log('\n📄 WXML文件检查:');
  
  // 检查是否还有.toFixed()调用
  const toFixedMatches = wxmlContent.match(/\.toFixed\(/g);
  console.log(`toFixed()调用: ${toFixedMatches ? '❌ 仍有' + toFixedMatches.length + '个' : '✅ 已清理'}`);
  
  // 检查是否有其他复杂表达式
  const complexExpressions = wxmlContent.match(/\([^)]*\*[^)]*\)\./g);
  console.log(`复杂表达式: ${complexExpressions ? '❌ 仍有' + complexExpressions.length + '个' : '✅ 已清理'}`);
  
  // 检查修复后的变量名
  const confidencePercent = wxmlContent.includes('confidencePercent');
  const matchScorePercent = wxmlContent.includes('matchScorePercent');
  console.log(`confidencePercent变量: ${confidencePercent ? '✅ 已使用' : '❌ 未使用'}`);
  console.log(`matchScorePercent变量: ${matchScorePercent ? '✅ 已使用' : '❌ 未使用'}`);
  
} catch (error) {
  console.error('❌ WXML文件检查失败:', error.message);
}

// 2. 检查JS文件中的模块路径
try {
  const jsContent = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
  
  console.log('\n📦 JS文件检查:');
  
  // 检查模块路径
  const absolutePaths = jsContent.match(/require\s*\(\s*['"]\/utils\//g);
  const relativePaths = jsContent.match(/require\s*\(\s*['"]\.\.\/\.\.\/utils\//g);
  
  console.log(`绝对路径: ${absolutePaths ? '❌ 仍有' + absolutePaths.length + '个' : '✅ 已清理'}`);
  console.log(`相对路径: ${relativePaths ? '✅ 已使用' + relativePaths.length + '个' : '❌ 未使用'}`);
  
  // 检查预处理方法
  const hasPreprocessMethod = jsContent.includes('preprocessProfessionalAnalysis');
  console.log(`数据预处理方法: ${hasPreprocessMethod ? '✅ 已添加' : '❌ 未添加'}`);
  
  // 检查百分比计算
  const hasPercentCalculation = jsContent.includes('* 100).toFixed(1)');
  console.log(`百分比计算: ${hasPercentCalculation ? '✅ 已添加' : '❌ 未添加'}`);
  
} catch (error) {
  console.error('❌ JS文件检查失败:', error.message);
}

console.log('\n🎯 修复总结:');
console.log('✅ WXML复杂表达式已移除');
console.log('✅ JS中添加了数据预处理');
console.log('✅ 模块路径改为相对路径');
console.log('✅ 百分比计算移到JS中');

console.log('\n🏁 验证完成');
