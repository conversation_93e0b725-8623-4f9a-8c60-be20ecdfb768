<!--pages/profile/security/index.wxml-->
<view class="security-container">
  <view class="header">
    <view class="title">账号安全</view>
    <view class="subtitle">管理您的账号安全设置</view>
  </view>

  <view class="security-section">
    <view class="section-item" bindtap="changePassword">
      <view class="item-left">
        <image class="item-icon" src="/assets/icons/profile.png"></image>
        <text class="item-title">修改密码</text>
      </view>
      <view class="item-right">
        <image class="arrow-icon" src="/assets/icons/profile.png"></image>
      </view>
    </view>

    <view class="section-item" bindtap="exportData">
      <view class="item-left">
        <image class="item-icon" src="/assets/icons/profile.png"></image>
        <text class="item-title">导出数据</text>
      </view>
      <view class="item-right">
        <image class="arrow-icon" src="/assets/icons/profile.png"></image>
      </view>
    </view>

    <view class="section-item delete-account" bindtap="showDeleteAccount">
      <view class="item-left">
        <image class="item-icon" src="/assets/icons/profile.png"></image>
        <text class="item-title">永久注销</text>
      </view>
      <view class="item-right">
        <image class="arrow-icon" src="/assets/icons/profile.png"></image>
      </view>
    </view>
  </view>

  <view class="security-notice">
    <view class="notice-title">注销须知</view>
    <view class="notice-item">1. 注销账号需要短信验证和密码验证</view>
    <view class="notice-item">2. 注销后，咨询记录将保留30天，可导出备份</view>
    <view class="notice-item">3. 注销后，身份信息将立即匿名化</view>
    <view class="notice-item">4. 注销操作不可逆，请谨慎操作</view>
  </view>

  <!-- 导出进度条 -->
  <view class="export-progress" wx:if="{{showExportProgress}}">
    <view class="progress-title">正在导出数据...</view>
    <progress percent="{{exportProgress}}" stroke-width="4" color="#6C5CE7" />
    <view class="progress-text">{{exportProgress}}%</view>
  </view>
</view>

<!-- 注销账号弹窗 -->
<view class="modal" wx:if="{{showDeleteAccountModal}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">账号注销</text>
    </view>
    <view class="modal-body">
      <view class="verification-section">
        <view class="input-label">短信验证码</view>
        <view class="verification-input-group">
          <input class="verification-input" type="number" maxlength="6" placeholder="请输入验证码" bindinput="onVerificationCodeInput" />
          <button class="send-code-btn {{countdown > 0 ? 'disabled' : ''}}" bindtap="sendVerificationCode">
            {{countdown > 0 ? countdown + 's' : '获取验证码'}}
          </button>
        </view>
      </view>

      <view class="password-section">
        <view class="input-label">账号密码</view>
        <input class="password-input" password type="text" placeholder="请输入密码" bindinput="onPasswordInput" />
      </view>

      <view class="warning-text">注销后，您的账号将无法恢复，请谨慎操作</view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="cancelDeleteAccount">取消</button>
      <button class="confirm-btn delete-btn" bindtap="confirmDeleteAccount">确认注销</button>
    </view>
  </view>
</view>