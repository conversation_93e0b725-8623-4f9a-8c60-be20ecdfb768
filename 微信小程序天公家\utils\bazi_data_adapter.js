// utils/bazi_data_adapter.js
// 八字数据适配器 - 统一API数据格式转换

/**
 * 八字数据适配器类
 * 负责将不同来源的数据转换为统一的前端格式
 */
class BaziDataAdapter {
  constructor() {
    // 五行符号映射
    this.elementSymbols = {
      '木': '🌿',
      '火': '🔥', 
      '土': '🏔️',
      '金': '🔸',
      '水': '💧'
    };
    
    // 天干五行映射
    this.ganElements = {
      '甲': '木', '乙': '木',
      '丙': '火', '丁': '火',
      '戊': '土', '己': '土',
      '庚': '金', '辛': '金',
      '壬': '水', '癸': '水'
    };
    
    // 地支五行映射
    this.zhiElements = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木',
      '辰': '土', '巳': '火', '午': '火', '未': '土',
      '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
    
    // 十神映射
    this.tenGodsMap = {
      '比肩': '比肩', '劫财': '劫财', '食神': '食神', '伤官': '伤官',
      '偏财': '偏财', '正财': '正财', '七杀': '七杀', '正官': '正官',
      '偏印': '偏印', '正印': '正印'
    };
  }

  /**
   * 适配API返回的分析结果数据
   * @param {Object} apiData - API返回的原始数据
   * @returns {Object} 适配后的统一格式数据
   */
  adaptAnalysisResult(apiData) {
    console.log('🔄 开始适配API分析结果数据');
    
    try {
      const result = {
        // 基础信息
        basicInfo: this.extractBasicInfo(apiData),
        
        // 八字数据
        baziData: this.extractBaziData(apiData),
        
        // 五行分析
        wuxingData: this.extractWuxingData(apiData),
        
        // 格局用神
        patternData: this.extractPatternData(apiData),
        
        // 详细分析
        analysisData: this.extractAnalysisData(apiData),
        
        // 元数据
        metadata: this.extractMetadata(apiData)
      };
      
      console.log('✅ API数据适配完成');
      return result;
      
    } catch (error) {
      console.error('❌ API数据适配失败:', error);
      return this.getDefaultData();
    }
  }

  /**
   * 提取基础信息
   */
  extractBasicInfo(apiData) {
    const analysisResult = apiData.analysis_result || {};
    const basicAnalysis = analysisResult['基础分析'] || {};
    const basicInfo = basicAnalysis['基本信息'] || {};
    
    return {
      name: apiData.name || '用户',
      gender: apiData.gender || '男',
      birth_time: basicInfo['出生时间'] || '未知',
      solar_time: basicInfo['公历时间'] || '未知',
      lunar_time: basicInfo['农历时间'] || '未知',
      true_solar_time: basicInfo['真太阳时'] || '未知',
      location: apiData.location || '北京',
      solar_term: basicInfo['节气'] || '未知'
    };
  }

  /**
   * 提取八字数据
   */
  extractBaziData(apiData) {
    const analysisResult = apiData.analysis_result || {};
    const basicAnalysis = analysisResult['基础分析'] || {};
    const basicInfo = basicAnalysis['基本信息'] || {};
    
    // 获取四柱
    const fourPillars = basicInfo['四柱'] || '戊戌 丁卯 己丑 戊未';
    const pillars = fourPillars.split(' ');
    
    // 解析四柱
    const baziData = this.parseFourPillars(pillars);
    
    // 添加额外信息
    baziData.four_pillars = fourPillars;
    baziData.day_master = basicInfo['日主'] || pillars[2]?.charAt(0) || '己';
    baziData.day_master_element = this.ganElements[baziData.day_master] || '土';
    
    // 添加十神信息
    this.addTenGodsInfo(baziData, analysisResult);
    
    return baziData;
  }

  /**
   * 解析四柱数据
   */
  parseFourPillars(pillars) {
    const result = {};
    const positions = ['year', 'month', 'day', 'hour'];
    
    positions.forEach((pos, index) => {
      const pillar = pillars[index] || '戊戌';
      const gan = pillar.charAt(0);
      const zhi = pillar.charAt(1);
      
      result[`${pos}_pillar`] = pillar;
      result[`${pos}_gan`] = gan;
      result[`${pos}_zhi`] = zhi;
      result[`${pos}_gan_element_symbol`] = this.elementSymbols[this.ganElements[gan]] || '🏔️';
      result[`${pos}_zhi_element_symbol`] = this.elementSymbols[this.zhiElements[zhi]] || '🏔️';
    });

    return result;
  }

  /**
   * 添加十神信息
   */
  addTenGodsInfo(baziData, analysisResult) {
    const dayMaster = baziData.day_gan;
    const positions = ['year', 'month', 'day', 'hour'];
    
    positions.forEach(pos => {
      const gan = baziData[`${pos}_gan`];
      baziData[`${pos}_star`] = this.calculateTenGod(gan, dayMaster);
    });
    
    // 特殊处理日柱
    baziData.day_star = '日主';
  }

  /**
   * 计算十神
   */
  calculateTenGod(gan, dayMaster) {
    const tenGods = ['比肩', '劫财', '食神', '伤官', '偏财', '正财', '七杀', '正官', '偏印', '正印'];
    const ganIndex = '甲乙丙丁戊己庚辛壬癸'.indexOf(gan);
    const dayIndex = '甲乙丙丁戊己庚辛壬癸'.indexOf(dayMaster);
    
    if (ganIndex === -1 || dayIndex === -1) return '未知';
    
    const diff = (ganIndex - dayIndex + 10) % 10;
    return tenGods[diff] || '未知';
  }

  /**
   * 提取五行数据
   */
  extractWuxingData(apiData) {
    const analysisResult = apiData.analysis_result || {};
    const basicAnalysis = analysisResult['基础分析'] || {};
    const wuxingAnalysis = basicAnalysis['五行分析'] || {};
    
    const strengthMap = wuxingAnalysis['五行强弱'] || {};
    const scoreMap = wuxingAnalysis['五行得分'] || {};
    
    return [
      {
        name: '木',
        symbol: '🌿',
        strength: strengthMap['木'] || '中等',
        percentage: Math.min((scoreMap['木'] || 20) * 2, 100),
        score: scoreMap['木'] || 20
      },
      {
        name: '火',
        symbol: '🔥',
        strength: strengthMap['火'] || '中等',
        percentage: Math.min((scoreMap['火'] || 20) * 2, 100),
        score: scoreMap['火'] || 20
      },
      {
        name: '土',
        symbol: '🏔️',
        strength: strengthMap['土'] || '中等',
        percentage: Math.min((scoreMap['土'] || 20) * 2, 100),
        score: scoreMap['土'] || 20
      },
      {
        name: '金',
        symbol: '🔸',
        strength: strengthMap['金'] || '中等',
        percentage: Math.min((scoreMap['金'] || 20) * 2, 100),
        score: scoreMap['金'] || 20
      },
      {
        name: '水',
        symbol: '💧',
        strength: strengthMap['水'] || '中等',
        percentage: Math.min((scoreMap['水'] || 20) * 2, 100),
        score: scoreMap['水'] || 20
      }
    ];
  }

  /**
   * 提取格局用神数据
   */
  extractPatternData(apiData) {
    const analysisResult = apiData.analysis_result || {};
    const professionalAnalysis = analysisResult['专业分析'] || {};
    const detailedAnalysis = professionalAnalysis['专业细盘'] || {};
    
    const patternAnalysis = detailedAnalysis['格局分析'] || {};
    const yongshenAnalysis = detailedAnalysis['用神分析'] || {};
    
    return {
      pattern_analysis: {
        main_pattern: patternAnalysis['主格局'] || '普通格局',
        pattern_type: patternAnalysis['格局类型'] || '一般',
        pattern_strength: patternAnalysis['格局强度'] || 0.5,
        pattern_description: patternAnalysis['格局说明'] || '格局分析中...'
      },
      yongshen_analysis: {
        primary_yongshen: yongshenAnalysis['用神'] || '待定',
        secondary_yongshen: yongshenAnalysis['喜神'] || '无',
        avoid_elements: [yongshenAnalysis['忌神'] || '待定'],
        yongshen_description: yongshenAnalysis['用神说明'] || '用神分析中...'
      }
    };
  }

  /**
   * 提取详细分析数据
   */
  extractAnalysisData(apiData) {
    const analysisResult = apiData.analysis_result || {};
    
    return {
      systemInfo: { 
        name: '天公师父',
        version: '2.0.0',
        analysis_time: new Date().toLocaleString()
      },
      personality: this.extractPersonalityAnalysis(analysisResult),
      career: this.extractCareerAnalysis(analysisResult),
      wealth: this.extractWealthAnalysis(analysisResult),
      relationship: this.extractRelationshipAnalysis(analysisResult),
      health: this.extractHealthAnalysis(analysisResult)
    };
  }

  /**
   * 提取各类分析内容
   */
  extractPersonalityAnalysis(analysisResult) {
    const professionalAnalysis = analysisResult['专业分析'] || {};
    return professionalAnalysis['性格分析'] || '基于八字分析的性格特征正在生成中...';
  }

  extractCareerAnalysis(analysisResult) {
    const professionalAnalysis = analysisResult['专业分析'] || {};
    return professionalAnalysis['事业分析'] || '基于八字分析的事业发展建议正在生成中...';
  }

  extractWealthAnalysis(analysisResult) {
    const professionalAnalysis = analysisResult['专业分析'] || {};
    return professionalAnalysis['财运分析'] || '基于八字分析的财运状况正在生成中...';
  }

  extractRelationshipAnalysis(analysisResult) {
    const professionalAnalysis = analysisResult['专业分析'] || {};
    return professionalAnalysis['感情分析'] || '基于八字分析的感情婚姻建议正在生成中...';
  }

  extractHealthAnalysis(analysisResult) {
    const professionalAnalysis = analysisResult['专业分析'] || {};
    return professionalAnalysis['健康分析'] || '基于八字分析的健康状况正在生成中...';
  }

  /**
   * 提取元数据
   */
  extractMetadata(apiData) {
    return {
      source: 'api',
      timestamp: new Date().toISOString(),
      confidence: 0.95,
      api_version: apiData.version || '2.0.0',
      analysis_id: apiData.id || 'unknown'
    };
  }

  /**
   * 获取默认数据（当API数据不可用时）
   */
  getDefaultData() {
    console.log('🔄 使用默认数据');
    
    return {
      basicInfo: {
        name: '用户',
        gender: '男',
        birth_time: '计算中...',
        solar_time: '计算中...',
        lunar_time: '计算中...',
        true_solar_time: '计算中...',
        location: '北京',
        solar_term: '计算中...'
      },
      baziData: {
        four_pillars: '戊戌 丁卯 己丑 戊未',
        year_pillar: '戊戌', month_pillar: '丁卯', day_pillar: '己丑', hour_pillar: '戊未',
        year_gan: '戊', year_zhi: '戌', month_gan: '丁', month_zhi: '卯',
        day_gan: '己', day_zhi: '丑', hour_gan: '戊', hour_zhi: '未',
        year_gan_element_symbol: '🏔️', year_zhi_element_symbol: '🏔️',
        month_gan_element_symbol: '🔥', month_zhi_element_symbol: '🌿',
        day_gan_element_symbol: '🏔️', day_zhi_element_symbol: '🏔️',
        hour_gan_element_symbol: '🏔️', hour_zhi_element_symbol: '🏔️',
        year_star: '偏印', month_star: '食神', day_star: '日主', hour_star: '比肩',
        day_master: '己', day_master_element: '土'
      },
      wuxingData: [
        { name: '木', symbol: '🌿', strength: '中等', percentage: 20, score: 10 },
        { name: '火', symbol: '🔥', strength: '偏强', percentage: 30, score: 15 },
        { name: '土', symbol: '🏔️', strength: '偏强', percentage: 25, score: 12 },
        { name: '金', symbol: '🔸', strength: '偏弱', percentage: 15, score: 8 },
        { name: '水', symbol: '💧', strength: '偏弱', percentage: 10, score: 5 }
      ],
      patternData: {
        pattern_analysis: {
          main_pattern: '普通格局',
          pattern_type: '一般',
          pattern_strength: 0.5,
          pattern_description: '格局分析中...'
        },
        yongshen_analysis: {
          primary_yongshen: '待定',
          secondary_yongshen: '无',
          avoid_elements: ['待定'],
          yongshen_description: '用神分析中...'
        }
      },
      analysisData: {
        systemInfo: { name: '天公师父', version: '2.0.0', analysis_time: new Date().toLocaleString() },
        personality: '正在分析性格特征...',
        career: '正在分析事业发展...',
        wealth: '正在分析财运状况...',
        relationship: '正在分析感情状况...',
        health: '正在分析健康状况...'
      },
      metadata: {
        source: 'default',
        timestamp: new Date().toISOString(),
        confidence: 0.5,
        api_version: '2.0.0',
        analysis_id: 'default'
      }
    };
  }
}

module.exports = BaziDataAdapter;
