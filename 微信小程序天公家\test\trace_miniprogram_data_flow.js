/**
 * 追踪小程序数据流程
 * 解释为什么会出现"计算正确但显示错误"的现象
 */

// 小程序数据流程分析
function traceMiniProgramDataFlow() {
  console.log('🧪 ===== 小程序数据流程追踪 =====\n');
  
  console.log('📱 小程序架构说明:');
  console.log('  ❌ 没有传统意义的"后端服务器"');
  console.log('  ✅ 所有计算都在前端进行（用户手机上）');
  console.log('  ✅ index.js = 前端逻辑层');
  console.log('  ✅ index.wxml = 前端视图层');
  console.log('  ✅ 数据通过 setData() 从逻辑层传递到视图层');
  
  console.log('\n🔄 完整数据流程:');
  console.log('  第1步: 用户输入八字信息');
  console.log('  第2步: index.js 中的计算函数处理数据');
  console.log('  第3步: 计算结果通过 setData() 设置到页面数据');
  console.log('  第4步: index.wxml 读取页面数据并显示');
  
  console.log('\n🚨 "计算正确但显示错误"的可能原因:');
  
  console.log('\n原因1: setData() 数据传递问题');
  console.log('  - 计算函数返回正确结果');
  console.log('  - 但 setData() 时数据被错误处理');
  console.log('  - 导致传递到视图层的数据有误');
  
  console.log('\n原因2: 数据格式转换问题');
  console.log('  - 计算结果是数字类型');
  console.log('  - setData() 时被错误转换为字符串');
  console.log('  - 视图层显示时出现格式问题');
  
  console.log('\n原因3: 视图层模板逻辑问题');
  console.log('  - 逻辑层数据正确');
  console.log('  - 但 wxml 模板中的显示逻辑有误');
  console.log('  - 导致正确数据被错误显示');
  
  console.log('\n原因4: 数据覆盖问题');
  console.log('  - 计算函数产生正确结果');
  console.log('  - 但后续代码又覆盖了正确数据');
  console.log('  - 最终传递给视图层的是错误数据');
  
  console.log('\n🔍 具体问题分析:');
  
  // 模拟当前的问题场景
  console.log('\n场景: 用户看到"100% / 60% ✅ 达标"');
  console.log('  可能的数据流程:');
  console.log('  1. 计算函数: calculateUnifiedEnergy() 返回 46.0%');
  console.log('  2. 数据处理: 某个环节将 46.0% 变成了 100%');
  console.log('  3. setData(): 将错误的 100% 传递给视图层');
  console.log('  4. 视图显示: wxml 显示 "100% / 60%"');
  
  console.log('\n🔧 排查步骤:');
  console.log('  步骤1: 检查计算函数的返回值');
  console.log('  步骤2: 检查 setData() 调用时的数据');
  console.log('  步骤3: 检查视图层的数据绑定');
  console.log('  步骤4: 检查是否有数据覆盖');
  
  // 模拟数据流程追踪
  console.log('\n🧪 数据流程模拟追踪:');
  
  // 第1步: 计算函数
  const calculationResult = {
    marriage_energy: {
      actual: 46.0,
      percentage: '46.0%',
      required: 45,
      met: true,
      completion_ratio: '46.0% / 45.0%'
    }
  };
  console.log('  第1步 - 计算函数结果:');
  console.log(`    actual: ${calculationResult.marriage_energy.actual}`);
  console.log(`    required: ${calculationResult.marriage_energy.required}`);
  console.log(`    met: ${calculationResult.marriage_energy.met}`);
  
  // 第2步: 数据提取和处理
  let userCurrentEnergy = calculationResult.marriage_energy.actual; // 46.0
  let requiredThreshold = calculationResult.marriage_energy.required; // 45
  let actuallyMet = calculationResult.marriage_energy.met; // true
  
  console.log('  第2步 - 数据提取:');
  console.log(`    userCurrentEnergy: ${userCurrentEnergy}`);
  console.log(`    requiredThreshold: ${requiredThreshold}`);
  console.log(`    actuallyMet: ${actuallyMet}`);
  
  // 第3步: 数据处理（可能出错的地方）
  const clampedUserEnergy = Math.max(userCurrentEnergy, 0);
  let finalRequiredThreshold = requiredThreshold;
  if (requiredThreshold < 1) {
    finalRequiredThreshold = requiredThreshold * 100;
  }
  const clampedRequiredThreshold = Math.min(Math.max(finalRequiredThreshold, 0), 100);
  
  console.log('  第3步 - 数据处理:');
  console.log(`    clampedUserEnergy: ${clampedUserEnergy}`);
  console.log(`    finalRequiredThreshold: ${finalRequiredThreshold}`);
  console.log(`    clampedRequiredThreshold: ${clampedRequiredThreshold}`);
  
  // 第4步: setData 数据准备
  const thresholds = {};
  thresholds['marriage_current_energy'] = Math.round(clampedUserEnergy * 10) / 10;
  thresholds['marriage_required_threshold'] = Math.round(clampedRequiredThreshold * 10) / 10;
  thresholds['marriage_met'] = actuallyMet;
  
  console.log('  第4步 - setData 数据:');
  console.log(`    marriage_current_energy: ${thresholds['marriage_current_energy']}`);
  console.log(`    marriage_required_threshold: ${thresholds['marriage_required_threshold']}`);
  console.log(`    marriage_met: ${thresholds['marriage_met']}`);
  
  // 第5步: 视图层显示
  console.log('  第5步 - 视图层显示:');
  console.log(`    预期显示: ${thresholds['marriage_current_energy']}% / ${thresholds['marriage_required_threshold']}% ${thresholds['marriage_met'] ? '✅' : '❌'}`);
  
  // 问题诊断
  console.log('\n🎯 问题诊断:');
  
  if (thresholds['marriage_current_energy'] === 100) {
    console.log('  ❌ 问题发现: 用户能量被错误设置为100%');
    console.log('  可能原因: 数据处理逻辑有误');
  } else {
    console.log('  ✅ 用户能量数据正确');
  }
  
  if (thresholds['marriage_required_threshold'] === 60) {
    console.log('  ❌ 问题发现: 阈值被错误设置为60%');
    console.log('  可能原因: 阈值配置没有生效');
  } else {
    console.log('  ✅ 阈值数据正确');
  }
  
  console.log('\n💡 解决方案:');
  console.log('  1. 在计算函数中添加 console.log 追踪数据');
  console.log('  2. 在 setData 调用前添加 console.log 检查数据');
  console.log('  3. 检查是否有多个地方调用 setData 覆盖数据');
  console.log('  4. 确认视图层的数据绑定是否正确');
  
  console.log('\n🔍 关键检查点:');
  console.log('  检查点1: calculateUnifiedEnergy 函数的返回值');
  console.log('  检查点2: 数据提取时的 parseFloat 处理');
  console.log('  检查点3: setData 调用时的数据内容');
  console.log('  检查点4: wxml 中的数据绑定表达式');
  
  return {
    expectedUserEnergy: thresholds['marriage_current_energy'],
    expectedThreshold: thresholds['marriage_required_threshold'],
    expectedMet: thresholds['marriage_met'],
    dataFlowCorrect: thresholds['marriage_current_energy'] !== 100 && thresholds['marriage_required_threshold'] !== 60
  };
}

// 运行数据流程追踪
traceMiniProgramDataFlow();
