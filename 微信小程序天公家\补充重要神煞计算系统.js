/**
 * 补充重要神煞计算系统
 * 添加太极贵人、禄神、亡神等专业神煞
 * 基于权威古籍和网络资料的精确计算方法
 */

// 测试数据：2021年6月24日 19:30 北京时间
const TEST_BAZI = [
  { gan: '辛', zhi: '丑' }, // 年柱
  { gan: '甲', zhi: '午' }, // 月柱
  { gan: '癸', zhi: '卯' }, // 日柱
  { gan: '壬', zhi: '戌' }  // 时柱
];

// 补充神煞计算表格
const SupplementaryShenshaTable = {
  // 1. 太极贵人计算表（基于日干）
  taijiTable: {
    // 甲乙生人子午中，丙丁鸡兔定亨通，戊己两干临四季，庚辛寅亥禄丰隆，壬癸巳申偏喜美
    '甲': ['子', '午'], '乙': ['子', '午'],  // 甲乙生人子午中
    '丙': ['卯', '酉'], '丁': ['卯', '酉'],  // 丙丁鸡兔定亨通
    '戊': ['辰', '戌', '丑', '未'], '己': ['辰', '戌', '丑', '未'],  // 戊己两干临四季
    '庚': ['寅', '亥'], '辛': ['寅', '亥'],  // 庚辛寅亥禄丰隆
    '壬': ['巳', '申'], '癸': ['巳', '申']   // 壬癸巳申偏喜美
  },

  // 2. 禄神计算表（基于日干）
  lushenTable: {
    // 甲禄在寅，乙禄在卯，丙戊禄在巳，丁己禄在午，庚禄在申，辛禄在酉，壬禄在亥，癸禄在子
    '甲': '寅', '乙': '卯', '丙': '巳', '丁': '午', '戊': '巳',
    '己': '午', '庚': '申', '辛': '酉', '壬': '亥', '癸': '子'
  },

  // 3. 亡神计算表（基于年支或日支）
  wangshenTable: {
    // 寅午戌见巳，亥卯未见寅，巳酉丑见申，申子辰见亥
    '寅': '巳', '午': '巳', '戌': '巳',  // 寅午戌见巳
    '亥': '寅', '卯': '寅', '未': '寅',  // 亥卯未见寅
    '巳': '申', '酉': '申', '丑': '申',  // 巳酉丑见申
    '申': '亥', '子': '亥', '辰': '亥'   // 申子辰见亥
  },

  // 4. 学堂计算表（基于日干）
  xuetangTable: {
    // 甲见巳，乙见午，丙见申，丁见酉，戊见申，己见酉，庚见亥，辛见子，壬见寅，癸见卯
    '甲': '巳', '乙': '午', '丙': '申', '丁': '酉', '戊': '申',
    '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
  },

  // 5. 词馆计算表（基于日干）
  ciguanTable: {
    // 甲见午，乙见巳，丙见酉，丁见申，戊见酉，己见申，庚见子，辛见亥，壬见卯，癸见寅
    '甲': '午', '乙': '巳', '丙': '酉', '丁': '申', '戊': '酉',
    '己': '申', '庚': '子', '辛': '亥', '壬': '卯', '癸': '寅'
  },

  // 6. 金舆计算表（基于日干）
  jinyuTable: {
    // 甲龙乙蛇丙戊羊，丁己猴歌庚犬方，辛猪壬牛癸逢虎，凡人遇此福气昌
    '甲': '辰', '乙': '巳', '丙': '未', '丁': '申', '戊': '未',
    '己': '申', '庚': '戌', '辛': '亥', '壬': '丑', '癸': '寅'
  }
};

// 补充神煞计算系统
const SupplementaryShenshaSystem = {
  // 1. 太极贵人计算（基于日干）
  calculateTaiji: function(dayGan, fourPillars) {
    const taijiZhi = SupplementaryShenshaTable.taijiTable[dayGan];
    if (!taijiZhi) return [];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      if (taijiZhi.includes(pillar.zhi)) {
        result.push(`${pillarNames[index]}柱太极贵人`);
      }
    });
    
    return result;
  },

  // 2. 禄神计算（基于日干）
  calculateLushen: function(dayGan, fourPillars) {
    const lushenZhi = SupplementaryShenshaTable.lushenTable[dayGan];
    if (!lushenZhi) return [];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === lushenZhi) {
        result.push(`${pillarNames[index]}柱禄神`);
      }
    });
    
    return result;
  },

  // 3. 亡神计算（基于年支或日支）
  calculateWangshen: function(yearZhi, dayZhi, fourPillars) {
    const wangshenFromYear = SupplementaryShenshaTable.wangshenTable[yearZhi];
    const wangshenFromDay = SupplementaryShenshaTable.wangshenTable[dayZhi];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      // 基于年支的亡神
      if (wangshenFromYear && pillar.zhi === wangshenFromYear) {
        result.push(`${pillarNames[index]}柱亡神`);
      }
      // 基于日支的亡神（避免重复）
      if (wangshenFromDay && pillar.zhi === wangshenFromDay && wangshenFromDay !== wangshenFromYear) {
        result.push(`${pillarNames[index]}柱亡神`);
      }
    });
    
    return result;
  },

  // 4. 学堂计算（基于日干）
  calculateXuetang: function(dayGan, fourPillars) {
    const xuetangZhi = SupplementaryShenshaTable.xuetangTable[dayGan];
    if (!xuetangZhi) return [];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === xuetangZhi) {
        result.push(`${pillarNames[index]}柱学堂`);
      }
    });
    
    return result;
  },

  // 5. 词馆计算（基于日干）
  calculateCiguan: function(dayGan, fourPillars) {
    const ciguanZhi = SupplementaryShenshaTable.ciguanTable[dayGan];
    if (!ciguanZhi) return [];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === ciguanZhi) {
        result.push(`${pillarNames[index]}柱词馆`);
      }
    });
    
    return result;
  },

  // 6. 金舆计算（基于日干）
  calculateJinyu: function(dayGan, fourPillars) {
    const jinyuZhi = SupplementaryShenshaTable.jinyuTable[dayGan];
    if (!jinyuZhi) return [];
    
    const result = [];
    const pillarNames = ['年', '月', '日', '时'];
    
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === jinyuZhi) {
        result.push(`${pillarNames[index]}柱金舆`);
      }
    });
    
    return result;
  },

  // 综合计算补充神煞
  calculateSupplementaryShenshas: function(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const yearZhi = fourPillars[0].zhi;
    const dayZhi = fourPillars[2].zhi;
    
    const results = [];
    
    // 1. 太极贵人
    results.push(...this.calculateTaiji(dayGan, fourPillars));
    
    // 2. 禄神
    results.push(...this.calculateLushen(dayGan, fourPillars));
    
    // 3. 亡神
    results.push(...this.calculateWangshen(yearZhi, dayZhi, fourPillars));
    
    // 4. 学堂
    results.push(...this.calculateXuetang(dayGan, fourPillars));
    
    // 5. 词馆
    results.push(...this.calculateCiguan(dayGan, fourPillars));
    
    // 6. 金舆
    results.push(...this.calculateJinyu(dayGan, fourPillars));
    
    return results;
  }
};

console.log('=== 补充重要神煞计算系统 ===');
console.log('');

console.log('📊 测试数据：');
console.log(`年柱：${TEST_BAZI[0].gan}${TEST_BAZI[0].zhi}`);
console.log(`月柱：${TEST_BAZI[1].gan}${TEST_BAZI[1].zhi}`);
console.log(`日柱：${TEST_BAZI[2].gan}${TEST_BAZI[2].zhi}`);
console.log(`时柱：${TEST_BAZI[3].gan}${TEST_BAZI[3].zhi}`);
console.log('');

// 计算补充神煞
const supplementaryResults = SupplementaryShenshaSystem.calculateSupplementaryShenshas(TEST_BAZI);

console.log('🔮 补充神煞计算结果：');
console.log(supplementaryResults.length > 0 ? supplementaryResults.join('、') : '无');
console.log('');

// 详细验证每个神煞
console.log('🎯 详细验证过程：');
console.log('');

console.log('1. 太极贵人验证：');
console.log('   计算方法：甲乙生人子午中，丙丁鸡兔定亨通，戊己两干临四季，庚辛寅亥禄丰隆，壬癸巳申偏喜美');
console.log(`   日干癸 → 太极贵人巳申 → 无匹配 → ❌ 无太极贵人`);

console.log('');
console.log('2. 禄神验证：');
console.log('   计算方法：甲禄在寅，乙禄在卯，丙戊禄在巳，丁己禄在午，庚禄在申，辛禄在酉，壬禄在亥，癸禄在子');
console.log(`   日干癸 → 禄神子 → 无匹配 → ❌ 无禄神`);

console.log('');
console.log('3. 亡神验证：');
console.log('   计算方法：寅午戌见巳，亥卯未见寅，巳酉丑见申，申子辰见亥');
console.log(`   年支丑 → 亡神申 → 无匹配`);
console.log(`   日支卯 → 亡神寅 → 无匹配 → ❌ 无亡神`);

console.log('');
console.log('4. 学堂验证：');
console.log('   计算方法：甲见巳，乙见午，丙见申，丁见酉，戊见申，己见酉，庚见亥，辛见子，壬见寅，癸见卯');
console.log(`   日干癸 → 学堂卯 → 日柱卯匹配 → ✅ 日柱学堂`);

console.log('');
console.log('5. 词馆验证：');
console.log('   计算方法：甲见午，乙见巳，丙见酉，丁见申，戊见酉，己见申，庚见子，辛见亥，壬见卯，癸见寅');
console.log(`   日干癸 → 词馆寅 → 无匹配 → ❌ 无词馆`);

console.log('');
console.log('6. 金舆验证：');
console.log('   计算方法：甲龙乙蛇丙戊羊，丁己猴歌庚犬方，辛猪壬牛癸逢虎');
console.log(`   日干癸 → 金舆寅 → 无匹配 → ❌ 无金舆`);

console.log('');

// 测试其他案例验证计算方法
console.log('🧪 测试其他案例验证：');
console.log('');

// 测试太极贵人案例
const testTaijiCase = [
  { gan: '甲', zhi: '寅' },
  { gan: '乙', zhi: '丑' },
  { gan: '甲', zhi: '子' },  // 甲日干，太极贵人子午
  { gan: '丁', zhi: '午' }   // 时柱午匹配
];

const taijiTestResult = SupplementaryShenshaSystem.calculateTaiji('甲', testTaijiCase);
console.log(`太极贵人测试（甲见子午）：${taijiTestResult.join('、') || '无'}`);
console.log('✅ 太极贵人计算正确：甲乙生人子午中，日柱子、时柱午匹配！');

// 测试禄神案例
const testLushenCase = [
  { gan: '甲', zhi: '寅' },  // 甲禄在寅
  { gan: '乙', zhi: '丑' },
  { gan: '甲', zhi: '子' },  // 甲日干
  { gan: '丁', zhi: '戌' }
];

const lushenTestResult = SupplementaryShenshaSystem.calculateLushen('甲', testLushenCase);
console.log(`禄神测试（甲禄在寅）：${lushenTestResult.join('、') || '无'}`);
console.log('✅ 禄神计算正确：甲禄在寅，年柱寅匹配！');

// 测试亡神案例
const testWangshenCase = [
  { gan: '甲', zhi: '寅' },  // 年支寅
  { gan: '乙', zhi: '丑' },
  { gan: '甲', zhi: '午' },  // 日支午
  { gan: '丁', zhi: '巳' }   // 寅午戌见巳为亡神
];

const wangshenTestResult = SupplementaryShenshaSystem.calculateWangshen('寅', '午', testWangshenCase);
console.log(`亡神测试（寅午戌见巳）：${wangshenTestResult.join('、') || '无'}`);
console.log('✅ 亡神计算正确：寅午戌见巳，时柱巳匹配！');

console.log('');
console.log('🏆 补充神煞系统建立完成！');
console.log('✅ 太极贵人：主聪明好学，位置崇高');
console.log('✅ 禄神：主财禄丰厚，衣食无忧');
console.log('✅ 亡神：主失落破败，需要注意');
console.log('✅ 学堂：主学业有成，文采出众');
console.log('✅ 词馆：主文学才华，口才出众');
console.log('✅ 金舆：主富贵荣华，出行顺利');
console.log('');
console.log('📈 神煞系统专业性再次提升！');
console.log('🎯 发现新匹配：日柱学堂！');
console.log('🌟 神煞种类更加丰富完整！');
