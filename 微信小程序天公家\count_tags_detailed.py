#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def count_tags_detailed(file_path):
    """详细统计标签数量"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content_no_comments = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 查找各种类型的view标签
    view_opens = re.findall(r'<view(?:\s[^>]*)?(?<!/)>', content_no_comments)
    view_closes = re.findall(r'</view>', content_no_comments)
    view_self_closing = re.findall(r'<view(?:\s[^>]*)?/>', content_no_comments)
    
    print(f"📊 详细标签统计:")
    print(f"  <view ...> 开始标签: {len(view_opens)}")
    print(f"  </view> 结束标签: {len(view_closes)}")
    print(f"  <view .../> 自闭合标签: {len(view_self_closing)}")
    
    # 计算实际需要的结束标签数量
    needed_closes = len(view_opens)
    actual_closes = len(view_closes)
    
    print(f"\n🔍 平衡分析:")
    print(f"  需要的结束标签: {needed_closes}")
    print(f"  实际的结束标签: {actual_closes}")
    print(f"  差值: {needed_closes - actual_closes}")
    
    if needed_closes == actual_closes:
        print(f"  ✅ 标签完全平衡")
    else:
        print(f"  ❌ 缺少 {needed_closes - actual_closes} 个结束标签")
    
    # 查找一些示例
    lines = content.split('\n')
    print(f"\n📋 查找一些开始标签示例:")
    count = 0
    for line_num, line in enumerate(lines, 1):
        if '<view' in line and not '</view>' in line and not '/>' in line:
            if count < 5:
                print(f"  第{line_num}行: {line.strip()}")
                count += 1

if __name__ == "__main__":
    count_tags_detailed("pages/bazi-result/index.wxml")
