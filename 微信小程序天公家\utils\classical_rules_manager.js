/**
 * 古籍规则管理器
 * 负责加载、查询和匹配古籍规则数据
 * 第二阶段：集成真实古籍内容
 */

class ClassicalRulesManager {
  constructor() {
    this.coreRules = null;
    this.completeRules = null;
    this.wuxingRules = null;
    this.isLoaded = false;
    this.loadPromise = null;

    // 🚀 新增：高级匹配算法
    this.advancedMatcher = null;
    this.matchingCache = new Map();
    this.performanceStats = {
      totalQueries: 0,
      cacheHits: 0,
      avgMatchTime: 0
    };

    // 🚀 新增：数据扩展管理器
    this.dataExpansionManager = null;

    // 🚀 新增：性能优化器
    this.performanceOptimizer = null;

    // 🚀 新增：分层规则管理器
    this.layeredRulesManager = null;
  }

  /**
   * 初始化加载所有古籍规则数据
   */
  async initialize() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = this._loadAllRules();
    return this.loadPromise;
  }

  /**
   * 加载所有规则数据
   */
  async _loadAllRules() {
    try {
      console.log('🔄 开始加载古籍规则数据...');

      // 并行加载三个数据文件
      const [coreResponse, wuxingResponse] = await Promise.all([
        fetch('./classical_rules_core_261.json'),
        fetch('./五行精纪集成规则.json')
      ]);

      if (!coreResponse.ok) {
        throw new Error(`核心规则加载失败: ${coreResponse.status}`);
      }
      if (!wuxingResponse.ok) {
        throw new Error(`五行精纪规则加载失败: ${wuxingResponse.status}`);
      }

      // 解析JSON数据
      this.coreRules = await coreResponse.json();
      this.wuxingRules = await wuxingResponse.json();

      this.isLoaded = true;

      // 🚀 初始化所有优化组件
      await this.initializeOptimizationComponents();

      // 🚀 初始化分层规则管理器
      await this.initializeLayeredRulesManager();

      console.log('✅ 古籍规则数据加载完成');
      console.log(`   📚 核心规则: ${this.coreRules.rules.length}条`);
      console.log(`   📚 五行精纪: ${this.wuxingRules.rules.length}条`);
      console.log('✅ 所有优化组件已初始化');

      return true;
    } catch (error) {
      console.error('❌ 古籍规则加载失败:', error);
      throw error;
    }
  }

  /**
   * 根据分类查询规则
   */
  getRulesByCategory(category) {
    if (!this.isLoaded) {
      throw new Error('古籍规则尚未加载，请先调用 initialize()');
    }

    const coreResults = this.coreRules.rules.filter(rule => 
      rule.category === category
    );

    const wuxingResults = this.wuxingRules.rules.filter(rule => 
      rule.category === category
    );

    return [...coreResults, ...wuxingResults];
  }

  /**
   * 根据古籍来源查询规则
   */
  getRulesBySource(bookSource) {
    if (!this.isLoaded) {
      throw new Error('古籍规则尚未加载，请先调用 initialize()');
    }

    return this.coreRules.rules.filter(rule => 
      rule.book_source === bookSource
    );
  }

  /**
   * 根据格局名称查询规则
   */
  getRulesByPattern(patternName) {
    if (!this.isLoaded) {
      throw new Error('古籍规则尚未加载，请先调用 initialize()');
    }

    const coreResults = this.coreRules.rules.filter(rule => 
      rule.pattern_name === patternName || 
      rule.pattern_name.includes(patternName)
    );

    const wuxingResults = this.wuxingRules.rules.filter(rule => 
      rule.title && rule.title.includes(patternName)
    );

    return [...coreResults, ...wuxingResults];
  }

  /**
   * 根据置信度筛选规则
   */
  getRulesByConfidence(minConfidence = 0.9) {
    if (!this.isLoaded) {
      throw new Error('古籍规则尚未加载，请先调用 initialize()');
    }

    const coreResults = this.coreRules.rules.filter(rule => 
      rule.confidence >= minConfidence
    );

    const wuxingResults = this.wuxingRules.rules.filter(rule => 
      rule.confidence >= minConfidence
    );

    return [...coreResults, ...wuxingResults];
  }

  /**
   * 🚀 智能匹配相关规则（升级版）
   * 使用高级匹配算法根据四柱特征匹配最相关的古籍规则
   */
  findRelevantRules(fourPillars, analysisType = 'comprehensive', options = {}) {
    if (!this.isLoaded) {
      throw new Error('古籍规则尚未加载，请先调用 initialize()');
    }

    this.performanceStats.totalQueries++;
    const startTime = Date.now();

    // 🚀 检查缓存
    const cacheKey = this.generateCacheKey(fourPillars, analysisType, options);
    if (this.matchingCache.has(cacheKey)) {
      this.performanceStats.cacheHits++;
      console.log('🎯 使用缓存结果');
      return this.matchingCache.get(cacheKey);
    }

    // 🚀 使用高级匹配算法
    if (this.advancedMatcher) {
      const result = this.findRelevantRulesAdvanced(fourPillars, analysisType, options);

      // 缓存结果
      this.matchingCache.set(cacheKey, result);

      // 更新性能统计
      const endTime = Date.now();
      const matchTime = endTime - startTime;
      this.performanceStats.avgMatchTime =
        (this.performanceStats.avgMatchTime + matchTime) / 2;

      console.log(`🎯 高级匹配完成 - 耗时: ${matchTime}ms, 匹配: ${result.length}条`);
      return result;
    } else {
      // 降级到基础匹配
      return this.findRelevantRulesBasic(fourPillars, analysisType);
    }
  }

  /**
   * 🚀 高级匹配算法实现
   */
  findRelevantRulesAdvanced(fourPillars, analysisType, options = {}) {
    // 获取候选规则
    const candidateRules = this.getCandidateRules(analysisType);

    // 使用高级匹配算法
    const matchedRules = this.advancedMatcher.matchRules(
      candidateRules,
      fourPillars,
      analysisType,
      options
    );

    // 应用额外过滤条件
    const filteredRules = this.applyAdvancedFilters(matchedRules, options);

    // 限制返回数量
    const maxResults = options.maxResults || 10;
    return filteredRules.slice(0, maxResults);
  }

  /**
   * 基础匹配算法（备用）
   */
  findRelevantRulesBasic(fourPillars, analysisType) {
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;
    const relevantRules = [];

    // 根据分析类型选择相关规则
    switch (analysisType) {
      case 'sanming':
        relevantRules.push(...this.getRulesBySource('三命通会'));
        break;
      case 'yuanhai':
        relevantRules.push(...this.getRulesBySource('渊海子平'));
        break;
      case 'ditian':
        relevantRules.push(...this.getRulesBySource('滴天髓'));
        break;
      case 'pattern':
        relevantRules.push(...this.getRulesByCategory('特殊格局'));
        relevantRules.push(...this.getRulesByCategory('正格'));
        break;
      case 'yongshen':
        relevantRules.push(...this.getRulesByCategory('用神理论'));
        break;
      case 'comprehensive':
      default:
        // 综合分析，获取多种类型的规则
        relevantRules.push(...this.getRulesByConfidence(0.9));
        break;
    }

    // 按置信度排序
    return relevantRules.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
  }

  /**
   * 生成基于古籍规则的分析文本
   */
  generateAnalysisFromRules(rules, fourPillars, analysisType) {
    if (!rules || rules.length === 0) {
      return this._getFallbackAnalysis(analysisType);
    }

    // 选择最相关的规则（前3-5条）
    const selectedRules = rules.slice(0, Math.min(5, rules.length));
    
    let analysisText = '';
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;

    selectedRules.forEach((rule, index) => {
      if (index > 0) analysisText += '；';
      
      // 根据规则类型生成分析文本
      if (rule.book_source) {
        // 核心规则格式
        analysisText += this._formatCoreRuleAnalysis(rule, dayGan, monthZhi);
      } else if (rule.source) {
        // 五行精纪规则格式
        analysisText += this._formatWuxingRuleAnalysis(rule, dayGan, monthZhi);
      }
    });

    return analysisText || this._getFallbackAnalysis(analysisType);
  }

  /**
   * 格式化核心规则分析
   */
  _formatCoreRuleAnalysis(rule, dayGan, monthZhi) {
    const bookName = rule.book_source;
    const patternName = rule.pattern_name;
    
    // 提取规则的关键信息
    let keyInfo = '';
    if (rule.interpretations && rule.interpretations.length > 100) {
      keyInfo = rule.interpretations.substring(0, 100) + '...';
    } else if (rule.original_text && rule.original_text.length > 50) {
      keyInfo = rule.original_text.substring(0, 50) + '...';
    }

    return `《${bookName}》论${patternName}：${keyInfo}`;
  }

  /**
   * 格式化五行精纪规则分析
   */
  _formatWuxingRuleAnalysis(rule, dayGan, monthZhi) {
    const title = rule.title;
    const content = rule.content;
    
    return `《五行精纪》${title}：${content}`;
  }

  /**
   * 获取备用分析（当没有匹配规则时）
   */
  _getFallbackAnalysis(analysisType) {
    const fallbacks = {
      'sanming': '《三命通会》论此命格局中正，五行配置得当，主平稳发展。',
      'yuanhai': '《渊海子平》云：此命日主强弱适中，用神明确，宜顺应天时。',
      'ditian': '《滴天髓》论：天干地支配合有情，格局清纯，一生平稳上升。',
      'pattern': '格局分析：此命格局尚可，配置平衡，宜后天努力。',
      'yongshen': '用神分析：用神明确，配置得当，宜把握机遇。',
      'comprehensive': '综合古籍理论分析：此命格局中等，五行平衡，主平稳发展。'
    };

    return fallbacks[analysisType] || fallbacks['comprehensive'];
  }

  /**
   * 🚀 初始化所有优化组件
   */
  async initializeOptimizationComponents() {
    console.log('🚀 开始初始化优化组件...');

    // 初始化高级匹配算法
    this.initializeAdvancedMatcher();

    // 初始化数据扩展管理器
    await this.initializeDataExpansion();

    // 初始化性能优化器
    this.initializePerformanceOptimizer();

    console.log('✅ 所有优化组件初始化完成');
  }

  /**
   * 🚀 初始化分层规则管理器
   */
  async initializeLayeredRulesManager() {
    try {
      if (typeof window !== 'undefined' && window.LayeredRulesManager) {
        this.layeredRulesManager = new window.LayeredRulesManager();
        await this.layeredRulesManager.initialize();
        console.log('✅ 分层规则管理器初始化成功');
      } else if (typeof LayeredRulesManager !== 'undefined') {
        this.layeredRulesManager = new LayeredRulesManager();
        await this.layeredRulesManager.initialize();
        console.log('✅ 分层规则管理器初始化成功');
      } else {
        console.warn('⚠️ 分层规则管理器未找到，将使用基础规则匹配');
      }
    } catch (error) {
      console.error('❌ 分层规则管理器初始化失败:', error);
    }
  }

  /**
   * 🚀 分层规则匹配（新增方法）
   */
  async matchRulesInLayers(fourPillars, analysisDepth = 'standard') {
    if (!this.layeredRulesManager || !this.layeredRulesManager.isInitialized) {
      console.warn('⚠️ 分层规则管理器未初始化，使用基础规则匹配');
      return this.findRelevantRulesBasic(fourPillars, 'comprehensive');
    }

    return await this.layeredRulesManager.matchRulesInLayers(fourPillars, analysisDepth);
  }

  /**
   * 🚀 初始化高级匹配算法
   */
  initializeAdvancedMatcher() {
    try {
      // 检查是否有高级匹配器类
      if (typeof window !== 'undefined' && window.AdvancedRuleMatcher) {
        this.advancedMatcher = new window.AdvancedRuleMatcher();
        console.log('✅ 高级匹配算法初始化成功');
      } else if (typeof AdvancedRuleMatcher !== 'undefined') {
        this.advancedMatcher = new AdvancedRuleMatcher();
        console.log('✅ 高级匹配算法初始化成功');
      } else {
        console.warn('⚠️ 高级匹配算法未找到，将使用基础匹配');
      }
    } catch (error) {
      console.error('❌ 高级匹配算法初始化失败:', error);
    }
  }

  /**
   * 🚀 初始化数据扩展管理器
   */
  async initializeDataExpansion() {
    try {
      if (typeof window !== 'undefined' && window.DataExpansionManager) {
        this.dataExpansionManager = new window.DataExpansionManager();
        await this.dataExpansionManager.initialize();
        console.log('✅ 数据扩展管理器初始化成功');
      } else if (typeof DataExpansionManager !== 'undefined') {
        this.dataExpansionManager = new DataExpansionManager();
        await this.dataExpansionManager.initialize();
        console.log('✅ 数据扩展管理器初始化成功');
      } else {
        console.warn('⚠️ 数据扩展管理器未找到，跳过扩展数据加载');
      }
    } catch (error) {
      console.error('❌ 数据扩展管理器初始化失败:', error);
    }
  }

  /**
   * 🚀 初始化性能优化器
   */
  initializePerformanceOptimizer() {
    try {
      if (typeof window !== 'undefined' && window.PerformanceOptimizer) {
        this.performanceOptimizer = new window.PerformanceOptimizer();
        this.performanceOptimizer.initialize();

        // 构建规则索引
        const allRules = this.getAllRules();
        this.performanceOptimizer.buildRuleIndexes(allRules);

        console.log('✅ 性能优化器初始化成功');
      } else if (typeof PerformanceOptimizer !== 'undefined') {
        this.performanceOptimizer = new PerformanceOptimizer();
        this.performanceOptimizer.initialize();

        // 构建规则索引
        const allRules = this.getAllRules();
        this.performanceOptimizer.buildRuleIndexes(allRules);

        console.log('✅ 性能优化器初始化成功');
      } else {
        console.warn('⚠️ 性能优化器未找到，将使用基础查询');
      }
    } catch (error) {
      console.error('❌ 性能优化器初始化失败:', error);
    }
  }

  /**
   * 🚀 生成缓存键
   */
  generateCacheKey(fourPillars, analysisType, options) {
    const pillarsStr = fourPillars.map(p => p.gan + p.zhi).join('');
    const optionsStr = JSON.stringify(options);
    return `${pillarsStr}_${analysisType}_${optionsStr}`;
  }

  /**
   * 🚀 获取候选规则
   */
  getCandidateRules(analysisType) {
    let candidateRules = [];

    switch (analysisType) {
      case 'sanming':
        candidateRules = this.getRulesBySource('三命通会');
        break;
      case 'yuanhai':
        candidateRules = this.getRulesBySource('渊海子平');
        break;
      case 'ditian':
        candidateRules = this.getRulesBySource('滴天髓');
        break;
      case 'pattern':
        candidateRules = [
          ...this.getRulesByCategory('特殊格局'),
          ...this.getRulesByCategory('正格'),
          ...this.getRulesByCategory('神煞格局')
        ];
        break;
      case 'yongshen':
        candidateRules = this.getRulesByCategory('用神理论');
        break;
      case 'strength':
        candidateRules = this.getRulesByCategory('强弱判断');
        break;
      case 'comprehensive':
      default:
        // 获取高质量规则作为候选
        candidateRules = this.getRulesByConfidence(0.8);
        break;
    }

    return candidateRules;
  }

  /**
   * 🚀 应用高级过滤条件
   */
  applyAdvancedFilters(rules, options) {
    let filteredRules = [...rules];

    // 最小匹配分数过滤
    const minScore = options.minMatchScore || 0.5;
    filteredRules = filteredRules.filter(rule => rule.matchScore >= minScore);

    // 置信度过滤
    const minConfidence = options.minConfidence || 0.7;
    filteredRules = filteredRules.filter(rule => (rule.confidence || 0) >= minConfidence);

    // 古籍来源过滤
    if (options.preferredSources && options.preferredSources.length > 0) {
      filteredRules = filteredRules.filter(rule =>
        options.preferredSources.includes(rule.book_source) ||
        options.preferredSources.includes(rule.source)
      );
    }

    // 分类过滤
    if (options.categories && options.categories.length > 0) {
      filteredRules = filteredRules.filter(rule =>
        options.categories.includes(rule.category)
      );
    }

    return filteredRules;
  }

  /**
   * 🚀 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performanceStats,
      cacheHitRate: this.performanceStats.totalQueries > 0 ?
        (this.performanceStats.cacheHits / this.performanceStats.totalQueries * 100).toFixed(2) + '%' : '0%',
      cacheSize: this.matchingCache.size
    };
  }

  /**
   * 🚀 清理缓存
   */
  clearCache() {
    this.matchingCache.clear();
    console.log('🧹 匹配缓存已清理');
  }

  /**
   * 🚀 获取所有规则
   */
  getAllRules() {
    const allRules = [];

    // 核心规则
    if (this.coreRules && this.coreRules.rules) {
      allRules.push(...this.coreRules.rules);
    }

    // 五行精纪规则
    if (this.wuxingRules && this.wuxingRules.rules) {
      allRules.push(...this.wuxingRules.rules);
    }

    // 扩展规则
    if (this.dataExpansionManager) {
      const expandedRules = this.dataExpansionManager.getExpandedRules();
      allRules.push(...expandedRules);
    }

    return allRules;
  }

  /**
   * 🚀 获取统计信息（全面升级版）
   */
  getStatistics() {
    if (!this.isLoaded) {
      return null;
    }

    const coreStats = {
      total: this.coreRules.rules.length,
      categories: [...new Set(this.coreRules.rules.map(r => r.category))],
      sources: [...new Set(this.coreRules.rules.map(r => r.book_source))],
      avgConfidence: this.coreRules.rules.reduce((sum, r) => sum + r.confidence, 0) / this.coreRules.rules.length
    };

    const wuxingStats = {
      total: this.wuxingRules.rules.length,
      categories: [...new Set(this.wuxingRules.rules.map(r => r.category))],
      avgConfidence: this.wuxingRules.rules.reduce((sum, r) => sum + r.confidence, 0) / this.wuxingRules.rules.length
    };

    // 🚀 扩展数据统计
    const expansionStats = this.dataExpansionManager ?
      this.dataExpansionManager.getExpansionStats() : null;

    // 🚀 性能统计
    const performanceStats = this.performanceOptimizer ?
      this.performanceOptimizer.getPerformanceStats() : null;

    // 🚀 分层规则统计
    const layeredStats = this.layeredRulesManager ?
      this.layeredRulesManager.getLayeredStats() : null;

    return {
      core: coreStats,
      wuxing: wuxingStats,
      totalRules: coreStats.total + wuxingStats.total + (expansionStats?.totalRules || 0),

      // 🚀 扩展数据统计
      expansion: expansionStats,

      // 🚀 性能统计
      performance: performanceStats,

      // 🚀 分层规则统计
      layered: layeredStats,

      // 🚀 组件状态
      components: {
        advancedMatcher: this.advancedMatcher ? 'enabled' : 'disabled',
        dataExpansion: this.dataExpansionManager ? 'enabled' : 'disabled',
        performanceOptimizer: this.performanceOptimizer ? 'enabled' : 'disabled',
        layeredRulesManager: this.layeredRulesManager ? 'enabled' : 'disabled'
      },

      // 🚀 系统信息
      system: {
        loadTime: this.loadPromise ? 'loaded' : 'not_loaded',
        cacheSize: this.matchingCache.size,
        version: '2.0.0'
      }
    };
  }
}

// 创建全局实例
const classicalRulesManager = new ClassicalRulesManager();

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ClassicalRulesManager;
} else if (typeof window !== 'undefined') {
  window.ClassicalRulesManager = ClassicalRulesManager;
  window.classicalRulesManager = classicalRulesManager;
}
