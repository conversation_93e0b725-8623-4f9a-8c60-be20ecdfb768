/**
 * 测试十神分析功能
 * 验证新增的十神分析模块是否正确工作
 * 测试用例：庚子 癸未 丙子 乙未
 */

console.log('🎭 测试十神分析功能');
console.log('='.repeat(50));
console.log('');

// 测试用例四柱
const testFourPillars = [
  { gan: '庚', zhi: '子' },  // 年柱
  { gan: '癸', zhi: '未' },  // 月柱
  { gan: '丙', zhi: '子' },  // 日柱
  { gan: '乙', zhi: '未' }   // 时柱
];

const dayGan = '丙'; // 日干

console.log('📋 测试四柱：');
testFourPillars.forEach((pillar, index) => {
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  console.log(`${pillarNames[index]}：${pillar.gan}${pillar.zhi}`);
});
console.log(`日干：${dayGan}`);
console.log('');

// 模拟前端十神计算函数
function calculateShishen(dayGan, gans) {
  const shishenMap = {
    '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
    '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
    '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
    '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
    '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
    '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
    '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
    '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
    '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
    '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
  };

  const dayGanMap = shishenMap[dayGan] || {};
  return {
    year: dayGanMap[gans[0]] || '未知',
    month: dayGanMap[gans[1]] || '未知',
    day: '日主',
    hour: dayGanMap[gans[3]] || '未知'
  };
}

// 模拟藏干数据
const mockCangganData = {
  year_pillar: {
    main_qi: '癸',
    hidden_gan: ['癸'],
    ten_gods: ['正官'],
    strength: ['旺']
  },
  month_pillar: {
    main_qi: '己',
    hidden_gan: ['己', '丁', '乙'],
    ten_gods: ['伤官', '劫财', '正印'],
    strength: ['旺', '中', '弱']
  },
  day_pillar: {
    main_qi: '癸',
    hidden_gan: ['癸'],
    ten_gods: ['正官'],
    strength: ['旺']
  },
  hour_pillar: {
    main_qi: '己',
    hidden_gan: ['己', '丁', '乙'],
    ten_gods: ['伤官', '劫财', '正印'],
    strength: ['旺', '中', '弱']
  }
};

// 模拟十神分析生成函数
function generateShishenAnalysis(dayGan, gans, cangganData) {
  // 统计主星（天干十神）
  const shishenResult = calculateShishen(dayGan, gans);
  const mainStars = {
    year: gans[0] !== dayGan ? shishenResult.year : null,
    month: gans[1] !== dayGan ? shishenResult.month : null,
    hour: gans[3] !== dayGan ? shishenResult.hour : null
  };

  // 统计副星（地支藏干十神）
  const auxStars = [];
  Object.values(cangganData).forEach(pillarData => {
    if (pillarData.ten_gods) {
      auxStars.push(...pillarData.ten_gods);
    }
  });

  // 统计所有十神
  const allShishen = [...Object.values(mainStars).filter(s => s), ...auxStars];
  const shishenCount = {};
  allShishen.forEach(shishen => {
    shishenCount[shishen] = (shishenCount[shishen] || 0) + 1;
  });

  // 找出主导十神
  const maxCount = Math.max(...Object.values(shishenCount));
  const dominantShishen = Object.keys(shishenCount).filter(s => shishenCount[s] === maxCount);

  // 分析格局类型
  const patternType = analyzeShishenPattern(dominantShishen);
  
  // 计算格局强度
  const patternStrength = calculatePatternStrength(shishenCount, allShishen.length);

  // 生成格局描述
  const patternDescription = generatePatternDescription(dominantShishen, patternType, patternStrength);

  return {
    main_stars: mainStars,
    aux_stars_count: auxStars.length,
    shishen_distribution: shishenCount,
    dominant_shishen: dominantShishen.join('、'),
    pattern_type: patternType,
    pattern_strength: patternStrength,
    pattern_description: patternDescription,
    total_shishen_count: allShishen.length
  };
}

// 辅助函数
function analyzeShishenPattern(dominantShishen) {
  if (dominantShishen.length === 0) return '平衡格局';
  
  const primary = dominantShishen[0];
  const patternMap = {
    '正官': '正官格',
    '偏官': '七杀格', 
    '正财': '正财格',
    '偏财': '偏财格',
    '食神': '食神格',
    '伤官': '伤官格',
    '比肩': '比肩格',
    '劫财': '劫财格',
    '正印': '正印格',
    '偏印': '偏印格'
  };

  return patternMap[primary] || '复合格局';
}

function calculatePatternStrength(shishenCount, totalCount) {
  if (totalCount === 0) return '无法判断';
  
  const maxCount = Math.max(...Object.values(shishenCount));
  const concentration = maxCount / totalCount;
  
  if (concentration >= 0.5) return '强';
  if (concentration >= 0.3) return '中等偏强';
  if (concentration >= 0.2) return '中等';
  return '弱';
}

function generatePatternDescription(dominantShishen, patternType, patternStrength) {
  if (dominantShishen.length === 0) {
    return '十神分布均衡，性格较为平和，适应能力强，发展方向多元化。';
  }

  const primary = dominantShishen[0];
  const descriptions = {
    '正官': '正官主导，品格端正，适合从政或管理，注重名誉和社会地位，发展稳健。',
    '偏官': '七杀主导，性格刚强，具有领导才能，适合竞争激烈的行业，敢于挑战。',
    '正财': '正财主导，理财有道，适合商业经营，注重实际利益，财运稳定。',
    '偏财': '偏财主导，机遇财运佳，适合投资理财，善于把握商机，财源广进。',
    '食神': '食神主导，才华横溢，适合文艺创作，性格温和，享受生活。',
    '伤官': '伤官主导，聪明机智，适合技术创新，个性鲜明，追求自由。',
    '比肩': '比肩主导，独立自主，适合创业发展，重视友情，坚持己见。',
    '劫财': '劫财主导，善于合作，适合团队工作，重视人际关系，慷慨大方。',
    '正印': '正印主导，学识渊博，适合教育文化，品德高尚，受人尊敬。',
    '偏印': '偏印主导，专业特长突出，适合研究技术，思维独特，多才多艺。'
  };

  const baseDesc = descriptions[primary] || '格局特殊，需要综合分析。';
  const strengthDesc = patternStrength === '强' ? '格局清晰，发展潜力大。' : 
                      patternStrength === '中等偏强' ? '格局较为明显，发展稳定。' :
                      patternStrength === '中等' ? '格局一般，需要努力发展。' : '格局较弱，需要借助外力。';
  
  return baseDesc + strengthDesc;
}

// 执行测试
console.log('🎭 开始十神分析测试：');
console.log('='.repeat(30));

const gans = testFourPillars.map(pillar => pillar.gan);
const analysisResult = generateShishenAnalysis(dayGan, gans, mockCangganData);

console.log('📊 十神分析结果：');
console.log('');

console.log('🌟 主星配置：');
Object.entries(analysisResult.main_stars).forEach(([position, star]) => {
  if (star) {
    const positionName = { year: '年柱', month: '月柱', hour: '时柱' }[position];
    console.log(`  ${positionName}主星：${star}`);
  }
});

console.log('\n⭐ 副星统计：');
console.log(`  副星总数：${analysisResult.aux_stars_count} 个`);

console.log('\n📈 十神分布：');
Object.entries(analysisResult.shishen_distribution).forEach(([shishen, count]) => {
  console.log(`  ${shishen}：${count} 个`);
});

console.log('\n🎯 格局分析：');
console.log(`  主导十神：${analysisResult.dominant_shishen}`);
console.log(`  格局类型：${analysisResult.pattern_type}`);
console.log(`  格局强度：${analysisResult.pattern_strength}`);
console.log(`  十神总数：${analysisResult.total_shishen_count} 个`);

console.log('\n📝 格局描述：');
console.log(`  ${analysisResult.pattern_description}`);

console.log('\n✅ 十神分析功能测试完成！');
console.log('🎯 功能验证：数字化十神分析系统运行正常');
