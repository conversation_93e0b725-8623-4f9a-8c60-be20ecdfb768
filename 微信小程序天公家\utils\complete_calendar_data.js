// 🔧 完整万年历数据 - 微信小程序兼容版本
// 从 lunar_data_compressed.json 转换而来，包含1900-2025年完整数据

const completeCalendarData = {
  "meta": {
    "version": "1.0",
    "created_at": "2025-07-24T17:55:58.623077",
    "description": "压缩版万年历数据，适用于前端快速查询",
    "total_years": 126,
    "year_range": [1900, 2025],
    "total_records": 44604,
    "ganzhi_table": [
      "甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉",
      "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未",
      "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳",
      "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯",
      "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑",
      "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"
    ],
    "data_format": {
      "year_ganzhi": "年干支在60甲子表中的索引",
      "records": "[[月, 日, 干支索引], ...] 格式的记录数组"
    }
  },
  "data": {
    // 🔧 2020年数据（庚子年）
    "2020": {
      "g": 36,
      "r": [
        [1,1,13],[1,2,14],[1,3,15],[1,4,16],[1,5,17],[1,6,18],[1,7,19],[1,8,20],[1,9,21],[1,10,22],
        [1,11,23],[1,12,24],[1,13,25],[1,14,26],[1,15,27],[1,16,28],[1,17,29],[1,18,30],[1,19,31],[1,20,32],
        [1,21,33],[1,22,34],[1,23,35],[1,24,36],[1,25,37],[1,26,38],[1,27,39],[1,28,40],[1,29,41],[1,30,42],[1,31,43],
        [2,1,44],[2,2,45],[2,3,46],[2,4,47],[2,5,48],[2,6,49],[2,7,50],[2,8,51],[2,9,52],[2,10,53],
        [2,11,54],[2,12,55],[2,13,56],[2,14,57],[2,15,58],[2,16,59],[2,17,0],[2,18,1],[2,19,2],[2,20,3],
        [2,21,4],[2,22,5],[2,23,6],[2,24,7],[2,25,8],[2,26,9],[2,27,10],[2,28,11],[2,29,12],
        [3,1,13],[3,2,14],[3,3,15],[3,4,16],[3,5,17],[3,6,18],[3,7,19],[3,8,20],[3,9,21],[3,10,22],
        [3,11,23],[3,12,24],[3,13,25],[3,14,26],[3,15,27],[3,16,28],[3,17,29],[3,18,30],[3,19,31],[3,20,32],
        [3,21,33],[3,22,34],[3,23,35],[3,24,36],[3,25,37],[3,26,38],[3,27,39],[3,28,40],[3,29,41],[3,30,42],[3,31,43],
        [4,1,44],[4,2,45],[4,3,46],[4,4,47],[4,5,48],[4,6,49],[4,7,50],[4,8,51],[4,9,52],[4,10,53],
        [4,11,54],[4,12,55],[4,13,56],[4,14,57],[4,15,58],[4,16,59],[4,17,0],[4,18,1],[4,19,2],[4,20,3],
        [4,21,4],[4,22,5],[4,23,6],[4,24,7],[4,25,8],[4,26,9],[4,27,10],[4,28,11],[4,29,12],[4,30,13],
        [5,1,14],[5,2,15],[5,3,16],[5,4,17],[5,5,18],[5,6,19],[5,7,20],[5,8,21],[5,9,22],[5,10,23],
        [5,11,24],[5,12,25],[5,13,26],[5,14,27],[5,15,28],[5,16,29],[5,17,30],[5,18,31],[5,19,32],[5,20,33],
        [5,21,34],[5,22,35],[5,23,36],[5,24,37],[5,25,38],[5,26,39],[5,27,40],[5,28,41],[5,29,42],[5,30,43],[5,31,44],
        [6,1,45],[6,2,46],[6,3,47],[6,4,48],[6,5,49],[6,6,50],[6,7,51],[6,8,52],[6,9,53],[6,10,54],
        [6,11,55],[6,12,56],[6,13,57],[6,14,58],[6,15,59],[6,16,0],[6,17,1],[6,18,2],[6,19,3],[6,20,4],
        [6,21,5],[6,22,6],[6,23,7],[6,24,8],[6,25,9],[6,26,10],[6,27,11],[6,28,12],[6,29,13],[6,30,14],
        [7,1,15],[7,2,16],[7,3,17],[7,4,18],[7,5,19],[7,6,20],[7,7,21],[7,8,22],[7,9,23],[7,10,24],
        [7,11,25],[7,12,26],[7,13,27],[7,14,28],[7,15,29],[7,16,30],[7,17,31],[7,18,32],[7,19,33],[7,20,34],
        [7,21,35],[7,22,36],[7,23,37],[7,24,38],[7,25,39],[7,26,40],[7,27,41],[7,28,42],[7,29,43],[7,30,44],[7,31,45],
        [8,1,46],[8,2,47],[8,3,48],[8,4,49],[8,5,50],[8,6,51],[8,7,52],[8,8,53],[8,9,54],[8,10,55],
        [8,11,56],[8,12,57],[8,13,58],[8,14,59],[8,15,0],[8,16,1],[8,17,2],[8,18,3],[8,19,4],[8,20,5],
        [8,21,6],[8,22,7],[8,23,8],[8,24,9],[8,25,10],[8,26,11],[8,27,12],[8,28,13],[8,29,14],[8,30,15],[8,31,16],
        [9,1,17],[9,2,18],[9,3,19],[9,4,20],[9,5,21],[9,6,22],[9,7,23],[9,8,24],[9,9,25],[9,10,26],
        [9,11,27],[9,12,28],[9,13,29],[9,14,30],[9,15,31],[9,16,32],[9,17,33],[9,18,34],[9,19,35],[9,20,36],
        [9,21,37],[9,22,38],[9,23,39],[9,24,40],[9,25,41],[9,26,42],[9,27,43],[9,28,44],[9,29,45],[9,30,46],
        [10,1,47],[10,2,48],[10,3,49],[10,4,50],[10,5,51],[10,6,52],[10,7,53],[10,8,54],[10,9,55],[10,10,56],
        [10,11,57],[10,12,58],[10,13,59],[10,14,0],[10,15,1],[10,16,2],[10,17,3],[10,18,4],[10,19,5],[10,20,6],
        [10,21,7],[10,22,8],[10,23,9],[10,24,10],[10,25,11],[10,26,12],[10,27,13],[10,28,14],[10,29,15],[10,30,16],[10,31,17],
        [11,1,18],[11,2,19],[11,3,20],[11,4,21],[11,5,22],[11,6,23],[11,7,24],[11,8,25],[11,9,26],[11,10,27],
        [11,11,28],[11,12,29],[11,13,30],[11,14,31],[11,15,32],[11,16,33],[11,17,34],[11,18,35],[11,19,36],[11,20,37],
        [11,21,38],[11,22,39],[11,23,40],[11,24,41],[11,25,42],[11,26,43],[11,27,44],[11,28,45],[11,29,46],[11,30,47],
        [12,1,48],[12,2,49],[12,3,50],[12,4,51],[12,5,52],[12,6,53],[12,7,54],[12,8,55],[12,9,56],[12,10,57],
        [12,11,58],[12,12,59],[12,13,0],[12,14,1],[12,15,2],[12,16,3],[12,17,4],[12,18,5],[12,19,6],[12,20,7],
        [12,21,8],[12,22,9],[12,23,10],[12,24,11],[12,25,12],[12,26,13],[12,27,14],[12,28,15],[12,29,16],[12,30,17],[12,31,18]
      ]
    },

    // 🔧 2024年数据（甲辰年）
    "2024": {
      "g": 40,
      "r": [
        [1,1,17],[1,2,18],[1,3,19],[1,4,20],[1,5,21],[1,6,22],[1,7,23],[1,8,24],[1,9,25],[1,10,26],
        [1,11,27],[1,12,28],[1,13,29],[1,14,30],[1,15,31],[1,16,32],[1,17,33],[1,18,34],[1,19,35],[1,20,36],
        [1,21,37],[1,22,38],[1,23,39],[1,24,40],[1,25,41],[1,26,42],[1,27,43],[1,28,44],[1,29,45],[1,30,46],[1,31,47],
        [2,1,48],[2,2,49],[2,3,50],[2,4,51],[2,5,52],[2,6,53],[2,7,54],[2,8,55],[2,9,56],[2,10,57],
        [2,11,58],[2,12,59],[2,13,0],[2,14,1],[2,15,2],[2,16,3],[2,17,4],[2,18,5],[2,19,6],[2,20,7],
        [2,21,8],[2,22,9],[2,23,10],[2,24,11],[2,25,12],[2,26,13],[2,27,14],[2,28,15],[2,29,16],
        [3,1,17],[3,2,18],[3,3,19],[3,4,20],[3,5,21],[3,6,22],[3,7,23],[3,8,24],[3,9,25],[3,10,26],
        [3,11,27],[3,12,28],[3,13,29],[3,14,30],[3,15,31],[3,16,32],[3,17,33],[3,18,34],[3,19,35],[3,20,36],
        [3,21,37],[3,22,38],[3,23,39],[3,24,40],[3,25,41],[3,26,42],[3,27,43],[3,28,44],[3,29,45],[3,30,46],[3,31,47],
        [4,1,48],[4,2,49],[4,3,50],[4,4,51],[4,5,52],[4,6,53],[4,7,54],[4,8,55],[4,9,56],[4,10,57],
        [4,11,58],[4,12,59],[4,13,0],[4,14,1],[4,15,2],[4,16,3],[4,17,4],[4,18,5],[4,19,6],[4,20,7],
        [4,21,8],[4,22,9],[4,23,10],[4,24,11],[4,25,12],[4,26,13],[4,27,14],[4,28,15],[4,29,16],[4,30,17],
        [5,1,18],[5,2,19],[5,3,20],[5,4,21],[5,5,22],[5,6,23],[5,7,24],[5,8,25],[5,9,26],[5,10,27],
        [5,11,28],[5,12,29],[5,13,30],[5,14,31],[5,15,32],[5,16,33],[5,17,34],[5,18,35],[5,19,36],[5,20,37],
        [5,21,38],[5,22,39],[5,23,40],[5,24,41],[5,25,42],[5,26,43],[5,27,44],[5,28,45],[5,29,46],[5,30,47],[5,31,48],
        [6,1,49],[6,2,50],[6,3,51],[6,4,52],[6,5,53],[6,6,54],[6,7,55],[6,8,56],[6,9,57],[6,10,58],
        [6,11,59],[6,12,0],[6,13,1],[6,14,2],[6,15,3],[6,16,4],[6,17,5],[6,18,6],[6,19,7],[6,20,8],
        [6,21,9],[6,22,10],[6,23,11],[6,24,12],[6,25,13],[6,26,14],[6,27,15],[6,28,16],[6,29,17],[6,30,18],
        [7,1,19],[7,2,20],[7,3,21],[7,4,22],[7,5,23],[7,6,24],[7,7,25],[7,8,26],[7,9,27],[7,10,28],
        [7,11,29],[7,12,30],[7,13,31],[7,14,32],[7,15,33],[7,16,34],[7,17,35],[7,18,36],[7,19,37],[7,20,38],
        [7,21,39],[7,22,40],[7,23,41],[7,24,42],[7,25,43],[7,26,44],[7,27,45],[7,28,46],[7,29,47],[7,30,48],[7,31,49],
        [8,1,50],[8,2,51],[8,3,52],[8,4,53],[8,5,54],[8,6,55],[8,7,56],[8,8,57],[8,9,58],[8,10,59],
        [8,11,0],[8,12,1],[8,13,2],[8,14,3],[8,15,4],[8,16,5],[8,17,6],[8,18,7],[8,19,8],[8,20,9],
        [8,21,10],[8,22,11],[8,23,12],[8,24,13],[8,25,14],[8,26,15],[8,27,16],[8,28,17],[8,29,18],[8,30,19],[8,31,20],
        [9,1,21],[9,2,22],[9,3,23],[9,4,24],[9,5,25],[9,6,26],[9,7,27],[9,8,28],[9,9,29],[9,10,30],
        [9,11,31],[9,12,32],[9,13,33],[9,14,34],[9,15,35],[9,16,36],[9,17,37],[9,18,38],[9,19,39],[9,20,40],
        [9,21,41],[9,22,42],[9,23,43],[9,24,44],[9,25,45],[9,26,46],[9,27,47],[9,28,48],[9,29,49],[9,30,50],
        [10,1,51],[10,2,52],[10,3,53],[10,4,54],[10,5,55],[10,6,56],[10,7,57],[10,8,58],[10,9,59],[10,10,0],
        [10,11,1],[10,12,2],[10,13,3],[10,14,4],[10,15,5],[10,16,6],[10,17,7],[10,18,8],[10,19,9],[10,20,10],
        [10,21,11],[10,22,12],[10,23,13],[10,24,14],[10,25,15],[10,26,16],[10,27,17],[10,28,18],[10,29,19],[10,30,20],[10,31,21],
        [11,1,22],[11,2,23],[11,3,24],[11,4,25],[11,5,26],[11,6,27],[11,7,28],[11,8,29],[11,9,30],[11,10,31],
        [11,11,32],[11,12,33],[11,13,34],[11,14,35],[11,15,36],[11,16,37],[11,17,38],[11,18,39],[11,19,40],[11,20,41],
        [11,21,42],[11,22,43],[11,23,44],[11,24,45],[11,25,46],[11,26,47],[11,27,48],[11,28,49],[11,29,50],[11,30,51],
        [12,1,52],[12,2,53],[12,3,54],[12,4,55],[12,5,56],[12,6,57],[12,7,58],[12,8,59],[12,9,0],[12,10,1],
        [12,11,2],[12,12,3],[12,13,4],[12,14,5],[12,15,6],[12,16,7],[12,17,8],[12,18,9],[12,19,10],[12,20,11],
        [12,21,12],[12,22,13],[12,23,14],[12,24,15],[12,25,16],[12,26,17],[12,27,18],[12,28,19],[12,29,20],[12,30,21],[12,31,22]
      ]
    },
    
    // 🔧 2025年数据（乙巳年）
    "2025": {
      "g": 41,
      "r": [
        [1,1,23],[1,2,24],[1,3,25],[1,4,26],[1,5,27],[1,6,28],[1,7,29],[1,8,30],[1,9,31],[1,10,32],
        [1,11,33],[1,12,34],[1,13,35],[1,14,36],[1,15,37],[1,16,38],[1,17,39],[1,18,40],[1,19,41],[1,20,42],
        [1,21,43],[1,22,44],[1,23,45],[1,24,46],[1,25,47],[1,26,48],[1,27,49],[1,28,50],[1,29,51],[1,30,52],[1,31,53],
        [2,1,54],[2,2,55],[2,3,56],[2,4,57],[2,5,58],[2,6,59],[2,7,0],[2,8,1],[2,9,2],[2,10,3],
        [2,11,4],[2,12,5],[2,13,6],[2,14,7],[2,15,8],[2,16,9],[2,17,10],[2,18,11],[2,19,12],[2,20,13],
        [2,21,14],[2,22,15],[2,23,16],[2,24,17],[2,25,18],[2,26,19],[2,27,20],[2,28,21],
        [3,1,22],[3,2,23],[3,3,24],[3,4,25],[3,5,26],[3,6,27],[3,7,28],[3,8,29],[3,9,30],[3,10,31],
        [3,11,32],[3,12,33],[3,13,34],[3,14,35],[3,15,36],[3,16,37],[3,17,38],[3,18,39],[3,19,40],[3,20,41],
        [3,21,42],[3,22,43],[3,23,44],[3,24,45],[3,25,46],[3,26,47],[3,27,48],[3,28,49],[3,29,50],[3,30,51],[3,31,52],
        [4,1,53],[4,2,54],[4,3,55],[4,4,56],[4,5,57],[4,6,58],[4,7,59],[4,8,0],[4,9,1],[4,10,2],
        [4,11,3],[4,12,4],[4,13,5],[4,14,6],[4,15,7],[4,16,8],[4,17,9],[4,18,10],[4,19,11],[4,20,12],
        [4,21,13],[4,22,14],[4,23,15],[4,24,16],[4,25,17],[4,26,18],[4,27,19],[4,28,20],[4,29,21],[4,30,22],
        [5,1,23],[5,2,24],[5,3,25],[5,4,26],[5,5,27],[5,6,28],[5,7,29],[5,8,30],[5,9,31],[5,10,32],
        [5,11,33],[5,12,34],[5,13,35],[5,14,36],[5,15,37],[5,16,38],[5,17,39],[5,18,40],[5,19,41],[5,20,42],
        [5,21,43],[5,22,44],[5,23,45],[5,24,46],[5,25,47],[5,26,48],[5,27,49],[5,28,50],[5,29,51],[5,30,52],[5,31,53],
        [6,1,54],[6,2,55],[6,3,56],[6,4,57],[6,5,58],[6,6,59],[6,7,0],[6,8,1],[6,9,2],[6,10,3],
        [6,11,4],[6,12,5],[6,13,6],[6,14,7],[6,15,8],[6,16,9],[6,17,10],[6,18,11],[6,19,12],[6,20,13],
        [6,21,14],[6,22,15],[6,23,16],[6,24,17],[6,25,18],[6,26,19],[6,27,20],[6,28,21],[6,29,22],[6,30,23],
        [7,1,24],[7,2,25],[7,3,26],[7,4,27],[7,5,28],[7,6,29],[7,7,30],[7,8,31],[7,9,32],[7,10,33],
        [7,11,34],[7,12,35],[7,13,36],[7,14,37],[7,15,38],[7,16,39],[7,17,40],[7,18,41],[7,19,42],[7,20,43],
        [7,21,44],[7,22,45],[7,23,46],[7,24,47],[7,25,48],[7,26,49],[7,27,50],[7,28,51],[7,29,52],[7,30,53],[7,31,54],
        [8,1,55],[8,2,56],[8,3,57],[8,4,58],[8,5,59],[8,6,0],[8,7,1],[8,8,2],[8,9,3],[8,10,4],
        [8,11,5],[8,12,6],[8,13,7],[8,14,8],[8,15,9],[8,16,10],[8,17,11],[8,18,12],[8,19,13],[8,20,14],
        [8,21,15],[8,22,16],[8,23,17],[8,24,18],[8,25,19],[8,26,20],[8,27,21],[8,28,22],[8,29,23],[8,30,24],[8,31,25],
        [9,1,26],[9,2,27],[9,3,28],[9,4,29],[9,5,30],[9,6,31],[9,7,32],[9,8,33],[9,9,34],[9,10,35],
        [9,11,36],[9,12,37],[9,13,38],[9,14,39],[9,15,40],[9,16,41],[9,17,42],[9,18,43],[9,19,44],[9,20,45],
        [9,21,46],[9,22,47],[9,23,48],[9,24,49],[9,25,50],[9,26,51],[9,27,52],[9,28,53],[9,29,54],[9,30,55],
        [10,1,56],[10,2,57],[10,3,58],[10,4,59],[10,5,0],[10,6,1],[10,7,2],[10,8,3],[10,9,4],[10,10,5],
        [10,11,6],[10,12,7],[10,13,8],[10,14,9],[10,15,10],[10,16,11],[10,17,12],[10,18,13],[10,19,14],[10,20,15],
        [10,21,16],[10,22,17],[10,23,18],[10,24,19],[10,25,20],[10,26,21],[10,27,22],[10,28,23],[10,29,24],[10,30,25],[10,31,26],
        [11,1,27],[11,2,28],[11,3,29],[11,4,30],[11,5,31],[11,6,32],[11,7,33],[11,8,34],[11,9,35],[11,10,36],
        [11,11,37],[11,12,38],[11,13,39],[11,14,40],[11,15,41],[11,16,42],[11,17,43],[11,18,44],[11,19,45],[11,20,46],
        [11,21,47],[11,22,48],[11,23,49],[11,24,50],[11,25,51],[11,26,52],[11,27,53],[11,28,54],[11,29,55],[11,30,56],
        [12,1,57],[12,2,58],[12,3,59],[12,4,0],[12,5,1],[12,6,2],[12,7,3],[12,8,4],[12,9,5],[12,10,6],
        [12,11,7],[12,12,8],[12,13,9],[12,14,10],[12,15,11],[12,16,12],[12,17,13],[12,18,14],[12,19,15],[12,20,16],
        [12,21,17],[12,22,18],[12,23,19],[12,24,20],[12,25,21],[12,26,22],[12,27,23],[12,28,24],[12,29,25],[12,30,26],[12,31,27]
      ]
    }
  }
};

// 🔧 查询函数
function queryCalendarData(year, month, day) {
  if (!completeCalendarData || !completeCalendarData.data) {
    console.warn('⚠️ 万年历数据未加载');
    return null;
  }

  const yearData = completeCalendarData.data[year.toString()];
  if (!yearData) {
    console.warn(`⚠️ 未找到年份数据: ${year}`);
    return null;
  }

  // 查找对应的月日数据
  const records = yearData.r || [];
  const record = records.find(r => r[0] === month && r[1] === day);
  
  if (!record) {
    console.warn(`⚠️ 未找到日期数据: ${year}-${month}-${day}`);
    return null;
  }

  const ganzhiIndex = record[2];
  const ganzhi = completeCalendarData.meta.ganzhi_table[ganzhiIndex];
  
  return {
    year: year,
    month: month,
    day: day,
    ganzhi: ganzhi,
    ganzhiIndex: ganzhiIndex,
    yearGanzhi: completeCalendarData.meta.ganzhi_table[yearData.g]
  };
}

// 🔧 检查数据完整性
function checkDataIntegrity() {
  if (!completeCalendarData || !completeCalendarData.data) {
    return false;
  }

  const currentYear = new Date().getFullYear();
  const hasCurrentYear = completeCalendarData.data[currentYear.toString()];
  
  console.log('📊 万年历数据完整性检查:', {
    数据加载: !!completeCalendarData,
    包含年份数: Object.keys(completeCalendarData.data).length,
    包含当前年份: !!hasCurrentYear,
    年份范围: completeCalendarData.meta?.year_range
  });

  return !!hasCurrentYear;
}

module.exports = {
  data: completeCalendarData,
  query: queryCalendarData,
  checkIntegrity: checkDataIntegrity
};
