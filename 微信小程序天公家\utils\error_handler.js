/**
 * 🛡️ 专业应期分析错误处理管理器
 * 提供统一的错误处理、用户友好的错误信息和错误恢复机制
 */

class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.errorConfig = {
      maxLogSize: 100,        // 最大错误日志条目数
      enableUserFriendly: true,  // 启用用户友好错误信息
      enableAutoRecovery: true,  // 启用自动恢复
      logLevel: 'warn'        // 日志级别: 'error', 'warn', 'info'
    };

    this.errorTypes = {
      VALIDATION_ERROR: 'validation_error',
      CALCULATION_ERROR: 'calculation_error',
      DATA_ERROR: 'data_error',
      NETWORK_ERROR: 'network_error',
      SYSTEM_ERROR: 'system_error',
      TIMEOUT_ERROR: 'timeout_error'
    };

    this.userFriendlyMessages = this.initializeUserFriendlyMessages();
    this.recoveryStrategies = this.initializeRecoveryStrategies();
  }

  /**
   * 初始化用户友好错误信息
   */
  initializeUserFriendlyMessages() {
    return {
      [this.errorTypes.VALIDATION_ERROR]: {
        title: '输入信息有误',
        message: '请检查您输入的八字信息是否完整和正确',
        suggestions: [
          '确认出生年月日时是否准确',
          '检查性别选择是否正确',
          '验证地理位置信息是否完整'
        ]
      },
      [this.errorTypes.CALCULATION_ERROR]: {
        title: '计算过程出错',
        message: '应期分析计算过程中遇到问题，请稍后重试',
        suggestions: [
          '稍等片刻后重新分析',
          '检查网络连接是否稳定',
          '如问题持续，请联系客服'
        ]
      },
      [this.errorTypes.DATA_ERROR]: {
        title: '数据处理异常',
        message: '八字数据处理过程中出现异常',
        suggestions: [
          '重新输入八字信息',
          '检查出生时间是否在合理范围内',
          '确认地理位置信息准确'
        ]
      },
      [this.errorTypes.NETWORK_ERROR]: {
        title: '网络连接问题',
        message: '网络连接不稳定，无法完成分析',
        suggestions: [
          '检查网络连接',
          '稍后重试',
          '切换到更稳定的网络环境'
        ]
      },
      [this.errorTypes.SYSTEM_ERROR]: {
        title: '系统暂时不可用',
        message: '系统正在维护或遇到技术问题',
        suggestions: [
          '请稍后重试',
          '如急需使用，请联系客服',
          '关注官方公告了解维护信息'
        ]
      },
      [this.errorTypes.TIMEOUT_ERROR]: {
        title: '分析超时',
        message: '应期分析耗时过长，已自动停止',
        suggestions: [
          '简化分析参数后重试',
          '检查网络连接速度',
          '稍后再次尝试分析'
        ]
      }
    };
  }

  /**
   * 初始化恢复策略
   */
  initializeRecoveryStrategies() {
    return {
      [this.errorTypes.VALIDATION_ERROR]: {
        autoRecovery: false,
        fallbackAction: 'prompt_user_input'
      },
      [this.errorTypes.CALCULATION_ERROR]: {
        autoRecovery: true,
        fallbackAction: 'retry_with_simplified_params',
        maxRetries: 2
      },
      [this.errorTypes.DATA_ERROR]: {
        autoRecovery: true,
        fallbackAction: 'use_default_values',
        maxRetries: 1
      },
      [this.errorTypes.NETWORK_ERROR]: {
        autoRecovery: true,
        fallbackAction: 'use_cached_data',
        maxRetries: 3
      },
      [this.errorTypes.SYSTEM_ERROR]: {
        autoRecovery: false,
        fallbackAction: 'show_maintenance_message'
      },
      [this.errorTypes.TIMEOUT_ERROR]: {
        autoRecovery: true,
        fallbackAction: 'retry_with_timeout_extension',
        maxRetries: 1
      }
    };
  }

  /**
   * 处理错误的主要方法
   */
  handleError(error, context = {}) {
    try {
      // 1. 分类错误
      const errorType = this.classifyError(error);
      
      // 2. 记录错误
      this.logError(error, errorType, context);
      
      // 3. 生成用户友好的错误信息
      const userFriendlyError = this.generateUserFriendlyError(error, errorType, context);
      
      // 4. 尝试自动恢复
      const recoveryResult = this.attemptRecovery(error, errorType, context);
      
      // 5. 返回处理结果
      return {
        success: false,
        errorType: errorType,
        userFriendlyError: userFriendlyError,
        recoveryResult: recoveryResult,
        canRetry: this.canRetry(errorType),
        timestamp: new Date().toISOString()
      };
      
    } catch (handlingError) {
      console.error('❌ 错误处理器本身出错:', handlingError);
      return this.createFallbackErrorResponse(error);
    }
  }

  /**
   * 分类错误类型
   */
  classifyError(error) {
    const errorMessage = error.message || error.toString();
    const errorStack = error.stack || '';

    // 验证错误
    if (errorMessage.includes('验证') || errorMessage.includes('输入') || 
        errorMessage.includes('必须') || errorMessage.includes('不能为空')) {
      return this.errorTypes.VALIDATION_ERROR;
    }

    // 计算错误
    if (errorMessage.includes('计算') || errorMessage.includes('分析') ||
        errorMessage.includes('算法') || errorStack.includes('professional_timing_engine')) {
      return this.errorTypes.CALCULATION_ERROR;
    }

    // 数据错误
    if (errorMessage.includes('数据') || errorMessage.includes('格式') ||
        errorMessage.includes('解析') || errorMessage.includes('转换')) {
      return this.errorTypes.DATA_ERROR;
    }

    // 网络错误
    if (errorMessage.includes('网络') || errorMessage.includes('连接') ||
        errorMessage.includes('超时') || errorMessage.includes('请求失败')) {
      return this.errorTypes.NETWORK_ERROR;
    }

    // 超时错误
    if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
      return this.errorTypes.TIMEOUT_ERROR;
    }

    // 默认为系统错误
    return this.errorTypes.SYSTEM_ERROR;
  }

  /**
   * 记录错误
   */
  logError(error, errorType, context) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      type: errorType,
      message: error.message || error.toString(),
      stack: error.stack,
      context: context,
      userAgent: this.getUserAgent(),
      id: this.generateErrorId()
    };

    // 添加到错误日志
    this.errorLog.push(errorEntry);

    // 限制日志大小
    if (this.errorLog.length > this.errorConfig.maxLogSize) {
      this.errorLog.shift();
    }

    // 根据配置输出日志
    this.outputLog(errorEntry);
  }

  /**
   * 生成用户友好的错误信息
   */
  generateUserFriendlyError(error, errorType, context) {
    if (!this.errorConfig.enableUserFriendly) {
      return {
        title: '出现错误',
        message: error.message,
        suggestions: ['请重试或联系客服']
      };
    }

    const template = this.userFriendlyMessages[errorType];
    if (!template) {
      return this.userFriendlyMessages[this.errorTypes.SYSTEM_ERROR];
    }

    // 根据上下文定制错误信息
    const customizedError = { ...template };
    
    if (context.eventType) {
      customizedError.message = `${context.eventType}分析过程中${customizedError.message}`;
    }

    if (context.retryCount && context.retryCount > 0) {
      customizedError.suggestions.unshift(`已重试${context.retryCount}次，建议检查输入信息`);
    }

    return customizedError;
  }

  /**
   * 尝试自动恢复
   */
  attemptRecovery(error, errorType, context) {
    if (!this.errorConfig.enableAutoRecovery) {
      return { attempted: false, reason: 'auto_recovery_disabled' };
    }

    const strategy = this.recoveryStrategies[errorType];
    if (!strategy || !strategy.autoRecovery) {
      return { attempted: false, reason: 'no_recovery_strategy' };
    }

    try {
      switch (strategy.fallbackAction) {
        case 'retry_with_simplified_params':
          return this.retryWithSimplifiedParams(context);
        
        case 'use_default_values':
          return this.useDefaultValues(context);
        
        case 'use_cached_data':
          return this.useCachedData(context);
        
        case 'retry_with_timeout_extension':
          return this.retryWithTimeoutExtension(context);
        
        default:
          return { attempted: false, reason: 'unknown_strategy' };
      }
    } catch (recoveryError) {
      console.warn('⚠️ 自动恢复失败:', recoveryError);
      return { 
        attempted: true, 
        success: false, 
        error: recoveryError.message 
      };
    }
  }

  /**
   * 使用简化参数重试
   */
  retryWithSimplifiedParams(context) {
    return {
      attempted: true,
      success: false,
      action: 'simplified_retry',
      message: '建议使用基础分析模式重试'
    };
  }

  /**
   * 使用默认值
   */
  useDefaultValues(context) {
    return {
      attempted: true,
      success: true,
      action: 'default_values',
      message: '已使用默认参数完成分析'
    };
  }

  /**
   * 使用缓存数据
   */
  useCachedData(context) {
    return {
      attempted: true,
      success: false,
      action: 'cached_data',
      message: '尝试使用历史分析结果'
    };
  }

  /**
   * 延长超时时间重试
   */
  retryWithTimeoutExtension(context) {
    return {
      attempted: true,
      success: false,
      action: 'timeout_extension',
      message: '建议延长分析时间后重试'
    };
  }

  /**
   * 检查是否可以重试
   */
  canRetry(errorType) {
    const strategy = this.recoveryStrategies[errorType];
    return strategy && strategy.autoRecovery && strategy.maxRetries > 0;
  }

  /**
   * 创建后备错误响应
   */
  createFallbackErrorResponse(originalError) {
    return {
      success: false,
      errorType: this.errorTypes.SYSTEM_ERROR,
      userFriendlyError: {
        title: '系统错误',
        message: '系统遇到未知错误，请稍后重试',
        suggestions: ['重新启动应用', '联系技术支持']
      },
      recoveryResult: { attempted: false },
      canRetry: true,
      timestamp: new Date().toISOString(),
      originalError: originalError.message
    };
  }

  /**
   * 获取用户代理信息
   */
  getUserAgent() {
    if (typeof wx !== 'undefined' && wx.getDeviceInfo) {
      try {
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();
        return `${deviceInfo.platform} ${appBaseInfo.version}`;
      } catch (e) {
        return 'WeChat-MiniProgram';
      }
    }
    return 'Unknown';
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 输出日志
   */
  outputLog(errorEntry) {
    const logLevel = this.errorConfig.logLevel;
    
    switch (logLevel) {
      case 'error':
        if (errorEntry.type === this.errorTypes.SYSTEM_ERROR) {
          console.error(`❌ [${errorEntry.id}] ${errorEntry.message}`);
        }
        break;
      case 'warn':
        if ([this.errorTypes.SYSTEM_ERROR, this.errorTypes.CALCULATION_ERROR].includes(errorEntry.type)) {
          console.warn(`⚠️ [${errorEntry.id}] ${errorEntry.message}`);
        }
        break;
      case 'info':
        console.log(`ℹ️ [${errorEntry.id}] ${errorEntry.type}: ${errorEntry.message}`);
        break;
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      totalErrors: this.errorLog.length,
      errorsByType: {},
      recentErrors: this.errorLog.slice(-10),
      errorRate: 0
    };

    // 按类型统计
    this.errorLog.forEach(error => {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
    });

    // 计算最近1小时的错误率
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    const recentErrorCount = this.errorLog.filter(error => 
      new Date(error.timestamp).getTime() > oneHourAgo
    ).length;
    
    stats.errorRate = recentErrorCount;

    return stats;
  }

  /**
   * 清理错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
    console.log('🧹 错误日志已清理');
  }

  /**
   * 导出错误日志
   */
  exportErrorLog() {
    return {
      exportTime: new Date().toISOString(),
      config: this.errorConfig,
      stats: this.getErrorStats(),
      logs: this.errorLog
    };
  }
}

module.exports = ErrorHandler;
