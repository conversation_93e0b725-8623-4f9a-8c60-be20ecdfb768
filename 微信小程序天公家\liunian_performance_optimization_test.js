/**
 * 专业级流年系统性能优化和用户反馈机制测试
 * 
 * 测试内容：
 * 1. 流年计算性能测试
 * 2. 数据加载优化验证
 * 3. 用户反馈机制测试
 * 4. 缓存机制验证
 * 5. 错误处理和降级测试
 */

const ProfessionalLiunianCalculator = require('./utils/professional_liunian_calculator.js');

class LiunianPerformanceOptimizationTest {
  constructor() {
    this.calculator = new ProfessionalLiunianCalculator();
    this.testResults = [];
    console.log('🚀 流年系统性能优化测试初始化完成');
  }

  /**
   * 执行完整的性能优化测试套件
   */
  async runCompleteOptimizationTest() {
    console.log('\n🎯 开始执行流年系统性能优化测试套件...');
    console.log('=' .repeat(60));

    const testSuite = [
      { name: '流年计算性能测试', method: 'testLiunianCalculationPerformance' },
      { name: '数据加载优化验证', method: 'testDataLoadingOptimization' },
      { name: '用户反馈机制测试', method: 'testUserFeedbackMechanism' },
      { name: '缓存机制验证', method: 'testCachingMechanism' },
      { name: '错误处理和降级测试', method: 'testErrorHandlingAndFallback' },
      { name: '内存使用优化测试', method: 'testMemoryOptimization' },
      { name: '并发处理能力测试', method: 'testConcurrentProcessing' }
    ];

    for (const test of testSuite) {
      try {
        console.log(`\n📊 执行测试: ${test.name}`);
        const result = await this[test.method]();
        this.testResults.push({
          name: test.name,
          status: 'PASSED',
          result: result,
          timestamp: new Date().toISOString()
        });
        console.log(`✅ ${test.name}: 通过`);
      } catch (error) {
        console.error(`❌ ${test.name}: 失败 - ${error.message}`);
        this.testResults.push({
          name: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    this.generateOptimizationReport();
  }

  /**
   * 测试1：流年计算性能测试
   */
  testLiunianCalculationPerformance() {
    console.log('🔍 测试流年计算性能...');

    const testBazi = {
      dayPillar: { gan: '癸', zhi: '卯' },
      yearPillar: { gan: '辛', zhi: '丑' },
      monthPillar: { gan: '甲', zhi: '午' },
      timePillar: { gan: '壬', zhi: '戌' },
      birthInfo: { year: 2021 }
    };

    const iterations = 100;
    const startTime = Date.now();
    const memoryBefore = this.getMemoryUsage();

    for (let i = 0; i < iterations; i++) {
      this.calculator.calculateCompleteLiunianAnalysis(testBazi, 2024, 5);
    }

    const endTime = Date.now();
    const memoryAfter = this.getMemoryUsage();
    const executionTime = endTime - startTime;
    const averageTime = executionTime / iterations;
    const memoryIncrease = memoryAfter - memoryBefore;

    const performanceResult = {
      totalExecutionTime: `${executionTime}ms`,
      averageExecutionTime: `${averageTime.toFixed(2)}ms`,
      iterationsPerSecond: Math.round(1000 / averageTime),
      memoryIncrease: `${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`,
      performanceGrade: this.gradePerformance(averageTime),
      recommendation: this.getPerformanceRecommendation(averageTime)
    };

    console.log('📈 性能测试结果:');
    console.log(`   平均执行时间: ${performanceResult.averageExecutionTime}`);
    console.log(`   每秒处理次数: ${performanceResult.iterationsPerSecond}`);
    console.log(`   内存增长: ${performanceResult.memoryIncrease}`);
    console.log(`   性能等级: ${performanceResult.performanceGrade}`);

    return performanceResult;
  }

  /**
   * 测试2：数据加载优化验证
   */
  testDataLoadingOptimization() {
    console.log('🔍 测试数据加载优化...');

    const testScenarios = [
      { name: '小数据集', yearCount: 3 },
      { name: '中数据集', yearCount: 10 },
      { name: '大数据集', yearCount: 20 }
    ];

    const optimizationResults = [];

    testScenarios.forEach(scenario => {
      const startTime = Date.now();
      
      const result = this.calculator.calculateCompleteLiunianAnalysis(
        this.getTestBazi(), 2024, scenario.yearCount
      );

      const loadTime = Date.now() - startTime;
      const dataSize = JSON.stringify(result).length;

      optimizationResults.push({
        scenario: scenario.name,
        yearCount: scenario.yearCount,
        loadTime: `${loadTime}ms`,
        dataSize: `${(dataSize / 1024).toFixed(2)}KB`,
        efficiency: Math.round(scenario.yearCount / loadTime * 1000), // 年/秒
        optimized: loadTime < 100 // 100ms以下认为优化良好
      });
    });

    const averageEfficiency = optimizationResults.reduce((sum, r) => sum + r.efficiency, 0) / optimizationResults.length;

    console.log('📊 数据加载优化结果:');
    optimizationResults.forEach(result => {
      console.log(`   ${result.scenario}: ${result.loadTime}, ${result.dataSize}, ${result.optimized ? '✅' : '⚠️'}`);
    });

    return {
      scenarios: optimizationResults,
      averageEfficiency: averageEfficiency,
      overallOptimized: optimizationResults.every(r => r.optimized)
    };
  }

  /**
   * 测试3：用户反馈机制测试
   */
  testUserFeedbackMechanism() {
    console.log('🔍 测试用户反馈机制...');

    const feedbackScenarios = [
      { type: 'loading', message: '正在计算流年数据...' },
      { type: 'progress', message: '计算进度: 60%' },
      { type: 'success', message: '流年分析完成' },
      { type: 'error', message: '计算异常，使用降级数据' },
      { type: 'warning', message: '部分数据可能不准确' }
    ];

    const feedbackResults = feedbackScenarios.map(scenario => {
      const feedback = this.simulateUserFeedback(scenario);
      return {
        type: scenario.type,
        message: scenario.message,
        responseTime: feedback.responseTime,
        userFriendly: feedback.userFriendly,
        actionable: feedback.actionable
      };
    });

    const averageResponseTime = feedbackResults.reduce((sum, r) => sum + r.responseTime, 0) / feedbackResults.length;
    const userFriendlyRate = feedbackResults.filter(r => r.userFriendly).length / feedbackResults.length;

    console.log('💬 用户反馈机制结果:');
    console.log(`   平均响应时间: ${averageResponseTime.toFixed(2)}ms`);
    console.log(`   用户友好度: ${(userFriendlyRate * 100).toFixed(1)}%`);

    return {
      feedbackScenarios: feedbackResults,
      averageResponseTime: averageResponseTime,
      userFriendlyRate: userFriendlyRate,
      mechanismEffective: averageResponseTime < 50 && userFriendlyRate > 0.8
    };
  }

  /**
   * 测试4：缓存机制验证
   */
  testCachingMechanism() {
    console.log('🔍 测试缓存机制...');

    const testBazi = this.getTestBazi();
    const cacheKey = JSON.stringify(testBazi);

    // 第一次计算（无缓存）
    const startTime1 = Date.now();
    const result1 = this.calculator.calculateCompleteLiunianAnalysis(testBazi, 2024, 5);
    const time1 = Date.now() - startTime1;

    // 模拟缓存存储
    const cache = new Map();
    cache.set(cacheKey, result1);

    // 第二次计算（有缓存）
    const startTime2 = Date.now();
    const result2 = cache.has(cacheKey) ? cache.get(cacheKey) : 
      this.calculator.calculateCompleteLiunianAnalysis(testBazi, 2024, 5);
    const time2 = Date.now() - startTime2;

    const speedImprovement = ((time1 - time2) / time1 * 100).toFixed(1);
    const cacheHit = cache.has(cacheKey);

    console.log('🗄️ 缓存机制结果:');
    console.log(`   首次计算: ${time1}ms`);
    console.log(`   缓存命中: ${time2}ms`);
    console.log(`   性能提升: ${speedImprovement}%`);

    return {
      firstCalculationTime: time1,
      cachedCalculationTime: time2,
      speedImprovement: parseFloat(speedImprovement),
      cacheHit: cacheHit,
      cacheEffective: parseFloat(speedImprovement) > 50
    };
  }

  /**
   * 测试5：错误处理和降级测试
   */
  testErrorHandlingAndFallback() {
    console.log('🔍 测试错误处理和降级机制...');

    const errorScenarios = [
      { name: '无效八字数据', data: null },
      { name: '缺失必要字段', data: { dayPillar: { gan: '癸' } } },
      { name: '无效年份范围', data: this.getTestBazi(), year: -1 },
      { name: '超大数据请求', data: this.getTestBazi(), yearCount: 1000 }
    ];

    const errorResults = errorScenarios.map(scenario => {
      try {
        const startTime = Date.now();
        const result = this.calculator.calculateCompleteLiunianAnalysis(
          scenario.data, scenario.year || 2024, scenario.yearCount || 5
        );
        const responseTime = Date.now() - startTime;

        return {
          scenario: scenario.name,
          handled: true,
          responseTime: responseTime,
          fallbackUsed: result.length < 5, // 如果结果少于预期，可能使用了降级
          graceful: responseTime < 1000
        };
      } catch (error) {
        return {
          scenario: scenario.name,
          handled: false,
          error: error.message,
          graceful: false
        };
      }
    });

    const handledRate = errorResults.filter(r => r.handled).length / errorResults.length;
    const gracefulRate = errorResults.filter(r => r.graceful).length / errorResults.length;

    console.log('🛡️ 错误处理结果:');
    console.log(`   错误处理率: ${(handledRate * 100).toFixed(1)}%`);
    console.log(`   优雅降级率: ${(gracefulRate * 100).toFixed(1)}%`);

    return {
      errorScenarios: errorResults,
      handledRate: handledRate,
      gracefulRate: gracefulRate,
      robustSystem: handledRate > 0.8 && gracefulRate > 0.6
    };
  }

  /**
   * 测试6：内存使用优化测试
   */
  testMemoryOptimization() {
    console.log('🔍 测试内存使用优化...');

    const memoryBefore = this.getMemoryUsage();
    const calculations = [];

    // 执行多次计算
    for (let i = 0; i < 50; i++) {
      const result = this.calculator.calculateCompleteLiunianAnalysis(
        this.getTestBazi(), 2024, 5
      );
      calculations.push(result);
    }

    const memoryAfter = this.getMemoryUsage();
    const memoryIncrease = memoryAfter - memoryBefore;
    const memoryPerCalculation = memoryIncrease / calculations.length;

    // 清理测试数据
    calculations.length = 0;

    const memoryAfterCleanup = this.getMemoryUsage();
    const memoryRecovered = memoryAfter - memoryAfterCleanup;

    console.log('💾 内存优化结果:');
    console.log(`   内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    console.log(`   单次计算内存: ${(memoryPerCalculation / 1024).toFixed(2)}KB`);
    console.log(`   内存回收: ${(memoryRecovered / 1024 / 1024).toFixed(2)}MB`);

    return {
      memoryIncrease: memoryIncrease,
      memoryPerCalculation: memoryPerCalculation,
      memoryRecovered: memoryRecovered,
      memoryEfficient: memoryPerCalculation < 100 * 1024, // 小于100KB认为高效
      memoryLeakDetected: memoryRecovered < memoryIncrease * 0.5
    };
  }

  /**
   * 测试7：并发处理能力测试
   */
  async testConcurrentProcessing() {
    console.log('🔍 测试并发处理能力...');

    const concurrentTasks = [];
    const taskCount = 10;
    const startTime = Date.now();

    // 创建并发任务
    for (let i = 0; i < taskCount; i++) {
      const task = new Promise((resolve) => {
        setTimeout(() => {
          const result = this.calculator.calculateCompleteLiunianAnalysis(
            this.getTestBazi(), 2024, 3
          );
          resolve({
            taskId: i,
            completed: true,
            resultLength: result.length
          });
        }, Math.random() * 100); // 随机延迟模拟真实场景
      });
      concurrentTasks.push(task);
    }

    // 等待所有任务完成
    const results = await Promise.all(concurrentTasks);
    const totalTime = Date.now() - startTime;
    const successRate = results.filter(r => r.completed).length / taskCount;

    console.log('⚡ 并发处理结果:');
    console.log(`   并发任务数: ${taskCount}`);
    console.log(`   总执行时间: ${totalTime}ms`);
    console.log(`   成功率: ${(successRate * 100).toFixed(1)}%`);

    return {
      taskCount: taskCount,
      totalTime: totalTime,
      successRate: successRate,
      averageTimePerTask: totalTime / taskCount,
      concurrentCapable: successRate === 1 && totalTime < 1000
    };
  }

  /**
   * 辅助方法：获取内存使用情况
   */
  getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0; // 浏览器环境无法精确测量
  }

  /**
   * 辅助方法：获取测试八字数据
   */
  getTestBazi() {
    return {
      dayPillar: { gan: '癸', zhi: '卯' },
      yearPillar: { gan: '辛', zhi: '丑' },
      monthPillar: { gan: '甲', zhi: '午' },
      timePillar: { gan: '壬', zhi: '戌' },
      birthInfo: { year: 2021 }
    };
  }

  /**
   * 辅助方法：性能等级评定
   */
  gradePerformance(averageTime) {
    if (averageTime < 10) return 'A+ (优秀)';
    if (averageTime < 50) return 'A (良好)';
    if (averageTime < 100) return 'B (一般)';
    if (averageTime < 200) return 'C (需优化)';
    return 'D (性能差)';
  }

  /**
   * 辅助方法：获取性能建议
   */
  getPerformanceRecommendation(averageTime) {
    if (averageTime < 50) return '性能优秀，无需优化';
    if (averageTime < 100) return '建议添加缓存机制';
    if (averageTime < 200) return '建议优化算法复杂度';
    return '建议重构核心计算逻辑';
  }

  /**
   * 辅助方法：模拟用户反馈
   */
  simulateUserFeedback(scenario) {
    const responseTime = Math.random() * 100; // 模拟响应时间
    const userFriendly = scenario.message.length < 50 && !scenario.message.includes('error');
    const actionable = scenario.type === 'error' || scenario.type === 'warning';

    return {
      responseTime: responseTime,
      userFriendly: userFriendly,
      actionable: actionable
    };
  }

  /**
   * 生成优化报告
   */
  generateOptimizationReport() {
    console.log('\n📋 流年系统性能优化测试报告');
    console.log('=' .repeat(60));

    const passedTests = this.testResults.filter(r => r.status === 'PASSED').length;
    const totalTests = this.testResults.length;
    const successRate = (passedTests / totalTests * 100).toFixed(1);

    console.log(`📊 总体测试结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);
    console.log('\n📈 详细测试结果:');

    this.testResults.forEach((result, index) => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}`);
      
      if (result.status === 'PASSED' && result.result) {
        // 显示关键指标
        if (result.result.performanceGrade) {
          console.log(`   性能等级: ${result.result.performanceGrade}`);
        }
        if (result.result.averageExecutionTime) {
          console.log(`   平均执行时间: ${result.result.averageExecutionTime}`);
        }
        if (result.result.successRate !== undefined) {
          console.log(`   成功率: ${(result.result.successRate * 100).toFixed(1)}%`);
        }
      }
    });

    console.log('\n🎯 优化建议:');
    this.generateOptimizationRecommendations();

    console.log('\n✅ 流年系统性能优化测试完成');
    return {
      totalTests: totalTests,
      passedTests: passedTests,
      successRate: parseFloat(successRate),
      testResults: this.testResults
    };
  }

  /**
   * 生成优化建议
   */
  generateOptimizationRecommendations() {
    const recommendations = [
      '1. 实现智能缓存机制，缓存常用八字组合的计算结果',
      '2. 添加数据预加载功能，提前计算下一年的流年数据',
      '3. 实现渐进式数据加载，优先显示当前年份数据',
      '4. 添加计算进度指示器，提升用户体验',
      '5. 实现错误边界和优雅降级机制',
      '6. 优化内存使用，及时清理不需要的计算结果',
      '7. 添加并发控制，避免同时进行过多计算'
    ];

    recommendations.forEach(rec => console.log(`   ${rec}`));
  }
}

// 执行测试
if (typeof module !== 'undefined' && require.main === module) {
  const test = new LiunianPerformanceOptimizationTest();
  test.runCompleteOptimizationTest().catch(console.error);
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LiunianPerformanceOptimizationTest;
}

console.log('📦 流年系统性能优化测试模块加载完成');
