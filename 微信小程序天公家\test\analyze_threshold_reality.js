/**
 * 深度分析阈值设置的现实性
 * 检查当前阈值是否过低，以及100%达标的真实性
 */

// 当前阈值设置
const currentThresholds = {
  marriage: 15,    // 15%
  promotion: 20,   // 20%
  childbirth: 12,  // 12%
  wealth: 18       // 18%
};

// 模拟真实的五行分布数据（基于大量八字统计）
const realisticBaziSamples = [
  // 普通人的八字（大多数情况）
  { name: '普通八字1', elements: { 金: 12, 木: 8, 水: 15, 火: 10, 土: 14 }, type: '普通' },
  { name: '普通八字2', elements: { 金: 18, 木: 12, 水: 22, 火: 16, 土: 20 }, type: '普通' },
  { name: '普通八字3', elements: { 金: 25, 木: 18, 水: 28, 火: 22, 土: 25 }, type: '普通' },
  
  // 偏强的八字（少数情况）
  { name: '偏强八字1', elements: { 金: 35, 木: 28, 水: 40, 火: 32, 土: 35 }, type: '偏强' },
  { name: '偏强八字2', elements: { 金: 45, 木: 38, 水: 50, 火: 42, 土: 45 }, type: '偏强' },
  
  // 很强的八字（极少数情况）
  { name: '很强八字1', elements: { 金: 60, 木: 55, 水: 65, 火: 58, 土: 60 }, type: '很强' },
  { name: '很强八字2', elements: { 金: 80, 木: 75, 水: 85, 火: 78, 土: 80 }, type: '很强' },
  
  // 偏弱的八字（常见情况）
  { name: '偏弱八字1', elements: { 金: 8, 木: 5, 水: 10, 火: 7, 土: 9 }, type: '偏弱' },
  { name: '偏弱八字2', elements: { 金: 15, 木: 10, 水: 18, 火: 12, 土: 16 }, type: '偏弱' },
  
  // 极弱的八字（少数情况）
  { name: '极弱八字1', elements: { 金: 5, 木: 3, 水: 6, 火: 4, 土: 5 }, type: '极弱' }
];

// 真实的能量计算算法（包含制约因素）
function calculateRealisticEnergy(elements, eventType) {
  const { 金, 木, 水, 火, 土 } = elements;
  let energy = 0;
  
  switch (eventType) {
    case 'marriage':
      // 配偶星能量 - 制约因素
      const spouseEnergy = (金 + 火) * 0.3;
      const palaceEnergy = 水 * 0.2;
      const constraint = Math.min((木 + 土) * 0.1, spouseEnergy * 0.3);
      energy = Math.max(0, spouseEnergy + palaceEnergy - constraint);
      break;
      
    case 'promotion':
      // 官印能量 - 制约因素
      const officialPower = (金 + 水) * 0.25;
      const sealPower = (水 + 木) * 0.2;
      const synergy = Math.min(officialPower, sealPower) * 0.15;
      const promotionConstraint = Math.min((火 + 木) * 0.08, officialPower * 0.4);
      energy = Math.max(0, officialPower + sealPower + synergy - promotionConstraint);
      break;
      
    case 'childbirth':
      // 食伤能量（适中为佳）
      const foodInjury = (水 + 木) * 0.35;
      const childrenPalace = 火 * 0.15;
      const childbirthConstraint = Math.max(0, (foodInjury - 25) * 0.2);
      energy = Math.max(0, foodInjury + childrenPalace - childbirthConstraint);
      break;
      
    case 'wealth':
      // 财星能量 - 身弱不胜财
      const wealthPower = (木 + 火) * 0.25;
      const treasury = 土 * 0.15;
      const officialBonus = Math.min(((金 + 水) * 0.25 + (水 + 木) * 0.2) * 0.1, 15);
      const bodyStrength = (金 + 水 + 木) / 3;
      const wealthConstraint = Math.max(0, (wealthPower - bodyStrength) * 0.3);
      energy = Math.max(0, wealthPower + treasury + officialBonus - wealthConstraint);
      break;
  }
  
  return energy;
}

// 分析函数
function analyzeThresholdReality() {
  console.log('🧪 ===== 阈值设置现实性深度分析 =====\n');
  
  console.log('📊 当前阈值设置:');
  Object.keys(currentThresholds).forEach(eventType => {
    console.log(`  ${eventType}: ${currentThresholds[eventType]}%`);
  });
  
  console.log('\n🧪 真实八字样本测试:');
  
  const analysisResults = [];
  
  realisticBaziSamples.forEach((sample, index) => {
    console.log(`\n🔍 ${sample.name} (${sample.type}命格):`);
    console.log(`  五行: 金${sample.elements.金} 木${sample.elements.木} 水${sample.elements.水} 火${sample.elements.火} 土${sample.elements.土}`);
    
    const results = {};
    const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
    
    eventTypes.forEach(eventType => {
      const energy = calculateRealisticEnergy(sample.elements, eventType);
      const threshold = currentThresholds[eventType];
      const met = energy >= threshold;
      
      results[eventType] = {
        energy: energy.toFixed(1),
        threshold: threshold,
        met: met,
        ratio: (energy / threshold).toFixed(2)
      };
      
      console.log(`    ${eventType}: ${energy.toFixed(1)}% / ${threshold}% ${met ? '✅' : '❌'} (${(energy/threshold).toFixed(2)}倍)`);
    });
    
    const metCount = Object.values(results).filter(r => r.met).length;
    console.log(`    达标率: ${metCount}/4 (${(metCount/4*100).toFixed(0)}%)`);
    
    analysisResults.push({
      sample: sample,
      results: results,
      metCount: metCount,
      metRate: metCount / 4
    });
  });
  
  console.log('\n📈 统计分析:');
  
  // 按命格类型分析达标率
  const typeStats = {};
  analysisResults.forEach(result => {
    const type = result.sample.type;
    if (!typeStats[type]) {
      typeStats[type] = { total: 0, totalMet: 0, samples: [] };
    }
    typeStats[type].total += 4; // 4个事件
    typeStats[type].totalMet += result.metCount;
    typeStats[type].samples.push(result);
  });
  
  console.log('\n  各类命格达标率:');
  Object.keys(typeStats).forEach(type => {
    const stats = typeStats[type];
    const rate = (stats.totalMet / stats.total * 100).toFixed(1);
    console.log(`    ${type}命格: ${stats.totalMet}/${stats.total} (${rate}%)`);
  });
  
  // 整体达标率
  const totalMet = analysisResults.reduce((sum, r) => sum + r.metCount, 0);
  const totalPossible = analysisResults.length * 4;
  const overallRate = (totalMet / totalPossible * 100).toFixed(1);
  
  console.log(`\n  整体达标率: ${totalMet}/${totalPossible} (${overallRate}%)`);
  
  // 分析能量分布
  console.log('\n📊 能量分布分析:');
  const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
  
  eventTypes.forEach(eventType => {
    const energies = analysisResults.map(r => parseFloat(r.results[eventType].energy));
    const threshold = currentThresholds[eventType];
    
    const min = Math.min(...energies);
    const max = Math.max(...energies);
    const avg = (energies.reduce((sum, e) => sum + e, 0) / energies.length).toFixed(1);
    const aboveThreshold = energies.filter(e => e >= threshold).length;
    const aboveRate = (aboveThreshold / energies.length * 100).toFixed(1);
    
    console.log(`\n  ${eventType}事件:`);
    console.log(`    能量范围: ${min.toFixed(1)}-${max.toFixed(1)}% (平均${avg}%)`);
    console.log(`    阈值: ${threshold}%`);
    console.log(`    超过阈值: ${aboveThreshold}/${energies.length} (${aboveRate}%)`);
    
    // 判断阈值是否合理
    const avgRatio = parseFloat(avg) / threshold;
    let assessment = '';
    if (avgRatio > 3) {
      assessment = '❌ 阈值过低';
    } else if (avgRatio > 2) {
      assessment = '⚠️ 阈值偏低';
    } else if (avgRatio > 1.5) {
      assessment = '✅ 阈值合理';
    } else if (avgRatio > 1) {
      assessment = '⚠️ 阈值偏高';
    } else {
      assessment = '❌ 阈值过高';
    }
    console.log(`    评估: ${assessment} (平均能量是阈值的${avgRatio.toFixed(1)}倍)`);
  });
  
  console.log('\n🎯 问题诊断:');
  
  // 问题1: 阈值是否过低
  const avgOverallEnergy = analysisResults.reduce((sum, r) => {
    return sum + Object.values(r.results).reduce((s, res) => s + parseFloat(res.energy), 0) / 4;
  }, 0) / analysisResults.length;
  
  const avgThreshold = Object.values(currentThresholds).reduce((sum, t) => sum + t, 0) / Object.values(currentThresholds).length;
  const energyThresholdRatio = avgOverallEnergy / avgThreshold;
  
  console.log(`  平均能量: ${avgOverallEnergy.toFixed(1)}%`);
  console.log(`  平均阈值: ${avgThreshold.toFixed(1)}%`);
  console.log(`  能量/阈值比: ${energyThresholdRatio.toFixed(1)}倍`);
  
  const thresholdTooLow = energyThresholdRatio > 2.5;
  console.log(`  阈值过低: ${thresholdTooLow ? '❌ 是' : '✅ 否'}`);
  
  // 问题2: 达标率是否过高
  const metRateTooHigh = parseFloat(overallRate) > 80;
  console.log(`  达标率过高: ${metRateTooHigh ? '❌ 是' : '✅ 否'} (${overallRate}%)`);
  
  // 问题3: 缺乏区分度
  const hasGoodDiscrimination = typeStats['极弱'] && typeStats['很强'] && 
    (typeStats['很强'].totalMet / typeStats['很强'].total - typeStats['极弱'].totalMet / typeStats['极弱'].total) > 0.5;
  console.log(`  区分度良好: ${hasGoodDiscrimination ? '✅ 是' : '❌ 否'}`);
  
  console.log('\n💡 修正建议:');
  
  if (thresholdTooLow) {
    console.log('  🔧 阈值修正建议:');
    eventTypes.forEach(eventType => {
      const energies = analysisResults.map(r => parseFloat(r.results[eventType].energy));
      const currentThreshold = currentThresholds[eventType];
      const avg = energies.reduce((sum, e) => sum + e, 0) / energies.length;
      
      // 建议阈值为平均能量的60-80%
      const suggestedThreshold = Math.round(avg * 0.7);
      const adjustment = suggestedThreshold - currentThreshold;
      
      console.log(`    ${eventType}: ${currentThreshold}% → ${suggestedThreshold}% (${adjustment > 0 ? '+' : ''}${adjustment})`);
    });
  }
  
  if (metRateTooHigh) {
    console.log('  📉 预期达标率: 40-70% (当前过高)');
  }
  
  console.log('\n🎊 总结:');
  const issueCount = [thresholdTooLow, metRateTooHigh, !hasGoodDiscrimination].filter(Boolean).length;
  
  if (issueCount === 0) {
    console.log('✅ 阈值设置合理，无需调整');
  } else {
    console.log(`❌ 发现${issueCount}个问题，需要调整阈值`);
    console.log('💡 调整目标:');
    console.log('   - 整体达标率控制在40-70%');
    console.log('   - 不同命格有明显区分度');
    console.log('   - 阈值与平均能量比例合理(1.2-2倍)');
  }
  
  return {
    thresholdTooLow,
    metRateTooHigh,
    hasGoodDiscrimination,
    overallRate: parseFloat(overallRate),
    energyThresholdRatio,
    needsAdjustment: issueCount > 0
  };
}

// 运行分析
analyzeThresholdReality();
