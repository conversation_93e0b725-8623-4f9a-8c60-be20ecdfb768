// 历史名人数据库页面逻辑
const CelebrityDatabaseAPI = require('../../utils/celebrity_database_api.js');
const BaziSimilarityMatcher = require('../../utils/bazi_similarity_matcher.js');

Page({
  data: {
    // 搜索和筛选
    searchKeyword: '',
    currentFilter: 'all',
    
    // 数据展示
    allCelebrities: [],
    displayedCelebrities: [],
    statistics: {},
    showStatistics: true,
    
    // 分页加载
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    
    // 弹窗
    showModal: false,
    selectedCelebrity: {}
  },

  onLoad: function(options) {
    this.initDatabase();
    this.loadStatistics();
    this.loadCelebrities();
  },

  /**
   * 初始化数据库管理器
   */
  initDatabase: function() {
    this.celebrityAPI = CelebrityDatabaseAPI; // CelebrityDatabaseAPI 是单例实例
    this.databaseManager = CelebrityDatabaseAPI; // 添加databaseManager别名以保持兼容性
    this.similarityMatcher = new BaziSimilarityMatcher(); // BaziSimilarityMatcher 需要实例化
    console.log('📚 历史名人数据库初始化完成');
  },

  /**
   * 加载统计信息
   */
  loadStatistics: function() {
    try {
      const statistics = this.celebrityAPI.getStatistics();
      this.setData({
        statistics: {
          totalCelebrities: statistics.totalCelebrities,
          averageVerificationScore: (statistics.averageVerificationScore * 100).toFixed(1) + '%',
          patternDistribution: statistics.patternDistribution
        }
      });
      console.log('📊 统计信息加载完成:', statistics);
    } catch (error) {
      console.error('❌ 统计信息加载失败:', error);
      wx.showToast({
        title: '统计信息加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载名人数据
   */
  loadCelebrities: function() {
    try {
      const allCelebrities = this.celebrityAPI.getAllCelebrities();
      const displayedCelebrities = this.paginateCelebrities(allCelebrities, 1, this.data.pageSize);

      this.setData({
        allCelebrities: allCelebrities,
        displayedCelebrities: displayedCelebrities,
        hasMore: allCelebrities.length > this.data.pageSize
      });

      console.log(`📋 加载了 ${displayedCelebrities.length} 位名人数据`);
    } catch (error) {
      console.error('❌ 名人数据加载失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分页处理
   */
  paginateCelebrities: function(celebrities, page, pageSize) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return celebrities.slice(startIndex, endIndex);
  },

  /**
   * 搜索输入处理
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch: function() {
    const keyword = this.data.searchKeyword.trim();
    
    if (!keyword) {
      this.loadCelebrities();
      return;
    }

    try {
      const searchResults = this.databaseManager.search(keyword);
      const celebrities = searchResults.map(result => result.celebrity);
      const displayedCelebrities = this.paginateCelebrities(celebrities, 1, this.data.pageSize);
      
      this.setData({
        allCelebrities: celebrities,
        displayedCelebrities: displayedCelebrities,
        currentPage: 1,
        hasMore: celebrities.length > this.data.pageSize,
        showStatistics: false
      });
      
      console.log(`🔍 搜索 "${keyword}" 找到 ${celebrities.length} 位名人`);
      
      if (celebrities.length === 0) {
        wx.showToast({
          title: '未找到匹配的名人',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('❌ 搜索失败:', error);
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  /**
   * 筛选器变更
   */
  onFilterChange: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      currentFilter: filter,
      currentPage: 1
    });
    
    this.applyFilter(filter);
  },

  /**
   * 应用筛选条件
   */
  applyFilter: function(filter) {
    try {
      let filteredCelebrities = [];

      switch (filter) {
        case 'all':
          filteredCelebrities = this.celebrityAPI.getAllCelebrities();
          this.setData({ showStatistics: true });
          break;

        case 'dynasty':
          // 按朝代分组显示，这里简化为显示清朝名人
          filteredCelebrities = this.celebrityAPI.searchCelebrities({ dynasty: '清朝' });
          this.setData({ showStatistics: false });
          break;

        case 'pattern':
          // 显示正官格名人
          filteredCelebrities = this.celebrityAPI.searchCelebrities({ pattern: '正官格' });
          this.setData({ showStatistics: false });
          break;

        case 'occupation':
          // 显示政治家
          filteredCelebrities = this.celebrityAPI.searchCelebrities({ occupation: '政治家' });
          this.setData({ showStatistics: false });
          break;

        case 'verification':
          // 显示高验证度名人
          filteredCelebrities = this.celebrityAPI.searchCelebrities({ minScore: 0.9 });
          this.setData({ showStatistics: false });
          break;

        default:
          filteredCelebrities = this.celebrityAPI.getAllCelebrities();
      }
      
      const displayedCelebrities = this.paginateCelebrities(filteredCelebrities, 1, this.data.pageSize);
      
      this.setData({
        allCelebrities: filteredCelebrities,
        displayedCelebrities: displayedCelebrities,
        hasMore: filteredCelebrities.length > this.data.pageSize
      });
      
      console.log(`🏷️ 筛选 "${filter}" 找到 ${filteredCelebrities.length} 位名人`);
    } catch (error) {
      console.error('❌ 筛选失败:', error);
      wx.showToast({
        title: '筛选失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载更多
   */
  onLoadMore: function() {
    if (!this.data.hasMore) return;
    
    const nextPage = this.data.currentPage + 1;
    const newCelebrities = this.paginateCelebrities(
      this.data.allCelebrities, 
      nextPage, 
      this.data.pageSize
    );
    
    if (newCelebrities.length === 0) {
      this.setData({ hasMore: false });
      return;
    }
    
    const updatedDisplayed = [...this.data.displayedCelebrities, ...newCelebrities];
    const hasMore = updatedDisplayed.length < this.data.allCelebrities.length;
    
    this.setData({
      displayedCelebrities: updatedDisplayed,
      currentPage: nextPage,
      hasMore: hasMore
    });
    
    console.log(`📄 加载第 ${nextPage} 页，新增 ${newCelebrities.length} 位名人`);
  },

  /**
   * 点击名人卡片
   */
  onCelebrityTap: function(e) {
    const celebrity = e.currentTarget.dataset.celebrity;
    this.setData({
      selectedCelebrity: celebrity,
      showModal: true
    });
    
    console.log('👤 查看名人详情:', celebrity.basicInfo.name);
  },

  /**
   * 关闭弹窗
   */
  onModalClose: function() {
    this.setData({
      showModal: false,
      selectedCelebrity: {}
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function() {
    return {
      title: '历史名人命理数据库 - 权威验证的古代名人八字分析',
      path: '/pages/celebrity-database/celebrity-database',
      imageUrl: '/images/celebrity-database-share.jpg'
    };
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('🔄 下拉刷新数据库');
    
    // 重置状态
    this.setData({
      searchKeyword: '',
      currentFilter: 'all',
      currentPage: 1,
      showStatistics: true
    });
    
    // 重新加载数据
    this.loadStatistics();
    this.loadCelebrities();
    
    // 停止下拉刷新
    wx.stopPullDownRefresh();
    
    wx.showToast({
      title: '刷新完成',
      icon: 'success'
    });
  },

  /**
   * 页面滚动到底部
   */
  onReachBottom: function() {
    if (this.data.hasMore) {
      this.onLoadMore();
    }
  }
});
