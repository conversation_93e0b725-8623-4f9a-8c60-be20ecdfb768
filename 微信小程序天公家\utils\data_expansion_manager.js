/**
 * 数据扩展管理器
 * 负责集成更多古籍来源和规则数据
 */

class DataExpansionManager {
  constructor() {
    this.expandedSources = new Map();
    this.loadedSources = new Set();
    this.expansionStats = {
      totalSources: 0,
      totalRules: 0,
      loadTime: 0
    };
  }

  /**
   * 初始化数据扩展
   */
  async initialize() {
    console.log('🚀 开始数据扩展初始化...');
    const startTime = Date.now();

    try {
      // 加载扩展古籍数据源
      await this.loadExpandedSources();
      
      // 处理和整合数据
      await this.processExpandedData();
      
      const endTime = Date.now();
      this.expansionStats.loadTime = endTime - startTime;
      
      console.log('✅ 数据扩展初始化完成');
      console.log(`   📊 扩展来源: ${this.expansionStats.totalSources}个`);
      console.log(`   📚 扩展规则: ${this.expansionStats.totalRules}条`);
      console.log(`   ⏱️ 加载耗时: ${this.expansionStats.loadTime}ms`);
      
      return true;
    } catch (error) {
      console.error('❌ 数据扩展初始化失败:', error);
      return false;
    }
  }

  /**
   * 加载扩展古籍数据源
   */
  async loadExpandedSources() {
    // 定义扩展数据源
    const expansionSources = [
      {
        name: '神峰通考',
        file: 'shenfeng_tongkao_rules.json',
        description: '明代张楠著，格局理论权威',
        priority: 0.9
      },
      {
        name: '子平真诠',
        file: 'ziping_zhenquan_rules.json', 
        description: '清代沈孝瞻著，用神理论经典',
        priority: 0.95
      },
      {
        name: '命理探源',
        file: 'mingli_tanyuan_rules.json',
        description: '民国袁树珊著，现代命理集大成',
        priority: 0.85
      },
      {
        name: '星平会海',
        file: 'xingping_huihai_rules.json',
        description: '明代万民英著，星命合参',
        priority: 0.8
      },
      {
        name: '三车一览',
        file: 'sancha_yilan_rules.json',
        description: '明代许先潮著，实用命理',
        priority: 0.75
      }
    ];

    // 尝试加载每个数据源
    for (const source of expansionSources) {
      try {
        await this.loadSingleSource(source);
      } catch (error) {
        console.warn(`⚠️ 加载${source.name}失败:`, error.message);
        // 创建模拟数据作为备用
        this.createMockData(source);
      }
    }
  }

  /**
   * 加载单个数据源
   */
  async loadSingleSource(source) {
    try {
      // 尝试从文件加载
      const response = await fetch(`./${source.file}`);
      if (response.ok) {
        const data = await response.json();
        this.expandedSources.set(source.name, {
          ...source,
          data: data,
          loaded: true
        });
        this.loadedSources.add(source.name);
        console.log(`✅ 已加载${source.name}: ${data.rules?.length || 0}条规则`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      // 文件不存在时创建模拟数据
      console.log(`📝 为${source.name}创建模拟数据`);
      this.createMockData(source);
    }
  }

  /**
   * 创建模拟数据
   */
  createMockData(source) {
    const mockRules = this.generateMockRules(source);
    
    this.expandedSources.set(source.name, {
      ...source,
      data: {
        metadata: {
          source: source.name,
          description: source.description,
          total_rules: mockRules.length,
          created_at: new Date().toISOString(),
          mock: true
        },
        rules: mockRules
      },
      loaded: true
    });
    
    this.loadedSources.add(source.name);
    console.log(`✅ 已创建${source.name}模拟数据: ${mockRules.length}条规则`);
  }

  /**
   * 生成模拟规则数据
   */
  generateMockRules(source) {
    const ruleTemplates = this.getRuleTemplates(source.name);
    const mockRules = [];

    ruleTemplates.forEach((template, index) => {
      mockRules.push({
        rule_id: `${source.name}_${template.category}_${String(index + 1).padStart(3, '0')}`,
        pattern_name: template.pattern,
        category: template.category,
        book_source: source.name,
        original_text: template.originalText,
        interpretations: template.interpretation,
        confidence: source.priority + (Math.random() * 0.1 - 0.05), // 添加小幅随机变化
        conditions: template.conditions,
        created_at: new Date().toISOString(),
        mock: true
      });
    });

    return mockRules;
  }

  /**
   * 获取规则模板
   */
  getRuleTemplates(sourceName) {
    const templates = {
      '神峰通考': [
        {
          category: '格局理论',
          pattern: '正官格',
          originalText: '正官格者，月令正官，日主有根，财印相配，富贵双全。',
          interpretation: '正官格局成立的条件：月令透正官，日主强健有根，配合财星生官或印星护身，主富贵双全。',
          conditions: '月令正官，日主有根，财印配合'
        },
        {
          category: '格局理论', 
          pattern: '财格',
          originalText: '财格喜身强，身强能任财，财旺生官贵，财衰身弱贫。',
          interpretation: '财格的关键在于身财平衡：身强能胜财则富，财旺生官则贵，身弱财多反为贫。',
          conditions: '月令财星，身强财旺'
        },
        {
          category: '用神理论',
          pattern: '调候用神',
          originalText: '春木秋金，最宜调候。夏火冬水，调候为急。',
          interpretation: '调候用神的重要性：春季木旺需金修剪，秋季金寒需火温暖，夏火需水润，冬水需火暖。',
          conditions: '根据季节和日主五行确定'
        }
      ],
      '子平真诠': [
        {
          category: '用神理论',
          pattern: '用神专论',
          originalText: '用神者，八字之关键，命局之枢纽也。',
          interpretation: '用神是八字命局的核心，是整个命局运转的关键，决定了命运的吉凶祸福。',
          conditions: '根据日主强弱和格局确定'
        },
        {
          category: '用神理论',
          pattern: '喜忌神',
          originalText: '用神得力则吉，用神受伤则凶。喜神助用神，忌神克用神。',
          interpretation: '用神有力则命运顺遂，用神受损则命运坎坷。喜神是帮助用神的，忌神是伤害用神的。',
          conditions: '分析用神的得失和喜忌'
        }
      ],
      '命理探源': [
        {
          category: '现代理论',
          pattern: '五行平衡',
          originalText: '命局贵在中和，过旺过衰皆非美。',
          interpretation: '命局最理想的状态是五行平衡中和，过旺或过衰都不是好的状态，需要调节平衡。',
          conditions: '分析五行旺衰程度'
        },
        {
          category: '现代理论',
          pattern: '格局变化',
          originalText: '格局有成有败，成则富贵，败则贫贱。',
          interpretation: '格局的成败决定命运层次：格局成功则富贵，格局破败则贫贱，关键在于配合。',
          conditions: '分析格局的成败得失'
        }
      ],
      '星平会海': [
        {
          category: '神煞理论',
          pattern: '贵人神煞',
          originalText: '天乙贵人最为尊，遇者聪明有福分。',
          interpretation: '天乙贵人是最重要的吉神，命中有此神煞者多聪明有福，能得贵人相助。',
          conditions: '根据日干和年支或日支查找'
        },
        {
          category: '神煞理论',
          pattern: '桃花煞',
          originalText: '咸池桃花主风流，男女逢之多情愁。',
          interpretation: '桃花煞主感情丰富，异性缘佳，但也容易因情感问题而烦恼。',
          conditions: '根据年支或日支查找'
        }
      ],
      '三车一览': [
        {
          category: '实用理论',
          pattern: '六亲分析',
          originalText: '正印为母，偏财为父，正官为夫，七杀为偏夫。',
          interpretation: '十神代表六亲关系：正印代表母亲，偏财代表父亲，正官代表丈夫，七杀代表情人。',
          conditions: '根据日干和其他干支的关系确定'
        },
        {
          category: '实用理论',
          pattern: '事业分析',
          originalText: '官杀为权，财星为财，食伤为才，印绶为学。',
          interpretation: '十神代表事业方向：官杀主权力地位，财星主财富经商，食伤主才艺技能，印绶主学术文化。',
          conditions: '根据命局中十神的旺衰确定'
        }
      ]
    };

    return templates[sourceName] || [];
  }

  /**
   * 处理和整合扩展数据
   */
  async processExpandedData() {
    let totalRules = 0;
    
    for (const [sourceName, sourceData] of this.expandedSources) {
      if (sourceData.loaded && sourceData.data.rules) {
        // 数据质量检查
        const validRules = this.validateRules(sourceData.data.rules, sourceName);
        
        // 数据标准化
        const standardizedRules = this.standardizeRules(validRules, sourceName);
        
        // 更新数据
        sourceData.data.rules = standardizedRules;
        totalRules += standardizedRules.length;
        
        console.log(`📊 ${sourceName}: ${standardizedRules.length}条有效规则`);
      }
    }
    
    this.expansionStats.totalSources = this.loadedSources.size;
    this.expansionStats.totalRules = totalRules;
  }

  /**
   * 验证规则数据
   */
  validateRules(rules, sourceName) {
    return rules.filter(rule => {
      // 基本字段检查
      if (!rule.rule_id || !rule.pattern_name || !rule.category) {
        console.warn(`⚠️ ${sourceName}中发现无效规则:`, rule);
        return false;
      }
      
      // 置信度检查
      if (typeof rule.confidence !== 'number' || rule.confidence < 0 || rule.confidence > 1) {
        rule.confidence = 0.8; // 设置默认值
      }
      
      return true;
    });
  }

  /**
   * 标准化规则数据
   */
  standardizeRules(rules, sourceName) {
    return rules.map(rule => ({
      ...rule,
      book_source: sourceName,
      source_type: 'expanded',
      processed_at: new Date().toISOString(),
      // 确保必要字段存在
      original_text: rule.original_text || rule.content || '',
      interpretations: rule.interpretations || rule.interpretation || rule.modern_interpretation || '',
      conditions: rule.conditions || rule.trigger_conditions || ''
    }));
  }

  /**
   * 获取扩展规则
   */
  getExpandedRules(sourceName = null) {
    if (sourceName) {
      const source = this.expandedSources.get(sourceName);
      return source?.data?.rules || [];
    }
    
    // 返回所有扩展规则
    const allRules = [];
    for (const [name, source] of this.expandedSources) {
      if (source.loaded && source.data.rules) {
        allRules.push(...source.data.rules);
      }
    }
    
    return allRules;
  }

  /**
   * 按分类获取扩展规则
   */
  getExpandedRulesByCategory(category) {
    const allRules = this.getExpandedRules();
    return allRules.filter(rule => rule.category === category);
  }

  /**
   * 按置信度获取扩展规则
   */
  getExpandedRulesByConfidence(minConfidence = 0.8) {
    const allRules = this.getExpandedRules();
    return allRules.filter(rule => rule.confidence >= minConfidence);
  }

  /**
   * 获取扩展统计信息
   */
  getExpansionStats() {
    const sourceStats = {};
    
    for (const [name, source] of this.expandedSources) {
      sourceStats[name] = {
        loaded: source.loaded,
        ruleCount: source.data?.rules?.length || 0,
        description: source.description,
        priority: source.priority,
        mock: source.data?.metadata?.mock || false
      };
    }
    
    return {
      ...this.expansionStats,
      sources: sourceStats,
      loadedSources: Array.from(this.loadedSources)
    };
  }

  /**
   * 清理扩展数据
   */
  cleanup() {
    this.expandedSources.clear();
    this.loadedSources.clear();
    this.expansionStats = {
      totalSources: 0,
      totalRules: 0,
      loadTime: 0
    };
    console.log('🧹 扩展数据已清理');
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataExpansionManager;
} else if (typeof window !== 'undefined') {
  window.DataExpansionManager = DataExpansionManager;
}
