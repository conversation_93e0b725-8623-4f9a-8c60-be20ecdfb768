
// 数据验证和调试工具
const DataValidator = {
  // 验证八字数据完整性
  validateBaziData: function(data) {
    console.log('🔍 验证八字数据:', data);
    
    if (!data) {
      console.error('❌ 数据为空');
      return false;
    }
    
    const required = ['userInfo', 'baziInfo', 'fiveElements'];
    const missing = required.filter(key => !data[key]);
    
    if (missing.length > 0) {
      console.error('❌ 缺少必要字段:', missing);
      return false;
    }
    
    console.log('✅ 数据验证通过');
    return true;
  },
  
  // 验证本地存储数据
  validateStorageData: function() {
    console.log('🔍 验证本地存储数据:');
    
    const keys = [
      'bazi_frontend_result',
      'bazi_birth_info', 
      'bazi_analysis_mode',
      'bazi_result_id'
    ];
    
    const storage = {};
    keys.forEach(key => {
      try {
        storage[key] = wx.getStorageSync(key);
        console.log(`  ${key}: ${storage[key] ? '存在' : '不存在'}`);
      } catch (error) {
        console.error(`  ${key}: 读取失败 - ${error.message}`);
        storage[key] = null;
      }
    });
    
    return storage;
  },
  
  // 清理本地存储
  clearStorageData: function() {
    console.log('🧹 清理本地存储数据');
    const keys = [
      'bazi_frontend_result',
      'bazi_birth_info',
      'bazi_analysis_mode', 
      'bazi_result_id'
    ];
    
    keys.forEach(key => {
      try {
        wx.removeStorageSync(key);
        console.log(`  已清理: ${key}`);
      } catch (error) {
        console.error(`  清理失败: ${key} - ${error.message}`);
      }
    });
  }
};

// 导出验证工具
module.exports = DataValidator;
