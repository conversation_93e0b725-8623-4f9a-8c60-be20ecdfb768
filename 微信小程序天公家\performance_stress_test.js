/**
 * 专业级五行计算引擎 - 性能压力测试
 * 测试大量计算请求下的性能表现和内存使用情况
 */

const ProfessionalWuxingEngine = require('./utils/professional_wuxing_engine.js');

class PerformanceStressTest {
  constructor() {
    this.engine = new ProfessionalWuxingEngine();
    this.testResults = [];
  }

  // 生成随机四柱数据
  generateRandomFourPillars() {
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    const fourPillars = [];
    for (let i = 0; i < 4; i++) {
      const gan = heavenlyStems[Math.floor(Math.random() * heavenlyStems.length)];
      const zhi = earthlyBranches[Math.floor(Math.random() * earthlyBranches.length)];
      fourPillars.push({ gan, zhi });
    }
    
    return fourPillars;
  }

  // 单次性能测试
  async performSingleTest() {
    const fourPillars = this.generateRandomFourPillars();
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();
    
    try {
      const result = this.engine.generateDetailedReport(fourPillars);
      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();
      
      const executionTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
      const memoryDelta = {
        rss: endMemory.rss - startMemory.rss,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external
      };
      
      return {
        success: true,
        fourPillars: fourPillars.map(p => p.gan + p.zhi).join(' '),
        executionTime,
        memoryDelta,
        totalPower: result.results.statistics.totalPower
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const executionTime = Number(endTime - startTime) / 1000000;
      
      return {
        success: false,
        fourPillars: fourPillars.map(p => p.gan + p.zhi).join(' '),
        executionTime,
        error: error.message
      };
    }
  }

  // 批量性能测试
  async performBatchTest(batchSize = 100) {
    console.log(`🚀 开始批量性能测试 (${batchSize}次计算)`);
    
    const results = [];
    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    
    for (let i = 0; i < batchSize; i++) {
      const result = await this.performSingleTest();
      results.push(result);
      
      if ((i + 1) % 10 === 0) {
        process.stdout.write(`\r进度: ${i + 1}/${batchSize} (${((i + 1) / batchSize * 100).toFixed(1)}%)`);
      }
    }
    
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    
    console.log('\n✅ 批量测试完成');
    
    return {
      batchSize,
      totalTime: endTime - startTime,
      totalMemoryDelta: {
        rss: endMemory.rss - startMemory.rss,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external
      },
      results
    };
  }

  // 分析性能结果
  analyzePerformanceResults(batchResult) {
    const { results, totalTime, totalMemoryDelta, batchSize } = batchResult;
    
    const successfulResults = results.filter(r => r.success);
    const failedResults = results.filter(r => !r.success);
    
    if (successfulResults.length === 0) {
      return {
        success: false,
        message: '所有测试都失败了'
      };
    }
    
    // 计算执行时间统计
    const executionTimes = successfulResults.map(r => r.executionTime);
    const avgExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
    const minExecutionTime = Math.min(...executionTimes);
    const maxExecutionTime = Math.max(...executionTimes);
    
    // 计算内存使用统计
    const memoryDeltas = successfulResults.map(r => r.memoryDelta);
    const avgMemoryDelta = {
      rss: memoryDeltas.reduce((sum, delta) => sum + delta.rss, 0) / memoryDeltas.length,
      heapUsed: memoryDeltas.reduce((sum, delta) => sum + delta.heapUsed, 0) / memoryDeltas.length,
      heapTotal: memoryDeltas.reduce((sum, delta) => sum + delta.heapTotal, 0) / memoryDeltas.length,
      external: memoryDeltas.reduce((sum, delta) => sum + delta.external, 0) / memoryDeltas.length
    };
    
    // 计算吞吐量
    const throughput = (successfulResults.length / totalTime) * 1000; // 每秒处理数
    
    return {
      success: true,
      statistics: {
        totalTests: batchSize,
        successfulTests: successfulResults.length,
        failedTests: failedResults.length,
        successRate: (successfulResults.length / batchSize) * 100,
        totalTime,
        throughput,
        executionTime: {
          average: avgExecutionTime,
          min: minExecutionTime,
          max: maxExecutionTime
        },
        memoryUsage: {
          average: avgMemoryDelta,
          total: totalMemoryDelta
        }
      }
    };
  }

  // 格式化内存大小
  formatMemorySize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 生成性能报告
  generatePerformanceReport(analysis) {
    if (!analysis.success) {
      console.log('❌ 性能测试失败:', analysis.message);
      return;
    }
    
    const stats = analysis.statistics;
    
    console.log('\n📊 性能测试报告');
    console.log('=' .repeat(60));
    
    console.log('\n🎯 测试概况:');
    console.log(`  总测试数: ${stats.totalTests}`);
    console.log(`  成功数: ${stats.successfulTests}`);
    console.log(`  失败数: ${stats.failedTests}`);
    console.log(`  成功率: ${stats.successRate.toFixed(2)}%`);
    
    console.log('\n⏱️ 性能指标:');
    console.log(`  总耗时: ${stats.totalTime} ms`);
    console.log(`  吞吐量: ${stats.throughput.toFixed(2)} 次/秒`);
    console.log(`  平均执行时间: ${stats.executionTime.average.toFixed(2)} ms`);
    console.log(`  最快执行时间: ${stats.executionTime.min.toFixed(2)} ms`);
    console.log(`  最慢执行时间: ${stats.executionTime.max.toFixed(2)} ms`);
    
    console.log('\n💾 内存使用:');
    console.log(`  总内存变化:`);
    console.log(`    RSS: ${this.formatMemorySize(stats.memoryUsage.total.rss)}`);
    console.log(`    Heap Used: ${this.formatMemorySize(stats.memoryUsage.total.heapUsed)}`);
    console.log(`    Heap Total: ${this.formatMemorySize(stats.memoryUsage.total.heapTotal)}`);
    console.log(`    External: ${this.formatMemorySize(stats.memoryUsage.total.external)}`);
    
    console.log(`  平均单次内存变化:`);
    console.log(`    RSS: ${this.formatMemorySize(stats.memoryUsage.average.rss)}`);
    console.log(`    Heap Used: ${this.formatMemorySize(stats.memoryUsage.average.heapUsed)}`);
    
    console.log('\n🎯 性能评估:');
    if (stats.executionTime.average < 50) {
      console.log('✅ 执行速度: 优秀 (<50ms)');
    } else if (stats.executionTime.average < 100) {
      console.log('✅ 执行速度: 良好 (<100ms)');
    } else if (stats.executionTime.average < 200) {
      console.log('⚠️ 执行速度: 一般 (<200ms)');
    } else {
      console.log('❌ 执行速度: 需要优化 (>200ms)');
    }
    
    if (stats.throughput > 20) {
      console.log('✅ 吞吐量: 优秀 (>20次/秒)');
    } else if (stats.throughput > 10) {
      console.log('✅ 吞吐量: 良好 (>10次/秒)');
    } else if (stats.throughput > 5) {
      console.log('⚠️ 吞吐量: 一般 (>5次/秒)');
    } else {
      console.log('❌ 吞吐量: 需要优化 (<5次/秒)');
    }
    
    if (stats.memoryUsage.average.heapUsed < 1024 * 1024) { // 1MB
      console.log('✅ 内存使用: 优秀 (<1MB/次)');
    } else if (stats.memoryUsage.average.heapUsed < 5 * 1024 * 1024) { // 5MB
      console.log('✅ 内存使用: 良好 (<5MB/次)');
    } else {
      console.log('⚠️ 内存使用: 需要关注 (>5MB/次)');
    }
    
    if (stats.successRate >= 99) {
      console.log('✅ 稳定性: 优秀 (>99%)');
    } else if (stats.successRate >= 95) {
      console.log('✅ 稳定性: 良好 (>95%)');
    } else {
      console.log('❌ 稳定性: 需要改进 (<95%)');
    }
  }

  // 运行完整的性能压力测试
  async runFullStressTest() {
    console.log('🚀 开始专业级五行计算引擎性能压力测试');
    console.log('=' .repeat(60));
    
    // 测试不同批次大小
    const batchSizes = [10, 50, 100, 500];
    
    for (const batchSize of batchSizes) {
      console.log(`\n📋 测试批次大小: ${batchSize}`);
      console.log('-' .repeat(40));
      
      const batchResult = await this.performBatchTest(batchSize);
      const analysis = this.analyzePerformanceResults(batchResult);
      
      console.log(`\n📊 批次 ${batchSize} 结果:`);
      if (analysis.success) {
        const stats = analysis.statistics;
        console.log(`  成功率: ${stats.successRate.toFixed(1)}%`);
        console.log(`  平均执行时间: ${stats.executionTime.average.toFixed(2)} ms`);
        console.log(`  吞吐量: ${stats.throughput.toFixed(2)} 次/秒`);
        console.log(`  平均内存使用: ${this.formatMemorySize(stats.memoryUsage.average.heapUsed)}`);
      } else {
        console.log('❌ 测试失败');
      }
      
      this.testResults.push({ batchSize, analysis });
    }
    
    // 生成综合报告
    console.log('\n📋 综合性能报告');
    console.log('=' .repeat(60));
    
    const lastResult = this.testResults[this.testResults.length - 1];
    if (lastResult && lastResult.analysis.success) {
      this.generatePerformanceReport(lastResult.analysis);
    }
    
    console.log('\n🎉 性能压力测试完成！');
  }
}

// 执行测试
if (require.main === module) {
  const stressTest = new PerformanceStressTest();
  stressTest.runFullStressTest().catch(error => {
    console.error('❌ 性能测试执行失败:', error);
  });
}

module.exports = PerformanceStressTest;
