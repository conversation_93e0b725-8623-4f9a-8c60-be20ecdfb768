/**
 * 语义搜索引擎 - 统一数据访问层版本
 * 基于《玉匣记》完整数据库的智能问题匹配系统
 * 现在使用统一数据访问层进行搜索
 */

const SemanticSearchAdapter = require('./semantic_search_adapter');

/**
 * 语义搜索引擎类 - 升级版
 */
class SemanticSearchEngine {
  
  /**
   * 智能问题分析和古典文献匹配
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @returns {Promise<object>} 匹配结果
   */
  static async searchClassicalText(questionText, godName) {
    console.log('🔍 启动统一数据访问层语义搜索引擎');
    console.log('问题:', questionText, '六神:', godName);
    
    try {
      // 使用统一数据访问层进行搜索
      const result = await SemanticSearchAdapter.searchClassicalText(questionText, godName);
      console.log('✅ 统一数据访问层搜索完成');
      return result;
    } catch (error) {
      console.error('❌ 统一数据访问层搜索失败:', error);
      throw error;
    }
  }
  
  /**
   * 同步版本的搜索方法（向下兼容）
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @returns {object} 匹配结果
   */
  static searchClassicalTextSync(questionText, godName) {
    console.log('⚠️ 使用同步版本的语义搜索（兼容模式）');

    // 小程序环境使用本地逻辑
    try {
      return SemanticSearchAdapter.fallbackToOriginalLogic(questionText, godName);
    } catch (error) {
      console.error('❌ 同步搜索也失败了:', error);

      // 返回基本结果
      return SemanticSearchAdapter.getBasicFallbackResult();
    }
  }
  
  /**
   * 混合搜索方法
   * @param {string} questionText - 用户问题
   * @param {string} godName - 六神名称
   * @param {string} keyword - 关键词
   * @returns {Promise<object>} 搜索结果
   */
  static async hybridSearch(questionText, godName, keyword) {
    console.log('🔄 启动混合搜索');
    
    try {
      const result = await SemanticSearchAdapter.hybridSearch(questionText, godName, keyword);
      console.log('✅ 混合搜索完成');
      return result;
    } catch (error) {
      console.error('❌ 混合搜索失败:', error);
      throw error;
    }
  }
  
  /**
   * 关键词搜索方法
   * @param {string} keyword - 关键词
   * @param {object} filters - 过滤条件
   * @returns {Promise<object>} 搜索结果
   */
  static async keywordSearch(keyword, filters = {}) {
    console.log('🔍 启动关键词搜索');
    
    try {
      const result = await SemanticSearchAdapter.keywordSearch(keyword, filters);
      console.log('✅ 关键词搜索完成');
      return result;
    } catch (error) {
      console.error('❌ 关键词搜索失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取统计信息
   * @returns {Promise<object>} 统计信息
   */
  static async getStatistics() {
    console.log('📊 获取统计信息');
    
    try {
      // 通过Python脚本获取统计信息
      const result = await SemanticSearchAdapter.callUnifiedDataAccess({
        type: 'statistics'
      });
      console.log('✅ 统计信息获取完成');
      return result;
    } catch (error) {
      console.error('❌ 获取统计信息失败:', error);
      return {
        total: 0,
        byGod: [],
        byDomain: []
      };
    }
  }
  
  /**
   * 清理缓存
   * @returns {Promise<void>}
   */
  static async clearCache() {
    console.log('🧹 清理缓存');
    
    try {
      await SemanticSearchAdapter.callUnifiedDataAccess({
        type: 'clear_cache'
      });
      console.log('✅ 缓存清理完成');
    } catch (error) {
      console.error('❌ 缓存清理失败:', error);
    }
  }
}

module.exports = SemanticSearchEngine;
