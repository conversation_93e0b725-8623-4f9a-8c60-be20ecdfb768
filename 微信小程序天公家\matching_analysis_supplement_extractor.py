#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匹配分析补充提取工具
专门针对匹配分析模块的60条缺口进行补充
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class MatchingAnalysisSupplementExtractor:
    def __init__(self):
        self.rule_id_counter = 950000
        self.target_supplement = 60  # 需要补充的数量
        
        # 匹配分析专用提取配置
        self.matching_analysis_config = {
            "渊海子平": {
                "file": "渊海子平.docx",
                "focus_patterns": [
                    # 夫妻配偶相关
                    r'[^。]*?[夫妻|配偶|婚姻|合婚|姻缘|情缘|恋爱|爱情|感情][^。]*?。',
                    r'[^。]*?[男女|阴阳|乾坤][^。]*?[配|合|和谐|协调|冲克|刑害][^。]*?。',
                    r'[^。]*?[夫星|妻星|夫宫|妻宫|配偶星|配偶宫|婚姻宫][^。]*?。',
                    r'[^。]*?[正官|偏官|正财|偏财|食神|伤官][^。]*?[夫妻|配偶|婚姻|感情][^。]*?。',
                    r'[^。]*?[日主|日元|日干][^。]*?[配偶|夫妻|婚姻|感情][^。]*?[关系|情况|状态][^。]*?。',
                    r'[^。]*?[相处|交往|互动|沟通|理解|包容|支持][^。]*?[和谐|融洽|美满|幸福][^。]*?。',
                    r'[^。]*?[合|冲|刑|害|破|穿][^。]*?[夫妻|配偶|婚姻|感情][^。]*?。',
                    r'[^。]*?[吉|凶|好|坏|利|害|顺|逆|美满|幸福][^。]*?[夫妻|配偶|婚姻][^。]*?。',
                    r'[^。]*?[夫妻][^。]*?[性格|脾气|品性|相貌|才能|能力][^。]*?。',
                    r'[^。]*?[配偶][^。]*?[外貌|长相|身材|气质|性情|品德][^。]*?。'
                ],
                "target": 40
            },
            "三命通会": {
                "file": "《三命通会》完整白话版  .pdf",
                "focus_patterns": [
                    # 婚姻神煞配合
                    r'[^。]*?[夫妻|配偶|婚姻][^。]*?[神煞|贵人|凶神|吉神][^。]*?。',
                    r'[^。]*?[男女|阴阳][^。]*?[配合|搭配|组合|匹配][^。]*?。',
                    r'[^。]*?[合|冲|刑|害][^。]*?[夫妻|配偶|婚姻|感情][^。]*?。',
                    r'[^。]*?[桃花|红鸾|天喜|咸池][^。]*?[婚姻|感情|配偶][^。]*?。',
                    r'[^。]*?[夫妻宫|配偶宫|婚姻宫][^。]*?[神煞|星曜][^。]*?。'
                ],
                "target": 15
            },
            "千里命稿": {
                "file": "千里命稿.txt",
                "focus_patterns": [
                    # 实用配偶分析
                    r'[^。]*?[配偶|夫妻|婚姻][^。]*?[分析|判断|推断|论断][^。]*?。',
                    r'[^。]*?[夫星|妻星][^。]*?[强弱|旺衰|有无|得失][^。]*?。',
                    r'[^。]*?[婚姻][^。]*?[早晚|迟早|时间|年龄][^。]*?。'
                ],
                "target": 5
            }
        }
        
        # 匹配分析关键词
        self.matching_keywords = [
            "夫妻", "配偶", "婚姻", "合婚", "配合", "和谐", "感情", "情缘",
            "恋爱", "爱情", "相处", "交往", "互动", "沟通", "理解", "包容",
            "夫星", "妻星", "夫宫", "妻宫", "配偶星", "配偶宫", "姻缘", "缘分",
            "男女", "阴阳", "关系", "情况", "状态", "美满", "幸福", "和睦",
            "桃花", "红鸾", "天喜", "咸池", "性格", "脾气", "相貌", "外貌"
        ]
    
    def load_book_content(self, book_name: str) -> str:
        """加载古籍内容"""
        if book_name not in self.matching_analysis_config:
            return ""
        
        filename = self.matching_analysis_config[book_name]["file"]
        file_path = os.path.join("古籍资料", filename)
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在: {filename}")
            return ""
        
        try:
            if filename.endswith('.txt'):
                return self._load_txt_file(file_path)
            elif filename.endswith('.docx'):
                return self._load_docx_file(file_path)
            elif filename.endswith('.pdf'):
                return self._load_pdf_file(file_path)
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
            return ""
        
        return ""
    
    def _load_txt_file(self, file_path: str) -> str:
        """加载TXT文件"""
        encodings = ['utf-8', 'gbk', 'gb2312']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    print(f"  ✅ TXT加载成功: {len(content):,} 字符")
                    return content
            except UnicodeDecodeError:
                continue
        return ""
    
    def _load_docx_file(self, file_path: str) -> str:
        """加载DOCX文件"""
        try:
            from docx import Document
            doc = Document(file_path)
            content = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            print(f"  ✅ DOCX加载成功: {len(content):,} 字符")
            return content
        except ImportError:
            print("  需要安装python-docx库")
            return ""
        except Exception:
            return ""
    
    def _load_pdf_file(self, file_path: str) -> str:
        """加载PDF文件"""
        try:
            import PyPDF2
            content_parts = []
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                for i in range(total_pages):
                    try:
                        page_text = pdf_reader.pages[i].extract_text()
                        if page_text and len(page_text.strip()) > 20:
                            cleaned_text = re.sub(r'\s+', ' ', page_text).strip()
                            content_parts.append(cleaned_text)
                    except:
                        continue
                
                content = '\n'.join(content_parts)
                print(f"  ✅ PDF加载成功: {len(content):,} 字符")
                return content
        except ImportError:
            print("  需要安装PyPDF2库")
            return ""
        except Exception:
            return ""
    
    def extract_matching_analysis_rules(self, content: str, book_name: str, config: Dict) -> List[Dict]:
        """提取匹配分析规则"""
        if not content:
            return []
        
        patterns = config["focus_patterns"]
        target = config["target"]
        
        all_extracted = []
        
        print(f"  🎯 专门提取匹配分析规则 (目标: {target}条)...")
        
        # 1. 专用模式匹配
        for i, pattern in enumerate(patterns):
            try:
                matches = re.findall(pattern, content)
                print(f"    专用模式{i+1}: {len(matches)}条")
                
                for match in matches:
                    cleaned_text = self._clean_matching_text(match)
                    if self._validate_matching_rule(cleaned_text):
                        rule = self._create_matching_rule(cleaned_text, book_name, f"专用模式{i+1}")
                        all_extracted.append(rule)
            except Exception as e:
                continue
        
        # 2. 关键词密集提取
        keyword_rules = self._extract_matching_keywords(content, book_name)
        all_extracted.extend(keyword_rules)
        
        # 3. 夫妻关系专项提取
        relationship_rules = self._extract_relationship_patterns(content, book_name)
        all_extracted.extend(relationship_rules)
        
        # 4. 去重和筛选
        unique_rules = self._deduplicate_matching_rules(all_extracted)
        
        # 5. 按质量排序并限制数量
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        final_rules = unique_rules[:target]
        
        print(f"  ✅ 提取完成: {len(final_rules)}条匹配分析规则")
        return final_rules
    
    def _extract_matching_keywords(self, content: str, book_name: str) -> List[Dict]:
        """基于关键词提取匹配分析规则"""
        rules = []
        sentences = re.split(r'[。；！？]', content)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if 25 <= len(sentence) <= 400:
                # 检查匹配分析关键词
                keyword_count = sum(1 for keyword in self.matching_keywords if keyword in sentence)
                if keyword_count >= 2:  # 至少包含2个相关关键词
                    cleaned_text = self._clean_matching_text(sentence)
                    if self._validate_matching_rule(cleaned_text):
                        rule = self._create_matching_rule(cleaned_text, book_name, "关键词提取")
                        rules.append(rule)
        
        return rules
    
    def _extract_relationship_patterns(self, content: str, book_name: str) -> List[Dict]:
        """提取夫妻关系模式"""
        rules = []
        
        # 专门寻找夫妻关系描述
        relationship_patterns = [
            r'[^。]*?夫妻[^。]*?[和睦|不和|相克|相生|相配|不配][^。]*?。',
            r'[^。]*?配偶[^。]*?[性格|脾气|外貌|才能|品德][^。]*?。',
            r'[^。]*?婚姻[^。]*?[美满|幸福|波折|困难|顺利][^。]*?。'
        ]
        
        for pattern in relationship_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                cleaned_text = self._clean_matching_text(match)
                if self._validate_matching_rule(cleaned_text):
                    rule = self._create_matching_rule(cleaned_text, book_name, "关系模式")
                    rules.append(rule)
        
        return rules
    
    def _clean_matching_text(self, text: str) -> str:
        """清理匹配分析文本"""
        if not text:
            return ""
        
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 移除无关内容
        text = text.replace('注：', '').replace('按：', '')
        text = text.replace('又云：', '').replace('古云：', '')
        
        return text
    
    def _validate_matching_rule(self, text: str) -> bool:
        """验证匹配分析规则"""
        if not text or len(text) < 20 or len(text) > 500:
            return False
        
        # 必须包含匹配分析相关关键词
        matching_indicators = ["夫妻", "配偶", "婚姻", "感情", "配合", "和谐", "匹配"]
        has_matching_keyword = any(indicator in text for indicator in matching_indicators)
        
        # 必须包含关系或分析词汇
        analysis_indicators = ["关系", "情况", "状态", "分析", "判断", "相处", "交往"]
        has_analysis_keyword = any(indicator in text for indicator in analysis_indicators)
        
        return has_matching_keyword and (has_analysis_keyword or len([k for k in self.matching_keywords if k in text]) >= 2)
    
    def _create_matching_rule(self, text: str, book_name: str, method: str) -> Dict:
        """创建匹配分析规则"""
        # 计算置信度
        confidence = 0.91 + (len(text) / 1000) * 0.03
        confidence = min(0.97, confidence)
        
        rule = {
            "rule_id": f"MATCHING_{self.rule_id_counter:06d}",
            "pattern_name": f"《{book_name}》·匹配分析补充规则",
            "category": "匹配分析",
            "dimension_type": "匹配分析",
            "book_source": book_name,
            "extraction_method": method,
            "original_text": text,
            "interpretations": f"出自《{book_name}》的匹配分析权威理论，专门补充提取",
            "confidence": confidence,
            "matching_analysis_supplement": True,
            "extracted_at": datetime.now().isoformat(),
            "extraction_phase": "匹配分析专项补充",
            "rule_type": "匹配分析补充规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _deduplicate_matching_rules(self, rules: List[Dict]) -> List[Dict]:
        """去重匹配分析规则"""
        seen_texts = set()
        unique_rules = []
        
        for rule in rules:
            text = rule.get('original_text', '')
            simplified = re.sub(r'[\s\W]', '', text)[:25]  # 检查前25个字符
            
            if simplified not in seen_texts and len(simplified) > 10:
                seen_texts.add(simplified)
                unique_rules.append(rule)
        
        return unique_rules
    
    def execute_matching_supplement(self) -> Dict:
        """执行匹配分析补充"""
        print("🚀 开始匹配分析专项补充...")
        print(f"🎯 目标补充: {self.target_supplement}条规则")
        
        all_matching_rules = []
        total_extracted = 0
        
        for book_name, config in self.matching_analysis_config.items():
            print(f"\n📚 处理《{book_name}》...")
            
            content = self.load_book_content(book_name)
            if not content:
                continue
            
            rules = self.extract_matching_analysis_rules(content, book_name, config)
            all_matching_rules.extend(rules)
            total_extracted += len(rules)
        
        # 去重和最终筛选
        unique_rules = self._deduplicate_matching_rules(all_matching_rules)
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        # 限制到目标数量
        final_rules = unique_rules[:self.target_supplement]
        
        result_data = {
            "metadata": {
                "supplement_type": "匹配分析专项补充",
                "supplement_date": datetime.now().isoformat(),
                "target_supplement": self.target_supplement,
                "actual_extracted": len(final_rules),
                "completion_rate": f"{len(final_rules)/self.target_supplement*100:.1f}%",
                "books_processed": len(self.matching_analysis_config)
            },
            "supplement_rules": final_rules
        }
        
        return {
            "success": True,
            "data": result_data,
            "summary": {
                "目标补充": self.target_supplement,
                "实际提取": len(final_rules),
                "完成率": f"{len(final_rules)/self.target_supplement*100:.1f}%"
            }
        }

def main():
    """主函数"""
    extractor = MatchingAnalysisSupplementExtractor()
    
    result = extractor.execute_matching_supplement()
    
    if result.get("success"):
        output_filename = f"matching_analysis_supplement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        print("\n" + "="*80)
        print("🎉 匹配分析专项补充完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print(f"\n✅ 补充结果已保存到: {output_filename}")
        print(f"💡 下一步：整合所有补充结果")
        
    else:
        print(f"❌ 补充失败")

if __name__ == "__main__":
    main()
