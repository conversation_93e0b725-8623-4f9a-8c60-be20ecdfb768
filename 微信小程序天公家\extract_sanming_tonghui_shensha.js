/**
 * 根据《三命通会》目录信息提取神煞计算方法
 * 基于目录.txt中的页码信息，重点提取卷三中的神煞内容
 */

// 《三命通会》卷三神煞内容页码对照表
const SANMING_SHENSHA_PAGES = {
  // 卷二神煞
  '将星华盖': 52,
  '咸池': 53,
  '六害': 53,
  '三刑': 54,
  '冲击': 56,
  
  // 卷三神煞（重点内容）
  '十干禄': 57,
  '金舆': 60,
  '驿马': 60,
  '总论禄马': 64,
  '天乙贵人': 64,  // 重点
  '三奇': 67,
  '天月德': 68,    // 重点
  '太极贵': 69,
  '学堂词馆': 70,
  '正印': 71,
  '德秀': 72,      // 重点
  '劫煞亡神': 72,
  '羊刃': 73,      // 重点
  '空亡': 74,      // 重点
  '元辰': 76,      // 重点
  '暗金的煞': 77,
  '灾煞': 78,      // 重点
  '六厄': 78,
  '勾绞': 78,
  '孤辰寡宿': 79,  // 重点
  '天罗地网': 80,
  '十恶大败': 81,
  '总论诸神煞': 81
};

// 我们当前系统缺失的神煞（根据"问真八字"标准）
const MISSING_SHENSHA = [
  '福星贵人',    // 年柱、日柱需要
  '天厨贵人',    // 日柱需要
  '德秀贵人',    // 日柱需要
  '童子煞',      // 日柱需要
  '血刃',        // 日柱需要
  '元辰',        // 月柱需要
  '寡宿',        // 时柱需要
  '披麻',        // 时柱需要
  '灾煞',        // 日柱需要
  '丧门',        // 日柱需要
  '月德合',      // 年柱需要
  '天乙贵人',    // 月柱缺失（需要昼夜贵人规则）
  '桃花'         // 月柱需要（需要多种桃花类型）
];

// 当前测试数据
const TEST_BAZI = {
  year: { gan: '辛', zhi: '丑' },
  month: { gan: '甲', zhi: '午' },
  day: { gan: '癸', zhi: '卯' },
  hour: { gan: '壬', zhi: '戌' }
};

// "问真八字"标准结果
const WENZHEN_STANDARD = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

console.log('=== 《三命通会》神煞提取分析 ===');
console.log('');

console.log('📚 根据目录信息，《三命通会》神煞内容分布：');
console.log('');

console.log('🔍 卷二神煞内容：');
Object.entries(SANMING_SHENSHA_PAGES).slice(0, 5).forEach(([name, page]) => {
  console.log(`  - ${name}：第${page}页`);
});

console.log('');
console.log('🔍 卷三神煞内容（重点）：');
Object.entries(SANMING_SHENSHA_PAGES).slice(5).forEach(([name, page]) => {
  const isImportant = MISSING_SHENSHA.some(missing => name.includes(missing.replace('贵人', '').replace('煞', '')));
  const marker = isImportant ? '⭐' : '  ';
  console.log(`${marker}- ${name}：第${page}页`);
});

console.log('');
console.log('🎯 我们急需提取的神煞计算方法：');
MISSING_SHENSHA.forEach(shensha => {
  const found = Object.keys(SANMING_SHENSHA_PAGES).find(key => 
    key.includes(shensha.replace('贵人', '').replace('煞', ''))
  );
  if (found) {
    console.log(`  ✅ ${shensha} -> 《三命通会》"${found}"章节（第${SANMING_SHENSHA_PAGES[found]}页）`);
  } else {
    console.log(`  ❌ ${shensha} -> 需要在其他章节或古籍中查找`);
  }
});

console.log('');
console.log('📋 提取策略：');
console.log('1. 重点提取第64页"论天乙贵人"章节，获取昼夜贵人计算规则');
console.log('2. 重点提取第68页"论天月德"章节，获取月德合计算方法');
console.log('3. 重点提取第72页"论德秀"章节，获取德秀贵人计算方法');
console.log('4. 重点提取第76页"论元辰"章节，获取元辰计算方法');
console.log('5. 重点提取第78页"论灾煞"章节，获取灾煞计算方法');
console.log('6. 重点提取第79页"论孤辰寡宿"章节，获取寡宿、披麻计算方法');
console.log('7. 重点提取第53页"论咸池"章节，获取桃花计算方法');

console.log('');
console.log('🔧 下一步行动：');
console.log('1. 尝试使用PDF文本提取工具获取指定页面内容');
console.log('2. 如果PDF无法直接提取，搜索网络上的《三命通会》数字化版本');
console.log('3. 根据提取的计算规则，更新前端神煞计算函数');
console.log('4. 验证更新后的计算结果与"问真八字"标准的匹配度');

console.log('');
console.log('📊 当前进度统计：');
console.log(`- 总需求神煞数量：${MISSING_SHENSHA.length}`);
console.log(`- 《三命通会》可找到：${MISSING_SHENSHA.filter(s => Object.keys(SANMING_SHENSHA_PAGES).some(k => k.includes(s.replace('贵人', '').replace('煞', '')))).length}`);
console.log(`- 需要其他途径：${MISSING_SHENSHA.filter(s => !Object.keys(SANMING_SHENSHA_PAGES).some(k => k.includes(s.replace('贵人', '').replace('煞', '')))).length}`);

// 创建PDF页面提取计划
console.log('');
console.log('📄 PDF页面提取计划：');
const PRIORITY_PAGES = [64, 68, 72, 76, 78, 79, 53];
PRIORITY_PAGES.forEach(page => {
  const chapters = Object.entries(SANMING_SHENSHA_PAGES).filter(([name, p]) => p === page);
  console.log(`  第${page}页：${chapters.map(([name]) => name).join('、')}`);
});
