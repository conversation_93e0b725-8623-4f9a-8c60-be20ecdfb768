/**
 * 运行数据库合并工具
 */

const DatabaseMerger = require('./utils/database_merger.js');

async function runMerger() {
  try {
    const merger = new DatabaseMerger();
    const result = merger.runMergeProcess();
    
    if (result.success) {
      console.log('\n✅ 数据库合并成功完成!');
    } else {
      console.error('\n❌ 数据库合并失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 运行失败:', error);
  }
}

// 运行合并工具
runMerger();
