/**
 * 版本切换管理器
 * 支持传统、专业、融合三种显示模式的智能切换
 * 第三阶段：专业解读功能优化 - 第三步
 */

class VersionSwitchManager {
  constructor() {
    this.currentVersion = 'traditional'; // 默认传统模式
    this.isInitialized = false;
    
    // 版本定义
    this.versions = {
      traditional: {
        name: '传统展示',
        description: '经典的古籍文字分析',
        icon: '📜',
        components: ['classical-text-display'],
        features: ['文字解读', '古籍引用', '传统格局分析'],
        suitableFor: '喜欢传统文化的用户',
        complexity: 'simple',
        dataRequirement: 'basic'
      },
      
      professional: {
        name: '专业数字化',
        description: '数字化可视化分析',
        icon: '📊',
        components: ['wuxing-radar', 'balance-meter', 'rule-matcher', 'numerical-analysis'],
        features: ['五行雷达图', '平衡指标', 'AI规则匹配', '数值分析', '综合报告'],
        suitableFor: '喜欢数据分析的用户',
        complexity: 'advanced',
        dataRequirement: 'comprehensive'
      },
      
      hybrid: {
        name: '融合模式',
        description: '传统与现代相结合',
        icon: '🔄',
        components: ['classical-text-display', 'balance-meter', 'enhanced-rules'],
        features: ['传统解读', '数字化图表', '双重验证', '智能匹配'],
        suitableFor: '希望全面了解的用户',
        complexity: 'moderate',
        dataRequirement: 'enhanced'
      }
    };

    // 用户画像因子
    this.userProfileFactors = {
      age: { weight: 0.3, ranges: { young: [18, 30], middle: [31, 50], senior: [51, 80] } },
      techSavviness: { weight: 0.4, levels: ['low', 'medium', 'high'] },
      experience: { weight: 0.2, levels: ['beginner', 'intermediate', 'expert'] },
      preference: { weight: 0.1, types: ['traditional', 'modern', 'balanced'] }
    };

    // 版本切换历史
    this.switchHistory = [];
    
    // 性能统计
    this.switchStats = {
      totalSwitches: 0,
      versionUsage: {
        traditional: 0,
        professional: 0,
        hybrid: 0
      },
      userSatisfaction: new Map(),
      avgSwitchTime: 0
    };

    // 组件加载状态
    this.componentLoadState = new Map();
    
    // 事件监听器
    this.eventListeners = new Map();
  }

  /**
   * 初始化版本切换管理器
   */
  async initialize(isolationManager = null) {
    if (this.isInitialized) {
      console.log('⚠️ 版本切换管理器已经初始化');
      return true;
    }

    try {
      console.log('🚀 开始初始化版本切换管理器...');

      // 保存隔离管理器引用
      this.isolationManager = isolationManager;

      // 加载用户偏好
      await this.loadUserPreferences();

      // 初始化组件加载状态
      this.initializeComponentLoadState();

      // 注册事件监听器
      this.registerEventListeners();

      // 预加载关键组件
      await this.preloadCriticalComponents();

      this.isInitialized = true;

      console.log('✅ 版本切换管理器初始化完成');
      console.log(`   📊 当前版本: ${this.currentVersion}`);
      console.log(`   📊 可用版本: ${Object.keys(this.versions).join(', ')}`);

      return true;
    } catch (error) {
      console.error('❌ 版本切换管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 加载用户偏好
   */
  async loadUserPreferences() {
    try {
      // 从隔离存储中加载用户偏好
      if (this.isolationManager) {
        const savedVersion = this.isolationManager.isolatedStorage.getLocal('preferredVersion');
        const switchHistory = this.isolationManager.isolatedStorage.getLocal('versionSwitchHistory') || [];
        const userProfile = this.isolationManager.isolatedStorage.getLocal('userProfile') || {};

        if (savedVersion && this.versions[savedVersion]) {
          this.currentVersion = savedVersion;
        }

        this.switchHistory = switchHistory;
        this.userProfile = userProfile;
      }

      console.log('✅ 用户偏好加载完成');
    } catch (error) {
      console.warn('⚠️ 用户偏好加载失败，使用默认设置:', error);
    }
  }

  /**
   * 初始化组件加载状态
   */
  initializeComponentLoadState() {
    // 为每个版本的组件初始化加载状态
    Object.values(this.versions).forEach(version => {
      version.components.forEach(component => {
        this.componentLoadState.set(component, {
          loaded: false,
          loading: false,
          error: null,
          loadTime: 0
        });
      });
    });
  }

  /**
   * 注册事件监听器
   */
  registerEventListeners() {
    // 版本切换事件
    this.on('versionSwitched', (data) => {
      this.recordVersionSwitch(data.from, data.to, data.reason);
    });

    // 组件加载事件
    this.on('componentLoaded', (data) => {
      this.updateComponentLoadState(data.component, true);
    });

    // 用户反馈事件
    this.on('userFeedback', (data) => {
      this.recordUserFeedback(data.version, data.rating, data.comment);
    });
  }

  /**
   * 预加载关键组件
   */
  async preloadCriticalComponents() {
    const criticalComponents = ['classical-text-display', 'balance-meter'];
    
    for (const component of criticalComponents) {
      try {
        await this.loadComponent(component);
      } catch (error) {
        console.warn(`⚠️ 关键组件 ${component} 预加载失败:`, error);
      }
    }
  }

  /**
   * 智能版本推荐
   */
  recommendVersion(userProfile = {}, analysisHistory = {}) {
    console.log('🤖 开始智能版本推荐...');

    // 合并用户画像
    const profile = { ...this.userProfile, ...userProfile };
    
    // 计算推荐分数
    const scores = {};
    
    Object.keys(this.versions).forEach(version => {
      scores[version] = this.calculateVersionScore(version, profile, analysisHistory);
    });

    // 找出最高分版本
    const recommendedVersion = Object.keys(scores).reduce((a, b) => 
      scores[a] > scores[b] ? a : b
    );

    console.log('🎯 版本推荐结果:');
    Object.entries(scores).forEach(([version, score]) => {
      console.log(`   ${version}: ${score.toFixed(2)}分`);
    });
    console.log(`   推荐版本: ${recommendedVersion}`);

    return {
      recommended: recommendedVersion,
      scores: scores,
      reason: this.generateRecommendationReason(recommendedVersion, profile)
    };
  }

  /**
   * 计算版本分数
   */
  calculateVersionScore(version, userProfile, analysisHistory) {
    let score = 0.5; // 基础分数

    // 年龄因子
    if (userProfile.age) {
      if (userProfile.age < 30 && version === 'professional') {
        score += 0.3;
      } else if (userProfile.age > 50 && version === 'traditional') {
        score += 0.3;
      } else if (userProfile.age >= 30 && userProfile.age <= 50 && version === 'hybrid') {
        score += 0.2;
      }
    }

    // 技术熟练度因子
    if (userProfile.techSavviness) {
      if (userProfile.techSavviness === 'high' && version === 'professional') {
        score += 0.4;
      } else if (userProfile.techSavviness === 'low' && version === 'traditional') {
        score += 0.4;
      } else if (userProfile.techSavviness === 'medium' && version === 'hybrid') {
        score += 0.3;
      }
    }

    // 历史偏好因子
    if (analysisHistory.preferredVersion === version) {
      score += 0.2;
    }

    // 使用频率因子
    const usageCount = this.switchStats.versionUsage[version] || 0;
    if (usageCount > 0) {
      score += Math.min(0.1, usageCount * 0.02);
    }

    // 用户满意度因子
    const satisfaction = this.getUserSatisfaction(version);
    if (satisfaction > 0) {
      score += satisfaction * 0.1;
    }

    return Math.min(1.0, score);
  }

  /**
   * 生成推荐理由
   */
  generateRecommendationReason(version, userProfile) {
    const versionInfo = this.versions[version];
    const reasons = [];

    if (userProfile.age < 30) {
      reasons.push('年轻用户通常偏好现代化界面');
    } else if (userProfile.age > 50) {
      reasons.push('成熟用户通常偏好传统界面');
    }

    if (userProfile.techSavviness === 'high') {
      reasons.push('技术熟练度高，适合数据分析功能');
    } else if (userProfile.techSavviness === 'low') {
      reasons.push('技术熟练度较低，适合简洁界面');
    }

    reasons.push(`${versionInfo.suitableFor}`);

    return reasons.join('，');
  }

  /**
   * 切换版本
   */
  async switchVersion(targetVersion, context = {}) {
    if (!this.versions[targetVersion]) {
      throw new Error(`不支持的版本: ${targetVersion}`);
    }

    if (targetVersion === this.currentVersion) {
      console.log(`⚠️ 已经是 ${targetVersion} 版本`);
      return { success: true, message: '版本未变更' };
    }

    const previousVersion = this.currentVersion;
    const startTime = Date.now();

    try {
      console.log(`🔄 开始版本切换: ${previousVersion} → ${targetVersion}`);

      // 1. 保存当前状态
      await this.saveCurrentState();

      // 2. 卸载当前版本组件
      await this.unloadVersionComponents(previousVersion);

      // 3. 加载目标版本组件
      await this.loadVersionComponents(targetVersion, context);

      // 4. 更新当前版本
      this.currentVersion = targetVersion;

      // 5. 保存用户偏好
      await this.saveUserPreference(targetVersion);

      // 6. 触发版本切换事件
      this.emit('versionSwitched', {
        from: previousVersion,
        to: targetVersion,
        context: context,
        timestamp: Date.now()
      });

      // 7. 更新统计
      const switchTime = Date.now() - startTime;
      this.updateSwitchStats(targetVersion, switchTime);

      console.log(`✅ 版本切换成功: ${previousVersion} → ${targetVersion} (耗时: ${switchTime}ms)`);

      return {
        success: true,
        message: `已切换到${this.versions[targetVersion].name}`,
        switchTime: switchTime,
        previousVersion: previousVersion,
        currentVersion: targetVersion
      };

    } catch (error) {
      console.error(`❌ 版本切换失败:`, error);
      
      // 回滚到之前版本
      await this.rollbackVersion(previousVersion);
      
      return {
        success: false,
        message: '版本切换失败，已回滚',
        error: error.message
      };
    }
  }

  /**
   * 加载版本组件
   */
  async loadVersionComponents(version, context) {
    const versionConfig = this.versions[version];
    const loadPromises = [];

    console.log(`📦 开始加载 ${version} 版本组件...`);

    for (const componentName of versionConfig.components) {
      const loadPromise = this.loadComponent(componentName, context);
      loadPromises.push(loadPromise);
    }

    await Promise.all(loadPromises);
    console.log(`✅ ${version} 版本组件加载完成`);
  }

  /**
   * 卸载版本组件
   */
  async unloadVersionComponents(version) {
    const versionConfig = this.versions[version];
    
    console.log(`📤 开始卸载 ${version} 版本组件...`);

    for (const componentName of versionConfig.components) {
      await this.unloadComponent(componentName);
    }

    console.log(`✅ ${version} 版本组件卸载完成`);
  }

  /**
   * 动态加载组件
   */
  async loadComponent(componentName, context = {}) {
    const loadState = this.componentLoadState.get(componentName);
    
    if (loadState && loadState.loaded) {
      console.log(`⚠️ 组件 ${componentName} 已经加载`);
      return;
    }

    if (loadState && loadState.loading) {
      console.log(`⚠️ 组件 ${componentName} 正在加载中`);
      return;
    }

    try {
      // 更新加载状态
      this.updateComponentLoadState(componentName, false, true);

      const startTime = Date.now();

      // 模拟组件加载（实际项目中这里会是真实的组件导入）
      await this.simulateComponentLoad(componentName);

      // 初始化组件
      await this.initializeComponent(componentName, context);

      const loadTime = Date.now() - startTime;

      // 更新加载状态
      this.updateComponentLoadState(componentName, true, false, null, loadTime);

      // 触发组件加载事件
      this.emit('componentLoaded', {
        component: componentName,
        loadTime: loadTime,
        context: context
      });

      console.log(`✅ 组件 ${componentName} 加载成功 (耗时: ${loadTime}ms)`);

    } catch (error) {
      // 更新错误状态
      this.updateComponentLoadState(componentName, false, false, error);
      
      console.error(`❌ 组件 ${componentName} 加载失败:`, error);
      throw error;
    }
  }

  /**
   * 卸载组件
   */
  async unloadComponent(componentName) {
    const loadState = this.componentLoadState.get(componentName);
    
    if (!loadState || !loadState.loaded) {
      return;
    }

    try {
      // 执行组件清理
      await this.cleanupComponent(componentName);

      // 更新加载状态
      this.updateComponentLoadState(componentName, false);

      console.log(`✅ 组件 ${componentName} 卸载成功`);

    } catch (error) {
      console.error(`❌ 组件 ${componentName} 卸载失败:`, error);
    }
  }

  /**
   * 模拟组件加载
   */
  async simulateComponentLoad(componentName) {
    // 模拟不同组件的加载时间
    const loadTimes = {
      'classical-text-display': 100,
      'wuxing-radar': 300,
      'balance-meter': 200,
      'rule-matcher': 250,
      'numerical-analysis': 400,
      'enhanced-rules': 150
    };

    const loadTime = loadTimes[componentName] || 200;
    
    return new Promise(resolve => {
      setTimeout(resolve, loadTime);
    });
  }

  /**
   * 初始化组件
   */
  async initializeComponent(componentName, context) {
    // 组件初始化逻辑
    console.log(`🔧 初始化组件: ${componentName}`);
    
    // 这里可以添加具体的组件初始化逻辑
    // 例如：绑定事件、设置数据、渲染界面等
  }

  /**
   * 清理组件
   */
  async cleanupComponent(componentName) {
    // 组件清理逻辑
    console.log(`🧹 清理组件: ${componentName}`);
    
    // 这里可以添加具体的组件清理逻辑
    // 例如：移除事件监听、清理数据、销毁实例等
  }

  /**
   * 更新组件加载状态
   */
  updateComponentLoadState(componentName, loaded = false, loading = false, error = null, loadTime = 0) {
    this.componentLoadState.set(componentName, {
      loaded: loaded,
      loading: loading,
      error: error,
      loadTime: loadTime
    });
  }

  /**
   * 保存当前状态
   */
  async saveCurrentState() {
    if (this.isolationManager) {
      this.isolationManager.isolatedStorage.setLocal('lastVersionState', {
        version: this.currentVersion,
        timestamp: Date.now(),
        componentStates: Array.from(this.componentLoadState.entries())
      });
    }
  }

  /**
   * 保存用户偏好
   */
  async saveUserPreference(version) {
    if (this.isolationManager) {
      this.isolationManager.isolatedStorage.setLocal('preferredVersion', version);
      this.isolationManager.isolatedStorage.setLocal('versionSwitchHistory', this.switchHistory);
    }
  }

  /**
   * 回滚版本
   */
  async rollbackVersion(previousVersion) {
    try {
      console.log(`🔄 回滚到版本: ${previousVersion}`);
      
      // 简单回滚：重新设置版本
      this.currentVersion = previousVersion;
      
      // 重新加载之前版本的组件
      await this.loadVersionComponents(previousVersion);
      
      console.log(`✅ 版本回滚成功: ${previousVersion}`);
      
    } catch (error) {
      console.error(`❌ 版本回滚失败:`, error);
    }
  }

  /**
   * 记录版本切换
   */
  recordVersionSwitch(from, to, reason = '') {
    const switchRecord = {
      from: from,
      to: to,
      reason: reason,
      timestamp: Date.now()
    };

    this.switchHistory.push(switchRecord);
    
    // 保持最近50条记录
    if (this.switchHistory.length > 50) {
      this.switchHistory.shift();
    }
  }

  /**
   * 更新切换统计
   */
  updateSwitchStats(version, switchTime) {
    this.switchStats.totalSwitches++;
    this.switchStats.versionUsage[version]++;
    this.switchStats.avgSwitchTime = 
      (this.switchStats.avgSwitchTime + switchTime) / 2;
  }

  /**
   * 记录用户反馈
   */
  recordUserFeedback(version, rating, comment = '') {
    if (!this.switchStats.userSatisfaction.has(version)) {
      this.switchStats.userSatisfaction.set(version, []);
    }
    
    this.switchStats.userSatisfaction.get(version).push({
      rating: rating,
      comment: comment,
      timestamp: Date.now()
    });
  }

  /**
   * 获取用户满意度
   */
  getUserSatisfaction(version) {
    const feedback = this.switchStats.userSatisfaction.get(version);
    if (!feedback || feedback.length === 0) {
      return 0;
    }
    
    const avgRating = feedback.reduce((sum, item) => sum + item.rating, 0) / feedback.length;
    return avgRating / 5; // 归一化到0-1
  }

  /**
   * 获取版本信息
   */
  getVersionInfo(version = null) {
    if (version) {
      return this.versions[version] || null;
    }
    return this.versions;
  }

  /**
   * 获取当前版本
   */
  getCurrentVersion() {
    return this.currentVersion;
  }

  /**
   * 获取切换统计
   */
  getSwitchStats() {
    return {
      ...this.switchStats,
      componentLoadState: Array.from(this.componentLoadState.entries()),
      switchHistory: this.switchHistory.slice(-10), // 最近10条
      currentVersion: this.currentVersion,
      availableVersions: Object.keys(this.versions)
    };
  }

  /**
   * 事件系统
   */
  on(event, handler) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(handler);
  }

  emit(event, data) {
    const handlers = this.eventListeners.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * 清理版本切换管理器
   */
  cleanup() {
    console.log('🧹 开始清理版本切换管理器...');
    
    // 清理事件监听器
    this.eventListeners.clear();
    
    // 清理组件状态
    this.componentLoadState.clear();
    
    // 重置状态
    this.isInitialized = false;
    this.currentVersion = 'traditional';
    
    console.log('✅ 版本切换管理器清理完成');
  }
}

// 导出版本切换管理器
module.exports = VersionSwitchManager;
