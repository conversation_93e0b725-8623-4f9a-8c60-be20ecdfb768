// test_shensha_fixes.js
// 测试神煞修复效果

console.log('🧪 测试神煞修复效果');
console.log('='.repeat(80));

// 模拟前端修正后的神煞计算函数
class FixedShenshaCalculator {
  
  // 修正后的天乙贵人计算
  calculateTianyiGuiren(dayGan, fourPillars) {
    console.log('🌟 计算天乙贵人:', { dayGan, fourPillars });
    
    const guirenMap = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };

    const targetZhi = guirenMap[dayGan] || [];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    fourPillars.forEach((pillar, index) => {
      if (targetZhi.includes(pillar.zhi)) {
        result.push({
          name: '天乙贵人',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '逢凶化吉，贵人相助',
          strength: '强'
        });
      }
    });

    console.log('天乙贵人计算结果:', result);
    return result;
  }

  // 修正后的桃花计算
  calculateTaohua(dayZhi, fourPillars) {
    console.log('🌸 计算桃花:', { dayZhi, fourPillars });
    
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    // 1. 咸池桃花（基于日支）
    const xianchiMap = {
      '寅': '卯', '午': '卯', '戌': '卯',
      '申': '酉', '子': '酉', '辰': '酉',
      '亥': '子', '卯': '子', '未': '子',
      '巳': '午', '酉': '午', '丑': '午'
    };

    const xianchiTarget = xianchiMap[dayZhi];
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === xianchiTarget) {
        result.push({
          name: '桃花',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '异性缘佳，感情丰富',
          strength: '中',
          type: '咸池'
        });
      }
    });

    // 2. 红鸾桃花（基于年支）
    const yearZhi = fourPillars[0].zhi;
    const hongluanMap = {
      '子': '卯', '丑': '寅', '寅': '丑', '卯': '子',
      '辰': '亥', '巳': '戌', '午': '酉', '未': '申',
      '申': '未', '酉': '午', '戌': '巳', '亥': '辰'
    };

    const hongluanTarget = hongluanMap[yearZhi];
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === hongluanTarget) {
        result.push({
          name: '红鸾',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '婚姻喜庆，感情顺利',
          strength: '中',
          type: '红鸾桃花'
        });
      }
    });

    console.log('桃花计算结果:', result);
    return result;
  }

  // 文昌贵人计算
  calculateWenchangGuiren(dayGan, fourPillars) {
    const wenchangMap = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉',
      '戊': '申', '己': '酉', '庚': '亥', '辛': '子',
      '壬': '寅', '癸': '卯'
    };

    const targetZhi = wenchangMap[dayGan];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === targetZhi) {
        result.push({
          name: '文昌贵人',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '聪明好学，文思敏捷',
          strength: '强'
        });
      }
    });

    return result;
  }

  // 丧门计算
  calculateSangmen(yearZhi, fourPillars) {
    const sangmenMap = {
      '子': '寅', '丑': '卯', '寅': '辰', '卯': '巳',
      '辰': '午', '巳': '未', '午': '申', '未': '酉',
      '申': '戌', '酉': '亥', '戌': '子', '亥': '丑'
    };

    const targetZhi = sangmenMap[yearZhi];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === targetZhi) {
        result.push({
          name: '丧门',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '易有丧事，需要谨慎',
          strength: '中'
        });
      }
    });

    return result;
  }

  // 计算所有神煞
  calculateAllShensha(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;
    const yearZhi = fourPillars[0].zhi;

    const allResults = [];
    
    allResults.push(...this.calculateTianyiGuiren(dayGan, fourPillars));
    allResults.push(...this.calculateTaohua(dayZhi, fourPillars));
    allResults.push(...this.calculateWenchangGuiren(dayGan, fourPillars));
    allResults.push(...this.calculateSangmen(yearZhi, fourPillars));

    return allResults;
  }
}

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ]
};

// "问真八字"标准结果
const wenZhenStandard = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

// 执行测试
function runTest() {
  console.log('\n🧪 执行神煞修复测试:');
  console.log('='.repeat(50));
  
  const calculator = new FixedShenshaCalculator();
  const results = calculator.calculateAllShensha(testData.fourPillars);
  
  console.log('\n修复后的计算结果:');
  results.forEach(result => {
    console.log(`${result.position}: ${result.name} (${result.pillar}) - ${result.effect}`);
  });
  
  // 按柱位分组
  const byPillar = { year: [], month: [], day: [], hour: [] };
  results.forEach(result => {
    const pillarMap = { '年柱': 'year', '月柱': 'month', '日柱': 'day', '时柱': 'hour' };
    const pillar = pillarMap[result.position];
    if (pillar) {
      byPillar[pillar].push(result.name);
    }
  });
  
  console.log('\n按柱位分组结果:');
  console.log(`年柱: ${byPillar.year.join(', ') || '无'}`);
  console.log(`月柱: ${byPillar.month.join(', ') || '无'}`);
  console.log(`日柱: ${byPillar.day.join(', ') || '无'}`);
  console.log(`时柱: ${byPillar.hour.join(', ') || '无'}`);
  
  console.log('\n与"问真八字"对比:');
  console.log(`年柱期望: ${wenZhenStandard.year.join(', ')}`);
  console.log(`月柱期望: ${wenZhenStandard.month.join(', ')}`);
  console.log(`日柱期望: ${wenZhenStandard.day.join(', ')}`);
  console.log(`时柱期望: ${wenZhenStandard.hour.join(', ')}`);
  
  // 匹配度分析
  console.log('\n匹配度分析:');
  const yearMatch = byPillar.year.some(s => wenZhenStandard.year.includes(s));
  const monthMatch = byPillar.month.some(s => wenZhenStandard.month.includes(s));
  const dayMatch = byPillar.day.some(s => wenZhenStandard.day.includes(s));
  const hourMatch = byPillar.hour.some(s => wenZhenStandard.hour.includes(s));
  
  console.log(`年柱匹配: ${yearMatch ? '✅ 部分匹配' : '❌ 无匹配'}`);
  console.log(`月柱匹配: ${monthMatch ? '✅ 部分匹配' : '❌ 无匹配'}`);
  console.log(`日柱匹配: ${dayMatch ? '✅ 部分匹配' : '❌ 无匹配'}`);
  console.log(`时柱匹配: ${hourMatch ? '✅ 部分匹配' : '❌ 无匹配'}`);
  
  return { results, byPillar, matches: { yearMatch, monthMatch, dayMatch, hourMatch } };
}

// 分析修复效果
function analyzeFixResults(testResults) {
  console.log('\n📊 修复效果分析:');
  console.log('='.repeat(50));
  
  const { results, byPillar, matches } = testResults;
  
  console.log('✅ 修复成功的部分:');
  console.log('- 天乙贵人计算逻辑正确');
  console.log('- 文昌贵人计算准确');
  console.log('- 丧门计算基本正确');
  console.log('- 强度等级已中文化');
  
  console.log('\n❌ 仍需改进的部分:');
  console.log('- 月柱天乙贵人缺失（午支问题）');
  console.log('- 月柱桃花缺失（可能需要其他桃花类型）');
  console.log('- 缺失大量神煞（福星贵人、月德合等）');
  console.log('- 年柱和时柱神煞完全缺失');
  
  console.log('\n🎯 下一步优化方向:');
  console.log('1. 研究天乙贵人的昼夜贵人规则');
  console.log('2. 实现更多桃花类型（天喜、沐浴等）');
  console.log('3. 实现缺失的重要神煞');
  console.log('4. 建立神煞计算的权威验证体系');
}

// 执行测试
console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('参考标准: "问真八字"权威软件');

const testResults = runTest();
analyzeFixResults(testResults);

console.log('\n🚀 总结:');
console.log('='.repeat(40));
console.log('✅ 基础神煞计算框架已建立');
console.log('✅ 部分神煞计算准确性提升');
console.log('❌ 仍需大量神煞实现和验证');
console.log('🎯 建议继续深入研究"问真八字"算法');
