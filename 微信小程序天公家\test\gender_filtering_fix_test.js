/**
 * 🧪 性别过滤修复测试
 * 验证历史名人库性别过滤功能是否正常工作
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  request: () => {}
};

const CelebrityDatabaseAPI = require('../utils/celebrity_database_api.js');

async function testGenderFiltering() {
  console.log('🧪 开始测试性别过滤修复...\n');

  try {
    // 初始化名人数据库
    const celebrityDB = new CelebrityDatabaseAPI();

    console.log(`📊 数据库加载完成，共${celebrityDB.celebrities.length}位名人`);

    // 测试性别标准化方法
    console.log('\n🔧 测试性别标准化方法:');
    const testCases = [
      { input: '男', expected: 'male' },
      { input: '女', expected: 'female' },
      { input: 'male', expected: 'male' },
      { input: 'female', expected: 'female' },
      { input: 'M', expected: 'male' },
      { input: 'f', expected: 'female' },
      { input: null, expected: null },
      { input: '', expected: null }
    ];

    for (const testCase of testCases) {
      const result = celebrityDB.normalizeGender(testCase.input);
      const status = result === testCase.expected ? '✅' : '❌';
      console.log(`${status} "${testCase.input}" -> "${result}" (期望: "${testCase.expected}")`);
    }

    // 测试用户数据（男性）
    const maleUserInfo = {
      bazi: {
        yearPillar: { gan: '甲', zhi: '子' },
        monthPillar: { gan: '丙', zhi: '寅' },
        dayPillar: { gan: '戊', zhi: '午' },
        timePillar: { gan: '庚', zhi: '申' }
      },
      pattern: {
        mainPattern: '正财格',
        yongshen: '木',
        dayMaster: '戊土'
      }
    };

    // 测试男性用户查找同性别名人
    console.log('\n👨 测试男性用户查找同性别名人:');
    const maleResults = celebrityDB.findSimilarCelebrities(maleUserInfo, {
      limit: 5,
      minSimilarity: 0.1,
      userGender: 'male'  // 使用英文格式
    });

    console.log(`找到 ${maleResults.length} 位相似男性名人:`);
    maleResults.forEach((result, index) => {
      const name = result.celebrity.basicInfo.name;
      const gender = result.celebrity.basicInfo.gender;
      console.log(`${index + 1}. ${name} (${gender}) - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
    });

    // 测试中文性别格式
    console.log('\n👨 测试中文性别格式 ("男"):');
    const maleResultsChinese = celebrityDB.findSimilarCelebrities(maleUserInfo, {
      limit: 5,
      minSimilarity: 0.1,
      userGender: '男'  // 使用中文格式
    });

    console.log(`找到 ${maleResultsChinese.length} 位相似男性名人:`);
    maleResultsChinese.forEach((result, index) => {
      const name = result.celebrity.basicInfo.name;
      const gender = result.celebrity.basicInfo.gender;
      console.log(`${index + 1}. ${name} (${gender}) - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
    });

    // 测试女性用户查找同性别名人
    console.log('\n👩 测试女性用户查找同性别名人:');
    const femaleResults = celebrityDB.findSimilarCelebrities(maleUserInfo, {
      limit: 5,
      minSimilarity: 0.1,
      userGender: 'female'  // 使用英文格式
    });

    console.log(`找到 ${femaleResults.length} 位相似女性名人:`);
    femaleResults.forEach((result, index) => {
      const name = result.celebrity.basicInfo.name;
      const gender = result.celebrity.basicInfo.gender;
      console.log(`${index + 1}. ${name} (${gender}) - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
    });

    // 测试中文性别格式
    console.log('\n👩 测试中文性别格式 ("女"):');
    const femaleResultsChinese = celebrityDB.findSimilarCelebrities(maleUserInfo, {
      limit: 5,
      minSimilarity: 0.1,
      userGender: '女'  // 使用中文格式
    });

    console.log(`找到 ${femaleResultsChinese.length} 位相似女性名人:`);
    femaleResultsChinese.forEach((result, index) => {
      const name = result.celebrity.basicInfo.name;
      const gender = result.celebrity.basicInfo.gender;
      console.log(`${index + 1}. ${name} (${gender}) - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
    });

    // 验证数据库中的性别分布
    console.log('\n📊 验证数据库性别分布:');
    const genderCount = { male: 0, female: 0, unknown: 0 };
    
    celebrityDB.celebrities.forEach(celebrity => {
      const normalizedGender = celebrityDB.normalizeGender(celebrity.basicInfo.gender);
      if (normalizedGender === 'male') {
        genderCount.male++;
      } else if (normalizedGender === 'female') {
        genderCount.female++;
      } else {
        genderCount.unknown++;
      }
    });

    console.log(`男性名人: ${genderCount.male}位`);
    console.log(`女性名人: ${genderCount.female}位`);
    console.log(`未知性别: ${genderCount.unknown}位`);

    // 测试结果验证
    console.log('\n🎯 测试结果验证:');
    
    const allTestsPassed = 
      maleResults.length > 0 && 
      maleResultsChinese.length > 0 && 
      femaleResults.length > 0 && 
      femaleResultsChinese.length > 0 &&
      genderCount.male > 0 && 
      genderCount.female > 0;

    if (allTestsPassed) {
      console.log('✅ 所有测试通过！性别过滤功能修复成功');
    } else {
      console.log('❌ 部分测试失败，需要进一步检查');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testGenderFiltering();
