/**
 * 性能优化管理器
 * 负责优化古籍规则系统的加载和查询性能
 */

class PerformanceOptimizer {
  constructor() {
    this.cacheManager = new Map();
    this.indexManager = new Map();
    this.performanceMetrics = {
      queryTimes: [],
      cacheHits: 0,
      cacheMisses: 0,
      indexBuilds: 0,
      totalQueries: 0
    };
    
    // 配置参数
    this.config = {
      maxCacheSize: 1000,
      cacheExpireTime: 30 * 60 * 1000, // 30分钟
      enableIndexing: true,
      enableCompression: false,
      batchSize: 100
    };
  }

  /**
   * 初始化性能优化器
   */
  initialize() {
    console.log('⚡ 初始化性能优化器...');
    
    // 启动缓存清理定时器
    this.startCacheCleanup();
    
    // 预热常用查询
    this.preWarmCache();
    
    console.log('✅ 性能优化器初始化完成');
  }

  /**
   * 构建规则索引
   */
  buildRuleIndexes(rules) {
    console.log(`🔍 开始构建规则索引 - 规则数: ${rules.length}`);
    const startTime = Date.now();
    
    // 清空现有索引
    this.indexManager.clear();
    
    // 按分类索引
    const categoryIndex = new Map();
    // 按来源索引
    const sourceIndex = new Map();
    // 按置信度索引
    const confidenceIndex = new Map();
    // 按关键词索引
    const keywordIndex = new Map();
    // 按天干索引
    const ganIndex = new Map();
    // 按地支索引
    const zhiIndex = new Map();

    rules.forEach((rule, index) => {
      // 分类索引
      if (rule.category) {
        if (!categoryIndex.has(rule.category)) {
          categoryIndex.set(rule.category, []);
        }
        categoryIndex.get(rule.category).push(index);
      }

      // 来源索引
      const source = rule.book_source || rule.source;
      if (source) {
        if (!sourceIndex.has(source)) {
          sourceIndex.set(source, []);
        }
        sourceIndex.get(source).push(index);
      }

      // 置信度索引
      const confidence = Math.floor((rule.confidence || 0.5) * 10) / 10;
      if (!confidenceIndex.has(confidence)) {
        confidenceIndex.set(confidence, []);
      }
      confidenceIndex.get(confidence).push(index);

      // 关键词索引
      this.buildKeywordIndex(rule, index, keywordIndex);
      
      // 天干地支索引
      this.buildGanZhiIndex(rule, index, ganIndex, zhiIndex);
    });

    // 存储索引
    this.indexManager.set('category', categoryIndex);
    this.indexManager.set('source', sourceIndex);
    this.indexManager.set('confidence', confidenceIndex);
    this.indexManager.set('keyword', keywordIndex);
    this.indexManager.set('gan', ganIndex);
    this.indexManager.set('zhi', zhiIndex);

    const endTime = Date.now();
    this.performanceMetrics.indexBuilds++;
    
    console.log(`✅ 规则索引构建完成 - 耗时: ${endTime - startTime}ms`);
    console.log(`   📊 分类索引: ${categoryIndex.size}个分类`);
    console.log(`   📊 来源索引: ${sourceIndex.size}个来源`);
    console.log(`   📊 关键词索引: ${keywordIndex.size}个关键词`);
  }

  /**
   * 构建关键词索引
   */
  buildKeywordIndex(rule, ruleIndex, keywordIndex) {
    const text = [
      rule.original_text || '',
      rule.interpretations || rule.interpretation || '',
      rule.pattern_name || rule.title || '',
      rule.conditions || ''
    ].join(' ').toLowerCase();

    // 提取关键词
    const keywords = this.extractKeywords(text);
    
    keywords.forEach(keyword => {
      if (!keywordIndex.has(keyword)) {
        keywordIndex.set(keyword, []);
      }
      keywordIndex.get(keyword).push(ruleIndex);
    });
  }

  /**
   * 构建天干地支索引
   */
  buildGanZhiIndex(rule, ruleIndex, ganIndex, zhiIndex) {
    const text = [
      rule.original_text || '',
      rule.conditions || ''
    ].join(' ');

    // 天干
    const gans = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    gans.forEach(gan => {
      if (text.includes(gan)) {
        if (!ganIndex.has(gan)) {
          ganIndex.set(gan, []);
        }
        ganIndex.get(gan).push(ruleIndex);
      }
    });

    // 地支
    const zhis = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    zhis.forEach(zhi => {
      if (text.includes(zhi)) {
        if (!zhiIndex.has(zhi)) {
          zhiIndex.set(zhi, []);
        }
        zhiIndex.get(zhi).push(ruleIndex);
      }
    });
  }

  /**
   * 提取关键词
   */
  extractKeywords(text) {
    // 命理学关键词
    const keywords = new Set();
    
    // 基础关键词
    const basicKeywords = [
      '格局', '用神', '喜神', '忌神', '调候', '通关',
      '身强', '身弱', '中和', '旺', '衰', '得令', '失令',
      '财', '官', '印', '食', '伤', '比', '劫', '杀',
      '正财', '偏财', '正官', '七杀', '正印', '偏印', '食神', '伤官',
      '建禄', '羊刃', '冲', '合', '刑', '害', '破', '墓',
      '贵人', '桃花', '华盖', '空亡', '驿马', '天德', '月德'
    ];

    basicKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        keywords.add(keyword);
      }
    });

    return Array.from(keywords);
  }

  /**
   * 快速查询规则
   */
  fastQuery(rules, queryParams) {
    const startTime = Date.now();
    this.performanceMetrics.totalQueries++;

    // 生成查询缓存键
    const cacheKey = this.generateQueryCacheKey(queryParams);
    
    // 检查缓存
    if (this.cacheManager.has(cacheKey)) {
      const cachedResult = this.cacheManager.get(cacheKey);
      if (this.isCacheValid(cachedResult)) {
        this.performanceMetrics.cacheHits++;
        console.log('🎯 使用缓存结果');
        return cachedResult.data;
      } else {
        this.cacheManager.delete(cacheKey);
      }
    }

    this.performanceMetrics.cacheMisses++;

    // 执行查询
    let resultIndexes = new Set();
    let firstQuery = true;

    // 使用索引查询
    if (queryParams.category && this.indexManager.has('category')) {
      const categoryIndex = this.indexManager.get('category');
      const indexes = categoryIndex.get(queryParams.category) || [];
      if (firstQuery) {
        resultIndexes = new Set(indexes);
        firstQuery = false;
      } else {
        resultIndexes = this.intersectSets(resultIndexes, new Set(indexes));
      }
    }

    if (queryParams.source && this.indexManager.has('source')) {
      const sourceIndex = this.indexManager.get('source');
      const indexes = sourceIndex.get(queryParams.source) || [];
      if (firstQuery) {
        resultIndexes = new Set(indexes);
        firstQuery = false;
      } else {
        resultIndexes = this.intersectSets(resultIndexes, new Set(indexes));
      }
    }

    if (queryParams.minConfidence && this.indexManager.has('confidence')) {
      const confidenceIndex = this.indexManager.get('confidence');
      const validIndexes = [];
      for (const [confidence, indexes] of confidenceIndex) {
        if (confidence >= queryParams.minConfidence) {
          validIndexes.push(...indexes);
        }
      }
      if (firstQuery) {
        resultIndexes = new Set(validIndexes);
        firstQuery = false;
      } else {
        resultIndexes = this.intersectSets(resultIndexes, new Set(validIndexes));
      }
    }

    if (queryParams.keywords && this.indexManager.has('keyword')) {
      const keywordIndex = this.indexManager.get('keyword');
      const keywordIndexes = new Set();
      queryParams.keywords.forEach(keyword => {
        const indexes = keywordIndex.get(keyword) || [];
        indexes.forEach(index => keywordIndexes.add(index));
      });
      if (firstQuery) {
        resultIndexes = keywordIndexes;
        firstQuery = false;
      } else {
        resultIndexes = this.intersectSets(resultIndexes, keywordIndexes);
      }
    }

    // 如果没有使用索引，返回所有规则
    if (firstQuery) {
      resultIndexes = new Set(rules.map((_, index) => index));
    }

    // 获取结果规则
    const resultRules = Array.from(resultIndexes).map(index => rules[index]).filter(Boolean);

    // 应用额外过滤
    const filteredRules = this.applyAdditionalFilters(resultRules, queryParams);

    // 排序
    const sortedRules = this.sortResults(filteredRules, queryParams);

    // 限制结果数量
    const limitedRules = queryParams.limit ? 
      sortedRules.slice(0, queryParams.limit) : sortedRules;

    // 缓存结果
    this.cacheResult(cacheKey, limitedRules);

    const endTime = Date.now();
    const queryTime = endTime - startTime;
    this.performanceMetrics.queryTimes.push(queryTime);

    console.log(`⚡ 快速查询完成 - 耗时: ${queryTime}ms, 结果: ${limitedRules.length}条`);

    return limitedRules;
  }

  /**
   * 集合交集
   */
  intersectSets(set1, set2) {
    const result = new Set();
    for (const item of set1) {
      if (set2.has(item)) {
        result.add(item);
      }
    }
    return result;
  }

  /**
   * 应用额外过滤
   */
  applyAdditionalFilters(rules, queryParams) {
    let filtered = [...rules];

    // 置信度过滤
    if (queryParams.minConfidence) {
      filtered = filtered.filter(rule => (rule.confidence || 0) >= queryParams.minConfidence);
    }

    // 文本匹配过滤
    if (queryParams.textMatch) {
      const searchText = queryParams.textMatch.toLowerCase();
      filtered = filtered.filter(rule => {
        const ruleText = [
          rule.original_text || '',
          rule.interpretations || rule.interpretation || '',
          rule.pattern_name || rule.title || ''
        ].join(' ').toLowerCase();
        return ruleText.includes(searchText);
      });
    }

    return filtered;
  }

  /**
   * 排序结果
   */
  sortResults(rules, queryParams) {
    const sortBy = queryParams.sortBy || 'confidence';
    const sortOrder = queryParams.sortOrder || 'desc';

    return rules.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'confidence':
          aValue = a.confidence || 0;
          bValue = b.confidence || 0;
          break;
        case 'relevance':
          aValue = a.matchScore || 0;
          bValue = b.matchScore || 0;
          break;
        case 'source':
          aValue = a.book_source || a.source || '';
          bValue = b.book_source || b.source || '';
          break;
        default:
          aValue = a.confidence || 0;
          bValue = b.confidence || 0;
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });
  }

  /**
   * 生成查询缓存键
   */
  generateQueryCacheKey(queryParams) {
    return JSON.stringify(queryParams);
  }

  /**
   * 缓存结果
   */
  cacheResult(cacheKey, result) {
    // 检查缓存大小限制
    if (this.cacheManager.size >= this.config.maxCacheSize) {
      this.cleanupOldCache();
    }

    this.cacheManager.set(cacheKey, {
      data: result,
      timestamp: Date.now(),
      accessCount: 1
    });
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cachedItem) {
    const now = Date.now();
    const isExpired = (now - cachedItem.timestamp) > this.config.cacheExpireTime;
    
    if (!isExpired) {
      cachedItem.accessCount++;
    }
    
    return !isExpired;
  }

  /**
   * 清理旧缓存
   */
  cleanupOldCache() {
    const now = Date.now();
    const itemsToDelete = [];

    for (const [key, item] of this.cacheManager) {
      if ((now - item.timestamp) > this.config.cacheExpireTime) {
        itemsToDelete.push(key);
      }
    }

    // 删除过期项
    itemsToDelete.forEach(key => this.cacheManager.delete(key));

    // 如果还是太多，删除访问次数最少的
    if (this.cacheManager.size >= this.config.maxCacheSize) {
      const items = Array.from(this.cacheManager.entries());
      items.sort((a, b) => a[1].accessCount - b[1].accessCount);
      
      const deleteCount = Math.floor(this.config.maxCacheSize * 0.2);
      for (let i = 0; i < deleteCount && i < items.length; i++) {
        this.cacheManager.delete(items[i][0]);
      }
    }
  }

  /**
   * 启动缓存清理定时器
   */
  startCacheCleanup() {
    setInterval(() => {
      this.cleanupOldCache();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }

  /**
   * 预热缓存
   */
  preWarmCache() {
    // 预热常用查询
    const commonQueries = [
      { category: '用神理论' },
      { category: '格局理论' },
      { source: '三命通会' },
      { source: '渊海子平' },
      { minConfidence: 0.9 }
    ];

    console.log('🔥 开始预热缓存...');
    // 这里可以预执行一些常用查询
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const avgQueryTime = this.performanceMetrics.queryTimes.length > 0 ?
      this.performanceMetrics.queryTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.queryTimes.length : 0;

    const cacheHitRate = this.performanceMetrics.totalQueries > 0 ?
      (this.performanceMetrics.cacheHits / this.performanceMetrics.totalQueries * 100).toFixed(2) + '%' : '0%';

    return {
      totalQueries: this.performanceMetrics.totalQueries,
      cacheHits: this.performanceMetrics.cacheHits,
      cacheMisses: this.performanceMetrics.cacheMisses,
      cacheHitRate: cacheHitRate,
      avgQueryTime: avgQueryTime.toFixed(2) + 'ms',
      cacheSize: this.cacheManager.size,
      indexCount: this.indexManager.size,
      indexBuilds: this.performanceMetrics.indexBuilds
    };
  }

  /**
   * 重置性能统计
   */
  resetStats() {
    this.performanceMetrics = {
      queryTimes: [],
      cacheHits: 0,
      cacheMisses: 0,
      indexBuilds: 0,
      totalQueries: 0
    };
  }

  /**
   * 清理所有缓存和索引
   */
  cleanup() {
    this.cacheManager.clear();
    this.indexManager.clear();
    this.resetStats();
    console.log('🧹 性能优化器已清理');
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceOptimizer;
} else if (typeof window !== 'undefined') {
  window.PerformanceOptimizer = PerformanceOptimizer;
}
