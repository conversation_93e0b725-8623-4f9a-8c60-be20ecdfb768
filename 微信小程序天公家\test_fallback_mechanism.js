// 测试容错备用机制
console.log('🧪 开始测试容错备用机制...');

// 模拟页面环境
const mockPage = {
  // 模拟简化版五行计算方法
  calculateSimplifiedWuxing: function(fourPillars) {
    console.log('⚠️ 专业级计算失败，启用容错备用方案');
    console.log('📊 使用优化简化版五行计算...');

    try {
      const wuxingCount = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };
      const wuxingMap = {
        '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
        '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
        '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
        '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
        '戌': '土', '亥': '水'
      };

      // 输入验证
      if (!Array.isArray(fourPillars) || fourPillars.length !== 4) {
        throw new Error('四柱数据格式错误');
      }

      fourPillars.forEach((pillar, index) => {
        const gan = pillar.gan || pillar.heavenlyStem;
        const zhi = pillar.zhi || pillar.earthlyBranch;
        
        if (!gan || !zhi || !wuxingMap[gan] || !wuxingMap[zhi]) {
          console.warn(`⚠️ 第${index + 1}柱数据异常: ${gan}-${zhi}`);
          return;
        }
        
        wuxingCount[wuxingMap[gan]]++;
        wuxingCount[wuxingMap[zhi]]++;
      });

      // 转换为前端格式
      const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
      const result = {};
      Object.entries(wuxingCount).forEach(([chineseElement, count]) => {
        const englishElement = elementMap[chineseElement];
        result[englishElement] = count;
      });

      // 增强的备用方案标识
      result.professionalData = {
        isProfessional: false,
        algorithm: '容错备用算法 v2.0',
        balanceStatus: '基础统计结果（容错模式）',
        fallbackReason: '专业级计算异常，自动降级',
        reliability: 'basic',
        frontendData: {
          elementComparisons: this.generateBasicElementComparisons(wuxingCount),
          interactionDetails: { all: [], combinations: [], conflicts: [] },
          recommendations: [{
            type: 'fallback_notice',
            title: '系统提示',
            content: '当前使用基础计算模式，建议稍后重试获取完整分析。',
            confidence: 60,
            priority: 'medium',
            icon: '⚠️'
          }],
          confidence: 60
        }
      };

      console.log('✅ 容错备用计算完成');
      return result;

    } catch (error) {
      console.error('❌ 连备用计算都失败了:', error);
      // 最后的兜底方案
      return this.generateEmergencyFallback();
    }
  },

  // 生成基础元素对比数据（备用方案）
  generateBasicElementComparisons: function(wuxingCount) {
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    const comparisons = [];
    
    Object.entries(wuxingCount).forEach(([chineseElement, count]) => {
      const englishElement = elementMap[chineseElement];
      comparisons.push({
        name: chineseElement,
        element: englishElement,
        staticPower: count,
        dynamicPower: count,
        staticValue: count,
        dynamicValue: count,
        staticPercent: 20,
        dynamicPercent: 20,
        change: 0,
        changePercent: 0,
        changePercentText: '0%',
        changeDirection: '➡️',
        changeLevel: 'stable'
      });
    });
    
    return comparisons;
  },

  // 最终兜底方案
  generateEmergencyFallback: function() {
    console.log('🆘 启用最终兜底方案');
    return {
      wood: 1, fire: 1, earth: 1, metal: 1, water: 1,
      professionalData: {
        isProfessional: false,
        algorithm: '紧急兜底方案',
        balanceStatus: '系统异常，请重试',
        fallbackReason: '计算引擎异常',
        reliability: 'emergency',
        frontendData: {
          elementComparisons: [],
          interactionDetails: { all: [], combinations: [], conflicts: [] },
          recommendations: [{
            type: 'system_error',
            title: '系统异常',
            content: '计算系统遇到异常，请稍后重试或联系技术支持。',
            confidence: 30,
            priority: 'high',
            icon: '🆘'
          }],
          confidence: 30
        }
      }
    };
  }
};

// 测试用例
const testCases = [
  {
    name: '正常四柱数据',
    fourPillars: [
      { gan: '辛', zhi: '丑' },
      { gan: '甲', zhi: '午' },
      { gan: '癸', zhi: '卯' },
      { gan: '壬', zhi: '戌' }
    ]
  },
  {
    name: '异常数据格式',
    fourPillars: [
      { gan: '辛', zhi: '丑' },
      { gan: '甲', zhi: '午' },
      { gan: '癸' }, // 缺少zhi
      { gan: '壬', zhi: '戌' }
    ]
  },
  {
    name: '空数据',
    fourPillars: []
  },
  {
    name: '无效字符',
    fourPillars: [
      { gan: 'X', zhi: 'Y' },
      { gan: '甲', zhi: '午' },
      { gan: '癸', zhi: '卯' },
      { gan: '壬', zhi: '戌' }
    ]
  }
];

// 执行测试
testCases.forEach((testCase, index) => {
  console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);
  try {
    const result = mockPage.calculateSimplifiedWuxing(testCase.fourPillars);
    console.log('✅ 测试通过');
    console.log('📊 结果摘要:');
    console.log(`   - 算法: ${result.professionalData.algorithm}`);
    console.log(`   - 可靠性: ${result.professionalData.reliability}`);
    console.log(`   - 建议数量: ${result.professionalData.frontendData.recommendations.length}`);
    console.log(`   - 置信度: ${result.professionalData.frontendData.confidence}%`);
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
  }
});

console.log('\n🏆 容错备用机制测试完成');
