// debug_liunian_data_flow.js
// 调试流年数据流问题

const ProfessionalLiunianCalculator = require('./utils/professional_liunian_calculator.js');

/**
 * 模拟页面的 calculateProfessionalLiunian 方法
 */
function simulateCalculateProfessionalLiunian(baziData, currentDayun = null) {
  console.log('🌟 开始计算专业级流年数据...');

  try {
    // 使用专业级流年计算器
    const calculator = new ProfessionalLiunianCalculator();

    // 安全获取出生信息
    const birthInfo = baziData.birthInfo || {};
    const birthYear = birthInfo.year || new Date().getFullYear() - 30; // 默认30岁

    // 构建八字数据格式
    const bazi = {
      dayPillar: {
        gan: baziData.baziInfo.dayPillar.heavenly,
        zhi: baziData.baziInfo.dayPillar.earthly
      },
      yearPillar: {
        gan: baziData.baziInfo.yearPillar.heavenly,
        zhi: baziData.baziInfo.yearPillar.earthly
      },
      monthPillar: {
        gan: baziData.baziInfo.monthPillar.heavenly,
        zhi: baziData.baziInfo.monthPillar.earthly
      },
      timePillar: {
        gan: baziData.baziInfo.timePillar.heavenly,
        zhi: baziData.baziInfo.timePillar.earthly
      },
      birthInfo: {
        year: birthYear
      }
    };

    console.log('📊 构建的八字数据:', JSON.stringify(bazi, null, 2));

    // 计算当前年份开始的5年流年
    const currentYear = new Date().getFullYear();
    const liunianAnalysis = calculator.calculateCompleteLiunianAnalysis(
      bazi, currentYear, 5, currentDayun
    );

    console.log('📈 流年分析原始结果:', JSON.stringify(liunianAnalysis, null, 2));

    // 验证计算结果
    if (!Array.isArray(liunianAnalysis) || liunianAnalysis.length === 0) {
      throw new Error('流年计算结果无效');
    }

    // 获取当前流年状态
    const currentLiunian = calculator.getCurrentLiunianStatus(bazi, currentDayun);
    console.log('📅 当前流年状态:', JSON.stringify(currentLiunian, null, 2));

    // 运势等级CSS类名映射
    const levelClassMap = {
      '大吉': 'excellent',
      '中吉': 'good',
      '平稳': 'stable',
      '小凶': 'poor',
      '大凶': 'bad'
    };

    // 为当前流年添加CSS类名
    if (currentLiunian && currentLiunian.fortuneLevel) {
      currentLiunian.fortuneLevel.levelClass = levelClassMap[currentLiunian.fortuneLevel.level] || 'stable';
    }

    // 为流年列表添加CSS类名
    const processedLiunianList = liunianAnalysis.map(item => {
      if (item.fortuneLevel) {
        item.fortuneLevel.levelClass = levelClassMap[item.fortuneLevel.level] || 'stable';
      }
      return item;
    });

    console.log('✅ 专业级流年计算完成');

    const result = {
      success: true,
      currentLiunian: currentLiunian,
      liunianList: processedLiunianList,
      summary: {
        totalYears: liunianAnalysis.length,
        averageScore: Math.round(liunianAnalysis.reduce((sum, item) => sum + item.fortuneLevel.score, 0) / liunianAnalysis.length),
        averageScore_display: Math.round(liunianAnalysis.reduce((sum, item) => sum + item.fortuneLevel.score, 0) / liunianAnalysis.length),
        bestYear: liunianAnalysis.reduce((best, current) =>
          current.fortuneLevel.score > best.fortuneLevel.score ? current : best
        ),
        worstYear: liunianAnalysis.reduce((worst, current) =>
          current.fortuneLevel.score < worst.fortuneLevel.score ? current : worst
        )
      },
      basis: '《三命通会·流年章》黄帝纪元法',
      calculation: {
        method: 'professional',
        engine: 'ProfessionalLiunianCalculator',
        timestamp: new Date().toISOString()
      }
    };

    console.log('🎯 最终计算结果:', JSON.stringify(result, null, 2));
    return result;

  } catch (error) {
    console.error('❌ 专业级流年计算失败:', error);
    return simulateGetFallbackLiunianData();
  }
}

/**
 * 模拟降级数据方法
 */
function simulateGetFallbackLiunianData() {
  const currentYear = new Date().getFullYear();

  // 运势等级CSS类名映射
  const levelClassMap = {
    '大吉': 'excellent',
    '中吉': 'good',
    '平稳': 'stable',
    '小凶': 'poor',
    '大凶': 'bad'
  };

  const fallbackLiunianList = [
    { year: currentYear, ganzhi: '甲辰', fortuneLevel: { level: '平稳', levelClass: levelClassMap['平稳'], score: 50 } },
    { year: currentYear + 1, ganzhi: '乙巳', fortuneLevel: { level: '平稳', levelClass: levelClassMap['平稳'], score: 50 } },
    { year: currentYear + 2, ganzhi: '丙午', fortuneLevel: { level: '平稳', levelClass: levelClassMap['平稳'], score: 50 } }
  ];

  return {
    success: false,
    currentLiunian: {
      year: currentYear,
      ganzhi: '甲辰',
      fortuneLevel: {
        level: '平稳',
        levelClass: levelClassMap['平稳'],
        score: 50,
        description: '运势平稳'
      },
      advice: ['系统计算异常，建议咨询专业命理师']
    },
    liunianList: fallbackLiunianList,
    summary: {
      totalYears: fallbackLiunianList.length,
      averageScore: 50,
      averageScore_display: 50,
      bestYear: {
        year: currentYear + 1,
        fortuneLevel: { score: 50 }
      },
      worstYear: {
        year: currentYear + 2,
        fortuneLevel: { score: 50 }
      }
    },
    basis: '降级数据',
    error: '专业级流年计算系统异常'
  };
}

/**
 * 测试数据流
 */
function testDataFlow() {
  console.log('🧪 开始测试流年数据流');
  console.log('=' * 50);

  // 模拟真实的八字数据
  const testBaziData = {
    baziInfo: {
      yearPillar: { heavenly: '甲', earthly: '子' },
      monthPillar: { heavenly: '丙', earthly: '寅' },
      dayPillar: { heavenly: '戊', earthly: '午' },
      timePillar: { heavenly: '庚', earthly: '申' }
    },
    birthInfo: {
      year: 1990
    }
  };

  console.log('📋 测试八字数据:', JSON.stringify(testBaziData, null, 2));

  // 测试专业计算
  console.log('\n🎯 测试专业级流年计算...');
  const professionalResult = simulateCalculateProfessionalLiunian(testBaziData);
  
  console.log('\n📊 专业计算结果验证:');
  console.log(`  成功状态: ${professionalResult.success}`);
  console.log(`  当前流年: ${professionalResult.currentLiunian ? '存在' : '不存在'}`);
  console.log(`  流年列表: ${professionalResult.liunianList ? professionalResult.liunianList.length + '年' : '不存在'}`);
  console.log(`  统计摘要: ${professionalResult.summary ? '存在' : '不存在'}`);
  
  if (professionalResult.summary) {
    console.log(`    总年数: ${professionalResult.summary.totalYears}`);
    console.log(`    平均分: ${professionalResult.summary.averageScore}`);
    console.log(`    最佳年: ${professionalResult.summary.bestYear ? professionalResult.summary.bestYear.year : '未知'}`);
    console.log(`    最差年: ${professionalResult.summary.worstYear ? professionalResult.summary.worstYear.year : '未知'}`);
  }

  // 测试降级数据
  console.log('\n🔄 测试降级数据...');
  const fallbackResult = simulateGetFallbackLiunianData();
  
  console.log('\n📊 降级数据结果验证:');
  console.log(`  成功状态: ${fallbackResult.success}`);
  console.log(`  统计摘要: ${fallbackResult.summary ? '存在' : '不存在'}`);
  
  if (fallbackResult.summary) {
    console.log(`    总年数: ${fallbackResult.summary.totalYears}`);
    console.log(`    平均分: ${fallbackResult.summary.averageScore}`);
    console.log(`    最佳年: ${fallbackResult.summary.bestYear ? fallbackResult.summary.bestYear.year : '未知'}`);
    console.log(`    最差年: ${fallbackResult.summary.worstYear ? fallbackResult.summary.worstYear.year : '未知'}`);
  }

  return {
    professional: professionalResult,
    fallback: fallbackResult
  };
}

// 执行测试
console.log('🚀 开始流年数据流调试');
console.log('调试时间:', new Date().toLocaleString());
console.log('');

const testResults = testDataFlow();

console.log('\n🎉 调试完成！');
console.log('测试结果已保存到 testResults 变量中');
