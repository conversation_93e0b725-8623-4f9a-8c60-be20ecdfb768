/* 布局样式文件 */

/* 顶部导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  padding: var(--spacing-md) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  backdrop-filter: blur(10rpx);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.nav-title {
  font-size: var(--font-lg);
  color: white;
  font-weight: bold;
}

.nav-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

/* 内容区域 */
.content-area {
  margin-top: 88rpx;
  padding: var(--spacing-xl);
  min-height: calc(100vh - 88rpx);
  background: var(--background);
  transition: opacity 0.3s ease;
}

.content-area.loading {
  opacity: 0.6;
}

.content-area.loaded {
  animation: fadeIn 0.4s ease;
}

.content-area.with-sidebar {
  margin-left: 240rpx;
  transition: margin-left 0.3s ease;
}

@media screen and (max-width: 768rpx) {
  .content-area.with-sidebar {
    margin-left: 0;
  }
}

.section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: var(--font-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: bold;
}

/* 底部操作区 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-xl);
  background: white;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 安全区适配 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 网格布局 */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}

.grid.compact {
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: var(--spacing-sm);
}

.grid.wide {
  grid-template-columns: repeat(auto-fit, minmax(400rpx, 1fr));
  gap: var(--spacing-lg);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* 弹性布局 */
.flex-row {
  display: flex;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-center {
  justify-content: center;
}

/* 间距类 */
.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* 边距类 */
.margin-top {
  margin-top: var(--spacing-md);
}

.margin-bottom {
  margin-bottom: var(--spacing-md);
}

/* 滚动容器 */
.scroll-view {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 98;
  backdrop-filter: blur(4rpx);
}