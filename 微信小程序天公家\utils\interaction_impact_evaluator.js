/**
 * 交互影响评估系统
 * 评估动态交互对整体命局的影响程度和方向
 * 
 * 核心功能：
 * 1. 整体影响度量化 (总体强弱变化)
 * 2. 日主影响评估 (对日干的具体影响)
 * 3. 用神影响分析 (对用神的帮扶/克制)
 * 4. 格局稳定性评估 (格局是否被破坏)
 * 5. 吉凶趋势判断 (整体运势方向)
 */

class InteractionImpactEvaluator {
  constructor() {
    this.initializeEvaluationCriteria();
    this.initializeImpactWeights();
    this.initializePatternAnalysis();
  }

  /**
   * 初始化评估标准
   */
  initializeEvaluationCriteria() {
    // 影响强度等级
    this.IMPACT_LEVELS = {
      extreme: { min: 0.8, description: '极强影响', score: 5 },
      strong: { min: 0.6, description: '强影响', score: 4 },
      moderate: { min: 0.4, description: '中等影响', score: 3 },
      mild: { min: 0.2, description: '轻微影响', score: 2 },
      minimal: { min: 0.0, description: '微弱影响', score: 1 }
    };

    // 影响方向
    this.IMPACT_DIRECTIONS = {
      positive: { description: '正面影响', multiplier: 1 },
      negative: { description: '负面影响', multiplier: -1 },
      neutral: { description: '中性影响', multiplier: 0 }
    };

    // 日主强弱判断标准
    this.DAYMASTER_STRENGTH = {
      veryStrong: { min: 35, description: '日主过旺' },
      strong: { min: 25, description: '日主偏旺' },
      balanced: { min: 15, description: '日主中和' },
      weak: { min: 8, description: '日主偏弱' },
      veryWeak: { min: 0, description: '日主过弱' }
    };
  }

  /**
   * 初始化影响权重
   */
  initializeImpactWeights() {
    // 不同交互类型的影响权重
    this.INTERACTION_WEIGHTS = {
      threeDirectional: 1.0,    // 三会方 - 最高权重
      threeHarmony: 0.8,        // 三合局 - 高权重
      sixCombination: 0.6,      // 六合 - 中等权重
      fiveCombination: 0.5,     // 五合 - 中等权重
      sixClash: 0.7,            // 六冲 - 较高权重
      threePunishment: 0.6      // 三刑 - 中等权重
    };

    // 位置权重 (年月日时的重要性)
    this.POSITION_WEIGHTS = {
      0: 0.7,  // 年柱 - 祖上，影响较小
      1: 1.0,  // 月柱 - 月令，影响最大
      2: 0.9,  // 日柱 - 日主，影响很大
      3: 0.8   // 时柱 - 子息，影响较大
    };
  }

  /**
   * 初始化格局分析
   */
  initializePatternAnalysis() {
    // 常见格局类型
    this.PATTERN_TYPES = {
      zhengcai: { name: '正财格', favorable: ['食神', '伤官'], unfavorable: ['比肩', '劫财'] },
      piancai: { name: '偏财格', favorable: ['食神', '伤官'], unfavorable: ['比肩', '劫财'] },
      zhengguan: { name: '正官格', favorable: ['正印', '偏印'], unfavorable: ['伤官', '七杀'] },
      qisha: { name: '七杀格', favorable: ['食神', '正印'], unfavorable: ['伤官', '财星'] },
      zhengyin: { name: '正印格', favorable: ['正官', '七杀'], unfavorable: ['财星'] },
      pianyin: { name: '偏印格', favorable: ['正官', '七杀'], unfavorable: ['食神'] },
      shishen: { name: '食神格', favorable: ['正财', '偏财'], unfavorable: ['正印', '偏印'] },
      shangguan: { name: '伤官格', favorable: ['正财', '偏财'], unfavorable: ['正官', '正印'] }
    };
  }

  /**
   * 综合影响评估主方法
   * @param {Object} staticPowers - 静态五行力量
   * @param {Object} dynamicPowers - 动态调整后力量
   * @param {Object} interactions - 交互关系
   * @param {Object} fourPillars - 四柱信息
   * @returns {Object} 综合影响评估报告
   */
  evaluateComprehensiveImpact(staticPowers, dynamicPowers, interactions, fourPillars) {
    console.log('\n🔍 开始交互影响综合评估...');
    console.log('=' .repeat(60));

    // 1. 整体影响度量化
    const overallImpact = this.calculateOverallImpact(staticPowers, dynamicPowers);

    // 2. 日主影响评估
    const daymasterImpact = this.evaluateDaymasterImpact(staticPowers, dynamicPowers, fourPillars);

    // 3. 五行平衡分析
    const balanceAnalysis = this.analyzeElementBalance(staticPowers, dynamicPowers);

    // 4. 交互强度评估
    const interactionStrength = this.evaluateInteractionStrength(interactions);

    // 5. 格局影响分析
    const patternImpact = this.analyzePatternImpact(interactions, fourPillars);

    // 6. 吉凶趋势判断
    const fortuneTrend = this.assessFortuneTrend(overallImpact, daymasterImpact, balanceAnalysis);

    // 7. 生成综合评估报告
    const comprehensiveReport = this.generateComprehensiveReport({
      overallImpact,
      daymasterImpact,
      balanceAnalysis,
      interactionStrength,
      patternImpact,
      fortuneTrend
    });

    console.log('\n📊 综合影响评估完成');
    console.log('整体影响等级:', overallImpact.level);
    console.log('日主影响方向:', daymasterImpact.direction);
    console.log('吉凶趋势:', fortuneTrend.trend);

    return comprehensiveReport;
  }

  /**
   * 计算整体影响
   */
  calculateOverallImpact(staticPowers, dynamicPowers) {
    console.log('\n📊 计算整体影响度...');

    let totalChange = 0;
    let maxChange = 0;
    const elementChanges = {};

    Object.keys(staticPowers).forEach(element => {
      const staticValue = staticPowers[element];
      const dynamicValue = dynamicPowers[element];
      const changeRate = staticValue > 0 ? Math.abs((dynamicValue - staticValue) / staticValue) : 0;
      
      elementChanges[element] = {
        static: staticValue,
        dynamic: dynamicValue,
        change: dynamicValue - staticValue,
        changeRate: changeRate,
        changePercent: (changeRate * 100).toFixed(1) + '%'
      };

      totalChange += changeRate;
      maxChange = Math.max(maxChange, changeRate);
    });

    const avgChange = totalChange / Object.keys(staticPowers).length;
    const impactLevel = this.determineImpactLevel(maxChange);

    console.log(`  平均变化率: ${(avgChange * 100).toFixed(1)}%`);
    console.log(`  最大变化率: ${(maxChange * 100).toFixed(1)}%`);
    console.log(`  影响等级: ${impactLevel.description}`);

    return {
      totalChange,
      avgChange,
      maxChange,
      elementChanges,
      level: impactLevel.description,
      score: impactLevel.score
    };
  }

  /**
   * 评估日主影响
   */
  evaluateDaymasterImpact(staticPowers, dynamicPowers, fourPillars) {
    console.log('\n📊 评估日主影响...');

    const daymaster = fourPillars[2].gan; // 日干
    const daymasterElement = this.getElementFromGan(daymaster);
    
    // 计算日主力量变化
    const staticStrength = staticPowers[daymasterElement];
    const dynamicStrength = dynamicPowers[daymasterElement];
    const strengthChange = dynamicStrength - staticStrength;
    const changeRate = staticStrength > 0 ? strengthChange / staticStrength : 0;

    // 判断日主强弱
    const strengthLevel = this.determineDaymasterStrength(dynamicStrength);
    
    // 判断影响方向
    const direction = strengthChange > 0 ? 'positive' : strengthChange < 0 ? 'negative' : 'neutral';
    
    console.log(`  日主 ${daymaster}(${daymasterElement}): ${staticStrength.toFixed(1)} → ${dynamicStrength.toFixed(1)}`);
    console.log(`  力量变化: ${strengthChange > 0 ? '+' : ''}${strengthChange.toFixed(1)} (${(changeRate * 100).toFixed(1)}%)`);
    console.log(`  强弱等级: ${strengthLevel.description}`);
    console.log(`  影响方向: ${this.IMPACT_DIRECTIONS[direction].description}`);

    return {
      daymaster,
      element: daymasterElement,
      staticStrength,
      dynamicStrength,
      strengthChange,
      changeRate,
      strengthLevel: strengthLevel.description,
      direction: this.IMPACT_DIRECTIONS[direction].description,
      isBalanced: strengthLevel.description === '日主中和'
    };
  }

  /**
   * 分析五行平衡
   */
  analyzeElementBalance(staticPowers, dynamicPowers) {
    console.log('\n📊 分析五行平衡变化...');

    const staticTotal = Object.values(staticPowers).reduce((sum, val) => sum + val, 0);
    const dynamicTotal = Object.values(dynamicPowers).reduce((sum, val) => sum + val, 0);

    const staticDistribution = {};
    const dynamicDistribution = {};
    const balanceChanges = {};

    Object.keys(staticPowers).forEach(element => {
      staticDistribution[element] = staticPowers[element] / staticTotal;
      dynamicDistribution[element] = dynamicPowers[element] / dynamicTotal;
      
      const balanceChange = dynamicDistribution[element] - staticDistribution[element];
      balanceChanges[element] = {
        staticPercent: (staticDistribution[element] * 100).toFixed(1) + '%',
        dynamicPercent: (dynamicDistribution[element] * 100).toFixed(1) + '%',
        balanceChange: balanceChange,
        changePercent: (balanceChange * 100).toFixed(1) + '%'
      };
    });

    // 计算平衡度 (标准差)
    const staticVariance = this.calculateVariance(Object.values(staticDistribution));
    const dynamicVariance = this.calculateVariance(Object.values(dynamicDistribution));
    const balanceImprovement = staticVariance - dynamicVariance;

    console.log(`  静态平衡度: ${(1 - staticVariance).toFixed(3)}`);
    console.log(`  动态平衡度: ${(1 - dynamicVariance).toFixed(3)}`);
    console.log(`  平衡改善: ${balanceImprovement > 0 ? '改善' : '恶化'} (${(balanceImprovement * 100).toFixed(1)}%)`);

    return {
      staticDistribution,
      dynamicDistribution,
      balanceChanges,
      staticBalance: 1 - staticVariance,
      dynamicBalance: 1 - dynamicVariance,
      balanceImprovement,
      isImproved: balanceImprovement > 0
    };
  }

  /**
   * 评估交互强度
   */
  evaluateInteractionStrength(interactions) {
    console.log('\n📊 评估交互强度...');

    let totalStrength = 0;
    let interactionCount = 0;
    const strengthDetails = {};

    Object.entries(interactions).forEach(([type, typeInteractions]) => {
      if (typeInteractions && typeInteractions.length > 0) {
        const weight = this.INTERACTION_WEIGHTS[type] || 0.5;
        const typeStrength = typeInteractions.length * weight;
        
        strengthDetails[type] = {
          count: typeInteractions.length,
          weight: weight,
          strength: typeStrength,
          interactions: typeInteractions
        };

        totalStrength += typeStrength;
        interactionCount += typeInteractions.length;
      }
    });

    const avgStrength = interactionCount > 0 ? totalStrength / interactionCount : 0;
    const strengthLevel = this.determineImpactLevel(avgStrength);

    console.log(`  总交互数: ${interactionCount}`);
    console.log(`  总强度: ${totalStrength.toFixed(2)}`);
    console.log(`  平均强度: ${avgStrength.toFixed(2)}`);
    console.log(`  强度等级: ${strengthLevel.description}`);

    return {
      totalStrength,
      avgStrength,
      interactionCount,
      strengthLevel: strengthLevel.description,
      strengthDetails
    };
  }

  /**
   * 分析格局影响
   */
  analyzePatternImpact(interactions, fourPillars) {
    console.log('\n📊 分析格局影响...');

    // 简化的格局分析 - 基于月令和日主关系
    const daymaster = fourPillars[2].gan;
    const monthBranch = fourPillars[1].zhi;
    
    // 检查是否有破格的交互
    const destructiveInteractions = [];
    const supportiveInteractions = [];

    Object.entries(interactions).forEach(([type, typeInteractions]) => {
      if (typeInteractions && typeInteractions.length > 0) {
        typeInteractions.forEach(interaction => {
          if (type.includes('Clash') || type.includes('Punishment')) {
            destructiveInteractions.push({
              type: type,
              description: interaction.description,
              severity: this.INTERACTION_WEIGHTS[type]
            });
          } else {
            supportiveInteractions.push({
              type: type,
              description: interaction.description,
              strength: this.INTERACTION_WEIGHTS[type]
            });
          }
        });
      }
    });

    const patternStability = destructiveInteractions.length === 0 ? 'stable' : 
                           destructiveInteractions.length <= 2 ? 'affected' : 'damaged';

    console.log(`  日主: ${daymaster}, 月令: ${monthBranch}`);
    console.log(`  支持性交互: ${supportiveInteractions.length}个`);
    console.log(`  破坏性交互: ${destructiveInteractions.length}个`);
    console.log(`  格局稳定性: ${patternStability}`);

    return {
      daymaster,
      monthBranch,
      supportiveInteractions,
      destructiveInteractions,
      patternStability,
      stabilityScore: patternStability === 'stable' ? 5 : patternStability === 'affected' ? 3 : 1
    };
  }

  /**
   * 评估吉凶趋势
   */
  assessFortuneTrend(overallImpact, daymasterImpact, balanceAnalysis) {
    console.log('\n📊 评估吉凶趋势...');

    let fortuneScore = 0;
    const factors = [];

    // 1. 日主强弱适中加分
    if (daymasterImpact.isBalanced) {
      fortuneScore += 2;
      factors.push('日主中和 (+2)');
    } else if (daymasterImpact.strengthLevel.includes('偏')) {
      fortuneScore += 1;
      factors.push('日主偏强/偏弱 (+1)');
    } else {
      fortuneScore -= 1;
      factors.push('日主过强/过弱 (-1)');
    }

    // 2. 五行平衡改善加分
    if (balanceAnalysis.isImproved) {
      fortuneScore += 1;
      factors.push('五行平衡改善 (+1)');
    } else {
      fortuneScore -= 1;
      factors.push('五行平衡恶化 (-1)');
    }

    // 3. 整体影响适度加分
    if (overallImpact.score <= 3) {
      fortuneScore += 1;
      factors.push('变化适度 (+1)');
    } else {
      fortuneScore -= 1;
      factors.push('变化过激 (-1)');
    }

    // 判断趋势
    let trend, description;
    if (fortuneScore >= 2) {
      trend = 'auspicious';
      description = '吉利趋势';
    } else if (fortuneScore >= 0) {
      trend = 'neutral';
      description = '中性趋势';
    } else {
      trend = 'inauspicious';
      description = '不利趋势';
    }

    console.log(`  吉凶得分: ${fortuneScore}`);
    console.log(`  趋势判断: ${description}`);
    console.log(`  影响因素: ${factors.join(', ')}`);

    return {
      score: fortuneScore,
      trend: description,
      factors,
      recommendation: this.generateRecommendation(trend, factors)
    };
  }

  /**
   * 生成综合报告
   */
  generateComprehensiveReport(evaluationResults) {
    return {
      algorithm: '交互影响综合评估系统',
      version: 'V1.0 - 基于传统命理学理论',
      timestamp: new Date().toISOString(),
      
      summary: {
        overallImpactLevel: evaluationResults.overallImpact.level,
        daymasterDirection: evaluationResults.daymasterImpact.direction,
        balanceImprovement: evaluationResults.balanceAnalysis.isImproved,
        interactionStrength: evaluationResults.interactionStrength.strengthLevel,
        patternStability: evaluationResults.patternImpact.patternStability,
        fortuneTrend: evaluationResults.fortuneTrend.trend
      },

      detailedAnalysis: evaluationResults,

      recommendations: {
        primary: evaluationResults.fortuneTrend.recommendation,
        secondary: this.generateSecondaryRecommendations(evaluationResults)
      },

      confidence: this.calculateConfidence(evaluationResults)
    };
  }

  /**
   * 辅助方法
   */
  getElementFromGan(gan) {
    const ganElementMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };
    return ganElementMap[gan];
  }

  determineImpactLevel(changeRate) {
    for (const [level, criteria] of Object.entries(this.IMPACT_LEVELS)) {
      if (changeRate >= criteria.min) {
        return criteria;
      }
    }
    return this.IMPACT_LEVELS.minimal;
  }

  determineDaymasterStrength(strength) {
    for (const [level, criteria] of Object.entries(this.DAYMASTER_STRENGTH)) {
      if (strength >= criteria.min) {
        return criteria;
      }
    }
    return this.DAYMASTER_STRENGTH.veryWeak;
  }

  calculateVariance(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }

  generateRecommendation(trend, factors) {
    const recommendations = {
      auspicious: '当前动态交互对命局产生正面影响，宜把握机遇，顺势而为。',
      neutral: '当前动态交互影响中性，宜保持现状，稳中求进。',
      inauspicious: '当前动态交互对命局产生负面影响，宜谨慎行事，化解不利。'
    };
    return recommendations[trend] || '建议详细分析具体情况。';
  }

  generateSecondaryRecommendations(results) {
    const recommendations = [];
    
    if (!results.daymasterImpact.isBalanced) {
      recommendations.push('建议通过五行调理平衡日主强弱');
    }
    
    if (!results.balanceAnalysis.isImproved) {
      recommendations.push('建议关注五行平衡，避免偏枯');
    }
    
    if (results.patternImpact.patternStability === 'damaged') {
      recommendations.push('格局受损，建议寻求专业指导');
    }

    return recommendations;
  }

  calculateConfidence(results) {
    let confidence = 0.8; // 基础置信度

    // 根据交互数量调整
    if (results.interactionStrength.interactionCount >= 3) {
      confidence += 0.1;
    }

    // 根据变化幅度调整
    if (results.overallImpact.score <= 3) {
      confidence += 0.1;
    } else {
      confidence -= 0.1;
    }

    return Math.min(Math.max(confidence, 0.5), 0.95);
  }
}

// 导出模块
module.exports = InteractionImpactEvaluator;
