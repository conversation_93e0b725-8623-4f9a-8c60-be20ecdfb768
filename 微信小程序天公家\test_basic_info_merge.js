/**
 * 测试基本信息模块合并功能
 * 验证八字概览内容是否正确合并到基本信息模块
 */

console.log('🧪 开始测试基本信息模块合并功能');
console.log('='.repeat(60));

// 模拟测试数据
const testBirthInfo = {
  year: 1990,
  month: 5,
  day: 15,
  hour: 10,
  minute: 30,
  name: '测试用户',
  gender: '男',
  birthCity: '北京'
};

const testFrontendResult = {
  basicInfo: {
    birth_solar_term: '立夏后15天',
    kong_wang: '戌亥空',
    ming_gua: '乾卦',
    auxiliary_stars: ['天乙贵人', '文昌贵人'],
    shen_sha: ['天德', '月德'],
    classical_analysis: '日主甲木生于巳月，火旺木焚...'
  },
  baziInfo: {
    dayPillar: {
      earthly: '午'
    }
  },
  jieqiInfo: '立夏后15天'
};

// 模拟页面方法
function getZodiac(year) {
  const zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
  return zodiacs[(year - 4) % 12];
}

function calculateConstellation(month, day) {
  const constellations = [
    { name: '摩羯座', start: [12, 22], end: [1, 19] },
    { name: '水瓶座', start: [1, 20], end: [2, 18] },
    { name: '双鱼座', start: [2, 19], end: [3, 20] },
    { name: '白羊座', start: [3, 21], end: [4, 19] },
    { name: '金牛座', start: [4, 20], end: [5, 20] },
    { name: '双子座', start: [5, 21], end: [6, 21] },
    { name: '巨蟹座', start: [6, 22], end: [7, 22] },
    { name: '狮子座', start: [7, 23], end: [8, 22] },
    { name: '处女座', start: [8, 23], end: [9, 22] },
    { name: '天秤座', start: [9, 23], end: [10, 23] },
    { name: '天蝎座', start: [10, 24], end: [11, 22] },
    { name: '射手座', start: [11, 23], end: [12, 21] }
  ];

  for (const constellation of constellations) {
    const [startMonth, startDay] = constellation.start;
    const [endMonth, endDay] = constellation.end;
    
    if (startMonth === endMonth) {
      if (month === startMonth && day >= startDay && day <= endDay) {
        return constellation.name;
      }
    } else {
      if ((month === startMonth && day >= startDay) || 
          (month === endMonth && day <= endDay)) {
        return constellation.name;
      }
    }
  }
  return '未知';
}

function calculateStarMansion(dayZhi) {
  const starMansions = {
    '子': '虚日鼠', '丑': '危月燕', '寅': '室火猪', '卯': '壁水貐',
    '辰': '奎木狼', '巳': '娄金狗', '午': '胃土雉', '未': '昴日鸡',
    '申': '毕月乌', '酉': '觜火猴', '戌': '参水猿', '亥': '井木犴'
  };
  return starMansions[dayZhi] || '未知';
}

// 测试数据转换
console.log('\n📊 测试数据转换:');
console.log('输入数据:', {
  birthInfo: testBirthInfo,
  frontendResult: testFrontendResult
});

// 模拟转换过程
const basicInfo = testFrontendResult.basicInfo || {};
const convertedBaziInfo = testFrontendResult.baziInfo || {};

const expectedResult = {
  // 基本用户信息
  name: testBirthInfo.name,
  gender: testBirthInfo.gender,
  zodiac: getZodiac(testBirthInfo.year),
  birthDate: `${testBirthInfo.year}年${testBirthInfo.month}月${testBirthInfo.day}日`,
  birthTime: `${testBirthInfo.hour}:${testBirthInfo.minute.toString().padStart(2, '0')}`,
  location: testBirthInfo.birthCity,
  
  // 八字概览合并的内容
  birth_solar_term: basicInfo.birth_solar_term,
  kong_wang: basicInfo.kong_wang,
  ming_gua: basicInfo.ming_gua,
  constellation: calculateConstellation(testBirthInfo.month, testBirthInfo.day),
  star_mansion: calculateStarMansion(convertedBaziInfo.dayPillar?.earthly),
  jieqiInfo: testFrontendResult.jieqiInfo
};

console.log('\n✅ 转换结果:');
console.log('基本信息字段:');
console.log(`  姓名: ${expectedResult.name}`);
console.log(`  性别: ${expectedResult.gender}`);
console.log(`  生肖: ${expectedResult.zodiac}`);
console.log(`  出生日期: ${expectedResult.birthDate}`);
console.log(`  出生时间: ${expectedResult.birthTime}`);
console.log(`  出生地点: ${expectedResult.location}`);

console.log('\n八字概览合并字段:');
console.log(`  出生节气: ${expectedResult.birth_solar_term}`);
console.log(`  空亡: ${expectedResult.kong_wang}`);
console.log(`  星座: ${expectedResult.constellation}`);
console.log(`  星宿: ${expectedResult.star_mansion}`);
console.log(`  命卦: ${expectedResult.ming_gua}`);

// 验证结果
console.log('\n🔍 验证结果:');
const validations = [
  { field: '生肖', value: expectedResult.zodiac, expected: '马', pass: expectedResult.zodiac === '马' },
  { field: '出生节气', value: expectedResult.birth_solar_term, expected: '立夏后15天', pass: expectedResult.birth_solar_term === '立夏后15天' },
  { field: '空亡', value: expectedResult.kong_wang, expected: '戌亥空', pass: expectedResult.kong_wang === '戌亥空' },
  { field: '星座', value: expectedResult.constellation, expected: '金牛座', pass: expectedResult.constellation === '金牛座' },
  { field: '星宿', value: expectedResult.star_mansion, expected: '胃土雉', pass: expectedResult.star_mansion === '胃土雉' },
  { field: '命卦', value: expectedResult.ming_gua, expected: '乾卦', pass: expectedResult.ming_gua === '乾卦' }
];

let passCount = 0;
validations.forEach(validation => {
  const status = validation.pass ? '✅' : '❌';
  console.log(`${status} ${validation.field}: ${validation.value} (期望: ${validation.expected})`);
  if (validation.pass) passCount++;
});

console.log(`\n📈 测试结果: ${passCount}/${validations.length} 项通过`);

if (passCount === validations.length) {
  console.log('🎉 所有测试通过！基本信息模块合并功能正常');
} else {
  console.log('⚠️ 部分测试失败，需要检查实现');
}

console.log('\n📋 前端页面显示验证:');
console.log('WXML模板中应包含以下字段:');
console.log('- {{baziData.zodiac || baziData.userInfo.zodiac || \'未知\'}}');
console.log('- {{baziData.birth_solar_term || baziData.jieqiInfo || \'未知\'}}');
console.log('- {{baziData.kong_wang || \'未知\'}}');
console.log('- {{baziData.constellation || \'未知\'}}');
console.log('- {{baziData.star_mansion || \'未知\'}}');
console.log('- {{baziData.ming_gua || \'未知\'}}');

console.log('\n✅ 基本信息模块合并测试完成');
