#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
古籍批量智能处理器
专门处理4本八字命理古籍的完整数据提取
"""

import os
import json
import sqlite3
import re
from datetime import datetime
from typing import Dict, List, Optional
import hashlib

# 导入文档处理库
DOCX_AVAILABLE = False
PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
    print("✅ python-docx 库加载成功")
except ImportError:
    print("⚠️ python-docx 库未安装")

try:
    import fitz  # PyMuPDF
    PDF_AVAILABLE = True
    print("✅ PyMuPDF 库加载成功")
except ImportError:
    try:
        import PyPDF2
        PDF_AVAILABLE = True
        print("✅ PyPDF2 库加载成功")
    except ImportError:
        print("⚠️ PDF处理库未安装")

from 古籍全文处理器 import AdvancedTextProcessor, DatabaseBuilder, ExtractedRule

class BatchBookProcessor:
    """批量古籍处理器"""
    
    def __init__(self):
        self.processor = AdvancedTextProcessor()
        self.db_builder = DatabaseBuilder("占卜系统/data/bazi_classical_complete.db")
        
        self.book_files = {
            "渊海子平": "d:/天公师兄/渊海子平.docx",
            "穷通宝鉴": "d:/天公师兄/穷通宝鉴11.docx",
            "三命通会": "d:/天公师兄/《三命通会》完整白话版  .pdf",  # 注意文件名有空格
            "滴天髓": "d:/天公师兄/滴天髓.pdf"
        }
        
        self.processing_stats = {}
        self.all_extracted_rules = []
    
    def process_all_books(self):
        """处理所有古籍"""
        print("🚀 开始批量处理4本八字命理古籍")
        print("=" * 60)
        
        total_start_time = datetime.now()
        
        for book_name, file_path in self.book_files.items():
            print(f"\n📚 开始处理《{book_name}》")
            print(f"📁 文件路径: {file_path}")
            
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    print(f"❌ 文件不存在: {file_path}")
                    continue
                
                # 读取文件内容
                book_content = self._read_book_file(file_path)
                if not book_content:
                    print(f"❌ 无法读取文件内容: {file_path}")
                    continue
                
                print(f"✅ 文件读取成功，内容长度: {len(book_content)} 字符")
                
                # 处理古籍内容
                start_time = datetime.now()
                extracted_rules = self.processor.process_book(book_name, book_content)
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # 保存处理结果
                save_stats = self.db_builder.save_rules(extracted_rules, book_name)
                
                # 记录统计信息
                self.processing_stats[book_name] = {
                    "file_path": file_path,
                    "content_length": len(book_content),
                    "extracted_rules": len(extracted_rules),
                    "processing_time": processing_time,
                    "avg_confidence": save_stats.get("avg_confidence", 0),
                    "saved_count": save_stats.get("saved_count", 0)
                }
                
                self.all_extracted_rules.extend(extracted_rules)
                
                print(f"✅ 《{book_name}》处理完成")
                print(f"   📊 提取规则: {len(extracted_rules)} 条")
                print(f"   ⏱️ 处理时间: {processing_time:.2f} 秒")
                print(f"   📈 平均置信度: {save_stats.get('avg_confidence', 0):.3f}")
                
            except Exception as e:
                print(f"❌ 处理《{book_name}》时出错: {e}")
                self.processing_stats[book_name] = {
                    "error": str(e),
                    "status": "failed"
                }
        
        total_processing_time = (datetime.now() - total_start_time).total_seconds()
        
        # 生成综合报告
        self._generate_comprehensive_report(total_processing_time)
        
        # 生成集成代码
        self._generate_integration_code()
        
        print(f"\n🎉 批量处理完成！总耗时: {total_processing_time:.2f} 秒")
    
    def _read_book_file(self, file_path: str) -> Optional[str]:
        """读取古籍文件内容"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == '.docx':
                return self._read_docx_file(file_path)
            elif file_ext == '.pdf':
                return self._read_pdf_file(file_path)
            elif file_ext == '.txt':
                return self._read_txt_file(file_path)
            else:
                print(f"⚠️ 不支持的文件格式: {file_ext}")
                return None
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return None
    
    def _read_docx_file(self, file_path: str) -> Optional[str]:
        """读取DOCX文件"""
        if not DOCX_AVAILABLE:
            print("❌ 缺少python-docx库，无法处理DOCX文件")
            return None
        
        try:
            doc = Document(file_path)
            full_text = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    full_text.append(paragraph.text.strip())
            
            return '\n'.join(full_text)
        except Exception as e:
            print(f"❌ 读取DOCX文件失败: {e}")
            return None
    
    def _read_pdf_file(self, file_path: str) -> Optional[str]:
        """读取PDF文件"""
        # 尝试使用PyMuPDF
        try:
            import fitz
            doc = fitz.open(file_path)
            full_text = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text.strip():
                    full_text.append(text.strip())
            
            doc.close()
            return '\n'.join(full_text)
        except ImportError:
            pass
        except Exception as e:
            print(f"⚠️ PyMuPDF读取失败: {e}")
        
        # 尝试使用PyPDF2
        try:
            import PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                full_text = []
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    if text.strip():
                        full_text.append(text.strip())
                
                return '\n'.join(full_text)
        except ImportError:
            print("❌ 缺少PDF处理库，无法处理PDF文件")
            return None
        except Exception as e:
            print(f"❌ 读取PDF文件失败: {e}")
            return None
    
    def _read_txt_file(self, file_path: str) -> Optional[str]:
        """读取TXT文件"""
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read()
                except UnicodeDecodeError:
                    continue
            
            print(f"❌ 无法确定文件编码: {file_path}")
            return None
        except Exception as e:
            print(f"❌ 读取TXT文件失败: {e}")
            return None
    
    def _generate_comprehensive_report(self, total_time: float):
        """生成综合处理报告"""
        report = {
            "processing_summary": {
                "total_books": len(self.book_files),
                "successful_books": len([s for s in self.processing_stats.values() if "error" not in s]),
                "total_rules_extracted": len(self.all_extracted_rules),
                "total_processing_time": f"{total_time:.2f}秒",
                "processing_date": datetime.now().isoformat()
            },
            "book_details": self.processing_stats,
            "quality_analysis": self._analyze_data_quality(),
            "category_distribution": self._analyze_category_distribution(),
            "confidence_distribution": self._analyze_confidence_distribution()
        }
        
        # 保存报告
        report_path = "古籍处理综合报告.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印报告摘要
        print("\n📊 处理结果统计")
        print("=" * 40)
        print(f"📚 处理古籍数量: {report['processing_summary']['total_books']}")
        print(f"✅ 成功处理: {report['processing_summary']['successful_books']}")
        print(f"📋 提取规则总数: {report['processing_summary']['total_rules_extracted']}")
        print(f"⏱️ 总处理时间: {report['processing_summary']['total_processing_time']}")
        
        print(f"\n📈 数据质量分析:")
        quality = report['quality_analysis']
        print(f"   平均置信度: {quality['avg_confidence']:.3f}")
        print(f"   高质量规则: {quality['high_quality_count']} 条 ({quality['high_quality_ratio']:.1f}%)")
        print(f"   中等质量规则: {quality['medium_quality_count']} 条 ({quality['medium_quality_ratio']:.1f}%)")
        
        print(f"\n📊 类别分布:")
        for category, count in report['category_distribution'].items():
            print(f"   {category}: {count} 条")
        
        print(f"\n💾 详细报告已保存至: {report_path}")
    
    def _analyze_data_quality(self) -> Dict:
        """分析数据质量"""
        if not self.all_extracted_rules:
            return {"avg_confidence": 0, "high_quality_count": 0}
        
        confidences = [rule.confidence for rule in self.all_extracted_rules]
        avg_confidence = sum(confidences) / len(confidences)
        
        high_quality = len([c for c in confidences if c >= 0.8])
        medium_quality = len([c for c in confidences if 0.6 <= c < 0.8])
        low_quality = len([c for c in confidences if c < 0.6])
        
        total = len(confidences)
        
        return {
            "avg_confidence": avg_confidence,
            "high_quality_count": high_quality,
            "high_quality_ratio": (high_quality / total) * 100,
            "medium_quality_count": medium_quality,
            "medium_quality_ratio": (medium_quality / total) * 100,
            "low_quality_count": low_quality,
            "low_quality_ratio": (low_quality / total) * 100
        }
    
    def _analyze_category_distribution(self) -> Dict:
        """分析类别分布"""
        category_count = {}
        for rule in self.all_extracted_rules:
            category = rule.category
            category_count[category] = category_count.get(category, 0) + 1
        
        return dict(sorted(category_count.items(), key=lambda x: x[1], reverse=True))
    
    def _analyze_confidence_distribution(self) -> Dict:
        """分析置信度分布"""
        confidence_ranges = {
            "0.9-1.0": 0,
            "0.8-0.9": 0,
            "0.7-0.8": 0,
            "0.6-0.7": 0,
            "0.5-0.6": 0,
            "0.0-0.5": 0
        }
        
        for rule in self.all_extracted_rules:
            conf = rule.confidence
            if conf >= 0.9:
                confidence_ranges["0.9-1.0"] += 1
            elif conf >= 0.8:
                confidence_ranges["0.8-0.9"] += 1
            elif conf >= 0.7:
                confidence_ranges["0.7-0.8"] += 1
            elif conf >= 0.6:
                confidence_ranges["0.6-0.7"] += 1
            elif conf >= 0.5:
                confidence_ranges["0.5-0.6"] += 1
            else:
                confidence_ranges["0.0-0.5"] += 1
        
        return confidence_ranges
    
    def _generate_integration_code(self):
        """生成集成代码"""
        integration_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字命理古籍数据访问接口
自动生成的集成代码
"""

import sqlite3
import json
from typing import Dict, List, Optional

class ClassicalBaziDataAccess:
    """古籍八字数据访问类"""
    
    def __init__(self, db_path: str = "占卜系统/data/bazi_classical_complete.db"):
        self.db_path = db_path
    
    def query_pattern_rules(self, pattern_name: str = None, category: str = None, 
                          book_source: str = None, min_confidence: float = 0.5) -> List[Dict]:
        """查询格局规则"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM classical_rules WHERE confidence >= ?"
        params = [min_confidence]
        
        if pattern_name:
            query += " AND pattern_name LIKE ?"
            params.append(f"%{pattern_name}%")
        
        if category:
            query += " AND category = ?"
            params.append(category)
        
        if book_source:
            query += " AND book_source = ?"
            params.append(book_source)
        
        query += " ORDER BY confidence DESC"
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 转换为字典格式
        columns = [description[0] for description in cursor.description]
        results = []
        for row in rows:
            rule_dict = dict(zip(columns, row))
            # 解析JSON字段
            if rule_dict['conditions']:
                rule_dict['conditions'] = json.loads(rule_dict['conditions'])
            if rule_dict['interpretations']:
                rule_dict['interpretations'] = json.loads(rule_dict['interpretations'])
            results.append(rule_dict)
        
        conn.close()
        return results
    
    def get_statistics(self) -> Dict:
        """获取数据统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM classical_rules")
        total_rules = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(confidence) FROM classical_rules")
        avg_confidence = cursor.fetchone()[0] or 0
        
        # 按书籍统计
        cursor.execute("""
            SELECT book_source, COUNT(*), AVG(confidence) 
            FROM classical_rules 
            GROUP BY book_source
        """)
        book_stats = cursor.fetchall()
        
        # 按类别统计
        cursor.execute("""
            SELECT category, COUNT(*) 
            FROM classical_rules 
            GROUP BY category
        """)
        category_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            "total_rules": total_rules,
            "avg_confidence": round(avg_confidence, 3),
            "book_stats": book_stats,
            "category_stats": category_stats
        }

# 使用示例
if __name__ == "__main__":
    data_access = ClassicalBaziDataAccess()
    
    # 查询正格规则
    zhengge_rules = data_access.query_pattern_rules(category="正格", min_confidence=0.7)
    print(f"正格规则数量: {len(zhengge_rules)}")
    
    # 查询渊海子平的规则
    yuanhai_rules = data_access.query_pattern_rules(book_source="渊海子平", min_confidence=0.6)
    print(f"渊海子平规则数量: {len(yuanhai_rules)}")
    
    # 获取统计信息
    stats = data_access.get_statistics()
    print(f"数据库统计: {stats}")
'''
        
        # 保存集成代码
        with open("古籍数据访问接口.py", 'w', encoding='utf-8') as f:
            f.write(integration_code)
        
        print(f"🔧 集成代码已生成: 古籍数据访问接口.py")

def main():
    """主函数"""
    print("🚀 启动古籍批量智能处理系统")
    
    # 检查依赖库
    missing_libs = []
    try:
        import docx
    except ImportError:
        missing_libs.append("python-docx")
    
    try:
        import fitz
    except ImportError:
        try:
            import PyPDF2
        except ImportError:
            missing_libs.append("PyMuPDF 或 PyPDF2")
    
    if missing_libs:
        print(f"⚠️ 缺少依赖库: {', '.join(missing_libs)}")
        print("📦 建议安装: pip install python-docx PyMuPDF")
        print("🔄 将尝试使用基础功能继续处理...")
    
    # 开始批量处理
    processor = BatchBookProcessor()
    processor.process_all_books()

if __name__ == "__main__":
    main()
