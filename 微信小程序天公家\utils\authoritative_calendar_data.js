/**
 * 权威万年历数据 - JS模块格式
 * 基于紫金台万年历压缩数据（1900-2025年，30,058条记录）
 * 微信小程序兼容版本
 */

// 🔧 加载完整的万年历数据
let authoritativeCalendarData = null;

try {
  // 🔧 修复：微信小程序环境，使用JS版本的完整数据
  console.log('🔧 微信小程序环境：加载完整万年历数据');

  const completeCalendarModule = require('./complete_calendar_data.js');
  authoritativeCalendarData = completeCalendarModule.data;

  console.log('✅ 成功加载完整万年历数据:', {
    年份范围: authoritativeCalendarData.meta.year_range,
    总记录数: authoritativeCalendarData.meta.total_records,
    数据年份数: Object.keys(authoritativeCalendarData.data).length,
    包含2025年: !!authoritativeCalendarData.data["2025"],
    包含2024年: !!authoritativeCalendarData.data["2024"],
    包含2020年: !!authoritativeCalendarData.data["2020"]
  });

} catch (error) {
  console.warn('⚠️ 无法加载完整万年历数据，使用备用数据:', error);

  // 备用数据结构
  authoritativeCalendarData = {
    "meta": {
      "version": "1.0",
      "created_at": "2025-07-24T17:55:58.623077",
      "description": "压缩版万年历数据，适用于前端快速查询",
      "total_years": 126,
      "year_range": [1900, 2025],
      "total_records": 44604,
      "ganzhi_table": [
        "甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉",
        "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未",
        "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳",
        "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯",
        "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑",
        "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"
      ],
      "data_format": {
        "year_ganzhi": "年干支在60甲子表中的索引",
        "records": "[[月, 日, 干支索引], ...] 格式的记录数组"
      }
    },
    "data": {
      // 备用数据，使用算法计算
    }
  };
}

// 🔧 添加查询函数
function queryCalendarData(year, month, day) {
  if (!authoritativeCalendarData || !authoritativeCalendarData.data) {
    console.warn('⚠️ 万年历数据未加载');
    return null;
  }

  const yearData = authoritativeCalendarData.data[year.toString()];
  if (!yearData) {
    // 🔧 如果没有找到年份数据，尝试使用算法计算
    console.warn(`⚠️ 未找到年份数据: ${year}，尝试算法计算`);
    return calculateDayGanzhi(year, month, day);
  }

  // 查找对应的月日数据
  const records = yearData.r || [];
  const record = records.find(r => r[0] === month && r[1] === day);

  if (!record) {
    console.warn(`⚠️ 未找到日期数据: ${year}-${month}-${day}，尝试算法计算`);
    return calculateDayGanzhi(year, month, day);
  }

  const ganzhiIndex = record[2];
  const ganzhi = authoritativeCalendarData.meta.ganzhi_table[ganzhiIndex];

  return {
    year: year,
    month: month,
    day: day,
    ganzhi: ganzhi,
    ganzhiIndex: ganzhiIndex,
    yearGanzhi: authoritativeCalendarData.meta.ganzhi_table[yearData.g]
  };
}

// 🔧 算法计算日柱干支（备用方案）
function calculateDayGanzhi(year, month, day) {
  try {
    // 使用蔡勒公式计算日干支
    const baseDate = new Date(1900, 0, 31); // 1900年1月31日是甲子日
    const targetDate = new Date(year, month - 1, day);
    const daysDiff = Math.floor((targetDate - baseDate) / (1000 * 60 * 60 * 24));

    const ganzhiIndex = (daysDiff + 0) % 60; // 0对应甲子
    const adjustedIndex = ganzhiIndex < 0 ? ganzhiIndex + 60 : ganzhiIndex;

    const ganzhi = authoritativeCalendarData.meta.ganzhi_table[adjustedIndex];

    console.log('🔧 算法计算日柱:', {
      year, month, day,
      daysDiff,
      ganzhiIndex: adjustedIndex,
      ganzhi
    });

    return {
      year: year,
      month: month,
      day: day,
      ganzhi: ganzhi,
      ganzhiIndex: adjustedIndex,
      yearGanzhi: calculateYearGanzhi(year)
    };
  } catch (error) {
    console.error('❌ 算法计算失败:', error);
    return null;
  }
}

// 🔧 计算年干支
function calculateYearGanzhi(year) {
  const yearIndex = (year - 4) % 60; // 公元4年是甲子年
  const adjustedIndex = yearIndex < 0 ? yearIndex + 60 : yearIndex;
  return authoritativeCalendarData.meta.ganzhi_table[adjustedIndex];
}

// 🔧 检查数据完整性
function checkDataIntegrity() {
  if (!authoritativeCalendarData || !authoritativeCalendarData.data) {
    return false;
  }

  const currentYear = new Date().getFullYear();
  const hasCurrentYear = authoritativeCalendarData.data[currentYear.toString()];

  console.log('📊 万年历数据完整性检查:', {
    数据加载: !!authoritativeCalendarData,
    包含年份数: Object.keys(authoritativeCalendarData.data).length,
    包含当前年份: !!hasCurrentYear,
    年份范围: authoritativeCalendarData.meta?.year_range
  });

  return !!hasCurrentYear;
}

module.exports = {
  data: authoritativeCalendarData,
  query: queryCalendarData,
  checkIntegrity: checkDataIntegrity,
  calculateDayGanzhi: calculateDayGanzhi,
  calculateYearGanzhi: calculateYearGanzhi
};
