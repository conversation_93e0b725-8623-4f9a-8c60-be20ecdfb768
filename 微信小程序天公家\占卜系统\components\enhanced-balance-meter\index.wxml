<!--components/enhanced-balance-meter/index.wxml-->
<!-- 增强的五行平衡指标组件 -->
<view class="enhanced-balance-meter">
  <view class="balance-header">
    <view class="header-icon">⚖️</view>
    <view class="header-text">
      <text class="balance-title">五行平衡指数</text>
      <text class="balance-subtitle">基于古籍理论的数字化平衡评估</text>
    </view>
  </view>
  
  <!-- 主要平衡指标 -->
  <view class="balance-main">
    <view class="balance-score-container">
      <text class="balance-score">{{balanceIndex}}</text>
      <text class="balance-unit">分</text>
    </view>
    
    <view class="balance-status">
      <text class="status-text" style="color: {{statusColor}};">{{balanceStatus}}</text>
      <text class="status-desc">{{balanceDescription}}</text>
    </view>
  </view>
  
  <!-- 可视化进度条 -->
  <view class="balance-progress">
    <view class="progress-track">
      <view class="progress-fill" style="width: {{balanceIndex}}%; background: {{progressColor}};"></view>
      <view class="progress-thumb" style="left: {{balanceIndex}}%; background: {{progressColor}};"></view>
    </view>
    
    <!-- 刻度标记 -->
    <view class="progress-marks">
      <view class="mark" style="left: 0%;">
        <text class="mark-label">0</text>
        <text class="mark-desc">极度失衡</text>
      </view>
      <view class="mark" style="left: 25%;">
        <text class="mark-label">25</text>
        <text class="mark-desc">失衡</text>
      </view>
      <view class="mark" style="left: 50%;">
        <text class="mark-label">50</text>
        <text class="mark-desc">一般</text>
      </view>
      <view class="mark" style="left: 75%;">
        <text class="mark-label">75</text>
        <text class="mark-desc">平衡</text>
      </view>
      <view class="mark" style="left: 100%;">
        <text class="mark-label">100</text>
        <text class="mark-desc">完美</text>
      </view>
    </view>
  </view>
  
  <!-- 详细分析 -->
  <view wx:if="{{showDetails}}" class="balance-details">
    <view class="details-header">
      <text class="details-title">平衡分析详情</text>
    </view>
    
    <!-- 五行偏差分析 -->
    <view class="deviation-analysis">
      <view class="analysis-item">
        <text class="analysis-label">最强元素</text>
        <view class="analysis-value">
          <text class="element-name" style="color: {{strongestElement.color}};">{{strongestElement.name}}</text>
          <text class="element-score">{{strongestElement.score}}分</text>
        </view>
      </view>
      
      <view class="analysis-item">
        <text class="analysis-label">最弱元素</text>
        <view class="analysis-value">
          <text class="element-name" style="color: {{weakestElement.color}};">{{weakestElement.name}}</text>
          <text class="element-score">{{weakestElement.score}}分</text>
        </view>
      </view>
      
      <view class="analysis-item">
        <text class="analysis-label">偏差程度</text>
        <view class="analysis-value">
          <text class="deviation-value">{{deviationValue}}</text>
          <text class="deviation-level">{{deviationLevel}}</text>
        </view>
      </view>
    </view>
    
    <!-- 改善建议 */
    <view class="improvement-suggestions">
      <view class="suggestions-header">
        <text class="suggestions-title">平衡改善建议</text>
      </view>
      <view class="suggestions-list">
        <view wx:for="{{suggestions}}" wx:key="index" class="suggestion-item">
          <view class="suggestion-icon">{{item.icon}}</view>
          <view class="suggestion-content">
            <text class="suggestion-title">{{item.title}}</text>
            <text class="suggestion-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="balance-actions">
    <button class="action-btn" bindtap="toggleDetails">
      {{showDetails ? '收起详情' : '查看详情'}}
    </button>
    <button class="action-btn secondary" bindtap="shareBalance">
      分享结果
    </button>
  </view>
</view>
