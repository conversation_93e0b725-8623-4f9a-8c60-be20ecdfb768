[system] WeChatLib: 3.8.12 (2025.7.28 18:52:40)
[system] Subpackages: N/A
[system] LazyCodeLoading: false
app.js? [sm]:6 ✅ 天公师兄小程序启动，所有WXSS编译错误已修复
unified_wuxing_calculator_safe.js? [sm]:24 🎯 统一五行计算器初始化完成
unified_wuxing_calculator_safe.js? [sm]:394 🗑️ 五行计算缓存已清理
unified_wuxing_calculator_safe.js? [sm]:407 🗑️ 已强制清除专业级引擎缓存
unified_wuxing_calculator_safe.js? [sm]:410 🎯 统一五行计算器已重置，将使用改进的计算逻辑
unified_wuxing_calculator_safe.js? [sm]:411 🚀 修复版本已加载 - 版本时间戳: 2025-08-05T05:32:24.977Z
index.js? [sm]:21 ✅ 成功加载完整权威节气数据 (1900-2025年)
minor_fortune_calculator.js? [sm]:364 📦 小运计算系统模块加载完成
true_solar_time_corrector.js? [sm]:354 🌅 真太阳时修正系统模块加载完成
professional_liunian_calculator.js? [sm]:446 📦 专业级流年计算系统模块加载完成
precise_solar_terms_engine.js? [sm]:346 🌸 精确节气计算引擎模块加载完成
professional_dayun_calculator.js? [sm]:437 🔮 专业级大运计算器模块加载完成
unified_basic_info_calculator.js? [sm]:11 🔧 初始化统一基本信息计算器 v1.0.0
unified_basic_info_calculator.js? [sm]:366 ✅ 统一基本信息计算器已加载
index.js? [sm]:23 ✅ 占卜中心页面加载，开始集成玉匣记功能
index.js? [sm]:212 更新主题类名为: tarot-theme
index.js? [sm]:49 🔮 检查天公师父综合服务(端口8000)...
index.js? [sm]:92 🔍 尝试连接天公师父服务: undefined
index.js? [sm]:107 ⚠️ 主地址连接失败，尝试备用地址: request:fail invalid url "undefined/"
index.js? [sm]:123 ℹ️ 天公师父综合服务(端口8000)不可用，使用本地功能
index.js? [sm]:124 🔍 连接错误详情: {主地址错误: "request:fail invalid url "undefined/"", 备用地址错误: "request:fail invalid url "undefined/""}
app.js? [sm]:56 正在修复路由问题: pages/assessment-hub/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/assessment-hub/index
index.js? [sm]:72 📱 使用本地功能模式（天公师父服务不可用）
app.js? [sm]:56 正在修复路由问题: pages/assessment-hub/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/assessment-hub/index
[system] Launch Time: 2257 ms
[Deprecation] SharedArrayBuffer will require cross-origin isolation as of M92, around July 2021. See https://developer.chrome.com/blog/enabling-shared-array-buffer/ for more details.
index.js? [sm]:190 当前滑块索引: 1
index.js? [sm]:212 更新主题类名为: bazi-theme
index.js? [sm]:310 🔮 开始八字排盘流程（天工师父）: 
[自动热重载] 已开启代码文件保存后自动热重载
index.js? [sm]:102 八字排盘页面加载 {role: "bazi", master: "天工师父", question: ""}
index.js? [sm]:135 🚀 开始初始化优化后的古籍规则系统...
index.js? [sm]:244 ✅ 优化规则管理器创建完成
index.js? [sm]:250 ⚡ 初始化优化组件...
index.js? [sm]:160 📚 加载优化组件...
index.js? [sm]:118 🔮 八字排盘页面 - 角色信息: {role: "bazi", master: "天工师父", question: ""}
index.js? [sm]:298 🧹 清理转换数据缓存
index.js? [sm]:5061 📅 年份范围: 1900-2030，当前年份：2025
index.js? [sm]:5103 📅 默认年份设置: {currentYear: 2025, currentYearIndex: 5, yearInArray: "2025年"}
index.js? [sm]:5133 🏙️ 初始化城市数据
index.js? [sm]:5142 🏙️ 城市数据初始化完成，支持城市数量: 306
index.js? [sm]:5155 🏙️ 出生地坐标初始化完成: {city: "北京", coordinates: {…}}
index.js? [sm]:5275 🌞 真太阳时校正计算完成: {birthCity: "北京", longitude: 116.4074, timeDifference: -20, originalTime: "13:32", correctedTime: "13:11"}
index.js? [sm]:298 🧹 清理转换数据缓存
app.js? [sm]:56 正在修复路由问题: pages/bazi-input/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/bazi-input/index
index.js? [sm]:166 ✅ 优化系统加载完成
index.js? [sm]:257 📊 优化系统统计: {totalRules: 777, core: {…}, expansion: {…}, performance: {…}, components: {…}, …}
index.js? [sm]:265 ✅ 所有优化组件初始化完成
index.js? [sm]:6080 🔍 验证输入数据: {name: "突然", year: 2025, month: 8, day: 5, hour: 13, …}
index.js? [sm]:6114 🔍 日期验证: {original: {…}, parsed: {…}}
index.js? [sm]:6120 🔍 创建的日期对象: {date: "2025-08-04T16:00:00.000Z", getFullYear: 2025, getMonth: 7, getDate: 5, expected: {…}}
index.js? [sm]:6141 ✅ 输入验证通过
index.js? [sm]:6159 🔮 八字排盘 - 使用正确的干支历计算方法
index.js? [sm]:6160 📅 用户输入历法类型: solar
index.js? [sm]:6197 ☀️ 用户输入阳历，直接用于八字计算
index.js? [sm]:6248 🌞 真太阳时校正应用: {original: "2025-8-5 13:32", corrected: "2025-8-5 13:11", longitude: 116.4074, timeDiff: -20}
index.js? [sm]:6308 🧹 采用前端优先架构，开始本地计算
index.js? [sm]:6311 🔧 修复真太阳时重复计算问题
index.js? [sm]:6323 📋 使用原始用户输入时间: {original: "2025-8-5 13:32", note: "避免重复真太阳时校正"}
index.js? [sm]:6365 🎯 使用统一完整计算器计算八字: {year: 2025, month: 8, day: 5, hour: 13, minute: 32, …}
index.js? [sm]:6369 🔄 使用统一精确算法计算器...
complete_bazi_calculator.js? [sm]:12 🚀 完整八字计算器初始化完成 v3.0.0
complete_bazi_calculator.js? [sm]:142 🚀 开始完整八字计算（使用精确算法）: {year: 2025, month: 8, day: 5, hour: 13, minute: 32, …}
complete_bazi_calculator.js? [sm]:214 🔮 使用精确四柱计算算法: {year: 2025, month: 8, day: 5, hour: 13, minute: 32, …}
complete_bazi_calculator.js? [sm]:225 🌞 真太阳时校正: {原始时间: "2025/8/5下午1:32:00", 真太阳时: "2025/8/5下午1:32:00"}
complete_bazi_calculator.js? [sm]:231 🔧 开始计算四柱...
complete_bazi_calculator.js? [sm]:234 📅 年柱计算结果: {gan: "乙", zhi: "巳"}
complete_bazi_calculator.js? [sm]:303 🌸 开始精确月柱计算: {year: 2025, month: 8, day: 5, yearPillar: {…}}
authoritative_calendar_data.js? [sm]:12 🔧 微信小程序环境：加载完整万年历数据
authoritative_calendar_data.js? [sm]:17 ✅ 成功加载完整万年历数据: {年份范围: Array(2), 总记录数: 44604, 数据年份数: 3, 包含2025年: true, 包含2024年: true, …}
authoritative_calendar_data.js? [sm]:143 📊 万年历数据完整性检查: {数据加载: true, 包含年份数: 3, 包含当前年份: true, 年份范围: Array(2)}
complete_bazi_calculator.js? [sm]:411 🌸 使用权威节气数据计算月柱: {year: 2025, month: 8, day: 5}
complete_bazi_calculator.js? [sm]:468 ⚠️ 权威节气数据表加载失败: TypeError: FrontendReadyJieqiData is not a constructor
    at CompleteBaziCalculator.getSolarMonthByNodeQi (complete_bazi_calculator.js? [sm]:436)
    at CompleteBaziCalculator.calculatePreciseMonthPillar (complete_bazi_calculator.js? [sm]:309)
    at CompleteBaziCalculator.calculateFourPillars (complete_bazi_calculator.js? [sm]:236)
    at CompleteBaziCalculator.calculateComplete (complete_bazi_calculator.js? [sm]:154)
    at li.calculateBaziWithFrontend (index.js? [sm]:6372)
    at li.startPaipan (index.js? [sm]:6329)
    at Object.o.safeCallback (VM1546 WASubContext.js:1)
    at VM1546 WASubContext.js:1
    at wn (VM1546 WASubContext.js:1)
    at VM1546 WASubContext.js:1
getSolarMonthByNodeQi @ complete_bazi_calculator.js? [sm]:468
calculatePreciseMonthPillar @ complete_bazi_calculator.js? [sm]:309
calculateFourPillars @ complete_bazi_calculator.js? [sm]:236
calculateComplete @ complete_bazi_calculator.js? [sm]:154
calculateBaziWithFrontend @ index.js? [sm]:6372
startPaipan @ index.js? [sm]:6329
complete_bazi_calculator.js? [sm]:476 🔄 使用传统节气算法
complete_bazi_calculator.js? [sm]:310 ✅ 权威节气数据获取成功，农历月: 6
complete_bazi_calculator.js? [sm]:363 🔧 月柱计算详情: {year: 2025, month: 8, day: 5, solarMonth: 6, yearGan: "乙", …}
complete_bazi_calculator.js? [sm]:374 ✅ 精确月柱计算完成: {gan: "癸", zhi: "未"}
complete_bazi_calculator.js? [sm]:237 🌸 月柱计算结果: {gan: "癸", zhi: "未"}
complete_bazi_calculator.js? [sm]:1300 🔍 权威万年历查询日柱: {year: 2025, month: 8, day: 5}
complete_bazi_calculator.js? [sm]:1319 ✅ 权威万年历日柱查询成功: {gan: "癸", zhi: "亥", ganzhi: "癸亥", source: "权威万年历数据"}
complete_bazi_calculator.js? [sm]:240 ☀️ 日柱计算结果: {gan: "癸", zhi: "亥"}
complete_bazi_calculator.js? [sm]:243 ⏰ 时柱计算结果: {gan: "己", zhi: "未"}
complete_bazi_calculator.js? [sm]:248 🔍 四柱数据完整性检查: {fourPillarsLength: 4, yearPillar: {…}, monthPillar: {…}, dayPillar: {…}, hourPillar: {…}}
complete_bazi_calculator.js? [sm]:266 ✅ 四柱计算完成: (4) [{…}, {…}, {…}, {…}]
complete_bazi_calculator.js? [sm]:1104 🏠 命卦计算详细调试: {输入年份: 2025, 年份类型: "number", 解析年份: 2025, 年份后两位: 25, 输入性别: "男", …}
complete_bazi_calculator.js? [sm]:1119 🚹 男性命卦计算: {公式: "(99 - 25) % 9", 结果: 2}
complete_bazi_calculator.js? [sm]:192 ✅ 完整八字计算完成
index.js? [sm]:6374 ✅ 统一精确算法计算完成: {birthInfo: {…}, fourPillars: Array(4), nayin: {…}, tenGods: {…}, canggan: {…}, …}
index.js? [sm]:6379 ✅ 数据格式转换完成: {baziInfo: {…}, completeData: {…}, calculatorVersion: "unified_v3.0.0"}
index.js? [sm]:6331 🎯 前端计算完成: {baziInfo: {…}, completeData: {…}, calculatorVersion: "unified_v3.0.0"}
index.js? [sm]:6334 🎯 使用纯前端计算结果
index.js? [sm]:6338 📋 生成结果ID: frontend_1754371957321_krqivmyt4
index.js? [sm]:5792 🔮 前端八字计算完成: {baziInfo: {…}, completeData: {…}, calculatorVersion: "unified_v3.0.0"}
index.js? [sm]:5815 🔧 增强的出生信息: {name: "突然", year: 2025, month: 8, day: 5, hour: 13, …}
index.js? [sm]:5818 🔧 开始保存数据到本地存储...
index.js? [sm]:5822 ✅ 保存 bazi_result_id: frontend_1754371957321_krqivmyt4
index.js? [sm]:5825 ✅ 保存 bazi_birth_info: {name: "突然", year: 2025, month: 8, day: 5, hour: 13, …}
index.js? [sm]:5828 ✅ 保存 bazi_analysis_mode: comprehensive
index.js? [sm]:5831 ✅ 保存 bazi_calendar_type: solar
index.js? [sm]:5834 ✅ 保存 bazi_frontend_result: {baziInfo: {…}, completeData: {…}, calculatorVersion: "unified_v3.0.0"}
index.js? [sm]:5840 🔍 验证保存结果:
index.js? [sm]:5841   - bazi_birth_info 保存成功: true
index.js? [sm]:5842   - bazi_frontend_result 保存成功: true
index.js? [sm]:5857 ✅ 数据保存完成，准备跳转到结果页面
[自动热重载] 已开启代码文件保存后自动热重载
index.js? [sm]:72 ✅ 八字分析结果页面加载 {id: "frontend_1754371957321_krqivmyt4"}
index.js? [sm]:73 🔍 开始数据加载流程...
complete_bazi_calculator.js? [sm]:12 🚀 完整八字计算器初始化完成 v3.0.0
minor_fortune_calculator.js? [sm]:22 🎯 小运计算系统初始化完成 - 基于《三命通会·卷八》
true_solar_time_corrector.js? [sm]:72 🌅 真太阳时修正系统初始化完成
true_solar_time_corrector.js? [sm]:73 📍 基准时区: 东经120°
true_solar_time_corrector.js? [sm]:74 🗺️ 集成完整城市坐标数据库: 306个城市
true_solar_time_corrector.js? [sm]:75 🔄 向后兼容: 34个主要城市
professional_liunian_calculator.js? [sm]:60 🌟 专业级流年计算系统初始化完成
professional_liunian_calculator.js? [sm]:61 🌅 集成真太阳时修正系统（306个城市支持）
professional_liunian_calculator.js? [sm]:62 🌸 集成权威节气数据表（1900-2025年，分钟级精度）
precise_solar_terms_engine.js? [sm]:42 🌸 精确节气计算引擎初始化完成
precise_solar_terms_engine.js? [sm]:43 📊 支持年份范围: 1900-2025年
precise_solar_terms_engine.js? [sm]:44 🎯 数据精度: 分钟级
true_solar_time_corrector.js? [sm]:72 🌅 真太阳时修正系统初始化完成
true_solar_time_corrector.js? [sm]:73 📍 基准时区: 东经120°
true_solar_time_corrector.js? [sm]:74 🗺️ 集成完整城市坐标数据库: 306个城市
true_solar_time_corrector.js? [sm]:75 🔄 向后兼容: 34个主要城市
professional_dayun_calculator.js? [sm]:37 🔮 专业级大运计算器初始化完成
professional_dayun_calculator.js? [sm]:38 📅 集成精确节气计算引擎
professional_dayun_calculator.js? [sm]:39 🌅 集成真太阳时修正系统
unified_basic_info_calculator.js? [sm]:11 🔧 初始化统一基本信息计算器 v1.0.0
index.js? [sm]:96 ✅ 专业级计算器初始化完成
index.js? [sm]:97   - 完整八字计算器: ✅
index.js? [sm]:98   - 专业五行引擎: ✅
index.js? [sm]:99   - 小运计算器: ✅
index.js? [sm]:100   - 流年计算器: ✅
index.js? [sm]:101   - 大运计算器: ✅
index.js? [sm]:102   - 增强算法模块: ✅
index.js? [sm]:103   - 历史验证模块: ✅
index.js? [sm]:123 🔍 开始加载和处理八字数据...
index.js? [sm]:130 🔍 数据读取状态: {baziData: true, userInfo: false, birthInfo: true, baziDataKeys: Array(3)}
index.js? [sm]:146 🔧 处理八字数据...
index.js? [sm]:151 🔍 检查baziData结构: {hasBaziData: true, baziDataKeys: Array(3), hasBaziInfo: true, hasCompleteData: true, completeDataKeys: Array(14), …}
index.js? [sm]:183 ✅ 使用已有的完整数据
index.js? [sm]:190 🔍 检查completeResult结构:
index.js? [sm]:191   - completeResult存在: true
index.js? [sm]:192   - completeResult类型: object
index.js? [sm]:193   - completeResult键名: (3) ["baziInfo", "completeData", "calculatorVersion"]
index.js? [sm]:194   - completeResult完整内容: {baziInfo: {…}, completeData: {…}, calculatorVersion: "unified_v3.0.0"}
index.js? [sm]:207   - fourPillars: undefined
index.js? [sm]:207   - baziInfo.fourPillars: undefined
index.js? [sm]:207   - completeData.fourPillars: (4) [{…}, {…}, {…}, {…}]
index.js? [sm]:207   - data.fourPillars: undefined
index.js? [sm]:207   - result.fourPillars: undefined
index.js? [sm]:214 📋 四柱数据: (4) [{…}, {…}, {…}, {…}]
index.js? [sm]:215 🔍 四柱数据详细检查: {fourPillarsType: "object", fourPillarsLength: 4, pillar0: {…}, pillar1: {…}, pillar2: {…}, …}
index.js? [sm]:231 🔍 传递给专业五行引擎的数据:
index.js? [sm]:232   - fourPillars类型: object
index.js? [sm]:233   - 是否数组: true
index.js? [sm]:234   - 数组长度: 4
index.js? [sm]:235   - 完整fourPillars: (4) [{…}, {…}, {…}, {…}]
index.js? [sm]:240   - 第1柱详情: {完整对象: {…}, 对象类型: "object", 对象键: Array(2), 所有属性值: Array(2), gan属性: "乙", …}
index.js? [sm]:240   - 第2柱详情: {完整对象: {…}, 对象类型: "object", 对象键: Array(2), 所有属性值: Array(2), gan属性: "癸", …}
index.js? [sm]:240   - 第3柱详情: {完整对象: {…}, 对象类型: "object", 对象键: Array(2), 所有属性值: Array(2), gan属性: "癸", …}
index.js? [sm]:240   - 第4柱详情: {完整对象: {…}, 对象类型: "object", 对象键: Array(2), 所有属性值: Array(2), gan属性: "己", …}
index.js? [sm]:276 🔧 转换后的四柱数据: (4) [{…}, {…}, {…}, {…}]
professional_wuxing_engine.js? [sm]:510 🔄 强制重新计算（缓存已禁用）: 乙巳-癸未-癸亥-己未
professional_wuxing_engine.js? [sm]:101 🚀 开始专业级五行静态力量量化计算...
professional_wuxing_engine.js? [sm]:102 📋 输入四柱: 乙巳 癸未 癸亥 己未
professional_wuxing_engine.js? [sm]:122 
📊 Step 2: 计算天干基础力量...
professional_wuxing_engine.js? [sm]:140   年干 乙(木) +10分
professional_wuxing_engine.js? [sm]:140   月干 癸(水) +10分
professional_wuxing_engine.js? [sm]:140   日干 癸(水) +10分
professional_wuxing_engine.js? [sm]:140   时干 己(土) +10分
professional_wuxing_engine.js? [sm]:143   天干力量小计: {金: 0, 木: 10, 水: 20, 火: 0, 土: 10}
professional_wuxing_engine.js? [sm]:151 
📊 Step 3: 计算地支藏干精确力量...
professional_wuxing_engine.js? [sm]:159   年支 巳:
professional_wuxing_engine.js? [sm]:182     主气 火 +6.0分 (60%)
professional_wuxing_engine.js? [sm]:182     中气 金 +3.0分 (30%)
professional_wuxing_engine.js? [sm]:182     余气 土 +1.0分 (10%)
professional_wuxing_engine.js? [sm]:159   月支 未:
professional_wuxing_engine.js? [sm]:182     主气 土 +6.0分 (60%)
professional_wuxing_engine.js? [sm]:182     中气 火 +3.0分 (30%)
professional_wuxing_engine.js? [sm]:182     余气 木 +1.0分 (10%)
professional_wuxing_engine.js? [sm]:159   日支 亥:
professional_wuxing_engine.js? [sm]:182     主气 水 +7.0分 (70%)
professional_wuxing_engine.js? [sm]:182     中气 木 +3.0分 (30%)
professional_wuxing_engine.js? [sm]:159   时支 未:
professional_wuxing_engine.js? [sm]:182     主气 土 +6.0分 (60%)
professional_wuxing_engine.js? [sm]:182     中气 火 +3.0分 (30%)
professional_wuxing_engine.js? [sm]:182     余气 木 +1.0分 (10%)
professional_wuxing_engine.js? [sm]:188   地支藏干力量小计: {金: 3, 木: 15, 水: 27, 火: 12, 土: 23}
professional_wuxing_engine.js? [sm]:196 
📊 Step 4: 应用月令季节修正...
professional_wuxing_engine.js? [sm]:202   月支: 未 → 季节: 夏
professional_wuxing_engine.js? [sm]:203   季节修正系数: {火: 1.5, 土: 1.2, 木: 0.8, 水: 0.6, 金: 0.4}
professional_wuxing_engine.js? [sm]:231     金: 3.0 × 0.4 = 1.2 (死)
professional_wuxing_engine.js? [sm]:231     木: 15.0 × 0.8 = 12 (休)
professional_wuxing_engine.js? [sm]:231     水: 27.0 × 0.6 = 16.2 (囚)
professional_wuxing_engine.js? [sm]:231     火: 12.0 × 1.5 = 18 (旺)
professional_wuxing_engine.js? [sm]:231     土: 23.0 × 1.2 = 27.6 (相)
professional_wuxing_engine.js? [sm]:234   季节修正后最终力量: {金: 1.2, 木: 12, 水: 16.2, 火: 18, 土: 27.6}
professional_wuxing_engine.js? [sm]:113 ✅ 五行静态力量量化完成: {金: 1.2, 木: 12, 水: 16.2, 火: 18, 土: 27.6}
professional_wuxing_engine.js? [sm]:540 🎯 返回新计算结果（未缓存）
index.js? [sm]:279 🔥 专业五行计算完成: {algorithm: "专业级三层权重模型", version: "V4.0 - 基于《五行计算.txt》文档标准", inputData: {…}, calculationSteps: {…}, calculationDetails: {…}, …}
index.js? [sm]:656 🔧 神煞数据格式转换: {原始格式: "object", 吉星数量: 1, 凶煞数量: 0, 转换后数组长度: 1}
index.js? [sm]:697 🔍 神煞数据类型检查: {原始类型: "object", 是否数组: true, 处理后数组长度: 1, 示例数据: {…}}
index.js? [sm]:288 ⚡ 神煞计算完成: {auspicious: Array(1), inauspicious: Array(0), neutral: Array(0), stats: {…}}
index.js? [sm]:530 🔍 检查原始八字数据结构: {baziInfo: {…}, fourPillars: Array(4), yearPillar: {…}, monthPillar: {…}}
index.js? [sm]:594 🔧 八字数据完整性检查完成: {原始数据: true, 年柱: {…}, 月柱: {…}, 日柱: {…}, 时柱: {…}}
index.js? [sm]:319 🔧 构建的大运计算数据: {yearPillar: {…}, monthPillar: {…}, dayPillar: {…}, timePillar: {…}, birthInfo: {…}, …}
index.js? [sm]:320 🔍 月柱检查: {monthPillar: {…}, gan: "癸", zhi: "未"}
professional_dayun_calculator.js? [sm]:50 🔮 开始专业级大运计算...
true_solar_time_corrector.js? [sm]:124 🌅 真太阳时修正完成: 2025-08-05 13:32:00 → 2025-08-05 13:17:37
true_solar_time_corrector.js? [sm]:125 📍 经度修正: 116.4074° (西退14.4分钟)
true_solar_time_corrector.js? [sm]:163 🏙️ 城市修正: 北京 (东经116.4074°)
professional_dayun_calculator.js? [sm]:67 🌅 时间修正: 2025-08-05 13:32:00 → 2025-08-05 13:17:37
professional_dayun_calculator.js? [sm]:158 🧭 大运方向: 男命阴年生，逆行大运
professional_dayun_calculator.js? [sm]:169 📅 计算精确起运时间...
precise_solar_terms_engine.js? [sm]:103 ✅ 成功加载权威节气数据（Node.js环境）
权威节气数据_前端就绪版.js:1499 🌸 使用关键年份权威数据 - 2025年 (完整精度)
precise_solar_terms_engine.js? [sm]:76 🌸 获取2025年权威节气数据成功 (24个节气)
precise_solar_terms_engine.js? [sm]:315 🎯 大运目标节气: 小暑 (逆行)
professional_dayun_calculator.js? [sm]:211 📅 起运计算: 29.38天 → 9岁9个月15天1小时
professional_dayun_calculator.js? [sm]:212 🎯 目标节气: 小暑 (2025-07-07 04:17)
professional_dayun_calculator.js? [sm]:255 🔄 生成12步大运序列 (逆行)
professional_dayun_calculator.js? [sm]:115 ✅ 专业级大运计算完成
professional_dayun_calculator.js? [sm]:116 🎯 起运时间: 9岁9个月
professional_dayun_calculator.js? [sm]:117 🔄 大运方向: 男命阴年生，逆行大运
index.js? [sm]:327 🔮 大运计算完成: {input: {…}, calculation: {…}, analysis: {…}, metadata: {…}}
index.js? [sm]:530 🔍 检查原始八字数据结构: {baziInfo: {…}, fourPillars: Array(4), yearPillar: {…}, monthPillar: {…}}
index.js? [sm]:594 🔧 八字数据完整性检查完成: {原始数据: true, 年柱: {…}, 月柱: {…}, 日柱: {…}, 时柱: {…}}
index.js? [sm]:358 🔧 构建的流年计算数据: {yearPillar: {…}, monthPillar: {…}, dayPillar: {…}, timePillar: {…}, birthInfo: {…}, …}
professional_liunian_calculator.js? [sm]:276 🔮 开始计算2025年起5年流年分析...
professional_liunian_calculator.js? [sm]:331 ✅ 流年分析计算完成，共5年
index.js? [sm]:363 🌟 流年计算完成: (5) [{…}, {…}, {…}, {…}, {…}]
celebrity_database_api.js? [sm]:361 ✅ 相似度匹配完成: 找到0位相似名人，平均相似度NaN%
[Perf][pages/bazi-result/index] Page.onLoad took 151ms
app.js? [sm]:56 正在修复路由问题: pages/bazi-result/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/bazi-result/index
index.js? [sm]:751 🏛️ 历史验证统计: {totalCount: 0, averageSimilarity: 0, highSimilarityCount: 0, categories: {…}}
index.js? [sm]:389 🏛️ 历史验证完成: {celebrities: Array(0), stats: {…}}
index.js? [sm]:398 🔍 检查completeResult结构: {completeResult: true, fourPillars: Array(4), fourPillarsLength: 4, fourPillarsType: "object"}
index.js? [sm]:431 ✅ 五行数据更新完成
index.js? [sm]:440 ✅ 神煞数据更新完成
index.js? [sm]:447 ✅ 大运数据更新完成
index.js? [sm]:452 ✅ 流年数据更新完成
index.js? [sm]:459 ✅ 历史验证数据更新完成
index.js? [sm]:464 ✅ 完整八字数据处理完成
index.js? [sm]:465 📊 最终数据状态: {baziInfo: true, fiveElements: true, auspiciousStars: 1, dayunData: 0}
index.js? [sm]:117 ✅ 页面加载完成
[pages/bazi-result/index] [Component] <scroll-view>: 设置 enable-flex 属性以使 flexbox 布局生效
index.js? [sm]:5863 页面跳转成功