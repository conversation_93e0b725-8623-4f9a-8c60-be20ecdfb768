// test_enhanced_pattern_fix.js
// 测试增强格局分析器修复

const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer.js');

/**
 * 测试修复后的格局分析器
 */
function testEnhancedPatternAnalyzer() {
  console.log('🧪 测试增强格局分析器修复');
  console.log('=' * 50);

  const analyzer = new EnhancedPatternAnalyzer();

  // 测试用例：模拟真实的八字数据
  const testBaziData = {
    year_gan: '甲',
    year_zhi: '子',
    month_gan: '丙',
    month_zhi: '寅',
    day_gan: '戊',
    day_zhi: '午',
    hour_gan: '庚',
    hour_zhi: '申',
    baziInfo: {
      yearPillar: { heavenly: '甲', earthly: '子' },
      monthPillar: { heavenly: '丙', earthly: '寅' },
      dayPillar: { heavenly: '戊', earthly: '午' },
      timePillar: { heavenly: '庚', earthly: '申' }
    }
  };

  // 构建四柱数组格式（修复后的格式）
  const fourPillars = [
    // 年柱 [0]
    {
      gan: testBaziData.year_gan,
      zhi: testBaziData.year_zhi,
      heavenly: testBaziData.year_gan,
      earthly: testBaziData.year_zhi
    },
    // 月柱 [1]
    {
      gan: testBaziData.month_gan,
      zhi: testBaziData.month_zhi,
      heavenly: testBaziData.month_gan,
      earthly: testBaziData.month_zhi
    },
    // 日柱 [2]
    {
      gan: testBaziData.day_gan,
      zhi: testBaziData.day_zhi,
      heavenly: testBaziData.day_gan,
      earthly: testBaziData.day_zhi
    },
    // 时柱 [3]
    {
      gan: testBaziData.hour_gan,
      zhi: testBaziData.hour_zhi,
      heavenly: testBaziData.hour_gan,
      earthly: testBaziData.hour_zhi
    }
  ];

  const birthDateTime = new Date(1990, 4, 15); // 1990年5月15日

  console.log('📋 测试数据：');
  console.log(`四柱：${fourPillars[0].gan}${fourPillars[0].zhi} ${fourPillars[1].gan}${fourPillars[1].zhi} ${fourPillars[2].gan}${fourPillars[2].zhi} ${fourPillars[3].gan}${fourPillars[3].zhi}`);
  console.log(`日主：${fourPillars[2].gan}`);
  console.log(`月令：${fourPillars[1].zhi}`);
  console.log('');

  try {
    // 测试格局分析
    console.log('🎯 开始格局分析测试...');
    const patternResult = analyzer.determinePattern(testBaziData, fourPillars, birthDateTime);
    
    console.log('✅ 格局分析成功！');
    console.log('📊 分析结果：');
    console.log(`  格局：${patternResult.pattern || '未确定'}`);
    console.log(`  格局类型：${patternResult.pattern_type || '未确定'}`);
    console.log(`  格局强度：${patternResult.pattern_strength || '未确定'}`);
    
    if (patternResult.element_powers) {
      console.log('  五行力量分布：');
      Object.entries(patternResult.element_powers.percentages || {}).forEach(([element, percentage]) => {
        if (percentage > 0) {
          console.log(`    ${element}: ${percentage.toFixed(1)}%`);
        }
      });
    }

    if (patternResult.ten_gods) {
      console.log('  十神分布：');
      Object.entries(patternResult.ten_gods).forEach(([position, tenGod]) => {
        console.log(`    ${position}: ${tenGod}`);
      });
    }

    console.log(`  分析置信度：${(patternResult.confidence * 100).toFixed(1)}%`);
    console.log(`  分析依据：${patternResult.basis || '传统命理'}`);

    return true;

  } catch (error) {
    console.error('❌ 格局分析失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log('\n🧪 测试边界情况');
  
  const analyzer = new EnhancedPatternAnalyzer();

  // 测试用例1：空数据
  console.log('\n📋 测试用例1：处理空数据');
  try {
    const result1 = analyzer.determinePattern({}, [], new Date());
    console.log('✅ 空数据处理成功');
  } catch (error) {
    console.log('❌ 空数据处理失败:', error.message);
  }

  // 测试用例2：不完整的四柱数据
  console.log('\n📋 测试用例2：不完整的四柱数据');
  try {
    const incompleteFourPillars = [
      { gan: '甲', zhi: '子' },
      { gan: '丙', zhi: '寅' }
      // 缺少日柱和时柱
    ];
    const result2 = analyzer.determinePattern({}, incompleteFourPillars, new Date());
    console.log('✅ 不完整数据处理成功');
  } catch (error) {
    console.log('❌ 不完整数据处理失败:', error.message);
  }

  // 测试用例3：无效的干支数据
  console.log('\n📋 测试用例3：无效的干支数据');
  try {
    const invalidFourPillars = [
      { gan: '无效干', zhi: '无效支' },
      { gan: '丙', zhi: '寅' },
      { gan: '戊', zhi: '午' },
      { gan: '庚', zhi: '申' }
    ];
    const result3 = analyzer.determinePattern({}, invalidFourPillars, new Date());
    console.log('✅ 无效数据处理成功');
  } catch (error) {
    console.log('❌ 无效数据处理失败:', error.message);
  }
}

// 执行测试
console.log('🚀 开始增强格局分析器修复测试');
console.log('测试时间:', new Date().toLocaleString());
console.log('');

const mainTestResult = testEnhancedPatternAnalyzer();
testEdgeCases();

console.log('\n📊 测试总结：');
console.log(`主要功能测试：${mainTestResult ? '✅ 通过' : '❌ 失败'}`);
console.log('边界情况测试：已完成');
console.log('\n🎉 测试完成！');
