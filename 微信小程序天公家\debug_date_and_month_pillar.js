// debug_date_and_month_pillar.js
// 调试日期错误和月柱计算错误

console.log('🚨 调试严重的日期和月柱计算错误...');

// 问题1：日期错误分析
console.log('\n❌ 问题1：日期错误分析');
console.log('用户输入：2024年7月30日');
console.log('日志显示：2024年7月31日');
console.log('问题：输入的日期被错误地改变了！');

console.log('\n🔍 可能的原因：');
console.log('1. 默认日期设置问题（使用了当前日期）');
console.log('2. 日期选择器索引计算错误');
console.log('3. 数据绑定时机问题');
console.log('4. 日期更新逻辑覆盖了用户选择');

// 问题2：月柱计算错误分析
console.log('\n❌ 问题2：月柱计算错误分析');
console.log('日志显示月柱：壬申');
console.log('正确月柱应该是：辛未');
console.log('问题：月柱计算算法有严重错误！');

// 分析2024年7月30日的正确月柱
function analyzeCorrectMonthPillar() {
  console.log('\n🔧 分析2024年7月30日的正确月柱：');
  
  const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
  const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
  
  // 2024年是甲辰年，年干是甲
  const yearGan = '甲';
  console.log('年干：', yearGan);
  
  // 7月30日的节气判断
  console.log('\n节气判断：');
  console.log('- 7月7日小暑 → 未月开始');
  console.log('- 7月23日大暑 → 仍是未月（大暑当日属未月）');
  console.log('- 7月24日大暑后 → 申月开始');
  console.log('- 7月30日 → 应该是申月');
  
  // 等等！这里有问题，让我重新分析
  console.log('\n⚠️ 重新分析节气：');
  console.log('根据传统命理：');
  console.log('- 7月7日小暑后 → 未月');
  console.log('- 7月23日大暑当日 → 仍是未月');
  console.log('- 7月24日大暑后 → 申月');
  console.log('- 所以7月30日应该是申月');
  
  // 五虎遁计算申月
  const wuhuDun = {
    '甲': 2, '己': 2, // 甲己之年丙作首
    '乙': 4, '庚': 4, // 乙庚之年戊为头
    '丙': 6, '辛': 6, // 丙辛之年庚寅上
    '丁': 8, '壬': 8, // 丁壬壬寅顺水流
    '戊': 0, '癸': 0  // 戊癸之年甲寅始
  };
  
  const monthGanStart = wuhuDun[yearGan]; // 甲年起丙寅，索引2
  console.log('甲年起始：', tiangan[monthGanStart], '（索引', monthGanStart, '）');
  
  // 申月是第7个月（寅=1, 卯=2, 辰=3, 巳=4, 午=5, 未=6, 申=7）
  const solarMonth = 7; // 申月
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  const monthZhi = '申';
  
  console.log('\n申月计算：');
  console.log('月干计算：(', monthGanStart, '+', solarMonth, '- 1) % 10 =', monthGanIndex);
  console.log('月干：', monthGan);
  console.log('月支：', monthZhi);
  console.log('申月月柱：', monthGan + monthZhi);
  
  // 但是用户说正确答案是辛未，这意味着7月30日应该是未月！
  console.log('\n🤔 用户说正确答案是辛未，这意味着：');
  console.log('- 7月30日应该是未月，不是申月');
  console.log('- 这说明大暑的边界判断有问题');
  
  // 重新计算未月
  const weiMonth = 6; // 未月
  const weiMonthGanIndex = (monthGanStart + weiMonth - 1) % 10;
  const weiMonthGan = tiangan[weiMonthGanIndex];
  
  console.log('\n未月计算：');
  console.log('月干计算：(', monthGanStart, '+', weiMonth, '- 1) % 10 =', weiMonthGanIndex);
  console.log('月干：', weiMonthGan);
  console.log('月支：未');
  console.log('未月月柱：', weiMonthGan + '未');
  
  if (weiMonthGan + '未' === '辛未') {
    console.log('✅ 正确！7月30日应该是未月，月柱是辛未');
    console.log('❌ 问题：前端错误地判断7月30日为申月');
  }
}

// 分析节气边界问题
function analyzeNodeQiBoundary() {
  console.log('\n🔍 分析节气边界问题：');
  
  console.log('传统命理中大暑的处理：');
  console.log('- 大暑通常在7月22-24日');
  console.log('- 2024年大暑具体时间需要查询');
  console.log('- 如果大暑在7月23日，那么：');
  console.log('  * 7月23日当天：未月');
  console.log('  * 7月24日开始：申月');
  console.log('  * 7月30日：申月');
  
  console.log('\n但用户说7月30日应该是辛未（未月），这说明：');
  console.log('1. 要么2024年大暑时间更晚');
  console.log('2. 要么前端的节气时间数据不准确');
  console.log('3. 要么节气边界判断逻辑有误');
}

// 分析前端代码的问题
function analyzeFrontendIssues() {
  console.log('\n🔧 分析前端代码的问题：');
  
  console.log('\n日期问题可能原因：');
  console.log('1. 默认日期设置使用了当前时间（7月31日）');
  console.log('2. 用户选择7月30日后，某个地方又被重置为默认值');
  console.log('3. 数据更新时机问题，后续操作覆盖了用户选择');
  
  console.log('\n月柱问题可能原因：');
  console.log('1. 节气时间数据不准确');
  console.log('2. 节气边界判断逻辑错误');
  console.log('3. 2024年的大暑时间可能不是7月23日');
  console.log('4. 五虎遁计算可能有误');
}

// 提供修复方案
function provideSolutions() {
  console.log('\n🔧 修复方案：');
  
  console.log('\n修复日期问题：');
  console.log('1. 检查默认日期设置逻辑');
  console.log('2. 确保用户选择后不被覆盖');
  console.log('3. 添加日期选择的调试日志');
  console.log('4. 检查数据更新的时机和顺序');
  
  console.log('\n修复月柱问题：');
  console.log('1. 查询2024年准确的大暑时间');
  console.log('2. 修正节气边界判断逻辑');
  console.log('3. 验证五虎遁计算的正确性');
  console.log('4. 对比权威万年历的结果');
  
  console.log('\n立即行动：');
  console.log('1. 先修复日期问题，确保用户输入的日期不被改变');
  console.log('2. 查询权威资料确定2024年7月30日的正确月柱');
  console.log('3. 修正节气边界判断');
  console.log('4. 全面测试修复效果');
}

// 执行分析
analyzeCorrectMonthPillar();
analyzeNodeQiBoundary();
analyzeFrontendIssues();
provideSolutions();

console.log('\n🚨 严重问题总结：');
console.log('1. ❌ 日期错误：用户输入7月30日，系统显示7月31日');
console.log('2. ❌ 月柱错误：系统计算壬申，正确应该是辛未');
console.log('3. 🔧 根本原因：节气边界判断和数据更新逻辑都有问题');
console.log('4. ⚡ 优先级：立即修复，这影响所有八字计算的准确性');

console.log('\n🏁 调试完成，需要立即修复这两个严重bug！');
