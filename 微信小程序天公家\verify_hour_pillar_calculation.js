// verify_hour_pillar_calculation.js
// 验证时柱计算的正确性

console.log('🔍 验证时柱计算的正确性');
console.log('='.repeat(60));

// 基础数据
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 测试数据：2021年6月24日19:30，日柱癸卯
const testData = {
  year: 2021,
  month: 6,
  day: 24,
  hour: 19,
  minute: 30,
  dayGan: '癸',
  dayZhi: '卯'
};

console.log('\n📋 测试数据:');
console.log(`日期: ${testData.year}年${testData.month}月${testData.day}日`);
console.log(`时间: ${testData.hour}:${testData.minute}`);
console.log(`日柱: ${testData.dayGan}${testData.dayZhi}`);

// 第一步：确定时支
function getHourZhi(hour, minute = 0) {
  const timeDecimal = hour + minute / 60.0;
  
  // 时辰表
  const hourTable = [
    { start: 23, end: 1, zhi: '子' },
    { start: 1, end: 3, zhi: '丑' },
    { start: 3, end: 5, zhi: '寅' },
    { start: 5, end: 7, zhi: '卯' },
    { start: 7, end: 9, zhi: '辰' },
    { start: 9, end: 11, zhi: '巳' },
    { start: 11, end: 13, zhi: '午' },
    { start: 13, end: 15, zhi: '未' },
    { start: 15, end: 17, zhi: '申' },
    { start: 17, end: 19, zhi: '酉' },
    { start: 19, end: 21, zhi: '戌' },
    { start: 21, end: 23, zhi: '亥' }
  ];
  
  for (const { start, end, zhi } of hourTable) {
    if (start < end) {
      // 正常时段
      if (timeDecimal >= start && timeDecimal < end) {
        return zhi;
      }
    } else {
      // 跨日时段（子时）
      if (timeDecimal >= start || timeDecimal < end) {
        return zhi;
      }
    }
  }
  
  return '子'; // 默认
}

const hourZhi = getHourZhi(testData.hour, testData.minute);
console.log(`\n🕐 时支判断: ${testData.hour}:${testData.minute} → ${hourZhi}时`);

// 第二步：五鼠遁计算时干
function calculateHourGan(dayGan, hourZhi) {
  // 五鼠遁表（日上起时）
  const wushuDunTable = {
    '甲': '甲', '己': '甲',  // 甲己还加甲
    '乙': '丙', '庚': '丙',  // 乙庚丙作初
    '丙': '戊', '辛': '戊',  // 丙辛从戊起
    '丁': '庚', '壬': '庚',  // 丁壬庚子居
    '戊': '壬', '癸': '壬'   // 戊癸何方发，壬子是真途
  };
  
  const baseGan = wushuDunTable[dayGan];
  const baseGanIndex = tiangan.indexOf(baseGan);
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
  const hourGan = tiangan[hourGanIndex];
  
  console.log('\n🔧 五鼠遁计算过程:');
  console.log(`日干: ${dayGan}`);
  console.log(`起始天干: ${baseGan} (${dayGan}日${hourZhi}时从${baseGan}开始)`);
  console.log(`基础天干索引: ${baseGanIndex}`);
  console.log(`时支: ${hourZhi}`);
  console.log(`时支索引: ${hourZhiIndex}`);
  console.log(`时干索引: (${baseGanIndex} + ${hourZhiIndex}) % 10 = ${hourGanIndex}`);
  console.log(`时干: ${hourGan}`);
  
  return hourGan;
}

const hourGan = calculateHourGan(testData.dayGan, hourZhi);
const hourPillar = hourGan + hourZhi;

console.log(`\n✅ 计算结果: ${hourPillar}`);

// 第三步：验证五鼠遁序列
function verifyWushuDunSequence(dayGan) {
  const wushuDunTable = {
    '甲': '甲', '己': '甲',  // 甲己还加甲
    '乙': '丙', '庚': '丙',  // 乙庚丙作初
    '丙': '戊', '辛': '戊',  // 丙辛从戊起
    '丁': '庚', '壬': '庚',  // 丁壬庚子居
    '戊': '壬', '癸': '壬'   // 戊癸何方发，壬子是真途
  };
  
  const baseGan = wushuDunTable[dayGan];
  const baseGanIndex = tiangan.indexOf(baseGan);
  
  console.log(`\n📋 ${dayGan}日十二时辰序列:`);
  
  for (let i = 0; i < 12; i++) {
    const zhi = dizhi[i];
    const ganIndex = (baseGanIndex + i) % 10;
    const gan = tiangan[ganIndex];
    const pillar = gan + zhi;
    
    if (zhi === hourZhi) {
      console.log(`${zhi}时: ${pillar} ← 当前时辰`);
    } else {
      console.log(`${zhi}时: ${pillar}`);
    }
  }
}

verifyWushuDunSequence(testData.dayGan);

// 第四步：对比其他权威算法
console.log('\n🔍 对比验证:');
console.log('根据传统八字理论:');
console.log('1. 19:30属于戌时（19:00-21:00）✅');
console.log('2. 癸日起时用"戊癸何方发，壬子是真途"✅');
console.log('3. 癸日戌时应该是壬戌✅');

console.log(`\n🎯 最终结论: ${testData.year}年${testData.month}月${testData.day}日${testData.hour}:${testData.minute}的时柱是 ${hourPillar}`);

// 第五步：检查是否与日志一致
console.log('\n📊 与系统日志对比:');
console.log('系统计算结果: 壬戌');
console.log('理论计算结果: ' + hourPillar);
console.log('结果一致性: ' + (hourPillar === '壬戌' ? '✅ 一致' : '❌ 不一致'));
