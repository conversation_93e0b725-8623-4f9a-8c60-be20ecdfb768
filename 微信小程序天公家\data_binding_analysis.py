#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def analyze_data_binding_mismatch():
    """分析WXML文件中的数据绑定与JavaScript数据结构的匹配情况"""
    
    print("🔍 数据绑定匹配性分析")
    print("=" * 50)
    
    # 读取WXML文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        wxml_content = f.read()
    
    # 提取所有数据绑定表达式
    bindings = re.findall(r'\{\{([^}]+)\}\}', wxml_content)
    
    print(f"📊 找到 {len(bindings)} 个数据绑定表达式")
    
    # 分析数据绑定的类型
    binding_analysis = {
        'unifiedData': [],
        'baziData': [],
        'userInfo': [],
        'birthInfo': [],
        'baziInfo': [],
        'professionalAnalysis': [],
        'timingAnalysis': [],
        'liuqinAnalysis': [],
        'classicalAnalysis': [],
        'pageState': [],
        'currentTab': [],
        'item': [],
        'other': []
    }
    
    # 根据JavaScript分析，正确的数据结构应该是：
    correct_mappings = {
        # 基本数据结构
        'unifiedData': 'baziData',  # unifiedData在JS中实际存储为baziData
        'userInfo': 'baziData.userInfo',  # 用户信息
        'birthInfo': 'baziData.birthInfo',  # 出生信息
        'baziInfo': 'baziData.baziInfo',  # 八字信息
        
        # 四柱数据 - 应该使用baziData中的字段
        'year_pillar': 'baziData.baziInfo.yearPillar',
        'month_pillar': 'baziData.baziInfo.monthPillar', 
        'day_pillar': 'baziData.baziInfo.dayPillar',
        'hour_pillar': 'baziData.baziInfo.timePillar',
        
        # 直接字段映射
        'year_gan': 'baziData.year_gan',
        'month_gan': 'baziData.month_gan',
        'day_gan': 'baziData.day_gan',
        'hour_gan': 'baziData.hour_gan',
        'year_zhi': 'baziData.year_zhi',
        'month_zhi': 'baziData.month_zhi',
        'day_zhi': 'baziData.day_zhi',
        'hour_zhi': 'baziData.hour_zhi',
        
        # 分析数据
        'professionalAnalysis': 'professionalAnalysis',
        'timingAnalysis': 'timingAnalysis',
        'liuqinAnalysis': 'liuqinAnalysis',
        'classicalAnalysis': 'classicalAnalysis',
        
        # 状态数据
        'pageState': 'pageState',
        'currentTab': 'currentTab'
    }
    
    # 分类数据绑定
    for binding in bindings:
        binding = binding.strip()
        
        if binding.startswith('unifiedData.'):
            binding_analysis['unifiedData'].append(binding)
        elif binding.startswith('baziData.'):
            binding_analysis['baziData'].append(binding)
        elif binding.startswith('userInfo.'):
            binding_analysis['userInfo'].append(binding)
        elif binding.startswith('birthInfo.'):
            binding_analysis['birthInfo'].append(binding)
        elif binding.startswith('baziInfo.'):
            binding_analysis['baziInfo'].append(binding)
        elif binding.startswith('professionalAnalysis.'):
            binding_analysis['professionalAnalysis'].append(binding)
        elif binding.startswith('timingAnalysis.'):
            binding_analysis['timingAnalysis'].append(binding)
        elif binding.startswith('liuqinAnalysis.'):
            binding_analysis['liuqinAnalysis'].append(binding)
        elif binding.startswith('classicalAnalysis.'):
            binding_analysis['classicalAnalysis'].append(binding)
        elif binding.startswith('pageState.'):
            binding_analysis['pageState'].append(binding)
        elif binding == 'currentTab':
            binding_analysis['currentTab'].append(binding)
        elif binding.startswith('item.'):
            binding_analysis['item'].append(binding)
        else:
            binding_analysis['other'].append(binding)
    
    # 输出分析结果
    print("\n📋 数据绑定分类统计:")
    for category, bindings_list in binding_analysis.items():
        if bindings_list:
            print(f"  {category}: {len(bindings_list)} 个")
            if len(bindings_list) <= 5:
                for binding in bindings_list:
                    print(f"    - {binding}")
            else:
                for binding in bindings_list[:3]:
                    print(f"    - {binding}")
                print(f"    ... 还有 {len(bindings_list) - 3} 个")
    
    # 识别问题绑定
    print("\n❌ 可能存在问题的数据绑定:")
    
    problem_bindings = []
    
    # 检查unifiedData绑定 - 这些应该改为baziData
    for binding in binding_analysis['unifiedData']:
        problem_bindings.append({
            'current': binding,
            'suggested': binding.replace('unifiedData.', 'baziData.'),
            'reason': 'unifiedData在JS中实际存储为baziData'
        })
    
    # 检查直接的userInfo/birthInfo/baziInfo绑定
    for binding in binding_analysis['userInfo']:
        if not binding.startswith('userInfo.'):
            continue
        problem_bindings.append({
            'current': binding,
            'suggested': binding.replace('userInfo.', 'baziData.userInfo.'),
            'reason': 'userInfo应该通过baziData访问'
        })
    
    for binding in binding_analysis['birthInfo']:
        if not binding.startswith('birthInfo.'):
            continue
        problem_bindings.append({
            'current': binding,
            'suggested': binding.replace('birthInfo.', 'baziData.birthInfo.'),
            'reason': 'birthInfo应该通过baziData访问'
        })
    
    for binding in binding_analysis['baziInfo']:
        if not binding.startswith('baziInfo.'):
            continue
        problem_bindings.append({
            'current': binding,
            'suggested': binding.replace('baziInfo.', 'baziData.baziInfo.'),
            'reason': 'baziInfo应该通过baziData访问'
        })
    
    # 输出问题绑定
    for i, problem in enumerate(problem_bindings[:10], 1):
        print(f"  {i}. {problem['current']}")
        print(f"     建议: {problem['suggested']}")
        print(f"     原因: {problem['reason']}")
        print()
    
    if len(problem_bindings) > 10:
        print(f"  ... 还有 {len(problem_bindings) - 10} 个问题绑定")
    
    print(f"\n📊 总计发现 {len(problem_bindings)} 个可能的问题绑定")
    
    return {
        'total_bindings': len(bindings),
        'problem_bindings': problem_bindings,
        'binding_analysis': binding_analysis
    }

if __name__ == "__main__":
    result = analyze_data_binding_mismatch()
    
    print(f"\n🎯 修复建议:")
    print(f"1. 将所有 unifiedData.xxx 改为 baziData.xxx")
    print(f"2. 将直接的 userInfo.xxx 改为 baziData.userInfo.xxx")
    print(f"3. 将直接的 birthInfo.xxx 改为 baziData.birthInfo.xxx")
    print(f"4. 将直接的 baziInfo.xxx 改为 baziData.baziInfo.xxx")
    print(f"5. 保持 professionalAnalysis、timingAnalysis、liuqinAnalysis 等分析数据不变")
    print(f"6. 保持 pageState、currentTab 等状态数据不变")
