// debug_data_flow.js
// 调试数据流向问题

console.log('🔍 开始调试数据流向问题...');

// 模拟检查本地存储数据
function checkStorageData() {
  console.log('\n📦 检查本地存储数据:');
  
  // 模拟从本地存储获取的数据
  const mockStorageData = {
    bazi_frontend_result: {
      // 这里应该是真实的八字计算结果
      bazi: {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        hour: { gan: '庚', zhi: '申' }
      },
      formatted: {
        year: '甲子',
        month: '丙寅', 
        day: '戊午',
        hour: '庚申'
      },
      nayin: {
        year: '海中金',
        month: '炉中火',
        day: '天上火',
        hour: '石榴木'
      },
      source: 'frontend_calculation',
      timestamp: new Date().toISOString()
    },
    bazi_birth_info: {
      name: '张三',
      gender: '男',
      year: 1990,
      month: 7,
      day: 21,
      hour: 18,
      minute: 2,
      birthCity: '北京'
    }
  };
  
  console.log('✅ 前端计算结果:', mockStorageData.bazi_frontend_result);
  console.log('✅ 出生信息:', mockStorageData.bazi_birth_info);
  
  return mockStorageData;
}

// 检查数据格式匹配问题
function checkDataFormatMismatch() {
  console.log('\n🔍 检查数据格式匹配问题:');
  
  const storageData = checkStorageData();
  
  // 检查结果页面期望的数据格式
  const expectedFormat = {
    userInfo: {
      name: '用户姓名',
      gender: '性别',
      birthDate: '出生日期',
      birthTime: '出生时间',
      location: '出生地点'
    },
    baziInfo: {
      yearPillar: { heavenly: '天干', earthly: '地支', nayin: '纳音' },
      monthPillar: { heavenly: '天干', earthly: '地支', nayin: '纳音' },
      dayPillar: { heavenly: '天干', earthly: '地支', nayin: '纳音' },
      timePillar: { heavenly: '天干', earthly: '地支', nayin: '纳音' }
    }
  };
  
  console.log('📋 结果页面期望格式:', expectedFormat);
  
  // 检查格式不匹配
  const frontendData = storageData.bazi_frontend_result;
  const birthInfo = storageData.bazi_birth_info;
  
  console.log('\n❌ 发现格式不匹配问题:');
  console.log('1. 前端数据使用 gan/zhi 格式，结果页面期望 heavenly/earthly');
  console.log('2. 出生信息格式不匹配，缺少 birthDate/birthTime 等字段');
  console.log('3. 数据结构层级不一致');
  
  return { frontendData, birthInfo, expectedFormat };
}

// 检查测试数据覆盖问题
function checkTestDataOverride() {
  console.log('\n⚠️ 检查测试数据覆盖问题:');
  
  // 模拟结果页面的数据加载逻辑
  const options = { id: 'test_123' }; // 模拟页面参数
  
  console.log('1. 检查URL参数:', options.data ? '有数据' : '无数据');
  console.log('2. 检查本地存储ID:', options.id ? '有ID' : '无ID');
  
  // 模拟本地存储检查
  const frontendResult = true; // 假设有前端结果
  const birthInfo = true; // 假设有出生信息
  
  if (frontendResult && birthInfo) {
    console.log('✅ 本地存储有数据，应该使用真实数据');
  } else {
    console.log('❌ 本地存储无数据，会使用测试数据');
  }
  
  // 检查数据格式转换问题
  console.log('\n🔧 数据格式转换问题:');
  console.log('- 前端计算结果格式与结果页面期望格式不匹配');
  console.log('- 需要添加数据格式转换逻辑');
  console.log('- 需要统一数据结构定义');
}

// 提供解决方案
function provideSolution() {
  console.log('\n🔧 解决方案:');
  
  console.log('1. 修复数据格式转换:');
  console.log('   - 在 loadFromStorage 中添加格式转换逻辑');
  console.log('   - 将前端 gan/zhi 格式转换为 heavenly/earthly 格式');
  
  console.log('2. 修复出生信息格式:');
  console.log('   - 将数字格式的日期时间转换为字符串格式');
  console.log('   - 添加缺失的字段映射');
  
  console.log('3. 优化数据加载优先级:');
  console.log('   - 确保真实数据优先于测试数据');
  console.log('   - 添加数据有效性检查');
  
  console.log('4. 增强调试信息:');
  console.log('   - 添加详细的数据来源日志');
  console.log('   - 显示数据转换过程');
}

// 执行调试
checkStorageData();
checkDataFormatMismatch();
checkTestDataOverride();
provideSolution();

console.log('\n🏁 数据流向调试完成');
console.log('💡 主要问题：数据格式不匹配导致真实数据无法正确显示');
console.log('🔧 解决方向：添加数据格式转换逻辑，统一数据结构');
