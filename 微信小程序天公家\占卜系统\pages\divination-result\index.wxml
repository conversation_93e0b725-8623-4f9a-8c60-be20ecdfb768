<!--pages/divination-result/index.wxml-->
<scroll-view class="scroll-container {{themeClass}}" scroll-y="{{true}}" enhanced="{{false}}" show-scrollbar="{{false}}" bounces="{{false}}" scroll-with-animation="{{false}}" bindscroll="onScroll" enable-flex="true">
  <view class="container">
  <!-- 背景装饰 -->
  <view class="background-decoration"></view>
  
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="master-info">
      <image class="master-avatar" src="/assets/icons/tiangong-shifu.png"></image>
      <view class="master-text">
        <text class="master-name">天工师父</text>
        <text class="master-subtitle">为您解卦</text>
      </view>
    </view>
  </view>

  <!-- 六神结果展示 -->
  <view class="god-result-section">
    <view class="god-card {{result.god.isAuspicious ? 'auspicious' : 'inauspicious'}} {{result.god.fortuneLevel}}"
          style="border-color: {{result.god.color}}">
      <view class="god-header">
        <view class="god-icon {{result.god.isAuspicious ? 'auspicious-icon' : 'inauspicious-icon'}}"
              style="background: {{result.god.color}}">
          <text class="god-name">{{result.god.name}}</text>
        </view>
        <view class="god-info">
          <text class="god-element">{{result.god.element}}行 · {{result.god.beast}}</text>
          <text class="god-fortune {{result.god.isAuspicious ? 'fortune-auspicious' : 'fortune-inauspicious'}}"
                style="color: {{result.god.color}}">{{result.god.fortune}}</text>
        </view>
      </view>
      
      <view class="god-description">
        <text class="description-text">{{analysis.overall.description}}</text>
        <text class="source-text">——《玉匣记》</text>
      </view>
      
      <view class="god-advice">
        <text class="advice-label">天公指引：</text>
        <text class="advice-text">{{analysis.specific.title}}</text>
      </view>
    </view>
  </view>

  <!-- 问题回顾 -->
  <view class="question-review">
    <view class="section-title">
      <text>您的问题</text>
    </view>
    <view class="question-content">
      <text class="question-type">{{questionTypeText}}</text>
      <text class="question-text">{{result.questionText}}</text>
    </view>
  </view>

  <!-- 详细分析 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <!-- 针对性建议 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>🎯 针对性指导</text>
      </view>
      <view class="card-content">
        <text class="specific-advice">{{analysis.specific.title}}</text>
        <view class="advice-details" wx:if="{{analysis.specific.details && analysis.specific.details.length > 0}}">
          <text wx:for="{{analysis.specific.details}}" wx:key="index" class="detail-item">
            • {{item}}
          </text>
        </view>
        <view class="action-items" wx:if="{{analysis.specific.actionItems && analysis.specific.actionItems.length > 0}}">
          <text class="action-title">具体行动建议：</text>
          <text wx:for="{{analysis.specific.actionItems}}" wx:key="index" class="action-item">
            ✓ {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 应期时间线 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>📅 应期时间</text>
      </view>
      <view class="timeline-content">
        <view class="timeline-item" wx:for="{{analysis.timeline}}" wx:key="day">
          <view class="timeline-dot"></view>
          <view class="timeline-info">
            <text class="timeline-day">第{{item.day}}日</text>
            <text class="timeline-date">{{item.date}}</text>
            <text class="timeline-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 化解建议（凶卦专用） -->
    <view class="analysis-card" wx:if="{{!result.god.isAuspicious && result.god.resolution}}">
      <view class="card-title">
        <text>🙏 化解建议</text>
      </view>
      <view class="resolution-content">
        <view class="resolution-section">
          <text class="resolution-subtitle">化解方法：</text>
          <view class="resolution-methods">
            <view class="method-item" wx:for="{{result.god.resolution.methods}}" wx:key="*this">
              <text class="method-text">• {{item}}</text>
            </view>
          </view>
        </view>

        <view class="resolution-section">
          <text class="resolution-subtitle">注意事项：</text>
          <view class="resolution-taboos">
            <view class="taboo-item" wx:for="{{result.god.resolution.taboos}}" wx:key="*this">
              <text class="taboo-text">⚠️ {{item}}</text>
            </view>
          </view>
        </view>

        <view class="resolution-timing">
          <text class="timing-text">⏰ {{result.god.resolution.timing}}</text>
        </view>
      </view>
    </view>

    <!-- 注意事项 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>⚠️ 注意事项</text>
      </view>
      <view class="card-content">
        <view class="precaution-list">
          <text wx:for="{{analysis.precautions}}" wx:key="index" class="precaution-item">
            • {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 化解方法 -->
    <view class="analysis-card">
      <view class="card-title">
        <text>🙏 化解方法</text>
      </view>
      <view class="card-content">
        <view class="ritual-list">
          <text wx:for="{{analysis.rituals}}" wx:key="index" class="ritual-item">
            • {{item}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 占卜过程（可展开） -->
  <view class="calculation-section" wx:if="{{showCalculation}}">
    <view class="section-title" bindtap="toggleCalculation">
      <text>🔍 占卜过程</text>
      <text class="toggle-icon">{{showCalculationDetail ? '▼' : '▶'}}</text>
    </view>
    
    <view class="calculation-detail" wx:if="{{showCalculationDetail}}">
      <view wx:if="{{result.calculation.method === 'time'}}" class="time-calculation">
        <text class="calc-title">天公师兄占卜过程：</text>
        <text class="calc-step">公历时间：{{result.calculation.solarTime}}</text>
        <text class="calc-step">真太阳时：{{result.calculation.trueSolarTime}}</text>
        <text class="calc-step">农历时间：{{result.calculation.lunarTime}}</text>
        <text class="calc-step">当前时辰：{{result.calculation.shichen}}</text>
        <text class="calc-divider">--- 起卦过程 ---</text>
        <text class="calc-step">1. 月份定位：农历{{result.calculation.lunarMonth}}月 → 第{{result.calculation.monthPosition}}位</text>
        <text class="calc-step">2. 日期推算：农历{{result.calculation.lunarDay}}日 → 第{{result.calculation.dayPosition}}位</text>
        <text class="calc-step">3. 时辰确定：{{result.calculation.shichen}} → 第{{result.calculation.finalPosition}}位</text>
        <text class="calc-result">最终结果：{{result.god.name}}</text>
        <text wx:if="{{result.calculation.approximate}}" class="calc-warning">⚠️ 农历时间为近似值，建议使用专业农历查询</text>
      </view>
      
      <view wx:if="{{result.calculation.method === 'number'}}" class="number-calculation">
        <text class="calc-step">输入数字：{{result.calculation.numbers.join(' + ')}}</text>
        <text class="calc-step">数字之和：{{result.calculation.sum}}</text>
        <text class="calc-step">取余计算：{{result.calculation.sum}} ÷ 6 = 余{{result.calculation.position}}</text>
        <text class="calc-result">对应六神：{{result.god.name}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="action-btn tertiary" bindtap="goHome">
        <text>返回首页</text>
      </button>
      <button class="action-btn secondary" bindtap="shareResult">
        <text>分享结果</text>
      </button>
      <button class="action-btn primary" bindtap="newDivination">
        <text>再次占卜</text>
      </button>
    </view>
    
    <view class="divination-note">
      <text>💡 同一问题一日内不宜重复占卜</text>
    </view>
  </view>

  <!-- 分享遮罩 -->
  <view class="share-overlay" wx:if="{{showShareModal}}" bindtap="hideShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text>分享占卜结果</text>
        <text class="close-btn" bindtap="hideShareModal">✕</text>
      </view>
      <view class="share-options">
        <view class="share-option" bindtap="shareToFriend">
          <text class="share-icon">👥</text>
          <text>分享给朋友</text>
        </view>
        <view class="share-option" bindtap="saveToAlbum">
          <text class="share-icon">📱</text>
          <text>保存到相册</text>
        </view>
      </view>
    </view>
  </view>
  </view>
</scroll-view>

<!-- 隐藏的Canvas用于海报生成 -->
<canvas id="poster-canvas" type="2d" style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"></canvas>
