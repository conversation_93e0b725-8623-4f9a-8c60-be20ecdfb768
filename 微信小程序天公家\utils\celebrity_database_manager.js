/**
 * 历史名人数据库管理器
 * 提供数据查询、验证、分析等功能
 */

const historicalCelebritiesDatabase = require('../data/historical_celebrities_database.js');

class CelebrityDatabaseManager {
  constructor() {
    this.database = historicalCelebritiesDatabase;
    this.celebrities = this.database.celebrities;
  }

  /**
   * 根据姓名查询名人信息
   */
  findByName(name) {
    return this.celebrities.find(celebrity => 
      celebrity.basicInfo.name === name || 
      celebrity.basicInfo.courtesy === name ||
      celebrity.basicInfo.nickname === name
    );
  }

  /**
   * 根据格局类型查询名人
   */
  findByPattern(pattern) {
    return this.celebrities.filter(celebrity => 
      celebrity.pattern.mainPattern === pattern ||
      celebrity.pattern.subPattern === pattern
    );
  }

  /**
   * 根据朝代查询名人
   */
  findByDynasty(dynasty) {
    return this.celebrities.filter(celebrity => 
      celebrity.basicInfo.dynasty.includes(dynasty)
    );
  }

  /**
   * 根据验证度查询高质量数据
   */
  findByVerificationScore(minScore = 0.9) {
    return this.celebrities.filter(celebrity => 
      celebrity.verification.algorithmMatch >= minScore
    );
  }

  /**
   * 根据职业查询名人
   */
  findByOccupation(occupation) {
    return this.celebrities.filter(celebrity => 
      celebrity.basicInfo.occupation.includes(occupation)
    );
  }

  /**
   * 根据出生地查询名人
   */
  findByBirthplace(province, city = null) {
    return this.celebrities.filter(celebrity => {
      const birthplace = celebrity.basicInfo.birthplace;
      if (city) {
        return birthplace.province === province && birthplace.city === city;
      }
      return birthplace.province === province;
    });
  }

  /**
   * 根据八字查询相似命理的名人
   */
  findBySimilarBazi(targetBazi) {
    const results = [];
    
    this.celebrities.forEach(celebrity => {
      const similarity = this.calculateBaziSimilarity(targetBazi, celebrity.bazi);
      if (similarity >= 0.5) {
        results.push({
          celebrity: celebrity,
          similarity: similarity,
          matchingElements: this.getBaziMatchingElements(targetBazi, celebrity.bazi)
        });
      }
    });

    return results.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * 计算八字相似度
   */
  calculateBaziSimilarity(bazi1, bazi2) {
    let matches = 0;
    const positions = ['year', 'month', 'day', 'hour'];
    
    positions.forEach(pos => {
      if (bazi1[pos].gan === bazi2[pos].gan) matches += 0.5;
      if (bazi1[pos].zhi === bazi2[pos].zhi) matches += 0.5;
    });
    
    return matches / 4; // 总共8个位置，每个0.5分
  }

  /**
   * 获取八字匹配元素
   */
  getBaziMatchingElements(bazi1, bazi2) {
    const matches = [];
    const positions = ['year', 'month', 'day', 'hour'];
    
    positions.forEach(pos => {
      if (bazi1[pos].gan === bazi2[pos].gan) {
        matches.push(`${pos}_gan: ${bazi1[pos].gan}`);
      }
      if (bazi1[pos].zhi === bazi2[pos].zhi) {
        matches.push(`${pos}_zhi: ${bazi1[pos].zhi}`);
      }
    });
    
    return matches;
  }

  /**
   * 获取格局统计信息
   */
  getPatternStatistics() {
    const patternCount = {};
    const dynastyCount = {};
    const occupationCount = {};
    
    this.celebrities.forEach(celebrity => {
      // 格局统计
      const pattern = celebrity.pattern.mainPattern;
      patternCount[pattern] = (patternCount[pattern] || 0) + 1;
      
      // 朝代统计
      const dynasty = celebrity.basicInfo.dynasty;
      dynastyCount[dynasty] = (dynastyCount[dynasty] || 0) + 1;
      
      // 职业统计
      celebrity.basicInfo.occupation.forEach(occ => {
        occupationCount[occ] = (occupationCount[occ] || 0) + 1;
      });
    });

    return {
      totalCelebrities: this.celebrities.length,
      patternDistribution: patternCount,
      dynastyDistribution: dynastyCount,
      occupationDistribution: occupationCount,
      averageVerificationScore: this.getAverageVerificationScore()
    };
  }

  /**
   * 获取平均验证分数
   */
  getAverageVerificationScore() {
    const totalScore = this.celebrities.reduce((sum, celebrity) => 
      sum + celebrity.verification.algorithmMatch, 0
    );
    return (totalScore / this.celebrities.length).toFixed(3);
  }

  /**
   * 验证新的名人数据
   */
  validateCelebrityData(celebrityData) {
    const errors = [];
    
    // 检查必需字段
    if (!celebrityData.basicInfo?.name) {
      errors.push('缺少姓名信息');
    }
    
    if (!celebrityData.bazi?.fullBazi) {
      errors.push('缺少完整八字信息');
    }
    
    if (!celebrityData.pattern?.mainPattern) {
      errors.push('缺少主要格局信息');
    }
    
    if (!celebrityData.verification?.algorithmMatch) {
      errors.push('缺少算法匹配度');
    }
    
    // 检查验证分数范围
    if (celebrityData.verification?.algorithmMatch < 0 || 
        celebrityData.verification?.algorithmMatch > 1) {
      errors.push('算法匹配度必须在0-1之间');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 添加新的名人数据
   */
  addCelebrity(celebrityData) {
    const validation = this.validateCelebrityData(celebrityData);
    
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
    }
    
    // 检查是否已存在
    const existing = this.findByName(celebrityData.basicInfo.name);
    if (existing) {
      throw new Error(`名人 ${celebrityData.basicInfo.name} 已存在于数据库中`);
    }
    
    // 生成ID
    celebrityData.id = this.generateCelebrityId(celebrityData.basicInfo.name);
    
    // 添加到数据库
    this.celebrities.push(celebrityData);
    
    return celebrityData.id;
  }

  /**
   * 生成名人ID
   */
  generateCelebrityId(name) {
    const pinyin = this.convertToPinyin(name);
    const timestamp = Date.now().toString().slice(-3);
    return `${pinyin}_${timestamp}`;
  }

  /**
   * 简单的汉字转拼音（示例实现）
   */
  convertToPinyin(name) {
    // 这里应该使用专业的汉字转拼音库
    // 暂时使用简化版本
    const pinyinMap = {
      '曾': 'zeng', '国': 'guo', '藩': 'fan',
      '李': 'li', '白': 'bai', '鸿': 'hong', '章': 'zhang',
      '诸': 'zhu', '葛': 'ge', '亮': 'liang',
      '苏': 'su', '轼': 'shi',
      '王': 'wang', '阳': 'yang', '明': 'ming',
      '郑': 'zheng', '和': 'he'
    };
    
    return name.split('').map(char => pinyinMap[char] || char).join('');
  }

  /**
   * 获取数据库元信息
   */
  getMetadata() {
    return this.database.metadata;
  }

  /**
   * 导出数据库为JSON
   */
  exportToJSON() {
    return JSON.stringify(this.database, null, 2);
  }

  /**
   * 搜索功能（模糊匹配）
   */
  search(keyword) {
    const results = [];
    
    this.celebrities.forEach(celebrity => {
      let score = 0;
      const info = celebrity.basicInfo;
      
      // 姓名匹配
      if (info.name.includes(keyword)) score += 10;
      if (info.courtesy?.includes(keyword)) score += 8;
      if (info.nickname?.includes(keyword)) score += 6;
      
      // 朝代匹配
      if (info.dynasty.includes(keyword)) score += 5;
      
      // 职业匹配
      if (info.occupation.some(occ => occ.includes(keyword))) score += 4;
      
      // 地点匹配
      if (info.birthplace.province.includes(keyword) || 
          info.birthplace.city.includes(keyword)) score += 3;
      
      if (score > 0) {
        results.push({
          celebrity: celebrity,
          relevanceScore: score
        });
      }
    });
    
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }
}

module.exports = CelebrityDatabaseManager;
