/**
 * 专业级五行计算引擎 - 错误处理机制完善
 * 完善输入验证、异常处理和错误恢复机制，确保系统稳定性
 */

const ProfessionalWuxingEngine = require('./utils/professional_wuxing_engine.js');

class ErrorHandlingEnhancement {
  constructor() {
    this.engine = new ProfessionalWuxingEngine();
    this.testResults = [];
  }

  // 输入验证测试用例
  getInputValidationTestCases() {
    return [
      {
        name: '空输入测试',
        input: null,
        expectedError: '输入不能为空'
      },
      {
        name: '未定义输入测试',
        input: undefined,
        expectedError: '输入不能为空'
      },
      {
        name: '非数组输入测试',
        input: '甲子乙丑丙寅丁卯',
        expectedError: '输入必须是数组格式'
      },
      {
        name: '四柱数量不足测试',
        input: [{ gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' }],
        expectedError: '必须提供完整的四柱八字'
      },
      {
        name: '四柱数量过多测试',
        input: [
          { gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '卯' },
          { gan: '戊', zhi: '辰' }
        ],
        expectedError: '必须提供完整的四柱八字'
      },
      {
        name: '无效天干测试',
        input: [
          { gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '无效', zhi: '卯' }
        ],
        expectedError: '包含无效的天干'
      },
      {
        name: '无效地支测试',
        input: [
          { gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '无效' }
        ],
        expectedError: '包含无效的地支'
      },
      {
        name: '缺少天干测试',
        input: [
          { zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '卯' }
        ],
        expectedError: '每个柱都必须包含天干和地支'
      },
      {
        name: '缺少地支测试',
        input: [
          { gan: '甲', zhi: '子' }, { gan: '乙' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '卯' }
        ],
        expectedError: '每个柱都必须包含天干和地支'
      }
    ];
  }

  // 边界条件测试用例
  getBoundaryTestCases() {
    return [
      {
        name: '极端配置测试1 - 全金',
        input: [
          { gan: '庚', zhi: '申' }, { gan: '辛', zhi: '酉' },
          { gan: '庚', zhi: '申' }, { gan: '辛', zhi: '酉' }
        ],
        description: '测试单一五行极端配置'
      },
      {
        name: '极端配置测试2 - 全水',
        input: [
          { gan: '壬', zhi: '子' }, { gan: '癸', zhi: '亥' },
          { gan: '壬', zhi: '子' }, { gan: '癸', zhi: '亥' }
        ],
        description: '测试单一五行极端配置'
      },
      {
        name: '特殊地支组合测试',
        input: [
          { gan: '甲', zhi: '辰' }, { gan: '乙', zhi: '戌' },
          { gan: '丙', zhi: '丑' }, { gan: '丁', zhi: '未' }
        ],
        description: '测试四库全的特殊组合'
      }
    ];
  }

  // 执行输入验证测试
  performInputValidationTest(testCase) {
    console.log(`\n🧪 执行输入验证测试: ${testCase.name}`);
    
    try {
      const result = this.engine.generateDetailedReport(testCase.input);
      
      // 如果没有抛出异常，说明验证失败
      return {
        testCase: testCase.name,
        success: false,
        error: '应该抛出异常但没有抛出',
        expected: testCase.expectedError,
        actual: '无异常'
      };
      
    } catch (error) {
      const errorMessage = error.message;
      const isExpectedError = errorMessage.includes(testCase.expectedError) || 
                             testCase.expectedError.includes(errorMessage);
      
      if (isExpectedError) {
        console.log(`✅ 正确捕获预期错误: ${errorMessage}`);
        return {
          testCase: testCase.name,
          success: true,
          expected: testCase.expectedError,
          actual: errorMessage
        };
      } else {
        console.log(`❌ 捕获了错误但不是预期的: ${errorMessage}`);
        return {
          testCase: testCase.name,
          success: false,
          expected: testCase.expectedError,
          actual: errorMessage
        };
      }
    }
  }

  // 执行边界条件测试
  performBoundaryTest(testCase) {
    console.log(`\n🧪 执行边界条件测试: ${testCase.name}`);
    console.log(`📋 描述: ${testCase.description}`);
    
    try {
      const result = this.engine.generateDetailedReport(testCase.input);
      
      // 检查结果的合理性
      const powers = result.results.finalPowers;
      const stats = result.results.statistics;
      
      // 验证五行力量都是非负数
      const hasNegativePower = Object.values(powers).some(power => power < 0);
      if (hasNegativePower) {
        throw new Error('五行力量不能为负数');
      }
      
      // 验证总力量大于0
      if (stats.totalPower <= 0) {
        throw new Error('总力量必须大于0');
      }
      
      // 验证最强最弱五行的逻辑
      if (stats.strongest.power < stats.weakest.power) {
        throw new Error('最强五行力量不能小于最弱五行');
      }
      
      console.log(`✅ 边界条件测试通过`);
      console.log(`📊 结果: 总力量${stats.totalPower.toFixed(1)}, 最强${stats.strongest.element}(${stats.strongest.power}), 最弱${stats.weakest.element}(${stats.weakest.power})`);
      
      return {
        testCase: testCase.name,
        success: true,
        result: {
          totalPower: stats.totalPower,
          strongest: stats.strongest,
          weakest: stats.weakest,
          powers
        }
      };
      
    } catch (error) {
      console.log(`❌ 边界条件测试失败: ${error.message}`);
      return {
        testCase: testCase.name,
        success: false,
        error: error.message
      };
    }
  }

  // 性能异常测试
  performPerformanceExceptionTest() {
    console.log('\n🧪 执行性能异常测试');
    
    const startTime = Date.now();
    const maxExecutionTime = 5000; // 5秒超时
    
    try {
      // 执行大量计算来测试性能
      for (let i = 0; i < 1000; i++) {
        const testInput = [
          { gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '卯' }
        ];
        
        this.engine.generateDetailedReport(testInput);
        
        // 检查是否超时
        if (Date.now() - startTime > maxExecutionTime) {
          throw new Error('执行超时');
        }
      }
      
      const executionTime = Date.now() - startTime;
      console.log(`✅ 性能测试通过: 1000次计算耗时${executionTime}ms`);
      
      return {
        testCase: '性能异常测试',
        success: true,
        executionTime,
        iterations: 1000
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.log(`❌ 性能测试失败: ${error.message} (耗时${executionTime}ms)`);
      
      return {
        testCase: '性能异常测试',
        success: false,
        error: error.message,
        executionTime
      };
    }
  }

  // 内存泄漏测试
  performMemoryLeakTest() {
    console.log('\n🧪 执行内存泄漏测试');
    
    const initialMemory = process.memoryUsage();
    const iterations = 500;
    
    try {
      for (let i = 0; i < iterations; i++) {
        const testInput = [
          { gan: '甲', zhi: '子' }, { gan: '乙', zhi: '丑' },
          { gan: '丙', zhi: '寅' }, { gan: '丁', zhi: '卯' }
        ];
        
        this.engine.generateDetailedReport(testInput);
        
        // 每100次检查一次内存
        if (i % 100 === 0) {
          const currentMemory = process.memoryUsage();
          const memoryIncrease = currentMemory.heapUsed - initialMemory.heapUsed;
          
          // 如果内存增长超过50MB，可能存在内存泄漏
          if (memoryIncrease > 50 * 1024 * 1024) {
            throw new Error(`可能存在内存泄漏: 内存增长${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
          }
        }
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`✅ 内存泄漏测试通过: ${iterations}次计算后内存增长${(memoryIncrease / 1024).toFixed(2)}KB`);
      
      return {
        testCase: '内存泄漏测试',
        success: true,
        iterations,
        memoryIncrease
      };
      
    } catch (error) {
      console.log(`❌ 内存泄漏测试失败: ${error.message}`);
      
      return {
        testCase: '内存泄漏测试',
        success: false,
        error: error.message
      };
    }
  }

  // 生成错误处理测试报告
  generateErrorHandlingReport(results) {
    console.log('\n📋 错误处理机制测试报告');
    console.log('=' .repeat(60));
    
    const inputValidationResults = results.filter(r => r.category === 'inputValidation');
    const boundaryResults = results.filter(r => r.category === 'boundary');
    const performanceResults = results.filter(r => r.category === 'performance');
    
    // 输入验证测试统计
    if (inputValidationResults.length > 0) {
      const successCount = inputValidationResults.filter(r => r.success).length;
      console.log('\n🎯 输入验证测试:');
      console.log(`  总测试数: ${inputValidationResults.length}`);
      console.log(`  通过数: ${successCount}`);
      console.log(`  通过率: ${(successCount / inputValidationResults.length * 100).toFixed(1)}%`);
    }
    
    // 边界条件测试统计
    if (boundaryResults.length > 0) {
      const successCount = boundaryResults.filter(r => r.success).length;
      console.log('\n🎯 边界条件测试:');
      console.log(`  总测试数: ${boundaryResults.length}`);
      console.log(`  通过数: ${successCount}`);
      console.log(`  通过率: ${(successCount / boundaryResults.length * 100).toFixed(1)}%`);
    }
    
    // 性能测试统计
    if (performanceResults.length > 0) {
      const successCount = performanceResults.filter(r => r.success).length;
      console.log('\n🎯 性能异常测试:');
      console.log(`  总测试数: ${performanceResults.length}`);
      console.log(`  通过数: ${successCount}`);
      console.log(`  通过率: ${(successCount / performanceResults.length * 100).toFixed(1)}%`);
    }
    
    // 总体评估
    const totalTests = results.length;
    const totalSuccess = results.filter(r => r.success).length;
    const overallSuccessRate = (totalSuccess / totalTests) * 100;
    
    console.log('\n🎯 总体评估:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  总通过数: ${totalSuccess}`);
    console.log(`  总通过率: ${overallSuccessRate.toFixed(1)}%`);
    
    if (overallSuccessRate >= 95) {
      console.log('✅ 错误处理机制: 优秀 (≥95%)');
      console.log('   系统具有完善的错误处理和异常恢复能力');
    } else if (overallSuccessRate >= 90) {
      console.log('✅ 错误处理机制: 良好 (≥90%)');
      console.log('   系统错误处理基本完善，建议进一步优化');
    } else if (overallSuccessRate >= 80) {
      console.log('⚠️ 错误处理机制: 一般 (≥80%)');
      console.log('   系统错误处理需要改进');
    } else {
      console.log('❌ 错误处理机制: 需要改进 (<80%)');
      console.log('   系统错误处理机制不完善，需要重点优化');
    }
  }

  // 运行完整的错误处理测试
  async runFullErrorHandlingTest() {
    console.log('🚀 开始专业级五行计算引擎错误处理机制测试');
    console.log('=' .repeat(60));
    
    const allResults = [];
    
    // 1. 输入验证测试
    console.log('\n📋 第一阶段: 输入验证测试');
    console.log('-' .repeat(40));
    
    const inputValidationCases = this.getInputValidationTestCases();
    for (const testCase of inputValidationCases) {
      const result = this.performInputValidationTest(testCase);
      result.category = 'inputValidation';
      allResults.push(result);
    }
    
    // 2. 边界条件测试
    console.log('\n📋 第二阶段: 边界条件测试');
    console.log('-' .repeat(40));
    
    const boundaryTestCases = this.getBoundaryTestCases();
    for (const testCase of boundaryTestCases) {
      const result = this.performBoundaryTest(testCase);
      result.category = 'boundary';
      allResults.push(result);
    }
    
    // 3. 性能异常测试
    console.log('\n📋 第三阶段: 性能异常测试');
    console.log('-' .repeat(40));
    
    const performanceResult = this.performPerformanceExceptionTest();
    performanceResult.category = 'performance';
    allResults.push(performanceResult);
    
    // 4. 内存泄漏测试
    const memoryResult = this.performMemoryLeakTest();
    memoryResult.category = 'performance';
    allResults.push(memoryResult);
    
    // 生成报告
    this.generateErrorHandlingReport(allResults);
    
    console.log('\n🎉 错误处理机制测试完成！');
    return allResults;
  }
}

// 执行测试
if (require.main === module) {
  const errorHandlingTest = new ErrorHandlingEnhancement();
  errorHandlingTest.runFullErrorHandlingTest().catch(error => {
    console.error('❌ 错误处理测试执行失败:', error);
  });
}

module.exports = ErrorHandlingEnhancement;
