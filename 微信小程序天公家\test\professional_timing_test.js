/**
 * 专业应期分析功能测试脚本
 * 验证病药平衡法则、三重引动机制、能量阈值模型等核心功能
 */

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');
const { TimingAnalysisInterface } = require('../utils/timing_analysis_interfaces.js');

class ProfessionalTimingTest {
  constructor() {
    this.engine = new ProfessionalTimingEngine();
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始专业应期分析功能测试...\n');

    // 测试用例数据
    const testCases = this.getTestCases();

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`📋 测试案例 ${i + 1}: ${testCase.name}`);
      console.log(`   八字: ${testCase.bazi_display}`);
      console.log(`   性别: ${testCase.gender}, 事件: ${testCase.event_type}\n`);

      try {
        // 执行专业应期分析
        const result = this.engine.analyzeProfessionalTiming(
          testCase.bazi_data,
          testCase.event_type,
          testCase.gender,
          2024,
          5
        );

        // 验证结果
        const validation = this.validateResult(result, testCase);
        
        this.testResults.push({
          case_name: testCase.name,
          success: validation.success,
          result: result,
          validation: validation,
          execution_time: validation.execution_time
        });

        // 输出结果
        this.printTestResult(testCase, result, validation);

      } catch (error) {
        console.error(`❌ 测试失败: ${error.message}\n`);
        this.testResults.push({
          case_name: testCase.name,
          success: false,
          error: error.message
        });
      }
    }

    // 输出测试总结
    this.printTestSummary();
  }

  /**
   * 获取测试用例
   */
  getTestCases() {
    return [
      {
        name: '男命婚姻应期测试',
        bazi_display: '甲子 丙寅 戊午 癸亥',
        gender: 'male',
        event_type: 'marriage',
        bazi_data: {
          year_pillar: { heavenly: '甲', earthly: '子' },
          month_pillar: { heavenly: '丙', earthly: '寅' },
          day_pillar: { heavenly: '戊', earthly: '午' },
          time_pillar: { heavenly: '癸', earthly: '亥' },
          day_master: '戊',
          birth_info: {
            year: 1984,
            month: 2,
            day: 15,
            hour: 22,
            gender: 'male'
          }
        },
        expected_features: ['病药分析', '三重引动', '能量阈值']
      },
      {
        name: '女命升职应期测试',
        bazi_display: '乙丑 己卯 辛未 庚寅',
        gender: 'female',
        event_type: 'promotion',
        bazi_data: {
          year_pillar: { heavenly: '乙', earthly: '丑' },
          month_pillar: { heavenly: '己', earthly: '卯' },
          day_pillar: { heavenly: '辛', earthly: '未' },
          time_pillar: { heavenly: '庚', earthly: '寅' },
          day_master: '辛',
          birth_info: {
            year: 1985,
            month: 3,
            day: 20,
            hour: 8,
            gender: 'female'
          }
        },
        expected_features: ['官印相生', '权威阈值', '星动检测']
      },
      {
        name: '生育应期测试',
        bazi_display: '丙辰 庚子 壬申 丁未',
        gender: 'female',
        event_type: 'childbirth',
        bazi_data: {
          year_pillar: { heavenly: '丙', earthly: '辰' },
          month_pillar: { heavenly: '庚', earthly: '子' },
          day_pillar: { heavenly: '壬', earthly: '申' },
          time_pillar: { heavenly: '丁', earthly: '未' },
          day_master: '壬',
          birth_info: {
            year: 1976,
            month: 12,
            day: 8,
            hour: 14,
            gender: 'female'
          }
        },
        expected_features: ['食伤通关', '子女宫', '水木能量']
      },
      {
        name: '财运应期测试',
        bazi_display: '戊戌 甲寅 己巳 乙亥',
        gender: 'male',
        event_type: 'wealth',
        bazi_data: {
          year_pillar: { heavenly: '戊', earthly: '戌' },
          month_pillar: { heavenly: '甲', earthly: '寅' },
          day_pillar: { heavenly: '己', earthly: '巳' },
          time_pillar: { heavenly: '乙', earthly: '亥' },
          day_master: '己',
          birth_info: {
            year: 1958,
            month: 2,
            day: 25,
            hour: 21,
            gender: 'male'
          }
        },
        expected_features: ['财星得用', '财库开启', '食伤生财']
      }
    ];
  }

  /**
   * 验证分析结果
   */
  validateResult(result, testCase) {
    const startTime = Date.now();
    const validation = {
      success: true,
      errors: [],
      warnings: [],
      feature_coverage: {},
      execution_time: 0
    };

    try {
      // 1. 基础结构验证
      if (!result.event_type) {
        validation.errors.push('缺少事件类型');
      }

      if (!result.disease_analysis) {
        validation.errors.push('缺少病药分析');
      } else {
        validation.feature_coverage['病药分析'] = true;
        
        // 验证病药分析结构
        if (!result.disease_analysis.diseases || !Array.isArray(result.disease_analysis.diseases)) {
          validation.errors.push('病神分析结构错误');
        }
        
        if (!result.disease_analysis.medicines || !Array.isArray(result.disease_analysis.medicines)) {
          validation.errors.push('药神分析结构错误');
        }
      }

      // 2. 能量分析验证
      if (!result.energy_analysis) {
        validation.errors.push('缺少能量分析');
      } else {
        validation.feature_coverage['能量阈值'] = true;
        
        if (!result.energy_analysis.element_energies) {
          validation.errors.push('缺少五行能量计算');
        }
        
        if (!result.energy_analysis.threshold_results) {
          validation.errors.push('缺少阈值检测结果');
        }
      }

      // 3. 三重引动验证
      if (!result.activation_analysis) {
        validation.errors.push('缺少三重引动分析');
      } else {
        validation.feature_coverage['三重引动'] = true;
        
        if (!result.activation_analysis.activation_results || !Array.isArray(result.activation_analysis.activation_results)) {
          validation.errors.push('引动结果结构错误');
        }
      }

      // 4. 应期预测验证
      if (!result.timing_prediction) {
        validation.errors.push('缺少应期预测');
      } else {
        if (!result.timing_prediction.best_timing) {
          validation.warnings.push('未找到最佳应期');
        }
      }

      // 5. 置信度验证
      if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 1) {
        validation.errors.push('置信度格式错误');
      }

      // 6. 古籍依据验证
      if (!result.ancient_basis) {
        validation.warnings.push('缺少古籍理论依据');
      }

      // 7. 接口规范验证
      const interfaceValidation = TimingAnalysisInterface.validateRequest({
        birth: '2024-01-01T00:00:00Z',
        gender: testCase.gender,
        event: testCase.event_type
      });

      if (!interfaceValidation.valid) {
        validation.errors.push(`接口规范验证失败: ${interfaceValidation.errors.join(', ')}`);
      }

      validation.success = validation.errors.length === 0;
      validation.execution_time = Date.now() - startTime;

    } catch (error) {
      validation.success = false;
      validation.errors.push(`验证过程异常: ${error.message}`);
      validation.execution_time = Date.now() - startTime;
    }

    return validation;
  }

  /**
   * 打印测试结果
   */
  printTestResult(testCase, result, validation) {
    if (validation.success) {
      console.log('✅ 测试通过');
      
      if (result.timing_prediction?.best_timing) {
        console.log(`   最佳应期: ${result.timing_prediction.best_timing.year}年`);
        console.log(`   置信度: ${(result.confidence * 100).toFixed(1)}%`);
      }
      
      if (result.disease_analysis?.balance_score) {
        console.log(`   病药平衡: ${(result.disease_analysis.balance_score * 100).toFixed(1)}%`);
      }
      
      console.log(`   功能覆盖: ${Object.keys(validation.feature_coverage).join(', ')}`);
      console.log(`   执行时间: ${validation.execution_time}ms`);
      
    } else {
      console.log('❌ 测试失败');
      validation.errors.forEach(error => {
        console.log(`   错误: ${error}`);
      });
    }

    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => {
        console.log(`   ⚠️ 警告: ${warning}`);
      });
    }

    console.log('');
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('📊 测试总结');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`通过率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
    
    if (passedTests > 0) {
      const avgExecutionTime = this.testResults
        .filter(r => r.success && r.execution_time)
        .reduce((sum, r) => sum + r.execution_time, 0) / passedTests;
      console.log(`平均执行时间: ${avgExecutionTime.toFixed(1)}ms`);
    }
    
    console.log('');
    
    // 功能覆盖率统计
    const featureCoverage = {};
    this.testResults.forEach(result => {
      if (result.validation?.feature_coverage) {
        Object.keys(result.validation.feature_coverage).forEach(feature => {
          featureCoverage[feature] = (featureCoverage[feature] || 0) + 1;
        });
      }
    });
    
    console.log('🎯 功能覆盖率:');
    Object.keys(featureCoverage).forEach(feature => {
      const coverage = (featureCoverage[feature] / totalTests * 100).toFixed(1);
      console.log(`   ${feature}: ${coverage}%`);
    });
    
    console.log('\n🔮 专业应期分析功能测试完成！');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const test = new ProfessionalTimingTest();
  test.runAllTests().catch(console.error);
}

module.exports = ProfessionalTimingTest;
