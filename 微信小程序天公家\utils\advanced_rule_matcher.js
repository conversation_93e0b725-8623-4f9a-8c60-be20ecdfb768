/**
 * 高级规则匹配算法
 * 基于多维度特征的智能规则匹配系统
 */

class AdvancedRuleMatcher {
  constructor() {
    this.matchingWeights = {
      // 基础匹配权重
      exactMatch: 1.0,        // 精确匹配
      partialMatch: 0.7,      // 部分匹配
      categoryMatch: 0.5,     // 分类匹配
      seasonMatch: 0.6,       // 季节匹配
      elementMatch: 0.8,      // 五行匹配
      
      // 高级匹配权重
      patternMatch: 0.9,      // 格局匹配
      strengthMatch: 0.7,     // 强弱匹配
      relationMatch: 0.6,     // 关系匹配
      contextMatch: 0.5       // 上下文匹配
    };
    
    this.elementMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };
    
    this.zhiElementMap = {
      '寅': '木', '卯': '木', '巳': '火', '午': '火', '辰': '土', '戌': '土',
      '丑': '土', '未': '土', '申': '金', '酉': '金', '亥': '水', '子': '水'
    };
    
    this.seasonMap = {
      '寅': '春', '卯': '春', '辰': '春',
      '巳': '夏', '午': '夏', '未': '夏',
      '申': '秋', '酉': '秋', '戌': '秋',
      '亥': '冬', '子': '冬', '丑': '冬'
    };
  }

  /**
   * 智能匹配规则
   * @param {Array} rules - 规则数组
   * @param {Array} fourPillars - 四柱数据
   * @param {string} analysisType - 分析类型
   * @param {Object} options - 匹配选项
   * @returns {Array} 排序后的匹配规则
   */
  matchRules(rules, fourPillars, analysisType, options = {}) {
    console.log(`🔍 开始智能规则匹配 - 类型: ${analysisType}, 规则数: ${rules.length}`);
    
    const startTime = Date.now();
    
    // 提取四柱特征
    const features = this.extractFeatures(fourPillars);
    
    // 为每个规则计算匹配分数
    const scoredRules = rules.map(rule => {
      const score = this.calculateMatchScore(rule, features, analysisType);
      return {
        ...rule,
        matchScore: score,
        matchDetails: this.getMatchDetails(rule, features)
      };
    });
    
    // 按匹配分数排序
    const sortedRules = scoredRules
      .filter(rule => rule.matchScore > 0.3) // 过滤低分规则
      .sort((a, b) => b.matchScore - a.matchScore);
    
    const endTime = Date.now();
    console.log(`✅ 规则匹配完成 - 耗时: ${endTime - startTime}ms, 匹配规则: ${sortedRules.length}条`);
    
    return sortedRules;
  }

  /**
   * 提取四柱特征
   */
  extractFeatures(fourPillars) {
    const [year, month, day, hour] = fourPillars;
    
    return {
      // 基础信息
      dayGan: day.gan,
      dayZhi: day.zhi,
      monthZhi: month.zhi,
      yearGan: year.gan,
      hourGan: hour.gan,
      
      // 五行信息
      dayElement: this.elementMap[day.gan],
      monthElement: this.zhiElementMap[month.zhi],
      season: this.seasonMap[month.zhi],
      
      // 格局信息
      pattern: this.identifyBasicPattern(day.gan, month.zhi),
      
      // 强弱信息
      strength: this.analyzeStrength(fourPillars),
      
      // 关系信息
      relations: this.analyzeRelations(fourPillars),
      
      // 神煞信息
      shensha: this.identifyShensha(fourPillars),
      
      // 组合特征
      combinations: this.identifyCombinations(fourPillars)
    };
  }

  /**
   * 计算规则匹配分数
   */
  calculateMatchScore(rule, features, analysisType) {
    let totalScore = 0;
    let weightSum = 0;
    
    // 基础置信度
    totalScore += (rule.confidence || 0.5) * 0.3;
    weightSum += 0.3;
    
    // 分析类型匹配
    if (this.matchesAnalysisType(rule, analysisType)) {
      totalScore += this.matchingWeights.categoryMatch;
      weightSum += this.matchingWeights.categoryMatch;
    }
    
    // 天干匹配
    const ganMatch = this.matchGan(rule, features);
    if (ganMatch > 0) {
      totalScore += ganMatch * this.matchingWeights.exactMatch;
      weightSum += this.matchingWeights.exactMatch;
    }
    
    // 地支匹配
    const zhiMatch = this.matchZhi(rule, features);
    if (zhiMatch > 0) {
      totalScore += zhiMatch * this.matchingWeights.exactMatch;
      weightSum += this.matchingWeights.exactMatch;
    }
    
    // 五行匹配
    const elementMatch = this.matchElement(rule, features);
    if (elementMatch > 0) {
      totalScore += elementMatch * this.matchingWeights.elementMatch;
      weightSum += this.matchingWeights.elementMatch;
    }
    
    // 季节匹配
    const seasonMatch = this.matchSeason(rule, features);
    if (seasonMatch > 0) {
      totalScore += seasonMatch * this.matchingWeights.seasonMatch;
      weightSum += this.matchingWeights.seasonMatch;
    }
    
    // 格局匹配
    const patternMatch = this.matchPattern(rule, features);
    if (patternMatch > 0) {
      totalScore += patternMatch * this.matchingWeights.patternMatch;
      weightSum += this.matchingWeights.patternMatch;
    }
    
    // 强弱匹配
    const strengthMatch = this.matchStrength(rule, features);
    if (strengthMatch > 0) {
      totalScore += strengthMatch * this.matchingWeights.strengthMatch;
      weightSum += this.matchingWeights.strengthMatch;
    }
    
    // 关键词匹配
    const keywordMatch = this.matchKeywords(rule, features);
    if (keywordMatch > 0) {
      totalScore += keywordMatch * this.matchingWeights.contextMatch;
      weightSum += this.matchingWeights.contextMatch;
    }
    
    // 计算加权平均分
    return weightSum > 0 ? totalScore / weightSum : 0;
  }

  /**
   * 分析类型匹配
   */
  matchesAnalysisType(rule, analysisType) {
    const typeMapping = {
      'sanming': ['三命通会'],
      'yuanhai': ['渊海子平'],
      'ditian': ['滴天髓'],
      'pattern': ['正格', '特殊格局', '格局理论'],
      'yongshen': ['用神理论'],
      'strength': ['强弱判断'],
      'shensha': ['神煞格局', '神煞理论']
    };
    
    const targetSources = typeMapping[analysisType] || [];
    
    return targetSources.some(source => 
      rule.book_source === source || 
      rule.category === source ||
      rule.source === source
    );
  }

  /**
   * 天干匹配
   */
  matchGan(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    const conditions = rule.conditions || '';
    
    let score = 0;
    
    // 精确匹配日干
    if (ruleText.includes(features.dayGan) || conditions.includes(features.dayGan)) {
      score += 1.0;
    }
    
    // 匹配年干、时干
    if (ruleText.includes(features.yearGan) || ruleText.includes(features.hourGan)) {
      score += 0.5;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 地支匹配
   */
  matchZhi(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    const conditions = rule.conditions || '';
    
    let score = 0;
    
    // 精确匹配月支
    if (ruleText.includes(features.monthZhi) || conditions.includes(features.monthZhi)) {
      score += 1.0;
    }
    
    // 匹配日支
    if (ruleText.includes(features.dayZhi)) {
      score += 0.7;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 五行匹配
   */
  matchElement(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    
    let score = 0;
    
    // 匹配日主五行
    if (ruleText.includes(features.dayElement)) {
      score += 0.8;
    }
    
    // 匹配月令五行
    if (ruleText.includes(features.monthElement)) {
      score += 0.6;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 季节匹配
   */
  matchSeason(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    
    const seasonKeywords = {
      '春': ['春', '木旺', '寅卯辰'],
      '夏': ['夏', '火旺', '巳午未'],
      '秋': ['秋', '金旺', '申酉戌'],
      '冬': ['冬', '水旺', '亥子丑']
    };
    
    const keywords = seasonKeywords[features.season] || [];
    
    for (const keyword of keywords) {
      if (ruleText.includes(keyword)) {
        return 0.8;
      }
    }
    
    return 0;
  }

  /**
   * 格局匹配
   */
  matchPattern(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    const patternName = rule.pattern_name || rule.title || '';
    
    // 直接格局名称匹配
    if (patternName.includes(features.pattern) || features.pattern.includes(patternName)) {
      return 1.0;
    }
    
    // 格局关键词匹配
    if (ruleText.includes(features.pattern) || ruleText.includes('格')) {
      return 0.6;
    }
    
    return 0;
  }

  /**
   * 强弱匹配
   */
  matchStrength(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    
    const strengthKeywords = {
      '强': ['身强', '旺', '得令', '有力'],
      '弱': ['身弱', '衰', '失令', '无力'],
      '中和': ['中和', '平衡', '适中']
    };
    
    const keywords = strengthKeywords[features.strength] || [];
    
    for (const keyword of keywords) {
      if (ruleText.includes(keyword)) {
        return 0.7;
      }
    }
    
    return 0;
  }

  /**
   * 关键词匹配
   */
  matchKeywords(rule, features) {
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    
    let score = 0;
    let matchCount = 0;
    
    // 检查各种特征关键词
    const allFeatures = [
      features.dayGan, features.dayZhi, features.monthZhi,
      features.dayElement, features.season, features.pattern
    ];
    
    for (const feature of allFeatures) {
      if (feature && ruleText.includes(feature.toLowerCase())) {
        matchCount++;
      }
    }
    
    // 根据匹配数量计算分数
    if (matchCount > 0) {
      score = Math.min(matchCount / allFeatures.length, 1.0);
    }
    
    return score;
  }

  /**
   * 获取匹配详情
   */
  getMatchDetails(rule, features) {
    return {
      ruleId: rule.rule_id,
      confidence: rule.confidence,
      matchedFeatures: this.getMatchedFeatures(rule, features),
      relevanceScore: this.calculateRelevanceScore(rule, features)
    };
  }

  /**
   * 辅助方法：识别基础格局
   */
  identifyBasicPattern(dayGan, monthZhi) {
    const patterns = {
      '甲': { '寅': '建禄格', '午': '食神格', '申': '七杀格' },
      '乙': { '卯': '建禄格', '巳': '食神格', '酉': '七杀格' },
      '丙': { '巳': '建禄格', '申': '偏财格', '子': '七杀格' },
      '丁': { '午': '建禄格', '酉': '偏财格', '丑': '七杀格' },
      '戊': { '巳': '建禄格', '申': '食神格', '寅': '七杀格' },
      '己': { '午': '建禄格', '酉': '食神格', '卯': '七杀格' },
      '庚': { '申': '建禄格', '子': '食神格', '午': '七杀格' },
      '辛': { '酉': '建禄格', '丑': '食神格', '未': '七杀格' },
      '壬': { '亥': '建禄格', '寅': '食神格', '戌': '七杀格' },
      '癸': { '子': '建禄格', '卯': '食神格', '酉': '七杀格' }
    };
    
    return patterns[dayGan]?.[monthZhi] || '普通格局';
  }

  /**
   * 辅助方法：分析强弱
   */
  analyzeStrength(fourPillars) {
    // 简化的强弱分析
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;
    
    // 根据月令判断基础强弱
    const seasonalStrength = {
      '甲': { '寅': '强', '卯': '强', '午': '中', '申': '弱', '酉': '弱' },
      '乙': { '寅': '强', '卯': '强', '午': '中', '申': '弱', '酉': '弱' },
      '丙': { '巳': '强', '午': '强', '戌': '中', '子': '弱', '丑': '弱' },
      '丁': { '巳': '强', '午': '强', '戌': '中', '子': '弱', '丑': '弱' },
      '戊': { '辰': '强', '戌': '强', '丑': '强', '未': '强', '寅': '中' },
      '己': { '辰': '强', '戌': '强', '丑': '强', '未': '强', '卯': '中' },
      '庚': { '申': '强', '酉': '强', '辰': '中', '寅': '弱', '卯': '弱' },
      '辛': { '申': '强', '酉': '强', '辰': '中', '寅': '弱', '卯': '弱' },
      '壬': { '亥': '强', '子': '强', '申': '中', '巳': '弱', '午': '弱' },
      '癸': { '亥': '强', '子': '强', '申': '中', '巳': '弱', '午': '弱' }
    };
    
    return seasonalStrength[dayGan]?.[monthZhi] || '中和';
  }

  /**
   * 辅助方法：分析关系
   */
  analyzeRelations(fourPillars) {
    // 简化的关系分析
    return {
      ganRelations: this.analyzeGanRelations(fourPillars),
      zhiRelations: this.analyzeZhiRelations(fourPillars)
    };
  }

  /**
   * 辅助方法：识别神煞
   */
  identifyShensha(fourPillars) {
    // 简化的神煞识别
    return ['天乙贵人', '文昌贵人']; // 示例
  }

  /**
   * 辅助方法：识别组合
   */
  identifyCombinations(fourPillars) {
    // 简化的组合识别
    return ['三合', '六合']; // 示例
  }

  /**
   * 辅助方法：分析天干关系
   */
  analyzeGanRelations(fourPillars) {
    return ['比肩', '劫财']; // 简化示例
  }

  /**
   * 辅助方法：分析地支关系
   */
  analyzeZhiRelations(fourPillars) {
    return ['三合', '六冲']; // 简化示例
  }

  /**
   * 辅助方法：获取匹配特征
   */
  getMatchedFeatures(rule, features) {
    const matched = [];
    const ruleText = (rule.original_text || rule.content || '').toLowerCase();
    
    if (ruleText.includes(features.dayGan)) matched.push('日干');
    if (ruleText.includes(features.monthZhi)) matched.push('月支');
    if (ruleText.includes(features.season)) matched.push('季节');
    if (ruleText.includes(features.pattern)) matched.push('格局');
    
    return matched;
  }

  /**
   * 辅助方法：计算相关性分数
   */
  calculateRelevanceScore(rule, features) {
    // 基于多个因素计算相关性
    let score = rule.confidence || 0.5;
    
    // 根据匹配特征调整分数
    const matchedFeatures = this.getMatchedFeatures(rule, features);
    score += matchedFeatures.length * 0.1;
    
    return Math.min(score, 1.0);
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdvancedRuleMatcher;
} else if (typeof window !== 'undefined') {
  window.AdvancedRuleMatcher = AdvancedRuleMatcher;
}
