// debug_real_data_flow.js
// 诊断真实的数据流问题

console.log('🔍 开始诊断真实数据流问题');

/**
 * 模拟微信小程序环境
 */
global.wx = {
  getStorageSync: function(key) {
    console.log(`📱 模拟获取存储: ${key}`);
    
    // 模拟真实的存储数据结构
    const mockStorage = {
      'bazi_frontend_result': {
        bazi: {
          year: { gan: '甲', zhi: '子' },
          month: { gan: '丙', zhi: '寅' },
          day: { gan: '戊', zhi: '午' },
          hour: { gan: '庚', zhi: '申' }
        },
        five_elements: {
          wood: 1,
          fire: 2,
          earth: 3,
          metal: 2,
          water: 0
        },
        nayin: {
          year: '海中金',
          month: '炉中火',
          day: '天上火',
          hour: '石榴木'
        },
        lunar_date: '甲子年丙寅月戊午日庚申时',
        solar_term: '立春',
        basicInfo: {
          birth_solar_term: '立春',
          kong_wang: '戌亥',
          ming_gua: '乾卦'
        }
      },
      'bazi_birth_info': {
        name: '测试用户',
        gender: '男',
        year: 1984,
        month: 2,
        day: 15,
        hour: 14,
        minute: 30,
        birthCity: '北京',
        location: '北京'
      },
      'bazi_analysis_mode': 'comprehensive'
    };
    
    return mockStorage[key] || null;
  }
};

/**
 * 模拟页面的关键方法
 */
class MockBaziResultPage {
  constructor() {
    this.data = {
      professionalLiunianData: {
        success: false,
        currentLiunian: null,
        liunianList: [],
        summary: null
      },
      loadingStates: {
        liunian: false
      }
    };
  }

  setData(updates) {
    Object.keys(updates).forEach(key => {
      if (key.includes('.')) {
        const parts = key.split('.');
        let current = this.data;
        for (let i = 0; i < parts.length - 1; i++) {
          current = current[parts[i]];
        }
        current[parts[parts.length - 1]] = updates[key];
      } else {
        this.data[key] = updates[key];
      }
    });
    console.log('📊 setData调用:', Object.keys(updates));
  }

  // 模拟数据转换方法
  convertFrontendDataToDisplayFormat(frontendResult, birthInfo, analysisMode) {
    console.log('🔄 开始数据格式转换...');
    console.log('📊 前端结果:', frontendResult);
    console.log('📊 出生信息:', birthInfo);

    // 检查关键字段
    if (!frontendResult) {
      console.error('❌ frontendResult 为空');
      return null;
    }

    if (!birthInfo) {
      console.error('❌ birthInfo 为空');
      return null;
    }

    // 检查八字数据
    if (!frontendResult.bazi) {
      console.error('❌ frontendResult.bazi 为空');
      return null;
    }

    const convertedData = {
      userInfo: {
        name: birthInfo.name || '用户',
        gender: birthInfo.gender || '未知',
        year: birthInfo.year,
        month: birthInfo.month,
        day: birthInfo.day,
        hour: birthInfo.hour,
        minute: birthInfo.minute
      },
      baziInfo: {
        yearPillar: {
          heavenly: frontendResult.bazi.year?.gan || '甲',
          earthly: frontendResult.bazi.year?.zhi || '子'
        },
        monthPillar: {
          heavenly: frontendResult.bazi.month?.gan || '丙',
          earthly: frontendResult.bazi.month?.zhi || '寅'
        },
        dayPillar: {
          heavenly: frontendResult.bazi.day?.gan || '戊',
          earthly: frontendResult.bazi.day?.zhi || '午'
        },
        timePillar: {
          heavenly: frontendResult.bazi.hour?.gan || '庚',
          earthly: frontendResult.bazi.hour?.zhi || '申'
        }
      },
      fiveElements: frontendResult.five_elements || {
        wood: 0, fire: 0, earth: 0, metal: 0, water: 0
      },
      birthInfo: {
        year: birthInfo.year,
        month: birthInfo.month,
        day: birthInfo.day,
        hour: birthInfo.hour,
        minute: birthInfo.minute
      },
      dataSource: 'converted_frontend_result'
    };

    console.log('✅ 转换后的数据:', convertedData);
    return convertedData;
  }

  // 模拟统一数据结构方法
  unifyDataStructure(rawData) {
    console.log('🔧 开始统一数据结构:', rawData);

    if (!rawData) {
      console.error('❌ rawData 为空');
      return null;
    }

    const unifiedData = {
      userInfo: rawData.userInfo || {},
      baziInfo: rawData.baziInfo || {},
      fiveElements: rawData.fiveElements || {},
      birthInfo: rawData.birthInfo || {}
    };

    console.log('✅ 统一后的数据结构:', unifiedData);
    return unifiedData;
  }

  // 模拟流年计算方法
  calculateProfessionalLiunian(baziData, currentDayun = null) {
    console.log('🌟 开始计算专业级流年数据...');
    console.log('📊 输入的八字数据:', baziData);

    // 检查关键字段
    if (!baziData) {
      console.error('❌ baziData 为空');
      return { success: false, error: 'baziData为空' };
    }

    if (!baziData.baziInfo) {
      console.error('❌ baziData.baziInfo 为空');
      return { success: false, error: 'baziInfo为空' };
    }

    if (!baziData.birthInfo) {
      console.error('❌ baziData.birthInfo 为空');
      return { success: false, error: 'birthInfo为空' };
    }

    // 检查八字四柱
    const pillars = ['yearPillar', 'monthPillar', 'dayPillar', 'timePillar'];
    for (const pillar of pillars) {
      if (!baziData.baziInfo[pillar]) {
        console.error(`❌ baziData.baziInfo.${pillar} 为空`);
        return { success: false, error: `${pillar}为空` };
      }
      if (!baziData.baziInfo[pillar].heavenly || !baziData.baziInfo[pillar].earthly) {
        console.error(`❌ baziData.baziInfo.${pillar} 缺少天干或地支`);
        return { success: false, error: `${pillar}缺少天干或地支` };
      }
    }

    console.log('✅ 数据验证通过，开始计算...');

    // 模拟计算结果
    const mockResult = {
      success: true,
      currentLiunian: {
        year: 2025,
        ganzhi: '乙巳',
        fortuneLevel: {
          level: '中吉',
          score: 75
        }
      },
      liunianList: [
        { year: 2025, ganzhi: '乙巳', fortuneLevel: { level: '中吉', score: 75 } },
        { year: 2026, ganzhi: '丙午', fortuneLevel: { level: '平稳', score: 60 } },
        { year: 2027, ganzhi: '丁未', fortuneLevel: { level: '大吉', score: 85 } }
      ],
      summary: {
        totalYears: 3,
        averageScore: 73,
        averageScore_display: 73,
        bestYear: { year: 2027, fortuneLevel: { score: 85 } },
        worstYear: { year: 2026, fortuneLevel: { score: 60 } }
      }
    };

    console.log('✅ 计算完成:', mockResult);
    return mockResult;
  }

  // 模拟完整的数据加载流程
  simulateDataLoadFlow() {
    console.log('\n🧪 模拟完整的数据加载流程');
    console.log('=' * 50);

    // 1. 模拟从存储获取数据
    console.log('\n📋 步骤1: 从存储获取数据');
    const frontendResult = wx.getStorageSync('bazi_frontend_result');
    const birthInfo = wx.getStorageSync('bazi_birth_info');
    const analysisMode = wx.getStorageSync('bazi_analysis_mode');

    console.log('存储数据检查:');
    console.log(`- frontendResult: ${frontendResult ? '存在' : '不存在'}`);
    console.log(`- birthInfo: ${birthInfo ? '存在' : '不存在'}`);
    console.log(`- analysisMode: ${analysisMode || '未设置'}`);

    if (!frontendResult || !birthInfo) {
      console.error('❌ 关键数据缺失，无法继续');
      return { success: false, error: '关键数据缺失' };
    }

    // 2. 数据格式转换
    console.log('\n📋 步骤2: 数据格式转换');
    const convertedData = this.convertFrontendDataToDisplayFormat(frontendResult, birthInfo, analysisMode);
    
    if (!convertedData) {
      console.error('❌ 数据转换失败');
      return { success: false, error: '数据转换失败' };
    }

    // 3. 统一数据结构
    console.log('\n📋 步骤3: 统一数据结构');
    const unifiedData = this.unifyDataStructure(convertedData);
    
    if (!unifiedData) {
      console.error('❌ 数据统一失败');
      return { success: false, error: '数据统一失败' };
    }

    // 4. 流年计算
    console.log('\n📋 步骤4: 流年计算');
    const liunianResult = this.calculateProfessionalLiunian(unifiedData);

    // 5. 设置到页面
    console.log('\n📋 步骤5: 设置到页面');
    this.setData({
      professionalLiunianData: liunianResult,
      'loadingStates.liunian': false
    });

    // 6. 验证最终结果
    console.log('\n📋 步骤6: 验证最终结果');
    const finalCheck = {
      hasData: !!this.data.professionalLiunianData,
      hasSuccess: this.data.professionalLiunianData.success,
      hasSummary: !!this.data.professionalLiunianData.summary,
      summaryContent: this.data.professionalLiunianData.summary
    };

    console.log('最终验证结果:', finalCheck);

    return {
      success: true,
      steps: {
        dataRetrieval: !!frontendResult && !!birthInfo,
        dataConversion: !!convertedData,
        dataUnification: !!unifiedData,
        liunianCalculation: liunianResult.success,
        pageDataSet: !!this.data.professionalLiunianData.summary
      },
      finalResult: this.data.professionalLiunianData
    };
  }
}

/**
 * 执行诊断
 */
function runRealDataFlowDiagnosis() {
  console.log('🚀 开始真实数据流诊断');
  console.log('诊断时间:', new Date().toLocaleString());

  const mockPage = new MockBaziResultPage();
  const result = mockPage.simulateDataLoadFlow();

  console.log('\n🎉 诊断完成！');
  console.log('\n📊 诊断结果:');
  
  if (result.success) {
    console.log('✅ 整体流程: 成功');
    console.log('📋 各步骤状态:');
    Object.entries(result.steps).forEach(([step, status]) => {
      console.log(`  ${step}: ${status ? '✅ 成功' : '❌ 失败'}`);
    });
    
    console.log('\n📋 最终数据状态:');
    console.log(`  success: ${result.finalResult.success}`);
    console.log(`  hasSummary: ${!!result.finalResult.summary}`);
    if (result.finalResult.summary) {
      console.log(`  averageScore: ${result.finalResult.summary.averageScore}`);
      console.log(`  totalYears: ${result.finalResult.summary.totalYears}`);
    }
  } else {
    console.log('❌ 整体流程: 失败');
    console.log('错误:', result.error);
  }

  return result;
}

// 执行诊断
const diagnosisResult = runRealDataFlowDiagnosis();

console.log('\n🔧 问题分析:');
if (diagnosisResult.success) {
  console.log('✅ 模拟环境下数据流正常');
  console.log('💡 真实环境问题可能在于:');
  console.log('1. 微信存储中的数据格式与预期不符');
  console.log('2. ProfessionalLiunianCalculator 模块加载失败');
  console.log('3. 页面生命周期中的异步时序问题');
  console.log('4. 真实数据中某些字段为 undefined 或 null');
} else {
  console.log('❌ 即使在模拟环境下也存在问题');
  console.log('💡 需要检查基础逻辑');
}

module.exports = { runRealDataFlowDiagnosis, MockBaziResultPage };
