#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去重后的规则需求分析
识别三个系统间的重复维度，计算真实的去重需求
"""

import json
from datetime import datetime
from typing import Dict, List, Set

class DeduplicatedRequirementsAnalyzer:
    def __init__(self):
        # 分析三个系统的维度重叠情况
        self.system_dimensions = {
            # 1. 数字化分析系统 (输出.txt)
            "数字化分析": {
                "核心维度": [
                    "五行力量计算",      # 基础维度
                    "五行平衡指数",      # 基础维度  
                    "规则匹配引擎",      # 通用维度
                    "古籍依据展示",      # 通用维度
                    "数字化可视化"       # 独有维度
                ],
                "依赖的基础理论": [
                    "天干地支理论", "五行生克理论", "月令旺衰", "藏干理论",
                    "十神理论", "格局理论", "用神理论"
                ]
            },
            
            # 2. 每日指南系统 (择日.txt)  
            "每日指南": {
                "核心维度": [
                    "日柱互动分析",      # 独有维度
                    "场景化建议生成",    # 独有维度
                    "神煞影响分析",      # 与匹配分析重叠
                    "时间选择建议",      # 独有维度
                    "个性化推荐"         # 独有维度
                ],
                "依赖的基础理论": [
                    "十神理论", "五行生克理论", "刑冲合害", "神煞理论",
                    "调候理论", "用神理论"
                ]
            },
            
            # 3. 匹配分析系统 (匹配关系.txt)
            "匹配分析": {
                "核心维度": [
                    "五行互补分析",      # 与数字化分析重叠
                    "用神互补分析",      # 与数字化分析重叠
                    "神煞配合分析",      # 与每日指南重叠
                    "十神关系分析",      # 与数字化分析重叠
                    "18种关系类型",      # 独有维度
                    "15个匹配维度",      # 独有维度
                    "古籍依据系统",      # 与数字化分析重叠
                    "心理暗示技巧"       # 独有维度
                ],
                "依赖的基础理论": [
                    "五行生克理论", "十神理论", "神煞理论", "格局理论",
                    "用神理论", "调候理论", "纳音理论", "宫位理论",
                    "十二长生", "刑冲合害", "三合三会"
                ]
            }
        }
        
        # 识别重叠的维度
        self.overlapping_dimensions = {
            "基础理论层": {
                "描述": "所有系统共同依赖的基础八字理论",
                "维度": [
                    "天干地支系统", "五行生克理论", "十神理论", "格局理论",
                    "用神理论", "神煞理论", "调候理论", "月令旺衰",
                    "藏干理论", "刑冲合害", "三合三会"
                ],
                "规则需求": 2000  # 这是所有系统的共同基础
            },
            "分析引擎层": {
                "描述": "多个系统共享的分析能力",
                "维度": [
                    "五行力量计算引擎",    # 数字化分析 + 匹配分析共用
                    "规则匹配引擎",        # 数字化分析 + 每日指南 + 匹配分析共用
                    "古籍依据系统",        # 数字化分析 + 匹配分析共用
                    "神煞分析引擎"         # 每日指南 + 匹配分析共用
                ],
                "规则需求": 800   # 共享的分析引擎
            },
            "应用功能层": {
                "描述": "各系统独有的应用功能",
                "数字化分析独有": {
                    "维度": ["数字化可视化", "平衡指数算法", "雷达图生成"],
                    "规则需求": 300
                },
                "每日指南独有": {
                    "维度": ["日柱互动分析", "场景化建议", "时间选择", "个性化推荐"],
                    "规则需求": 1200
                },
                "匹配分析独有": {
                    "维度": ["18种关系类型", "15个匹配维度", "心理暗示技巧"],
                    "规则需求": 1500
                }
            }
        }
    
    def calculate_deduplicated_requirements(self) -> Dict:
        """计算去重后的真实需求"""
        
        # 基础层（所有系统共享）
        base_layer = self.overlapping_dimensions["基础理论层"]["规则需求"]
        
        # 引擎层（多系统共享）
        engine_layer = self.overlapping_dimensions["分析引擎层"]["规则需求"]
        
        # 应用层（各系统独有）
        app_layer = self.overlapping_dimensions["应用功能层"]
        digital_unique = app_layer["数字化分析独有"]["规则需求"]
        daily_unique = app_layer["每日指南独有"]["规则需求"]
        match_unique = app_layer["匹配分析独有"]["规则需求"]
        
        # 计算去重后的总需求
        total_deduplicated = base_layer + engine_layer + digital_unique + daily_unique + match_unique
        
        # 应用质量和冗余因子
        quality_factor = 1.3  # 30%的质量筛选损失
        redundancy_factor = 1.2  # 20%的冗余备份
        
        final_requirements = int(total_deduplicated * quality_factor * redundancy_factor)
        
        # 与原始数据对比
        original_rules = 4933
        current_rules = 38
        core_rules = 261
        
        result = {
            "去重分析": {
                "基础理论层": {
                    "规则数": base_layer,
                    "描述": "所有系统共享的八字基础理论",
                    "包含": self.overlapping_dimensions["基础理论层"]["维度"]
                },
                "分析引擎层": {
                    "规则数": engine_layer,
                    "描述": "多系统共享的分析引擎",
                    "包含": self.overlapping_dimensions["分析引擎层"]["维度"]
                },
                "应用功能层": {
                    "数字化分析独有": digital_unique,
                    "每日指南独有": daily_unique,
                    "匹配分析独有": match_unique,
                    "小计": digital_unique + daily_unique + match_unique
                }
            },
            "需求计算": {
                "去重前估算": 13050,  # 之前的计算
                "去重后基础需求": total_deduplicated,
                "质量因子": f"×{quality_factor} (质量筛选损失)",
                "冗余因子": f"×{redundancy_factor} (系统稳定性)",
                "最终需求": final_requirements
            },
            "覆盖率分析": {
                "4933条原始规则": {
                    "覆盖率": f"{(original_rules/final_requirements)*100:.1f}%",
                    "评估": "基本够用" if original_rules >= final_requirements*0.8 else "不够用"
                },
                "261条核心规则": {
                    "覆盖率": f"{(core_rules/final_requirements)*100:.1f}%",
                    "评估": "严重不足"
                },
                "38条当前规则": {
                    "覆盖率": f"{(current_rules/final_requirements)*100:.2f}%",
                    "评估": "完全不足"
                }
            },
            "系统实现策略": self._generate_implementation_strategy(final_requirements)
        }
        
        return result
    
    def _generate_implementation_strategy(self, total_requirements: int) -> Dict:
        """生成实现策略"""
        
        strategy = {
            "分层实现策略": {
                "第一优先级：基础理论层": {
                    "目标": "建立所有系统共享的基础理论",
                    "规则数": 2000,
                    "时间": "4-6周",
                    "来源": "从4933条原始规则中筛选和补充",
                    "效果": "为所有系统提供理论基础"
                },
                "第二优先级：分析引擎层": {
                    "目标": "构建共享的分析引擎",
                    "规则数": 800,
                    "时间": "2-3周",
                    "来源": "基于基础理论层开发专用算法",
                    "效果": "多系统复用，提高开发效率"
                },
                "第三优先级：应用功能层": {
                    "目标": "实现各系统独有功能",
                    "规则数": 3000,
                    "时间": "6-8周",
                    "来源": "针对性开发和古籍补充",
                    "效果": "完整的功能体验"
                }
            },
            "资源利用策略": {
                "4933条原始规则": "重点用于基础理论层建设",
                "古籍资料补充": "用于应用功能层的专项需求",
                "质量提升": "对所有规则进行质量筛选和优化"
            },
            "开发优先级": [
                "1. 基础理论层 → 支撑所有系统",
                "2. 数字化分析 → 最容易实现，快速见效",
                "3. 每日指南 → 用户粘性高",
                "4. 匹配分析 → 功能最复杂，最后实现"
            ]
        }
        
        return strategy
    
    def identify_specific_overlaps(self) -> Dict:
        """识别具体的重叠维度"""
        
        overlaps = {
            "完全重叠": {
                "五行分析": {
                    "涉及系统": ["数字化分析", "匹配分析"],
                    "重叠度": "100%",
                    "可共享": True,
                    "规则需求": 200  # 只需要一套规则
                },
                "古籍依据": {
                    "涉及系统": ["数字化分析", "匹配分析"],
                    "重叠度": "100%",
                    "可共享": True,
                    "规则需求": 300  # 只需要一套规则
                },
                "神煞分析": {
                    "涉及系统": ["每日指南", "匹配分析"],
                    "重叠度": "80%",
                    "可共享": True,
                    "规则需求": 250  # 大部分可共享
                }
            },
            "部分重叠": {
                "十神理论": {
                    "涉及系统": ["数字化分析", "每日指南", "匹配分析"],
                    "重叠度": "60%",
                    "可共享": True,
                    "规则需求": 300  # 基础部分共享，应用部分独立
                },
                "用神理论": {
                    "涉及系统": ["数字化分析", "每日指南", "匹配分析"],
                    "重叠度": "70%",
                    "可共享": True,
                    "规则需求": 350  # 基础部分共享，应用部分独立
                }
            },
            "无重叠": {
                "18种关系类型": {
                    "涉及系统": ["匹配分析"],
                    "重叠度": "0%",
                    "可共享": False,
                    "规则需求": 720  # 完全独立
                },
                "日柱互动分析": {
                    "涉及系统": ["每日指南"],
                    "重叠度": "0%",
                    "可共享": False,
                    "规则需求": 600  # 完全独立
                },
                "数字化可视化": {
                    "涉及系统": ["数字化分析"],
                    "重叠度": "0%",
                    "可共享": False,
                    "规则需求": 100  # 完全独立
                }
            }
        }
        
        return overlaps
    
    def generate_analysis_report(self) -> str:
        """生成去重分析报告"""
        
        requirements = self.calculate_deduplicated_requirements()
        overlaps = self.identify_specific_overlaps()
        
        report = []
        report.append("=" * 80)
        report.append("去重后的规则需求分析报告")
        report.append("=" * 80)
        report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 重叠维度分析
        report.append("🔍 维度重叠分析")
        report.append("-" * 50)
        for overlap_type, items in overlaps.items():
            report.append(f"\n{overlap_type}:")
            for item_name, item_info in items.items():
                systems = ", ".join(item_info["涉及系统"])
                report.append(f"  • {item_name}: {systems} (重叠度{item_info['重叠度']})")
                report.append(f"    规则需求: {item_info['规则需求']}条")
        
        # 去重后需求计算
        report.append(f"\n📊 去重后需求计算")
        report.append("-" * 50)
        dedup = requirements["去重分析"]
        report.append(f"基础理论层: {dedup['基础理论层']['规则数']}条 (所有系统共享)")
        report.append(f"分析引擎层: {dedup['分析引擎层']['规则数']}条 (多系统共享)")
        report.append(f"应用功能层: {dedup['应用功能层']['小计']}条 (各系统独有)")
        
        calc = requirements["需求计算"]
        report.append(f"\n去重前估算: {calc['去重前估算']}条")
        report.append(f"去重后基础: {calc['去重后基础需求']}条")
        report.append(f"最终需求: {calc['最终需求']}条")
        
        # 覆盖率重新评估
        report.append(f"\n📈 覆盖率重新评估")
        report.append("-" * 50)
        coverage = requirements["覆盖率分析"]
        for source, info in coverage.items():
            report.append(f"{source}: {info['覆盖率']} - {info['评估']}")
        
        # 实现策略
        report.append(f"\n🚀 分层实现策略")
        report.append("-" * 50)
        strategy = requirements["系统实现策略"]["分层实现策略"]
        for priority, info in strategy.items():
            report.append(f"\n{priority}:")
            report.append(f"  目标: {info['目标']}")
            report.append(f"  规则数: {info['规则数']}条")
            report.append(f"  时间: {info['时间']}")
            report.append(f"  效果: {info['效果']}")
        
        # 关键结论
        report.append(f"\n🎯 关键结论")
        report.append("-" * 50)
        final_req = calc['最终需求']
        report.append(f"1. 去重后真实需求: {final_req}条规则 (比之前减少{13050-final_req}条)")
        report.append(f"2. 4933条原始规则覆盖率: {coverage['4933条原始规则']['覆盖率']}")
        report.append("3. 通过去重和分层设计，大幅降低了开发复杂度")
        report.append("4. 基础理论层是关键，一次建设，多系统复用")
        report.append("5. 分层实现策略可以显著提高开发效率")
        
        return "\n".join(report)

def main():
    """主函数"""
    analyzer = DeduplicatedRequirementsAnalyzer()
    report = analyzer.generate_analysis_report()
    
    print(report)
    
    # 保存报告
    with open("deduplicated_requirements_analysis_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📊 去重需求分析报告已保存到: deduplicated_requirements_analysis_report.txt")

if __name__ == "__main__":
    main()
