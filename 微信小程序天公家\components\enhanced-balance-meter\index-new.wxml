<!--components/enhanced-balance-meter/index.wxml-->
<!-- 增强的五行平衡指标组件 -->
<view class="enhanced-balance-meter">
  <view class="balance-header">
    <view class="header-icon">⚖️</view>
    <view class="header-text">
      <text class="balance-title">五行平衡指数</text>
      <text class="balance-subtitle">基于古籍理论的数字化平衡评估</text>
    </view>
  </view>
  
  <!-- 主要平衡指标 -->
  <view class="balance-main">
    <view class="balance-score-container">
      <text class="balance-score">{{balanceIndex}}</text>
      <text class="balance-unit">分</text>
    </view>
    
    <view class="balance-status">
      <text class="status-text" style="color: {{statusColor}};">{{balanceStatus}}</text>
      <text class="status-desc">{{balanceDescription}}</text>
    </view>
  </view>
  
  <!-- 可视化进度条 -->
  <view class="balance-progress">
    <view class="progress-track">
      <view class="progress-fill" style="width: {{balanceIndex}}%; background: {{progressColor}};"></view>
      <view class="progress-thumb" style="left: {{balanceIndex}}%; background: {{progressColor}};"></view>
    </view>
    
    <!-- 刻度标记 -->
    <view class="progress-marks">
      <view class="mark" style="left: 0%;">
        <text class="mark-label">0</text>
        <text class="mark-desc">极度失衡</text>
      </view>
      <view class="mark" style="left: 25%;">
        <text class="mark-label">25</text>
        <text class="mark-desc">失衡</text>
      </view>
      <view class="mark" style="left: 50%;">
        <text class="mark-label">50</text>
        <text class="mark-desc">一般</text>
      </view>
      <view class="mark" style="left: 75%;">
        <text class="mark-label">75</text>
        <text class="mark-desc">平衡</text>
      </view>
      <view class="mark" style="left: 100%;">
        <text class="mark-label">100</text>
        <text class="mark-desc">完美</text>
      </view>
    </view>

  <!-- 操作按钮 -->
  <view class="balance-actions">
    <button class="action-btn" bindtap="toggleDetails">
      {{showDetails ? '收起详情' : '查看详情'}}
    </button>
    <button class="action-btn secondary" bindtap="shareBalance">
      分享结果
    </button>
  </view>
</view>
