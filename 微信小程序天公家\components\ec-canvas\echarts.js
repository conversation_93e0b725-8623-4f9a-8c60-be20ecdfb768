// components/ec-canvas/echarts.js

/**
 * 微信小程序ECharts
 * 基于Apache ECharts的微信小程序适配版本
 * 用于在微信小程序中绘制各种图表
 */

// 避免全局变量污染
function wrapTouch(event) {
  for (let i = 0; i < event.touches.length; ++i) {
    const touch = event.touches[i];
    touch.offsetX = touch.x;
    touch.offsetY = touch.y;
  }
  return event;
}

// 初始化ECharts核心功能
var echarts = {};

// 设置canvas创建器
echarts.setCanvasCreator = function(creator) {
  echarts._canvasCreator = creator;
};

// 图表类型定义
echarts.init = function(canvas, theme, opts) {
  if (!canvas) {
    console.error('Canvas element is required');
    return null;
  }
  
  var chart = {
    id: opts.id || 'ec-canvas',
    canvas: canvas,
    ctx: canvas.getContext('2d'),
    width: opts.width || 350,
    height: opts.height || 300,
    devicePixelRatio: opts.devicePixelRatio || 1,
    option: {},
    setOption: function(option) {
      console.log('设置图表配置:', option);
      this.option = option;
      this.render();
      return this;
    },
    render: function() {
      // 简化版渲染逻辑
      if (!this.option) return;
      
      const ctx = this.ctx;
      const width = this.width;
      const height = this.height;
      
      // 清空画布
      ctx.clearRect(0, 0, width, height);
      
      // 根据图表类型渲染
      if (this.option.series && this.option.series.length > 0) {
        const series = this.option.series[0];
        
        if (series.type === 'radar') {
          this.drawRadar(ctx, this.option);
        } else if (series.type === 'bar') {
          this.drawBar(ctx, this.option);
        } else if (series.type === 'pie') {
          this.drawPie(ctx, this.option);
        }
      }
      
      return this;
    },
    resize: function() {
      // 图表尺寸调整
      return this;
    },
    on: function(eventName, handler) {
      // 事件绑定
      return this;
    },
    off: function(eventName, handler) {
      // 事件解绑
      return this;
    },
    getWidth: function() {
      return this.width;
    },
    getHeight: function() {
      return this.height;
    },
    getOption: function() {
      return this.option;
    },
    dispatchAction: function() {
      return this;
    },
    clear: function() {
      this.ctx.clearRect(0, 0, this.width, this.height);
      return this;
    },
    dispose: function() {
      this.clear();
      return this;
    },
    getZr: function() {
      return {
        handler: {
          dispatch: function() {},
          processGesture: function() {}
        }
      };
    },
    drawRadar: function(ctx, option) {
      const radar = option.radar;
      const series = option.series[0];
      const data = series.data[0];
      const values = data.value;
      const indicators = radar.indicator;
      const centerX = this.width / 2;
      const centerY = this.height / 2;
      const radius = Math.min(centerX, centerY) * 0.7;
      
      // 绘制雷达图背景
      ctx.beginPath();
      ctx.strokeStyle = '#E0E0E0';
      ctx.fillStyle = 'rgba(245, 245, 245, 0.3)';
      
      const angleStep = (Math.PI * 2) / indicators.length;
      
      // 绘制雷达图轴线
      for (let i = 0; i < indicators.length; i++) {
        const angle = i * angleStep - Math.PI / 2;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(x, y);
        
        // 绘制标签
        const labelX = centerX + (radius + 15) * Math.cos(angle);
        const labelY = centerY + (radius + 15) * Math.sin(angle);
        ctx.fillStyle = '#333';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(indicators[i].name, labelX, labelY);
      }
      
      ctx.stroke();
      
      // 绘制数据区域
      ctx.beginPath();
      for (let i = 0; i < values.length; i++) {
        const value = values[i];
        const max = indicators[i].max || 100;
        const ratio = value / max;
        const angle = i * angleStep - Math.PI / 2;
        const x = centerX + radius * ratio * Math.cos(angle);
        const y = centerY + radius * ratio * Math.sin(angle);
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.closePath();
      ctx.fillStyle = 'rgba(108, 92, 231, 0.3)';
      ctx.strokeStyle = '#6C5CE7';
      ctx.lineWidth = 2;
      ctx.fill();
      ctx.stroke();
    },
    drawPie: function(ctx, option) {
      const series = option.series[0];
      const data = series.data;
      const centerX = this.width / 2;
      const centerY = this.height / 2;
      const radius = Math.min(centerX, centerY) * 0.7;
      
      let total = 0;
      data.forEach(item => {
        total += item.value;
      });
      
      let startAngle = 0;
      const colors = ['#6C5CE7', '#74b9ff', '#00cec9', '#fdcb6e', '#e17055', '#d63031'];
      
      // 绘制饼图
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        const angle = (item.value / total) * Math.PI * 2;
        const endAngle = startAngle + angle;
        
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.closePath();
        
        const color = item.itemStyle && item.itemStyle.color ? 
                    item.itemStyle.color : colors[i % colors.length];
        ctx.fillStyle = color;
        ctx.fill();
        
        // 绘制标签
        const labelAngle = startAngle + angle / 2;
        const labelX = centerX + (radius + 20) * Math.cos(labelAngle);
        const labelY = centerY + (radius + 20) * Math.sin(labelAngle);
        
        ctx.fillStyle = '#333';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(item.name, labelX, labelY);
        ctx.fillText(Math.round(item.value / total * 100) + '%', labelX, labelY + 15);
        
        startAngle = endAngle;
      }
    },
    drawBar: function(ctx, option) {
      // 简单柱状图实现
      const series = option.series[0];
      const data = series.data;
      const xAxis = option.xAxis || {};
      const categories = xAxis.data || [];
      
      const barWidth = this.width * 0.8 / data.length;
      const maxValue = Math.max(...data.map(item => item.value || item));
      const height = this.height * 0.8;
      const startY = this.height * 0.9;
      const startX = this.width * 0.1;
      
      // 绘制坐标轴
      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(startX + this.width * 0.8, startY);
      ctx.strokeStyle = '#999';
      ctx.stroke();
      
      // 绘制柱状
      for (let i = 0; i < data.length; i++) {
        const value = data[i].value || data[i];
        const barHeight = (value / maxValue) * height;
        const x = startX + i * barWidth + barWidth * 0.1;
        const y = startY - barHeight;
        
        ctx.fillStyle = data[i].itemStyle && data[i].itemStyle.color ? 
                      data[i].itemStyle.color : '#6C5CE7';
        ctx.fillRect(x, y, barWidth * 0.8, barHeight);
        
        // 绘制标签
        if (categories[i]) {
          ctx.fillStyle = '#333';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(categories[i], x + barWidth * 0.4, startY + 15);
        }
      }
    }
  };
  
  return chart;
};

// 图形工具
echarts.graphic = {
  RadialGradient: function(x, y, r, colorStops) {
    return {
      type: 'radial',
      x: x,
      y: y,
      r: r,
      colorStops: colorStops,
      global: false
    };
  },
  LinearGradient: function(x, y, x2, y2, colorStops) {
    return {
      type: 'linear',
      x: x,
      y: y,
      x2: x2,
      y2: y2,
      colorStops: colorStops,
      global: false
    };
  }
};

// 导出模块
module.exports = echarts;