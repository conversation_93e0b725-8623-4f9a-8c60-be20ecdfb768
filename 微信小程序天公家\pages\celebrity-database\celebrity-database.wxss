/* 历史名人数据库样式 */

.celebrity-database-container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 30rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
}

.search-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 筛选标签 */
.filter-tags {
  margin-bottom: 20rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tag {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 40rpx;
  font-size: 24rpx;
  color: #7f8c8d;
  border: 2rpx solid #ecf0f1;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

/* 统计信息 */
.statistics-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stat-card {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 名人卡片列表 */
.celebrity-list {
  margin-bottom: 30rpx;
}

.celebrity-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.celebrity-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 名人头部信息 */
.celebrity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.celebrity-name-section {
  flex: 1;
}

.celebrity-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.celebrity-courtesy {
  display: inline-block;
  font-size: 22rpx;
  color: #7f8c8d;
  background: #ecf0f1;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
}

.celebrity-nickname {
  display: inline-block;
  font-size: 22rpx;
  color: #e74c3c;
  background: #ffeaa7;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.verification-badge {
  text-align: center;
  background: linear-gradient(135deg, #00b894, #00cec9);
  color: white;
  padding: 16rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
}

.verification-score {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
}

.verification-label {
  display: block;
  font-size: 20rpx;
  opacity: 0.9;
}

/* 元信息 */
.celebrity-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.meta-label {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.meta-value {
  font-size: 22rpx;
  color: #2c3e50;
}

/* 八字信息 */
.bazi-section {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  color: white;
}

.bazi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.bazi-label {
  font-size: 24rpx;
  font-weight: bold;
}

.pattern-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.bazi-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bazi-text {
  font-size: 26rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
}

.yongshen-text {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 事件预览 */
.events-preview {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff3cd;
  border-radius: 12rpx;
  border-left: 6rpx solid #ffc107;
}

.events-label {
  display: block;
  font-size: 22rpx;
  color: #856404;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.event-item {
  display: flex;
  align-items: center;
}

.event-date {
  font-size: 20rpx;
  color: #856404;
  background: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
  min-width: 120rpx;
  text-align: center;
}

.event-desc {
  font-size: 22rpx;
  color: #856404;
  flex: 1;
}

/* 古籍依据 */
.evidence-section {
  padding: 20rpx;
  background: #e8f5e8;
  border-radius: 12rpx;
  border-left: 6rpx solid #28a745;
}

.evidence-label {
  display: block;
  font-size: 22rpx;
  color: #155724;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.evidence-text {
  font-size: 22rpx;
  color: #155724;
  line-height: 1.6;
  font-style: italic;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.load-more-text {
  font-size: 28rpx;
  color: #3498db;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-subtitle {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 弹窗样式 */
.celebrity-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.celebrity-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: absolute;
  top: 10%;
  left: 5%;
  width: 90%;
  height: 80%;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  cursor: pointer;
}

.modal-body {
  height: calc(100% - 120rpx);
  padding: 30rpx;
}

/* 详情区块 */
.detail-section {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #ecf0f1;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 八字详情 */
.bazi-detail {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30rpx;
  border-radius: 16rpx;
}

.bazi-full {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  letter-spacing: 8rpx;
  margin-bottom: 20rpx;
}

.pattern-detail {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
}

/* 时间线 */
.events-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 120rpx;
  top: 0;
  bottom: -30rpx;
  width: 2rpx;
  background: #ecf0f1;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-date {
  width: 120rpx;
  font-size: 20rpx;
  color: #7f8c8d;
  background: #ecf0f1;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-right: 30rpx;
  position: relative;
  z-index: 1;
}

.timeline-content {
  flex: 1;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.timeline-title {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.timeline-desc {
  display: block;
  font-size: 22rpx;
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.timeline-impact {
  display: block;
  font-size: 20rpx;
  color: #e74c3c;
}

/* 验证详情 */
.verification-detail {
  background: #e8f5e8;
  padding: 30rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #28a745;
}

.verification-item {
  display: block;
  font-size: 24rpx;
  color: #155724;
  margin-bottom: 15rpx;
}

.ancient-evidence {
  margin-top: 20rpx;
}

.evidence-title {
  display: block;
  font-size: 22rpx;
  font-weight: bold;
  color: #155724;
  margin-bottom: 10rpx;
}

.evidence-item {
  display: block;
  font-size: 22rpx;
  color: #155724;
  line-height: 1.6;
  margin-bottom: 10rpx;
  font-style: italic;
  padding-left: 20rpx;
  border-left: 2rpx solid #28a745;
}
