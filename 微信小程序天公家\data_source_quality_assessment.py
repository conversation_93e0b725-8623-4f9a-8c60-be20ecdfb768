#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源质量评估工具
评估4933条原始规则和古籍资料的质量分布，制定最优数据提取策略
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Tuple
from collections import Counter, defaultdict

class DataSourceQualityAssessment:
    def __init__(self):
        # 高质量标准（基于现有38条规则）
        self.quality_standards = {
            "文本清晰率": 0.816,  # ≥ 81.6%
            "置信度": 0.92,       # ≥ 0.92
            "结构完整性": 1.0,    # 100%
            "最小文本长度": 50,   # 最少50字符
            "必需字段": ["rule_id", "pattern_name", "category", "original_text", "interpretations"]
        }
        
        # 数据源配置
        self.data_sources = {
            "原始规则": "classical_rules_complete.json",
            "基础清理": "classical_rules_complete_cleaned.json", 
            "高质量示例": "classical_rules_advanced_cleaned.json",
            "当前高质量": "classical_rules_advanced_complete.json"
        }
        
        # 古籍资料配置
        self.ancient_books = {
            "千里命稿": {"file": "千里命稿.txt", "format": "txt", "priority": 1},
            "三命通会": {"file": "《三命通会》完整白话版  .pdf", "format": "pdf", "priority": 2},
            "五行精纪": {"file": "五行精纪.docx", "format": "docx", "priority": 3},
            "渊海子平": {"file": "渊海子平.docx", "format": "docx", "priority": 3},
            "滴天髓": {"file": "滴天髓.txt", "format": "txt", "priority": 2},
            "穷通宝鉴": {"file": "穷通宝鉴.txt", "format": "txt", "priority": 2}
        }
        
        self.assessment_results = {}
        
    def assess_original_4933_rules(self) -> Dict:
        """评估4933条原始规则的质量分布"""
        print("📊 评估4933条原始规则质量...")
        
        try:
            with open(self.data_sources["原始规则"], 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            total_rules = len(rules)
            
            # 质量分析指标
            quality_metrics = {
                "文本长度分布": defaultdict(int),
                "置信度分布": defaultdict(int),
                "分类分布": defaultdict(int),
                "文本质量": {
                    "清晰文本": 0,
                    "OCR错误": 0,
                    "文本截断": 0,
                    "格式混乱": 0
                },
                "结构完整性": {
                    "完整字段": 0,
                    "缺失字段": 0
                },
                "可用性评估": {
                    "高质量": 0,
                    "中等质量": 0,
                    "低质量": 0,
                    "不可用": 0
                }
            }
            
            # OCR错误模式
            ocr_errors = ['氺', '灬', '釒', '本', '士', '沂水易士注']
            
            for rule in rules:
                # 文本长度分析
                text = rule.get('original_text', '')
                text_len = len(text)
                if text_len < 50:
                    quality_metrics["文本长度分布"]["<50字符"] += 1
                elif text_len < 100:
                    quality_metrics["文本长度分布"]["50-100字符"] += 1
                elif text_len < 200:
                    quality_metrics["文本长度分布"]["100-200字符"] += 1
                else:
                    quality_metrics["文本长度分布"]["≥200字符"] += 1
                
                # 置信度分析
                confidence = rule.get('confidence', 0)
                if confidence >= 0.95:
                    quality_metrics["置信度分布"]["≥0.95"] += 1
                elif confidence >= 0.90:
                    quality_metrics["置信度分布"]["0.90-0.95"] += 1
                elif confidence >= 0.85:
                    quality_metrics["置信度分布"]["0.85-0.90"] += 1
                else:
                    quality_metrics["置信度分布"]["<0.85"] += 1
                
                # 分类分析
                category = rule.get('category', '未分类')
                quality_metrics["分类分布"][category] += 1
                
                # 文本质量分析
                has_ocr_error = any(error in text for error in ocr_errors)
                is_truncated = text.endswith('...') or len(text) < 30
                is_messy = '例如：' in text or text.count('。') < 1
                
                if has_ocr_error:
                    quality_metrics["文本质量"]["OCR错误"] += 1
                if is_truncated:
                    quality_metrics["文本质量"]["文本截断"] += 1
                if is_messy:
                    quality_metrics["文本质量"]["格式混乱"] += 1
                if not (has_ocr_error or is_truncated or is_messy) and len(text) > 50:
                    quality_metrics["文本质量"]["清晰文本"] += 1
                
                # 结构完整性分析
                required_fields = self.quality_standards["必需字段"]
                has_all_fields = all(field in rule and rule[field] for field in required_fields)
                
                if has_all_fields:
                    quality_metrics["结构完整性"]["完整字段"] += 1
                else:
                    quality_metrics["结构完整性"]["缺失字段"] += 1
                
                # 综合可用性评估
                quality_score = 0
                if confidence >= 0.92:
                    quality_score += 3
                elif confidence >= 0.85:
                    quality_score += 2
                elif confidence >= 0.80:
                    quality_score += 1
                
                if len(text) >= 100 and not has_ocr_error and not is_truncated:
                    quality_score += 2
                elif len(text) >= 50:
                    quality_score += 1
                
                if has_all_fields:
                    quality_score += 1
                
                if quality_score >= 5:
                    quality_metrics["可用性评估"]["高质量"] += 1
                elif quality_score >= 3:
                    quality_metrics["可用性评估"]["中等质量"] += 1
                elif quality_score >= 1:
                    quality_metrics["可用性评估"]["低质量"] += 1
                else:
                    quality_metrics["可用性评估"]["不可用"] += 1
            
            # 计算百分比
            for category in quality_metrics:
                if isinstance(quality_metrics[category], dict):
                    for key in quality_metrics[category]:
                        count = quality_metrics[category][key]
                        percentage = (count / total_rules) * 100
                        quality_metrics[category][key] = {
                            "数量": count,
                            "百分比": f"{percentage:.1f}%"
                        }
            
            assessment = {
                "总规则数": total_rules,
                "质量指标": quality_metrics,
                "高质量规则估算": quality_metrics["可用性评估"]["高质量"]["数量"],
                "可用规则估算": (quality_metrics["可用性评估"]["高质量"]["数量"] + 
                              quality_metrics["可用性评估"]["中等质量"]["数量"]),
                "推荐策略": self._generate_original_rules_strategy(quality_metrics)
            }
            
            return assessment
            
        except Exception as e:
            print(f"❌ 评估原始规则失败: {e}")
            return {"error": str(e)}
    
    def assess_ancient_books_quality(self) -> Dict:
        """评估古籍资料的质量和可用性"""
        print("📚 评估古籍资料质量...")
        
        books_dir = "古籍资料"
        assessment = {}
        
        for book_name, book_config in self.ancient_books.items():
            file_path = os.path.join(books_dir, book_config["file"])
            
            if not os.path.exists(file_path):
                assessment[book_name] = {"error": "文件不存在"}
                continue
            
            try:
                # 获取文件基本信息
                file_size = os.path.getsize(file_path)
                file_info = {
                    "文件大小": f"{file_size / 1024:.1f} KB",
                    "格式": book_config["format"],
                    "优先级": book_config["priority"]
                }
                
                # 根据格式分析内容
                if book_config["format"] == "txt":
                    content_analysis = self._analyze_txt_content(file_path)
                elif book_config["format"] == "docx":
                    content_analysis = self._analyze_docx_content(file_path)
                elif book_config["format"] == "pdf":
                    content_analysis = self._analyze_pdf_content(file_path)
                else:
                    content_analysis = {"error": "不支持的格式"}
                
                assessment[book_name] = {
                    "文件信息": file_info,
                    "内容分析": content_analysis,
                    "提取潜力": self._evaluate_extraction_potential(content_analysis, book_name)
                }
                
            except Exception as e:
                assessment[book_name] = {"error": f"分析失败: {e}"}
        
        return assessment
    
    def _analyze_txt_content(self, file_path: str) -> Dict:
        """分析TXT文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            non_empty_lines = [line.strip() for line in lines if line.strip()]
            
            # 内容结构分析
            structure_keywords = ['第', '章', '节', '篇', '格', '论', '用神', '五行']
            structured_lines = sum(1 for line in non_empty_lines 
                                 if any(keyword in line for keyword in structure_keywords))
            
            # 理论密度分析
            theory_keywords = ['格局', '用神', '五行', '十神', '大运', '流年', '神煞', '调候']
            theory_lines = sum(1 for line in non_empty_lines 
                             if any(keyword in line for keyword in theory_keywords))
            
            return {
                "总字符数": len(content),
                "总行数": len(lines),
                "有效行数": len(non_empty_lines),
                "结构化程度": f"{(structured_lines/len(non_empty_lines)*100):.1f}%",
                "理论密度": f"{(theory_lines/len(non_empty_lines)*100):.1f}%",
                "平均行长": sum(len(line) for line in non_empty_lines) / len(non_empty_lines),
                "质量评估": "高" if structured_lines > len(non_empty_lines) * 0.3 else "中"
            }
            
        except Exception as e:
            return {"error": f"TXT分析失败: {e}"}
    
    def _analyze_docx_content(self, file_path: str) -> Dict:
        """分析DOCX文件内容"""
        try:
            # 尝试导入docx库
            try:
                from docx import Document
            except ImportError:
                return {"error": "缺少python-docx库"}
            
            doc = Document(file_path)
            paragraphs = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
            
            total_text = '\n'.join(paragraphs)
            
            # 类似TXT的分析
            structure_keywords = ['第', '章', '节', '篇', '格', '论', '用神', '五行']
            structured_paras = sum(1 for para in paragraphs 
                                 if any(keyword in para for keyword in structure_keywords))
            
            theory_keywords = ['格局', '用神', '五行', '十神', '大运', '流年', '神煞', '调候']
            theory_paras = sum(1 for para in paragraphs 
                             if any(keyword in para for keyword in theory_keywords))
            
            return {
                "总字符数": len(total_text),
                "段落数": len(paragraphs),
                "结构化程度": f"{(structured_paras/len(paragraphs)*100):.1f}%",
                "理论密度": f"{(theory_paras/len(paragraphs)*100):.1f}%",
                "平均段落长": sum(len(para) for para in paragraphs) / len(paragraphs),
                "质量评估": "高" if structured_paras > len(paragraphs) * 0.2 else "中"
            }
            
        except Exception as e:
            return {"error": f"DOCX分析失败: {e}"}
    
    def _analyze_pdf_content(self, file_path: str) -> Dict:
        """分析PDF文件内容"""
        try:
            # 尝试导入PyPDF2库
            try:
                import PyPDF2
            except ImportError:
                return {"error": "缺少PyPDF2库"}
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                # 只分析前10页来评估质量
                sample_text = ""
                for i in range(min(10, total_pages)):
                    sample_text += pdf_reader.pages[i].extract_text()
                
                lines = sample_text.split('\n')
                non_empty_lines = [line.strip() for line in lines if line.strip()]
                
                # 检查PDF提取质量
                garbled_lines = sum(1 for line in non_empty_lines 
                                  if len(re.findall(r'[^\u4e00-\u9fff\w\s]', line)) > len(line) * 0.3)
                
                theory_keywords = ['格局', '用神', '五行', '十神', '大运', '流年', '神煞', '调候']
                theory_lines = sum(1 for line in non_empty_lines 
                                 if any(keyword in line for keyword in theory_keywords))
                
                return {
                    "总页数": total_pages,
                    "样本字符数": len(sample_text),
                    "样本行数": len(non_empty_lines),
                    "乱码率": f"{(garbled_lines/len(non_empty_lines)*100):.1f}%",
                    "理论密度": f"{(theory_lines/len(non_empty_lines)*100):.1f}%",
                    "质量评估": "低" if garbled_lines > len(non_empty_lines) * 0.2 else "中"
                }
                
        except Exception as e:
            return {"error": f"PDF分析失败: {e}"}
    
    def _evaluate_extraction_potential(self, content_analysis: Dict, book_name: str) -> Dict:
        """评估提取潜力"""
        if "error" in content_analysis:
            return {"潜力": "无法评估", "原因": content_analysis["error"]}
        
        # 基于内容分析评估提取潜力
        quality = content_analysis.get("质量评估", "低")
        theory_density = float(content_analysis.get("理论密度", "0%").rstrip('%'))
        
        if quality == "高" and theory_density > 20:
            potential = "极高"
            estimated_rules = 800
        elif quality == "高" and theory_density > 10:
            potential = "高"
            estimated_rules = 500
        elif quality == "中" and theory_density > 15:
            potential = "中等"
            estimated_rules = 300
        elif quality == "中" and theory_density > 5:
            potential = "较低"
            estimated_rules = 150
        else:
            potential = "低"
            estimated_rules = 50
        
        return {
            "潜力": potential,
            "预估规则数": estimated_rules,
            "推荐优先级": self.ancient_books[book_name]["priority"],
            "提取建议": self._generate_extraction_advice(potential, book_name)
        }
    
    def _generate_extraction_advice(self, potential: str, book_name: str) -> str:
        """生成提取建议"""
        advice_map = {
            "极高": f"强烈推荐优先从{book_name}提取，可作为主要数据源",
            "高": f"推荐从{book_name}提取，可获得大量高质量规则",
            "中等": f"可以从{book_name}提取，需要重点筛选质量",
            "较低": f"谨慎从{book_name}提取，需要大量后处理",
            "低": f"不推荐从{book_name}提取，投入产出比低"
        }
        return advice_map.get(potential, "需要进一步评估")
    
    def _generate_original_rules_strategy(self, quality_metrics: Dict) -> Dict:
        """生成原始规则使用策略"""
        high_quality = quality_metrics["可用性评估"]["高质量"]["数量"]
        medium_quality = quality_metrics["可用性评估"]["中等质量"]["数量"]
        
        if high_quality > 1000:
            strategy = "优先策略"
            description = "高质量规则充足，建议优先从4933条中筛选"
        elif high_quality + medium_quality > 2000:
            strategy = "混合策略"
            description = "中等质量规则较多，建议筛选+古籍补充"
        else:
            strategy = "补充策略"
            description = "高质量规则不足，建议以古籍提取为主"
        
        return {
            "推荐策略": strategy,
            "策略描述": description,
            "可用高质量": high_quality,
            "可用中等质量": medium_quality
        }
    
    def generate_comprehensive_assessment(self) -> Dict:
        """生成综合评估报告"""
        print("🔍 开始综合数据源质量评估...")
        
        # 评估原始规则
        original_assessment = self.assess_original_4933_rules()
        
        # 评估古籍资料
        books_assessment = self.assess_ancient_books_quality()
        
        # 生成数据提取策略
        extraction_strategy = self._generate_optimal_strategy(original_assessment, books_assessment)
        
        comprehensive_report = {
            "评估时间": datetime.now().isoformat(),
            "原始4933条规则评估": original_assessment,
            "古籍资料评估": books_assessment,
            "最优数据提取策略": extraction_strategy,
            "质量标准": self.quality_standards,
            "实施建议": self._generate_implementation_recommendations(extraction_strategy)
        }
        
        return comprehensive_report
    
    def _generate_optimal_strategy(self, original_assessment: Dict, books_assessment: Dict) -> Dict:
        """生成最优数据提取策略"""
        if "error" in original_assessment:
            return {"策略": "仅古籍提取", "原因": "原始规则无法访问"}
        
        high_quality_original = original_assessment.get("高质量规则估算", 0)
        usable_original = original_assessment.get("可用规则估算", 0)
        
        # 计算古籍提取潜力
        total_book_potential = 0
        high_potential_books = 0
        
        for book_name, assessment in books_assessment.items():
            if "error" not in assessment:
                potential = assessment.get("提取潜力", {}).get("预估规则数", 0)
                total_book_potential += potential
                
                if assessment.get("提取潜力", {}).get("潜力") in ["极高", "高"]:
                    high_potential_books += 1
        
        # 决策逻辑
        if high_quality_original >= 1500 and usable_original >= 3000:
            strategy = "原始规则优先策略"
            description = "4933条原始规则质量较高，优先筛选使用"
            primary_source = "原始规则筛选"
            secondary_source = "古籍补充"
        elif high_quality_original >= 800 and total_book_potential >= 2000:
            strategy = "混合提取策略"
            description = "原始规则和古籍资料并重，分层提取"
            primary_source = "原始规则+古籍并行"
            secondary_source = "质量优化"
        elif total_book_potential >= 3000 and high_potential_books >= 2:
            strategy = "古籍重构策略"
            description = "古籍资料质量更高，重新构建规则库"
            primary_source = "古籍重新提取"
            secondary_source = "原始规则参考"
        else:
            strategy = "渐进式构建策略"
            description = "数据源质量有限，需要渐进式构建"
            primary_source = "多源混合"
            secondary_source = "质量严格控制"
        
        return {
            "推荐策略": strategy,
            "策略描述": description,
            "主要数据源": primary_source,
            "辅助数据源": secondary_source,
            "原始规则可用性": {
                "高质量": high_quality_original,
                "可用总数": usable_original,
                "推荐使用": high_quality_original >= 800
            },
            "古籍资料可用性": {
                "总提取潜力": total_book_potential,
                "高潜力书籍": high_potential_books,
                "推荐使用": total_book_potential >= 2000
            }
        }
    
    def _generate_implementation_recommendations(self, strategy: Dict) -> List[str]:
        """生成实施建议"""
        recommendations = [
            "基于数据源质量评估的实施建议:",
            f"1. 采用{strategy['推荐策略']}",
            f"2. 主要数据源: {strategy['主要数据源']}",
            f"3. 辅助数据源: {strategy['辅助数据源']}"
        ]
        
        strategy_name = strategy['推荐策略']
        
        if "原始规则优先" in strategy_name:
            recommendations.extend([
                "4. 优先从4933条原始规则中筛选高质量规则",
                "5. 建立严格的质量筛选标准和流程",
                "6. 用古籍资料补充特定领域的规则缺口"
            ])
        elif "混合提取" in strategy_name:
            recommendations.extend([
                "4. 并行进行原始规则筛选和古籍提取",
                "5. 建立统一的质量标准和验证机制",
                "6. 优先处理高质量数据源"
            ])
        elif "古籍重构" in strategy_name:
            recommendations.extend([
                "4. 重点从高质量古籍中重新提取规则",
                "5. 用原始规则作为参考和验证",
                "6. 建立完整的古籍解析和处理流程"
            ])
        else:
            recommendations.extend([
                "4. 采用多源混合的渐进式构建方法",
                "5. 严格控制每个阶段的质量标准",
                "6. 建立持续的质量监控和改进机制"
            ])
        
        return recommendations

def main():
    """主函数"""
    assessor = DataSourceQualityAssessment()
    
    # 生成综合评估报告
    report = assessor.generate_comprehensive_assessment()
    
    # 保存详细报告
    with open("data_source_quality_assessment_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印关键信息
    print("\n" + "="*80)
    print("📊 数据源质量评估报告")
    print("="*80)
    
    if "error" not in report["原始4933条规则评估"]:
        original = report["原始4933条规则评估"]
        print(f"\n📋 原始4933条规则:")
        print(f"  高质量规则: {original['高质量规则估算']}条")
        print(f"  可用规则: {original['可用规则估算']}条")
        print(f"  推荐策略: {original['推荐策略']['推荐策略']}")
    
    print(f"\n📚 古籍资料评估:")
    books = report["古籍资料评估"]
    for book_name, assessment in books.items():
        if "error" not in assessment:
            potential = assessment.get("提取潜力", {})
            print(f"  {book_name}: {potential.get('潜力', '未知')} ({potential.get('预估规则数', 0)}条)")
    
    strategy = report["最优数据提取策略"]
    print(f"\n🎯 推荐策略: {strategy['推荐策略']}")
    print(f"策略描述: {strategy['策略描述']}")
    
    print(f"\n📊 详细评估报告已保存到: data_source_quality_assessment_report.json")

if __name__ == "__main__":
    main()
