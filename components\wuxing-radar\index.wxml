<!--components/wuxing-radar/index.wxml-->
<!-- 五行雷达图组件 -->
<view class="wuxing-radar-container">
  <view class="radar-header">
    <text class="radar-title">五行力量分布</text>
    <text class="radar-subtitle">直观展示您八字中五行元素的强弱分布</text>
  </view>
  
  <!-- 雷达图画布 -->
  <view class="radar-canvas-wrapper">
    <canvas 
      canvas-id="wuxingRadarCanvas" 
      id="wuxingRadarCanvas"
      class="radar-canvas"
      bindtouchstart="onCanvasTouch"
      bindtouchmove="onCanvasTouch"
      bindtouchend="onCanvasTouch">
    </canvas>
    
    <!-- 中心显示区域 -->
    <view class="radar-center-info">
      <text class="center-title">综合评分</text>
      <text class="center-score">{{totalScore}}</text>
    </view>
  </view>
  
  <!-- 五行图例 -->
  <view class="radar-legend">
    <view wx:for="{{wuxingList}}" wx:key="element" class="legend-item">
      <view class="legend-color" style="background-color: {{item.color}};"></view>
      <text class="legend-name">{{item.name}}</text>
      <text class="legend-score">{{item.score}}分</text>
      <text class="legend-strength">{{item.strength}}</text>
    </view>
  </view>
  
  <!-- 详细数据展示 -->
  <view wx:if="{{showDetails}}" class="radar-details">
    <view class="details-header">
      <text class="details-title">详细数据</text>
    </view>
    <view class="details-grid">
      <view wx:for="{{wuxingList}}" wx:key="element" class="detail-item">
        <view class="detail-icon" style="color: {{item.color}};">{{item.symbol}}</view>
        <view class="detail-info">
          <text class="detail-name">{{item.name}}</text>
          <text class="detail-score">{{item.score}}/100</text>
          <view class="detail-bar">
            <view class="detail-fill" style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
          </view>
          <text class="detail-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
