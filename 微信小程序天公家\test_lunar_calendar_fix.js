/**
 * 农历显示修复测试
 * 验证农历年份和日期的正确显示
 */

const UnifiedBasicInfoCalculator = require('./utils/unified_basic_info_calculator.js');

console.log('🌙 农历显示修复测试');
console.log('='.repeat(60));

const calculator = new UnifiedBasicInfoCalculator();

// 测试用例
const testCases = [
  {
    name: '测试1 - 1990年5月15日',
    birthInfo: {
      year: 1990,
      month: 5,
      day: 15,
      hour: 14,
      minute: 30,
      name: '张三',
      gender: '男'
    },
    expected: {
      lunarYear: '庚午',
      zodiac: '马',
      lunarDay: '十五'
    }
  },
  {
    name: '测试2 - 1985年12月25日',
    birthInfo: {
      year: 1985,
      month: 12,
      day: 25,
      hour: 8,
      minute: 15,
      name: '李四',
      gender: '女'
    },
    expected: {
      lunarYear: '乙丑',
      zodiac: '牛',
      lunarDay: '廿五'
    }
  },
  {
    name: '测试3 - 2000年1月1日',
    birthInfo: {
      year: 2000,
      month: 1,
      day: 1,
      hour: 0,
      minute: 0,
      name: '王五',
      gender: '男'
    },
    expected: {
      lunarYear: '庚辰',
      zodiac: '龙',
      lunarDay: '初一'
    }
  },
  {
    name: '测试4 - 1995年8月4日',
    birthInfo: {
      year: 1995,
      month: 8,
      day: 4,
      hour: 10,
      minute: 30,
      name: '赵六',
      gender: '女'
    },
    expected: {
      lunarYear: '乙亥',
      zodiac: '猪',
      lunarDay: '初四'
    }
  },
  {
    name: '测试5 - 1982年6月21日',
    birthInfo: {
      year: 1982,
      month: 6,
      day: 21,
      hour: 15,
      minute: 45,
      name: '孙七',
      gender: '男'
    },
    expected: {
      lunarYear: '壬戌',
      zodiac: '狗',
      lunarDay: '廿一'
    }
  }
];

console.log('🧪 开始农历显示测试...\n');

testCases.forEach((testCase, index) => {
  console.log(`🔍 ${testCase.name}`);
  console.log('-'.repeat(50));
  
  try {
    const result = calculator.calculate(testCase.birthInfo, [
      { gan: '甲', zhi: '子' },
      { gan: '丙', zhi: '寅' },
      { gan: '戊', zhi: '午' },
      { gan: '壬', zhi: '戌' }
    ]);
    
    console.log('📋 计算结果:');
    console.log(`   公历: ${result.birthDate}`);
    console.log(`   农历: ${result.lunar_time}`);
    console.log(`   生肖: ${result.zodiac}`);
    
    // 解析农历显示
    const lunarMatch = result.lunar_time.match(/^(.+)年(.+)月(.+)$/);
    if (lunarMatch) {
      const [, lunarYear, lunarMonth, lunarDay] = lunarMatch;
      
      console.log('🔍 农历解析:');
      console.log(`   农历年份: ${lunarYear}`);
      console.log(`   农历月份: ${lunarMonth}`);
      console.log(`   农历日期: ${lunarDay}`);
      
      // 验证结果
      let allCorrect = true;
      
      // 检查农历年份（天干地支）
      if (lunarYear === testCase.expected.lunarYear) {
        console.log(`✅ 农历年份正确: ${lunarYear}`);
      } else {
        console.log(`❌ 农历年份错误: 期望 ${testCase.expected.lunarYear}, 实际 ${lunarYear}`);
        allCorrect = false;
      }
      
      // 检查生肖
      if (result.zodiac === testCase.expected.zodiac) {
        console.log(`✅ 生肖正确: ${result.zodiac}`);
      } else {
        console.log(`❌ 生肖错误: 期望 ${testCase.expected.zodiac}, 实际 ${result.zodiac}`);
        allCorrect = false;
      }
      
      // 检查农历日期
      if (lunarDay === testCase.expected.lunarDay) {
        console.log(`✅ 农历日期正确: ${lunarDay}`);
      } else {
        console.log(`❌ 农历日期错误: 期望 ${testCase.expected.lunarDay}, 实际 ${lunarDay}`);
        allCorrect = false;
      }
      
      if (allCorrect) {
        console.log('🎉 测试通过');
      } else {
        console.log('⚠️ 测试失败');
      }
      
    } else {
      console.log('❌ 农历格式解析失败');
    }
    
  } catch (error) {
    console.log('❌ 计算异常:', error.message);
  }
  
  console.log('');
});

// 农历日期格式测试
console.log('📅 农历日期格式测试');
console.log('-'.repeat(50));

const lunarDayTests = [
  { day: 1, expected: '初一' },
  { day: 2, expected: '初二' },
  { day: 4, expected: '初四' },
  { day: 10, expected: '初十' },
  { day: 11, expected: '十一' },
  { day: 15, expected: '十五' },
  { day: 20, expected: '二十' },
  { day: 21, expected: '廿一' },
  { day: 25, expected: '廿五' },
  { day: 30, expected: '三十' }
];

lunarDayTests.forEach((test) => {
  const birthInfo = { year: 2000, month: 6, day: test.day, hour: 12, minute: 0 };
  const lunarTime = calculator.calculateLunarTime(birthInfo);
  const dayMatch = lunarTime.match(/(.+)$/);
  const actualDay = dayMatch ? dayMatch[1] : '解析失败';
  
  const isCorrect = actualDay === test.expected;
  const status = isCorrect ? '✅' : '❌';
  console.log(`${status} ${test.day}日 → ${actualDay} (期望: ${test.expected})`);
});

// 天干地支年份测试
console.log('\n🗓️ 天干地支年份测试');
console.log('-'.repeat(50));

const ganZhiTests = [
  { year: 1984, expected: '甲子' },
  { year: 1985, expected: '乙丑' },
  { year: 1990, expected: '庚午' },
  { year: 1995, expected: '乙亥' },
  { year: 2000, expected: '庚辰' },
  { year: 2020, expected: '庚子' },
  { year: 2024, expected: '甲辰' }
];

ganZhiTests.forEach((test) => {
  const birthInfo = { year: test.year, month: 6, day: 15, hour: 12, minute: 0 };
  const lunarTime = calculator.calculateLunarTime(birthInfo);
  const yearMatch = lunarTime.match(/^(.+)年/);
  const actualYear = yearMatch ? yearMatch[1] : '解析失败';
  
  const isCorrect = actualYear === test.expected;
  const status = isCorrect ? '✅' : '❌';
  console.log(`${status} ${test.year}年 → ${actualYear} (期望: ${test.expected})`);
});

// 生肖对应测试
console.log('\n🐲 生肖对应测试');
console.log('-'.repeat(50));

const zodiacTests = [
  { year: 1984, expected: '鼠' },
  { year: 1985, expected: '牛' },
  { year: 1986, expected: '虎' },
  { year: 1987, expected: '兔' },
  { year: 1988, expected: '龙' },
  { year: 1989, expected: '蛇' },
  { year: 1990, expected: '马' },
  { year: 1991, expected: '羊' },
  { year: 1992, expected: '猴' },
  { year: 1993, expected: '鸡' },
  { year: 1994, expected: '狗' },
  { year: 1995, expected: '猪' }
];

zodiacTests.forEach((test) => {
  const zodiac = calculator.calculateZodiac(test.year);
  const isCorrect = zodiac === test.expected;
  const status = isCorrect ? '✅' : '❌';
  console.log(`${status} ${test.year}年 → ${zodiac} (期望: ${test.expected})`);
});

// 修复前后对比
console.log('\n🔄 修复前后对比');
console.log('-'.repeat(50));

const comparisonCase = {
  year: 1995,
  month: 8,
  day: 4
};

console.log('📋 测试用例: 1995年8月4日');
console.log('❌ 修复前: 虎年八月4日 (错误)');

const fixedResult = calculator.calculateLunarTime(comparisonCase);
console.log(`✅ 修复后: ${fixedResult} (正确)`);

console.log('\n🔍 修复内容:');
console.log('1. ❌ "虎年" → ✅ "乙亥年" (正确的天干地支年份)');
console.log('2. ❌ "4日" → ✅ "初四" (正确的农历日期格式)');
console.log('3. ✅ 生肖显示独立: 猪 (与农历年份分开显示)');

console.log('\n📈 农历修复测试总结');
console.log('='.repeat(60));
console.log('✅ 农历年份修复: 生肖年 → 天干地支年');
console.log('✅ 农历日期修复: 阿拉伯数字 → 传统农历格式');
console.log('✅ 生肖显示独立: 单独字段显示生肖');
console.log('✅ 格式规范化: 统一农历显示格式');
console.log('🎉 农历显示修复完成！');

console.log('\n' + '='.repeat(60));
