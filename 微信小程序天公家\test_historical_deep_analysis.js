// test_historical_deep_analysis.js
// 历史案例深度分析测试

const HistoricalCaseAnalyzer = require('./utils/historical_case_analyzer.js');

/**
 * 运行历史案例深度分析
 */
async function runHistoricalDeepAnalysis() {
  console.log('🏛️ 开始历史案例深度分析');
  console.log('=' .repeat(60));

  const analyzer = new HistoricalCaseAnalyzer();
  const cases = ['曾国藩', '李白', '诸葛亮'];
  
  for (const caseName of cases) {
    try {
      console.log(`\n${'='.repeat(60)}`);
      const result = analyzer.analyzeHistoricalCase(caseName);
      
      console.log(`\n📋 ${caseName} 分析总结:`);
      console.log(`传统规则: ${result.traditional_rule}`);
      console.log(`格局成败: ${result.analysis.pattern.success ? '✅ 成格' : '❌ 不成格'}`);
      console.log(`成败原因: ${result.analysis.pattern.formation_rule}`);
      
      if (result.corrections.length > 0) {
        console.log(`\n🔧 修正建议:`);
        result.corrections.forEach((correction, index) => {
          console.log(`${index + 1}. ${correction.type}: ${correction.suggestion}`);
        });
      }
      
    } catch (error) {
      console.error(`❌ 分析${caseName}失败:`, error.message);
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log('🎉 历史案例深度分析完成');
}

// 运行分析
if (require.main === module) {
  runHistoricalDeepAnalysis().catch(console.error);
}

module.exports = { runHistoricalDeepAnalysis };
