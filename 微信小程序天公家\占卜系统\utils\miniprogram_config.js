// utils/config.js - API配置文件
const config = {
  // 本地开发环境
  development: {
    apiBaseUrl: 'http://localhost:8000',
    timeout: 10000,
    debug: true
  },
  
  // 生产环境（部署后使用）
  production: {
    apiBaseUrl: 'https://yourdomain.com',
    timeout: 5000,
    debug: false
  }
};

// 检测当前环境
function getCurrentEnvironment() {
  // 在开发者工具中运行时为开发环境
  try {
    const deviceInfo = wx.getDeviceInfo();
    if (deviceInfo.platform === 'devtools') {
      return 'development';
    }
  } catch (e) {
    console.log('获取系统信息失败，默认为开发环境');
  }

  // 其他情况为生产环境
  return 'production';
}

const currentEnv = getCurrentEnvironment();
const currentConfig = config[currentEnv];

console.log('当前环境:', currentEnv);
console.log('API配置:', currentConfig);

module.exports = currentConfig;
