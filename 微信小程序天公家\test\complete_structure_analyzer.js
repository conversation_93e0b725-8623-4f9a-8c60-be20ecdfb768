/**
 * 完整结构分析器
 * 分析整个WXML文件的标签匹配情况
 */

const fs = require('fs');
const path = require('path');

function analyzeCompleteStructure() {
  console.log('🔍 分析完整WXML文件结构');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📄 文件总行数: ${lines.length}`);
  
  // 分析整个文件的标签匹配
  let depth = 0;
  const tagStack = [];
  const issues = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 匹配所有标签
    const openTags = line.match(/<(\w+)[^>]*>/g) || [];
    const closeTags = line.match(/<\/(\w+)>/g) || [];
    
    // 处理开始标签
    openTags.forEach(tag => {
      const tagName = tag.match(/<(\w+)/)[1];
      // 跳过自闭合标签
      if (!tag.includes('/>')) {
        tagStack.push({ tag: tagName, line: lineNum });
        depth++;
      }
    });
    
    // 处理结束标签
    closeTags.forEach(tag => {
      const tagName = tag.match(/<\/(\w+)>/)[1];
      if (tagStack.length > 0) {
        const lastTag = tagStack.pop();
        if (lastTag.tag !== tagName) {
          issues.push({
            line: lineNum,
            issue: `标签不匹配: 期望 </${lastTag.tag}> 但找到 </${tagName}>`,
            expected: lastTag.tag,
            found: tagName
          });
        }
        depth--;
      } else {
        issues.push({
          line: lineNum,
          issue: `多余的结束标签: </${tagName}>`,
          found: tagName
        });
        depth--;
      }
    });
  }
  
  console.log(`\n📊 分析结果:`);
  console.log(`最终深度: ${depth}`);
  console.log(`未匹配的开始标签: ${tagStack.length}`);
  console.log(`发现的问题: ${issues.length}`);
  
  if (tagStack.length > 0) {
    console.log(`\n❌ 未关闭的标签:`);
    tagStack.forEach(item => {
      console.log(`  第${item.line}行: <${item.tag}>`);
    });
  }
  
  if (issues.length > 0) {
    console.log(`\n❌ 发现的问题:`);
    issues.forEach(issue => {
      console.log(`  第${issue.line}行: ${issue.issue}`);
    });
  }
  
  // 检查关键结构
  const containerStart = lines.findIndex(line => line.includes('<view class="tianggong-container">'));
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`\n📍 关键结构位置:`);
  console.log(`tianggong-container 开始: 第${containerStart + 1}行`);
  console.log(`scroll-view 开始: 第${scrollViewStart + 1}行`);
  console.log(`scroll-view 结束: 第${scrollViewEnd + 1}行`);
  
  // 检查文件结尾
  console.log(`\n📄 文件结尾结构:`);
  for (let i = Math.max(0, lines.length - 8); i < lines.length; i++) {
    const line = lines[i];
    console.log(`${i + 1}: ${line}`);
  }
  
  // 给出修复建议
  if (depth > 0) {
    console.log(`\n🔧 修复建议:`);
    console.log(`需要在文件末尾添加 ${depth} 个结束标签`);
    
    if (tagStack.length > 0) {
      console.log(`建议添加的标签:`);
      for (let i = tagStack.length - 1; i >= 0; i--) {
        console.log(`  </${tagStack[i].tag}>`);
      }
    }
  } else if (depth < 0) {
    console.log(`\n🔧 修复建议:`);
    console.log(`有 ${Math.abs(depth)} 个多余的结束标签需要删除`);
  } else {
    console.log(`\n✅ 标签匹配正确`);
  }
}

// 运行分析
if (require.main === module) {
  analyzeCompleteStructure();
}

module.exports = { analyzeCompleteStructure };
