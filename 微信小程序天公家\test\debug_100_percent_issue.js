/**
 * 深度调试100%能量值问题
 * 追踪从五行数据到前端显示的完整数据流
 */

// 模拟当前的计算逻辑
function debugEnergyCalculationFlow() {
  console.log('🧪 ===== 深度调试100%能量值问题 =====\n');
  
  // 模拟真实的五行数据（基于用户实际数据）
  const realElementEnergies = {
    金: 45.2,
    木: 23.8, 
    水: 67.1,
    火: 34.6,
    土: 29.3
  };
  
  console.log('📋 输入五行数据:');
  console.log(`  金: ${realElementEnergies.金}`);
  console.log(`  木: ${realElementEnergies.木}`);
  console.log(`  水: ${realElementEnergies.水}`);
  console.log(`  火: ${realElementEnergies.火}`);
  console.log(`  土: ${realElementEnergies.土}`);
  console.log(`  总和: ${Object.values(realElementEnergies).reduce((sum, val) => sum + val, 0).toFixed(1)}`);
  
  // 当前的阈值配置
  const currentThresholds = {
    marriage: 0.60,    // 60%
    promotion: 0.60,   // 60%
    childbirth: 0.60,  // 60%
    wealth: 0.60       // 60%
  };
  
  console.log('\n📊 当前阈值配置:');
  Object.entries(currentThresholds).forEach(([eventType, threshold]) => {
    console.log(`  ${eventType}: ${(threshold * 100).toFixed(1)}%`);
  });
  
  // 当前的权重配置
  const currentWeights = {
    marriage: { 金: 0.35, 木: 0.05, 水: 0.15, 火: 0.35, 土: 0.10 },
    promotion: { 金: 0.40, 木: 0.15, 水: 0.35, 火: 0.05, 土: 0.05 },
    childbirth: { 金: 0.05, 木: 0.35, 水: 0.40, 火: 0.15, 土: 0.05 },
    wealth: { 金: 0.15, 木: 0.35, 火: 0.35, 土: 0.15, 水: 0.00 }
  };
  
  console.log('\n🔍 逐步计算过程分析:');
  
  const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
  
  eventTypes.forEach(eventType => {
    console.log(`\n📈 ${eventType}能量计算详细过程:`);
    
    const weights = currentWeights[eventType];
    const threshold = currentThresholds[eventType];
    
    console.log(`  权重配置: 金${weights.金} 木${weights.木} 水${weights.水} 火${weights.火} 土${weights.土}`);
    
    // 第1步：计算加权能量
    const weightedEnergy = realElementEnergies.金 * weights.金 + 
                          realElementEnergies.木 * weights.木 + 
                          realElementEnergies.水 * weights.水 + 
                          realElementEnergies.火 * weights.火 + 
                          realElementEnergies.土 * weights.土;
    
    console.log(`  第1步 - 加权计算:`);
    console.log(`    ${realElementEnergies.金}×${weights.金} + ${realElementEnergies.木}×${weights.木} + ${realElementEnergies.水}×${weights.水} + ${realElementEnergies.火}×${weights.火} + ${realElementEnergies.土}×${weights.土}`);
    console.log(`    = ${weightedEnergy.toFixed(2)}`);
    
    // 第2步：标准化（这里可能有问题）
    const maxPossibleEnergy = 100; // 当前设定的理论最大值
    const normalizedEnergy = (weightedEnergy / maxPossibleEnergy) * 100;
    
    console.log(`  第2步 - 标准化:`);
    console.log(`    (${weightedEnergy.toFixed(2)} ÷ ${maxPossibleEnergy}) × 100 = ${normalizedEnergy.toFixed(2)}%`);
    
    // 🚨 问题分析：如果weightedEnergy > 100，normalizedEnergy就会 > 100%
    if (normalizedEnergy > 100) {
      console.log(`    ⚠️ 问题：标准化后超过100%！`);
    }
    
    // 第3步：平衡度调整
    const balance = calculateBalance(realElementEnergies);
    const balanceAdjustment = (balance - 50) * 0.2;
    
    console.log(`  第3步 - 平衡度调整:`);
    console.log(`    平衡度: ${balance.toFixed(1)}%`);
    console.log(`    调整值: (${balance.toFixed(1)} - 50) × 0.2 = ${balanceAdjustment.toFixed(2)}%`);
    
    // 第4步：最终能量
    const finalEnergyBeforeLimit = normalizedEnergy + balanceAdjustment;
    const finalEnergy = Math.max(0, Math.min(100, finalEnergyBeforeLimit));
    
    console.log(`  第4步 - 最终能量:`);
    console.log(`    调整前: ${normalizedEnergy.toFixed(2)}% + ${balanceAdjustment.toFixed(2)}% = ${finalEnergyBeforeLimit.toFixed(2)}%`);
    console.log(`    调整后: Math.min(100, ${finalEnergyBeforeLimit.toFixed(2)}) = ${finalEnergy.toFixed(2)}%`);
    
    // 第5步：达标判定
    const met = finalEnergy >= (threshold * 100);
    
    console.log(`  第5步 - 达标判定:`);
    console.log(`    ${finalEnergy.toFixed(2)}% >= ${(threshold * 100).toFixed(1)}% = ${met ? '✅ 达标' : '❌ 未达标'}`);
    
    // 🚨 关键问题分析
    console.log(`  🚨 问题分析:`);
    
    if (finalEnergy === 100) {
      console.log(`    ❌ 能量值被限制在100%，可能原始计算超过100%`);
      console.log(`    ❌ 原始计算: ${finalEnergyBeforeLimit.toFixed(2)}%`);
    }
    
    if (met && finalEnergy === 100) {
      console.log(`    ❌ 100%达标可能是因为能量计算错误，而非真实达标`);
    }
    
    // 理论最大值分析
    const theoreticalMax = Math.max(...Object.values(realElementEnergies)) * Math.max(...Object.values(weights));
    console.log(`    理论单项最大: ${Math.max(...Object.values(realElementEnergies)).toFixed(1)} × ${Math.max(...Object.values(weights)).toFixed(2)} = ${theoreticalMax.toFixed(1)}`);
    
    if (theoreticalMax > 100) {
      console.log(`    ❌ 理论最大值超过100，标准化基准有误`);
    }
  });
  
  console.log('\n🎯 根本问题诊断:');
  
  // 问题1：阈值统一性
  const uniqueThresholds = [...new Set(Object.values(currentThresholds))];
  console.log(`  问题1 - 阈值统一: ${uniqueThresholds.length === 1 ? '❌ 所有阈值相同' : '✅ 阈值有差异'}`);
  console.log(`    当前阈值: ${uniqueThresholds.map(t => (t * 100).toFixed(1) + '%').join(', ')}`);
  
  // 问题2：标准化基准
  const maxWeightedEnergies = eventTypes.map(eventType => {
    const weights = currentWeights[eventType];
    return Math.max(...Object.values(realElementEnergies)) * Object.values(weights).reduce((sum, w) => sum + w, 0);
  });
  
  const maxPossibleWeighted = Math.max(...maxWeightedEnergies);
  console.log(`  问题2 - 标准化基准: 当前100，实际最大可能${maxPossibleWeighted.toFixed(1)}`);
  
  if (maxPossibleWeighted > 100) {
    console.log(`    ❌ 标准化基准过低，导致能量值超过100%`);
  }
  
  // 问题3：权重合理性
  eventTypes.forEach(eventType => {
    const weights = currentWeights[eventType];
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    if (Math.abs(totalWeight - 1.0) > 0.01) {
      console.log(`  问题3 - ${eventType}权重总和: ${totalWeight.toFixed(3)} (应该是1.0)`);
    }
  });
  
  console.log('\n💡 修正建议:');
  console.log('  1. 阈值差异化: 不同维度设置不同阈值');
  console.log('  2. 标准化修正: 基准应该是实际可能的最大值');
  console.log('  3. 能量上限: 避免能量值达到100%的虚假满值');
  console.log('  4. 权重验证: 确保所有权重总和为1.0');
  
  return {
    hasUniformThresholds: uniqueThresholds.length === 1,
    hasStandardizationIssue: maxPossibleWeighted > 100,
    maxPossibleWeighted: maxPossibleWeighted,
    currentThresholds: currentThresholds
  };
}

// 计算五行平衡度
function calculateBalance(elementEnergies) {
  const values = Object.values(elementEnergies);
  const average = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length;
  const standardDeviation = Math.sqrt(variance);
  
  // 标准差越小，平衡度越高
  const balance = Math.max(0, Math.min(100, 100 - standardDeviation * 2));
  return balance;
}

// 运行调试
debugEnergyCalculationFlow();
