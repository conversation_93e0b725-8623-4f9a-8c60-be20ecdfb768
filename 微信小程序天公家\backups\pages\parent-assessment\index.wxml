<!-- pages/parent-assessment/index.wxml -->
<view class="chat-container">
  <!-- 顶部导航栏 -->
  <view class="custom-nav-bar">
    <view class="nav-bar-title">心理评估小程序</view>
    <view class="nav-bar-right">
      <view class="menu-icon">
        <text>...</text>
      </view>
      <view class="settings-icon" bindtap="navigateToProfile" hover-class="icon-hover">
        <image src="/assets/icons/new/profile_icon.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  
  <!-- 标签选择器 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 'today' ? 'active' : ''}}" bindtap="switchTab" data-tab="today">今天</view>
    <view class="tab {{activeTab === 'history' ? 'active' : ''}}" bindtap="navigateToHistory" data-tab="history">历史</view>
  </view>
  
  <!-- 日期显示 -->
  <view class="date-display">{{currentDate}} <text class="date-dot">•</text> 家长测评</view>
  
  <!-- 测评进度条 -->
  <view class="progress-container" wx:if="{{assessmentProgress > 0 && !assessmentComplete}}">
    <view class="progress-status">
      <text class="progress-title">测评进度</text>
      <text class="progress-count">{{currentQuestionIndex + 1}}/{{totalQuestions}}</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{assessmentProgress}}%"></view>
    </view>
    <view class="progress-text">{{assessmentProgress}}%</view>
  </view>
  
  <!-- 欢迎消息区域 -->
  <view class="welcome-message" wx:if="{{messages.length === 0}}">
    <view class="welcome-title">{{welcomeTitle}}</view>
    <view class="welcome-subtitle">{{welcomeSubtitle}}</view>
    <view class="welcome-hint">
      <image src="/assets/icons/time.svg" mode="aspectFit" class="hint-icon"></image>
      <text>大约需要5-10分钟</text>
    </view>
    <view class="welcome-start-button" bindtap="sendMessage">
      <text>开始评估</text>
      <image src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 消息区域 -->
  <scroll-view class="message-area" scroll-y scroll-into-view="{{lastMessageId}}" id="message-container" enhanced="true" show-scrollbar="{{false}}" bounces="{{true}}">
    <block wx:for="{{messages}}" wx:key="id">
      <view class="message-wrapper {{item.role === 'AI' ? 'ai-wrapper' : 'user-wrapper'}}" id="msg-{{item.id}}">
        <view wx:if="{{item.role === 'AI'}}" class="avatar-container">
          <image src="/assets/icons/new/avatar_icon.svg" class="avatar" mode="aspectFill"></image>
        </view>
        
        <!-- 智能识别选项题 -->
        <block wx:if="{{item.role === 'AI' && item.content.includes('A.') && item.content.includes('B.')}}">
          <view class="message ai-message options-message">
            <view class="options-title">{{item.content.split('\n\n')[0]}}</view>
            <view class="options-list">
              <view class="option-item" wx:for="{{item.parsedOptions}}" wx:for-item="option" wx:key="index">
                <view class="option-content">{{option}}</view>
              </view>
            </view>
          </view>
        </block>
        
        <!-- 普通消息 -->
        <block wx:else>
          <view class="message {{item.role === 'AI' ? 'ai-message' : 'user-message'}} {{item.emotionType ? 'emotion-' + item.emotionType : ''}}">
            <view class="message-content">{{item.content}}</view>
            <!-- 情绪反馈指示器 -->
            <view wx:if="{{item.role === 'User' && item.emotionType}}" class="emotion-indicator {{item.emotionType}}">
              <image wx:if="{{item.emotionType === 'positive'}}" src="/assets/icons/emotion-positive.svg" mode="aspectFit" class="emotion-icon"></image>
              <image wx:if="{{item.emotionType === 'negative'}}" src="/assets/icons/emotion-negative.svg" mode="aspectFit" class="emotion-icon"></image>
              <image wx:if="{{item.emotionType === 'neutral'}}" src="/assets/icons/emotion-neutral.svg" mode="aspectFit" class="emotion-icon"></image>
            </view>
          </view>
        </block>
        
        <view class="message-time" wx:if="{{item.time}}">{{item.time}}</view>
      </view>
    </block>
    
    <!-- 正在输入指示器 -->
    <view class="typing-wrapper" wx:if="{{typing}}">
      <view class="avatar-container">
        <image src="/assets/icons/new/avatar_icon.svg" class="avatar" mode="aspectFill"></image>
      </view>
      <view class="typing-indicator">
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
      </view>
    </view>
    
    <!-- 底部空白，确保滚动到底部有足够空间 -->
    <view class="message-bottom-space"></view>
  </scroll-view>
  
  <!-- 底部输入区域 -->
  <view class="input-area {{assessmentComplete ? 'input-completed' : ''}}">
    <input class="message-input" placeholder="输入您的回答..." value="{{inputValue}}" bindinput="onInputChange" 
      disabled="{{assessmentComplete || sending}}" focus="{{messages.length > 0 && !assessmentComplete && !sending}}" 
      confirm-type="send" bindconfirm="sendMessage" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage" hover-class="button-hover">
      <image src="/assets/icons/new/send_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="voice-button" hover-class="button-hover">
      <image src="/assets/icons/new/voice_icon.svg" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 评估完成浮动按钮 -->
  <view class="action-button-container" wx:if="{{assessmentComplete}}">
    <view class="action-button view-report" bindtap="viewFullReport">
      <image src="/assets/icons/report.svg" mode="aspectFit"></image>
      <text>查看完整报告</text>
    </view>
    <view class="action-button restart" bindtap="restartAssessment">
      <image src="/assets/icons/assessment.svg" mode="aspectFit"></image>
      <text>重新开始</text>
    </view>
  </view>
</view>

<!-- 引导提示 -->
<view class="guide-tooltip {{messages.length === 1 ? 'show' : ''}}" wx:if="{{messages.length === 1}}">
  <view class="tooltip-content">
    <text>输入您的回答，帮助我了解孩子的家庭生活情况</text>
    <view class="tooltip-arrow"></view>
  </view>
</view>

<!-- 智能提示气泡 -->
<view class="smart-tip {{showSmartTip ? 'show' : ''}}" wx:if="{{showSmartTip}}">
  <view class="tooltip-content">
    <text>{{smartTipContent}}</text>
    <view class="tooltip-arrow"></view>
  </view>
</view> 