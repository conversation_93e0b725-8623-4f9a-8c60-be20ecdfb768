/**
 * 数据库合并工具
 * 将多个名人数据库文件合并为完整数据库
 */

const mainDatabase = require('../data/historical_celebrities_database.js');
const extendedDatabase = require('../data/extended_celebrities_database.js');
const suiTangBatch1 = require('../data/sui_tang_celebrities_batch1_complete.js');
const batchGeneratedDatabase = require('../data/batch_generated_celebrities.js');
const fs = require('fs');
const path = require('path');

class DatabaseMerger {
  constructor() {
    this.mergedDatabase = {
      metadata: {
        version: "2.0.0",
        lastUpdated: new Date().toISOString().split('T')[0],
        totalRecords: 0,
        description: "完整的历史名人命理数据库",
        dataSources: [],
        verificationStandards: "专家交叉校验+古籍依据双重认证"
      },
      celebrities: []
    };
  }

  /**
   * 合并所有数据库
   */
  mergeAllDatabases() {
    console.log('🔄 开始合并数据库...');
    
    // 合并主数据库
    if (mainDatabase && mainDatabase.celebrities) {
      console.log(`📚 合并主数据库: ${mainDatabase.celebrities.length} 位名人`);
      this.mergedDatabase.celebrities.push(...mainDatabase.celebrities);
      
      // 合并数据源
      if (mainDatabase.metadata && mainDatabase.metadata.dataSources) {
        this.mergedDatabase.metadata.dataSources.push(...mainDatabase.metadata.dataSources);
      }
    }

    // 合并扩展数据库
    if (extendedDatabase && extendedDatabase.celebrities) {
      console.log(`📚 合并扩展数据库: ${extendedDatabase.celebrities.length} 位名人`);
      this.mergedDatabase.celebrities.push(...extendedDatabase.celebrities);

      // 合并数据源
      if (extendedDatabase.metadata && extendedDatabase.metadata.dataSources) {
        const newSources = extendedDatabase.metadata.dataSources.filter(
          source => !this.mergedDatabase.metadata.dataSources.includes(source)
        );
        this.mergedDatabase.metadata.dataSources.push(...newSources);
      }
    }

    // 合并批量生成数据库
    if (batchGeneratedDatabase && batchGeneratedDatabase.celebrities) {
      console.log(`📚 合并批量生成数据库: ${batchGeneratedDatabase.celebrities.length} 位名人`);
      this.mergedDatabase.celebrities.push(...batchGeneratedDatabase.celebrities);
    }

    // 合并隋唐五代数据库
    if (suiTangBatch1 && suiTangBatch1.celebrities) {
      console.log(`📚 合并隋唐五代第一批次: ${suiTangBatch1.celebrities.length} 位名人`);
      this.mergedDatabase.celebrities.push(...suiTangBatch1.celebrities);

      // 添加隋唐数据源
      const suiTangSources = ["《隋书》", "《旧唐书》", "《新唐书》", "《资治通鉴》"];
      suiTangSources.forEach(source => {
        if (!this.mergedDatabase.metadata.dataSources.includes(source)) {
          this.mergedDatabase.metadata.dataSources.push(source);
        }
      });
    }

    // 去重数据源
    this.mergedDatabase.metadata.dataSources = [...new Set(this.mergedDatabase.metadata.dataSources)];
    
    // 更新总记录数
    this.mergedDatabase.metadata.totalRecords = this.mergedDatabase.celebrities.length;
    
    console.log(`✅ 合并完成: 总计 ${this.mergedDatabase.metadata.totalRecords} 位名人`);
    console.log(`📖 数据源: ${this.mergedDatabase.metadata.dataSources.length} 个古籍文献`);
    
    return this.mergedDatabase;
  }

  /**
   * 验证合并后的数据
   */
  validateMergedData() {
    console.log('\n🔍 验证合并后的数据...');
    
    const issues = [];
    const idSet = new Set();
    
    this.mergedDatabase.celebrities.forEach((celebrity, index) => {
      // 检查ID重复
      if (idSet.has(celebrity.id)) {
        issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
      } else {
        idSet.add(celebrity.id);
      }
      
      // 检查必要字段
      if (!celebrity.basicInfo?.name) {
        issues.push(`缺少姓名: 索引 ${index}`);
      }
      
      if (!celebrity.bazi?.fullBazi) {
        issues.push(`缺少八字: ${celebrity.basicInfo?.name || '未知'} (索引: ${index})`);
      }
      
      if (!celebrity.pattern?.mainPattern) {
        issues.push(`缺少格局: ${celebrity.basicInfo?.name || '未知'} (索引: ${index})`);
      }
      
      if (!celebrity.verification?.algorithmMatch) {
        issues.push(`缺少验证度: ${celebrity.basicInfo?.name || '未知'} (索引: ${index})`);
      }
    });
    
    if (issues.length === 0) {
      console.log('✅ 数据验证通过，未发现问题');
    } else {
      console.log(`❌ 发现 ${issues.length} 个问题:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    return issues;
  }

  /**
   * 生成统计报告
   */
  generateStatistics() {
    console.log('\n📊 生成统计报告...');
    
    const stats = {
      totalCelebrities: this.mergedDatabase.celebrities.length,
      dynastyDistribution: {},
      patternDistribution: {},
      occupationDistribution: {},
      verificationScores: [],
      averageVerificationScore: 0
    };
    
    this.mergedDatabase.celebrities.forEach(celebrity => {
      // 朝代分布
      const dynasty = celebrity.basicInfo?.dynasty || '未知';
      stats.dynastyDistribution[dynasty] = (stats.dynastyDistribution[dynasty] || 0) + 1;
      
      // 格局分布
      const pattern = celebrity.pattern?.mainPattern || '未知';
      stats.patternDistribution[pattern] = (stats.patternDistribution[pattern] || 0) + 1;
      
      // 职业分布
      if (celebrity.basicInfo?.occupation) {
        celebrity.basicInfo.occupation.forEach(occupation => {
          stats.occupationDistribution[occupation] = (stats.occupationDistribution[occupation] || 0) + 1;
        });
      }
      
      // 验证分数
      if (celebrity.verification?.algorithmMatch) {
        stats.verificationScores.push(celebrity.verification.algorithmMatch);
      }
    });
    
    // 计算平均验证分数
    if (stats.verificationScores.length > 0) {
      stats.averageVerificationScore = stats.verificationScores.reduce((a, b) => a + b, 0) / stats.verificationScores.length;
    }
    
    console.log(`👥 总名人数: ${stats.totalCelebrities}`);
    console.log(`⭐ 平均验证度: ${stats.averageVerificationScore.toFixed(3)}`);
    console.log(`🏛️ 朝代覆盖: ${Object.keys(stats.dynastyDistribution).length} 个朝代`);
    console.log(`🎭 格局覆盖: ${Object.keys(stats.patternDistribution).length} 种格局`);
    console.log(`👤 职业覆盖: ${Object.keys(stats.occupationDistribution).length} 种职业`);
    
    return stats;
  }

  /**
   * 保存合并后的数据库
   */
  saveMergedDatabase(outputPath = '../data/complete_celebrities_database.js') {
    console.log('\n💾 保存合并后的数据库...');
    
    const fullPath = path.resolve(__dirname, outputPath);
    const content = `/**
 * 完整历史名人数据库
 * 合并所有名人数据的完整版本
 * 自动生成于 ${new Date().toISOString()}
 */

const completeCelebritiesDatabase = ${JSON.stringify(this.mergedDatabase, null, 2)};

module.exports = completeCelebritiesDatabase;
`;
    
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ 数据库已保存到: ${fullPath}`);
    
    return fullPath;
  }

  /**
   * 导出为JSON格式
   */
  exportToJSON(outputPath = '../exports/celebrities_database.json') {
    console.log('\n📄 导出JSON格式...');
    
    const fullPath = path.resolve(__dirname, outputPath);
    
    // 确保目录存在
    const dir = path.dirname(fullPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(fullPath, JSON.stringify(this.mergedDatabase, null, 2), 'utf8');
    console.log(`✅ JSON文件已导出到: ${fullPath}`);
    
    return fullPath;
  }

  /**
   * 运行完整的合并流程
   */
  runMergeProcess() {
    console.log('🚀 开始数据库合并流程');
    console.log('============================================================');
    
    try {
      // 合并数据库
      this.mergeAllDatabases();
      
      // 验证数据
      const issues = this.validateMergedData();
      
      // 生成统计
      const stats = this.generateStatistics();
      
      // 保存文件
      const jsPath = this.saveMergedDatabase();
      const jsonPath = this.exportToJSON();
      
      console.log('\n🎉 数据库合并流程完成!');
      console.log('============================================================');
      console.log(`📊 统计摘要:`);
      console.log(`   - 总名人数: ${stats.totalCelebrities}`);
      console.log(`   - 平均验证度: ${stats.averageVerificationScore.toFixed(3)}`);
      console.log(`   - 数据质量: ${issues.length === 0 ? '优秀' : '需要改进'}`);
      console.log(`📁 输出文件:`);
      console.log(`   - JavaScript: ${jsPath}`);
      console.log(`   - JSON: ${jsonPath}`);
      
      return {
        success: true,
        database: this.mergedDatabase,
        statistics: stats,
        issues: issues,
        outputFiles: { js: jsPath, json: jsonPath }
      };
      
    } catch (error) {
      console.error('❌ 合并流程失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = DatabaseMerger;
