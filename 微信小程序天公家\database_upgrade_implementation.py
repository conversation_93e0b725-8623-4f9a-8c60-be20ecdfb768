#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库升级实施方案
基于维度分析结果，制定具体的规则提取和数据库升级计划
"""

import json
import os
from datetime import datetime
from typing import Dict, List

class DatabaseUpgradeImplementation:
    def __init__(self):
        # 基于分析结果的具体升级计划
        self.upgrade_phases = {
            "第一阶段": {
                "目标": "从38条扩展到88条规则 (新增50条)",
                "时间": "1-2周",
                "优先级": "立即处理",
                "重点模块": ["数字化分析", "每日指南核心"],
                "具体任务": [
                    {
                        "任务": "五行力量计算规则",
                        "来源": "千里命稿强弱篇、五行精纪",
                        "目标数量": 14,
                        "关键词": ["五行生克", "旺相休囚死", "月令", "藏干"],
                        "提取重点": "五行力量计算的具体方法和判断标准"
                    },
                    {
                        "任务": "五行平衡指数规则",
                        "来源": "千里命稿调候篇、穷通宝鉴",
                        "目标数量": 9,
                        "关键词": ["调候", "平衡", "中和", "偏枯"],
                        "提取重点": "五行平衡状态的量化标准"
                    },
                    {
                        "任务": "十神关系规则",
                        "来源": "千里命稿十神篇、渊海子平",
                        "目标数量": 15,
                        "关键词": ["十神", "生克", "关系", "作用"],
                        "提取重点": "十神之间的相互作用关系"
                    },
                    {
                        "任务": "日柱互动分析规则",
                        "来源": "千里命稿日主篇",
                        "目标数量": 12,
                        "关键词": ["日主", "日柱", "互动", "影响"],
                        "提取重点": "日柱与其他柱位的互动关系"
                    }
                ]
            },
            
            "第二阶段": {
                "目标": "从88条扩展到138条规则 (新增50条)",
                "时间": "3-4周",
                "优先级": "短期补充",
                "重点模块": ["匹配分析基础", "专业分析完善"],
                "具体任务": [
                    {
                        "任务": "基础匹配理论规则",
                        "来源": "三命通会合婚篇、千里命稿配偶篇",
                        "目标数量": 20,
                        "关键词": ["合婚", "配偶", "匹配", "六亲"],
                        "提取重点": "基础的八字匹配理论和方法"
                    },
                    {
                        "任务": "格局分析规则",
                        "来源": "千里命稿格局篇、渊海子平",
                        "目标数量": 15,
                        "关键词": ["格局", "成格", "破格", "变格"],
                        "提取重点": "各种格局的成立条件和判断方法"
                    },
                    {
                        "任务": "神煞分析规则",
                        "来源": "三命通会神煞篇、滴天髓",
                        "目标数量": 15,
                        "关键词": ["神煞", "贵人", "凶煞", "作用"],
                        "提取重点": "神煞的作用机制和影响分析"
                    }
                ]
            },
            
            "第三阶段": {
                "目标": "从138条扩展到372条规则 (新增234条)",
                "时间": "5-6周",
                "优先级": "全面完善",
                "重点模块": ["匹配分析完整", "每日指南完整", "专业分析完整"],
                "具体任务": [
                    {
                        "任务": "15个匹配维度规则",
                        "来源": "各大古籍综合",
                        "目标数量": 60,
                        "关键词": ["五行互补", "用神互补", "神煞配合", "十神关系", "藏干配合"],
                        "提取重点": "匹配分析的15个维度的详细理论"
                    },
                    {
                        "任务": "场景化建议规则",
                        "来源": "千里命稿应用篇、三命通会实例",
                        "目标数量": 30,
                        "关键词": ["事业", "财运", "感情", "健康", "建议"],
                        "提取重点": "具体生活场景的命理指导"
                    },
                    {
                        "任务": "运程分析规则",
                        "来源": "千里命稿大运篇、流年篇",
                        "目标数量": 30,
                        "关键词": ["大运", "流年", "运势", "变化"],
                        "提取重点": "大运流年的分析方法和预测理论"
                    },
                    {
                        "任务": "古籍依据规则",
                        "来源": "各古籍原文精选",
                        "目标数量": 40,
                        "关键词": ["原文", "依据", "理论", "实例"],
                        "提取重点": "高质量的古籍原文和理论依据"
                    },
                    {
                        "任务": "18种关系类型规则",
                        "来源": "三命通会、渊海子平关系篇",
                        "目标数量": 35,
                        "关键词": ["婚姻", "合作", "朋友", "亲子", "师徒"],
                        "提取重点": "不同关系类型的匹配特点"
                    },
                    {
                        "任务": "用神体系完善规则",
                        "来源": "千里命稿用神篇、滴天髓",
                        "目标数量": 22,
                        "关键词": ["用神", "喜神", "忌神", "通关"],
                        "提取重点": "完整的用神理论体系"
                    },
                    {
                        "任务": "神煞影响规则",
                        "来源": "三命通会神煞篇详细版",
                        "目标数量": 17,
                        "关键词": ["神煞影响", "每日", "作用", "化解"],
                        "提取重点": "神煞对日常生活的具体影响"
                    }
                ]
            }
        }
        
        # 提取工具配置
        self.extraction_tools = {
            "高级提取器": "advanced_classical_rules_extractor.py",
            "专项提取器": {},  # 将为每个阶段创建专项提取器
            "质量验证器": "rule_quality_validator.py"
        }
        
    def generate_phase_extractor(self, phase_name: str, phase_config: Dict) -> str:
        """为每个阶段生成专项提取器代码"""
        extractor_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{phase_name}专项规则提取器
{phase_config["目标"]}
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class {phase_name.replace("第", "Phase").replace("阶段", "")}Extractor:
    def __init__(self):
        self.rule_id_counter = 1
        self.target_tasks = {json.dumps(phase_config["具体任务"], ensure_ascii=False, indent=8)}
        
    def extract_phase_rules(self, books_dir: str = "古籍资料") -> Dict:
        """提取{phase_name}所需规则"""
        all_rules = []
        
        for task in self.target_tasks:
            print(f"正在提取: {{task['任务']}}")
            task_rules = self.extract_task_rules(task, books_dir)
            all_rules.extend(task_rules)
            print(f"提取了 {{len(task_rules)}} 条规则")
        
        metadata = {{
            "phase": "{phase_name}",
            "export_date": datetime.now().isoformat(),
            "total_rules": len(all_rules),
            "target_count": {sum(task["目标数量"] for task in phase_config["具体任务"])},
            "completion_rate": f"{{len(all_rules)/sum(task['目标数量'] for task in self.target_tasks)*100:.1f}}%"
        }}
        
        return {{
            "metadata": metadata,
            "rules": all_rules
        }}
    
    def extract_task_rules(self, task: Dict, books_dir: str) -> List[Dict]:
        """提取单个任务的规则"""
        rules = []
        keywords = task["关键词"]
        target_count = task["目标数量"]
        
        # 根据来源确定要处理的文件
        source_files = self.get_source_files(task["来源"], books_dir)
        
        for file_path in source_files:
            if not os.path.exists(file_path):
                continue
                
            content = self.load_file_content(file_path)
            file_rules = self.extract_rules_from_content(
                content, keywords, task, file_path
            )
            rules.extend(file_rules)
            
            if len(rules) >= target_count:
                break
        
        return rules[:target_count]  # 限制数量
    
    def get_source_files(self, sources: str, books_dir: str) -> List[str]:
        """根据来源获取文件路径"""
        files = []
        source_mapping = {{
            "千里命稿": "千里命稿.txt",
            "三命通会": "《三命通会》完整白话版  .pdf",
            "五行精纪": "五行精纪.docx",
            "渊海子平": "渊海子平.docx",
            "滴天髓": "滴天髓.txt",
            "穷通宝鉴": "穷通宝鉴.txt"
        }}
        
        for source in sources.split("、"):
            source = source.strip()
            for key, filename in source_mapping.items():
                if key in source:
                    files.append(os.path.join(books_dir, filename))
        
        return files
    
    def load_file_content(self, file_path: str) -> str:
        """加载文件内容"""
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_path.endswith('.docx'):
                # 需要docx库
                try:
                    from docx import Document
                    doc = Document(file_path)
                    return "\\n".join([p.text for p in doc.paragraphs])
                except ImportError:
                    print(f"跳过 {{file_path}} - 缺少docx库")
                    return ""
            else:
                return ""
        except Exception as e:
            print(f"加载文件失败 {{file_path}}: {{e}}")
            return ""
    
    def extract_rules_from_content(self, content: str, keywords: List[str], 
                                 task: Dict, file_path: str) -> List[Dict]:
        """从内容中提取规则"""
        rules = []
        
        # 按关键词搜索相关段落
        for keyword in keywords:
            pattern = rf'[^。]*{{keyword}}[^。]*。'
            matches = re.findall(pattern, content)
            
            for match in matches:
                if len(match.strip()) > 20:  # 确保有足够内容
                    rule = {{
                        "rule_id": f"{{task['任务']}}_{{self.rule_id_counter:03d}}",
                        "pattern_name": task["任务"],
                        "category": self.get_category_from_task(task["任务"]),
                        "book_source": os.path.basename(file_path).split('.')[0],
                        "original_text": self.clean_text(match),
                        "interpretations": task["提取重点"],
                        "confidence": 0.92,
                        "conditions": f"与{{keyword}}相关的条件",
                        "advanced_cleaned": True,
                        "advanced_cleaned_at": datetime.now().isoformat(),
                        "extraction_phase": "{phase_name}",
                        "target_keyword": keyword
                    }}
                    rules.append(rule)
                    self.rule_id_counter += 1
        
        return rules
    
    def get_category_from_task(self, task_name: str) -> str:
        """根据任务名称确定分类"""
        category_mapping = {{
            "五行": "强弱判断",
            "十神": "用神理论", 
            "格局": "正格",
            "神煞": "神煞格局",
            "匹配": "六亲关系",
            "运程": "大运流年",
            "调候": "调候格局"
        }}
        
        for key, category in category_mapping.items():
            if key in task_name:
                return category
        
        return "综合理论"
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        text = re.sub(r'\\s+', ' ', text)
        text = text.replace('，', '，').replace('。', '。')
        return text.strip()
    
    def save_rules(self, data: Dict, filename: str):
        """保存规则到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"{{data['metadata']['phase']}}规则已保存到: {{filename}}")

def main():
    """主函数"""
    extractor = {phase_name.replace("第", "Phase").replace("阶段", "")}Extractor()
    
    # 提取规则
    data = extractor.extract_phase_rules()
    
    # 保存结果
    filename = f"classical_rules_{phase_name.lower().replace('第', 'phase_').replace('阶段', '')}.json"
    extractor.save_rules(data, filename)

if __name__ == "__main__":
    main()
'''
        return extractor_code
    
    def create_quality_validator(self) -> str:
        """创建规则质量验证器"""
        validator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则质量验证器
验证提取的规则是否符合质量标准
"""

import json
from typing import Dict, List, Tuple

class RuleQualityValidator:
    def __init__(self):
        self.quality_standards = {
            "最小文本长度": 30,
            "最低置信度": 0.85,
            "必需字段": ["rule_id", "pattern_name", "category", "original_text", "interpretations"],
            "文本质量检查": True,
            "重复检查": True
        }
    
    def validate_rules(self, rules: List[Dict]) -> Tuple[List[Dict], List[str]]:
        """验证规则质量"""
        valid_rules = []
        issues = []
        
        for i, rule in enumerate(rules):
            rule_issues = self.validate_single_rule(rule, i)
            
            if not rule_issues:
                valid_rules.append(rule)
            else:
                issues.extend(rule_issues)
        
        return valid_rules, issues
    
    def validate_single_rule(self, rule: Dict, index: int) -> List[str]:
        """验证单条规则"""
        issues = []
        
        # 检查必需字段
        for field in self.quality_standards["必需字段"]:
            if field not in rule or not rule[field]:
                issues.append(f"规则{index}: 缺少必需字段 {field}")
        
        # 检查文本长度
        original_text = rule.get("original_text", "")
        if len(original_text) < self.quality_standards["最小文本长度"]:
            issues.append(f"规则{index}: 文本长度不足 ({len(original_text)}字符)")
        
        # 检查置信度
        confidence = rule.get("confidence", 0)
        if confidence < self.quality_standards["最低置信度"]:
            issues.append(f"规则{index}: 置信度过低 ({confidence})")
        
        # 检查文本质量
        if self.quality_standards["文本质量检查"]:
            text_issues = self.check_text_quality(original_text)
            if text_issues:
                issues.extend([f"规则{index}: {issue}" for issue in text_issues])
        
        return issues
    
    def check_text_quality(self, text: str) -> List[str]:
        """检查文本质量"""
        issues = []
        
        # 检查OCR错误
        ocr_errors = ['氺', '灬', '釒', '本', '士']
        if any(error in text for error in ocr_errors):
            issues.append("包含OCR错误字符")
        
        # 检查文本完整性
        if text.endswith('...') or '...' in text:
            issues.append("文本可能不完整")
        
        # 检查标点符号
        if text.count('。') < 1:
            issues.append("缺少句号，可能不是完整句子")
        
        return issues
    
    def generate_quality_report(self, rules: List[Dict], issues: List[str]) -> str:
        """生成质量报告"""
        total_rules = len(rules) + len([i for i in issues if "规则" in i])
        valid_rules = len(rules)
        
        report = f"""
规则质量验证报告
================
总规则数: {total_rules}
有效规则数: {valid_rules}
通过率: {valid_rules/total_rules*100:.1f}%

质量问题:
"""
        for issue in issues[:10]:  # 只显示前10个问题
            report += f"- {issue}\\n"
        
        if len(issues) > 10:
            report += f"... 还有 {len(issues)-10} 个问题\\n"
        
        return report

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python rule_quality_validator.py <规则文件.json>")
        return
    
    filename = sys.argv[1]
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        validator = RuleQualityValidator()
        valid_rules, issues = validator.validate_rules(data.get('rules', []))
        
        # 更新数据
        data['rules'] = valid_rules
        data['metadata']['validated'] = True
        data['metadata']['validation_issues'] = len(issues)
        
        # 保存验证后的数据
        validated_filename = filename.replace('.json', '_validated.json')
        with open(validated_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 生成报告
        report = validator.generate_quality_report(valid_rules, issues)
        print(report)
        
        print(f"验证后的规则已保存到: {validated_filename}")
        
    except Exception as e:
        print(f"验证失败: {e}")

if __name__ == "__main__":
    main()
'''
        return validator_code
    
    def generate_implementation_plan(self) -> str:
        """生成完整的实施计划"""
        plan = []
        plan.append("=" * 80)
        plan.append("数据库升级实施方案")
        plan.append("=" * 80)
        plan.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        plan.append("")
        
        plan.append("📋 升级概览")
        plan.append("-" * 50)
        plan.append("当前状态: 38条规则，覆盖率10.2%")
        plan.append("目标状态: 372条规则，覆盖率100%")
        plan.append("升级策略: 三阶段渐进式升级")
        plan.append("预计时间: 5-6周")
        plan.append("")
        
        for phase_name, phase_config in self.upgrade_phases.items():
            plan.append(f"🚀 {phase_name}")
            plan.append("-" * 50)
            plan.append(f"目标: {phase_config['目标']}")
            plan.append(f"时间: {phase_config['时间']}")
            plan.append(f"优先级: {phase_config['优先级']}")
            plan.append(f"重点模块: {', '.join(phase_config['重点模块'])}")
            plan.append("")
            
            plan.append("具体任务:")
            for i, task in enumerate(phase_config["具体任务"], 1):
                plan.append(f"  {i}. {task['任务']} ({task['目标数量']}条)")
                plan.append(f"     来源: {task['来源']}")
                plan.append(f"     关键词: {', '.join(task['关键词'])}")
                plan.append(f"     重点: {task['提取重点']}")
                plan.append("")
        
        plan.append("🛠️ 实施步骤")
        plan.append("-" * 50)
        plan.append("1. 创建阶段性提取器")
        plan.append("   - 为每个阶段生成专项提取器")
        plan.append("   - 配置目标任务和关键词")
        plan.append("   - 设置质量标准和验证机制")
        plan.append("")
        plan.append("2. 执行规则提取")
        plan.append("   - 按阶段顺序执行提取")
        plan.append("   - 实时监控提取质量")
        plan.append("   - 及时调整提取策略")
        plan.append("")
        plan.append("3. 质量验证和优化")
        plan.append("   - 运行质量验证器")
        plan.append("   - 修复质量问题")
        plan.append("   - 优化规则内容")
        plan.append("")
        plan.append("4. 集成测试")
        plan.append("   - 测试系统功能覆盖")
        plan.append("   - 验证分析准确性")
        plan.append("   - 优化用户体验")
        plan.append("")
        
        plan.append("📊 预期效果")
        plan.append("-" * 50)
        plan.append("第一阶段完成后:")
        plan.append("  - 数字化分析功能基本可用")
        plan.append("  - 每日指南核心功能可用")
        plan.append("  - 系统覆盖率达到23.7%")
        plan.append("")
        plan.append("第二阶段完成后:")
        plan.append("  - 匹配分析基础功能可用")
        plan.append("  - 专业分析功能完善")
        plan.append("  - 系统覆盖率达到37.1%")
        plan.append("")
        plan.append("第三阶段完成后:")
        plan.append("  - 所有功能模块完整可用")
        plan.append("  - 系统功能全面覆盖")
        plan.append("  - 系统覆盖率达到100%")
        plan.append("")
        
        return "\n".join(plan)
    
    def create_all_extractors(self):
        """创建所有阶段的提取器"""
        for phase_name, phase_config in self.upgrade_phases.items():
            extractor_code = self.generate_phase_extractor(phase_name, phase_config)
            filename = f"{phase_name.lower().replace('第', 'phase_').replace('阶段', '')}_extractor.py"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(extractor_code)
            
            print(f"✅ 创建了 {phase_name} 提取器: {filename}")
        
        # 创建质量验证器
        validator_code = self.create_quality_validator()
        with open("rule_quality_validator.py", 'w', encoding='utf-8') as f:
            f.write(validator_code)
        
        print("✅ 创建了质量验证器: rule_quality_validator.py")
    
    def save_implementation_plan(self, plan: str, filename: str = "database_upgrade_implementation_plan.txt"):
        """保存实施计划"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(plan)
        print(f"📋 实施计划已保存到: {filename}")

def main():
    """主函数"""
    implementation = DatabaseUpgradeImplementation()
    
    # 生成实施计划
    plan = implementation.generate_implementation_plan()
    print(plan)
    
    # 保存实施计划
    implementation.save_implementation_plan(plan)
    
    # 创建所有提取器
    implementation.create_all_extractors()
    
    print("\n🎯 下一步操作:")
    print("1. 运行 python phase_1_extractor.py 开始第一阶段提取")
    print("2. 运行 python rule_quality_validator.py <生成的文件> 进行质量验证")
    print("3. 重复执行后续阶段的提取器")

if __name__ == "__main__":
    main()
