/**
 * 最终错误检查
 * 验证用户报告的两个错误是否已解决
 */

const fs = require('fs');
const path = require('path');

function finalErrorCheck() {
  console.log('🔍 最终错误检查');
  
  // 检查1: WXML编译错误
  console.log('\n📄 检查WXML编译错误:');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`文件总行数: ${lines.length}`);
  
  // 检查原错误位置 (第2660行)
  const originalErrorLine = 2660;
  if (originalErrorLine <= lines.length) {
    console.log(`❌ 原错误行(${originalErrorLine})仍存在: ${lines[originalErrorLine - 1]?.trim()}`);
  } else {
    console.log(`✅ 原错误行(${originalErrorLine})已消除 (文件现在只有${lines.length}行)`);
  }
  
  // 检查scroll-view结构
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  if (scrollViewEnd >= 0) {
    console.log(`✅ scroll-view结束标签在第${scrollViewEnd + 1}行`);
    
    // 检查scroll-view后面的结构
    console.log(`scroll-view后面的结构:`);
    for (let i = scrollViewEnd; i < Math.min(scrollViewEnd + 5, lines.length); i++) {
      console.log(`  ${i + 1}: ${lines[i]}`);
    }
  } else {
    console.log(`❌ 未找到scroll-view结束标签`);
  }
  
  // 检查2: JavaScript运行时错误
  console.log('\n📄 检查JavaScript运行时错误:');
  
  const jsPath = path.join(__dirname, '../pages/bazi-result/index.js');
  const jsContent = fs.readFileSync(jsPath, 'utf8');
  
  // 检查getShenshaCalculator方法中的null安全检查
  const hasNullCheck = jsContent.includes('if (page && page.route && page.route.includes');
  if (hasNullCheck) {
    console.log(`✅ getShenshaCalculator方法已添加null安全检查`);
  } else {
    console.log(`❌ getShenshaCalculator方法缺少null安全检查`);
  }
  
  // 检查getCurrentPages的使用
  const getCurrentPagesUsage = jsContent.match(/getCurrentPages\(\)/g) || [];
  console.log(`getCurrentPages使用次数: ${getCurrentPagesUsage.length}`);
  
  // 总结
  console.log('\n🎯 错误修复状态总结:');
  
  const wxmlFixed = originalErrorLine > lines.length && scrollViewEnd >= 0;
  const jsFixed = hasNullCheck;
  
  console.log(`${wxmlFixed ? '✅' : '❌'} WXML编译错误: ${wxmlFixed ? '已修复' : '未修复'}`);
  console.log(`${jsFixed ? '✅' : '❌'} JavaScript运行时错误: ${jsFixed ? '已修复' : '未修复'}`);
  
  if (wxmlFixed && jsFixed) {
    console.log('\n🎉 所有报告的错误都已修复！');
    console.log('用户现在应该可以在微信开发者工具中正常编译和运行小程序了。');
    return true;
  } else {
    console.log('\n❌ 仍有错误需要解决');
    return false;
  }
}

// 运行检查
if (require.main === module) {
  finalErrorCheck();
}

module.exports = { finalErrorCheck };
