/**
 * 调试缺失模块问题
 * 检查数据计算和传递流程
 */

console.log('🔍 开始调试缺失模块问题');
console.log('='.repeat(60));

// 模拟测试数据
const testBirthInfo = {
  year: 2006,
  month: 7,
  day: 23,
  hour: 14,
  minute: 30,
  gender: '男',
  longitude: 116.4074,
  latitude: 39.9042
};

// 模拟四柱数据
const mockFourPillars = [
  { gan: '丙', zhi: '戌' }, // 年柱
  { gan: '乙', zhi: '未' }, // 月柱
  { gan: '癸', zhi: '丑' }, // 日柱
  { gan: '己', zhi: '未' }  // 时柱
];

console.log('\n📋 测试数据:');
console.log('出生信息:', testBirthInfo);
console.log('四柱数据:', mockFourPillars);

// 1. 测试纳音计算
console.log('\n🎵 1. 测试纳音五行计算');
console.log('-'.repeat(40));

function testNayinCalculation() {
  // 六十甲子纳音表
  const nayinTable = {
    '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火',
    '戊辰': '大林木', '己巳': '大林木', '庚午': '路旁土', '辛未': '路旁土',
    '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
    '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城头土', '己卯': '城头土',
    '庚辰': '白蜡金', '辛巳': '白蜡金', '壬午': '杨柳木', '癸未': '杨柳木',
    '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
    '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木',
    '壬辰': '长流水', '癸巳': '长流水', '甲午': '砂中金', '乙未': '砂中金',
    '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
    '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金',
    '甲辰': '覆灯火', '乙巳': '覆灯火', '丙午': '天河水', '丁未': '天河水',
    '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
    '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水',
    '丙辰': '沙中土', '丁巳': '沙中土', '戊午': '天上火', '己未': '天上火',
    '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
  };

  const result = {};
  const pillarNames = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar'];
  const pillarLabels = ['年柱', '月柱', '日柱', '时柱'];

  mockFourPillars.forEach((pillar, index) => {
    const ganzhi = pillar.gan + pillar.zhi;
    const nayin = nayinTable[ganzhi] || '未知';
    result[pillarNames[index]] = nayin;
    console.log(`   ${pillarLabels[index]} ${ganzhi}: ${nayin}`);
  });

  console.log('✅ 纳音计算完成');
  return result;
}

const nayinResult = testNayinCalculation();

// 2. 测试长生十二宫计算
console.log('\n⭐ 2. 测试长生十二宫计算');
console.log('-'.repeat(40));

function testChangshengCalculation() {
  const dayGan = mockFourPillars[2].gan; // 癸
  console.log('日主天干:', dayGan);

  // 癸干的长生十二宫映射
  const changshengMap = {
    '癸': { 
      '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', 
      '亥': '帝旺', '戌': '衰', '酉': '病', '申': '死', 
      '未': '墓', '午': '绝', '巳': '胎', '辰': '养' 
    }
  };

  const result = {};
  const pillarNames = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar'];
  const pillarLabels = ['年柱', '月柱', '日柱', '时柱'];

  const dayGanMap = changshengMap[dayGan];

  mockFourPillars.forEach((pillar, index) => {
    const changshengState = dayGanMap[pillar.zhi] || '未知';
    result[pillarNames[index]] = changshengState;
    console.log(`   ${pillarLabels[index]} ${pillar.gan}${pillar.zhi}: ${changshengState}`);
  });

  console.log('✅ 长生十二宫计算完成');
  return result;
}

const changshengResult = testChangshengCalculation();

// 3. 测试自坐分析
console.log('\n🎯 3. 测试自坐分析');
console.log('-'.repeat(40));

function testSelfSittingAnalysis() {
  const dayPillar = mockFourPillars[2]; // 癸丑
  const dayGan = dayPillar.gan;
  const dayZhi = dayPillar.zhi;

  console.log('日柱:', dayGan + dayZhi);

  // 简化的自坐分析
  const selfSittingMap = {
    '癸丑': '癸水坐丑土，正官坐支，品格高尚，事业有成，贵人相助',
    '甲寅': '甲木坐寅木，比肩坐支，自强不息，独立自主',
    '乙卯': '乙木坐卯木，比肩坐支，温和善良，人缘良好'
  };

  const result = selfSittingMap[dayGan + dayZhi] || `${dayGan}${dayZhi} - 自坐分析待完善`;

  console.log('   自坐分析:', result);
  console.log('✅ 自坐分析完成');
  return result;
}

const selfSittingResult = testSelfSittingAnalysis();

// 4. 测试空亡计算
console.log('\n⭕ 4. 测试空亡计算');
console.log('-'.repeat(40));

function testKongwangCalculation() {
  const dayGan = mockFourPillars[2].gan; // 癸
  const dayZhi = mockFourPillars[2].zhi; // 丑
  const dayGanzhi = dayGan + dayZhi; // 癸丑

  console.log('日柱干支:', dayGanzhi);

  // 六甲旬空亡对照表
  const kongwangMap = {
    '甲子旬': ['戌', '亥'],
    '甲戌旬': ['申', '酉'],
    '甲申旬': ['午', '未'],
    '甲午旬': ['辰', '巳'],
    '甲辰旬': ['寅', '卯'],
    '甲寅旬': ['子', '丑']
  };

  // 根据日柱确定所属旬
  const xunMap = {
    '癸丑': '甲辰旬'
  };

  const xunName = xunMap[dayGanzhi] || '未知旬';
  const kongwangZhi = kongwangMap[xunName] || [];

  const result = {
    empty_branches: kongwangZhi.join('、'),
    xun_name: xunName,
    effect: `空亡为六甲旬中缺失的地支，主虚空、变化、不稳定`
  };

  console.log('   所属旬:', xunName);
  console.log('   空亡地支:', kongwangZhi.join('、'));
  console.log('✅ 空亡计算完成');
  return result;
}

const kongwangResult = testKongwangCalculation();

// 5. 测试命卦计算
console.log('\n🧭 5. 测试命卦计算');
console.log('-'.repeat(40));

function testMingguaCalculation() {
  const year = testBirthInfo.year; // 2006
  const gender = testBirthInfo.gender; // 男
  const yearLastTwo = year % 100; // 06

  console.log('出生年份:', year);
  console.log('年份后两位:', yearLastTwo);
  console.log('性别:', gender);

  // 命卦计算公式
  let remainder;
  if (gender === '男') {
    remainder = (99 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 4) % 9;
    if (remainder === 0) remainder = 9;
  }

  // 卦位对应表
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };

  let gua = guaMap[remainder];

  // 处理中宫情况
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }

  const result = {
    gua_name: gua + '卦',
    gua_number: remainder,
    category: ['震', '巽', '离', '坎'].includes(gua) ? '东四命' : '西四命'
  };

  console.log('   计算余数:', remainder);
  console.log('   命卦:', result.gua_name);
  console.log('   类别:', result.category);
  console.log('✅ 命卦计算完成');
  return result;
}

const mingguaResult = testMingguaCalculation();

// 6. 模拟完整数据结构
console.log('\n📊 6. 模拟完整数据结构');
console.log('-'.repeat(40));

const mockBaziResult = {
  // 基础四柱
  formatted: {
    year: '丙戌',
    month: '乙未',
    day: '癸丑',
    hour: '己未',
    full: '丙戌 乙未 癸丑 己未'
  },

  // 纳音分析
  nayin: nayinResult,

  // 长生十二宫
  changshengAnalysis: changshengResult,

  // 自坐分析
  selfSittingAnalysis: selfSittingResult,

  // 基本信息
  basicInfo: {
    kong_wang: kongwangResult,
    ming_gua: mingguaResult
  },

  // 其他已有模块
  tenGods: {
    year_star: '正财',
    month_star: '食神',
    day_star: '日主',
    hour_star: '正官'
  },

  cangganAnalysis: {
    year_pillar: {
      main_qi: '戊',
      hidden_gan: '戊、辛、丁',
      ten_gods: '正官、偏印、偏财'
    }
  }
};

console.log('✅ 完整数据结构构建完成');

// 7. 检查WXML条件
console.log('\n🔍 7. 检查WXML显示条件');
console.log('-'.repeat(40));

const wxmlConditions = [
  { name: '纳音分析', condition: 'baziResult.nayin', value: !!mockBaziResult.nayin },
  { name: '长生十二宫', condition: 'baziResult.changshengAnalysis', value: !!mockBaziResult.changshengAnalysis },
  { name: '自坐分析', condition: 'baziResult.selfSittingAnalysis', value: !!mockBaziResult.selfSittingAnalysis },
  { name: '空亡分析', condition: 'baziResult.basicInfo.kong_wang', value: !!mockBaziResult.basicInfo.kong_wang },
  { name: '命卦分析', condition: 'baziResult.basicInfo.ming_gua', value: !!mockBaziResult.basicInfo.ming_gua }
];

wxmlConditions.forEach(item => {
  const status = item.value ? '✅ 满足' : '❌ 不满足';
  console.log(`   ${item.name} (${item.condition}): ${status}`);
});

// 8. 总结
console.log('\n📋 问题诊断总结');
console.log('='.repeat(60));

const allConditionsMet = wxmlConditions.every(item => item.value);

if (allConditionsMet) {
  console.log('🎉 所有模块数据都正确计算！');
  console.log('💡 问题可能在于:');
  console.log('   1. 前端计算函数没有正确调用这些模块');
  console.log('   2. 数据传递过程中丢失了某些字段');
  console.log('   3. WXML模板的条件判断有问题');
} else {
  console.log('⚠️ 发现数据计算问题');
  console.log('💡 需要检查相应的计算函数');
}

console.log('\n🔧 建议修复步骤:');
console.log('1. 检查 calculateBazi 函数是否调用了所有计算模块');
console.log('2. 验证数据传递到前端的完整性');
console.log('3. 确认WXML模板的数据绑定路径正确');
console.log('4. 添加调试日志跟踪数据流');

console.log('\n🏁 调试完成');

// 导出结果
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    mockBaziResult, 
    nayinResult, 
    changshengResult, 
    selfSittingResult, 
    kongwangResult, 
    mingguaResult 
  };
}
