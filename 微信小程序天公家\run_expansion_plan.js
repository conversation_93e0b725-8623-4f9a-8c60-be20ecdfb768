/**
 * 运行数据库扩展计划
 */

const DatabaseExpansionPlan = require('./utils/database_expansion_plan.js');

async function runExpansionPlan() {
  try {
    console.log('🚀 启动数据库扩展计划分析');
    console.log('============================================================');
    
    const expansionPlan = new DatabaseExpansionPlan();
    
    // 显示扩展计划概览
    expansionPlan.showExpansionOverview();
    
    // 获取下一批优先级列表
    const nextBatch = expansionPlan.getNextBatchPriority(25);
    
    // 生成实施建议
    expansionPlan.generateImplementationSuggestions();
    
    console.log('\n🎉 扩展计划分析完成!');
    console.log('============================================================');
    console.log('📋 总结:');
    console.log(`   - 当前已有 37 位历史名人`);
    console.log(`   - 目标总数 200 位名人`);
    console.log(`   - 已规划详细扩展方案`);
    console.log(`   - 下一批建议添加 ${nextBatch.length} 位高优先级名人`);
    
  } catch (error) {
    console.error('❌ 扩展计划分析失败:', error);
  }
}

// 运行扩展计划
runExpansionPlan();
