/**
 * 六亲页面深度计算器
 * 专门计算六亲页面的view标签深度
 */

const fs = require('fs');
const path = require('path');

function calculateLiuqinDepth() {
  console.log('🔍 计算六亲页面的 view 标签深度');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到六亲页面的开始和结束
  let liuqinStart = -1;
  let liuqinEnd = lines.length; // 默认到文件结尾
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('wx:elif') && line.includes('liuqin')) {
      liuqinStart = i;
      console.log(`📍 六亲页面开始: 第${i + 1}行`);
      break;
    }
  }
  
  // 找到scroll-view结束位置
  for (let i = liuqinStart + 1; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('</scroll-view>')) {
      liuqinEnd = i;
      console.log(`📍 scroll-view结束: 第${i + 1}行`);
      break;
    }
  }
  
  if (liuqinStart === -1) {
    console.log('❌ 无法找到六亲页面的开始');
    return;
  }
  
  console.log(`\n🔍 分析第${liuqinStart + 1}行到第${liuqinEnd}行的view标签:`);
  
  let depth = 0;
  const depthHistory = [];
  
  for (let i = liuqinStart; i < liuqinEnd; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 计算这一行的view标签变化
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    
    const prevDepth = depth;
    depth += openViews - closeViews;
    
    // 记录所有的深度变化
    if (openViews > 0 || closeViews > 0) {
      depthHistory.push({
        line: lineNum,
        content: line.trim(),
        openViews,
        closeViews,
        prevDepth,
        newDepth: depth,
        change: depth - prevDepth
      });
    }
  }
  
  console.log(`\n📊 六亲页面统计结果:`);
  console.log(`最终深度: ${depth}`);
  
  console.log(`\n📄 所有深度变化:`);
  depthHistory.forEach(item => {
    const changeStr = item.change > 0 ? `+${item.change}` : `${item.change}`;
    console.log(`第${item.line}行: 深度 ${item.prevDepth} → ${item.newDepth} (${changeStr}) | 开始:${item.openViews} 结束:${item.closeViews}`);
    if (item.content.length > 80) {
      console.log(`    ${item.content.substring(0, 80)}...`);
    } else {
      console.log(`    ${item.content}`);
    }
  });
  
  // 分析结果
  if (depth === 0) {
    console.log(`\n✅ 六亲页面的view标签匹配正确`);
  } else if (depth > 0) {
    console.log(`\n❌ 六亲页面缺少 ${depth} 个结束标签`);
    console.log(`\n🔧 修复建议:`);
    console.log(`在第${liuqinEnd}行之前添加 ${depth} 个 </view> 标签`);
  } else {
    console.log(`\n❌ 六亲页面有 ${Math.abs(depth)} 个多余的结束标签`);
  }
}

// 运行计算
if (require.main === module) {
  calculateLiuqinDepth();
}

module.exports = { calculateLiuqinDepth };
