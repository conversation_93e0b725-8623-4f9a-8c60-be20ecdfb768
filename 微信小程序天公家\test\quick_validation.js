/**
 * 快速验证测试
 * 检查WXML文件的基本结构是否正确
 */

const fs = require('fs');
const path = require('path');

function quickValidation() {
  console.log('🔍 快速验证WXML文件');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📄 文件总行数: ${lines.length}`);
  
  // 检查关键结构
  const containerStart = lines.findIndex(line => line.includes('<view class="tianggong-container">'));
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`📍 关键结构:`);
  console.log(`tianggong-container: 第${containerStart + 1}行`);
  console.log(`scroll-view 开始: 第${scrollViewStart + 1}行`);
  console.log(`scroll-view 结束: 第${scrollViewEnd + 1}行`);
  
  // 检查文件结尾
  console.log(`\n📄 文件结尾:`);
  for (let i = Math.max(0, lines.length - 5); i < lines.length; i++) {
    console.log(`${i + 1}: ${lines[i]}`);
  }
  
  // 简单的标签匹配检查
  const allOpenViews = content.match(/<view[^>]*>/g) || [];
  const allCloseViews = content.match(/<\/view>/g) || [];
  
  console.log(`\n📊 标签统计:`);
  console.log(`开始view标签: ${allOpenViews.length}`);
  console.log(`结束view标签: ${allCloseViews.length}`);
  console.log(`差值: ${allOpenViews.length - allCloseViews.length}`);
  
  if (allOpenViews.length === allCloseViews.length) {
    console.log(`✅ view标签数量匹配`);
  } else {
    console.log(`❌ view标签数量不匹配`);
  }
  
  // 检查原错误位置
  const originalErrorLine = 2660;
  if (originalErrorLine <= lines.length) {
    console.log(`\n📍 原错误行(${originalErrorLine}): ${lines[originalErrorLine - 1]?.trim() || '(空行)'}`);
  } else {
    console.log(`\n📍 原错误行(${originalErrorLine}): 已超出文件范围`);
  }
  
  console.log(`\n🎯 验证结果:`);
  const hasContainer = containerStart >= 0;
  const hasScrollView = scrollViewStart >= 0 && scrollViewEnd >= 0;
  const tagsMatch = allOpenViews.length === allCloseViews.length;
  const errorLineGone = originalErrorLine > lines.length;
  
  console.log(`${hasContainer ? '✅' : '❌'} 主容器存在`);
  console.log(`${hasScrollView ? '✅' : '❌'} scroll-view结构完整`);
  console.log(`${tagsMatch ? '✅' : '❌'} view标签数量匹配`);
  console.log(`${errorLineGone ? '✅' : '❌'} 原错误行已消除`);
  
  if (hasContainer && hasScrollView && tagsMatch && errorLineGone) {
    console.log(`\n🎉 WXML文件验证通过！`);
    return true;
  } else {
    console.log(`\n❌ 仍有问题需要解决`);
    return false;
  }
}

// 运行验证
if (require.main === module) {
  quickValidation();
}

module.exports = { quickValidation };
