// utils/true_solar_time_corrector.js
// 真太阳时修正系统 - 确保时辰判断准确性
// 依据：《大小运，流年.txt》技术文档要求

// 引入完整的城市坐标数据库（306个城市）
const CityCoordinates = require('./city_coordinates.js');

/**
 * 真太阳时修正器
 * 
 * 功能特点：
 * 1. 基于出生地经度进行时间修正
 * 2. 确保时辰判断的准确性
 * 3. 支持全国各地区经度修正
 * 4. 提供详细的修正计算过程
 * 
 * 技术原理：
 * - 中国标准时间基于东经120°
 * - 每15°经度差对应1小时时差
 * - 真太阳时 = 北京时间 + 经度修正
 * 
 * 修正公式：
 * 经度时差(分钟) = (当地经度 - 120°) / 15° × 60分钟
 */
class TrueSolarTimeCorrector {
  constructor() {
    this.name = 'TrueSolarTimeCorrector';
    this.version = '1.0.0';
    this.baseTimezone = 120; // 中国标准时间基准经度（东经120°）

    // 集成完整的城市坐标数据库（306个城市）
    this.cityCoordinates = CityCoordinates.CITY_COORDINATES;

    // 为了向后兼容，保留原有的34个城市经度数据作为备用
    this.cityLongitudes = {
      '北京': 116.4074,
      '上海': 121.4737,
      '广州': 113.2644,
      '深圳': 114.0579,
      '杭州': 120.1551,
      '南京': 118.7969,
      '武汉': 114.3054,
      '成都': 104.0665,
      '重庆': 106.5516,
      '西安': 108.9402,
      '天津': 117.1901,
      '沈阳': 123.4315,
      '大连': 121.6147,
      '青岛': 120.3826,
      '济南': 117.0009,
      '郑州': 113.6401,
      '长沙': 112.9388,
      '南昌': 115.8921,
      '福州': 119.2965,
      '厦门': 118.0894,
      '昆明': 102.8329,
      '贵阳': 106.6302,
      '南宁': 108.3669,
      '海口': 110.3312,
      '拉萨': 91.1409,
      '乌鲁木齐': 87.6168,
      '银川': 106.2309,
      '西宁': 101.7782,
      '呼和浩特': 111.7519,
      '哈尔滨': 126.5358,
      '长春': 125.3245,
      '石家庄': 114.5149,
      '太原': 112.5489,
      '兰州': 103.8236
    };
    
    console.log('🌅 真太阳时修正系统初始化完成');
    console.log(`📍 基准时区: 东经${this.baseTimezone}°`);
    console.log(`🗺️ 集成完整城市坐标数据库: ${Object.keys(this.cityCoordinates).length}个城市`);
    console.log(`🔄 向后兼容: ${Object.keys(this.cityLongitudes).length}个主要城市`);
  }
  
  /**
   * 计算真太阳时
   * @param {Date} beijingTime - 北京时间
   * @param {number} longitude - 出生地经度（东经为正，西经为负）
   * @returns {Object} 修正结果
   */
  calculateTrueSolarTime(beijingTime, longitude) {
    if (!beijingTime || typeof longitude !== 'number') {
      throw new Error('参数错误：需要有效的北京时间和经度');
    }
    
    // 验证经度范围
    if (longitude < -180 || longitude > 180) {
      throw new Error(`经度超出有效范围：${longitude}°（有效范围：-180° ~ 180°）`);
    }
    
    // 计算经度时差（分钟）
    const longitudeDiff = longitude - this.baseTimezone;
    const timeOffsetMinutes = (longitudeDiff / 15) * 60;
    
    // 计算真太阳时
    const trueSolarTime = new Date(beijingTime.getTime() + timeOffsetMinutes * 60 * 1000);
    
    // 生成详细的修正信息
    const correctionInfo = {
      input: {
        beijingTime: beijingTime,
        longitude: longitude,
        beijingTimeString: this.formatDateTime(beijingTime)
      },
      calculation: {
        baseTimezone: this.baseTimezone,
        longitudeDiff: longitudeDiff,
        timeOffsetMinutes: timeOffsetMinutes,
        timeOffsetHours: timeOffsetMinutes / 60,
        direction: longitudeDiff > 0 ? '东进' : longitudeDiff < 0 ? '西退' : '无修正'
      },
      result: {
        trueSolarTime: trueSolarTime,
        trueSolarTimeString: this.formatDateTime(trueSolarTime),
        timeDifference: timeOffsetMinutes,
        needsCorrection: Math.abs(timeOffsetMinutes) >= 1 // 1分钟以上才需要修正
      },
      analysis: this.generateCorrectionAnalysis(longitudeDiff, timeOffsetMinutes)
    };
    
    console.log(`🌅 真太阳时修正完成: ${correctionInfo.input.beijingTimeString} → ${correctionInfo.result.trueSolarTimeString}`);
    console.log(`📍 经度修正: ${longitude}° (${correctionInfo.calculation.direction}${Math.abs(timeOffsetMinutes).toFixed(1)}分钟)`);
    
    return correctionInfo;
  }
  
  /**
   * 根据城市名称获取经度
   * 优先使用完整的城市坐标数据库（306个城市），备用34个主要城市
   * @param {string} cityName - 城市名称
   * @returns {number} 经度
   */
  getCityLongitude(cityName) {
    // 优先使用完整的城市坐标数据库
    if (this.cityCoordinates && this.cityCoordinates[cityName]) {
      return this.cityCoordinates[cityName].longitude;
    }

    // 备用：使用原有的34个主要城市数据
    const longitude = this.cityLongitudes[cityName];
    if (longitude !== undefined) {
      return longitude;
    }

    throw new Error(`未找到城市"${cityName}"的经度数据（支持${Object.keys(this.cityCoordinates || {}).length + Object.keys(this.cityLongitudes).length}个城市）`);
  }
  
  /**
   * 根据城市计算真太阳时
   * @param {Date} beijingTime - 北京时间
   * @param {string} cityName - 城市名称
   * @returns {Object} 修正结果
   */
  calculateTrueSolarTimeByCity(beijingTime, cityName) {
    const longitude = this.getCityLongitude(cityName);
    const result = this.calculateTrueSolarTime(beijingTime, longitude);
    result.input.cityName = cityName;
    result.input.cityLongitude = longitude;
    
    console.log(`🏙️ 城市修正: ${cityName} (东经${longitude}°)`);
    return result;
  }
  
  /**
   * 判断修正后的时辰
   * @param {Date} trueSolarTime - 真太阳时
   * @returns {Object} 时辰信息
   */
  determineTimeZhi(trueSolarTime) {
    const hour = trueSolarTime.getHours();
    const minute = trueSolarTime.getMinutes();
    const timeDecimal = hour + minute / 60;
    
    // 时辰对照表（真太阳时）
    const timeZhiTable = [
      { start: 23, end: 1, name: '子', index: 0 },
      { start: 1, end: 3, name: '丑', index: 1 },
      { start: 3, end: 5, name: '寅', index: 2 },
      { start: 5, end: 7, name: '卯', index: 3 },
      { start: 7, end: 9, name: '辰', index: 4 },
      { start: 9, end: 11, name: '巳', index: 5 },
      { start: 11, end: 13, name: '午', index: 6 },
      { start: 13, end: 15, name: '未', index: 7 },
      { start: 15, end: 17, name: '申', index: 8 },
      { start: 17, end: 19, name: '酉', index: 9 },
      { start: 19, end: 21, name: '戌', index: 10 },
      { start: 21, end: 23, name: '亥', index: 11 }
    ];
    
    let currentZhi = null;
    
    for (const timeRange of timeZhiTable) {
      if (timeRange.start < timeRange.end) {
        // 正常时段
        if (timeDecimal >= timeRange.start && timeDecimal < timeRange.end) {
          currentZhi = timeRange;
          break;
        }
      } else {
        // 跨日时段（子时：23:00-1:00）
        if (timeDecimal >= timeRange.start || timeDecimal < timeRange.end) {
          currentZhi = timeRange;
          break;
        }
      }
    }
    
    if (!currentZhi) {
      currentZhi = timeZhiTable[0]; // 默认子时
    }
    
    return {
      trueSolarTime: trueSolarTime,
      timeDecimal: timeDecimal,
      zhi: currentZhi.name,
      zhiIndex: currentZhi.index,
      timeRange: `${currentZhi.start}:00-${currentZhi.end}:00`,
      description: `${this.formatDateTime(trueSolarTime)} → ${currentZhi.name}时`
    };
  }
  
  /**
   * 完整的时辰修正流程
   * @param {Date} beijingTime - 北京时间
   * @param {number|string} location - 经度数值或城市名称
   * @returns {Object} 完整修正结果
   */
  correctTimeZhi(beijingTime, location) {
    let correctionResult;
    
    if (typeof location === 'string') {
      // 城市名称
      correctionResult = this.calculateTrueSolarTimeByCity(beijingTime, location);
    } else if (typeof location === 'number') {
      // 经度数值
      correctionResult = this.calculateTrueSolarTime(beijingTime, location);
    } else {
      throw new Error('位置参数错误：需要经度数值或城市名称');
    }
    
    // 判断修正后的时辰
    const zhiInfo = this.determineTimeZhi(correctionResult.result.trueSolarTime);
    
    // 判断修正前的时辰（用于对比）
    const originalZhiInfo = this.determineTimeZhi(beijingTime);
    
    return {
      ...correctionResult,
      timeZhi: {
        original: originalZhiInfo,
        corrected: zhiInfo,
        changed: originalZhiInfo.zhi !== zhiInfo.zhi,
        changeDescription: originalZhiInfo.zhi !== zhiInfo.zhi 
          ? `时辰修正: ${originalZhiInfo.zhi}时 → ${zhiInfo.zhi}时`
          : '时辰无变化'
      }
    };
  }
  
  /**
   * 生成修正分析
   * @param {number} longitudeDiff - 经度差
   * @param {number} timeOffsetMinutes - 时间偏移（分钟）
   * @returns {string} 分析说明
   */
  generateCorrectionAnalysis(longitudeDiff, timeOffsetMinutes) {
    const absOffset = Math.abs(timeOffsetMinutes);
    
    if (absOffset < 1) {
      return '经度接近标准时区，无需修正';
    } else if (absOffset < 15) {
      return `轻微修正：${longitudeDiff > 0 ? '东部' : '西部'}地区，时差${absOffset.toFixed(1)}分钟`;
    } else if (absOffset < 60) {
      return `中等修正：${longitudeDiff > 0 ? '东部' : '西部'}地区，时差${absOffset.toFixed(1)}分钟，可能影响时辰判断`;
    } else {
      return `重要修正：${longitudeDiff > 0 ? '东部' : '西部'}地区，时差${(absOffset/60).toFixed(1)}小时，强烈建议使用修正时间`;
    }
  }
  
  /**
   * 格式化日期时间
   * @param {Date} date - 日期对象
   * @returns {string} 格式化字符串
   */
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }
  
  /**
   * 获取支持的城市列表
   * @returns {Array} 城市列表
   */
  getSupportedCities() {
    const allCities = new Set();

    // 添加完整城市坐标数据库中的城市
    if (this.cityCoordinates) {
      Object.keys(this.cityCoordinates).forEach(city => allCities.add(city));
    }

    // 添加备用城市数据中的城市
    Object.keys(this.cityLongitudes).forEach(city => allCities.add(city));

    return Array.from(allCities).sort();
  }
  
  /**
   * 检查修正器状态
   * @returns {Object} 状态信息
   */
  getCorrectorStatus() {
    const totalCities = this.getSupportedCities().length;
    const primaryCities = Object.keys(this.cityCoordinates || {}).length;
    const backupCities = Object.keys(this.cityLongitudes).length;

    return {
      name: this.name,
      version: this.version,
      baseTimezone: this.baseTimezone,
      supportedCities: {
        total: totalCities,
        primary: primaryCities,
        backup: backupCities
      },
      features: [
        '经度时差修正',
        `${totalCities}个城市完整支持`,
        '城市快速查询',
        '时辰准确判断',
        '详细修正分析'
      ],
      status: 'ready'
    };
  }
}

// 导出修正器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TrueSolarTimeCorrector;
} else if (typeof window !== 'undefined') {
  window.TrueSolarTimeCorrector = TrueSolarTimeCorrector;
}

console.log('🌅 真太阳时修正系统模块加载完成');
