<!-- components/dialogue-form/index.wxml -->
<view class="form-wrapper">
  <view class="form-title">基本信息</view>
  
  <view class="form-item">
    <view class="form-label">姓名</view>
    <input 
      class="form-input" 
      placeholder="请输入姓名" 
      value="{{userInfo.name}}" 
      data-field="name" 
      bindinput="onInputChange"
    />
  </view>
  
  <view class="form-item">
    <view class="form-label">年龄</view>
    <input 
      class="form-input" 
      type="number" 
      placeholder="请输入年龄(选填)" 
      value="{{userInfo.age}}" 
      data-field="age" 
      bindinput="onInputChange"
    />
  </view>
  
  <view class="form-item">
    <view class="form-label">性别</view>
    <picker 
      mode="selector" 
      range="{{genderOptions}}" 
      value="{{genderOptions.indexOf(userInfo.gender)}}" 
      data-field="gender" 
      bindchange="onPickerChange"
    >
      <view class="picker-content">
        {{userInfo.gender || '请选择性别'}}
        <view class="arrow">▼</view>
      </view>
    </picker>
  </view>
  
  <view class="form-item">
    <view class="form-label">职业</view>
    <picker 
      mode="selector" 
      range="{{occupationOptions}}" 
      value="{{occupationOptions.indexOf(userInfo.occupation)}}" 
      data-field="occupation" 
      bindchange="onPickerChange"
    >
      <view class="picker-content">
        {{userInfo.occupation || '请选择职业'}}
        <view class="arrow">▼</view>
      </view>
    </picker>
  </view>
  
  <button class="submit-button" bindtap="submitForm">提交信息</button>
  
  <view class="form-tips">
    * 信息仅用于评估分析，不会被用于其他用途
  </view>
</view>