/* pages/dialogue-assessment/index.wxss */

/* 页面级样式，确保背景色铺满整个页面 */
page {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 主题样式 - 天公师兄占卜 */
page.tarot-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
}

/* 整体容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  padding: 50rpx 30rpx 20rpx;
  align-items: center;
  position: relative;
  z-index: 10;
}

.settings-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.settings-icon:active, .icon-hover {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.15);
}

.settings-icon image {
  width: 40rpx;
  height: 40rpx;
}

.tab-container {
  flex: 1;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 40rpx;
  padding: 8rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.tab.active {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 日期显示 */
.date-display {
  padding: 20rpx 40rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
}

.date-dot {
  margin: 0 8rpx;
  opacity: 0.6;
}

/* 欢迎消息 */
.welcome-message {
  position: relative;
  padding: 40rpx;
  margin: 60rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.welcome-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 40rpx;
}



.welcome-start-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  width: 80%;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  animation: fadeInUp 1.4s ease-out;
}

.welcome-start-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
}

.welcome-start-button image {
  width: 36rpx;
  height: 36rpx;
}

/* 消息区域 */
.message-area {
  flex: 1;
  padding: 20rpx 30rpx 120rpx;
  overflow-y: auto;
  position: relative;
}

/* 消息容器 */
.message-wrapper {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  max-width: 90%;
}

.ai-wrapper {
  align-items: flex-start;
}

/* 用户消息容器 */
.user-wrapper {
  align-items: flex-end;
  align-self: flex-end;
  margin-left: auto;
  margin-right: 40rpx;
  max-width: 70%;
}

/* 消息气泡 */
.message {
  padding: 24rpx 30rpx;
  border-radius: 24rpx;
  font-size: 30rpx;
  line-height: 1.5;
  position: relative;
  margin-bottom: 8rpx;
  max-width: 85%;
}

/* AI消息样式 */
.ai-message {
  background: rgba(255, 255, 255, 0.12);
  color: white;
  border-top-left-radius: 4rpx;
}

/* 用户消息样式 */
.user-message {
  background: rgba(255, 255, 255, 0.22);
  color: white;
  border-top-right-radius: 4rpx;
  margin-right: 10rpx;
  padding-right: 24rpx;
}

/* 消息内容 */
.message-content {
  word-break: break-all;
}

/* 用户消息时间 */
.user-wrapper .message-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 6rpx;
  align-self: flex-end;
  margin-right: 15rpx;
}

/* 头像容器 */
.avatar-container {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.avatar-image {
  width: 60%;
  height: 60%;
}

/* AI消息时间样式 */
.ai-wrapper .message-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 6rpx;
  margin-left: 30rpx;
  align-self: flex-start;
}

/* 打字指示器 */
.typing-wrapper {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.typing-indicator {
  display: flex;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24rpx;
  border-top-left-radius: 4rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  background: white;
  border-radius: 50%;
  margin: 0 6rpx;
  opacity: 0.7;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10rpx);
  }
}

/* 底部空白区域，确保可以滚动到最后一条消息 */
.message-bottom-space {
  height: 120rpx;
}



/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 60rpx 50rpx 30rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
  z-index: 100;
  box-shadow: 0 -4rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
  color: white;
  font-size: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.send-button, .voice-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
}

.send-button {
  margin-right: 15rpx;
}

.send-button.active {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.send-button image, .voice-button image {
  width: 40rpx;
  height: 40rpx;
}

.button-hover {
  transform: scale(0.95);
  opacity: 0.9;
}





/* 主题特定样式 */

/* 占卜主题特定样式 */
/* 塔罗占卜主题 */
.tarot-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
  position: relative;
}

.tarot-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.tarot-theme .send-button.active, .tarot-theme .welcome-start-button {
  background: rgba(107, 91, 115, 0.8);
}

.tarot-theme .tab.active {
  background: rgba(107, 91, 115, 0.8);
}

.tarot-theme .typing-dot {
  background: #6B5B73;
}

/* 天公师兄头像样式 */
.tarot-theme .avatar {
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 100%);
  border: 3px solid rgba(139, 69, 19, 0.6);
  box-shadow: 0 4rpx 15rpx rgba(139, 69, 19, 0.4);
}

.tarot-theme .avatar-image {
  filter: brightness(1.0) contrast(1.1);
  width: 70%;
  height: 70%;
}

/* 易经八卦主题 */
.yijing-theme {
  background: linear-gradient(135deg, #2F4F4F 0%, #708090 50%, #2F4F4F 100%);
  position: relative;
}

.yijing-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.yijing-theme .send-button.active, .yijing-theme .welcome-start-button {
  background: rgba(47, 79, 79, 0.8);
}

.yijing-theme .tab.active {
  background: rgba(47, 79, 79, 0.8);
}

.yijing-theme .typing-dot {
  background: #2F4F4F;
}

/* 紫微斗数主题 */
.ziwei-theme {
  background: linear-gradient(135deg, #4B0082 0%, #8A2BE2 50%, #4B0082 100%);
  position: relative;
}

.ziwei-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.ziwei-theme .send-button.active, .ziwei-theme .welcome-start-button {
  background: rgba(75, 0, 130, 0.8);
}

.ziwei-theme .tab.active {
  background: rgba(75, 0, 130, 0.8);
}

.ziwei-theme .typing-dot {
  background: #4B0082;
}

/* 奇门遁甲主题 */
.qimen-theme {
  background: linear-gradient(135deg, #B8860B 0%, #DAA520 50%, #B8860B 100%);
  position: relative;
}

.qimen-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 40% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                    radial-gradient(circle at 60% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
  z-index: 0;
}

.qimen-theme .send-button.active, .qimen-theme .welcome-start-button {
  background: rgba(184, 134, 11, 0.8);
}

.qimen-theme .tab.active {
  background: rgba(184, 134, 11, 0.8);
}

.qimen-theme .typing-dot {
  background: #B8860B;
}

/* 六爻占卜主题 */
.liuyao-theme {
  background: linear-gradient(135deg, #2E4057 0%, #4A6741 50%, #2E4057 100%);
  position: relative;
}

.liuyao-theme::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 50% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 50% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 25%);
  z-index: 0;
}

.liuyao-theme .send-button.active, .liuyao-theme .welcome-start-button {
  background: rgba(46, 64, 87, 0.8);
}

.liuyao-theme .tab.active {
  background: rgba(46, 64, 87, 0.8);
}

.liuyao-theme .typing-dot {
  background: #2E4057;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.8);
  }
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    transform: translateX(-50%) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}



/* 助手名称样式 */
.assistant-name {
  font-size: 26rpx;
  font-weight: 500;
  margin-left: 15rpx;
  color: white;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

/* 智能提示气泡样式 */
.smart-tip {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.smart-tip.show {
  opacity: 1;
  transform: translateX(-50%) translateY(-10rpx);
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  line-height: 1.4;
  max-width: 500rpx;
  text-align: center;
  position: relative;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
}

.tooltip-arrow {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid rgba(0, 0, 0, 0.8);
}