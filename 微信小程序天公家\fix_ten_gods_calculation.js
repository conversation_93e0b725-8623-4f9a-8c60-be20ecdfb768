// fix_ten_gods_calculation.js
// 修复十神计算错误

console.log('🔧 修复十神计算错误');
console.log('='.repeat(80));

// 正确的十神关系表（基于阴阳五行理论）
const correctTenGodsRelations = {
  '癸': {
    // 癸水为日干
    '甲': '伤官',  // 癸水生甲木，异性相生
    '乙': '食神',  // 癸水生乙木，同性相生
    '丙': '偏财',  // 癸水克丙火，异性相克
    '丁': '正财',  // 癸水克丁火，同性相克
    '戊': '七杀',  // 戊土克癸水，异性相克
    '己': '正官',  // 己土克癸水，同性相克
    '庚': '偏印',  // 庚金生癸水，异性相生
    '辛': '正印',  // 辛金生癸水，同性相生
    '壬': '比肩',  // 壬水与癸水，异性同类
    '癸': '劫财'   // 癸水与癸水，同性同类
  }
};

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  dayGan: '癸'
};

// "问真八字"标准结果
const wenZhenStandard = {
  mainStars: ['偏印', '伤官', '元男', '比肩'],
  deputyStars: {
    year: ['正官', '劫财', '正印'],  // 丑: 己土癸水辛金
    month: ['正财', '正官'],        // 午: 丁火己土  
    day: ['食神'],                 // 卯: 乙木
    hour: ['七杀', '正印', '正财']  // 戌: 戊土辛金丁火
  }
};

// 修正后的十神计算函数
function calculateCorrectTenGod(dayGan, otherGan) {
  if (!correctTenGodsRelations[dayGan]) {
    return '未知';
  }
  return correctTenGodsRelations[dayGan][otherGan] || '未知';
}

// 检查主星计算
function checkMainStarsCorrection() {
  console.log('\n🌟 检查修正后的主星计算:');
  console.log('='.repeat(50));
  
  const positions = ['年柱', '月柱', '日柱', '时柱'];
  const calculatedMainStars = [];
  
  testData.fourPillars.forEach((pillar, index) => {
    let calculated;
    if (index === 2) {
      calculated = '元男'; // 日主
    } else {
      calculated = calculateCorrectTenGod(testData.dayGan, pillar.gan);
    }
    
    calculatedMainStars.push(calculated);
    const expected = wenZhenStandard.mainStars[index];
    const match = calculated === expected;
    
    console.log(`${positions[index]} ${pillar.gan}: ${calculated} ${match ? '✅' : '❌'} (期望: ${expected})`);
  });
  
  return calculatedMainStars;
}

// 检查副星计算
function checkDeputyStarsCorrection() {
  console.log('\n🌟 检查修正后的副星计算:');
  console.log('='.repeat(50));
  
  // 地支藏干表
  const cangganMap = {
    '丑': ['己', '癸', '辛'],
    '午': ['丁', '己'],
    '卯': ['乙'],
    '戌': ['戊', '辛', '丁']
  };
  
  const positions = ['year', 'month', 'day', 'hour'];
  const positionNames = ['年柱', '月柱', '日柱', '时柱'];
  
  testData.fourPillars.forEach((pillar, index) => {
    const canggan = cangganMap[pillar.zhi];
    const calculatedDeputyStars = [];
    
    console.log(`\n${positionNames[index]} ${pillar.zhi}:`);
    console.log(`  藏干: ${canggan.join(', ')}`);
    
    canggan.forEach(gan => {
      const tenGod = calculateCorrectTenGod(testData.dayGan, gan);
      calculatedDeputyStars.push(tenGod);
      console.log(`  ${gan} → ${tenGod}`);
    });
    
    const expectedDeputyStars = wenZhenStandard.deputyStars[positions[index]];
    console.log(`  期望副星: ${expectedDeputyStars.join(', ')}`);
    console.log(`  计算副星: ${calculatedDeputyStars.join(', ')}`);
    
    // 检查副星匹配度
    const deputyMatch = calculatedDeputyStars.length === expectedDeputyStars.length &&
                       calculatedDeputyStars.every((star, i) => star === expectedDeputyStars[i]);
    
    console.log(`  匹配度: ${deputyMatch ? '✅ 完全匹配' : '⚠️ 需要检查'}`);
  });
}

// 分析错误原因
function analyzeErrors() {
  console.log('\n🔍 错误原因分析:');
  console.log('='.repeat(50));
  
  console.log('发现的问题:');
  console.log('1. 十神关系表可能有错误');
  console.log('2. 阴阳五行理论应用不正确');
  console.log('3. 同性异性关系判断错误');
  
  console.log('\n具体错误分析:');
  console.log('年柱 辛→癸:');
  console.log('  - 辛金生癸水，同性相生 → 应为"正印"');
  console.log('  - 但"问真八字"显示"偏印"');
  console.log('  - 可能是阴阳判断有误');
  
  console.log('\n副星错误分析:');
  console.log('年柱丑藏干:');
  console.log('  - 己→癸: 己土克癸水，同性相克 → 应为"正官"');
  console.log('  - 癸→癸: 癸水与癸水，同性同类 → 应为"劫财"');
  console.log('  - 辛→癸: 辛金生癸水，同性相生 → 应为"正印"');
}

// 重新研究阴阳理论
function researchYinYangTheory() {
  console.log('\n📚 重新研究阴阳理论:');
  console.log('='.repeat(50));
  
  console.log('天干阴阳属性:');
  console.log('阳干: 甲丙戊庚壬');
  console.log('阴干: 乙丁己辛癸');
  
  console.log('\n十神关系规律:');
  console.log('1. 生我者为印星: 同性为正印，异性为偏印');
  console.log('2. 我生者为食伤: 同性为食神，异性为伤官');
  console.log('3. 克我者为官杀: 同性为正官，异性为七杀');
  console.log('4. 我克者为财星: 同性为正财，异性为偏财');
  console.log('5. 同我者为比劫: 同性为劫财，异性为比肩');
  
  console.log('\n关键发现:');
  console.log('癸水(阴) vs 辛金(阴) → 同性 → 辛生癸 → 正印');
  console.log('但"问真八字"显示"偏印"，说明可能有特殊规则');
}

// 生成修复方案
function generateFixSolution() {
  console.log('\n🔧 修复方案:');
  console.log('='.repeat(50));
  
  console.log('方案1: 对标"问真八字"标准');
  console.log('- 直接使用"问真八字"的十神结果作为标准');
  console.log('- 反推正确的十神关系表');
  console.log('- 确保100%匹配权威软件');
  
  console.log('\n方案2: 深入研究传统理论');
  console.log('- 查阅《三命通会》等古籍');
  console.log('- 确认阴阳五行的正确应用');
  console.log('- 建立理论基础扎实的十神表');
  
  console.log('\n推荐方案: 方案1');
  console.log('理由: 现阶段以实用性为主，确保与权威软件一致');
  console.log('后续可以研究理论差异的原因');
}

// 执行检查
console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('参考标准: "问真八字"权威软件');

checkMainStarsCorrection();
checkDeputyStarsCorrection();
analyzeErrors();
researchYinYangTheory();
generateFixSolution();

console.log('\n📊 修复总结:');
console.log('='.repeat(40));
console.log('❌ 当前十神计算与"问真八字"不匹配');
console.log('❌ 需要重新构建十神关系表');
console.log('✅ 已识别具体错误位置');
console.log('✅ 已制定修复方案');

console.log('\n🚀 下一步行动:');
console.log('1. 基于"问真八字"标准重建十神关系表');
console.log('2. 修复前端十神计算逻辑');
console.log('3. 验证所有测试案例');
console.log('4. 确保100%匹配权威标准');
