/**
 * 应期分析数据接口规范
 * 基于应期.txt要求的标准化数据结构
 */

/**
 * 八字应期请求接口
 * @typedef {Object} BaziPeriodRequest
 * @property {string} birth - 出生时间 ISO格式
 * @property {string} gender - 性别 "male" | "female"
 * @property {string} event - 事件类型 "marriage" | "promotion" | "childbirth" | "wealth"
 * @property {Object} location - 出生地坐标 (可选)
 * @property {number} location.lat - 纬度
 * @property {number} location.lng - 经度
 * @property {Object} options - 分析选项 (可选)
 * @property {number} options.forecast_years - 预测年数，默认5年
 * @property {string} options.precision_level - 精度级别 "high" | "medium" | "low"
 * @property {boolean} options.include_cultural_context - 是否包含文化语境
 */

/**
 * 八字应期响应接口
 * @typedef {Object} BaziPeriodResponse
 * @property {number} period - 应期年份
 * @property {string} reason - 应期原因详述
 * @property {number} confidence - 置信度 0-1
 * @property {Object} triggers - 引动因素
 * @property {string} triggers.star - 星动信息
 * @property {string} triggers.palace - 宫动信息
 * @property {string} triggers.god - 神煞动信息
 * @property {Object} disease_medicine - 病药分析
 * @property {Array} disease_medicine.diseases - 病神列表
 * @property {Array} disease_medicine.medicines - 药神列表
 * @property {number} disease_medicine.balance_score - 病药平衡评分
 * @property {Object} energy_analysis - 能量分析
 * @property {Object} energy_analysis.thresholds - 阈值达成情况
 * @property {Object} energy_analysis.element_powers - 五行力量分布
 * @property {string} ancient_basis - 古籍理论依据
 * @property {string} cultural_context - 文化语境说明
 * @property {Array} alternative_periods - 备选应期
 */

/**
 * 病药分析结果
 * @typedef {Object} DiseaseMedicineAnalysis
 * @property {Array<DiseaseInfo>} diseases - 病神信息
 * @property {Array<MedicineInfo>} medicines - 药神信息
 * @property {number} balance_score - 病药平衡评分 0-1
 * @property {string} treatment_priority - 治疗优先级
 */

/**
 * 病神信息
 * @typedef {Object} DiseaseInfo
 * @property {string} type - 病神类型
 * @property {string} description - 病理描述
 * @property {number} severity - 严重程度 0-1
 * @property {string} location - 病神位置
 * @property {string} ancient_reference - 古籍出处
 */

/**
 * 药神信息
 * @typedef {Object} MedicineInfo
 * @property {string} type - 药神类型
 * @property {number} effectiveness - 有效性 0-1
 * @property {Object} availability - 可用性信息
 * @property {boolean} availability.present - 是否存在
 * @property {number} availability.strength - 药力强度
 * @property {Array<string>} availability.location - 药神位置
 * @property {string} treatment_method - 治疗方法
 */

/**
 * 三重引动分析
 * @typedef {Object} TripleActivationAnalysis
 * @property {StarActivation} star_activation - 星动分析
 * @property {PalaceActivation} palace_activation - 宫动分析
 * @property {GodActivation} god_activation - 神煞动分析
 * @property {number} activation_score - 综合激活评分
 * @property {string} priority_level - 优先级等级
 */

/**
 * 星动分析
 * @typedef {Object} StarActivation
 * @property {Array<ActivatedStar>} activated_stars - 激活的星
 * @property {number} activation_count - 激活数量
 * @property {number} total_strength - 总强度
 */

/**
 * 激活的星
 * @typedef {Object} ActivatedStar
 * @property {string} star - 星名
 * @property {string} activation_type - 激活类型
 * @property {number} strength - 强度
 * @property {string} influence - 影响描述
 */

/**
 * 宫动分析
 * @typedef {Object} PalaceActivation
 * @property {string} palace - 相关宫位
 * @property {Array<PalaceInteraction>} activations - 宫位激活
 * @property {PalaceInteraction} strongest_activation - 最强激活
 */

/**
 * 宫位互动
 * @typedef {Object} PalaceInteraction
 * @property {string} palace - 宫位名称
 * @property {string} interaction_type - 互动类型 "三合" | "六合" | "冲" | "刑"
 * @property {number} strength - 强度
 * @property {number} priority - 优先级
 * @property {string} effect - 作用效果
 */

/**
 * 神煞动分析
 * @typedef {Object} GodActivation
 * @property {Array<ActivatedGod>} activated_gods - 激活的神煞
 * @property {number} total_influence - 总影响力
 * @property {ActivatedGod} dominant_god - 主导神煞
 */

/**
 * 激活的神煞
 * @typedef {Object} ActivatedGod
 * @property {string} god - 神煞名称
 * @property {string} activation_method - 激活方式
 * @property {number} influence - 影响力
 * @property {string} meaning - 含义解释
 * @property {string} effect_period - 作用时期
 */

/**
 * 能量阈值分析
 * @typedef {Object} EnergyThresholdAnalysis
 * @property {Object} element_energies - 五行能量分布
 * @property {Object} threshold_results - 阈值检测结果
 * @property {number} overall_energy_score - 总体能量评分
 * @property {string} ancient_basis - 古籍依据
 */

/**
 * 阈值检测结果
 * @typedef {Object} ThresholdResult
 * @property {number} required - 要求阈值
 * @property {number} actual - 实际值
 * @property {boolean} met - 是否达标
 * @property {string} percentage - 达成百分比
 * @property {string} assessment - 评估说明
 */

/**
 * 综合应期预测
 * @typedef {Object} ComprehensiveTimingPrediction
 * @property {Array<YearTimingResult>} all_years - 所有年份结果
 * @property {YearTimingResult} best_timing - 最佳应期
 * @property {Array<YearTimingResult>} optimal_years - 最优年份列表
 * @property {string} timing_pattern - 应期模式
 * @property {string} recommendation - 建议说明
 */

/**
 * 年份应期结果
 * @typedef {Object} YearTimingResult
 * @property {number} year - 年份
 * @property {Object} gan_zhi - 干支信息
 * @property {string} gan_zhi.gan - 天干
 * @property {string} gan_zhi.zhi - 地支
 * @property {string} gan_zhi.full - 完整干支
 * @property {number} disease_resolution - 病药解决度
 * @property {number} energy_fulfillment - 能量达标度
 * @property {number} activation_completeness - 引动完整度
 * @property {number} comprehensive_score - 综合评分
 * @property {string} timing_level - 应期等级 "excellent" | "good" | "fair" | "poor"
 * @property {string} detailed_reason - 详细原因
 * @property {Array<string>} key_factors - 关键因素
 * @property {Object} monthly_breakdown - 月份细分 (可选)
 */

/**
 * 文化语境适配
 * @typedef {Object} CulturalContextAdaptation
 * @property {string} historical_period - 历史时期
 * @property {Object} regional_adjustments - 地域调整
 * @property {Array<string>} cultural_factors - 文化因素
 * @property {Object} god_sha_mapping - 神煞地域映射
 * @property {string} cultural_interpretation - 文化解读
 */

/**
 * 验证与调优配置
 * @typedef {Object} ValidationConfig
 * @property {Array<HistoricalCase>} historical_cases - 历史案例
 * @property {Object} accuracy_metrics - 准确度指标
 * @property {Object} feedback_weights - 反馈权重
 * @property {Object} optimization_parameters - 优化参数
 */

/**
 * 历史案例
 * @typedef {Object} HistoricalCase
 * @property {string} case_id - 案例ID
 * @property {BaziPeriodRequest} request - 原始请求
 * @property {number} actual_period - 实际应期
 * @property {number} predicted_period - 预测应期
 * @property {number} accuracy_score - 准确度评分
 * @property {string} validation_notes - 验证说明
 */

/**
 * 应期分析接口类
 */
class TimingAnalysisInterface {
  /**
   * 验证请求数据格式
   * @param {BaziPeriodRequest} request 
   * @returns {Object} 验证结果
   */
  static validateRequest(request) {
    const errors = [];
    
    if (!request.birth) errors.push('缺少出生时间');
    if (!['male', 'female'].includes(request.gender)) errors.push('性别格式错误');
    if (!['marriage', 'promotion', 'childbirth', 'wealth'].includes(request.event)) {
      errors.push('事件类型不支持');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 格式化响应数据
   * @param {Object} analysisResult 
   * @returns {BaziPeriodResponse}
   */
  static formatResponse(analysisResult) {
    // 🔧 正确提取最佳年份
    let bestYear = null;
    if (analysisResult.timing_prediction?.threshold_status === 'met') {
      bestYear = analysisResult.timing_prediction?.best_timing?.year ||
                 analysisResult.timing_prediction?.best_timing?.gan_zhi ||
                 null;
    }

    return {
      period: bestYear,
      reason: analysisResult.timing_prediction?.best_timing?.detailed_reason ||
              analysisResult.timing_prediction?.recommendation || '',
      confidence: analysisResult.confidence || 0,
      triggers: {
        star: this.formatStarTriggers(analysisResult.activation_analysis?.star_activation),
        palace: this.formatPalaceTriggers(analysisResult.activation_analysis?.palace_activation),
        god: this.formatGodTriggers(analysisResult.activation_analysis?.god_activation)
      },
      disease_medicine: analysisResult.disease_analysis || {},
      energy_analysis: analysisResult.energy_analysis || {},
      ancient_basis: analysisResult.ancient_basis || '',
      cultural_context: analysisResult.cultural_context || '',
      alternative_periods: this.formatAlternativePeriods(analysisResult.timing_prediction?.optimal_years),
      // 🆕 添加阈值状态信息
      threshold_status: analysisResult.timing_prediction?.threshold_status || 'unknown',
      threshold_message: analysisResult.timing_prediction?.message || null
    };
  }

  /**
   * 格式化星动触发信息
   */
  static formatStarTriggers(starActivation) {
    if (!starActivation?.activated_stars?.length) return '无星动';
    
    return starActivation.activated_stars
      .map(star => `${star.star}(${star.activation_type})`)
      .join('、');
  }

  /**
   * 格式化宫动触发信息
   */
  static formatPalaceTriggers(palaceActivation) {
    if (!palaceActivation?.strongest_activation) return '无宫动';
    
    const activation = palaceActivation.strongest_activation;
    return `${activation.palace}${activation.interaction_type}`;
  }

  /**
   * 格式化神煞动触发信息
   */
  static formatGodTriggers(godActivation) {
    if (!godActivation?.activated_gods?.length) return '无神煞动';
    
    return godActivation.activated_gods
      .map(god => god.god)
      .join('、');
  }

  /**
   * 格式化备选应期
   */
  static formatAlternativePeriods(optimalYears) {
    if (!optimalYears?.length) return [];
    
    return optimalYears.slice(1, 4).map(year => ({
      year: year.year,
      score: year.comprehensive_score,
      level: year.timing_level,
      reason: year.detailed_reason
    }));
  }
}

module.exports = {
  TimingAnalysisInterface,
  // 导出类型定义供TypeScript使用
  types: {
    BaziPeriodRequest: 'BaziPeriodRequest',
    BaziPeriodResponse: 'BaziPeriodResponse',
    DiseaseMedicineAnalysis: 'DiseaseMedicineAnalysis',
    TripleActivationAnalysis: 'TripleActivationAnalysis',
    EnergyThresholdAnalysis: 'EnergyThresholdAnalysis',
    ComprehensiveTimingPrediction: 'ComprehensiveTimingPrediction',
    CulturalContextAdaptation: 'CulturalContextAdaptation',
    ValidationConfig: 'ValidationConfig'
  }
};
