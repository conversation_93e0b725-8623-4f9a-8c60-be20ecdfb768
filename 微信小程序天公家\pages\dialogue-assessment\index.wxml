<!-- pages/dialogue-assessment/index.wxml -->
<!-- 全屏背景 -->
<view class="full-page-bg {{themeClass}}" style="background-color: {{themeColor}}"></view>

<view class="chat-container {{themeClass}}" style="background-color: {{themeColor}}">
  <!-- 顶部导航栏 -->
  <view class="top-nav" style="{{customNavBarStyle}}">
    <view class="settings-icon" bindtap="navigateToProfile" hover-class="icon-hover">
      <image src="/assets/icons/new/profile_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="tab-container">
      <view class="tab {{activeTab === 'today' ? 'active' : ''}}" bindtap="switchTab" data-tab="today">今日问事</view>
      <view class="tab {{activeTab === 'history' ? 'active' : ''}}" bindtap="navigateToHistory" data-tab="history">历史记录</view>
    </view>
  </view>
  
  <!-- 日期显示 -->
  <view class="date-display">{{currentDate}}</view>
  

  
  <!-- 欢迎消息区域 -->
  <view class="welcome-message" wx:if="{{messages.length === 0}}">
    <view class="welcome-title">{{welcomeTitle}}</view>
    <view class="welcome-subtitle">{{welcomeSubtitle}}</view>
    <view class="welcome-start-button" bindtap="sendMessage">
      <text>开始占卜</text>
      <image src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 消息区域 -->
  <scroll-view class="message-area" scroll-y scroll-into-view="{{lastMessageId}}" enhanced="true" show-scrollbar="{{false}}" bounces="{{true}}">
    <!-- 现有消息列表 -->
    <block wx:for="{{messages}}" wx:key="id">
      <view class="message-wrapper {{item.role === 'AI' ? 'ai-wrapper' : 'user-wrapper'}}" id="msg-{{item.id}}">
        <view wx:if="{{item.role === 'AI'}}" class="avatar-container">
          <view class="avatar">
            <image src="/assets/icons/tiangong-master.svg" class="avatar-image" mode="aspectFit"></image>
          </view>
          <text class="assistant-name">{{assistantName}}</text>
        </view>
        

        
        <!-- 普通消息 -->
        <view class="message {{item.role === 'AI' ? 'ai-message' : 'user-message'}}">
          <view class="message-content">{{item.content}}</view>
        </view>
        
        <view class="message-time" wx:if="{{item.time}}">{{item.time}}</view>
      </view>
    </block>
    
    <!-- 正在输入指示器 -->
    <view class="typing-wrapper" wx:if="{{typing}}">
      <view class="avatar-container">
        <view class="avatar">
          <image src="/assets/icons/tiangong-master.svg" class="avatar-image" mode="aspectFit"></image>
        </view>
        <text class="assistant-name">{{assistantName}}</text>
      </view>
      <view class="typing-indicator">
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
      </view>
    </view>
    
    <!-- 底部空白，确保滚动到底部有足够空间 -->
    <view class="message-bottom-space"></view>
  </scroll-view>
  
  <!-- 底部输入区域 -->
  <view class="input-area" style="{{inputAreaStyle}}">
    <input class="message-input" placeholder="请告知您的问题..." value="{{inputValue}}" bindinput="onInputChange"
      disabled="{{sending}}" focus="{{messages.length > 0 && !sending}}"
      confirm-type="send" bindconfirm="sendMessage" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage" hover-class="button-hover">
      <image src="/assets/icons/new/send_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="voice-button" hover-class="button-hover">
      <image src="/assets/icons/new/voice_icon.svg" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 智能提示气泡 -->
  <view class="smart-tip {{showSmartTip ? 'show' : ''}}" wx:if="{{showSmartTip}}">
    <view class="tooltip-content">
      <text>{{smartTipContent}}</text>
      <view class="tooltip-arrow"></view>
    </view>
  </view>

</view>


