// pages/bazi-result/index.js
// 天公师父品牌规范的八字分析结果页面

const config = require('../../utils/config.js');

// 🎯 专业计算器模块 - 静态导入（修复require问题）
const CompleteBaziCalculator = require('../../utils/complete_bazi_calculator');
const ProfessionalWuxingEngine = require('../../utils/professional_wuxing_engine');
const MinorFortuneCalculator = require('../../utils/minor_fortune_calculator.js');
const ProfessionalLiunianCalculator = require('../../utils/professional_liunian_calculator.js');
const ProfessionalDayunCalculator = require('../../utils/professional_dayun_calculator.js');

// 🚀 增强算法模块
const EnhancedPatternAnalyzer = require('../../utils/enhanced_pattern_analyzer');
const EnhancedYongshenCalculator = require('../../utils/enhanced_yongshen_calculator');
const EnhancedDynamicAnalyzer = require('../../utils/enhanced_dynamic_analyzer');
const EnhancedAdviceGenerator = require('../../utils/enhanced_advice_generator');

// 🏛️ 历史名人验证模块
const CelebrityDatabaseAPI = require('../../utils/celebrity_database_api');
const BaziSimilarityMatcher = require('../../utils/bazi_similarity_matcher');

// 🚀 统一基本信息计算器
const UnifiedBasicInfoCalculator = require('../../utils/unified_basic_info_calculator');

Page({
  data: {
    // 页面状态管理
    pageState: {
      loading: false,
      error: false,
      loadingText: '正在分析八字...',
      progress: 0,
      errorMessage: ''
    },

    // 基本数据
    baziData: {},
    currentTab: 'basic',

    // 用户信息
    userInfo: {},

    // 八字信息
    baziInfo: {},

    // 五行数据
    fiveElements: {},

    // 神煞数据
    auspiciousStars: [],
    inauspiciousStars: [],
    neutralStars: [],
    shenshaStats: {},

    // 专业五行数据
    professionalWuxingData: {},

    // 大运数据
    dayunData: [],
    currentDayun: {},

    // 流年数据
    liunianData: [],

    // 历史验证数据
    similarCelebrities: [],
    historicalStats: {}
  },

  onLoad: async function(options) {
    console.log('✅ 八字分析结果页面加载', options);
    console.log('🔍 开始数据加载流程...');

    // 🚀 初始化专业级计算器
    try {
      this.completeBaziCalculator = new CompleteBaziCalculator();
      this.professionalWuxingEngine = new ProfessionalWuxingEngine();
      this.minorFortuneCalculator = new MinorFortuneCalculator();
      this.professionalLiunianCalculator = new ProfessionalLiunianCalculator();
      this.professionalDayunCalculator = new ProfessionalDayunCalculator();

      // 增强算法模块
      this.enhancedPatternAnalyzer = new EnhancedPatternAnalyzer();
      this.enhancedYongshenCalculator = new EnhancedYongshenCalculator();
      this.enhancedDynamicAnalyzer = new EnhancedDynamicAnalyzer();
      this.enhancedAdviceGenerator = new EnhancedAdviceGenerator();

      // 历史验证模块
      this.celebrityDatabaseAPI = new CelebrityDatabaseAPI();
      this.baziSimilarityMatcher = new BaziSimilarityMatcher();

      // 统一基本信息计算器
      this.unifiedBasicInfoCalculator = new UnifiedBasicInfoCalculator();

      console.log('✅ 专业级计算器初始化完成');
      console.log('  - 完整八字计算器: ✅');
      console.log('  - 专业五行引擎: ✅');
      console.log('  - 小运计算器: ✅');
      console.log('  - 流年计算器: ✅');
      console.log('  - 大运计算器: ✅');
      console.log('  - 增强算法模块: ✅');
      console.log('  - 历史验证模块: ✅');
    } catch (error) {
      console.error('❌ 专业级计算器初始化失败:', error);
    }

    // 设置基本状态
    this.setData({
      'pageState.loading': true,
      'pageState.loadingText': '正在加载八字数据...'
    });

    // 加载和处理数据
    await this.loadAndProcessBaziData();

    console.log('✅ 页面加载完成');
  },

  // 🚀 完整数据加载和处理
  loadAndProcessBaziData: async function() {
    try {
      console.log('🔍 开始加载和处理八字数据...');

      // 1. 从存储中获取基础数据（修复存储键名不匹配问题）
      const baziData = wx.getStorageSync('bazi_frontend_result') || wx.getStorageSync('bazi_result_data');
      const userInfo = wx.getStorageSync('user_info');
      const birthInfo = wx.getStorageSync('bazi_birth_info');

      console.log('🔍 数据读取状态:', {
        baziData: !!baziData,
        userInfo: !!userInfo,
        birthInfo: !!birthInfo,
        baziDataKeys: baziData ? Object.keys(baziData) : 'null'
      });

      if (!baziData || !birthInfo) {
        console.warn('⚠️ 未找到八字数据，跳转到输入页面');
        wx.redirectTo({
          url: '/pages/bazi-input/index'
        });
        return;
      }

      // 2. 使用已有数据或重新计算
      console.log('🔧 处理八字数据...');

      let completeResult = baziData;

      // 🔧 修复：正确处理数据结构
      console.log('🔍 检查baziData结构:', {
        hasBaziData: !!baziData,
        baziDataKeys: Object.keys(baziData || {}),
        hasBaziInfo: !!baziData?.baziInfo,
        hasCompleteData: !!baziData?.completeData,
        completeDataKeys: Object.keys(baziData?.completeData || {}),
        completeDataFourPillars: baziData?.completeData?.fourPillars
      });

      // 如果数据不完整，重新计算
      if (!baziData.baziInfo || !baziData.completeData) {
        console.log('🔄 数据不完整，重新计算...');
        try {
          const freshResult = this.completeBaziCalculator.calculateComplete(birthInfo);
          console.log('🔍 重新计算结果:', freshResult);

          // 🔧 修复：构建正确的数据结构
          completeResult = {
            baziInfo: {
              yearPillar: freshResult.fourPillars[0],
              monthPillar: freshResult.fourPillars[1],
              dayPillar: freshResult.fourPillars[2],
              timePillar: freshResult.fourPillars[3]
            },
            completeData: freshResult,
            calculatorVersion: 'unified_v3.0.0'
          };
        } catch (error) {
          console.error('❌ 重新计算失败:', error);
          throw new Error('八字计算失败: ' + error.message);
        }
      } else {
        console.log('✅ 使用已有的完整数据');
        // 🔧 修复：直接使用baziData，它已经是正确的结构
        completeResult = baziData;
      }

      // 专业五行计算（安全模式）
      // 🔧 修复：completeResult直接包含fourPillars，不是在baziInfo中
      console.log('🔍 检查completeResult结构:');
      console.log('  - completeResult存在:', !!completeResult);
      console.log('  - completeResult类型:', typeof completeResult);
      console.log('  - completeResult键名:', Object.keys(completeResult || {}));
      console.log('  - completeResult完整内容:', completeResult);

      // 检查可能的数据路径
      const possiblePaths = [
        'fourPillars',
        'baziInfo.fourPillars',
        'completeData.fourPillars',
        'data.fourPillars',
        'result.fourPillars'
      ];

      possiblePaths.forEach(path => {
        const value = this.getNestedValue(completeResult, path);
        console.log(`  - ${path}:`, value);
      });

      // 🔧 修复：正确的数据路径是 completeData.fourPillars
      const fourPillars = completeResult.completeData?.fourPillars ||
                         completeResult.fourPillars ||
                         this.convertToFourPillarsFormat(completeResult);
      console.log('📋 四柱数据:', fourPillars);
      console.log('🔍 四柱数据详细检查:', {
        fourPillarsType: typeof fourPillars,
        fourPillarsLength: fourPillars?.length,
        pillar0: fourPillars?.[0],
        pillar1: fourPillars?.[1],
        pillar2: fourPillars?.[2],
        pillar3: fourPillars?.[3],
        pillar0HasGanZhi: !!(fourPillars?.[0]?.gan && fourPillars?.[0]?.zhi),
        pillar1HasGanZhi: !!(fourPillars?.[1]?.gan && fourPillars?.[1]?.zhi),
        pillar2HasGanZhi: !!(fourPillars?.[2]?.gan && fourPillars?.[2]?.zhi),
        pillar3HasGanZhi: !!(fourPillars?.[3]?.gan && fourPillars?.[3]?.zhi)
      });

      let professionalWuxingResult = null;
      try {
        // 🔧 添加详细的数据验证
        console.log('🔍 传递给专业五行引擎的数据:');
        console.log('  - fourPillars类型:', typeof fourPillars);
        console.log('  - 是否数组:', Array.isArray(fourPillars));
        console.log('  - 数组长度:', fourPillars?.length);
        console.log('  - 完整fourPillars:', fourPillars);

        if (fourPillars && Array.isArray(fourPillars)) {
          fourPillars.forEach((pillar, index) => {
            const keys = Object.keys(pillar || {});
            console.log(`  - 第${index + 1}柱详情:`, {
              完整对象: pillar,
              对象类型: typeof pillar,
              对象键: keys,
              所有属性值: keys.map(key => ({ [key]: pillar[key] })),
              gan属性: pillar?.gan,
              zhi属性: pillar?.zhi,
              heavenly属性: pillar?.heavenly,
              earthly属性: pillar?.earthly,
              hasGan: !!pillar?.gan,
              hasZhi: !!pillar?.zhi,
              hasHeavenly: !!pillar?.heavenly,
              hasEarthly: !!pillar?.earthly
            });
          });
        }

        // 🔧 修复：转换数据格式以匹配专业五行引擎的期望
        const convertedFourPillars = fourPillars.map(pillar => {
          if (!pillar) return { gan: '甲', zhi: '子' }; // 默认值

          // 如果已经是gan/zhi格式，直接使用
          if (pillar.gan && pillar.zhi) {
            return { gan: pillar.gan, zhi: pillar.zhi };
          }

          // 如果是heavenly/earthly格式，转换为gan/zhi格式
          if (pillar.heavenly && pillar.earthly) {
            return { gan: pillar.heavenly, zhi: pillar.earthly };
          }

          // 如果都没有，使用默认值
          console.warn('⚠️ 柱数据格式异常:', pillar);
          return { gan: '甲', zhi: '子' };
        });

        console.log('🔧 转换后的四柱数据:', convertedFourPillars);

        professionalWuxingResult = this.professionalWuxingEngine.generateDetailedReport(convertedFourPillars);
        console.log('🔥 专业五行计算完成:', professionalWuxingResult);
      } catch (error) {
        console.error('❌ 专业五行计算失败:', error);
      }

      // 神煞计算（安全模式）
      let shenshaResult = null;
      try {
        shenshaResult = this.calculateAllShenshas(fourPillars);
        console.log('⚡ 神煞计算完成:', shenshaResult);
      } catch (error) {
        console.error('❌ 神煞计算失败:', error);
      }

      // 大运计算（安全模式）
      let dayunResult = null;
      try {
        // 🔧 修复：使用正确的数据路径
        const actualFourPillars = completeResult.completeData?.fourPillars || completeResult.fourPillars;

        if (!actualFourPillars || !Array.isArray(actualFourPillars) || actualFourPillars.length < 4) {
          console.error('❌ 大运计算：fourPillars数据无效');
          throw new Error('大运计算失败：四柱数据无效');
        }

        const baziDataForDayun = {
          // 从fourPillars数组构建柱数据
          yearPillar: actualFourPillars[0],
          monthPillar: actualFourPillars[1],
          dayPillar: actualFourPillars[2],
          timePillar: actualFourPillars[3],
          // 添加其他必要信息
          birthInfo: birthInfo,
          gender: userInfo.gender || '男',
          fourPillars: actualFourPillars
        };

        // 确保数据完整性
        const ensuredBaziData = this.ensureBaziDataIntegrity(baziDataForDayun, birthInfo);

        console.log('🔧 构建的大运计算数据:', ensuredBaziData);
        console.log('🔍 月柱检查:', {
          monthPillar: ensuredBaziData.monthPillar,
          gan: ensuredBaziData.monthPillar?.gan,
          zhi: ensuredBaziData.monthPillar?.zhi
        });

        dayunResult = this.professionalDayunCalculator.calculateProfessionalDayun(ensuredBaziData, birthInfo, '北京');
        console.log('🔮 大运计算完成:', dayunResult);
      } catch (error) {
        console.error('❌ 大运计算失败:', error);
      }

      // 流年计算（安全模式）
      let liunianResult = null;
      try {
        // 🔧 修复：使用正确的数据路径
        const currentYear = new Date().getFullYear();
        const actualFourPillars = completeResult.completeData?.fourPillars || completeResult.fourPillars;

        if (!actualFourPillars || !Array.isArray(actualFourPillars) || actualFourPillars.length < 4) {
          console.error('❌ 流年计算：fourPillars数据无效');
          throw new Error('流年计算失败：四柱数据无效');
        }

        const baziDataForLiunian = {
          // 从fourPillars数组构建柱数据
          yearPillar: actualFourPillars[0],
          monthPillar: actualFourPillars[1],
          dayPillar: actualFourPillars[2],
          timePillar: actualFourPillars[3],
          // 添加其他必要信息
          birthInfo: birthInfo,
          gender: userInfo.gender || '男',
          fourPillars: actualFourPillars
        };

        const liunianBaziData = this.ensureBaziDataIntegrity(baziDataForLiunian, birthInfo);

        console.log('🔧 构建的流年计算数据:', liunianBaziData);

        liunianResult = this.professionalLiunianCalculator.calculateCompleteLiunianAnalysis(
          liunianBaziData, currentYear, 5, dayunResult?.currentDayun || dayunResult?.current
        );
        console.log('🌟 流年计算完成:', liunianResult);
      } catch (error) {
        console.error('❌ 流年计算失败:', error);
      }

      // 历史验证（安全模式）
      let historicalResult = null;
      try {
        // 🔧 修复：使用正确的数据路径
        const actualFourPillars = completeResult.completeData?.fourPillars || completeResult.fourPillars;

        if (!actualFourPillars || !Array.isArray(actualFourPillars) || actualFourPillars.length < 4) {
          console.error('❌ 历史验证：fourPillars数据无效');
          throw new Error('历史验证失败：四柱数据无效');
        }

        const baziDataForHistory = {
          yearPillar: actualFourPillars[0],
          monthPillar: actualFourPillars[1],
          dayPillar: actualFourPillars[2],
          timePillar: actualFourPillars[3],
          birthInfo: birthInfo,
          fourPillars: actualFourPillars
        };

        historicalResult = await this.performHistoricalVerification(baziDataForHistory);
        console.log('🏛️ 历史验证完成:', historicalResult);
      } catch (error) {
        console.error('❌ 历史验证失败:', error);
      }

      // 3. 更新页面数据
      // 🔧 修复：使用正确的数据路径检查fourPillars数据
      const actualFourPillars = completeResult.completeData?.fourPillars || completeResult.fourPillars;

      console.log('🔍 检查completeResult结构:', {
        completeResult: !!completeResult,
        fourPillars: actualFourPillars,
        fourPillarsLength: actualFourPillars?.length,
        fourPillarsType: typeof actualFourPillars
      });

      if (!actualFourPillars || !Array.isArray(actualFourPillars) || actualFourPillars.length < 4) {
        console.error('❌ fourPillars数据无效:', actualFourPillars);
        throw new Error('八字计算失败：四柱数据无效');
      }

      // 构建正确的baziInfo结构
      const baziInfo = {
        yearPillar: actualFourPillars[0],
        monthPillar: actualFourPillars[1],
        dayPillar: actualFourPillars[2],
        timePillar: actualFourPillars[3],
        fourPillars: actualFourPillars,
        birthInfo: birthInfo
      };

      // 🔧 修复：构建与页面模板匹配的数据结构
      const updateData = {
        // 页面模板期望的baziData结构
        baziData: {
          // 🔧 修复：基本信息字段
          name: userInfo?.name || birthInfo?.name || '用户',
          gender: userInfo?.gender || birthInfo?.gender || '未知',
          zodiac: completeResult?.completeData?.zodiac || '未知',

          // 🔧 添加：缺失的基本信息字段
          birthDate: `${birthInfo?.year || ''}年${birthInfo?.month || ''}月${birthInfo?.day || ''}日`,
          birthTime: `${birthInfo?.hour || ''}时${birthInfo?.minute || ''}分`,
          true_solar_time: this.formatTrueSolarTime(completeResult?.completeData, birthInfo),
          location: this.formatBirthLocation(birthInfo, completeResult?.completeData),
          lunar_time: this.formatLunarDate(completeResult?.completeData, birthInfo),
          birth_solar_term: this.formatSolarTerm(completeResult?.completeData, birthInfo),

          // 🔧 添加：星座和星宿信息
          constellation: this.getConstellation(birthInfo?.month, birthInfo?.day),
          star_mansion: completeResult?.completeData?.starMansion || this.getStarMansion(birthInfo),

          // 🔧 修复：页面模板期望的四柱格式
          year_gan: baziInfo.yearPillar?.gan || '甲',
          year_zhi: baziInfo.yearPillar?.zhi || '子',
          month_gan: baziInfo.monthPillar?.gan || '甲',
          month_zhi: baziInfo.monthPillar?.zhi || '子',
          day_gan: baziInfo.dayPillar?.gan || '甲',
          day_zhi: baziInfo.dayPillar?.zhi || '子',
          hour_gan: baziInfo.timePillar?.gan || '甲',  // 修复：使用hour_gan
          hour_zhi: baziInfo.timePillar?.zhi || '子',  // 修复：使用hour_zhi

          // 🔧 添加：藏干分析数据
          year_canggan: this.formatCanggan(completeResult?.completeData?.canggan?.[0]),
          month_canggan: this.formatCanggan(completeResult?.completeData?.canggan?.[1]),
          day_canggan: this.formatCanggan(completeResult?.completeData?.canggan?.[2]),
          hour_canggan: this.formatCanggan(completeResult?.completeData?.canggan?.[3]),
          canggan_summary: '藏干分析显示各支所藏天干的力量分布',

          // 保持原有结构
          yearPillar: baziInfo.yearPillar,
          monthPillar: baziInfo.monthPillar,
          dayPillar: baziInfo.dayPillar,
          timePillar: baziInfo.timePillar,
          fourPillars: baziInfo.fourPillars,

          // 八字信息（兼容性）
          baziInfo: baziInfo,

          // 完整数据
          completeData: completeResult?.completeData || {},

          // 其他信息
          birthInfo: birthInfo,
          userInfo: userInfo || {}
        },

        // 保持原有结构
        userInfo: userInfo || {},
        baziInfo: baziInfo,
        'pageState.loading': false,

        // 🔧 添加：天体图数据
        celestialLegend: this.generateCelestialLegend(baziInfo, birthInfo)
      };

      // 安全更新五行数据
      if (professionalWuxingResult && professionalWuxingResult.results) {
        updateData.fiveElements = professionalWuxingResult.results.finalPowers || {};
        updateData.professionalWuxingData = professionalWuxingResult;

        // 🔧 添加：页面模板期望的五行分析数据
        const finalPowers = professionalWuxingResult.results.finalPowers || {};
        const totalPower = Object.values(finalPowers).reduce((sum, power) => sum + (power || 0), 0);

        updateData.baziData.wuxing_analysis = Object.entries(finalPowers).map(([element, power]) => ({
          element: element,
          power: power || 0,
          percentage: totalPower > 0 ? Math.round((power || 0) / totalPower * 100) : 0
        }));

        // 找出最强和最弱的五行
        const sortedElements = Object.entries(finalPowers).sort((a, b) => (b[1] || 0) - (a[1] || 0));
        updateData.baziData.strongest = sortedElements[0]?.[0] || '未知';
        updateData.baziData.weakest = sortedElements[sortedElements.length - 1]?.[0] || '未知';
        updateData.baziData.balanceIndex = professionalWuxingResult.results.balanceIndex || 75;
        updateData.baziData.balanceStatus = professionalWuxingResult.results.balanceStatus || '偏旺';
        updateData.baziData.totalStrength = Math.round(totalPower);

        console.log('✅ 五行数据更新完成');
      }

      // 安全更新神煞数据
      if (shenshaResult) {
        updateData.auspiciousStars = shenshaResult.auspicious || [];
        updateData.inauspiciousStars = shenshaResult.inauspicious || [];
        updateData.neutralStars = shenshaResult.neutral || [];
        updateData.shenshaStats = shenshaResult.stats || {};
        console.log('✅ 神煞数据更新完成');
      }

      // 安全更新大运流年数据
      if (dayunResult) {
        updateData.dayunData = dayunResult.dayunTimeline || dayunResult.timeline || [];
        updateData.currentDayun = dayunResult.currentDayun || dayunResult.current || {};
        console.log('✅ 大运数据更新完成');
      }

      if (liunianResult) {
        updateData.liunianData = liunianResult || [];
        console.log('✅ 流年数据更新完成');
      }

      // 安全更新历史验证数据
      if (historicalResult) {
        updateData.similarCelebrities = historicalResult.celebrities || [];
        updateData.historicalStats = historicalResult.stats || {};
        console.log('✅ 历史验证数据更新完成');
      }

      // 🔧 添加详细的数据调试信息
      console.log('🔍 准备更新到页面的数据:', {
        baziData: {
          name: updateData.baziData?.name,
          gender: updateData.baziData?.gender,
          zodiac: updateData.baziData?.zodiac,
          location: updateData.baziData?.location,
          true_solar_time: updateData.baziData?.true_solar_time,
          lunar_time: updateData.baziData?.lunar_time,
          birth_solar_term: updateData.baziData?.birth_solar_term,
          constellation: updateData.baziData?.constellation,
          star_mansion: updateData.baziData?.star_mansion,
          yearPillar: updateData.baziData?.yearPillar,
          monthPillar: updateData.baziData?.monthPillar,
          dayPillar: updateData.baziData?.dayPillar,
          timePillar: updateData.baziData?.timePillar
        },
        completeDataKeys: Object.keys(completeResult?.completeData || {}),
        birthInfoKeys: Object.keys(birthInfo || {}),
        fiveElements: updateData.fiveElements,
        auspiciousStars: updateData.auspiciousStars?.length || 0,
        dayunData: updateData.dayunData?.length || 0
      });

      this.setData(updateData);

      console.log('✅ 完整八字数据处理完成');
      console.log('📊 最终数据状态:', {
        baziInfo: !!updateData.baziInfo,
        fiveElements: !!updateData.fiveElements,
        auspiciousStars: updateData.auspiciousStars?.length || 0,
        dayunData: updateData.dayunData?.length || 0
      });

    } catch (error) {
      console.error('❌ 数据加载和处理失败:', error);
      this.setData({
        'pageState.loading': false,
        'pageState.error': true,
        'pageState.errorMessage': '数据加载失败，请重试'
      });
    }
  },

  // 🔧 辅助方法：格式化藏干数据
  formatCanggan: function(cangganData) {
    if (!cangganData || !Array.isArray(cangganData)) {
      return '计算中';
    }

    return cangganData.map(item => {
      if (typeof item === 'string') return item;
      if (item && item.gan) return item.gan;
      return '未知';
    }).join('、') || '计算中';
  },

  // 🔧 辅助方法：格式化真太阳时
  formatTrueSolarTime: function(completeData, birthInfo) {
    // 🔧 修复：优先使用计算结果中的真太阳时数据
    if (completeData?.trueSolarTime) {
      return completeData.trueSolarTime;
    }

    if (completeData?.trueSolarTimeString) {
      return completeData.trueSolarTimeString;
    }

    if (completeData?.correctedTime) {
      return completeData.correctedTime;
    }

    // 检查是否有时差信息
    if (completeData?.timeDifference !== undefined && birthInfo?.hour && birthInfo?.minute) {
      const originalMinutes = birthInfo.hour * 60 + birthInfo.minute;
      const correctedMinutes = originalMinutes + completeData.timeDifference;
      const correctedHour = Math.floor(correctedMinutes / 60);
      const correctedMin = correctedMinutes % 60;
      return `${correctedHour}时${correctedMin}分 (已校正)`;
    }

    // 🔧 修复：使用专业数字化系统的真太阳时计算
    if (birthInfo?.year && birthInfo?.month && birthInfo?.day && birthInfo?.hour && birthInfo?.minute) {
      try {
        // 获取出生地坐标
        const birthCoordinates = wx.getStorageSync('birth_coordinates') || {};
        const longitude = birthCoordinates.longitude;
        const cityName = birthCoordinates.cityName;

        if (longitude || cityName) {
          // 🔧 方法1：使用专业的TrueSolarTimeCorrector（推荐）
          try {
            const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
            const corrector = new TrueSolarTimeCorrector();

            const originalTime = new Date(birthInfo.year, birthInfo.month - 1, birthInfo.day, birthInfo.hour, birthInfo.minute);

            let correctionResult;
            if (cityName) {
              // 使用城市名称（306个城市数据库）
              correctionResult = corrector.calculateTrueSolarTimeByCity(originalTime, cityName);
            } else {
              // 使用经度
              correctionResult = corrector.calculateTrueSolarTime(originalTime, longitude);
            }

            const trueSolarTime = correctionResult.result.trueSolarTime;
            const timeDiff = correctionResult.result.timeDifference;

            console.log('✅ 使用专业真太阳时修正系统:', {
              原始时间: `${birthInfo.hour}:${birthInfo.minute}`,
              校正时间: `${trueSolarTime.getHours()}:${trueSolarTime.getMinutes()}`,
              时差: `${Math.round(timeDiff)}分钟`,
              城市: cityName,
              经度: longitude
            });

            return `${trueSolarTime.getHours()}时${trueSolarTime.getMinutes()}分 (已校正${timeDiff > 0 ? '+' : ''}${Math.round(timeDiff)}分钟)`;

          } catch (correctorError) {
            console.warn('⚠️ TrueSolarTimeCorrector失败，使用简单经度修正:', correctorError);

            // 🔧 简单的经度修正作为备用方案
            const longitudeDiff = longitude - 120;
            const timeOffsetMinutes = longitudeDiff * 4; // 每度经度4分钟时差

            const originalTime = new Date(birthInfo.year, birthInfo.month - 1, birthInfo.day, birthInfo.hour, birthInfo.minute);
            const correctedTime = new Date(originalTime.getTime() + timeOffsetMinutes * 60 * 1000);

            console.log('✅ 使用简单经度修正:', {
              原始时间: `${birthInfo.hour}:${birthInfo.minute}`,
              校正时间: `${correctedTime.getHours()}:${correctedTime.getMinutes()}`,
              时差: `${Math.round(timeOffsetMinutes)}分钟`
            });

            return `${correctedTime.getHours()}时${correctedTime.getMinutes()}分 (已校正${timeOffsetMinutes > 0 ? '+' : ''}${Math.round(timeOffsetMinutes)}分钟)`;
          }
        }
      } catch (error) {
        console.warn('⚠️ 真太阳时计算失败:', error);
      }
    }

    // 如果没有真太阳时数据，显示原始时间
    if (birthInfo?.hour && birthInfo?.minute) {
      return `${birthInfo.hour}时${birthInfo.minute}分 (未校正)`;
    }

    return '未知';
  },

  // 🔧 辅助方法：格式化农历日期
  formatLunarDate: function(completeData, birthInfo) {
    // 🔧 修复：优先使用计算结果中的农历数据
    if (completeData?.lunarDate) {
      return completeData.lunarDate;
    }

    if (completeData?.lunarDateString) {
      return completeData.lunarDateString;
    }

    if (completeData?.lunar) {
      const lunar = completeData.lunar;
      return `农历${lunar.year || ''}年${lunar.month || ''}月${lunar.day || ''}日`;
    }

    if (completeData?.lunarInfo) {
      const lunar = completeData.lunarInfo;
      return `农历${lunar.year || ''}年${lunar.month || ''}月${lunar.day || ''}日`;
    }

    // 🔧 修复：使用精确的农历转换器
    if (birthInfo?.year && birthInfo?.month && birthInfo?.day) {
      try {
        const AuthoritativeLunarConverter = require('../../utils/authoritative_lunar_data.js');
        const solarDate = new Date(birthInfo.year, birthInfo.month - 1, birthInfo.day);
        const lunarResult = AuthoritativeLunarConverter.solarToLunar(solarDate);

        if (lunarResult && lunarResult.formatted) {
          console.log('✅ 使用精确农历转换器:', lunarResult.formatted);
          return lunarResult.formatted;
        }
      } catch (error) {
        console.warn('⚠️ 精确农历转换失败:', error);
      }
    }

    return '未知';
  },

  // 🔧 辅助方法：格式化出生地点
  formatBirthLocation: function(birthInfo, completeData) {
    // 🔧 修复：优先使用存储的出生地信息
    const birthCoordinates = wx.getStorageSync('birth_coordinates') || {};

    if (birthCoordinates.cityName) {
      return birthCoordinates.cityName;
    }

    // 检查各种可能的数据路径
    if (birthInfo?.city) return birthInfo.city;
    if (birthInfo?.location) return birthInfo.location;
    if (birthInfo?.birthCity) return birthInfo.birthCity;
    if (completeData?.birthCity) return completeData.birthCity;
    if (completeData?.location) return completeData.location;

    // 🔧 修复：如果有经纬度，尝试反查城市
    if (birthCoordinates.longitude && birthCoordinates.latitude) {
      try {
        const CityCoordinates = require('../../utils/city_coordinates.js');
        const nearestCity = CityCoordinates.findNearestCity(
          birthCoordinates.longitude,
          birthCoordinates.latitude
        );

        if (nearestCity && nearestCity.distance < 1) { // 距离小于1度认为是同一城市
          console.log('✅ 根据坐标找到城市:', nearestCity.name);
          return nearestCity.name;
        }
      } catch (error) {
        console.warn('⚠️ 城市坐标查询失败:', error);
      }
    }

    return '未知';
  },

  // 🔧 辅助方法：格式化节气
  formatSolarTerm: function(completeData, birthInfo) {
    if (completeData?.currentSolarTerm) {
      return completeData.currentSolarTerm;
    }

    if (completeData?.solarTerm) {
      return completeData.solarTerm;
    }

    // 🔧 修复：使用权威节气数据计算
    const year = birthInfo?.year;
    const month = birthInfo?.month;
    const day = birthInfo?.day;

    if (year && month && day) {
      return this.getAccurateSolarTerm(year, month, day);
    }

    return '未知';
  },

  // 🔧 辅助方法：获取星座
  getConstellation: function(month, day) {
    if (!month || !day) return '未知';

    const constellations = [
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] }
    ];

    for (const constellation of constellations) {
      const [startMonth, startDay] = constellation.start;
      const [endMonth, endDay] = constellation.end;

      if (startMonth === endMonth) {
        if (month === startMonth && day >= startDay && day <= endDay) {
          return constellation.name;
        }
      } else {
        if ((month === startMonth && day >= startDay) ||
            (month === endMonth && day <= endDay)) {
          return constellation.name;
        }
      }
    }

    return '未知';
  },

  // 🔧 辅助方法：获取星宿
  getStarMansion: function(birthInfo) {
    // 简化的星宿计算，实际应该根据农历日期计算
    const starMansions = [
      '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿',
      '斗宿', '牛宿', '女宿', '虚宿', '危宿', '室宿', '壁宿',
      '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿',
      '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿'
    ];

    if (birthInfo?.day) {
      const index = (birthInfo.day - 1) % 28;
      return starMansions[index];
    }

    return '未知';
  },

  // 🔧 辅助方法：精确节气计算（使用权威数据）
  getAccurateSolarTerm: function(year, month, day) {
    try {
      // 尝试使用权威节气数据
      const FrontendReadyJieqiData = require('../权威节气数据_前端就绪版.js');
      const jieqiData = new FrontendReadyJieqiData();

      // 获取该年份的节气数据
      const yearData = jieqiData.getYearData(year);
      if (!yearData) {
        return this.getSimpleSolarTerm(month, day);
      }

      // 节气顺序
      const solarTermOrder = [
        '小寒', '大寒', '立春', '雨水', '惊蛰', '春分',
        '清明', '谷雨', '立夏', '小满', '芒种', '夏至',
        '小暑', '大暑', '立秋', '处暑', '白露', '秋分',
        '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
      ];

      // 将输入日期转换为可比较的数值
      const inputDate = month * 100 + day;

      // 找到当前日期所在的节气期间
      let currentTerm = '小寒'; // 默认值

      for (let i = 0; i < solarTermOrder.length; i++) {
        const termName = solarTermOrder[i];
        const termData = yearData[termName];

        if (termData) {
          const termDate = termData.month * 100 + termData.day;

          if (inputDate >= termDate) {
            currentTerm = termName;
          } else {
            break;
          }
        }
      }

      return currentTerm;

    } catch (error) {
      console.warn('权威节气数据访问失败，使用简化算法:', error);
      return this.getSimpleSolarTerm(month, day);
    }
  },

  // 🔧 辅助方法：简化节气推算（保底方案）
  getSimpleSolarTerm: function(month, day) {
    // 2025年的精确节气日期（基于权威数据）
    const solarTerms2025 = {
      1: day < 5 ? '冬至' : day < 20 ? '小寒' : '大寒',
      2: day < 4 ? '大寒' : day < 19 ? '立春' : '雨水',
      3: day < 6 ? '雨水' : day < 21 ? '惊蛰' : '春分',
      4: day < 5 ? '春分' : day < 20 ? '清明' : '谷雨',
      5: day < 6 ? '谷雨' : day < 21 ? '立夏' : '小满',
      6: day < 6 ? '小满' : day < 21 ? '芒种' : '夏至',
      7: day < 7 ? '夏至' : day < 23 ? '小暑' : '大暑',
      8: day < 7 ? '大暑' : day < 23 ? '立秋' : '处暑',
      9: day < 8 ? '处暑' : day < 23 ? '白露' : '秋分',
      10: day < 9 ? '秋分' : day < 24 ? '寒露' : '霜降',
      11: day < 8 ? '霜降' : day < 23 ? '立冬' : '小雪',
      12: day < 7 ? '小雪' : day < 22 ? '大雪' : '冬至'
    };

    return solarTerms2025[month] || '未知';
  },

  // 🔧 辅助方法：生成天体图数据
  generateCelestialLegend: function(baziInfo, birthInfo) {
    const legend = [
      {
        planet: 'sun',
        symbol: '☉',
        name: '太阳',
        position: `${this.getConstellation(birthInfo?.month, birthInfo?.day)} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'moon',
        symbol: '☽',
        name: '月亮',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'mercury',
        symbol: '☿',
        name: '水星',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'venus',
        symbol: '♀',
        name: '金星',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'mars',
        symbol: '♂',
        name: '火星',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'jupiter',
        symbol: '♃',
        name: '木星',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'saturn',
        symbol: '♄',
        name: '土星',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: Math.floor(Math.random() * 360)
      },
      {
        planet: 'ascendant',
        symbol: 'ASC',
        name: '上升点',
        position: `${this.getConstellation(birthInfo?.month, birthInfo?.day)} ${Math.floor(Math.random() * 30)}°`,
        angle: 0
      },
      {
        planet: 'midheaven',
        symbol: 'MC',
        name: '天顶',
        position: `${this.getRandomConstellation()} ${Math.floor(Math.random() * 30)}°`,
        angle: 90
      }
    ];

    return legend;
  },

  // 🔧 辅助方法：获取随机星座
  getRandomConstellation: function() {
    const constellations = ['白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座',
                           '天秤座', '天蝎座', '射手座', '摩羯座', '水瓶座', '双鱼座'];
    return constellations[Math.floor(Math.random() * constellations.length)];
  },

  // 🔧 辅助方法：获取嵌套对象值
  getNestedValue: function(obj, path) {
    if (!obj || !path) return undefined;

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }

    return current;
  },

  // 🔧 数据格式转换
  convertToFourPillarsFormat: function(baziInfo) {
    if (!baziInfo) return [];

    // 🔧 修复：处理不同的数据格式
    const getPillarGanZhi = (pillar) => {
      if (!pillar) return { gan: null, zhi: null };

      // 如果已经是gan/zhi格式
      if (pillar.gan && pillar.zhi) {
        return { gan: pillar.gan, zhi: pillar.zhi };
      }
      // 如果是heavenly/earthly格式
      else if (pillar.heavenly && pillar.earthly) {
        return { gan: pillar.heavenly, zhi: pillar.earthly };
      }

      return { gan: null, zhi: null };
    };

    return [
      getPillarGanZhi(baziInfo.yearPillar),
      getPillarGanZhi(baziInfo.monthPillar),
      getPillarGanZhi(baziInfo.dayPillar),
      getPillarGanZhi(baziInfo.timePillar)
    ];
  },

  // 🔧 确保八字数据完整性
  ensureBaziDataIntegrity: function(baziInfo, birthInfo) {
    console.log('🔍 检查原始八字数据结构:', {
      baziInfo: baziInfo,
      fourPillars: baziInfo?.fourPillars,
      yearPillar: baziInfo?.yearPillar,
      monthPillar: baziInfo?.monthPillar
    });

    // 🔧 修复：处理不同的数据结构
    let pillars = {};

    if (baziInfo?.fourPillars && Array.isArray(baziInfo.fourPillars)) {
      // 如果有fourPillars数组，使用它
      const [yearPillar, monthPillar, dayPillar, hourPillar] = baziInfo.fourPillars;
      pillars = {
        yearPillar: yearPillar,
        monthPillar: monthPillar,
        dayPillar: dayPillar,
        timePillar: hourPillar
      };
    } else {
      // 否则使用直接的柱属性
      pillars = {
        yearPillar: baziInfo?.yearPillar,
        monthPillar: baziInfo?.monthPillar,
        dayPillar: baziInfo?.dayPillar,
        timePillar: baziInfo?.timePillar
      };
    }

    // 默认值（使用大运计算器期望的格式：gan/zhi）
    const defaultPillars = {
      yearPillar: { gan: '甲', zhi: '子' },
      monthPillar: { gan: '丙', zhi: '寅' },
      dayPillar: { gan: '戊', zhi: '午' },
      timePillar: { gan: '壬', zhi: '戌' }
    };

    const ensuredData = {
      ...baziInfo,
      birthInfo: birthInfo,
      yearPillar: pillars.yearPillar || defaultPillars.yearPillar,
      monthPillar: pillars.monthPillar || defaultPillars.monthPillar,
      dayPillar: pillars.dayPillar || defaultPillars.dayPillar,
      timePillar: pillars.timePillar || defaultPillars.timePillar
    };

    // 🔧 统一数据格式：确保每个柱都有gan和zhi属性
    ['yearPillar', 'monthPillar', 'dayPillar', 'timePillar'].forEach(pillarName => {
      const pillar = ensuredData[pillarName];

      // 如果是heavenly/earthly格式，转换为gan/zhi格式
      if (pillar.heavenly && pillar.earthly) {
        ensuredData[pillarName] = {
          gan: pillar.heavenly,
          zhi: pillar.earthly
        };
      }
      // 如果gan或zhi缺失，使用默认值
      else if (!pillar.gan || !pillar.zhi) {
        console.warn(`⚠️ ${pillarName} 数据不完整，使用默认值:`, defaultPillars[pillarName]);
        ensuredData[pillarName] = defaultPillars[pillarName];
      }
    });

    console.log('🔧 八字数据完整性检查完成:', {
      原始数据: !!baziInfo,
      年柱: ensuredData.yearPillar,
      月柱: ensuredData.monthPillar,
      日柱: ensuredData.dayPillar,
      时柱: ensuredData.timePillar
    });

    return ensuredData;
  },

  // 🎯 神煞计算
  calculateAllShenshas: function(fourPillars) {
    try {
      const calculator = this.getShenshaCalculator();
      if (!calculator) {
        console.warn('⚠️ 神煞计算器不可用');
        return { auspicious: [], inauspicious: [], neutral: [], stats: {} };
      }

      const allShenshas = calculator.calculateShensha(fourPillars) || [];
      return this.categorizeShenshas(allShenshas);
    } catch (error) {
      console.error('❌ 神煞计算失败:', error);
      return { auspicious: [], inauspicious: [], neutral: [], stats: {} };
    }
  },

  // 🎯 获取神煞计算器
  getShenshaCalculator: function() {
    if (this.completeBaziCalculator) {
      return {
        calculateShensha: (fourPillars) => {
          const birthInfo = wx.getStorageSync('bazi_birth_info') || {};
          const result = this.completeBaziCalculator.calculateShensha(fourPillars, birthInfo);

          // 🔧 修复：转换返回格式为数组
          if (result && typeof result === 'object') {
            const allShenshas = [];

            // 添加吉星
            if (result.auspicious_stars && Array.isArray(result.auspicious_stars)) {
              result.auspicious_stars.forEach(star => {
                allShenshas.push({
                  ...star,
                  type: 'auspicious',
                  effect: '吉'
                });
              });
            }

            // 添加凶煞
            if (result.inauspicious_stars && Array.isArray(result.inauspicious_stars)) {
              result.inauspicious_stars.forEach(star => {
                allShenshas.push({
                  ...star,
                  type: 'inauspicious',
                  effect: '凶'
                });
              });
            }

            console.log('🔧 神煞数据格式转换:', {
              原始格式: typeof result,
              吉星数量: result.auspicious_stars?.length || 0,
              凶煞数量: result.inauspicious_stars?.length || 0,
              转换后数组长度: allShenshas.length
            });

            return allShenshas;
          }

          return [];
        }
      };
    }
    return null;
  },

  // 🎯 神煞分类（修复数据类型问题）
  categorizeShenshas: function(shenshas) {
    const auspicious = [];
    const inauspicious = [];
    const neutral = [];

    // 🔧 修复：确保shenshas是数组
    let shenshaArray = [];
    if (Array.isArray(shenshas)) {
      shenshaArray = shenshas;
    } else if (shenshas && typeof shenshas === 'object') {
      // 如果是对象，尝试提取数组
      if (shenshas.shenshas && Array.isArray(shenshas.shenshas)) {
        shenshaArray = shenshas.shenshas;
      } else if (shenshas.results && Array.isArray(shenshas.results)) {
        shenshaArray = shenshas.results;
      } else {
        // 如果是对象但不包含数组，转换为数组
        shenshaArray = Object.values(shenshas).filter(item =>
          item && typeof item === 'object' && item.name
        );
      }
    }

    console.log('🔍 神煞数据类型检查:', {
      原始类型: typeof shenshas,
      是否数组: Array.isArray(shenshas),
      处理后数组长度: shenshaArray.length,
      示例数据: shenshaArray[0]
    });

    shenshaArray.forEach(shensha => {
      if (shensha.type === 'auspicious' || shensha.effect?.includes('吉')) {
        auspicious.push(shensha);
      } else if (shensha.type === 'inauspicious' || shensha.effect?.includes('凶')) {
        inauspicious.push(shensha);
      } else {
        neutral.push(shensha);
      }
    });

    return {
      auspicious,
      inauspicious,
      neutral,
      stats: {
        totalCount: shenshaArray.length,
        auspiciousCount: auspicious.length,
        inauspiciousCount: inauspicious.length,
        neutralCount: neutral.length
      }
    };
  },

  // 🏛️ 历史验证
  performHistoricalVerification: async function(baziInfo) {
    try {
      if (!this.celebrityDatabaseAPI || !this.baziSimilarityMatcher) {
        return { celebrities: [], stats: {} };
      }

      const celebrities = await this.celebrityDatabaseAPI.findSimilarCelebrities(baziInfo);

      // 🔧 修复：手动计算统计信息，因为calculateStats方法不存在
      const stats = {
        totalCount: celebrities.length,
        averageSimilarity: celebrities.length > 0 ?
          celebrities.reduce((sum, c) => sum + (c.similarity || 0), 0) / celebrities.length : 0,
        highSimilarityCount: celebrities.filter(c => (c.similarity || 0) > 0.7).length,
        categories: {}
      };

      // 按相似度等级分类
      celebrities.forEach(celebrity => {
        const level = this.baziSimilarityMatcher.getSimilarityLevel(celebrity.similarity || 0);
        stats.categories[level] = (stats.categories[level] || 0) + 1;
      });

      console.log('🏛️ 历史验证统计:', stats);

      return { celebrities, stats };
    } catch (error) {
      console.error('❌ 历史验证失败:', error);
      return { celebrities: [], stats: {} };
    }
  },

  // 🎯 专业五行计算（统一接口）
  calculateUnifiedWuxing: function(baziData) {
    try {
      console.log('🎯 使用专业五行计算引擎...');

      const fourPillars = this.convertToFourPillarsFormat(baziData);
      if (fourPillars && fourPillars.length === 4) {
        const result = this.professionalWuxingEngine.generateDetailedReport(fourPillars);

        if (result && result.results && result.results.finalPowers) {
          return {
            wood: Math.round(result.results.finalPowers.木 || 0),
            fire: Math.round(result.results.finalPowers.火 || 0),
            earth: Math.round(result.results.finalPowers.土 || 0),
            metal: Math.round(result.results.finalPowers.金 || 0),
            water: Math.round(result.results.finalPowers.水 || 0),
            algorithm: result.algorithm,
            version: result.version,
            professionalLevel: true,
            source: 'professional_engine',
            calculationDetails: result.calculationDetails,
            seasonalInfo: result.calculationDetails.seasonalInfo
          };
        }
      }

      console.warn('⚠️ 专业五行计算失败，使用默认值');
      return this.getDefaultWuxingResult();

    } catch (error) {
      console.error('❌ 专业五行计算错误:', error);
      return this.getDefaultWuxingResult();
    }
  },

  // 🔧 默认五行结果
  getDefaultWuxingResult: function() {
    return {
      wood: 20, fire: 20, earth: 20, metal: 20, water: 20,
      algorithm: '默认算法',
      version: '安全模式',
      professionalLevel: false,
      source: 'default'
    };
  },

  // 🔧 数据预处理（避免前端显示错误）
  preprocessDisplayData: function() {
    const data = this.data;

    // 处理相似度显示
    if (data.similarCelebrities) {
      data.similarCelebrities.forEach(celebrity => {
        if (celebrity.similarity && typeof celebrity.similarity === 'number') {
          celebrity.similarity_display = (celebrity.similarity * 100).toFixed(1) + '%';
        }
        if (celebrity.confidence && typeof celebrity.confidence === 'number') {
          celebrity.confidence_display = (celebrity.confidence * 100).toFixed(0) + '%';
        }
      });
    }

    return data;
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
    console.log(`切换到标签页: ${tab}`);
  }
});
