// pages/profile/security/index.js
Page({
  data: {
    userInfo: null,
    showDeleteAccountModal: false,
    verificationCode: '',
    password: '',
    countdown: 0,
    timer: null,
    exportProgress: 0,
    showExportProgress: false
  },

  onLoad: function() {
    const app = getApp();
    this.setData({
      userInfo: app.globalData.userInfo
    });
  },

  onUnload: function() {
    // 清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 修改密码
  changePassword: function() {
    wx.showToast({
      title: '修改密码功能开发中',
      icon: 'none'
    });
    // 实际应用中应该跳转到修改密码页面
    // wx.navigateTo({
    //   url: '/pages/profile/security/change_password'
    // });
  },

  // 导出数据
  exportData: function() {
    wx.showModal({
      title: '导出数据',
      content: '确定要导出您的所有咨询记录和个人数据吗？',
      success: res => {
        if (res.confirm) {
          // 显示导出进度
          this.setData({
            showExportProgress: true,
            exportProgress: 0
          });

          // 模拟导出进度
          const timer = setInterval(() => {
            if (this.data.exportProgress >= 100) {
              clearInterval(timer);
              this.setData({
                showExportProgress: false
              });

              wx.showModal({
                title: '导出成功',
                content: '数据已成功导出，请在系统通知中查看下载链接',
                showCancel: false
              });
            } else {
              this.setData({
                exportProgress: this.data.exportProgress + 10
              });
            }
          }, 300);

          // 实际应用中应该调用云函数导出数据
          // wx.cloud.callFunction({
          //   name: 'exportUserData',
          //   success: res => {
          //     wx.showModal({
          //       title: '导出成功',
          //       content: '数据已成功导出，请在系统通知中查看下载链接',
          //       showCancel: false
          //     });
          //   },
          //   fail: err => {
          //     console.error('导出数据失败', err);
          //     wx.showToast({
          //       title: '导出失败，请重试',
          //       icon: 'none'
          //     });
          //   }
          // });
        }
      }
    });
  },

  // 显示注销账号弹窗
  showDeleteAccount: function() {
    wx.showModal({
      title: '警告',
      content: '注销账号将删除您的所有数据，此操作不可逆，确定要继续吗？',
      success: res => {
        if (res.confirm) {
          this.setData({
            showDeleteAccountModal: true,
            verificationCode: '',
            password: ''
          });
        }
      }
    });
  },

  // 发送验证码
  sendVerificationCode: function() {
    if (this.data.countdown > 0) return;

    // 模拟发送验证码
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    });

    // 开始倒计时
    this.setData({
      countdown: 60
    });

    const timer = setInterval(() => {
      if (this.data.countdown <= 1) {
        clearInterval(timer);
        this.setData({
          countdown: 0
        });
      } else {
        this.setData({
          countdown: this.data.countdown - 1
        });
      }
    }, 1000);

    this.setData({
      timer: timer
    });

    // 实际应用中应该调用云函数发送验证码
    // wx.cloud.callFunction({
    //   name: 'sendVerificationCode',
    //   data: {
    //     type: 'deleteAccount'
    //   },
    //   success: res => {
    //     wx.showToast({
    //       title: '验证码已发送',
    //       icon: 'success'
    //     });

    //     // 开始倒计时
    //     this.setData({
    //       countdown: 60
    //     });

    //     const timer = setInterval(() => {
    //       if (this.data.countdown <= 1) {
    //         clearInterval(timer);
    //         this.setData({
    //           countdown: 0
    //         });
    //       } else {
    //         this.setData({
    //           countdown: this.data.countdown - 1
    //         });
    //       }
    //     }, 1000);

    //     this.setData({
    //       timer: timer
    //     });
    //   },
    //   fail: err => {
    //     console.error('发送验证码失败', err);
    //     wx.showToast({
    //       title: '发送失败，请重试',
    //       icon: 'none'
    //     });
    //   }
    // });
  },

  // 验证码输入
  onVerificationCodeInput: function(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 取消注销账号
  cancelDeleteAccount: function() {
    this.setData({
      showDeleteAccountModal: false
    });
  },

  // 确认注销账号
  confirmDeleteAccount: function() {
    if (!this.data.verificationCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    // 模拟验证过程
    wx.showLoading({
      title: '验证中'
    });

    setTimeout(() => {
      wx.hideLoading();

      // 二次确认
      wx.showModal({
        title: '最终确认',
        content: '注销后，您的咨询记录将保留30天，身份信息将立即匿名化，确定要注销吗？',
        success: res => {
          if (res.confirm) {
            wx.showLoading({
              title: '处理中'
            });

            setTimeout(() => {
              wx.hideLoading();
              this.setData({
                showDeleteAccountModal: false
              });

              wx.showToast({
                title: '账号已注销',
                icon: 'success'
              });

              // 跳转到登录页面
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }, 1500);
            }, 2000);

            // 实际应用中应该调用云函数注销账号
            // wx.cloud.callFunction({
            //   name: 'deleteAccount',
            //   data: {
            //     verificationCode: this.data.verificationCode,
            //     password: this.data.password
            //   },
            //   success: res => {
            //     this.setData({
            //       showDeleteAccountModal: false
            //     });

            //     wx.showToast({
            //       title: '账号已注销',
            //       icon: 'success'
            //     });

            //     // 跳转到登录页面
            //     setTimeout(() => {
            //       wx.reLaunch({
            //         url: '/pages/index/index'
            //       });
            //     }, 1500);
            //   },
            //   fail: err => {
            //     console.error('注销账号失败', err);
            //     wx.showToast({
            //       title: '注销失败，请重试',
            //       icon: 'none'
            //     });
            //   }
            // });
          }
        }
      });
    }, 1500);
  }
});