/**
 * 测试更新后的前端神煞计算系统
 * 验证权威神煞计算方法的准确性
 */

// 模拟前端环境
global.console = console;

// 测试数据：2021年6月24日 19:30 北京时间
const TEST_BAZI = {
  year: { gan: '辛', zhi: '丑' },
  month: { gan: '甲', zhi: '午' },
  day: { gan: '癸', zhi: '卯' },
  hour: { gan: '壬', zhi: '戌' }
};

// "问真八字"标准结果
const WENZHEN_STANDARD = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

// 模拟前端神煞计算函数
const mockFrontendCalculations = {
  // 天乙贵人（权威版本）
  calculateTianyiGuiren: function(dayGan, fourPillars) {
    const tianyiMap = {
      '甲': ['丑', '未'], '戊': ['丑', '未'], '庚': ['丑', '未'],
      '乙': ['子', '申'], '己': ['子', '申'],
      '丙': ['亥', '酉'], '丁': ['亥', '酉'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳'],
      '辛': ['寅', '午']
    };
    
    const nobles = tianyiMap[dayGan] || [];
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (nobles.includes(pillar.zhi)) {
        result.push({ pillar: pillarNames[index], name: '天乙贵人' });
      }
    });
    
    return result;
  },

  // 文昌贵人（权威版本）
  calculateWenchangGuiren: function(dayGan, fourPillars) {
    const wenchangMap = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉', '戊': '申',
      '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
    };
    
    const wenchangZhi = wenchangMap[dayGan];
    if (!wenchangZhi) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (pillar.zhi === wenchangZhi) {
        result.push({ pillar: pillarNames[index], name: '文昌贵人' });
      }
    });
    
    return result;
  },

  // 福星贵人（权威版本）
  calculateFuxingGuiren: function(dayGan, fourPillars) {
    const fuxingMap = {
      '甲': '寅', '乙': '丑', '丙': '子', '丁': '酉', '戊': '申',
      '己': '未', '庚': '午', '辛': '巳', '壬': '辰', '癸': '卯'
    };
    
    const fuxingZhi = fuxingMap[dayGan];
    if (!fuxingZhi) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (pillar.zhi === fuxingZhi) {
        result.push({ pillar: pillarNames[index], name: '福星贵人' });
      }
    });
    
    return result;
  },

  // 德秀贵人（权威版本）
  calculateDexiuGuiren: function(monthZhi, fourPillars) {
    const dexiuMap = {
      // 寅午戌月
      '寅': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
      '午': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
      '戌': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
      
      // 申子辰月
      '申': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
      '子': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
      '辰': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
      
      // 巳酉丑月
      '巳': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
      '酉': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
      '丑': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
      
      // 亥卯未月
      '亥': { de: ['甲', '乙'], xiu: ['丁', '壬'] },
      '卯': { de: ['甲', '乙'], xiu: ['丁', '壬'] },
      '未': { de: ['甲', '乙'], xiu: ['丁', '壬'] }
    };
    
    const config = dexiuMap[monthZhi];
    if (!config) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      const gan = pillar.gan;
      if (config.de.includes(gan) || config.xiu.includes(gan)) {
        result.push({ pillar: pillarNames[index], name: '德秀贵人' });
      }
    });
    
    return result;
  },

  // 元辰（权威版本）
  calculateYuanchen: function(yearZhi, gender, fourPillars) {
    const chongMap = {
      '子': '午', '午': '子', '丑': '未', '未': '丑',
      '寅': '申', '申': '寅', '卯': '酉', '酉': '卯',
      '辰': '戌', '戌': '辰', '巳': '亥', '亥': '巳'
    };
    
    const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const yangZhi = ['子', '寅', '辰', '午', '申', '戌'];
    const isYangYear = yangZhi.includes(yearZhi);
    
    const chongZhi = chongMap[yearZhi];
    const chongIndex = zhiOrder.indexOf(chongZhi);
    
    let yuanchenZhi;
    
    if ((gender === 'male' && isYangYear) || (gender === 'female' && !isYangYear)) {
      yuanchenZhi = zhiOrder[(chongIndex + 1) % 12];
    } else {
      yuanchenZhi = zhiOrder[(chongIndex - 1 + 12) % 12];
    }
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (pillar.zhi === yuanchenZhi) {
        result.push({ pillar: pillarNames[index], name: '元辰' });
      }
    });
    
    return result;
  },

  // 咸池桃花（权威版本）
  calculateXianchi: function(yearZhi, fourPillars) {
    const xianchiMap = {
      '申': '酉', '子': '酉', '辰': '酉',
      '寅': '卯', '午': '卯', '戌': '卯',
      '巳': '午', '酉': '午', '丑': '午',
      '亥': '子', '卯': '子', '未': '子'
    };
    
    const taohuaZhi = xianchiMap[yearZhi];
    if (!taohuaZhi) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (pillar.zhi === taohuaZhi) {
        result.push({ pillar: pillarNames[index], name: '桃花' });
      }
    });
    
    return result;
  },

  // 孤辰寡宿（权威版本）
  calculateGuasuGuchen: function(yearZhi, fourPillars) {
    const guasuMap = {
      '亥': { guchen: '寅', guasu: '戌' }, '子': { guchen: '寅', guasu: '戌' }, '丑': { guchen: '寅', guasu: '戌' },
      '寅': { guchen: '巳', guasu: '丑' }, '卯': { guchen: '巳', guasu: '丑' }, '辰': { guchen: '巳', guasu: '丑' },
      '巳': { guchen: '申', guasu: '辰' }, '午': { guchen: '申', guasu: '辰' }, '未': { guchen: '申', guasu: '辰' },
      '申': { guchen: '亥', guasu: '未' }, '酉': { guchen: '亥', guasu: '未' }, '戌': { guchen: '亥', guasu: '未' }
    };
    
    const config = guasuMap[yearZhi];
    if (!config) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      const zhi = pillar.zhi;
      if (zhi === config.guasu) {
        result.push({ pillar: pillarNames[index], name: '寡宿' });
      }
    });
    
    return result;
  },

  // 月德合（权威版本）
  calculateYuehe: function(monthZhi, fourPillars) {
    const yueheMap = {
      '寅': '辛', '午': '辛', '戌': '辛',  // 寅午戌月德合辛
      '亥': '己', '卯': '己', '未': '己',  // 亥卯未月德合己
      '申': '丁', '子': '丁', '辰': '丁',  // 申子辰月德合丁
      '巳': '乙', '酉': '乙', '丑': '乙'   // 巳酉丑月德合乙
    };
    
    const yueheGan = yueheMap[monthZhi];
    if (!yueheGan) return [];
    
    const result = [];
    const pillars = [fourPillars.year, fourPillars.month, fourPillars.day, fourPillars.hour];
    const pillarNames = ['year', 'month', 'day', 'hour'];
    
    pillars.forEach((pillar, index) => {
      if (pillar.gan === yueheGan) {
        result.push({ pillar: pillarNames[index], name: '月德合' });
      }
    });
    
    return result;
  }
};

// 测试更新后的前端神煞系统
console.log('=== 测试更新后的前端神煞计算系统 ===');
console.log('');

console.log('📊 测试数据：');
console.log(`年柱：${TEST_BAZI.year.gan}${TEST_BAZI.year.zhi}`);
console.log(`月柱：${TEST_BAZI.month.gan}${TEST_BAZI.month.zhi}`);
console.log(`日柱：${TEST_BAZI.day.gan}${TEST_BAZI.day.zhi}`);
console.log(`时柱：${TEST_BAZI.hour.gan}${TEST_BAZI.hour.zhi}`);
console.log('');

// 执行所有神煞计算
const allResults = [];

// 1. 天乙贵人
allResults.push(...mockFrontendCalculations.calculateTianyiGuiren(TEST_BAZI.day.gan, TEST_BAZI));

// 2. 文昌贵人
allResults.push(...mockFrontendCalculations.calculateWenchangGuiren(TEST_BAZI.day.gan, TEST_BAZI));

// 3. 福星贵人
allResults.push(...mockFrontendCalculations.calculateFuxingGuiren(TEST_BAZI.day.gan, TEST_BAZI));

// 4. 德秀贵人
allResults.push(...mockFrontendCalculations.calculateDexiuGuiren(TEST_BAZI.month.zhi, TEST_BAZI));

// 5. 元辰
allResults.push(...mockFrontendCalculations.calculateYuanchen(TEST_BAZI.year.zhi, 'male', TEST_BAZI));

// 6. 咸池桃花
allResults.push(...mockFrontendCalculations.calculateXianchi(TEST_BAZI.year.zhi, TEST_BAZI));

// 7. 孤辰寡宿
allResults.push(...mockFrontendCalculations.calculateGuasuGuchen(TEST_BAZI.year.zhi, TEST_BAZI));

// 8. 月德合
allResults.push(...mockFrontendCalculations.calculateYuehe(TEST_BAZI.month.zhi, TEST_BAZI));

console.log('🔮 前端神煞计算结果：');
const groupedResults = {};
allResults.forEach(result => {
  if (!groupedResults[result.pillar]) {
    groupedResults[result.pillar] = [];
  }
  groupedResults[result.pillar].push(result.name);
});

['year', 'month', 'day', 'hour'].forEach(pillar => {
  const pillarName = { year: '年柱', month: '月柱', day: '日柱', hour: '时柱' }[pillar];
  const shenshas = groupedResults[pillar] || [];
  console.log(`${pillarName}：${shenshas.length > 0 ? shenshas.join('、') : '无'}`);
});

console.log('');
console.log('📋 与"问真八字"标准对比：');
['year', 'month', 'day', 'hour'].forEach(pillar => {
  const pillarName = { year: '年柱', month: '月柱', day: '日柱', hour: '时柱' }[pillar];
  const ourResults = groupedResults[pillar] || [];
  const standard = WENZHEN_STANDARD[pillar] || [];
  
  const matches = ourResults.filter(s => standard.includes(s));
  const missing = standard.filter(s => !ourResults.includes(s));
  
  console.log(`${pillarName}：`);
  console.log(`  ✅ 匹配：${matches.length > 0 ? matches.join('、') : '无'}`);
  console.log(`  ❌ 缺失：${missing.length > 0 ? missing.join('、') : '无'}`);
});

console.log('');
console.log('📈 准确率统计：');
const totalStandard = Object.values(WENZHEN_STANDARD).flat().length;
const totalMatches = Object.keys(groupedResults).reduce((sum, pillar) => {
  const ourResults = groupedResults[pillar] || [];
  const standard = WENZHEN_STANDARD[pillar] || [];
  return sum + ourResults.filter(s => standard.includes(s)).length;
}, 0);

console.log(`总标准神煞数：${totalStandard}`);
console.log(`成功匹配数：${totalMatches}`);
console.log(`准确率：${((totalMatches / totalStandard) * 100).toFixed(1)}%`);

console.log('');
console.log('🎯 前端系统更新成果：');
console.log('1. ✅ 天乙贵人：基于《三命通会》权威古籍');
console.log('2. ✅ 文昌贵人：基于权威古诀"甲乙巳午报君知"');
console.log('3. ✅ 福星贵人：基于权威古诀"甲木相邀入虎乡"');
console.log('4. ✅ 德秀贵人：基于《三命通会》完整月令对应表');
console.log('5. ✅ 元辰：基于《三命通会》精确阴阳男女计算规则');
console.log('6. ✅ 咸池桃花：基于《三命通会》传统三合局计算');
console.log('7. ✅ 孤辰寡宿：基于《三命通会》完整四季对应关系');
console.log('8. ✅ 月德合：基于权威古籍月德合化关系');
