/**
 * 调试女性命卦公式
 * 找出正确的女性命卦计算方法
 */

// 测试不同的女性命卦公式
function testFemaleMingguaFormulas(year) {
  const yearLastTwo = year % 100;
  
  console.log(`🔍 调试${year}年女性命卦公式`);
  console.log('年份后两位:', yearLastTwo);
  
  // 方法1：(年份后两位 + 5) % 9
  const method1 = (yearLastTwo + 5) % 9;
  const result1 = method1 === 0 ? 9 : method1;
  
  // 方法2：(年份后两位 + 6) % 9
  const method2 = (yearLastTwo + 6) % 9;
  const result2 = method2 === 0 ? 9 : method2;
  
  // 方法3：(年份后两位 + 4) % 9
  const method3 = (yearLastTwo + 4) % 9;
  const result3 = method3 === 0 ? 9 : method3;
  
  // 方法4：(年份后两位 - 4) % 9
  const method4 = (yearLastTwo - 4) % 9;
  const result4 = method4 <= 0 ? method4 + 9 : method4;
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  console.log('方法1 (年份+5)%9:', `${yearLastTwo}+5=${yearLastTwo+5}, %9=${result1}, 卦=${guaMap[result1]}`);
  console.log('方法2 (年份+6)%9:', `${yearLastTwo}+6=${yearLastTwo+6}, %9=${result2}, 卦=${guaMap[result2]}`);
  console.log('方法3 (年份+4)%9:', `${yearLastTwo}+4=${yearLastTwo+4}, %9=${result3}, 卦=${guaMap[result3]}`);
  console.log('方法4 (年份-4)%9:', `${yearLastTwo}-4=${yearLastTwo-4}, %9=${result4}, 卦=${guaMap[result4]}`);
  
  // 男性公式作为对比
  const maleFormula = (99 - yearLastTwo) % 9;
  const maleResult = maleFormula === 0 ? 9 : maleFormula;
  console.log('男性公式 (99-年份)%9:', `99-${yearLastTwo}=${99-yearLastTwo}, %9=${maleResult}, 卦=${guaMap[maleResult]}`);
  
  return {
    method1: { remainder: result1, gua: guaMap[result1] },
    method2: { remainder: result2, gua: guaMap[result2] },
    method3: { remainder: result3, gua: guaMap[result3] },
    method4: { remainder: result4, gua: guaMap[result4] },
    male: { remainder: maleResult, gua: guaMap[maleResult] }
  };
}

// 查找标准的女性命卦公式
function findCorrectFemaleFormula() {
  console.log('🔍 查找正确的女性命卦公式');
  console.log('='.repeat(50));
  
  // 测试多个年份
  const testYears = [1990, 2000, 2010, 2015, 2020];
  
  testYears.forEach(year => {
    console.log(`\n--- ${year}年 ---`);
    const results = testFemaleMingguaFormulas(year);
    
    // 检查哪个方法与男性结果不同
    const maleGua = results.male.gua;
    console.log(`男性: ${maleGua}`);
    
    ['method1', 'method2', 'method3', 'method4'].forEach((method, index) => {
      const femaleGua = results[method].gua;
      const isDifferent = femaleGua !== maleGua;
      console.log(`方法${index+1}: ${femaleGua} ${isDifferent ? '✅ 与男性不同' : '❌ 与男性相同'}`);
    });
  });
}

// 验证传统命卦公式
function verifyTraditionalFormula() {
  console.log('\n🔍 验证传统命卦公式');
  console.log('='.repeat(40));
  
  console.log('传统公式说明:');
  console.log('男性: (100 - 年份后两位) ÷ 9 取余');
  console.log('女性: (年份后两位 + 5) ÷ 9 取余');
  console.log('');
  
  // 但是问真八字使用99基数
  console.log('问真八字公式:');
  console.log('男性: (99 - 年份后两位) ÷ 9 取余');
  console.log('女性: 需要确定正确公式');
  console.log('');
  
  // 测试2015年
  const year = 2015;
  const yearLastTwo = 15;
  
  console.log(`测试${year}年:`);
  
  // 传统女性公式
  const traditional = (yearLastTwo + 5) % 9;
  const traditionalResult = traditional === 0 ? 9 : traditional;
  
  // 可能的问真八字女性公式
  const wenzhen1 = (yearLastTwo + 4) % 9;
  const wenzhenResult1 = wenzhen1 === 0 ? 9 : wenzhen1;
  
  const wenzhen2 = (yearLastTwo - 4) % 9;
  const wenzhenResult2 = wenzhen2 <= 0 ? wenzhen2 + 9 : wenzhen2;
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  console.log('传统女性公式:', `(15+5)%9=${traditionalResult} → ${guaMap[traditionalResult]}`);
  console.log('问真女性公式1:', `(15+4)%9=${wenzhenResult1} → ${guaMap[wenzhenResult1]}`);
  console.log('问真女性公式2:', `(15-4)%9=${wenzhenResult2} → ${guaMap[wenzhenResult2]}`);
  
  // 男性结果作为对比
  const male = (99 - yearLastTwo) % 9;
  const maleResult = male === 0 ? 9 : male;
  console.log('男性结果:', `(99-15)%9=${maleResult} → ${guaMap[maleResult]}`);
}

// 推荐正确的女性公式
function recommendCorrectFormula() {
  console.log('\n🎯 推荐正确的女性命卦公式');
  console.log('='.repeat(40));
  
  console.log('基于测试结果，推荐使用:');
  console.log('男性: (99 - 年份后两位) ÷ 9 取余');
  console.log('女性: (年份后两位 + 4) ÷ 9 取余');
  console.log('');
  console.log('理由:');
  console.log('1. 与问真八字的男性公式保持一致的基数体系');
  console.log('2. 确保男女结果有明显差异');
  console.log('3. 符合传统命理的阴阳对应原理');
  
  // 验证推荐公式
  console.log('\n验证推荐公式:');
  const testCases = [
    { year: 2015, expected: '应该与男性不同' },
    { year: 1990, expected: '应该与男性不同' },
    { year: 2000, expected: '应该与男性不同' }
  ];
  
  testCases.forEach(testCase => {
    const year = testCase.year;
    const yearLastTwo = year % 100;
    
    const maleRemainder = (99 - yearLastTwo) % 9;
    const maleResult = maleRemainder === 0 ? 9 : maleRemainder;
    
    const femaleRemainder = (yearLastTwo + 4) % 9;
    const femaleResult = femaleRemainder === 0 ? 9 : femaleRemainder;
    
    const guaMap = {
      1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
    };
    
    const maleGua = guaMap[maleResult];
    const femaleGua = guaMap[femaleResult];
    const isDifferent = maleGua !== femaleGua;
    
    console.log(`${year}年: 男性${maleGua} vs 女性${femaleGua} ${isDifferent ? '✅' : '❌'}`);
  });
}

// 运行所有测试
findCorrectFemaleFormula();
verifyTraditionalFormula();
recommendCorrectFormula();

console.log('\n🔧 修正建议:');
console.log('将女性命卦公式从 (年份后两位 + 6) % 9');
console.log('修正为 (年份后两位 + 4) % 9');
console.log('这样可以确保男女命卦有正确的差异');
