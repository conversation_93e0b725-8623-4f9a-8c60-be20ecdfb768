/* pages/profile/security/index.wxss */

.security-container {
  padding: 30rpx;
  background-color: #f8f9fc;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999999;
}

.security-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.item-title {
  font-size: 30rpx;
  color: #333333;
}

.delete-account .item-title {
  color: #FF6B6B;
}

.item-right {
  display: flex;
  align-items: center;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.security-notice {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.notice-item {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

/* 导出进度条 */
.export-progress {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #6C5CE7;
  text-align: center;
  margin-top: 10rpx;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.verification-section, .password-section {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.verification-input-group {
  display: flex;
  align-items: center;
}

.verification-input {
  flex: 1;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.send-code-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #6C5CE7;
  border-radius: 8rpx;
  text-align: center;
  padding: 0;
}

.send-code-btn.disabled {
  background-color: #cccccc;
}

.password-input {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.warning-text {
  font-size: 26rpx;
  color: #FF6B6B;
  margin-top: 20rpx;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
}

.cancel-btn {
  color: #999999;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #6C5CE7;
  font-weight: bold;
}

.delete-btn {
  color: #FF6B6B;
}