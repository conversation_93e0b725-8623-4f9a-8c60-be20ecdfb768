// test_enhanced_pattern_analyzer.js
// 测试增强版格局分析器

// 引入分析器
const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer.js');

/**
 * 测试格局判定算法
 */
function testPatternAnalyzer() {
  console.log('🧪 开始测试增强版格局分析器');
  console.log('=' * 50);

  const analyzer = new EnhancedPatternAnalyzer();

  // 测试用例1：正官格
  console.log('\n📋 测试用例1：正官格');
  const testCase1 = {
    fourPillars: [
      { gan: '戊', zhi: '戌' }, // 年柱
      { gan: '丁', zhi: '巳' }, // 月柱
      { gan: '甲', zhi: '子' }, // 日柱
      { gan: '己', zhi: '未' }  // 时柱
    ],
    birthDateTime: new Date(1990, 4, 15, 10, 30), // 1990年5月15日10:30
    bazi: {
      birth_datetime: new Date(1990, 4, 15, 10, 30)
    }
  };

  const result1 = analyzer.determinePattern(testCase1.bazi, testCase1.fourPillars, testCase1.birthDateTime);
  console.log('🎯 分析结果1:', JSON.stringify(result1, null, 2));

  // 测试用例2：从格
  console.log('\n📋 测试用例2：从格测试');
  const testCase2 = {
    fourPillars: [
      { gan: '庚', zhi: '申' }, // 年柱
      { gan: '庚', zhi: '申' }, // 月柱
      { gan: '乙', zhi: '亥' }, // 日柱
      { gan: '庚', zhi: '申' }  // 时柱
    ],
    birthDateTime: new Date(1985, 7, 20, 14, 0), // 1985年8月20日14:00
    bazi: {
      birth_datetime: new Date(1985, 7, 20, 14, 0)
    }
  };

  const result2 = analyzer.determinePattern(testCase2.bazi, testCase2.fourPillars, testCase2.birthDateTime);
  console.log('🎯 分析结果2:', JSON.stringify(result2, null, 2));

  // 测试用例3：专旺格
  console.log('\n📋 测试用例3：专旺格测试');
  const testCase3 = {
    fourPillars: [
      { gan: '丙', zhi: '午' }, // 年柱
      { gan: '丁', zhi: '巳' }, // 月柱
      { gan: '丙', zhi: '午' }, // 日柱
      { gan: '丁', zhi: '巳' }  // 时柱
    ],
    birthDateTime: new Date(1988, 5, 10, 12, 0), // 1988年6月10日12:00
    bazi: {
      birth_datetime: new Date(1988, 5, 10, 12, 0)
    }
  };

  const result3 = analyzer.determinePattern(testCase3.bazi, testCase3.fourPillars, testCase3.birthDateTime);
  console.log('🎯 分析结果3:', JSON.stringify(result3, null, 2));

  // 测试清浊评估功能
  console.log('\n🧪 测试清浊评估功能');
  testClarityCalculation(analyzer);

  // 测试月令藏干功能
  console.log('\n🧪 测试月令藏干功能');
  testMonthQiCalculation(analyzer);

  console.log('\n✅ 格局分析器测试完成');
}

/**
 * 测试清浊评估计算
 */
function testClarityCalculation(analyzer) {
  const testFourPillars = [
    { gan: '甲', zhi: '寅' },
    { gan: '丁', zhi: '巳' },
    { gan: '甲', zhi: '子' },
    { gan: '己', zhi: '未' }
  ];

  const tenGods = analyzer.mapTenGods('甲', testFourPillars);
  const clarityScore = analyzer.calculateClarity(tenGods, testFourPillars);
  
  console.log('📊 清浊评估测试:');
  console.log('  十神映射:', JSON.stringify(tenGods, null, 2));
  console.log('  清浊得分:', clarityScore.toFixed(3));
}

/**
 * 测试月令藏干计算
 */
function testMonthQiCalculation(analyzer) {
  const testCases = [
    { zhi: '寅', date: new Date(2024, 1, 5), expected: 'early' },
    { zhi: '寅', date: new Date(2024, 1, 10), expected: 'middle' },
    { zhi: '寅', date: new Date(2024, 1, 20), expected: 'late' },
    { zhi: '午', date: new Date(2024, 5, 15), expected: 'middle' }
  ];

  console.log('📅 月令藏干测试:');
  testCases.forEach((testCase, index) => {
    const result = analyzer.getMonthQi(testCase.date, testCase.zhi);
    console.log(`  测试${index + 1}: ${testCase.zhi}月 ${testCase.date.getDate()}日`);
    console.log(`    期望: ${testCase.expected}, 实际: ${result.period}`);
    console.log(`    主气: ${result.gan}, 强度: ${result.strength}`);
  });
}

/**
 * 测试五行力量计算
 */
function testElementPowerCalculation() {
  console.log('\n🧪 测试五行力量计算');
  
  const analyzer = new EnhancedPatternAnalyzer();
  const testFourPillars = [
    { gan: '甲', zhi: '寅' }, // 木木
    { gan: '丁', zhi: '巳' }, // 火火
    { gan: '戊', zhi: '辰' }, // 土土
    { gan: '庚', zhi: '申' }  // 金金
  ];

  const monthMainQi = { gan: '甲', strength: 0.8 };
  const elementPowers = analyzer.calculateElementPowers(testFourPillars, monthMainQi);
  
  console.log('⚡ 五行力量分布:');
  Object.keys(elementPowers.percentages).forEach(element => {
    console.log(`  ${element}: ${elementPowers.percentages[element].toFixed(1)}%`);
  });
  console.log(`  总力量: ${elementPowers.total.toFixed(2)}`);
}

/**
 * 验证文档中的历史案例
 */
function validateHistoricalCases() {
  console.log('\n📚 验证历史案例');
  
  const analyzer = new EnhancedPatternAnalyzer();
  
  // 曾国藩：辛未 己亥 丙辰 己亥 - 预期正官格
  const zengGuofan = {
    fourPillars: [
      { gan: '辛', zhi: '未' },
      { gan: '己', zhi: '亥' },
      { gan: '丙', zhi: '辰' },
      { gan: '己', zhi: '亥' }
    ],
    birthDateTime: new Date(1811, 10, 26, 19, 0), // 假设时间
    bazi: { birth_datetime: new Date(1811, 10, 26, 19, 0) }
  };

  const result = analyzer.determinePattern(zengGuofan.bazi, zengGuofan.fourPillars, zengGuofan.birthDateTime);
  console.log('👑 曾国藩八字分析:');
  console.log(`  格局: ${result.pattern}`);
  console.log(`  类型: ${result.pattern_type}`);
  console.log(`  置信度: ${(result.confidence * 100).toFixed(1)}%`);
  console.log(`  清浊得分: ${result.clarity_score.toFixed(3)}`);
  
  // 验证是否符合预期
  const isCorrect = result.pattern.includes('正官') || result.pattern.includes('官');
  console.log(`  ✅ 预期正官格，实际${result.pattern}: ${isCorrect ? '符合' : '不符合'}`);
}

// 运行测试
if (require.main === module) {
  try {
    testPatternAnalyzer();
    testElementPowerCalculation();
    validateHistoricalCases();
    
    console.log('\n🎉 所有测试完成！');
    console.log('📈 格局判定算法已按照文档要求实现：');
    console.log('  ✅ 月令藏干动态调整算法');
    console.log('  ✅ 清浊评估多维度加权公式');
    console.log('  ✅ 特殊格局阈值判断');
    console.log('  ✅ 五行力量精确计算');
    console.log('  ✅ 正格与特殊格局分类');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    console.error(error.stack);
  }
}

module.exports = {
  testPatternAnalyzer,
  testClarityCalculation,
  testMonthQiCalculation,
  testElementPowerCalculation,
  validateHistoricalCases
};
