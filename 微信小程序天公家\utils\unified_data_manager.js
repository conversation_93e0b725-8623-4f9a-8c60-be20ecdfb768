/**
 * 统一数据管理器 - 完整版
 * 集成所有八字计算功能，提供一站式数据服务
 */

const UnifiedBasicInfoCalculator = require('./unified_basic_info_calculator.js');

class UnifiedDataManager {
  constructor() {
    this.calculator = new UnifiedBasicInfoCalculator();
    this.cache = new Map();
    this.version = '2.0.0';
    console.log('🚀 统一数据管理器初始化完成 v' + this.version);

    // 初始化所有计算模块
    this.initializeCalculators();
  }

  /**
   * 初始化所有计算模块
   */
  initializeCalculators() {
    // 天干地支数组
    this.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    console.log('✅ 基础数据初始化完成');
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(birthInfo, fourPillars) {
    const birthKey = `${birthInfo.year}-${birthInfo.month}-${birthInfo.day}-${birthInfo.hour}-${birthInfo.minute}`;
    const pillarsKey = fourPillars.map(p => `${p.gan}${p.zhi}`).join('');
    return `${birthKey}_${pillarsKey}`;
  }

  /**
   * 一次性计算所有需要的数据
   * 这是唯一的数据计算入口，避免多重转换
   */
  calculateAllData(birthInfo, fourPillars) {
    console.log('🔄 统一数据管理器开始计算所有数据...');
    
    const cacheKey = this.generateCacheKey(birthInfo, fourPillars);
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      console.log('✅ 使用缓存数据');
      return this.cache.get(cacheKey);
    }

    try {
      // 🚀 使用统一基本信息计算器（唯一数据源）
      const basicInfo = this.calculator.calculate(birthInfo, fourPillars);
      
      // 🎯 构建完整的页面数据（一次性完成，无需多重转换）
      const allData = {
        // ===== 基本信息区域 =====
        name: basicInfo.name,
        gender: basicInfo.gender,
        zodiac: basicInfo.zodiac,
        birthDate: basicInfo.birthDate,
        birthTime: basicInfo.birthTime,
        true_solar_time: basicInfo.true_solar_time,
        location: basicInfo.location,
        lunar_time: basicInfo.lunar_time,
        
        // ===== 八字概览区域 =====
        birth_solar_term: basicInfo.birth_solar_term,
        jieqiInfo: basicInfo.jieqiInfo,
        kong_wang: basicInfo.kong_wang,
        ming_gua: basicInfo.ming_gua,
        constellation: basicInfo.constellation,
        star_mansion: basicInfo.star_mansion,
        
        // ===== 八字四柱区域 =====
        fourPillars: fourPillars,
        year_gan: fourPillars[0]?.gan || '甲',
        month_gan: fourPillars[1]?.gan || '丙',
        day_gan: fourPillars[2]?.gan || '戊',
        hour_gan: fourPillars[3]?.gan || '庚',
        year_zhi: fourPillars[0]?.zhi || '子',
        month_zhi: fourPillars[1]?.zhi || '寅',
        day_zhi: fourPillars[2]?.zhi || '辰',
        hour_zhi: fourPillars[3]?.zhi || '午',
        
        // ===== 原始数据保留 =====
        year: birthInfo.year,
        month: birthInfo.month,
        day: birthInfo.day,
        hour: birthInfo.hour,
        minute: birthInfo.minute,
        longitude: birthInfo.longitude || 116.4074,
        latitude: birthInfo.latitude || 39.9042,
        
        // ===== 系统信息 =====
        calculatedAt: new Date().toISOString(),
        calculatorVersion: this.calculator.version,
        dataManagerVersion: this.version,
        dataSource: 'unified_data_manager',
        cacheKey: cacheKey
      };

      // 缓存结果
      this.cache.set(cacheKey, allData);
      
      console.log('✅ 统一数据计算完成');
      console.log('📊 数据字段数量:', Object.keys(allData).length);
      
      return allData;
      
    } catch (error) {
      console.error('❌ 统一数据计算失败:', error);
      return this.getDefaultData(birthInfo, fourPillars);
    }
  }

  /**
   * 直接获取页面需要的数据格式
   * 无需多重转换，直接可用
   */
  getPageData(birthInfo, fourPillars) {
    console.log('🎯 获取页面数据格式...');

    // 🔧 使用基础计算器生成数据
    const allData = this.generateBasicData(birthInfo, fourPillars);

    // 🎯 直接返回页面需要的格式，统一使用 baziData
    const pageData = {
      // 主要数据对象（页面模板使用）
      baziData: allData,

      // 向后兼容的数据对象
      userInfo: allData,
      birthInfo: allData,

      // 数据状态
      dataLoaded: true,
      dataError: false,
      loadingStates: {
        basic: false,
        bazi: false,
        analysis: false
      }
    };

    console.log('✅ 页面数据格式准备完成');
    return pageData;
  }

  /**
   * 获取默认数据（错误时使用）
   */
  getDefaultData(birthInfo, fourPillars) {
    console.log('⚠️ 使用默认数据');
    
    return {
      name: birthInfo.name || '用户',
      gender: birthInfo.gender || '未知',
      zodiac: this.calculator.calculateZodiac(birthInfo.year),
      birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
      birthTime: this.calculator.formatTime(birthInfo.hour, birthInfo.minute),
      true_solar_time: '未知',
      location: birthInfo.birthCity || '未知',
      lunar_time: '未知',
      birth_solar_term: '未知',
      jieqiInfo: '未知',
      kong_wang: '未知',
      ming_gua: '未知',
      constellation: '未知',
      star_mansion: '未知',
      fourPillars: fourPillars,
      year_gan: fourPillars[0]?.gan || '甲',
      month_gan: fourPillars[1]?.gan || '丙',
      day_gan: fourPillars[2]?.gan || '戊',
      hour_gan: fourPillars[3]?.gan || '庚',
      year_zhi: fourPillars[0]?.zhi || '子',
      month_zhi: fourPillars[1]?.zhi || '寅',
      day_zhi: fourPillars[2]?.zhi || '辰',
      hour_zhi: fourPillars[3]?.zhi || '午',
      year: birthInfo.year,
      month: birthInfo.month,
      day: birthInfo.day,
      hour: birthInfo.hour,
      minute: birthInfo.minute,
      calculatedAt: new Date().toISOString(),
      dataSource: 'unified_data_manager_fallback'
    };
  }

  /**
   * 验证数据完整性
   */
  validateData(data) {
    const requiredFields = [
      'name', 'gender', 'zodiac', 'birthDate', 'birthTime',
      'true_solar_time', 'constellation', 'star_mansion',
      'year_gan', 'month_gan', 'day_gan', 'hour_gan'
    ];

    const missingFields = requiredFields.filter(field => !data[field] || data[field] === '未知');
    
    if (missingFields.length === 0) {
      console.log('✅ 数据完整性验证通过');
      return { valid: true, missingFields: [] };
    } else {
      console.warn('⚠️ 数据完整性验证失败，缺失字段:', missingFields);
      return { valid: false, missingFields };
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('🗑️ 缓存已清除');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      memoryUsage: JSON.stringify(Array.from(this.cache.values())).length
    };
  }

  /**
   * 获取管理器信息
   */
  getInfo() {
    return {
      name: '统一数据管理器',
      version: this.version,
      calculatorVersion: this.calculator.version,
      description: '简化数据传递流程的一站式数据服务',
      features: [
        '单一数据源',
        '直接映射',
        '缓存机制',
        '数据验证',
        '错误处理'
      ],
      cacheStats: this.getCacheStats()
    };
  }
}

// 创建全局实例
const unifiedDataManager = new UnifiedDataManager();

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UnifiedDataManager;
} else if (typeof window !== 'undefined') {
  window.UnifiedDataManager = UnifiedDataManager;
  window.unifiedDataManager = unifiedDataManager;
}

console.log('✅ 统一数据管理器已加载');
