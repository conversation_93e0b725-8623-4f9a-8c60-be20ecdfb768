// pages/divination-input/index.js
// 🎯 天公师兄主系统 - 集成版占卜页面
// 用途：完整的天公师兄系统，包含多角色、多功能集成
// 用户：普通用户，完整产品体验
// 部署：主要产品，生产环境
// 维护：主要开发分支，用户界面优化

const DivinationCalculator = require('../../utils/divination_calculator');
const NavigationColor = require('../../utils/navigation_color');
// ✅ 天公师父系统也使用权威农历转换器
const AuthoritativeLunarConverter = require('../../utils/authoritative_lunar_data');
const TrueSolarTimeEngine = require('../../utils/true_solar_time_engine');
const config = require('../../utils/config.js');
const liurenConfig = require('../../utils/liuren_config.js');

Page({
  data: {
    // 主题相关
    themeClass: 'tarot-theme',

    // 角色身份信息
    role: 'tarot', // 角色类型
    master: '天公师兄', // 师父名称

    // 基本信息

    // 真太阳时相关
    trueSolarTime: {
      enabled: true, // 默认启用真太阳时
      locationInfo: null, // 位置信息
      timeDifference: 0, // 时间差（分钟）
      correctedTime: null, // 校正后的时间
      correctedTimeDisplay: '', // 格式化显示
      showCorrection: false, // 是否显示校正信息
      locationStatus: 'pending' // pending, success, failed
    },

    // 占卜方式
    selectedMethod: 'time', // 'time' 或 'number'

    // 问题相关
    questionTypes: [
      { label: '失物寻找', value: 'lost' },
      { label: '求财问事', value: 'wealth' },
      { label: '出行安全', value: 'travel' },
      { label: '感情问题', value: 'love' },
      { label: '学习考试', value: 'study' },
      { label: '工作事业', value: 'career' },
      { label: '健康状况', value: 'health' },
      { label: '天气预测', value: 'weather' },
      { label: '疾病医疗', value: 'disease' },
      { label: '饮食宜忌', value: 'food' },
      { label: '其他事务', value: 'other' }
    ],
    selectedType: '',
    questionText: '',

    // 数字占卜
    numbers: ['', '', ''],

    // 时间占卜
    currentTime: '',
    lunarTime: '',
    trueSolarTime: '',
    currentShichen: '',
    locationInfo: '',
    fullTimeInfo: null,

    // 事件时间选择
    eventDate: '',
    eventTime: '',
    eventDateTime: '',
    eventLunarTime: '',
    eventTrueSolarTime: '',
    eventShichen: '',

    // 状态控制
    canStartDivination: false,
    isCalculating: false,

    // 天公师父集成相关
    useTianggongshifu: false, // 是否使用天公师父系统
    tianggongshifuServiceAvailable: false, // 天公师父服务是否可用
    tianggongshifuSearchResults: [], // 天公师父搜索结果
    showTianggongshifuResults: false // 是否显示天公师父结果
  },

  onLoad(options) {
    console.log('占卜信息收集页面加载', options);

    // 检查是否使用天公师父系统
    const useTianggongshifu = options.useTianggongshifu === 'true';
    const presetQuestion = options.question ? decodeURIComponent(options.question) : '';

    // 获取角色和师父身份信息
    const role = options.role || 'tarot';
    const master = options.master || '天公师兄';

    this.setData({
      useTianggongshifu: useTianggongshifu,
      questionText: presetQuestion,
      role: role,
      master: master
    });

    if (useTianggongshifu) {
      console.log('🔮 使用天公师父占卜系统');
      // 初始化天公师父服务
      this.initTianggongshifuService();
    } else {
      console.log('📿 使用传统占卜系统');
      // 测试新增问题类型功能
      this.testNewQuestionTypes();

      // 端到端功能测试
      this.testEndToEndFlow();

      // 测试万事皆可问功能
      this.testUniversalQuestions();
    }

    // 初始化真太阳时功能
    this.initializeTrueSolarTime();

    // 初始化默认事件时间
    this.initDefaultEventTime();

    // 初始化当前时间显示
    this.updateCurrentTimeDisplay();

    // 处理路由问题 - 使用安全的方式
    try {
      const currentRoute = 'pages/divination-input/index';
      const app = getApp();
      if (app && typeof app._fixRouteIssue === 'function') {
        app._fixRouteIssue(currentRoute);
      }
      this.route = currentRoute;
      console.log('页面路径设置为:', currentRoute);
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }

    // 延迟初始化，避免jsbridge调用过早
    setTimeout(() => {
      this.initializePage();
    }, 100);
  },

  /**
   * 初始化页面（延迟执行）
   */
  initializePage() {
    console.log('开始初始化占卜页面');

    // 显示页面加载状态
    wx.showLoading({
      title: '正在准备占卜环境...',
      mask: true
    });

    try {
      // 设置导航栏颜色
      NavigationColor.setNavigationBarColorByRole('tarot');

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '起盘问卦'
      });

      // 初始化时间信息
      this.initTimeInfo();

      // 获取位置信息（用于真太阳时计算）
      this.getLocationInfo();

      // 初始化双系统服务
      this.initServices();

      console.log('页面初始化完成');

    } catch (error) {
      console.error('页面初始化失败:', error);
    }

    // 确保loading状态被隐藏
    setTimeout(() => {
      wx.hideLoading();
      console.log('隐藏loading状态');

      // 初始化完成后检查占卜状态
      this.checkCanStartDivination();
    }, 1500);
  },

  onShow() {
    console.log('占卜信息收集页面显示');
    // 检查是否可以开始占卜
    this.checkCanStartDivination();
  },

  /**
   * 初始化双系统服务
   */
  async initServices() {
    try {
      console.log('🔮 正在初始化双系统服务...');

      // 检查李淳风六壬时课服务（集成古籍解读功能）
      const liurenAvailable = await this.checkLiurenService();

      this.setData({
        liurenServiceAvailable: liurenAvailable
      });

      if (liurenAvailable) {
        console.log('✅ 李淳风六壬时课专业服务(端口5000)可用，已集成古籍解读');
      } else {
        console.log('❌ 李淳风六壬时课专业服务(端口5000)不可用，将使用本地计算');
      }

    } catch (error) {
      console.error('🔮 初始化服务失败:', error);
      this.setData({
        liurenServiceAvailable: false,
        tianggongshifuServiceAvailable: false
      });
    }
  },

  /**
   * 检查李淳风六壬时课服务可用性
   */
  async checkLiurenService() {
    return new Promise((resolve) => {
      wx.request({
        url: liurenConfig.API_BASE_URL + '/',
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          resolve(res.statusCode === 200 && res.data?.name?.includes('李淳风六壬时课'));
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 初始化天公师父服务（从assessment-hub复制）
   */
  async initTianggongshifuService() {
    try {
      console.log('🔮 检查天公师父综合服务(端口8000)...');

      // 静默检查天公师父服务可用性
      const isAvailable = await this.checkTianggongshifuService();

      if (isAvailable) {
        console.log('🎉 天公师父综合服务(端口8000)已连接，加载增强功能...');

        this.setData({
          tianggongshifuServiceAvailable: true
        });

        console.log('✅ 天公师父综合服务增强功能加载完成');

      } else {
        console.log('📱 使用本地功能模式（天公师父服务不可用）');
        this.setData({
          tianggongshifuServiceAvailable: false
        });
      }

    } catch (error) {
      console.log('ℹ️ 天公师父综合服务(端口8000)初始化异常，使用本地功能:', error.message);
      this.setData({
        tianggongshifuServiceAvailable: false
      });
    }
  },

  /**
   * 检查天公师父服务可用性（静默检查，带回退机制）
   */
  async checkTianggongshifuService() {
    return new Promise((resolve) => {
      // 首先尝试主要地址
      console.log('🔍 尝试连接天公师父服务:', config.API_BASE_URL);

      wx.request({
        url: config.API_BASE_URL + '/',
        method: 'GET',
        timeout: 3000,
        success: (res) => {
          console.log('📡 天公师父服务响应:', res.statusCode, res.data?.name);
          const isAvailable = res.statusCode === 200 && res.data?.name?.includes('天公师父');
          if (isAvailable) {
            console.log('✅ 天公师父综合服务(端口8000)可用 - 主地址');
          }
          resolve(isAvailable);
        },
        fail: (error) => {
          console.log('⚠️ 主地址连接失败，尝试备用地址:', error.errMsg);

          // 尝试备用地址
          wx.request({
            url: config.API_BASE_URL_FALLBACK + '/',
            method: 'GET',
            timeout: 3000,
            success: (res) => {
              console.log('📡 天公师父服务备用地址响应:', res.statusCode, res.data?.name);
              const isAvailable = res.statusCode === 200 && res.data?.name?.includes('天公师父');
              if (isAvailable) {
                console.log('✅ 天公师父综合服务(端口8000)可用 - 备用地址');
              }
              resolve(isAvailable);
            },
            fail: (fallbackError) => {
              console.log('ℹ️ 天公师父综合服务(端口8000)不可用，使用本地功能');
              console.log('🔍 连接错误详情:', {
                主地址错误: error.errMsg,
                备用地址错误: fallbackError.errMsg
              });
              resolve(false);
            }
          });
        }
      });
    });
  },



  /**
   * 分析天公师父内容
   */
  async analyzeTianggongshifuContent(keyword) {
    if (!this.data.tianggongshifuServiceAvailable || !keyword.trim()) {
      return;
    }

    try {
      console.log('🔮 分析天公师父内容:', keyword);

      wx.showLoading({
        title: '正在分析问题...',
        mask: true
      });

      // 简单的问题分析（可以扩展为调用天公师父API）
      const analysisResult = this.analyzeQuestionLocally(keyword.trim());

      wx.hideLoading();

      if (analysisResult && analysisResult.suggestions.length > 0) {
        this.setData({
          tianggongshifuSearchResults: analysisResult.suggestions,
          showTianggongshifuResults: true
        });

        console.log('🔮 分析完成，找到建议:', analysisResult.suggestions.length, '条');

        wx.showToast({
          title: `分析完成，${analysisResult.suggestions.length}条建议`,
          icon: 'success'
        });
      } else {
        console.log('🔮 未找到相关建议');
        wx.showToast({
          title: '问题分析完成',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('🔮 分析天公师父内容失败:', error);
      wx.showToast({
        title: '分析失败: ' + error.message,
        icon: 'none'
      });
    }
  },

  /**
   * 本地问题分析
   */
  analyzeQuestionLocally(question) {
    const suggestions = [];

    // 基于关键词提供建议
    if (question.includes('工作') || question.includes('事业')) {
      suggestions.push({
        title: '事业运势分析',
        content: '建议关注当前工作环境的变化，把握机遇。',
        type: '事业'
      });
    }

    if (question.includes('感情') || question.includes('爱情')) {
      suggestions.push({
        title: '感情运势分析',
        content: '感情需要耐心经营，真诚相待。',
        type: '感情'
      });
    }

    if (question.includes('财运') || question.includes('金钱')) {
      suggestions.push({
        title: '财运分析',
        content: '财运需要稳健投资，避免冒险。',
        type: '财运'
      });
    }

    // 默认建议
    if (suggestions.length === 0) {
      suggestions.push({
        title: '综合运势分析',
        content: '建议保持积极心态，顺应自然规律。',
        type: '综合'
      });
    }

    return {
      question: question,
      suggestions: suggestions,
      analysis_time: new Date().toISOString()
    };
  },

  /**
   * 获取天公师父解读
   */
  async getTianggongshifuInterpretation(question, questionType, analysis) {
    return new Promise((resolve) => {
      // 模拟API调用，实际可以调用天公师父的占卜API
      setTimeout(() => {
        const interpretations = {
          '事业': {
            title: '事业运势解读',
            content: '当前事业运势平稳，建议稳扎稳打，不宜冒进。近期可能有新的机会出现，需要仔细甄别。',
            advice: '保持专注，提升自身能力，机会总是留给有准备的人。',
            lucky_direction: '东南方',
            lucky_time: '午时（11:00-13:00）'
          },
          '感情': {
            title: '感情运势解读',
            content: '感情方面需要更多的耐心和理解，真诚相待是关键。单身者近期有桃花运，已婚者需要多沟通。',
            advice: '以诚待人，用心经营感情，切忌急躁。',
            lucky_direction: '西南方',
            lucky_time: '酉时（17:00-19:00）'
          },
          '财运': {
            title: '财运分析',
            content: '财运方面需要谨慎理财，避免投机取巧。正财运较好，偏财运一般，建议以稳为主。',
            advice: '开源节流，理性投资，切勿贪心。',
            lucky_direction: '正北方',
            lucky_time: '子时（23:00-01:00）'
          }
        };

        const result = interpretations[questionType] || {
          title: '综合运势解读',
          content: '运势总体平稳，建议顺应自然，保持积极心态。遇事多思考，不要急于求成。',
          advice: '心态平和，顺其自然，积极面对生活中的挑战。',
          lucky_direction: '正南方',
          lucky_time: '巳时（09:00-11:00）'
        };

        resolve({
          success: true,
          interpretation: result,
          alternatives: [],
          isRandom: false,
          searchStrategy: 'tianggongshifu_analysis'
        });
      }, 1000);
    });
  },

  /**
   * 获取本地随机占卜
   */
  getLocalRandomDivination() {
    const divinations = [
      {
        title: '吉卦',
        content: '此卦大吉，诸事顺遂。当前所问之事有良好发展，建议积极行动。',
        advice: '把握时机，勇敢前行，但需保持谦逊之心。',
        lucky_direction: '东方',
        lucky_time: '卯时（05:00-07:00）'
      },
      {
        title: '平卦',
        content: '此卦平稳，无大吉大凶。当前状况稳定，需要耐心等待时机。',
        advice: '保持现状，稳中求进，不宜急躁冒进。',
        lucky_direction: '南方',
        lucky_time: '午时（11:00-13:00）'
      },
      {
        title: '变卦',
        content: '此卦多变，事情可能有转机。需要灵活应对，随机应变。',
        advice: '顺应变化，灵活处理，机会往往在变化中出现。',
        lucky_direction: '西方',
        lucky_time: '酉时（17:00-19:00）'
      }
    ];

    const randomIndex = Math.floor(Math.random() * divinations.length);
    return divinations[randomIndex];
  },

  /**
   * 初始化时间信息（使用新组件）
   */
  initTimeInfo() {
    const now = new Date();

    // 使用默认经纬度（北京）初始化，后续会通过GPS更新
    const defaultLongitude = 116.4074;
    const defaultLatitude = 39.9042;

    try {
      // 使用AuthoritativeLunarConverter进行农历转换
      const lunarInfo = AuthoritativeLunarConverter.solarToLunar(now);

      // 使用TrueSolarTimeCorrector计算真太阳时
      const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
      const corrector = new TrueSolarTimeCorrector();
      const trueSolarResult = corrector.calculateTrueSolarTime(now, defaultLongitude);

      // 从真太阳时计算时辰
      const trueSolarTime = new Date(trueSolarResult.result.trueSolarTime);
      const shichen = this.getShichen(trueSolarTime);

      // 格式化显示信息（使用24小时制）
      const formattedInfo = {
        solar: this.formatDateTime24(now),
        lunar: `${lunarInfo.year}年${lunarInfo.monthName}${lunarInfo.dayName}`,
        trueSolar: this.formatDateTime24(trueSolarTime),
        shichen: shichen
      };

      this.setData({
        currentTime: formattedInfo.solar,
        lunarTime: formattedInfo.lunar,
        currentShichen: formattedInfo.shichen,
        trueSolarTime: formattedInfo.trueSolar,
        fullTimeInfo: {
          solar: now,
          lunar: lunarInfo,
          trueSolar: trueSolarTime,
          shichen: shichen,
          formatted: formattedInfo
        }
      });

    } catch (error) {
      console.error('❌ 初始化时间信息失败:', error);
      // 设置基本时间信息（使用24小时制）
      this.setData({
        currentTime: this.formatDateTime24(now),
        lunarTime: '农历信息获取失败',
        currentShichen: '未知',
        trueSolarTime: this.formatDateTime24(now)
      });
    }
  },

  /**
   * 根据时间计算时辰
   */
  getShichen(date) {
    const hour = date.getHours();
    const shichenMap = {
      23: '子时', 0: '子时', 1: '丑时', 2: '丑时',
      3: '寅时', 4: '寅时', 5: '卯时', 6: '卯时',
      7: '辰时', 8: '辰时', 9: '巳时', 10: '巳时',
      11: '午时', 12: '午时', 13: '未时', 14: '未时',
      15: '申时', 16: '申时', 17: '酉时', 18: '酉时',
      19: '戌时', 20: '戌时', 21: '亥时', 22: '亥时'
    };
    return shichenMap[hour] || '未知';
  },

  // 旧的时间处理方法已移至 LunarCalendar 工具类

  /**
   * 获取位置信息
   */
  getLocationInfo() {
    console.log('开始获取位置信息 - GPS定位初始化');

    // 首先检查位置权限
    wx.getSetting({
      success: (res) => {
        console.log('当前权限设置:', res.authSetting);
        console.log('位置权限状态:', res.authSetting['scope.userLocation']);

        if (res.authSetting['scope.userLocation'] === false) {
          // 用户之前拒绝了位置权限
          console.log('用户之前拒绝了位置权限，显示引导');
          this.showLocationPermissionGuide();
          return;
        }

        // 尝试获取位置
        console.log('权限检查通过，开始请求位置');
        this.requestLocation();
      },
      fail: (err) => {
        console.log('获取权限设置失败:', err);
        console.log('直接尝试定位');
        this.requestLocation();
      }
    });
  },

  /**
   * 请求位置信息
   */
  requestLocation() {
    wx.getLocation({
      type: 'gcj02',
      altitude: false,
      success: (res) => {
        console.log('获取位置成功:', res);

        // 更新位置信息
        this.setData({
          locationInfo: `经度: ${res.longitude.toFixed(2)}°, 纬度: ${res.latitude.toFixed(2)}°`,
          longitude: res.longitude,
          latitude: res.latitude
        });

        // 使用真实位置重新计算时间信息
        try {
          const now = new Date();

          // 使用AuthoritativeLunarConverter进行农历转换
          const lunarInfo = AuthoritativeLunarConverter.solarToLunar(now);

          // 使用TrueSolarTimeEngine计算真太阳时
          const trueSolarTimeEngine = new TrueSolarTimeEngine();
          const trueSolarResult = trueSolarTimeEngine.calculateTrueSolarTime({
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            day: now.getDate(),
            hour: now.getHours(),
            minute: now.getMinutes(),
            longitude: res.longitude,
            latitude: res.latitude
          });

          // 从真太阳时计算时辰
          const trueSolarTime = new Date(trueSolarResult.result.trueSolarTime);
          const shichen = this.getShichen(trueSolarTime);

          // 格式化显示信息（使用24小时制）
          const formattedInfo = {
            solar: this.formatDateTime24(now),
            lunar: `${lunarInfo.year}年${lunarInfo.monthName}${lunarInfo.dayName}`,
            trueSolar: this.formatDateTime24(trueSolarTime),
            shichen: shichen
          };

          // 更新基于真实位置的时间信息
          this.setData({
            currentTime: formattedInfo.solar,
            lunarTime: formattedInfo.lunar,
            currentShichen: formattedInfo.shichen,
            trueSolarTime: formattedInfo.trueSolar,
            fullTimeInfo: {
              solar: now,
              lunar: lunarInfo,
              trueSolar: trueSolarTime,
              shichen: shichen,
              formatted: formattedInfo
            }
          });

        } catch (error) {
          console.error('❌ 位置校正时间计算失败:', error);
        }

        // 显示定位成功提示
        wx.showToast({
          title: '定位成功，时间已校正',
          icon: 'success',
          duration: 2000
        });
      },
      fail: (err) => {
        console.log('获取位置失败:', err);
        this.handleLocationError(err);
      }
    });
  },

  /**
   * 处理定位错误
   */
  handleLocationError(err) {
    let errorMsg = '定位失败';
    let detailMsg = '';

    console.log('GPS定位错误详情:', err);

    // 根据错误类型提供具体说明
    if (err.errMsg.includes('auth deny')) {
      errorMsg = '位置权限被拒绝';
      detailMsg = '请在小程序设置中开启位置权限';
    } else if (err.errMsg.includes('location fail')) {
      errorMsg = 'GPS定位失败';
      detailMsg = '请检查手机GPS功能是否开启，或移动到信号较好的位置';
    } else if (err.errMsg.includes('requiredPrivateInfos')) {
      errorMsg = 'API配置错误';
      detailMsg = '位置API配置问题，请联系开发者';
      console.error('需要在app.json中配置requiredPrivateInfos字段');
    } else if (err.errMsg.includes('need to be declared')) {
      errorMsg = 'API声明错误';
      detailMsg = '位置API需要声明，正在尝试修复...';
      console.error('位置API未正确声明:', err.errMsg);
    }

    wx.showModal({
      title: errorMsg,
      content: `${detailMsg}\n\n将使用北京时间进行占卜计算。\n\n真太阳时校正对占卜精度有重要影响，建议开启位置权限以获得更准确的结果。`,
      confirmText: '继续占卜',
      cancelText: '重新定位',
      success: (res) => {
        if (res.cancel) {
          // 用户选择重新定位
          this.getLocationInfo();
        }
      }
    });

    // 使用北京时间作为默认值
    this.setData({
      locationInfo: '使用北京时间（建议开启定位获得更准确结果）',
      longitude: 116.4074,
      latitude: 39.9042
    });
  },

  /**
   * 显示位置权限引导
   */
  showLocationPermissionGuide() {
    wx.showModal({
      title: '需要位置权限',
      content: '李淳风六壬时课需要您的位置信息来计算真太阳时，确保占卜结果的准确性。\n\n请点击"去设置"开启位置权限。',
      confirmText: '去设置',
      cancelText: '使用北京时间',
      success: (res) => {
        if (res.confirm) {
          // 打开设置页面
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userLocation']) {
                // 用户开启了位置权限，重新获取位置
                this.requestLocation();
              }
            }
          });
        } else {
          // 用户选择使用北京时间
          this.setData({
            locationInfo: '使用北京时间（建议开启定位获得更准确结果）',
            longitude: 116.4074,
            latitude: 39.9042
          });
        }
      }
    });
  },



  /**
   * 初始化真太阳时功能
   */
  initializeTrueSolarTime() {
    console.log('🌞 占卜页面初始化真太阳时功能');

    // 显示获取位置的提示
    wx.showLoading({
      title: '获取位置信息...',
      mask: false
    });

    // 获取用户位置
    wx.getLocation({
      type: 'gcj02',
      altitude: false,
      success: (res) => {
        console.log('📍 占卜页面位置获取成功:', res);

        const locationInfo = {
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          speed: res.speed || 0
        };

        this.setData({
          'trueSolarTime.locationInfo': locationInfo,
          'trueSolarTime.locationStatus': 'success'
        });

        // 计算当前时间的真太阳时校正
        this.calculateCurrentTrueSolarTime();

        wx.hideLoading();

        // 显示位置获取成功的提示
        wx.showToast({
          title: '已启用真太阳时',
          icon: 'success',
          duration: 2000
        });
      },
      fail: (err) => {
        console.error('📍 占卜页面位置获取失败:', err);

        this.setData({
          'trueSolarTime.locationStatus': 'failed'
        });

        wx.hideLoading();

        // 显示获取位置失败的提示
        wx.showModal({
          title: '位置获取失败',
          content: '无法获取精确位置，将使用北京时间进行占卜。如需更精确的结果，请在设置中开启位置权限。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  /**
   * 计算当前时间的真太阳时校正
   */
  calculateCurrentTrueSolarTime() {
    const locationInfo = this.data.trueSolarTime.locationInfo;
    if (!locationInfo) {
      console.log('🌞 无位置信息，跳过真太阳时计算');
      return;
    }

    try {
      // 使用当前时间计算真太阳时
      const currentTime = new Date();

      // 使用TrueSolarTimeCorrector计算真太阳时
      const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
      const corrector = new TrueSolarTimeCorrector();
      const trueSolarResult = corrector.calculateTrueSolarTime(currentTime, locationInfo.longitude);

      const trueSolarTime = new Date(trueSolarResult.result.trueSolarTime);

      // 计算时间差（分钟）
      const timeDifference = Math.round((trueSolarTime.getTime() - currentTime.getTime()) / (1000 * 60));

      this.setData({
        'trueSolarTime.timeDifference': timeDifference,
        'trueSolarTime.correctedTime': trueSolarTime,
        'trueSolarTime.correctedTimeDisplay': this.formatTime24(trueSolarTime),
        'trueSolarTime.showCorrection': true
      });

      console.log('🌞 占卜页面真太阳时校正计算完成:', {
        longitude: locationInfo.longitude,
        timeDifference: timeDifference,
        originalTime: this.formatTime24(currentTime),
        correctedTime: this.formatTime24(trueSolarTime)
      });

    } catch (error) {
      console.error('🌞 占卜页面真太阳时计算失败:', error);
    }
  },

  /**
   * 格式化日期时间为24小时制
   */
  formatDateTime24(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  },

  /**
   * 格式化时间为24小时制（仅时分）
   */
  formatTime24(date) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  /**
   * 更新当前时间显示
   */
  updateCurrentTimeDisplay() {
    const currentTime = new Date();
    this.setData({
      currentTimeDisplay: this.formatTime24(currentTime)
    });

    // 每分钟更新一次时间显示
    setTimeout(() => {
      this.updateCurrentTimeDisplay();
    }, 60000);
  },

  /**
   * 选择占卜方式
   */
  selectMethod(e) {
    const method = e.currentTarget.dataset.method;
    console.log('选择占卜方式:', method);

    this.setData({
      selectedMethod: method
    });

    // 给用户反馈
    wx.showToast({
      title: method === 'time' ? '已选择时间占卜' : '已选择数字占卜',
      icon: 'success',
      duration: 1000
    });

    this.checkCanStartDivination();
  },

  /**
   * 选择问题类型
   */
  selectQuestionType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    });
    this.checkCanStartDivination();
  },

  /**
   * 问题输入 - 彻底优化版本，避免失焦问题
   */
  onQuestionInput(e) {
    const newValue = e.detail.value;
    const hasInput = newValue.trim().length > 0;

    // 一次性更新所有数据，避免多次setData导致的重新渲染
    this.setData({
      questionText: newValue,
      canStartDivination: hasInput  // 直接设置按钮状态，不调用验证函数
    });
  },

  /**
   * 数字输入
   */
  onNumberInput(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const numbers = [...this.data.numbers];
    numbers[index] = value;

    this.setData({
      numbers
    });
    this.checkCanStartDivination();
  },

  /**
   * 事件日期选择
   */
  onEventDateChange(e) {
    console.log('事件日期选择:', e.detail.value);
    this.setData({
      eventDate: e.detail.value
    });
    this.calculateEventTimeInfo();
  },

  /**
   * 事件时间选择
   */
  onEventTimeChange(e) {
    console.log('事件时间选择:', e.detail.value);
    this.setData({
      eventTime: e.detail.value
    });
    this.calculateEventTimeInfo();
  },

  /**
   * 计算事件时间信息
   */
  calculateEventTimeInfo() {
    const { eventDate, eventTime, trueSolarTime } = this.data;

    if (!eventDate || !eventTime) {
      return;
    }

    try {
      // 修复时区问题 - 构建本地时间而不是UTC时间
      const [year, month, day] = eventDate.split('-').map(Number);
      const [hour, minute] = eventTime.split(':').map(Number);
      const eventDateTime = new Date(year, month - 1, day, hour, minute);

      console.log('🕐 开始计算事件时间信息:', {
        eventDate,
        eventTime,
        构建参数: { year, month: month - 1, day, hour, minute },
        eventDateTime: eventDateTime.toString()
      });

      // 使用AuthoritativeLunarConverter进行农历转换
      const lunarInfo = AuthoritativeLunarConverter.solarToLunar(eventDateTime);

      // 使用TrueSolarTimeCorrector计算真太阳时
      const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
      const corrector = new TrueSolarTimeCorrector();
      const longitude = this.data.trueSolarTime?.locationInfo?.longitude || 116.4074;

      const trueSolarResult = corrector.calculateTrueSolarTime(eventDateTime, longitude);

      // 从真太阳时计算时辰
      const trueSolarTime_date = new Date(trueSolarResult.result.trueSolarTime);
      const shichen = this.getShichen(trueSolarTime_date);

      // 格式化显示信息（使用24小时制）
      const formattedInfo = {
        solar: this.formatDateTime24(eventDateTime),
        lunar: `${lunarInfo.year}年${lunarInfo.monthName}${lunarInfo.dayName}`,
        trueSolar: this.formatDateTime24(trueSolarTime_date),
        shichen: shichen
      };

      this.setData({
        eventDateTime: formattedInfo.solar,
        eventLunarTime: formattedInfo.lunar,
        eventShichen: formattedInfo.shichen,
        eventTrueSolarTime: formattedInfo.trueSolar
      });

      console.log('✅ 事件时间信息计算完成:', formattedInfo);

    } catch (error) {
      console.error('❌ 计算事件时间信息失败:', error);
      wx.showToast({
        title: '时间计算失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 初始化默认事件时间
   */
  initDefaultEventTime() {
    const now = new Date();

    // 设置默认的事件发生时间为当前时间
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    this.setData({
      eventDate: `${year}-${month}-${day}`,
      eventTime: `${hour}:${minute}`
    });

    // 计算事件时间信息
    this.calculateEventTimeInfo();
  },

  /**
   * 构建事件时间的完整信息（用于占卜计算）
   */
  buildEventTimeInfo() {
    const { eventDate, eventTime, trueSolarTime } = this.data;

    if (!eventDate || !eventTime) {
      return null;
    }

    try {
      // 修复时区问题 - 构建本地时间而不是UTC时间
      const [year, month, day] = eventDate.split('-').map(Number);
      const [hour, minute] = eventTime.split(':').map(Number);
      const eventDateTime = new Date(year, month - 1, day, hour, minute);

      console.log('🔧 构建事件时间信息:', {
        eventDate,
        eventTime,
        构建参数: { year, month: month - 1, day, hour, minute },
        eventDateTime: eventDateTime.toString()
      });

      // 使用AuthoritativeLunarConverter进行农历转换
      const lunarInfo = AuthoritativeLunarConverter.solarToLunar(eventDateTime);

      // 使用TrueSolarTimeCorrector计算真太阳时
      const TrueSolarTimeCorrector = require('../../utils/true_solar_time_corrector.js');
      const corrector = new TrueSolarTimeCorrector();
      const longitude = this.data.trueSolarTime?.locationInfo?.longitude || 116.4074;

      const trueSolarResult = corrector.calculateTrueSolarTime(eventDateTime, longitude);

      // 从真太阳时计算时辰
      const trueSolarTime_date = new Date(trueSolarResult.result.trueSolarTime);
      const shichen = this.getShichen(trueSolarTime_date);

      // 格式化显示信息（使用24小时制）
      const formattedInfo = {
        solar: this.formatDateTime24(eventDateTime),
        lunar: `${lunarInfo.year}年${lunarInfo.monthName}${lunarInfo.dayName}`,
        trueSolar: this.formatDateTime24(trueSolarTime_date),
        shichen: shichen
      };

      // 返回与fullTimeInfo相同格式的数据结构
      return {
        solar: eventDateTime,
        lunar: lunarInfo,
        trueSolar: trueSolarTime_date,
        shichen: shichen,
        formatted: formattedInfo
      };

    } catch (error) {
      console.error('❌ 构建事件时间信息失败:', error);
      return null;
    }
  },

  /**
   * 根据时间计算时辰
   */
  getShichen(date) {
    const hour = date.getHours();
    const shichenMap = {
      23: '子时', 0: '子时', 1: '丑时', 2: '丑时',
      3: '寅时', 4: '寅时', 5: '卯时', 6: '卯时',
      7: '辰时', 8: '辰时', 9: '巳时', 10: '巳时',
      11: '午时', 12: '午时', 13: '未时', 14: '未时',
      15: '申时', 16: '申时', 17: '酉时', 18: '酉时',
      19: '戌时', 20: '戌时', 21: '亥时', 22: '亥时'
    };
    return shichenMap[hour] || '未知';
  },

  /**
   * 检查是否可以开始占卜 - 优化版本，避免不必要的setData
   */
  checkCanStartDivination() {
    const { selectedMethod, questionText, numbers, canStartDivination } = this.data;

    let canStart = false;

    if (selectedMethod === 'time') {
      // 时间占卜：只要有输入就可以开始（去掉字数限制）
      canStart = questionText.trim().length > 0;
    } else if (selectedMethod === 'number') {
      // 数字占卜：需要问题描述和三个数字（只要有输入即可）
      const validNumbers = numbers.every(num => num && num >= 1 && num <= 9);
      canStart = questionText.trim().length > 0 && validNumbers;
    }

    // 只有当状态真正改变时才调用setData，避免不必要的页面重新渲染
    if (canStart !== canStartDivination) {
      console.log('按钮状态改变:', {
        from: canStartDivination,
        to: canStart,
        method: selectedMethod
      });

      this.setData({
        canStartDivination: canStart
      });
    }
  },

  /**
   * 详细表单验证
   */
  validateFormDetailed() {
    const { selectedMethod, questionText, numbers } = this.data;

    // 验证问题输入
    if (!questionText.trim()) {
      wx.showModal({
        title: '温馨提示',
        content: '请输入您要占卜的问题。\n\n根据李淳风六壬时课传统：\n• 无事不占，一事一占\n• 问题要具体明确\n• 诚心为要',
        confirmText: '我知道了',
        showCancel: false
      });
      return false;
    }

    // 验证是否有输入（只要不是空白即可）
    if (questionText.trim().length === 0) {
      wx.showToast({
        title: '请输入您的问题',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 数字占卜特殊验证
    if (selectedMethod === 'number') {
      // 检查数字有效性
      for (let i = 0; i < numbers.length; i++) {
        const num = numbers[i];
        if (!num || num < 1 || num > 9) {
          wx.showToast({
            title: `请输入第${i + 1}个数字(1-9)`,
            icon: 'none'
          });
          return false;
        }
      }
    }

    return true;
  },

  /**
   * 开始占卜（双系统架构版本）
   */
  startDivination() {
    // 详细表单验证
    if (!this.validateFormDetailed()) {
      return;
    }

    // 优先级：李淳风六壬时课API > 本地计算
    if (this.data.liurenServiceAvailable) {
      this.startLiurenAPIDivination();
    } else {
      this.startTraditionalDivination();
    }
  },

  /**
   * 开始李淳风六壬时课API占卜流程（API优先策略）
   */
  async startLiurenAPIDivination() {
    console.log('🔮 开始李淳风六壬时课占卜（API优先 + 前端降级）');

    wx.showLoading({
      title: '正在调用李淳风六壬时课API...',
      mask: true
    });

    this.setData({
      isCalculating: true
    });

    try {
      // 第一步：使用前端本地计算李淳风六壬时课
      console.log('🔮 第一步：本地计算李淳风六壬时课');

      // 构建正确的时间信息
      let fullTimeInfo;
      if (this.data.eventDate && this.data.eventTime) {
        // 用户选择了具体的事件时间，使用事件时间进行占卜
        fullTimeInfo = this.buildEventTimeInfo();
        console.log('🔮 使用用户选择的事件时间进行占卜:', {
          eventDate: this.data.eventDate,
          eventTime: this.data.eventTime,
          fullTimeInfo: fullTimeInfo
        });
      } else {
        // 用户没有选择具体时间，使用当前时间
        fullTimeInfo = this.data.fullTimeInfo;
        console.log('🔮 使用当前时间进行占卜:', {
          fullTimeInfo: this.data.fullTimeInfo
        });
      }

      const divinationData = {
        method: this.data.selectedMethod,
        questionType: this.data.selectedType || this.detectQuestionType(this.data.questionText),
        questionText: this.data.questionText,
        timeInfo: {
          currentTime: this.data.currentTime,
          lunarTime: this.data.lunarTime,
          shichen: this.data.currentShichen,
          longitude: this.data.longitude,
          latitude: this.data.latitude
        },
        fullTimeInfo: fullTimeInfo,
        longitude: this.data.longitude || 116.4074,  // 确保有默认值
        latitude: this.data.latitude || 39.9042     // 确保有默认值
      };

      if (this.data.selectedMethod === 'number') {
        divinationData.numbers = this.data.numbers.map(n => parseInt(n));
      }

      // 🎯 API优先策略：先尝试调用API进行完整计算
      console.log('🔮 第一步：尝试API完整计算');

      const apiResult = await this.callLiurenCalculateAPI(divinationData);

      if (apiResult.success) {
        console.log('✅ API计算成功，使用API结果');

        // 使用API的完整计算结果
        const resultData = {
          apiResult: apiResult.data,          // API完整计算结果
          localResult: null,                  // 无需本地计算
          hasEnhanced: true,
          source: 'api_priority'
        };

        this.navigateToResult(resultData);
        return;
      } else {
        console.warn('⚠️ API计算失败，降级到前端本地计算:', apiResult.error);
      }

      // 🔄 降级策略：API失败时使用前端本地计算
      console.log('🔮 第二步：降级到前端本地计算');

      const DivinationCalculator = require('../../utils/divination_calculator.js');
      const localResult = DivinationCalculator.calculate(divinationData);

      console.log('🔮 前端本地计算完成:', localResult);

      // 第三步：调用API进行增强分析（可选）
      console.log('🔮 第三步：调用API进行增强分析');

      wx.showLoading({
        title: '正在获取古籍解读增强...',
        mask: true
      });

      const enhanceRequest = {
        god_name: localResult.god.name,
        god_index: localResult.godIndex,
        question_text: this.data.questionText,
        question_type: divinationData.questionType,
        method: this.data.selectedMethod
      };

      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: 'http://127.0.0.1:5000/api/v1/divination/enhance',
          method: 'POST',
          data: enhanceRequest,
          header: {
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject,
          timeout: 10000
        });
      });

      if (response.statusCode === 200 && response.data.success) {
        console.log('🔮 API增强分析成功:', response.data);

        // 合并本地计算结果和API增强分析
        const enhancedResult = {
          ...localResult,
          enhanced: true,
          apiEnhancement: response.data.data,
          source: 'local_calculation_with_api_enhancement'
        };

        const resultData = {
          method: this.data.selectedMethod,
          source: 'local_calculation_with_api_enhancement',
          questionType: divinationData.questionType,
          questionText: this.data.questionText,
          timestamp: Date.now(),
          hasEnhanced: true,
          localResult: localResult,
          apiEnhancement: response.data.data,
          timeInfo: divinationData.timeInfo,
          fullTimeInfo: fullTimeInfo
        };

        console.log('🔮 准备跳转到结果页面（本地计算+API增强）:', resultData);

        wx.hideLoading();
        this.setData({ isCalculating: false });

        wx.navigateTo({
          url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(resultData))}`
        });

      } else {
        throw new Error('API增强分析失败');
      }

    } catch (error) {
      console.error('❌ API增强失败，使用本地计算结果:', error);

      // API失败时，仍然使用本地计算的正确结果
      const resultData = {
        method: this.data.selectedMethod,
        source: 'local_calculation_only',
        questionType: divinationData.questionType,
        questionText: this.data.questionText,
        timestamp: Date.now(),
        hasEnhanced: false,
        localResult: localResult,  // 关键：传递本地计算结果
        timeInfo: divinationData.timeInfo,
        fullTimeInfo: fullTimeInfo,
        longitude: this.data.longitude,
        latitude: this.data.latitude
      };

      if (this.data.selectedMethod === 'number') {
        resultData.numbers = this.data.numbers.map(n => parseInt(n));
      }

      wx.hideLoading();
      this.setData({ isCalculating: false });

      wx.navigateTo({
        url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(resultData))}`
      });
    }

    try {
      // 准备占卜数据
      const divinationData = {
        method: this.data.selectedMethod,
        question_text: this.data.questionText,
        question_type: this.data.selectedType || this.detectQuestionType(this.data.questionText),
        longitude: this.data.longitude,
        latitude: this.data.latitude
      };

      if (this.data.selectedMethod === 'number') {
        divinationData.numbers = this.data.numbers.map(n => parseInt(n));
      }

      // 调用李淳风六壬时课API
      const result = await this.callLiurenAPI(divinationData);

      if (result.success) {
        console.log('✅ 李淳风六壬时课API调用成功（已包含古籍解读增强）');

        // 准备结果数据 - 保持原来的method值
        const resultData = {
          method: divinationData.method,  // ✅ 保持原来的method值（time或number）
          source: 'liuren_5000_enhanced',
          apiResult: result.data,
          questionType: divinationData.question_type,
          questionText: this.data.questionText,
          timestamp: Date.now(),
          hasEnhanced: result.data.has_enhanced || false,
          // 添加API来源标识
          apiSource: 'liuren_api'
        };

        this.navigateToResult(resultData);

      } else {
        console.error('❌ 李淳风六壬时课API调用失败:', result.error);
        // 降级到本地计算
        this.startTraditionalDivination();
      }

    } catch (error) {
      console.error('🔮 李淳风六壬时课API占卜失败:', error);
      // 降级到本地计算
      this.startTraditionalDivination();
    }
  },

  /**
   * 调用李淳风六壬时课API
   */
  async callLiurenAPI(data) {
    return new Promise((resolve) => {
      wx.request({
        url: liurenConfig.API_BASE_URL + liurenConfig.API_ENDPOINTS.DIVINATION,
        method: 'POST',
        data: data,
        timeout: liurenConfig.TIMEOUT,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            resolve({
              success: false,
              error: `API返回错误状态码: ${res.statusCode}`
            });
          }
        },
        fail: (err) => {
          resolve({
            success: false,
            error: `API请求失败: ${err.errMsg}`
          });
        }
      });
    });
  },

  /**
   * 调用李淳风六壬时课API进行完整计算（新增 - API优先策略）
   */
  async callLiurenCalculateAPI(data) {
    return new Promise((resolve) => {
      wx.request({
        url: liurenConfig.API_BASE_URL + '/api/v1/divination/calculate',
        method: 'POST',
        data: data,
        timeout: liurenConfig.TIMEOUT,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            resolve({
              success: false,
              error: `API返回错误状态码: ${res.statusCode}`
            });
          }
        },
        fail: (err) => {
          resolve({
            success: false,
            error: `API请求失败: ${err.errMsg}`
          });
        }
      });
    });
  },

  /**
   * 跳转到结果页面
   */
  navigateToResult(resultData) {
    wx.hideLoading();

    this.setData({
      isCalculating: false
    });

    wx.navigateTo({
      url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(resultData))}`,
      success: () => {
        console.log('✅ 成功跳转到占卜结果页面');
      },
      fail: (err) => {
        console.error('❌ 跳转到结果页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 开始天公师父占卜流程（智能分析版本）
   */
  async startTianggongshifuDivination() {
    console.log('🔮 开始天公师父智能占卜流程');

    wx.showLoading({
      title: '正在分析问题并计算运势...',
      mask: true
    });

    this.setData({
      isCalculating: true
    });

    try {
      // 使用智能匹配系统分析问题
      const intelligentMatcher = require('../../utils/intelligent_matcher');
      const questionAnalysis = intelligentMatcher.analyzeQuestion(this.data.questionText);

      console.log('🤖 问题智能分析结果:', questionAnalysis);

      // 智能识别问题类型（优先使用智能分析结果）
      let questionType = questionAnalysis.questionType;
      if (!questionType || questionType === 'other') {
        questionType = this.data.selectedType || this.detectQuestionType(this.data.questionText);
      }

      console.log('🎯 最终问题类型:', questionType);

      // 调用天公师父API获取占卜解读
      const interpretation = await this.getTianggongshifuInterpretation(
        this.data.questionText,
        questionType,
        questionAnalysis
      );

      wx.hideLoading();

      if (interpretation.success) {
        console.log('✅ 天公师父解读获取成功:', interpretation);

        // 准备结果数据，包含天公师父解读和智能分析
        const resultData = {
          method: 'tianggongshifu',
          userName: '求卜者',
          questionType: questionType,
          questionText: this.data.questionText,
          timestamp: Date.now(),
          tianggongshifuInterpretation: interpretation.interpretation,
          alternatives: interpretation.alternatives || [],
          isRandom: interpretation.isRandom || false,
          searchStrategy: interpretation.searchStrategy,
          intelligentAnalysis: questionAnalysis,
          confidence: questionAnalysis.confidence
        };

        this.setData({
          isCalculating: false
        });

        // 跳转到结果页面
        wx.navigateTo({
          url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(resultData))}&source=tianggongshifu`,
          success: () => {
            console.log('🔮 成功跳转到天公师父占卜结果页面');
          },
          fail: (err) => {
            console.error('🔮 跳转到结果页面失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'error'
            });
          }
        });
      } else {
        throw new Error('获取天公师父解读失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('🔮 天公师父综合服务(端口8000)占卜失败:', error);

      this.setData({
        isCalculating: false
      });

      // 提供更友好的错误处理
      if (error.message.includes('网络') || error.message.includes('连接')) {
        wx.showModal({
          title: '网络连接问题',
          content: '无法连接到天公师父综合服务(端口8000)，是否使用本地占卜数据？',
          success: (res) => {
            if (res.confirm) {
              this.startLocalTianggongshifuDivination();
            }
          }
        });
      } else {
        wx.showModal({
          title: '天公师父服务查询失败',
          content: '天公师父综合服务查询过程中出现问题，是否使用传统占卜方式？',
          success: (res) => {
            if (res.confirm) {
              this.startTraditionalDivination();
            }
          }
        });
      }
    }
  },

  /**
   * 根据情感分析获取吉凶等级
   */
  getLuckLevelFromEmotion(emotion) {
    if (!emotion || !emotion.dominant) return '中平';

    const emotionToLuck = {
      'positive': '大吉',
      'negative': '小凶',
      'neutral': '中平'
    };

    return emotionToLuck[emotion.dominant] || '中平';
  },

  /**
   * 启动本地天公师父占卜（API不可用时的备选方案）
   */
  async startLocalTianggongshifuDivination() {
    console.log('🔮 启动本地天公师父占卜');

    wx.showLoading({
      title: '使用本地天公师父数据...',
      mask: true
    });

    try {
      // 使用本地数据进行占卜
      const localResult = this.getLocalRandomDivination();

      // 简单的问题类型识别
      let questionType = this.data.selectedType || this.detectQuestionType(this.data.questionText);

      const resultData = {
        method: 'tianggongshifu',
        userName: '求卜者',
        questionType: questionType,
        questionText: this.data.questionText,
        timestamp: Date.now(),
        tianggongshifuInterpretation: localResult,
        alternatives: [],
        isRandom: true,
        searchStrategy: 'local',
        isLocal: true
      };

      wx.hideLoading();

      this.setData({
        isCalculating: false
      });

      // 跳转到结果页面
      wx.navigateTo({
        url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(resultData))}&source=tianggongshifu`,
        success: () => {
          console.log('🔮 成功跳转到本地天公师父结果页面');
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('🔮 本地天公师父占卜失败:', error);

      this.setData({
        isCalculating: false
      });

      wx.showToast({
        title: '占卜失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 开始传统占卜流程
   */
  startTraditionalDivination() {
    console.log('📿 开始传统占卜流程');

    // 显示专业的占卜加载提示
    wx.showLoading({
      title: '天公师兄正在为您占卜...',
      mask: true
    });

    this.setData({
      isCalculating: true
    });

    // 智能识别问题类型（如果用户没有选择）
    let questionType = this.data.selectedType;
    if (!questionType) {
      questionType = this.detectQuestionType(this.data.questionText);
      console.log('智能识别问题类型:', questionType);
    }

    // 准备占卜数据
    const divinationData = {
      method: this.data.selectedMethod,
      userName: '求卜者',
      questionType: questionType,
      questionText: this.data.questionText,
      timestamp: Date.now(),
      trueSolarTime: this.data.trueSolarTime // 包含真太阳时信息
    };

    if (this.data.selectedMethod === 'time') {
      // 时间占卜数据 - 传递完整的时间信息
      divinationData.timeInfo = {
        currentTime: this.data.currentTime,
        lunarTime: this.data.lunarTime,
        shichen: this.data.currentShichen,
        longitude: this.data.longitude,
        latitude: this.data.latitude
      };

      // 关键修复：使用用户选择的事件时间而不是当前时间
      if (this.data.eventDate && this.data.eventTime) {
        // 用户选择了具体的事件时间，使用事件时间进行占卜
        divinationData.fullTimeInfo = this.buildEventTimeInfo();
        console.log('🔮 使用用户选择的事件时间进行占卜:', {
          eventDate: this.data.eventDate,
          eventTime: this.data.eventTime,
          fullTimeInfo: divinationData.fullTimeInfo
        });
      } else {
        // 用户没有选择具体时间，使用当前时间
        divinationData.fullTimeInfo = this.data.fullTimeInfo;
        console.log('🔮 使用当前时间进行占卜:', {
          fullTimeInfo: this.data.fullTimeInfo
        });
      }
    } else {
      // 数字占卜数据
      divinationData.numbers = this.data.numbers.map(n => parseInt(n));
    }

    // 模拟计算过程（李淳风六壬时课需要时间计算）
    setTimeout(() => {
      wx.hideLoading();

      this.setData({
        isCalculating: false
      });

      // 跳转到结果页面
      wx.navigateTo({
        url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(divinationData))}`,
        success: () => {
          console.log('成功跳转到占卜结果页面');
        },
        fail: (err) => {
          console.error('跳转到结果页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    }, 2000);
  },

  /**
   * 智能检测问题类型（升级版语义理解系统）
   */
  detectQuestionType(questionText) {
    console.log('🤖 启动智能问题类型检测，输入文本:', questionText);

    // 首先尝试使用新的智能语义理解系统
    const DivinationCalculator = require('../../utils/divination_calculator');
    const intelligentResult = DivinationCalculator.intelligentQuestionClassification(questionText);

    if (intelligentResult.questionType && intelligentResult.confidence > 5) {
      console.log('✅ 智能语义识别成功:', intelligentResult.questionType, '置信度:', intelligentResult.confidence);
      return intelligentResult.questionType;
    }

    // 如果智能识别失败，回退到关键词匹配系统
    console.log('🔄 回退到关键词匹配系统');
    const text = questionText.toLowerCase();

    // 失物寻找关键词（优先级最高）- 扩展版
    const lostKeywords = [
      // 动作词（标准表达）
      '丢了', '丢失', '找不到', '不见了', '遗失', '失踪', '掉了', '弄丢',
      // 动作词（口语化）
      '搞丢了', '弄没了', '搞不见了', '弄掉了', '搞掉了', '弄丢了',
      // 动作词（方言）
      '搞丢', '弄丢', '搞没', '弄没', '搞不见', '弄不见',
      // 疑问表达
      '哪去了', '哪里去了', '在哪里', '在哪儿', '去哪了', '跑哪了',
      // 物品词（常见）
      '钥匙', '手机', '钱包', '包', '东西', '物品', '失物', '证件', '首饰',
      '戒指', '项链', '手表', '眼镜', '文件', '资料', '卡片', '银行卡',
      // 物品词（扩展）
      '身份证', '驾驶证', '护照', '学生证', '工作证', '会员卡', '充电器',
      '耳机', '背包', '书包', '公文包', '化妆品', '口红', '香水', '药品',
      '伞', '雨伞', '帽子', '围巾', '手套', '袜子', '内衣', '衣服',
      // 组合词
      '找东西', '寻物', '寻找', '找回', '寻回', '追回', '找寻',
      // 状态描述
      '不知道放哪了', '忘记放哪了', '记不起来了', '想不起来了'
    ];

    // 使用加权匹配，多个关键词匹配得分更高
    let lostScore = 0;
    lostKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        lostScore += keyword.length > 2 ? 2 : 1; // 长关键词权重更高
      }
    });

    if (lostScore >= 1) {
      console.log('检测为失物寻找类型，匹配得分:', lostScore);
      return 'lost';
    }

    // 求财问事关键词 - 扩展版
    const wealthKeywords = [
      // 赚钱相关（标准）
      '赚钱', '发财', '投资', '生意', '财运', '收入', '工资', '奖金', '中奖',
      '股票', '基金', '理财', '买卖', '经商', '盈利', '财富', '金钱', '资金',
      // 赚钱相关（口语化）
      '挣钱', '搞钱', '弄钱', '来钱', '进账', '入账', '到账', '回本',
      // 投资理财
      '炒股', '买股票', '买基金', '买房', '房产', '房子', '楼盘', '地产',
      '期货', '外汇', '黄金', '白银', '数字货币', '比特币', '虚拟币',
      // 生意经营
      '开店', '开公司', '创业', '做生意', '做买卖', '摆摊', '开铺',
      '合伙', '入股', '股份', '分红', '利润', '营业额', '销售额',
      // 工作收入
      '薪水', '薪资', '月薪', '年薪', '提成', '佣金', '绩效', '津贴',
      '补贴', '福利', '红包', '年终奖', '季度奖', '业绩奖',
      // 意外之财
      '彩票', '中彩', '中奖', '刮刮乐', '双色球', '大乐透', '福彩',
      '偏财', '横财', '意外收入', '额外收入',
      // 财务状况
      '财务', '资产', '存款', '储蓄', '积蓄', '家底', '身家', '净值'
    ];
    let wealthScore = 0;
    wealthKeywords.forEach(keyword => {
      if (text.includes(keyword)) wealthScore += 1;
    });
    if (wealthScore >= 1) {
      console.log('检测为求财问事类型，匹配得分:', wealthScore);
      return 'wealth';
    }

    // 学习考试关键词（高优先级，避免与事业混淆）
    const studyKeywords = [
      // 考试相关
      '考试', '考研', '高考', '中考', '期末考试', '期中考试', '模拟考试', '测试',
      '考级', '资格考试', '证书考试', '驾考', '公务员考试', '教师资格证',
      // 学习相关
      '学习', '学业', '成绩', '分数', '及格', '复习', '备考', '刷题',
      '上课', '听课', '作业', '论文', '毕业', '升学', '入学', '录取',
      // 学校相关
      '学校', '大学', '高中', '初中', '小学', '班级', '同学', '老师', '教授',
      '校园', '宿舍', '图书馆', '实验室', '课程', '专业', '学科'
    ];
    let studyScore = 0;
    studyKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        // 考试相关词汇权重更高
        studyScore += (keyword.includes('考试') || keyword.includes('考研') || keyword.includes('高考')) ? 3 : 1;
      }
    });

    // 检查是否存在职场关键词冲突（如"面试"+"通过"的情况）
    const hasCareerContext = text.includes('面试') || text.includes('工作') || text.includes('职位') ||
                            text.includes('公司') || text.includes('升职') || text.includes('跳槽');

    if (studyScore >= 1 && !hasCareerContext) {
      console.log('检测为学习考试类型，匹配得分:', studyScore);
      return 'study';
    }

    // 感情问题关键词（优先级较高）- 扩展版
    const loveKeywords = [
      // 感情状态（标准）
      '感情', '恋爱', '结婚', '分手', '复合', '喜欢', '爱情', '男友', '女友',
      '老公', '老婆', '对象', '表白', '求婚', '婚姻', '相亲', '约会', '暗恋',
      // 感情状态（口语化）
      '谈恋爱', '处对象', '找对象', '脱单', '单身', '恋人', '情侣',
      '男朋友', '女朋友', '另一半', '伴侣', '爱人', '心上人', '意中人',
      // 感情发展
      '追求', '追女孩', '追男孩', '被追', '告白', '表白', '求爱',
      '牵手', '接吻', '拥抱', '同居', '订婚', '领证', '办婚礼',
      // 感情问题
      '吵架', '冷战', '误会', '矛盾', '争执', '闹别扭', '生气',
      '分居', '离婚', '出轨', '背叛', '第三者', '小三', '劈腿',
      // 感情挽回
      '挽回', '和好', '重新开始', '破镜重圆', '旧情复燃',
      // 婚恋相关
      '结婚证', '婚礼', '婚纱', '蜜月', '新婚', '夫妻', '家庭',
      '丈夫', '妻子', '老伴', '配偶', '枕边人',
      // 单身相关
      '单身狗', '光棍', '剩女', '剩男', '母胎单身', '万年单身',
      // 网络用语
      'cp', '官宣', '秀恩爱', '撒狗粮', '吃狗粮', '柠檬精'
    ];
    let loveScore = 0;
    loveKeywords.forEach(keyword => {
      if (text.includes(keyword)) loveScore += 1;
    });
    if (loveScore >= 1) {
      console.log('检测为感情问题类型，匹配得分:', loveScore);
      return 'love';
    }

    // 工作事业关键词 - 扩展版
    const careerKeywords = [
      // 工作核心词汇（标准）
      '工作', '事业', '职业', '职位', '岗位', '上班', '下班', '加班',
      // 工作核心词汇（口语化）
      '打工', '搬砖', '上班族', '社畜', '996', '007', '朝九晚五',
      // 职场发展
      '升职', '晋升', '提拔', '跳槽', '面试', '求职', '辞职', '转行', '调动',
      '入职', '离职', '试用期', '转正', '实习', '兼职', '全职', '临时工',
      // 职位类型
      '经理', '主管', '总监', '总裁', '董事', '员工', '助理', '秘书',
      '销售', '客服', '技术', '研发', '设计', '运营', '市场', '财务',
      // 商业经营
      '创业', '生意', '经商', '开店', '合作', '投资', '项目', '业绩', '销售',
      '开公司', '注册公司', '营业执照', '商标', '专利', '融资', '上市',
      // 职场环境
      '公司', '企业', '单位', '部门', '团队', '老板', '领导', '同事', '客户',
      '集团', '分公司', '子公司', '总部', '分部', '办事处', '工厂', '车间',
      // 职场相关
      '职场', '办公室', '会议', '出差', '培训', '绩效', '薪水', '奖金',
      '考勤', '请假', '休假', '年假', '病假', '调休', '值班', '轮班',
      // 合同商务
      '合同', '签约', '签署', '协议', '谈判', '商务', '交易', '订单', '签字',
      '签合同', '签协议', '合作协议', '劳动合同', '保密协议', '竞业协议',
      // 工作成果
      '业绩', '成果', '成绩', '表现', '评价', '考核', '评估', '汇报',
      '方案', '计划', '报告', '总结', '提案', '建议', '意见', '反馈',
      // 工作问题
      '压力', '困难', '挑战', '问题', '麻烦', '冲突', '矛盾', '竞争'
    ];
    let careerScore = 0;
    careerKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        // 核心职场词汇、面试相关和合同相关词汇权重更高
        careerScore += (keyword === '工作' || keyword === '事业' || keyword === '升职' ||
                       keyword === '面试' || keyword === '求职' || keyword === '入职' ||
                       keyword === '合同' || keyword === '签约' || keyword === '签署') ? 3 : 1;
      }
    });

    // 特别处理面试相关问题，确保优先识别为career类型
    if (text.includes('面试')) {
      careerScore += 5; // 给面试额外高权重
      console.log('✅ 检测到面试关键词，增加career权重，确保正确分类');
    }

    if (careerScore >= 1) {
      console.log('检测为工作事业类型，匹配得分:', careerScore);
      return 'career';
    }

    // 出行安全关键词 - 扩展版
    const travelKeywords = [
      // 出行动作（标准）
      '出行', '旅游', '搬家', '出门', '路上', '旅行', '外出', '远行',
      '回家', '探亲', '度假', '出游', '自驾', '坐车', '坐飞机', '坐火车',
      // 出行动作（口语化）
      '出去玩', '去玩', '去旅游', '去度假', '出差', '出门办事',
      '走亲戚', '串门', '拜访', '看望', '回老家', '回娘家',
      // 交通工具
      '开车', '骑车', '走路', '步行', '打车', '叫车', '网约车',
      '公交', '地铁', '高铁', '动车', '绿皮车', '客车', '大巴',
      '飞机', '航班', '船', '轮船', '游轮', '摩托车', '电动车',
      // 出行目的
      '旅游', '观光', '游玩', '散心', '放松', '休闲', '娱乐',
      '出差', '公务', '办事', '谈业务', '开会', '培训',
      '搬家', '搬迁', '迁移', '乔迁', '换房', '搬办公室',
      // 出行地点
      '景点', '景区', '名胜', '古迹', '公园', '海边', '山区',
      '国外', '出国', '境外', '海外', '异地', '外地', '远方',
      // 出行安全
      '平安', '安全', '路况', '交通', '堵车', '事故', '意外'
    ];
    let travelScore = 0;
    travelKeywords.forEach(keyword => {
      if (text.includes(keyword)) travelScore += 1;
    });
    if (travelScore >= 1) {
      console.log('检测为出行安全类型，匹配得分:', travelScore);
      return 'travel';
    }

    // 健康状况关键词 - 扩展版
    const healthKeywords = [
      // 健康状态（标准）
      '健康', '身体', '生病', '医院', '治疗', '康复', '病情', '手术', '检查',
      '疾病', '痊愈', '养病', '调理', '体检', '药物', '医生',
      // 健康状态（口语化）
      '身体好', '身体差', '不舒服', '难受', '疼痛', '痛苦', '虚弱',
      '精神', '体力', '元气', '气色', '脸色', '状态', '感觉',
      // 疾病症状
      '头痛', '头晕', '发烧', '感冒', '咳嗽', '流鼻涕', '打喷嚏',
      '胃痛', '肚子痛', '腹痛', '腰痛', '背痛', '关节痛', '肌肉痛',
      '失眠', '睡不着', '多梦', '疲劳', '乏力', '没精神', '犯困',
      // 医疗相关
      '看病', '就医', '挂号', '门诊', '急诊', '住院', '出院',
      '诊断', '化验', 'ct', 'b超', 'x光', '核磁', '胃镜', '肠镜',
      '吃药', '打针', '输液', '开药', '配药', '中药', '西药',
      // 医疗机构
      '医院', '诊所', '卫生院', '社区医院', '三甲医院', '专科医院',
      '中医院', '西医', '中医', '医生', '大夫', '护士', '专家',
      // 健康管理
      '养生', '保健', '锻炼', '运动', '健身', '跑步', '散步',
      '饮食', '营养', '补品', '保健品', '维生素', '钙片',
      // 心理健康
      '心理', '情绪', '压力', '焦虑', '抑郁', '烦躁', '紧张',
      '心情', '开心', '难过', '伤心', '愤怒', '恐惧', '担心'
    ];
    let healthScore = 0;
    healthKeywords.forEach(keyword => {
      if (text.includes(keyword)) healthScore += 1;
    });
    if (healthScore >= 1) {
      console.log('检测为健康状况类型，匹配得分:', healthScore);
      return 'health';
    }

    // 默认返回其他事务
    console.log('未匹配到特定类型，返回其他事务');
    return 'other';
  },

  /**
   * 测试新增问题类型的识别和建议生成
   */
  testNewQuestionTypes() {
    console.log('\n🧪 开始测试新增问题类型识别和建议生成...');

    const testCases = [
      // 天气预测测试
      {
        question: '明天会下雨吗？',
        expectedType: 'weather',
        description: '天气预测-降雨'
      },
      {
        question: '今天天气怎么样？',
        expectedType: 'weather',
        description: '天气预测-一般'
      },
      {
        question: '这几天会刮风吗？',
        expectedType: 'weather',
        description: '天气预测-风力'
      },
      // 疾病医疗测试
      {
        question: '我的感冒什么时候好？',
        expectedType: 'disease',
        description: '疾病医疗-感冒'
      },
      {
        question: '头疼会不会严重？',
        expectedType: 'disease',
        description: '疾病医疗-头疼'
      },
      {
        question: '这个病能治好吗？',
        expectedType: 'disease',
        description: '疾病医疗-一般'
      },
      // 饮食宜忌测试
      {
        question: '这个药能吃吗？',
        expectedType: 'food',
        description: '饮食宜忌-药物'
      },
      {
        question: '海鲜适合我吃吗？',
        expectedType: 'food',
        description: '饮食宜忌-海鲜'
      },
      {
        question: '这个食物好不好？',
        expectedType: 'food',
        description: '饮食宜忌-一般'
      }
    ];

    testCases.forEach((testCase, index) => {
      console.log(`\n--- 测试用例 ${index + 1}: ${testCase.description} ---`);
      console.log('问题:', testCase.question);

      // 测试智能语义识别
      const DivinationCalculator = require('../../utils/divination_calculator');
      const intelligentResult = DivinationCalculator.intelligentQuestionClassification(testCase.question);

      console.log('智能识别结果:', intelligentResult);
      console.log('预期类型:', testCase.expectedType);
      console.log('实际类型:', intelligentResult.questionType);
      console.log('置信度:', intelligentResult.confidence);

      // 验证识别准确性
      if (intelligentResult.questionType === testCase.expectedType) {
        console.log('✅ 识别正确');
      } else {
        console.log('❌ 识别错误');
      }

      // 测试回退到关键词匹配
      const fallbackType = this.detectQuestionType(testCase.question);
      console.log('关键词匹配结果:', fallbackType);
    });

    console.log('\n🎉 新增问题类型测试完成！');
  },

  /**
   * 完整的端到端测试 - 从问题识别到建议生成
   */
  testEndToEndFlow() {
    console.log('\n🚀 开始端到端功能测试...');

    const testQuestion = '明天会下雨吗？';
    console.log('测试问题:', testQuestion);

    // 步骤1: 智能问题类型识别
    const DivinationCalculator = require('../../utils/divination_calculator');
    const intelligentResult = DivinationCalculator.intelligentQuestionClassification(testQuestion);
    console.log('步骤1 - 智能识别结果:', intelligentResult);

    // 步骤2: 模拟占卜计算
    const mockResult = {
      god: {
        name: '速喜',
        fortune: '大吉',
        color: '#FF6B6B'
      },
      questionType: intelligentResult.questionType || 'weather',
      questionText: testQuestion,
      timestamp: new Date().toISOString()
    };

    console.log('步骤2 - 模拟占卜结果:', mockResult);

    // 步骤3: 生成详细分析
    try {
      const analysis = DivinationCalculator.generateAnalysis(mockResult);
      console.log('步骤3 - 分析生成成功:');
      console.log('- 总体运势:', analysis.overall.description);
      console.log('- 具体建议:', analysis.specific.title);
      console.log('- 详细指导:', analysis.specific.details);
      console.log('- 注意事项:', analysis.precautions);

      console.log('✅ 端到端测试成功！新增问题类型功能正常工作');
    } catch (error) {
      console.error('❌ 端到端测试失败:', error);
    }
  },

  /**
   * 测试万事皆可问功能
   */
  testUniversalQuestions() {
    console.log('=== 开始测试万事皆可问功能 ===');

    const testQuestions = [
      '今天晚上的火锅好不好吃',
      '明天买这件衣服合适吗',
      '这个电影值得看吗',
      '今天去公园散步怎么样',
      '这个游戏好玩吗',
      '明天的聚会会开心吗'
    ];

    testQuestions.forEach((question, index) => {
      console.log(`\n--- 测试问题 ${index + 1}: ${question} ---`);

      // 测试问题类型检测
      const questionType = this.detectQuestionType(question);
      console.log('检测到的问题类型:', questionType);

      // 验证是否正确识别为'other'类型
      if (questionType === 'other') {
        console.log('✅ 正确识别为万能问题类型');
      } else {
        console.log('❌ 未正确识别为万能问题类型，实际类型:', questionType);
      }
    });

    console.log('=== 万事皆可问功能测试完成 ===');
  }
});
