<!-- components/conversational-option/index.wxml -->
<view 
  class="option-item {{selected ? 'selected' : ''}} {{gradeStyle}}" 
  bind:tap="handleTap"
  animation="{{animationData}}">
  
  <view class="option-label">{{label || labels[index]}}</view>
  
  <view class="option-content">
    <block wx:if="{{hasEmoji}}">
      <view class="emoji" wx:if="{{parseText(text).emoji}}">{{parseText(text).emoji}}</view>
      <text class="text">{{parseText(text).text}}</text>
    </block>
    <block wx:else>
      <text class="text">{{text}}</text>
    </block>
  </view>
  
  <view class="selection-indicator" wx:if="{{selected}}">
    <view class="checkmark"></view>
  </view>
</view> 