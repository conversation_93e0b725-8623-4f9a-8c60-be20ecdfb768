/**
 * 测试新增的标准神煞
 * 验证天德贵人、月德贵人、三奇贵人、红鸾、天喜、飞刃、魁罡贵人
 */

console.log('🚀 测试新增的标准神煞');
console.log('='.repeat(60));
console.log('');

// 测试用例：辛丑 甲午 癸卯 壬戌
const testBaziData = {
  year_gan: '辛', year_zhi: '丑',
  month_gan: '甲', month_zhi: '午',
  day_gan: '癸', day_zhi: '卯',
  hour_gan: '壬', hour_zhi: '戌'
};

console.log('📋 测试用例信息：');
console.log('='.repeat(30));
console.log(`四柱：${testBaziData.year_gan}${testBaziData.year_zhi} ${testBaziData.month_gan}${testBaziData.month_zhi} ${testBaziData.day_gan}${testBaziData.day_zhi} ${testBaziData.hour_gan}${testBaziData.hour_zhi}`);
console.log(`月支：${testBaziData.month_zhi} (午月)`);
console.log(`年支：${testBaziData.year_zhi} (丑年)`);
console.log(`日干：${testBaziData.day_gan}`);
console.log('');

// 构建四柱数据
const fourPillars = [
  { gan: testBaziData.year_gan, zhi: testBaziData.year_zhi },   // 年柱
  { gan: testBaziData.month_gan, zhi: testBaziData.month_zhi }, // 月柱
  { gan: testBaziData.day_gan, zhi: testBaziData.day_zhi },     // 日柱
  { gan: testBaziData.hour_gan, zhi: testBaziData.hour_zhi }    // 时柱
];

// 模拟新增神煞计算器
const newShenshaCalculator = {
  // 🌟 天德贵人
  calculateTiandeGuiren: function(monthZhi, fourPillars) {
    const tiandeMap = {
      '寅': '丁', '卯': '申', '辰': '壬', '巳': '辛',
      '午': '亥', '未': '甲', '申': '癸', '酉': '寅',
      '戌': '丙', '亥': '乙', '子': '巳', '丑': '庚'
    };

    const results = [];
    const tiandeTarget = tiandeMap[monthZhi];
    if (tiandeTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === tiandeTarget || pillar.zhi === tiandeTarget) {
          results.push({
            name: '天德贵人',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi,
            formula: `${monthZhi}月见${tiandeTarget}`
          });
        }
      });
    }
    return results;
  },

  // 🌙 月德贵人
  calculateYuedeGuiren: function(monthZhi, fourPillars) {
    const yuedeMap = {
      '寅': '丙', '午': '丙', '戌': '丙',
      '申': '壬', '子': '壬', '辰': '壬',
      '亥': '甲', '卯': '甲', '未': '甲',
      '巳': '庚', '酉': '庚', '丑': '庚'
    };

    const results = [];
    const yuedeTarget = yuedeMap[monthZhi];
    if (yuedeTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.gan === yuedeTarget) {
          results.push({
            name: '月德贵人',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi,
            formula: `${monthZhi}月见${yuedeTarget}`
          });
        }
      });
    }
    return results;
  },

  // ⭐ 三奇贵人
  calculateSanqiGuiren: function(fourPillars) {
    const results = [];
    const gans = fourPillars.map(p => p.gan);
    
    const tianshangSanqi = ['甲', '戊', '庚'];
    const dixiaSanqi = ['乙', '丙', '丁'];
    const renzhongSanqi = ['壬', '癸', '辛'];

    // 检查天上三奇
    if (this.checkSanqiSequence(gans, tianshangSanqi)) {
      results.push({
        name: '三奇贵人',
        position: '天上三奇',
        pillar: '甲戊庚',
        formula: '天上三奇甲戊庚'
      });
    }

    // 检查地下三奇
    if (this.checkSanqiSequence(gans, dixiaSanqi)) {
      results.push({
        name: '三奇贵人',
        position: '地下三奇',
        pillar: '乙丙丁',
        formula: '地下三奇乙丙丁'
      });
    }

    // 检查人中三奇
    if (this.checkSanqiSequence(gans, renzhongSanqi)) {
      results.push({
        name: '三奇贵人',
        position: '人中三奇',
        pillar: '壬癸辛',
        formula: '人中三奇壬癸辛'
      });
    }

    return results;
  },

  checkSanqiSequence: function(gans, targetSequence) {
    for (let i = 0; i <= gans.length - targetSequence.length; i++) {
      let match = true;
      for (let j = 0; j < targetSequence.length; j++) {
        if (gans[i + j] !== targetSequence[j]) {
          match = false;
          break;
        }
      }
      if (match) return true;
    }
    return false;
  },

  // 🌹 红鸾
  calculateHongluan: function(yearZhi, fourPillars) {
    const hongluanMap = {
      '子': '卯', '丑': '寅', '寅': '丑', '卯': '子',
      '辰': '亥', '巳': '戌', '午': '酉', '未': '申',
      '申': '未', '酉': '午', '戌': '巳', '亥': '辰'
    };

    const results = [];
    const hongluanTarget = hongluanMap[yearZhi];
    if (hongluanTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === hongluanTarget) {
          results.push({
            name: '红鸾',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi,
            formula: `${yearZhi}年见${hongluanTarget}`
          });
        }
      });
    }
    return results;
  },

  // 🎉 天喜
  calculateTianxi: function(yearZhi, fourPillars) {
    const tianxiMap = {
      '子': '酉', '丑': '申', '寅': '未', '卯': '午',
      '辰': '巳', '巳': '辰', '午': '卯', '未': '寅',
      '申': '丑', '酉': '子', '戌': '亥', '亥': '戌'
    };

    const results = [];
    const tianxiTarget = tianxiMap[yearZhi];
    if (tianxiTarget) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === tianxiTarget) {
          results.push({
            name: '天喜',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi,
            formula: `${yearZhi}年见${tianxiTarget}`
          });
        }
      });
    }
    return results;
  },

  // 🗡️ 飞刃
  calculateFeiren: function(dayGan, fourPillars) {
    const yangrenMap = {
      '甲': '卯', '乙': '辰', '丙': '午', '丁': '未',
      '戊': '午', '己': '未', '庚': '酉', '辛': '戌',
      '壬': '子', '癸': '丑'
    };

    const chongMap = {
      '卯': '酉', '辰': '戌', '午': '子', '未': '丑',
      '酉': '卯', '戌': '辰', '子': '午', '丑': '未'
    };

    const results = [];
    const yangrenZhi = yangrenMap[dayGan];
    const feirenZhi = chongMap[yangrenZhi];
    
    if (feirenZhi) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === feirenZhi) {
          results.push({
            name: '飞刃',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            pillar: pillar.gan + pillar.zhi,
            formula: `${dayGan}日羊刃${yangrenZhi}冲${feirenZhi}`
          });
        }
      });
    }
    return results;
  },

  // 👑 魁罡贵人
  calculateKuigangGuiren: function(fourPillars) {
    const kuigangDays = ['庚辰', '庚戌', '壬辰', '戊戌'];
    const results = [];
    
    const dayPillar = fourPillars[2].gan + fourPillars[2].zhi;
    if (kuigangDays.includes(dayPillar)) {
      results.push({
        name: '魁罡贵人',
        position: '日柱',
        pillar: dayPillar,
        formula: `日柱为${dayPillar}`
      });
    }
    return results;
  }
};

console.log('🧪 开始测试新增神煞：');
console.log('='.repeat(40));

let totalResults = [];

// 1. 测试天德贵人
console.log('1. 天德贵人测试：');
const tiandeResults = newShenshaCalculator.calculateTiandeGuiren(testBaziData.month_zhi, fourPillars);
totalResults.push(...tiandeResults);
console.log(`   结果：${tiandeResults.length} 个神煞`);
tiandeResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 2. 测试月德贵人
console.log('2. 月德贵人测试：');
const yuedeResults = newShenshaCalculator.calculateYuedeGuiren(testBaziData.month_zhi, fourPillars);
totalResults.push(...yuedeResults);
console.log(`   结果：${yuedeResults.length} 个神煞`);
yuedeResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 3. 测试三奇贵人
console.log('3. 三奇贵人测试：');
const sanqiResults = newShenshaCalculator.calculateSanqiGuiren(fourPillars);
totalResults.push(...sanqiResults);
console.log(`   结果：${sanqiResults.length} 个神煞`);
sanqiResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 4. 测试红鸾
console.log('4. 红鸾测试：');
const hongluanResults = newShenshaCalculator.calculateHongluan(testBaziData.year_zhi, fourPillars);
totalResults.push(...hongluanResults);
console.log(`   结果：${hongluanResults.length} 个神煞`);
hongluanResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 5. 测试天喜
console.log('5. 天喜测试：');
const tianxiResults = newShenshaCalculator.calculateTianxi(testBaziData.year_zhi, fourPillars);
totalResults.push(...tianxiResults);
console.log(`   结果：${tianxiResults.length} 个神煞`);
tianxiResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 6. 测试飞刃
console.log('6. 飞刃测试：');
const feirenResults = newShenshaCalculator.calculateFeiren(testBaziData.day_gan, fourPillars);
totalResults.push(...feirenResults);
console.log(`   结果：${feirenResults.length} 个神煞`);
feirenResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

// 7. 测试魁罡贵人
console.log('7. 魁罡贵人测试：');
const kuigangResults = newShenshaCalculator.calculateKuigangGuiren(fourPillars);
totalResults.push(...kuigangResults);
console.log(`   结果：${kuigangResults.length} 个神煞`);
kuigangResults.forEach(result => {
  console.log(`   ✅ ${result.name} - ${result.position} (${result.pillar}) [${result.formula}]`);
});
console.log('');

console.log('📊 测试结果统计：');
console.log('='.repeat(30));
console.log(`新增神煞总数：${totalResults.length}`);
console.log(`顶级福贵：${tiandeResults.length + yuedeResults.length + sanqiResults.length}`);
console.log(`感情人缘：${hongluanResults.length + tianxiResults.length}`);
console.log(`刑伤斗争：${feirenResults.length + kuigangResults.length}`);

console.log('');
console.log('🎯 新增神煞效果：');
console.log('='.repeat(30));
console.log('✅ 天德贵人：化解灾厄，慈祥之心');
console.log('✅ 月德贵人：逢凶化吉，品性温和');
console.log('✅ 三奇贵人：博学多能，才华出众');
console.log('✅ 红鸾：婚姻喜庆，恋爱顺利');
console.log('✅ 天喜：喜事临门，意外之喜');
console.log('✅ 飞刃：意外血光，外来刀刃');
console.log('✅ 魁罡贵人：刚烈果断，有吉有凶');

console.log('');
console.log('✅ 新增标准神煞测试完成！');
console.log(`🎯 成果：新增7种标准神煞，提升系统完整性`);
