// test_data_fix.js
// 测试数据修复功能

console.log('🧪 测试前端数据缺失问题修复...');

// 模拟前端计算结果（真实格式）
const mockFrontendResult = {
  bazi: {
    year: { gan: '庚', zhi: '午' },
    month: { gan: '辛', zhi: '巳' },
    day: { gan: '甲', zhi: '子' },
    hour: { gan: '己', zhi: '巳' }
  },
  nayin: {
    year: '路旁土',
    month: '白蜡金',
    day: '海中金',
    hour: '大林木'
  },
  five_elements: {
    wood: 2,
    fire: 3,
    earth: 2,
    metal: 1,
    water: 0
  },
  lunar_date: '庚午年四月廿一日巳时',
  solar_term: '立夏',
  source: 'frontend_calculation'
};

// 模拟出生信息
const mockBirthInfo = {
  name: '测试用户',
  gender: '男',
  year: 1990,
  month: 5,
  day: 15,
  hour: 10,
  minute: 30,
  birthCity: '北京市'
};

// 模拟页面的数据转换方法
function convertFrontendDataToDisplayFormat(frontendResult, birthInfo, analysisMode) {
  console.log('🔄 开始数据格式转换...');

  // 转换出生信息格式
  const convertedUserInfo = {
    name: birthInfo.name || '用户',
    gender: birthInfo.gender || '未知',
    birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
    birthTime: formatTime(birthInfo.hour, birthInfo.minute),
    location: birthInfo.birthCity || birthInfo.location || '未知',
    zodiac: getZodiac(birthInfo.year),
    solar_time: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日 ${formatTime(birthInfo.hour, birthInfo.minute)}`,
    lunar_time: frontendResult.lunar_date || '未知',
    true_solar_time: formatTime(birthInfo.hour, birthInfo.minute),
    longitude: '未知',
    latitude: '未知',
    timezone: 'UTC+8',
    solar_term: frontendResult.solar_term || '未知'
  };

  // 转换八字信息格式
  const convertedBaziInfo = {
    yearPillar: {
      heavenly: frontendResult.bazi?.year?.gan || '甲',
      earthly: frontendResult.bazi?.year?.zhi || '子',
      nayin: frontendResult.nayin?.year || '海中金'
    },
    monthPillar: {
      heavenly: frontendResult.bazi?.month?.gan || '丙',
      earthly: frontendResult.bazi?.month?.zhi || '寅',
      nayin: frontendResult.nayin?.month || '炉中火'
    },
    dayPillar: {
      heavenly: frontendResult.bazi?.day?.gan || '戊',
      earthly: frontendResult.bazi?.day?.zhi || '午',
      nayin: frontendResult.nayin?.day || '天上火'
    },
    timePillar: {
      heavenly: frontendResult.bazi?.hour?.gan || '庚',
      earthly: frontendResult.bazi?.hour?.zhi || '申',
      nayin: frontendResult.nayin?.hour || '石榴木'
    }
  };

  // 转换五行信息
  const convertedFiveElements = {
    wood: frontendResult.five_elements?.wood || 0,
    fire: frontendResult.five_elements?.fire || 0,
    earth: frontendResult.five_elements?.earth || 0,
    metal: frontendResult.five_elements?.metal || 0,
    water: frontendResult.five_elements?.water || 0
  };

  // 生成扩展数据
  const extendedBaziData = generateExtendedBaziData(frontendResult, convertedBaziInfo);

  // 组装完整数据
  const convertedData = {
    userInfo: convertedUserInfo,
    baziInfo: convertedBaziInfo,
    fiveElements: convertedFiveElements,
    analysisMode: analysisMode,
    dataSource: 'converted_frontend_result',
    ...extendedBaziData
  };

  return convertedData;
}

// 辅助函数
function formatTime(hour, minute) {
  const h = parseInt(hour) || 0;
  const m = parseInt(minute) || 0;
  const period = h < 12 ? '上午' : '下午';
  const displayHour = h === 0 ? 12 : (h > 12 ? h - 12 : h);
  return `${period}${displayHour}:${m.toString().padStart(2, '0')}`;
}

function getZodiac(year) {
  const zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
  return zodiacs[(year - 4) % 12];
}

function generateExtendedBaziData(frontendResult, baziInfo) {
  // 提取四柱天干地支
  const yearGan = baziInfo.yearPillar.heavenly;
  const yearZhi = baziInfo.yearPillar.earthly;
  const monthGan = baziInfo.monthPillar.heavenly;
  const monthZhi = baziInfo.monthPillar.earthly;
  const dayGan = baziInfo.dayPillar.heavenly;
  const dayZhi = baziInfo.dayPillar.earthly;
  const hourGan = baziInfo.timePillar.heavenly;
  const hourZhi = baziInfo.timePillar.earthly;

  return {
    // 四柱基本数据
    year_gan: yearGan,
    year_zhi: yearZhi,
    month_gan: monthGan,
    month_zhi: monthZhi,
    day_gan: dayGan,
    day_zhi: dayZhi,
    hour_gan: hourGan,
    hour_zhi: hourZhi,

    // 十神数据（简化）
    year_star: '偏印',
    month_star: '食神',
    day_star: '日主',
    hour_star: '七杀',

    // 五行符号
    year_gan_element_symbol: '🔸',
    year_zhi_element_symbol: '🔥',
    month_gan_element_symbol: '🔸',
    month_zhi_element_symbol: '🔥',
    day_gan_element_symbol: '🌿',
    day_zhi_element_symbol: '💧',
    hour_gan_element_symbol: '🏔️',
    hour_zhi_element_symbol: '🔥',

    // 纳音数据
    nayin: {
      year_pillar: baziInfo.yearPillar.nayin,
      month_pillar: baziInfo.monthPillar.nayin,
      day_pillar: baziInfo.dayPillar.nayin,
      hour_pillar: baziInfo.timePillar.nayin
    },

    // 藏干数据（简化）
    canggan: {
      year_pillar: { main_qi: '丁', hidden_gan: ['丁', '己'], ten_gods: ['伤官', '比肩'], strength: ['旺', '中'] },
      month_pillar: { main_qi: '丙', hidden_gan: ['丙', '戊', '庚'], ten_gods: ['食神', '偏财', '七杀'], strength: ['旺', '中', '弱'] },
      day_pillar: { main_qi: '癸', hidden_gan: ['癸'], ten_gods: ['正印'], strength: ['旺'] },
      hour_pillar: { main_qi: '丙', hidden_gan: ['丙', '戊', '庚'], ten_gods: ['食神', '偏财', '七杀'], strength: ['旺', '中', '弱'] }
    },

    // 自坐分析数据
    self_sitting: {
      year_pillar: '庚午 - 七杀坐旺',
      month_pillar: '辛巳 - 正官坐生',
      day_pillar: '甲子 - 日主坐旺',
      hour_pillar: '己巳 - 偏财坐生'
    },

    // 平衡度数据
    balanceIndex: 75,
    balanceStatus: '平衡良好，五行配置较为协调'
  };
}

// 执行测试
console.log('📊 测试数据转换...');
const result = convertFrontendDataToDisplayFormat(mockFrontendResult, mockBirthInfo, '综合模式');

console.log('✅ 转换结果:');
console.log('用户信息:', result.userInfo);
console.log('八字信息:', result.baziInfo);
console.log('五行信息:', result.fiveElements);
console.log('扩展数据样例:');
console.log('- 十神:', { year: result.year_star, month: result.month_star, day: result.day_star, hour: result.hour_star });
console.log('- 纳音:', result.nayin);
console.log('- 自坐:', result.self_sitting);

console.log('🎯 数据修复测试完成！');
