// fix_shensha_calculations.js
// 修正神煞计算错误并完善缺失的神煞

console.log('🔧 修正神煞计算错误');
console.log('='.repeat(80));

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  birthMonth: 6
};

// 深度分析天乙贵人问题
function analyzeTianyiGuirenProblem() {
  console.log('\n🔍 深度分析天乙贵人问题:');
  console.log('='.repeat(50));
  
  const dayGan = testData.fourPillars[2].gan; // 癸
  console.log(`日干: ${dayGan}`);
  
  // 天乙贵人表
  const tianyiTable = {
    '甲': ['丑', '未'], '戊': ['丑', '未'],
    '乙': ['子', '申'], '己': ['子', '申'],
    '丙': ['亥', '酉'], '丁': ['亥', '酉'],
    '庚': ['丑', '未'], '辛': ['寅', '午'],
    '壬': ['卯', '巳'], '癸': ['卯', '巳']
  };
  
  const expectedZhi = tianyiTable[dayGan];
  console.log(`${dayGan}的天乙贵人地支: ${expectedZhi.join(', ')}`);
  
  console.log('\n四柱检查:');
  testData.fourPillars.forEach((pillar, index) => {
    const positions = ['年柱', '月柱', '日柱', '时柱'];
    const hasGuiren = expectedZhi.includes(pillar.zhi);
    console.log(`${positions[index]} ${pillar.gan}${pillar.zhi}: ${hasGuiren ? '✅ 天乙贵人' : '❌ 无'}`);
  });
  
  console.log('\n问题分析:');
  console.log('- 日柱卯: ✅ 正确，癸的天乙贵人包含卯');
  console.log('- 月柱午: ❓ 需要检查，午不在癸的天乙贵人表中');
  console.log('- 可能"问真八字"使用了不同的天乙贵人表');
}

// 分析桃花问题
function analyzeTaohuaProblem() {
  console.log('\n🌸 分析桃花问题:');
  console.log('='.repeat(50));
  
  const dayZhi = testData.fourPillars[2].zhi; // 卯
  console.log(`日支: ${dayZhi}`);
  
  // 桃花表（咸池）
  const taohuaTable = {
    '申子辰': '酉',
    '寅午戌': '卯',
    '巳酉丑': '午',
    '亥卯未': '子'
  };
  
  let expectedTaohua = null;
  let matchedGroup = null;
  
  for (const [group, taohua] of Object.entries(taohuaTable)) {
    if (group.includes(dayZhi)) {
      expectedTaohua = taohua;
      matchedGroup = group;
      break;
    }
  }
  
  console.log(`日支${dayZhi}属于${matchedGroup}组`);
  console.log(`对应桃花: ${expectedTaohua}`);
  
  console.log('\n四柱检查:');
  testData.fourPillars.forEach((pillar, index) => {
    const positions = ['年柱', '月柱', '日柱', '时柱'];
    const hasTaohua = pillar.zhi === expectedTaohua;
    console.log(`${positions[index]} ${pillar.gan}${pillar.zhi}: ${hasTaohua ? '✅ 桃花' : '❌ 无'}`);
  });
  
  console.log('\n问题分析:');
  console.log('- 日支卯属于亥卯未组，桃花应该是子');
  console.log('- 四柱中没有子，所以没有桃花');
  console.log('- 但"问真八字"显示月柱有桃花');
  console.log('- 可能需要检查其他桃花计算方法');
}

// 分析福星贵人问题
function analyzeFuxingGuirenProblem() {
  console.log('\n⭐ 分析福星贵人问题:');
  console.log('='.repeat(50));
  
  const yearZhi = testData.fourPillars[0].zhi; // 丑
  console.log(`年支: ${yearZhi}`);
  
  // 福星贵人表（基于年支）
  const fuxingTable = {
    '子': ['申', '辰'], '丑': ['巳', '酉'], '寅': ['午', '戌'],
    '卯': ['未', '亥'], '辰': ['申', '子'], '巳': ['酉', '丑'],
    '午': ['戌', '寅'], '未': ['亥', '卯'], '申': ['子', '辰'],
    '酉': ['丑', '巳'], '戌': ['寅', '午'], '亥': ['卯', '未']
  };
  
  const expectedZhi = fuxingTable[yearZhi];
  console.log(`年支${yearZhi}的福星贵人: ${expectedZhi.join(', ')}`);
  
  console.log('\n四柱检查:');
  testData.fourPillars.forEach((pillar, index) => {
    const positions = ['年柱', '月柱', '日柱', '时柱'];
    const hasFuxing = expectedZhi.includes(pillar.zhi);
    console.log(`${positions[index]} ${pillar.gan}${pillar.zhi}: ${hasFuxing ? '✅ 福星贵人' : '❌ 无'}`);
  });
  
  console.log('\n问题分析:');
  console.log('- 年支丑的福星贵人是巳、酉');
  console.log('- 四柱中没有巳、酉，所以没有福星贵人');
  console.log('- 但"问真八字"显示年柱和日柱都有福星贵人');
  console.log('- 可能福星贵人有其他计算方法');
}

// 研究"问真八字"的特殊计算规则
function researchWenZhenSpecialRules() {
  console.log('\n📚 研究"问真八字"特殊规则:');
  console.log('='.repeat(50));
  
  console.log('可能的特殊规则:');
  console.log('1. 天乙贵人可能有多种查表方法');
  console.log('2. 桃花可能不只是咸池，还包括其他桃花星');
  console.log('3. 福星贵人可能基于不同的计算基准');
  console.log('4. 某些神煞可能有组合计算规则');
  
  console.log('\n需要验证的假设:');
  console.log('1. 天乙贵人是否有昼夜贵人之分？');
  console.log('2. 桃花是否包括红鸾、天喜等？');
  console.log('3. 福星贵人是否基于日支而非年支？');
  console.log('4. 是否有其他未知的神煞计算规则？');
}

// 尝试修正的计算方法
function tryCorrectiveCalculations() {
  console.log('\n🔧 尝试修正的计算方法:');
  console.log('='.repeat(50));
  
  // 尝试不同的天乙贵人计算
  console.log('1. 尝试昼夜贵人计算:');
  const dayGan = testData.fourPillars[2].gan;
  const hourZhi = testData.fourPillars[3].zhi;
  
  // 昼夜贵人表（假设）
  const dayNightGuiren = {
    '癸': {
      day: ['卯', '巳'],    // 白天贵人
      night: ['午', '申']   // 夜间贵人（假设）
    }
  };
  
  console.log(`日干${dayGan}的昼贵人: ${dayNightGuiren[dayGan]?.day.join(', ')}`);
  console.log(`日干${dayGan}的夜贵人: ${dayNightGuiren[dayGan]?.night.join(', ')}`);
  
  // 尝试不同的桃花计算
  console.log('\n2. 尝试其他桃花计算:');
  console.log('- 红鸾桃花');
  console.log('- 天喜桃花');
  console.log('- 沐浴桃花');
  
  // 尝试不同的福星计算
  console.log('\n3. 尝试基于日支的福星计算:');
  const dayZhi = testData.fourPillars[2].zhi; // 卯
  const fuxingByDayZhi = {
    '卯': ['未', '亥']  // 假设基于日支的福星
  };
  console.log(`日支${dayZhi}的福星贵人: ${fuxingByDayZhi[dayZhi]?.join(', ')}`);
}

// 建议的解决方案
function suggestSolutions() {
  console.log('\n💡 建议的解决方案:');
  console.log('='.repeat(50));
  
  console.log('短期解决方案:');
  console.log('1. 🔍 深入研究"问真八字"的具体算法');
  console.log('2. 📚 查阅更多权威神煞计算资料');
  console.log('3. 🧪 建立神煞计算测试用例');
  console.log('4. 🔄 逐步调整计算规则直到匹配');
  
  console.log('\n长期解决方案:');
  console.log('1. 🏗️ 建立完整的神煞数据库');
  console.log('2. 🎯 实现多种神煞计算方法');
  console.log('3. ⚖️ 建立神煞计算权威性验证系统');
  console.log('4. 🔧 提供神煞计算规则配置功能');
  
  console.log('\n立即行动:');
  console.log('1. 先修正已知正确的神煞（如文昌贵人）');
  console.log('2. 实现缺失但规则明确的神煞');
  console.log('3. 对有争议的神煞进行深入研究');
  console.log('4. 建立神煞计算的测试验证体系');
}

// 执行分析
console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('参考标准: "问真八字"权威软件');

analyzeTianyiGuirenProblem();
analyzeTaohuaProblem();
analyzeFuxingGuirenProblem();
researchWenZhenSpecialRules();
tryCorrectiveCalculations();
suggestSolutions();

console.log('\n📊 分析总结:');
console.log('='.repeat(40));
console.log('❌ 当前神煞计算规则与"问真八字"不完全匹配');
console.log('❓ 可能存在未知的特殊计算规则');
console.log('🎯 需要更深入的研究和验证');

console.log('\n🚀 下一步行动:');
console.log('1. 研究"问真八字"的具体算法');
console.log('2. 实现已知正确的神煞计算');
console.log('3. 建立神煞测试验证体系');
console.log('4. 逐步完善神煞计算准确性');
