#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的大运流年计算系统
实现精确的起运计算、大运排盘、流年流月分析
"""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class Gender(Enum):
    """性别枚举"""
    MALE = "男"
    FEMALE = "女"

class YearType(Enum):
    """年份类型"""
    YANG = "阳年"
    YIN = "阴年"

@dataclass
class QiyunInfo:
    """起运信息"""
    qiyun_age: int              # 起运年龄
    qiyun_date: str             # 起运日期
    calculation_method: str     # 计算方法（顺行/逆行）
    days_to_jieqi: int         # 距离节气天数
    precise_age: float         # 精确起运年龄（包含月日）

@dataclass
class DayunInfo:
    """大运信息"""
    sequence: int               # 大运序号
    ganzhi: str                # 大运干支
    tiangan: str               # 天干
    dizhi: str                 # 地支
    age_range: str             # 年龄段
    start_age: int             # 起始年龄
    end_age: int               # 结束年龄
    start_year: int            # 起始年份
    end_year: int              # 结束年份
    strength_analysis: Dict    # 旺衰分析
    fortune_tendency: str      # 运势倾向

@dataclass
class LiunianInfo:
    """流年信息"""
    year: int                  # 年份
    ganzhi: str               # 流年干支
    tiangan: str              # 天干
    dizhi: str                # 地支
    age: int                  # 当年年龄
    current_dayun: str        # 当前大运
    fortune_analysis: Dict    # 运势分析
    major_events: List[str]   # 主要事件预测

@dataclass
class LiuyueInfo:
    """流月信息"""
    month: int                # 月份
    ganzhi: str              # 流月干支
    jieqi: str               # 节气
    jieqi_date: str          # 节气日期
    fortune_brief: str       # 运势简述

class PreciseDayunCalculator:
    """精确的大运流年计算系统"""
    
    def __init__(self):
        """初始化大运计算器"""
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        self.jieqi_data = self._init_jieqi_data()
        self.dayun_analysis_rules = self._init_dayun_analysis_rules()
    
    def _init_jieqi_data(self) -> Dict:
        """初始化24节气数据"""
        return {
            1: {"立春": 4, "雨水": 19},
            2: {"惊蛰": 5, "春分": 20},
            3: {"清明": 5, "谷雨": 20},
            4: {"立夏": 5, "小满": 21},
            5: {"芒种": 6, "夏至": 21},
            6: {"小暑": 7, "大暑": 23},
            7: {"立秋": 8, "处暑": 23},
            8: {"白露": 8, "秋分": 23},
            9: {"寒露": 8, "霜降": 23},
            10: {"立冬": 8, "小雪": 22},
            11: {"大雪": 7, "冬至": 22},
            12: {"小寒": 6, "大寒": 20}
        }
    
    def _init_dayun_analysis_rules(self) -> Dict:
        """初始化大运分析规则"""
        return {
            "天干分析": {
                "甲": {"特点": "阳木主动", "适宜": "创业开拓", "注意": "避免冲动"},
                "乙": {"特点": "阴木柔韧", "适宜": "稳步发展", "注意": "防止优柔寡断"},
                "丙": {"特点": "阳火热情", "适宜": "展现才华", "注意": "控制脾气"},
                "丁": {"特点": "阴火温和", "适宜": "文化艺术", "注意": "增强自信"},
                "戊": {"特点": "阳土厚重", "适宜": "稳定发展", "注意": "避免固执"},
                "己": {"特点": "阴土包容", "适宜": "服务他人", "注意": "提高决断力"},
                "庚": {"特点": "阳金刚强", "适宜": "果断决策", "注意": "学会变通"},
                "辛": {"特点": "阴金精细", "适宜": "精工细作", "注意": "避免过于挑剔"},
                "壬": {"特点": "阳水流动", "适宜": "灵活应变", "注意": "保持专注"},
                "癸": {"特点": "阴水润泽", "适宜": "默默耕耘", "注意": "增强主动性"}
            },
            "地支分析": {
                "子": {"特点": "水旺之地", "运势": "智慧增长", "注意": "防止过于理想化"},
                "丑": {"特点": "土库湿土", "运势": "积累财富", "注意": "避免过于保守"},
                "寅": {"特点": "木旺生发", "运势": "事业发展", "注意": "控制冲动"},
                "卯": {"特点": "木旺茂盛", "运势": "人际和谐", "注意": "防止优柔寡断"},
                "辰": {"特点": "土库湿土", "运势": "稳步上升", "注意": "避免固执己见"},
                "巳": {"特点": "火旺炎热", "运势": "名声显达", "注意": "控制脾气"},
                "午": {"特点": "火旺光明", "运势": "事业辉煌", "注意": "防止骄傲"},
                "未": {"特点": "土库燥土", "运势": "收获成果", "注意": "保持谦逊"},
                "申": {"特点": "金旺肃杀", "运势": "决断有力", "注意": "学会变通"},
                "酉": {"特点": "金旺收敛", "运势": "精益求精", "注意": "避免过于严苛"},
                "戌": {"特点": "土库燥土", "运势": "基业稳固", "注意": "防止过于保守"},
                "亥": {"特点": "水旺流动", "运势": "智慧开启", "注意": "保持专注"}
            }
        }
    
    def calculate_precise_dayun(self, birth_datetime: datetime, gender: Gender, 
                               four_pillars: List[Tuple[str, str]]) -> Dict:
        """精确计算大运"""
        # 1. 计算起运信息
        qiyun_info = self._calculate_precise_qiyun(birth_datetime, gender, four_pillars)
        
        # 2. 计算大运序列
        dayun_sequence = self._calculate_dayun_sequence(
            birth_datetime, four_pillars, qiyun_info, gender
        )
        
        # 3. 计算流年分析
        liunian_analysis = self._calculate_liunian_analysis(
            birth_datetime, dayun_sequence
        )
        
        # 4. 计算流月分析
        liuyue_analysis = self._calculate_liuyue_analysis(
            birth_datetime.year, birth_datetime.month
        )
        
        # 5. 生成综合预测
        comprehensive_forecast = self._generate_comprehensive_forecast(
            dayun_sequence, liunian_analysis
        )
        
        return {
            "qiyun_info": qiyun_info,
            "dayun_sequence": dayun_sequence,
            "liunian_analysis": liunian_analysis,
            "liuyue_analysis": liuyue_analysis,
            "comprehensive_forecast": comprehensive_forecast,
            "current_analysis": self._get_current_analysis(
                birth_datetime, dayun_sequence, liunian_analysis
            )
        }
    
    def _calculate_precise_qiyun(self, birth_datetime: datetime, gender: Gender, 
                                four_pillars: List[Tuple[str, str]]) -> QiyunInfo:
        """精确计算起运时间"""
        year_gan = four_pillars[0][0]
        birth_month = birth_datetime.month
        birth_day = birth_datetime.day
        
        # 判断阳年阴年
        yang_gan = ["甲", "丙", "戊", "庚", "壬"]
        is_yang_year = year_gan in yang_gan
        
        # 确定顺逆行
        if (gender == Gender.MALE and is_yang_year) or (gender == Gender.FEMALE and not is_yang_year):
            calculation_method = "顺行"
            direction = 1
        else:
            calculation_method = "逆行"
            direction = -1
        
        # 计算距离最近节气的天数
        jieqi_info = self.jieqi_data.get(birth_month, {})
        
        # 简化计算：假设每月第一个节气在5号，第二个在20号
        if birth_day <= 15:
            # 距离当月第一个节气
            first_jieqi_day = list(jieqi_info.values())[0] if jieqi_info else 5
            days_to_jieqi = abs(birth_day - first_jieqi_day)
        else:
            # 距离当月第二个节气
            second_jieqi_day = list(jieqi_info.values())[1] if len(jieqi_info) > 1 else 20
            days_to_jieqi = abs(birth_day - second_jieqi_day)
        
        # 计算起运年龄（3天为1年）
        qiyun_years = days_to_jieqi / 3.0
        qiyun_age = int(qiyun_years)
        precise_age = qiyun_years
        
        # 计算起运日期
        qiyun_date = birth_datetime + timedelta(days=days_to_jieqi * 120)  # 简化计算
        
        return QiyunInfo(
            qiyun_age=qiyun_age,
            qiyun_date=qiyun_date.strftime("%Y年%m月%d日"),
            calculation_method=calculation_method,
            days_to_jieqi=days_to_jieqi,
            precise_age=precise_age
        )
    
    def _calculate_dayun_sequence(self, birth_datetime: datetime, 
                                 four_pillars: List[Tuple[str, str]], 
                                 qiyun_info: QiyunInfo, gender: Gender) -> List[DayunInfo]:
        """计算大运序列"""
        month_gan, month_zhi = four_pillars[1]  # 月柱
        
        # 确定方向
        direction = 1 if qiyun_info.calculation_method == "顺行" else -1
        
        gan_index = self.tiangan.index(month_gan)
        zhi_index = self.dizhi.index(month_zhi)
        
        dayun_sequence = []
        birth_year = birth_datetime.year
        
        for i in range(8):  # 计算8步大运
            # 计算大运干支
            current_gan_index = (gan_index + direction * (i + 1)) % 10
            current_zhi_index = (zhi_index + direction * (i + 1)) % 12
            
            current_gan = self.tiangan[current_gan_index]
            current_zhi = self.dizhi[current_zhi_index]
            ganzhi = f"{current_gan}{current_zhi}"
            
            # 计算年龄段
            start_age = qiyun_info.qiyun_age + i * 10
            end_age = start_age + 9
            start_year = birth_year + start_age
            end_year = birth_year + end_age
            
            # 分析大运强度
            strength_analysis = self._analyze_dayun_strength(current_gan, current_zhi)
            
            # 运势倾向
            fortune_tendency = self._analyze_fortune_tendency(current_gan, current_zhi)
            
            dayun_info = DayunInfo(
                sequence=i + 1,
                ganzhi=ganzhi,
                tiangan=current_gan,
                dizhi=current_zhi,
                age_range=f"{start_age}-{end_age}岁",
                start_age=start_age,
                end_age=end_age,
                start_year=start_year,
                end_year=end_year,
                strength_analysis=strength_analysis,
                fortune_tendency=fortune_tendency
            )
            
            dayun_sequence.append(dayun_info)
        
        return dayun_sequence
    
    def _analyze_dayun_strength(self, gan: str, zhi: str) -> Dict:
        """分析大运强度"""
        gan_analysis = self.dayun_analysis_rules["天干分析"].get(gan, {})
        zhi_analysis = self.dayun_analysis_rules["地支分析"].get(zhi, {})
        
        return {
            "天干特点": gan_analysis.get("特点", ""),
            "天干适宜": gan_analysis.get("适宜", ""),
            "天干注意": gan_analysis.get("注意", ""),
            "地支特点": zhi_analysis.get("特点", ""),
            "地支运势": zhi_analysis.get("运势", ""),
            "地支注意": zhi_analysis.get("注意", ""),
            "综合评价": self._get_comprehensive_evaluation(gan, zhi)
        }
    
    def _get_comprehensive_evaluation(self, gan: str, zhi: str) -> str:
        """获取综合评价"""
        # 简化的综合评价逻辑
        gan_score = {"甲": 8, "乙": 7, "丙": 9, "丁": 6, "戊": 7, 
                    "己": 6, "庚": 8, "辛": 7, "壬": 8, "癸": 6}.get(gan, 7)
        zhi_score = {"子": 8, "丑": 6, "寅": 9, "卯": 7, "辰": 7, "巳": 8,
                    "午": 9, "未": 6, "申": 8, "酉": 7, "戌": 6, "亥": 8}.get(zhi, 7)
        
        total_score = (gan_score + zhi_score) / 2
        
        if total_score >= 8.5:
            return "大吉，运势强劲，宜积极进取"
        elif total_score >= 7.5:
            return "中吉，运势平稳，稳步发展"
        elif total_score >= 6.5:
            return "平运，需要努力，谨慎行事"
        else:
            return "需要注意，多有阻碍，宜守不宜攻"
    
    def _analyze_fortune_tendency(self, gan: str, zhi: str) -> str:
        """分析运势倾向"""
        # 基于天干地支的运势倾向分析
        fortune_map = {
            ("甲", "子"): "智慧与行动并重，事业有成",
            ("乙", "丑"): "稳中求进，财运渐佳",
            ("丙", "寅"): "创业良机，名声显达",
            ("丁", "卯"): "文艺发展，人际和谐",
            ("戊", "辰"): "基业稳固，财富积累",
            ("己", "巳"): "服务他人，德望提升",
            ("庚", "午"): "决断有力，事业辉煌",
            ("辛", "未"): "精工细作，收获颇丰",
            ("壬", "申"): "灵活应变，机遇多多",
            ("癸", "酉"): "默默耕耘，精益求精"
        }
        
        return fortune_map.get((gan, zhi), "运势平稳，需要努力")
    
    def _calculate_liunian_analysis(self, birth_datetime: datetime, 
                                   dayun_sequence: List[DayunInfo]) -> List[LiunianInfo]:
        """计算流年分析"""
        birth_year = birth_datetime.year
        current_year = datetime.now().year
        liunian_list = []
        
        # 计算未来10年的流年
        for i in range(10):
            year = current_year + i
            age = year - birth_year
            
            # 计算流年干支
            gan_index = (year - 4) % 10  # 甲子年为基准
            zhi_index = (year - 4) % 12
            
            gan = self.tiangan[gan_index]
            zhi = self.dizhi[zhi_index]
            ganzhi = f"{gan}{zhi}"
            
            # 找到对应的大运
            current_dayun = "未知"
            for dayun in dayun_sequence:
                if dayun.start_year <= year <= dayun.end_year:
                    current_dayun = dayun.ganzhi
                    break
            
            # 运势分析
            fortune_analysis = self._analyze_liunian_fortune(gan, zhi, current_dayun)
            
            # 主要事件预测
            major_events = self._predict_major_events(gan, zhi, age)
            
            liunian_info = LiunianInfo(
                year=year,
                ganzhi=ganzhi,
                tiangan=gan,
                dizhi=zhi,
                age=age,
                current_dayun=current_dayun,
                fortune_analysis=fortune_analysis,
                major_events=major_events
            )
            
            liunian_list.append(liunian_info)
        
        return liunian_list
    
    def _analyze_liunian_fortune(self, gan: str, zhi: str, dayun_ganzhi: str) -> Dict:
        """分析流年运势"""
        return {
            "整体运势": self._get_overall_fortune(gan, zhi),
            "事业运": self._get_career_fortune(gan, zhi),
            "财运": self._get_wealth_fortune(gan, zhi),
            "感情运": self._get_relationship_fortune(gan, zhi),
            "健康运": self._get_health_fortune(gan, zhi),
            "与大运关系": self._analyze_dayun_liunian_relation(gan + zhi, dayun_ganzhi)
        }
    
    def _get_overall_fortune(self, gan: str, zhi: str) -> str:
        """获取整体运势"""
        fortune_scores = {
            "甲": 8, "乙": 7, "丙": 9, "丁": 6, "戊": 7,
            "己": 6, "庚": 8, "辛": 7, "壬": 8, "癸": 6
        }
        
        score = fortune_scores.get(gan, 7)
        
        if score >= 8:
            return "运势旺盛，诸事顺利"
        elif score >= 7:
            return "运势平稳，稳步发展"
        else:
            return "运势一般，需要努力"
    
    def _get_career_fortune(self, gan: str, zhi: str) -> str:
        """获取事业运"""
        career_map = {
            "甲": "创业开拓，事业有成", "乙": "稳步发展，渐入佳境",
            "丙": "展现才华，名声显达", "丁": "文化艺术，发展良好",
            "戊": "稳定发展，基业稳固", "己": "服务他人，德望提升",
            "庚": "果断决策，事业辉煌", "辛": "精工细作，品质提升",
            "壬": "灵活应变，机遇多多", "癸": "默默耕耘，厚积薄发"
        }
        
        return career_map.get(gan, "事业平稳发展")
    
    def _get_wealth_fortune(self, gan: str, zhi: str) -> str:
        """获取财运"""
        wealth_map = {
            "甲": "正财运佳，投资有道", "乙": "偏财运好，意外收获",
            "丙": "财运旺盛，收入增加", "丁": "财运平稳，量入为出",
            "戊": "财富积累，稳步增长", "己": "理财有方，收益稳定",
            "庚": "财运强劲，投资获利", "辛": "精打细算，财富增值",
            "壬": "财运流动，把握时机", "癸": "财运渐佳，积少成多"
        }
        
        return wealth_map.get(gan, "财运平稳")
    
    def _get_relationship_fortune(self, gan: str, zhi: str) -> str:
        """获取感情运"""
        relationship_map = {
            "甲": "感情主动，桃花运佳", "乙": "感情温和，关系和谐",
            "丙": "感情热烈，魅力十足", "丁": "感情细腻，温馨浪漫",
            "戊": "感情稳定，家庭和睦", "己": "感情包容，相处融洽",
            "庚": "感情直接，需要沟通", "辛": "感情精致，追求完美",
            "壬": "感情多变，需要专一", "癸": "感情深沉，默默付出"
        }
        
        return relationship_map.get(gan, "感情运平稳")
    
    def _get_health_fortune(self, gan: str, zhi: str) -> str:
        """获取健康运"""
        health_map = {
            "甲": "肝胆健康，注意眼部", "乙": "肝胆保养，防止过劳",
            "丙": "心脏健康，控制情绪", "丁": "心脏保养，注意血压",
            "戊": "脾胃健康，饮食规律", "己": "脾胃保养，避免湿气",
            "庚": "肺部健康，注意呼吸", "辛": "肺部保养，防止干燥",
            "壬": "肾脏健康，注意腰部", "癸": "肾脏保养，避免寒湿"
        }
        
        return health_map.get(gan, "身体健康，注意保养")
    
    def _analyze_dayun_liunian_relation(self, liunian_ganzhi: str, dayun_ganzhi: str) -> str:
        """分析大运流年关系"""
        if not dayun_ganzhi or dayun_ganzhi == "未知":
            return "大运信息不明"
        
        # 简化的大运流年关系分析
        if liunian_ganzhi == dayun_ganzhi:
            return "流年与大运相同，运势加强"
        else:
            return "流年与大运配合，运势平稳"
    
    def _predict_major_events(self, gan: str, zhi: str, age: int) -> List[str]:
        """预测主要事件"""
        events = []
        
        # 基于年龄的事件预测
        if 25 <= age <= 35:
            events.append("事业发展关键期")
            events.append("婚姻感情重要阶段")
        elif 35 <= age <= 45:
            events.append("事业稳定发展期")
            events.append("家庭责任加重")
        elif 45 <= age <= 55:
            events.append("事业巅峰期")
            events.append("财富积累期")
        elif age > 55:
            events.append("享受成果期")
            events.append("健康保养期")
        
        # 基于天干的特殊事件
        gan_events = {
            "甲": ["创业机会", "领导职位"],
            "乙": ["合作机会", "艺术发展"],
            "丙": ["名声提升", "表现机会"],
            "丁": ["文化活动", "学习进修"],
            "戊": ["房产投资", "基业建设"],
            "己": ["服务机会", "人际拓展"],
            "庚": ["决策机会", "变革时期"],
            "辛": ["技能提升", "品质改善"],
            "壬": ["旅行机会", "环境变化"],
            "癸": ["学习机会", "内在成长"]
        }
        
        if gan in gan_events:
            events.extend(gan_events[gan])
        
        return events[:3]  # 返回前3个主要事件
    
    def _calculate_liuyue_analysis(self, year: int, start_month: int) -> List[LiuyueInfo]:
        """计算流月分析"""
        liuyue_list = []
        
        for month in range(1, 13):
            # 计算流月干支（简化计算）
            gan_index = (year * 12 + month - 1) % 10
            zhi_index = (month - 1) % 12
            
            gan = self.tiangan[gan_index]
            zhi = self.dizhi[zhi_index]
            ganzhi = f"{gan}{zhi}"
            
            # 获取节气信息
            jieqi_info = self.jieqi_data.get(month, {})
            jieqi_names = list(jieqi_info.keys())
            jieqi = jieqi_names[0] if jieqi_names else "未知"
            jieqi_date = f"{month}月{jieqi_info.get(jieqi, 1)}日" if jieqi_info else "未知"
            
            # 运势简述
            fortune_brief = self._get_month_fortune_brief(gan, zhi, month)
            
            liuyue_info = LiuyueInfo(
                month=month,
                ganzhi=ganzhi,
                jieqi=jieqi,
                jieqi_date=jieqi_date,
                fortune_brief=fortune_brief
            )
            
            liuyue_list.append(liuyue_info)
        
        return liuyue_list
    
    def _get_month_fortune_brief(self, gan: str, zhi: str, month: int) -> str:
        """获取月运势简述"""
        season_fortune = {
            1: "新年伊始，万象更新", 2: "春回大地，生机勃勃", 3: "春暖花开，事业发展",
            4: "春末夏初，变化较多", 5: "夏季来临，活力充沛", 6: "夏日炎炎，需要冷静",
            7: "夏秋之交，收获在望", 8: "秋高气爽，成果显现", 9: "秋意渐浓，稳步发展",
            10: "深秋时节，收获颇丰", 11: "冬季来临，宜于储备", 12: "年终岁末，总结反思"
        }
        
        return season_fortune.get(month, "运势平稳")
    
    def _generate_comprehensive_forecast(self, dayun_sequence: List[DayunInfo], 
                                       liunian_analysis: List[LiunianInfo]) -> Dict:
        """生成综合预测"""
        return {
            "近期运势": "基于当前大运和流年的综合分析",
            "中期发展": "未来3-5年的发展趋势预测",
            "长期规划": "人生大运的整体规划建议",
            "关键节点": self._identify_key_periods(dayun_sequence, liunian_analysis),
            "发展建议": self._generate_development_suggestions(dayun_sequence)
        }
    
    def _identify_key_periods(self, dayun_sequence: List[DayunInfo], 
                            liunian_analysis: List[LiunianInfo]) -> List[str]:
        """识别关键时期"""
        key_periods = []
        
        # 分析大运转换期
        for i, dayun in enumerate(dayun_sequence[:3]):  # 分析前3步大运
            if "大吉" in dayun.strength_analysis.get("综合评价", ""):
                key_periods.append(f"{dayun.age_range}：{dayun.ganzhi}大运，运势强劲")
        
        # 分析流年关键点
        for liunian in liunian_analysis[:5]:  # 分析前5年
            if "旺盛" in liunian.fortune_analysis.get("整体运势", ""):
                key_periods.append(f"{liunian.year}年：{liunian.ganzhi}年，运势旺盛")
        
        return key_periods[:5]  # 返回前5个关键时期
    
    def _generate_development_suggestions(self, dayun_sequence: List[DayunInfo]) -> List[str]:
        """生成发展建议"""
        suggestions = []
        
        for dayun in dayun_sequence[:3]:  # 分析前3步大运
            gan_advice = dayun.strength_analysis.get("天干适宜", "")
            if gan_advice:
                suggestions.append(f"{dayun.age_range}：{gan_advice}")
        
        return suggestions[:5]  # 返回前5个建议
    
    def _get_current_analysis(self, birth_datetime: datetime, 
                            dayun_sequence: List[DayunInfo], 
                            liunian_analysis: List[LiunianInfo]) -> Dict:
        """获取当前分析"""
        current_age = datetime.now().year - birth_datetime.year
        current_year = datetime.now().year
        
        # 找到当前大运
        current_dayun = None
        for dayun in dayun_sequence:
            if dayun.start_age <= current_age <= dayun.end_age:
                current_dayun = dayun
                break
        
        # 找到当前流年
        current_liunian = None
        for liunian in liunian_analysis:
            if liunian.year == current_year:
                current_liunian = liunian
                break
        
        return {
            "当前年龄": current_age,
            "当前大运": current_dayun.ganzhi if current_dayun else "未知",
            "当前流年": current_liunian.ganzhi if current_liunian else "未知",
            "运势评价": current_dayun.strength_analysis.get("综合评价", "运势平稳") if current_dayun else "运势平稳",
            "发展建议": current_dayun.strength_analysis.get("天干适宜", "稳步发展") if current_dayun else "稳步发展"
        }


# 测试和使用示例
def main():
    """测试大运流年计算系统"""
    print("🌟 大运流年计算系统测试")
    print("=" * 50)
    
    # 创建大运计算器
    calculator = PreciseDayunCalculator()
    
    # 测试数据
    birth_datetime = datetime(1990, 7, 21, 18, 2)
    gender = Gender.MALE
    test_four_pillars = [
        ("庚", "午"),  # 年柱
        ("癸", "未"),  # 月柱
        ("丁", "丑"),  # 日柱
        ("丁", "未")   # 时柱
    ]
    
    # 计算大运流年
    result = calculator.calculate_precise_dayun(birth_datetime, gender, test_four_pillars)
    
    # 显示起运信息
    qiyun = result["qiyun_info"]
    print("📅 起运信息:")
    print(f"   起运年龄: {qiyun.qiyun_age}岁")
    print(f"   起运日期: {qiyun.qiyun_date}")
    print(f"   计算方法: {qiyun.calculation_method}")
    print(f"   精确年龄: {qiyun.precise_age:.2f}岁")
    
    # 显示大运序列
    print("\n🔮 大运序列:")
    for dayun in result["dayun_sequence"][:3]:  # 显示前3步大运
        print(f"   第{dayun.sequence}步: {dayun.ganzhi} ({dayun.age_range})")
        print(f"      运势倾向: {dayun.fortune_tendency}")
        print(f"      综合评价: {dayun.strength_analysis['综合评价']}")
        print()
    
    # 显示流年分析
    print("📊 流年分析 (未来3年):")
    for liunian in result["liunian_analysis"][:3]:
        print(f"   {liunian.year}年 ({liunian.age}岁): {liunian.ganzhi}")
        print(f"      整体运势: {liunian.fortune_analysis['整体运势']}")
        print(f"      事业运: {liunian.fortune_analysis['事业运']}")
        print(f"      主要事件: {', '.join(liunian.major_events[:2])}")
        print()
    
    # 显示当前分析
    current = result["current_analysis"]
    print("🎯 当前分析:")
    print(f"   当前年龄: {current['当前年龄']}岁")
    print(f"   当前大运: {current['当前大运']}")
    print(f"   当前流年: {current['当前流年']}")
    print(f"   运势评价: {current['运势评价']}")
    print(f"   发展建议: {current['发展建议']}")
    
    print("\n✅ 大运流年计算系统测试完成！")


if __name__ == "__main__":
    main()
