/**
 * 标签页内容分配验证测试
 * 验证所有模块是否在正确的标签页位置
 */

const fs = require('fs');
const path = require('path');

class TabContentAllocationVerifier {
  constructor() {
    this.wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
    this.wxmlContent = '';
    this.verificationResults = {};
  }

  async runVerification() {
    console.log('🔍 开始标签页内容分配验证...\n');
    
    try {
      // 读取WXML文件
      this.wxmlContent = fs.readFileSync(this.wxmlPath, 'utf8');
      
      // 验证各个标签页
      await this.verifyBasicTab();
      await this.verifyPaipanTab();
      await this.verifyAdvancedTab();
      await this.verifyFortuneTab();
      await this.verifyProfessionalTab();
      await this.verifyClassicalTab();
      await this.verifyTimingTab();
      await this.verifyLiuqinTab();
      
      // 生成总结报告
      this.generateSummaryReport();
      
    } catch (error) {
      console.error('❌ 验证过程中出现错误:', error.message);
    }
  }

  verifyBasicTab() {
    console.log('📋 验证基本信息标签页...');
    const basicTabContent = this.extractTabContent('basic');
    
    const expectedModules = [
      '用户基本信息',
      '八字概览',
      '出生天体图'
    ];
    
    const unexpectedModules = [
      '四柱八字',
      '五行分析',
      '十神分析',
      '大运流年'
    ];
    
    this.verificationResults.basic = this.checkModules(basicTabContent, expectedModules, unexpectedModules);
    console.log(`   ✅ 预期模块: ${this.verificationResults.basic.expectedFound}/${expectedModules.length}`);
    console.log(`   ✅ 错配检查: ${this.verificationResults.basic.unexpectedNotFound}/${unexpectedModules.length} 已移除\n`);
  }

  verifyPaipanTab() {
    console.log('🔮 验证四柱排盘标签页...');
    const paipanTabContent = this.extractTabContent('paipan');
    
    const expectedModules = [
      '四柱排盘主卡片',
      '十神分析',
      '藏干分析'
    ];
    
    const unexpectedModules = [
      '大运流年',
      '五行分析',
      '神煞'
    ];
    
    this.verificationResults.paipan = this.checkModules(paipanTabContent, expectedModules, unexpectedModules);
    console.log(`   ✅ 预期模块: ${this.verificationResults.paipan.expectedFound}/${expectedModules.length}`);
    console.log(`   ✅ 错配检查: ${this.verificationResults.paipan.unexpectedNotFound}/${unexpectedModules.length} 已移除\n`);
  }

  verifyAdvancedTab() {
    console.log('⚡ 验证神煞五行标签页...');
    const advancedTabContent = this.extractTabContent('advanced');
    
    const expectedModules = [
      '五行分析',
      '五行强弱',
      '吉星神煞',
      '凶星神煞',
      '神煞综合分析'
    ];
    
    const unexpectedModules = [
      '用神喜忌',
      '格局分析',
      '大运流年'
    ];
    
    this.verificationResults.advanced = this.checkModules(advancedTabContent, expectedModules, unexpectedModules);
    console.log(`   ✅ 预期模块: ${this.verificationResults.advanced.expectedFound}/${expectedModules.length}`);
    console.log(`   ✅ 错配检查: ${this.verificationResults.advanced.unexpectedNotFound}/${unexpectedModules.length} 已移除\n`);
  }

  verifyFortuneTab() {
    console.log('🌊 验证大运流年标签页...');
    const fortuneTabContent = this.extractTabContent('fortune');
    
    const expectedModules = [
      '当前大运',
      '近期流年',
      '十年大运总览'
    ];
    
    this.verificationResults.fortune = this.checkModules(fortuneTabContent, expectedModules, []);
    console.log(`   ✅ 预期模块: ${this.verificationResults.fortune.expectedFound}/${expectedModules.length}\n`);
  }

  verifyProfessionalTab() {
    console.log('👑 验证格局用神标签页...');
    const professionalTabContent = this.extractTabContent('professional');
    
    const expectedModules = [
      '数字化分析总览',
      '增强格局分析',
      '增强用神计算',
      '用神喜忌',
      '专业级五行分析'
    ];
    
    this.verificationResults.professional = this.checkModules(professionalTabContent, expectedModules, []);
    console.log(`   ✅ 预期模块: ${this.verificationResults.professional.expectedFound}/${expectedModules.length}\n`);
  }

  verifyClassicalTab() {
    console.log('📜 验证古籍分析标签页...');
    const classicalTabContent = this.extractTabContent('classical');
    
    const expectedModules = [
      '古籍命理'
    ];
    
    this.verificationResults.classical = this.checkModules(classicalTabContent, expectedModules, []);
    console.log(`   ✅ 预期模块: ${this.verificationResults.classical.expectedFound}/${expectedModules.length}\n`);
  }

  verifyTimingTab() {
    console.log('⏰ 验证应期分析标签页...');
    const timingTabContent = this.extractTabContent('timing');
    
    const expectedModules = [
      '病药平衡法则',
      '能量阈值模型',
      '三重引动机制',
      '动态分析引擎'
    ];
    
    this.verificationResults.timing = this.checkModules(timingTabContent, expectedModules, []);
    console.log(`   ✅ 预期模块: ${this.verificationResults.timing.expectedFound}/${expectedModules.length}\n`);
  }

  verifyLiuqinTab() {
    console.log('👥 验证六亲分析标签页...');
    const liuqinTabContent = this.extractTabContent('liuqin');
    
    const expectedModules = [
      '配偶分析',
      '父母分析',
      '子女分析'
    ];
    
    this.verificationResults.liuqin = this.checkModules(liuqinTabContent, expectedModules, []);
    console.log(`   ✅ 预期模块: ${this.verificationResults.liuqin.expectedFound}/${expectedModules.length}\n`);
  }

  extractTabContent(tabName) {
    const tabPattern = new RegExp(`currentTab === '${tabName}'.*?(?=currentTab === '|$)`, 's');
    const match = this.wxmlContent.match(tabPattern);
    return match ? match[0] : '';
  }

  checkModules(content, expectedModules, unexpectedModules) {
    let expectedFound = 0;
    let unexpectedNotFound = 0;
    
    expectedModules.forEach(module => {
      if (content.includes(module) || this.checkModuleVariations(content, module)) {
        expectedFound++;
      }
    });
    
    unexpectedModules.forEach(module => {
      if (!content.includes(module) && !this.checkModuleVariations(content, module)) {
        unexpectedNotFound++;
      }
    });
    
    return {
      expectedFound,
      unexpectedNotFound,
      expectedTotal: expectedModules.length,
      unexpectedTotal: unexpectedModules.length
    };
  }

  checkModuleVariations(content, module) {
    const variations = {
      '用户基本信息': ['user-info-card', 'card-title">基本信息'],
      '八字概览': ['bazi-overview-card', 'card-title">八字概览'],
      '出生天体图': ['celestial-chart-card', 'card-title">出生天体图'],
      '四柱排盘主卡片': ['paipan-table', 'card-title">四柱排盘'],
      '十神分析': ['shishen-card', 'card-title">十神分析'],
      '藏干分析': ['canggan-card', 'card-title">藏干分析'],
      '五行分析': ['wuxing-card', 'card-title">五行分析'],
      '五行强弱': ['wuxing-strength-card', 'card-title">五行强弱'],
      '吉星神煞': ['auspicious-stars-card', 'card-title">吉星神煞'],
      '凶星神煞': ['inauspicious-stars-card', 'card-title">凶星神煞'],
      '神煞综合分析': ['shensha-summary-card', 'card-title">神煞综合分析'],
      '当前大运': ['current-dayun-card', 'card-title">当前大运'],
      '近期流年': ['liunian-card', 'card-title">近期流年'],
      '十年大运总览': ['dayun-overview-card', 'card-title">十年大运总览'],
      '数字化分析总览': ['digital-overview-card', 'card-title">数字化分析总览'],
      '增强格局分析': ['enhanced-pattern-card', 'card-title">增强格局分析'],
      '增强用神计算': ['enhanced-yongshen-card', 'card-title">增强用神计算'],
      '用神喜忌': ['yongshen-card', 'card-title">用神喜忌'],
      '专业级五行分析': ['professional-wuxing-card', 'card-title">专业级五行分析'],
      '古籍命理': ['classical-analysis-card', 'card-title">古籍命理'],
      '病药平衡法则': ['timing-card', 'card-title">病药平衡法则'],
      '能量阈值模型': ['energy-threshold-card', 'card-title">能量阈值模型'],
      '三重引动机制': ['triple-activation-card', 'card-title">三重引动机制'],
      '动态分析引擎': ['dynamic-analysis-card', 'card-title">动态分析引擎'],
      '配偶分析': ['spouse-analysis-card', 'card-title">配偶分析'],
      '父母分析': ['parents-analysis-card', 'card-title">父母分析'],
      '子女分析': ['children-analysis-card', 'card-title">子女分析'],
      // 错配检查
      '四柱八字': ['bazi-card', 'card-title">四柱八字'],
      '大运流年': ['dayun-card', 'card-title">大运流年'],
      '神煞': ['shensha', '神煞'],
      '格局分析': ['pattern-card', 'card-title">格局分析']
    };

    if (variations[module]) {
      return variations[module].some(variation => content.includes(variation));
    }

    return false;
  }

  generateSummaryReport() {
    console.log('📊 标签页内容分配验证总结报告');
    console.log('='.repeat(50));
    
    let totalExpected = 0;
    let totalExpectedFound = 0;
    let totalUnexpected = 0;
    let totalUnexpectedRemoved = 0;
    
    Object.keys(this.verificationResults).forEach(tab => {
      const result = this.verificationResults[tab];
      totalExpected += result.expectedTotal;
      totalExpectedFound += result.expectedFound;
      totalUnexpected += result.unexpectedTotal;
      totalUnexpectedRemoved += result.unexpectedNotFound;
    });
    
    const expectedScore = (totalExpectedFound / totalExpected * 100).toFixed(1);
    const cleanupScore = totalUnexpected > 0 ? (totalUnexpectedRemoved / totalUnexpected * 100).toFixed(1) : 100;
    const overallScore = ((parseFloat(expectedScore) + parseFloat(cleanupScore)) / 2).toFixed(1);
    
    console.log(`🎯 预期模块完整性: ${totalExpectedFound}/${totalExpected} (${expectedScore}%)`);
    console.log(`🧹 错配清理完成度: ${totalUnexpectedRemoved}/${totalUnexpected} (${cleanupScore}%)`);
    console.log(`🏆 总体评分: ${overallScore}%`);
    
    if (overallScore >= 95) {
      console.log('\n✅ 标签页内容分配修正完成！所有模块都在正确位置。');
    } else if (overallScore >= 80) {
      console.log('\n⚠️ 标签页内容分配基本正确，但仍有少量问题需要处理。');
    } else {
      console.log('\n❌ 标签页内容分配仍存在较多问题，需要进一步修正。');
    }
    
    console.log('\n🔧 修正效果:');
    console.log('   ✅ 基本信息页面: 专注于用户信息和概览');
    console.log('   ✅ 四柱排盘页面: 专注于四柱详细分析');
    console.log('   ✅ 神煞五行页面: 专注于五行和神煞分析');
    console.log('   ✅ 格局用神页面: 专注于格局和用神分析');
    console.log('   ✅ 其他页面: 内容保持专业性和逻辑性');
  }
}

// 运行验证
const verifier = new TabContentAllocationVerifier();
verifier.runVerification();
