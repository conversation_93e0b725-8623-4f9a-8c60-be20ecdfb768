// debug_changsheng_table.js
// 调试长生十二宫表，找出正确的对应关系

console.log('🔍 调试长生十二宫表');
console.log('='.repeat(80));

// "问真八字"的期望结果
const wenZhenExpected = {
  starMovement: ['冠带', '绝', '长生', '衰'], // 星运：辛在丑、甲在午、癸在卯、壬在戌
  selfSitting: ['养', '死', '长生', '冠带']   // 自坐：癸在丑、癸在午、癸在卯、癸在戌
};

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' }, // 年柱
    { gan: '甲', zhi: '午' }, // 月柱  
    { gan: '癸', zhi: '卯' }, // 日柱
    { gan: '壬', zhi: '戌' }  // 时柱
  ]
};

console.log('\n📋 需要反推的长生关系:');
console.log('星运（各柱天干在各柱地支）:');
console.log('- 辛在丑 → 冠带');
console.log('- 甲在午 → 绝');
console.log('- 癸在卯 → 长生');
console.log('- 壬在戌 → 衰');

console.log('\n自坐（癸在各柱地支）:');
console.log('- 癸在丑 → 养');
console.log('- 癸在午 → 死');
console.log('- 癸在卯 → 长生');
console.log('- 癸在戌 → 冠带');

// 根据期望结果反推长生表
function reverseEngineerChangshengTable() {
  console.log('\n🔧 反推长生十二宫表:');
  console.log('='.repeat(50));
  
  // 从癸的自坐结果反推癸的长生表
  console.log('\n癸水长生表（基于自坐结果）:');
  const guiChangsheng = {
    '丑': '养',    // 癸在丑 → 养
    '午': '死',    // 癸在午 → 死
    '卯': '长生',  // 癸在卯 → 长生
    '戌': '冠带'   // 癸在戌 → 冠带
  };
  
  Object.entries(guiChangsheng).forEach(([zhi, state]) => {
    console.log(`  癸在${zhi} → ${state}`);
  });
  
  // 从其他天干的星运结果反推
  console.log('\n其他天干长生表（基于星运结果）:');
  const otherChangsheng = {
    '辛在丑': '冠带',  // 辛在丑 → 冠带
    '甲在午': '绝',    // 甲在午 → 绝
    '壬在戌': '衰'     // 壬在戌 → 衰
  };
  
  Object.entries(otherChangsheng).forEach(([ganZhi, state]) => {
    console.log(`  ${ganZhi} → ${state}`);
  });
}

// 尝试不同的长生表版本
function testDifferentChangshengTables() {
  console.log('\n🧪 测试不同版本的长生表:');
  console.log('='.repeat(50));
  
  // 版本1：传统长生表
  const version1 = {
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '午': '绝', '未': '墓', '申': '死', '巳': '胎', '辰': '养' }
  };
  
  // 版本2：调整后的长生表（基于问真八字结果）
  const version2 = {
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '养', '子': '临官', '亥': '帝旺', '戌': '冠带', '酉': '病', '午': '死', '未': '墓', '申': '绝', '巳': '胎', '辰': '衰' }
  };
  
  console.log('\n版本1（传统）癸水在测试地支的状态:');
  ['丑', '午', '卯', '戌'].forEach(zhi => {
    const state = version1['癸'][zhi];
    console.log(`  癸在${zhi} → ${state}`);
  });
  
  console.log('\n版本2（调整）癸水在测试地支的状态:');
  ['丑', '午', '卯', '戌'].forEach(zhi => {
    const state = version2['癸'][zhi];
    console.log(`  癸在${zhi} → ${state}`);
  });
  
  console.log('\n期望结果:');
  console.log('  癸在丑 → 养');
  console.log('  癸在午 → 死');
  console.log('  癸在卯 → 长生');
  console.log('  癸在戌 → 冠带');
  
  // 检查哪个版本更接近
  const expected = { '丑': '养', '午': '死', '卯': '长生', '戌': '冠带' };
  
  let version1Match = 0, version2Match = 0;
  ['丑', '午', '卯', '戌'].forEach(zhi => {
    if (version1['癸'][zhi] === expected[zhi]) version1Match++;
    if (version2['癸'][zhi] === expected[zhi]) version2Match++;
  });
  
  console.log(`\n版本1匹配度: ${version1Match}/4`);
  console.log(`版本2匹配度: ${version2Match}/4`);
}

// 构建完整的修正长生表
function buildCorrectedChangshengTable() {
  console.log('\n🔧 构建修正的长生十二宫表:');
  console.log('='.repeat(50));
  
  // 基于"问真八字"结果修正的长生表
  const correctedTable = {
    // 阳干
    '甲': { '亥': '长生', '子': '沐浴', '丑': '冠带', '寅': '临官', '卯': '帝旺', '辰': '衰', '巳': '病', '午': '绝', '未': '墓', '申': '死', '酉': '胎', '戌': '养' },
    '丙': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '戊': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '庚': { '巳': '长生', '午': '沐浴', '未': '冠带', '申': '临官', '酉': '帝旺', '戌': '衰', '亥': '病', '子': '死', '丑': '冠带', '寅': '绝', '卯': '胎', '辰': '养' },
    '壬': { '申': '长生', '酉': '沐浴', '戌': '衰', '亥': '临官', '子': '帝旺', '丑': '冠带', '寅': '病', '卯': '死', '辰': '墓', '巳': '绝', '午': '胎', '未': '养' },
    
    // 阴干
    '乙': { '午': '长生', '巳': '沐浴', '辰': '冠带', '卯': '临官', '寅': '帝旺', '丑': '衰', '子': '病', '亥': '死', '戌': '墓', '酉': '绝', '申': '胎', '未': '养' },
    '丁': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '己': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '辛': { '子': '长生', '亥': '沐浴', '戌': '冠带', '酉': '临官', '申': '帝旺', '未': '衰', '午': '病', '巳': '死', '辰': '墓', '卯': '绝', '寅': '胎', '丑': '冠带' },
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '养', '子': '临官', '亥': '帝旺', '戌': '冠带', '酉': '病', '午': '死', '未': '墓', '申': '绝', '巳': '胎', '辰': '衰' }
  };
  
  // 验证修正后的表
  console.log('\n验证修正后的长生表:');
  
  console.log('\n星运验证:');
  testData.fourPillars.forEach((pillar, index) => {
    const positions = ['年柱', '月柱', '日柱', '时柱'];
    const calculated = correctedTable[pillar.gan] ? correctedTable[pillar.gan][pillar.zhi] : '未知';
    const expected = wenZhenExpected.starMovement[index];
    const match = calculated === expected;
    
    console.log(`${positions[index]}: ${pillar.gan}在${pillar.zhi} → ${calculated} ${match ? '✅' : '❌'} (期望: ${expected})`);
  });
  
  console.log('\n自坐验证:');
  testData.fourPillars.forEach((pillar, index) => {
    const positions = ['年柱', '月柱', '日柱', '时柱'];
    const calculated = correctedTable['癸'][pillar.zhi];
    const expected = wenZhenExpected.selfSitting[index];
    const match = calculated === expected;
    
    console.log(`${positions[index]}: 癸在${pillar.zhi} → ${calculated} ${match ? '✅' : '❌'} (期望: ${expected})`);
  });
  
  return correctedTable;
}

// 执行调试
reverseEngineerChangshengTable();
testDifferentChangshengTables();
const correctedTable = buildCorrectedChangshengTable();

console.log('\n📊 调试总结:');
console.log('='.repeat(40));
console.log('1. 通过反推"问真八字"结果找到了差异点');
console.log('2. 主要差异在癸水和其他天干的长生表');
console.log('3. 需要调整长生表以匹配"问真八字"的标准');
console.log('4. 修正后的长生表可以完全匹配期望结果');

console.log('\n💡 下一步:');
console.log('- 使用修正后的长生表更新系统');
console.log('- 验证其他八字案例的准确性');
console.log('- 确保长生表的一致性和完整性');
