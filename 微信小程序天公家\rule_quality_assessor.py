#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则质量评估器
验证筛选出的规则是否符合高质量标准，并生成详细的质量报告
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Tuple
from collections import defaultdict, Counter

class RuleQualityAssessor:
    def __init__(self):
        # 基于38条高质量规则的标准
        self.quality_benchmarks = {
            "文本清晰率": 0.816,  # 81.6%
            "置信度": 0.92,       # 92%
            "结构完整性": 1.0,    # 100%
            "平均文本长度": 534.2, # 534字符
            "高置信度比例": 0.921  # 92.1%在0.9-1.0范围
        }
        
        # 质量检测标准
        self.quality_criteria = {
            "必需字段": ["rule_id", "pattern_name", "category", "original_text", "interpretations", "confidence"],
            "最小文本长度": 30,
            "最大文本长度": 2000,
            "最低置信度": 0.85,
            "最高置信度": 1.0,
            "清晰文本模式": [
                r'[\u4e00-\u9fff]+',  # 包含中文
                r'[。；！？]',         # 包含标点
            ],
            "问题文本模式": [
                r'氺|灬|釒|本|士',     # OCR错误
                r'\.{3,}',            # 多个省略号
                r'例如：',             # 示例标记
                r'沂水易士注',         # 注释标记
            ]
        }
    
    def load_rules_data(self, filename: str) -> Dict:
        """加载规则数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            metadata = data.get('metadata', {})
            
            print(f"✅ 成功加载 {len(rules)} 条规则")
            return {"rules": rules, "metadata": metadata}
            
        except Exception as e:
            print(f"❌ 加载规则数据失败: {e}")
            return {"rules": [], "metadata": {}}
    
    def assess_single_rule(self, rule: Dict) -> Dict:
        """评估单条规则的质量"""
        assessment = {
            "rule_id": rule.get('rule_id', ''),
            "quality_score": 0,
            "issues": [],
            "strengths": [],
            "metrics": {}
        }
        
        # 1. 结构完整性检查
        structure_score = 0
        missing_fields = []
        
        for field in self.quality_criteria["必需字段"]:
            if field in rule and rule[field]:
                structure_score += 1
            else:
                missing_fields.append(field)
        
        structure_completeness = structure_score / len(self.quality_criteria["必需字段"])
        assessment["metrics"]["structure_completeness"] = structure_completeness
        
        if missing_fields:
            assessment["issues"].append(f"缺失字段: {', '.join(missing_fields)}")
        else:
            assessment["strengths"].append("结构完整")
        
        # 2. 文本质量检查
        text = rule.get('original_text', '')
        text_length = len(text)
        assessment["metrics"]["text_length"] = text_length
        
        # 文本长度检查
        if text_length < self.quality_criteria["最小文本长度"]:
            assessment["issues"].append(f"文本过短 ({text_length}字符)")
        elif text_length > self.quality_criteria["最大文本长度"]:
            assessment["issues"].append(f"文本过长 ({text_length}字符)")
        else:
            assessment["strengths"].append("文本长度适中")
        
        # 文本清晰度检查
        clear_patterns = 0
        for pattern in self.quality_criteria["清晰文本模式"]:
            if re.search(pattern, text):
                clear_patterns += 1
        
        text_clarity = clear_patterns / len(self.quality_criteria["清晰文本模式"])
        assessment["metrics"]["text_clarity"] = text_clarity
        
        if text_clarity >= 0.8:
            assessment["strengths"].append("文本清晰")
        else:
            assessment["issues"].append("文本清晰度不足")
        
        # 问题文本检查
        problem_patterns = 0
        detected_problems = []
        
        for pattern in self.quality_criteria["问题文本模式"]:
            if re.search(pattern, text):
                problem_patterns += 1
                detected_problems.append(pattern)
        
        if problem_patterns > 0:
            assessment["issues"].append(f"包含问题模式: {', '.join(detected_problems)}")
        else:
            assessment["strengths"].append("无明显文本问题")
        
        # 3. 置信度检查
        confidence = rule.get('confidence', 0)
        assessment["metrics"]["confidence"] = confidence
        
        if confidence < self.quality_criteria["最低置信度"]:
            assessment["issues"].append(f"置信度过低 ({confidence})")
        elif confidence > self.quality_criteria["最高置信度"]:
            assessment["issues"].append(f"置信度异常 ({confidence})")
        elif confidence >= 0.92:
            assessment["strengths"].append("高置信度")
        else:
            assessment["strengths"].append("置信度合格")
        
        # 4. 内容质量检查
        interpretations = rule.get('interpretations', '')
        if interpretations and len(interpretations) > 10:
            assessment["strengths"].append("包含解释说明")
        else:
            assessment["issues"].append("缺少或解释过短")
        
        # 5. 计算综合质量分数
        quality_score = 0
        
        # 结构完整性 (25分)
        quality_score += structure_completeness * 25
        
        # 文本质量 (35分)
        text_score = 0
        if text_length >= 50:
            text_score += 15
        if text_clarity >= 0.8:
            text_score += 15
        if problem_patterns == 0:
            text_score += 5
        quality_score += text_score
        
        # 置信度 (25分)
        if confidence >= 0.95:
            quality_score += 25
        elif confidence >= 0.92:
            quality_score += 22
        elif confidence >= 0.90:
            quality_score += 20
        elif confidence >= 0.85:
            quality_score += 15
        
        # 内容完整性 (15分)
        if interpretations and len(interpretations) > 20:
            quality_score += 15
        elif interpretations and len(interpretations) > 10:
            quality_score += 10
        
        assessment["quality_score"] = quality_score
        
        # 质量等级
        if quality_score >= 90:
            assessment["quality_level"] = "优秀"
        elif quality_score >= 80:
            assessment["quality_level"] = "良好"
        elif quality_score >= 70:
            assessment["quality_level"] = "中等"
        elif quality_score >= 60:
            assessment["quality_level"] = "及格"
        else:
            assessment["quality_level"] = "不合格"
        
        return assessment
    
    def assess_rules_batch(self, rules: List[Dict]) -> Dict:
        """批量评估规则质量"""
        print("🔍 开始批量质量评估...")
        
        assessments = []
        quality_stats = {
            "total_rules": len(rules),
            "quality_distribution": defaultdict(int),
            "issue_frequency": defaultdict(int),
            "strength_frequency": defaultdict(int),
            "metrics_summary": {
                "avg_quality_score": 0,
                "avg_confidence": 0,
                "avg_text_length": 0,
                "avg_text_clarity": 0,
                "structure_completeness_rate": 0
            }
        }
        
        total_score = 0
        total_confidence = 0
        total_text_length = 0
        total_text_clarity = 0
        total_structure_completeness = 0
        
        for i, rule in enumerate(rules):
            if i % 100 == 0:
                print(f"  评估进度: {i}/{len(rules)}")
            
            assessment = self.assess_single_rule(rule)
            assessments.append(assessment)
            
            # 统计质量分布
            quality_level = assessment["quality_level"]
            quality_stats["quality_distribution"][quality_level] += 1
            
            # 统计问题频率
            for issue in assessment["issues"]:
                quality_stats["issue_frequency"][issue] += 1
            
            # 统计优势频率
            for strength in assessment["strengths"]:
                quality_stats["strength_frequency"][strength] += 1
            
            # 累计指标
            total_score += assessment["quality_score"]
            total_confidence += assessment["metrics"].get("confidence", 0)
            total_text_length += assessment["metrics"].get("text_length", 0)
            total_text_clarity += assessment["metrics"].get("text_clarity", 0)
            total_structure_completeness += assessment["metrics"].get("structure_completeness", 0)
        
        # 计算平均值
        if len(rules) > 0:
            quality_stats["metrics_summary"]["avg_quality_score"] = total_score / len(rules)
            quality_stats["metrics_summary"]["avg_confidence"] = total_confidence / len(rules)
            quality_stats["metrics_summary"]["avg_text_length"] = total_text_length / len(rules)
            quality_stats["metrics_summary"]["avg_text_clarity"] = total_text_clarity / len(rules)
            quality_stats["metrics_summary"]["structure_completeness_rate"] = total_structure_completeness / len(rules)
        
        return {
            "assessments": assessments,
            "statistics": quality_stats
        }
    
    def compare_with_benchmark(self, statistics: Dict) -> Dict:
        """与基准标准对比"""
        metrics = statistics["metrics_summary"]
        
        comparison = {
            "文本清晰率": {
                "当前值": f"{metrics['avg_text_clarity']:.3f}",
                "基准值": f"{self.quality_benchmarks['文本清晰率']:.3f}",
                "达标": metrics['avg_text_clarity'] >= self.quality_benchmarks['文本清晰率']
            },
            "平均置信度": {
                "当前值": f"{metrics['avg_confidence']:.3f}",
                "基准值": f"{self.quality_benchmarks['置信度']:.3f}",
                "达标": metrics['avg_confidence'] >= self.quality_benchmarks['置信度']
            },
            "结构完整性": {
                "当前值": f"{metrics['structure_completeness_rate']:.3f}",
                "基准值": f"{self.quality_benchmarks['结构完整性']:.3f}",
                "达标": metrics['structure_completeness_rate'] >= self.quality_benchmarks['结构完整性']
            },
            "平均文本长度": {
                "当前值": f"{metrics['avg_text_length']:.1f}",
                "基准值": f"{self.quality_benchmarks['平均文本长度']:.1f}",
                "达标": metrics['avg_text_length'] >= self.quality_benchmarks['平均文本长度'] * 0.8
            }
        }
        
        # 计算总体达标率
        passed_count = sum(1 for item in comparison.values() if item["达标"])
        overall_pass_rate = passed_count / len(comparison)
        
        return {
            "detailed_comparison": comparison,
            "overall_pass_rate": overall_pass_rate,
            "passed_criteria": passed_count,
            "total_criteria": len(comparison)
        }
    
    def generate_quality_report(self, data: Dict, assessment_result: Dict, comparison: Dict) -> str:
        """生成质量评估报告"""
        rules = data["rules"]
        metadata = data["metadata"]
        statistics = assessment_result["statistics"]
        
        report = []
        report.append("=" * 80)
        report.append("规则质量评估报告")
        report.append("=" * 80)
        report.append(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"数据来源: {metadata.get('phase', '未知阶段')}")
        report.append(f"评估规则数: {len(rules)}")
        report.append("")
        
        # 质量分布
        report.append("📊 质量分布统计")
        report.append("-" * 50)
        total_rules = statistics["total_rules"]
        for level, count in statistics["quality_distribution"].items():
            percentage = (count / total_rules) * 100
            report.append(f"{level}: {count} ({percentage:.1f}%)")
        
        # 平均指标
        report.append(f"\n📈 平均质量指标")
        report.append("-" * 50)
        metrics = statistics["metrics_summary"]
        report.append(f"平均质量分数: {metrics['avg_quality_score']:.1f}/100")
        report.append(f"平均置信度: {metrics['avg_confidence']:.3f}")
        report.append(f"平均文本长度: {metrics['avg_text_length']:.1f} 字符")
        report.append(f"平均文本清晰率: {metrics['avg_text_clarity']:.3f}")
        report.append(f"结构完整性率: {metrics['structure_completeness_rate']:.3f}")
        
        # 与基准对比
        report.append(f"\n🎯 与基准标准对比")
        report.append("-" * 50)
        detailed_comparison = comparison["detailed_comparison"]
        for criterion, values in detailed_comparison.items():
            status = "✅" if values["达标"] else "❌"
            report.append(f"{status} {criterion}: {values['当前值']} (基准: {values['基准值']})")
        
        report.append(f"\n总体达标率: {comparison['overall_pass_rate']:.1%} ({comparison['passed_criteria']}/{comparison['total_criteria']})")
        
        # 常见问题
        report.append(f"\n⚠️ 常见质量问题 (前5项)")
        report.append("-" * 50)
        top_issues = sorted(statistics["issue_frequency"].items(), key=lambda x: x[1], reverse=True)[:5]
        for issue, count in top_issues:
            percentage = (count / total_rules) * 100
            report.append(f"{issue}: {count} ({percentage:.1f}%)")
        
        # 主要优势
        report.append(f"\n✅ 主要质量优势 (前5项)")
        report.append("-" * 50)
        top_strengths = sorted(statistics["strength_frequency"].items(), key=lambda x: x[1], reverse=True)[:5]
        for strength, count in top_strengths:
            percentage = (count / total_rules) * 100
            report.append(f"{strength}: {count} ({percentage:.1f}%)")
        
        # 改进建议
        report.append(f"\n💡 质量改进建议")
        report.append("-" * 50)
        
        if comparison["overall_pass_rate"] >= 0.8:
            report.append("✅ 整体质量良好，符合高质量标准")
        elif comparison["overall_pass_rate"] >= 0.6:
            report.append("⚠️ 质量基本合格，建议进一步优化")
        else:
            report.append("❌ 质量不达标，需要重新筛选和处理")
        
        # 具体建议
        if not detailed_comparison["平均置信度"]["达标"]:
            report.append("• 提高规则置信度，建议重新评估或筛选")
        
        if not detailed_comparison["文本清晰率"]["达标"]:
            report.append("• 改善文本清晰度，清理OCR错误和格式问题")
        
        if not detailed_comparison["结构完整性"]["达标"]:
            report.append("• 补充缺失字段，完善规则结构")
        
        if not detailed_comparison["平均文本长度"]["达标"]:
            report.append("• 增加文本内容，提供更详细的说明")
        
        return "\n".join(report)
    
    def execute_assessment(self, filename: str) -> Dict:
        """执行完整的质量评估"""
        print("🚀 启动规则质量评估...")
        
        # 1. 加载数据
        data = self.load_rules_data(filename)
        if not data["rules"]:
            return {"error": "无法加载规则数据"}
        
        # 2. 批量评估
        assessment_result = self.assess_rules_batch(data["rules"])
        
        # 3. 与基准对比
        comparison = self.compare_with_benchmark(assessment_result["statistics"])
        
        # 4. 生成报告
        report = self.generate_quality_report(data, assessment_result, comparison)
        
        return {
            "success": True,
            "data": data,
            "assessment": assessment_result,
            "comparison": comparison,
            "report": report
        }

def main():
    """主函数"""
    import sys
    
    # 默认评估第一阶段的结果
    filename = "classical_rules_phase1_foundation_theory.json"
    
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    
    assessor = RuleQualityAssessor()
    result = assessor.execute_assessment(filename)
    
    if result.get("success"):
        # 打印报告
        print(result["report"])
        
        # 保存详细评估结果
        assessment_filename = f"quality_assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(assessment_filename, 'w', encoding='utf-8') as f:
            # 只保存统计信息，不保存详细评估（文件太大）
            save_data = {
                "metadata": result["data"]["metadata"],
                "statistics": result["assessment"]["statistics"],
                "comparison": result["comparison"],
                "assessment_time": datetime.now().isoformat()
            }
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        # 保存报告
        report_filename = f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(result["report"])
        
        print(f"\n📊 详细评估已保存到: {assessment_filename}")
        print(f"📋 质量报告已保存到: {report_filename}")
        
        # 输出关键指标
        comparison = result["comparison"]
        print(f"\n🎯 关键质量指标:")
        print(f"总体达标率: {comparison['overall_pass_rate']:.1%}")
        print(f"达标项目: {comparison['passed_criteria']}/{comparison['total_criteria']}")
        
    else:
        print(f"❌ 质量评估失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
