#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def trace_tags(file_path):
    """逐行跟踪标签，找到缺失的结束标签"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 使用栈来跟踪标签
    view_stack = []
    max_depth = 0
    
    for line_num, line in enumerate(lines, 1):
        # 移除注释
        if '<!--' in line and '-->' in line:
            line = re.sub(r'<!--.*?-->', '', line)
        
        # 查找开始标签
        view_opens = re.findall(r'<view(?:\s[^>]*)?(?<!/)>', line)
        view_closes = re.findall(r'</view>', line)
        
        # 处理开始标签
        for _ in view_opens:
            view_stack.append(line_num)
        
        # 处理结束标签
        for _ in view_closes:
            if view_stack:
                view_stack.pop()
        
        # 记录最大深度
        if len(view_stack) > max_depth:
            max_depth = len(view_stack)
        
        # 如果栈深度很大，显示当前状态
        if len(view_stack) > 15:
            print(f"第{line_num}行，栈深度: {len(view_stack)}")
    
    print(f"🔍 最终分析结果:")
    print(f"  最大嵌套深度: {max_depth}")
    print(f"  未闭合的标签数量: {len(view_stack)}")
    
    if view_stack:
        print(f"  未闭合标签的开始行号: {view_stack}")
        
        # 显示最后几个未闭合的标签
        print(f"\n📋 最后10个未闭合标签:")
        for line_num in view_stack[-10:]:
            if line_num <= len(lines):
                print(f"  第{line_num}行: {lines[line_num-1].strip()}")

if __name__ == "__main__":
    trace_tags("pages/bazi-result/index.wxml")
