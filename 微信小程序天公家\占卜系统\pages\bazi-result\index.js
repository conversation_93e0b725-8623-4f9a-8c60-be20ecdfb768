// pages/bazi-result/index.js
// 八字排盘结果展示页面

const app = getApp();

Page({
  data: {
    // 排盘结果数据
    resultId: '',
    birthInfo: null,
    analysisMode: null,
    fourPillars: '',
    confidence: 0,
    basicInfo: {},
    analysisResult: null,
    
    // 界面状态
    loading: true,
    loadingDetail: false,
    showDetailModal: false,
    currentDetailType: '',
    currentDetailData: null,
    
    // 标签页
    activeTab: 0,
    tabs: [
      { key: 'basic', name: '基础信息', icon: '📊' },
      { key: 'professional', name: '专业分析', icon: '🔬' },
      { key: 'classical', name: '古籍理论', icon: '📚' },
      { key: 'comprehensive', name: '综合评价', icon: '🎯' }
    ],
    
    // 报告相关
    showReportModal: false,
    reportContent: '',
    reportFormat: 'text',

    // 数字化分析数据
    digitalAnalysis: {
      enabled: true,
      rulesCount: 1148,
      wuxingScores: {
        wood: 50,
        fire: 50,
        earth: 50,
        metal: 50,
        water: 50
      },
      balanceIndex: 50,
      initialized: false
    }
  },

  onLoad: function (options) {
    console.log('八字结果页面加载', options);
    
    const resultId = options.id;
    if (resultId) {
      this.setData({ resultId });
      this.loadBasicResult();
    } else {
      // 尝试从本地存储获取
      const cachedId = wx.getStorageSync('bazi_result_id');
      if (cachedId) {
        this.setData({ resultId: cachedId });
        this.loadBasicResult();
      } else {
        this.showError('未找到排盘结果');
      }
    }
  },

  onShow: function () {
    wx.setNavigationBarTitle({
      title: '八字排盘结果'
    });
  },

  // 加载基础结果
  loadBasicResult: function() {
    // 从本地存储获取基础信息
    const birthInfo = wx.getStorageSync('bazi_birth_info');
    const analysisMode = wx.getStorageSync('bazi_analysis_mode');
    
    if (birthInfo && analysisMode) {
      this.setData({
        birthInfo,
        analysisMode
      });
    }
    
    // 加载详细分析结果
    this.loadDetailedAnalysis();
  },

  // 加载详细分析结果
  loadDetailedAnalysis: function() {
    wx.showLoading({
      title: '加载分析结果...',
      mask: true
    });
    
    wx.request({
      url: `http://localhost:8000/api/bazi/analysis/${this.data.resultId}`,
      method: 'GET',
      success: (res) => {
        console.log('详细分析结果:', res);
        
        if (res.data && res.data.success) {
          const data = res.data.data;
          const analysisResult = data.analysis_result;
          
          // 提取基础信息
          let basicInfo = {};
          let fourPillars = '';
          
          if (analysisResult['基础分析'] && analysisResult['基础分析']['基本信息']) {
            basicInfo = analysisResult['基础分析']['基本信息'];
            fourPillars = basicInfo['四柱'] || '';
          }
          
          this.setData({
            analysisResult,
            basicInfo,
            fourPillars,
            confidence: data.confidence,
            loading: false
          });
          
          // 计算数字化分析
          this.calculateDigitalAnalysis(analysisResult);

          // 根据分析结果调整标签页显示
          this.updateTabsVisibility();
          
        } else {
          this.showError(res.data?.error || '加载分析结果失败');
        }
      },
      fail: (err) => {
        console.error('加载分析结果失败:', err);
        this.showError('网络连接失败，请检查API服务');
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 更新标签页显示
  updateTabsVisibility: function() {
    const analysisResult = this.data.analysisResult;
    if (!analysisResult) return;
    
    const tabs = this.data.tabs.map(tab => {
      let visible = true;
      switch (tab.key) {
        case 'basic':
          visible = !!analysisResult['基础分析'];
          break;
        case 'professional':
          visible = !!analysisResult['专业分析'];
          break;
        case 'classical':
          visible = !!analysisResult['古籍分析'];
          break;
        case 'comprehensive':
          visible = !!analysisResult['综合评价'];
          break;
      }
      return { ...tab, visible };
    });
    
    this.setData({ tabs });
  },

  // 切换标签页
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ activeTab: index });
  },

  // 显示详细信息
  showDetail: function(e) {
    const type = e.currentTarget.dataset.type;
    const analysisResult = this.data.analysisResult;
    
    let detailData = null;
    let title = '';
    
    switch (type) {
      case 'wuxing':
        detailData = analysisResult['基础分析']?.['五行分析'];
        title = '五行分析详情';
        break;
      case 'shishen':
        detailData = analysisResult['基础分析']?.['十神分析'];
        title = '十神分析详情';
        break;
      case 'professional':
        detailData = analysisResult['专业分析'];
        title = '专业分析详情';
        break;
      case 'classical':
        detailData = analysisResult['古籍分析'];
        title = '古籍分析详情';
        break;
      case 'comprehensive':
        detailData = analysisResult['综合评价'];
        title = '综合评价详情';
        break;
    }
    
    if (detailData) {
      this.setData({
        showDetailModal: true,
        currentDetailType: title,
        currentDetailData: detailData
      });
    }
  },

  // 关闭详情弹窗
  closeDetailModal: function() {
    this.setData({
      showDetailModal: false,
      currentDetailType: '',
      currentDetailData: null
    });
  },

  // 导出报告
  exportReport: function() {
    this.setData({ loadingDetail: true });
    
    wx.showLoading({
      title: '生成报告...',
      mask: true
    });
    
    wx.request({
      url: `http://localhost:8000/api/bazi/report/${this.data.resultId}?format=text`,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          this.setData({
            showReportModal: true,
            reportContent: res.data.data.content,
            reportFormat: res.data.data.format
          });
        } else {
          wx.showToast({
            title: res.data?.error || '报告生成失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('报告生成失败:', err);
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
        this.setData({ loadingDetail: false });
      }
    });
  },

  // 关闭报告弹窗
  closeReportModal: function() {
    this.setData({
      showReportModal: false,
      reportContent: '',
      reportFormat: 'text'
    });
  },

  // 复制报告内容
  copyReport: function() {
    wx.setClipboardData({
      data: this.data.reportContent,
      success: () => {
        wx.showToast({
          title: '报告已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 分享结果
  shareResult: function() {
    const birthInfo = this.data.birthInfo;
    const fourPillars = this.data.fourPillars;
    
    if (!birthInfo || !fourPillars) {
      wx.showToast({
        title: '分享信息不完整',
        icon: 'none'
      });
      return;
    }
    
    const shareText = `我的八字排盘结果：\n` +
                     `出生：${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日 ${birthInfo.hour}:${birthInfo.minute.toString().padStart(2, '0')}\n` +
                     `四柱：${fourPillars}\n` +
                     `来自玉匣记八字排盘系统`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '分享内容已复制',
          icon: 'success'
        });
      }
    });
  },

  // 重新排盘
  newPaipan: function() {
    wx.navigateTo({
      url: '/pages/bazi-input/index'
    });
  },

  // 返回首页
  goHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 显示错误信息
  showError: function(message) {
    this.setData({ loading: false });
    wx.showModal({
      title: '加载失败',
      content: message,
      showCancel: true,
      cancelText: '返回',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.loadDetailedAnalysis();
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 格式化对象为可读文本
  formatObjectToText: function(obj, indent = 0) {
    if (!obj) return '';
    
    const spaces = '  '.repeat(indent);
    let result = '';
    
    if (typeof obj === 'object' && obj !== null) {
      if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          result += `${spaces}${index + 1}. ${this.formatObjectToText(item, indent)}\n`;
        });
      } else {
        Object.keys(obj).forEach(key => {
          const value = obj[key];
          if (typeof value === 'object' && value !== null) {
            result += `${spaces}${key}:\n${this.formatObjectToText(value, indent + 1)}`;
          } else {
            result += `${spaces}${key}: ${value}\n`;
          }
        });
      }
    } else {
      result = obj.toString();
    }
    
    return result;
  },

  // 计算数字化分析
  calculateDigitalAnalysis: function(analysisResult) {
    console.log('🔢 开始计算数字化分析...');

    try {
      // 从五行分析中提取数据
      const wuxingAnalysis = analysisResult['基础分析']?.['五行分析'];
      if (!wuxingAnalysis) {
        console.warn('⚠️ 未找到五行分析数据，使用默认值');
        return;
      }

      // 计算五行分数
      const wuxingScores = this.calculateWuxingScores(wuxingAnalysis);

      // 计算平衡指数
      const balanceIndex = this.calculateBalanceIndex(wuxingScores);

      // 更新数字化分析数据
      this.setData({
        'digitalAnalysis.wuxingScores': wuxingScores,
        'digitalAnalysis.balanceIndex': balanceIndex,
        'digitalAnalysis.initialized': true
      });

      console.log('✅ 数字化分析计算完成:', { wuxingScores, balanceIndex });

    } catch (error) {
      console.error('❌ 数字化分析计算失败:', error);
    }
  },

  // 计算五行分数
  calculateWuxingScores: function(wuxingAnalysis) {
    const wuxingStats = wuxingAnalysis['五行统计'] || {};
    const wuxingScores = { wood: 0, fire: 0, earth: 0, metal: 0, water: 0 };

    // 五行映射
    const elementMap = {
      '木': 'wood',
      '火': 'fire',
      '土': 'earth',
      '金': 'metal',
      '水': 'water'
    };

    // 计算总数
    const totalCount = Object.values(wuxingStats).reduce((sum, count) => sum + count, 0);

    // 转换为分数 (0-100)
    Object.keys(elementMap).forEach(chineseElement => {
      const englishElement = elementMap[chineseElement];
      const count = wuxingStats[chineseElement] || 0;

      // 基础分数：根据数量计算
      let score = totalCount > 0 ? (count / totalCount) * 100 : 20;

      // 调整分数范围，避免极端值
      score = Math.max(10, Math.min(90, score));

      wuxingScores[englishElement] = Math.round(score);
    });

    return wuxingScores;
  },

  // 计算平衡指数
  calculateBalanceIndex: function(wuxingScores) {
    const scores = Object.values(wuxingScores);
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // 计算标准差
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);

    // 转换为平衡指数 (标准差越小，平衡度越高)
    const balanceIndex = Math.max(0, Math.min(100, 100 - (standardDeviation / 30) * 100));

    return Math.round(balanceIndex);
  },

  // 处理数字化分析分享事件
  onShareDigitalAnalysis: function(e) {
    const { balanceIndex, balanceStatus, type } = e.detail;

    const shareText = `我的五行平衡指数：${balanceIndex}分 (${balanceStatus})
来自天公家专业八字分析系统`;

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 设置分享内容
    this.shareContent = {
      title: '我的五行数字化分析',
      desc: shareText,
      path: '/pages/bazi-result/index'
    };

    wx.showToast({
      title: '点击右上角分享',
      icon: 'none'
    });
  }
});
