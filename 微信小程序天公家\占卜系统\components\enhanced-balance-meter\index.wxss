/* components/enhanced-balance-meter/index.wxss */
/* 增强的五行平衡指标组件样式 */

.enhanced-balance-meter {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 头部区域 */
.balance-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.header-text {
  flex: 1;
}

.balance-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.balance-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 主要指标区域 */
.balance-main {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.balance-score-container {
  display: flex;
  align-items: baseline;
  margin-right: 30rpx;
}

.balance-score {
  font-size: 72rpx;
  font-weight: 700;
  color: #2196F3;
  line-height: 1;
}

.balance-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.balance-status {
  flex: 1;
}

.status-text {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 进度条区域 */
.balance-progress {
  position: relative;
  margin-bottom: 30rpx;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.8s ease;
}

.progress-thumb {
  position: absolute;
  top: -6rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: left 0.8s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 刻度标记 */
.progress-marks {
  position: relative;
  margin-top: 20rpx;
  height: 60rpx;
}

.mark {
  position: absolute;
  transform: translateX(-50%);
  text-align: center;
}

.mark-label {
  display: block;
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.mark-desc {
  display: block;
  font-size: 18rpx;
  color: #ccc;
}

/* 详细分析区域 */
.balance-details {
  border-top: 1rpx solid #eee;
  padding-top: 30rpx;
  margin-bottom: 30rpx;
}

.details-header {
  margin-bottom: 20rpx;
}

.details-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 偏差分析 */
.deviation-analysis {
  margin-bottom: 30rpx;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.analysis-item:last-child {
  border-bottom: none;
}

.analysis-label {
  font-size: 26rpx;
  color: #666;
}

.analysis-value {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.element-name {
  font-size: 26rpx;
  font-weight: 500;
}

.element-score,
.deviation-value {
  font-size: 24rpx;
  color: #666;
}

.deviation-level {
  font-size: 22rpx;
  color: #999;
}

/* 改善建议 */
.improvement-suggestions {
  margin-bottom: 20rpx;
}

.suggestions-header {
  margin-bottom: 16rpx;
}

.suggestions-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.suggestion-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 操作按钮 */
.balance-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  background: #2196F3;
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn:active {
  opacity: 0.8;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .balance-main {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .balance-score-container {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
  
  .balance-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
}

/* 动画效果 */
.enhanced-balance-meter {
  transition: all 0.3s ease;
}

.suggestion-item {
  transition: background-color 0.2s ease;
}

.suggestion-item:active {
  background: #e9ecef;
}

/* 无障碍支持 */
.action-btn:focus {
  outline: 2rpx solid #2196F3;
  outline-offset: 2rpx;
}
