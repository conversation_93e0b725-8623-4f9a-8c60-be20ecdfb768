/**
 * 《三命通会》权威神煞计算系统
 * 基于古籍《三命通会》卷三神煞内容，提供权威的神煞计算方法
 */

// 测试数据：2021年6月24日 19:30 北京时间
const TEST_BAZI = {
  year: { gan: '辛', zhi: '丑' },
  month: { gan: '甲', zhi: '午' },
  day: { gan: '癸', zhi: '卯' },
  hour: { gan: '壬', zhi: '戌' }
};

// "问真八字"标准结果
const WENZHEN_STANDARD = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

/**
 * 1. 天乙贵人（《三命通会》论天乙贵人）
 * 口诀：甲戊庚牛羊，乙己鼠猴乡，丙丁豬雞位，壬癸兔蛇藏，六辛逢虎馬，此是貴人方
 */
function calculateSanmingTianyiGuiren(dayGan, fourPillars) {
  const tianyiMap = {
    '甲': ['丑', '未'], '戊': ['丑', '未'], '庚': ['丑', '未'],
    '乙': ['子', '申'], '己': ['子', '申'],
    '丙': ['亥', '酉'], '丁': ['亥', '酉'],
    '壬': ['卯', '巳'], '癸': ['卯', '巳'],
    '辛': ['寅', '午']
  };
  
  const nobles = tianyiMap[dayGan] || [];
  const result = [];
  
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    if (nobles.includes(fourPillars[pillar].zhi)) {
      result.push({ pillar, shensha: '天乙贵人' });
    }
  });
  
  return result;
}

/**
 * 2. 德秀贵人（《三命通会》论德秀）
 * 口诀：寅午戌月，丙丁为德，戊癸为秀；申子辰月，壬癸戊己为德，丙辛甲己为秀；
 *       巳酉丑月，庚辛为德，乙庚为秀；亥卯未月，甲乙为德，丁壬为秀
 */
function calculateSanmingDexiuGuiren(monthZhi, fourPillars) {
  const dexiuMap = {
    // 寅午戌月
    '寅': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
    '午': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
    '戌': { de: ['丙', '丁'], xiu: ['戊', '癸'] },
    
    // 申子辰月
    '申': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
    '子': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
    '辰': { de: ['壬', '癸', '戊', '己'], xiu: ['丙', '辛', '甲', '己'] },
    
    // 巳酉丑月
    '巳': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
    '酉': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
    '丑': { de: ['庚', '辛'], xiu: ['乙', '庚'] },
    
    // 亥卯未月
    '亥': { de: ['甲', '乙'], xiu: ['丁', '壬'] },
    '卯': { de: ['甲', '乙'], xiu: ['丁', '壬'] },
    '未': { de: ['甲', '乙'], xiu: ['丁', '壬'] }
  };
  
  const config = dexiuMap[monthZhi];
  if (!config) return [];
  
  const result = [];
  
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    const gan = fourPillars[pillar].gan;
    if (config.de.includes(gan)) {
      result.push({ pillar, shensha: '德秀贵人' });
    }
    if (config.xiu.includes(gan)) {
      result.push({ pillar, shensha: '德秀贵人' });
    }
  });
  
  return result;
}

/**
 * 3. 元辰（《三命通会》论元辰）
 * 阳男阴女：冲前一位；阴男阳女：冲后一位
 */
function calculateSanmingYuanchen(yearZhi, gender, fourPillars) {
  // 地支对冲表
  const chongMap = {
    '子': '午', '午': '子',
    '丑': '未', '未': '丑',
    '寅': '申', '申': '寅',
    '卯': '酉', '酉': '卯',
    '辰': '戌', '戌': '辰',
    '巳': '亥', '亥': '巳'
  };
  
  // 地支顺序
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  // 阳支：子寅辰午申戌，阴支：丑卯巳未酉亥
  const yangZhi = ['子', '寅', '辰', '午', '申', '戌'];
  const isYangYear = yangZhi.includes(yearZhi);
  
  const chongZhi = chongMap[yearZhi];
  const chongIndex = zhiOrder.indexOf(chongZhi);
  
  let yuanchenZhi;
  
  if ((gender === 'male' && isYangYear) || (gender === 'female' && !isYangYear)) {
    // 阳男阴女：冲前一位
    yuanchenZhi = zhiOrder[(chongIndex + 1) % 12];
  } else {
    // 阴男阳女：冲后一位
    yuanchenZhi = zhiOrder[(chongIndex - 1 + 12) % 12];
  }
  
  const result = [];
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    if (fourPillars[pillar].zhi === yuanchenZhi) {
      result.push({ pillar, shensha: '元辰' });
    }
  });
  
  return result;
}

/**
 * 4. 咸池桃花（《三命通会》论咸池）
 * 申子辰见酉，寅午戌见卯，巳酉丑见午，亥卯未见子
 */
function calculateSanmingXianchi(yearZhi, fourPillars) {
  const xianchiMap = {
    '申': '酉', '子': '酉', '辰': '酉',
    '寅': '卯', '午': '卯', '戌': '卯',
    '巳': '午', '酉': '午', '丑': '午',
    '亥': '子', '卯': '子', '未': '子'
  };
  
  const taohuaZhi = xianchiMap[yearZhi];
  if (!taohuaZhi) return [];
  
  const result = [];
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    if (fourPillars[pillar].zhi === taohuaZhi) {
      result.push({ pillar, shensha: '桃花' });
    }
  });
  
  return result;
}

/**
 * 5. 孤辰寡宿（《三命通会》论孤辰寡宿）
 * 亥子丑人，见寅为孤，见戌为寡；寅卯辰人，见巳为孤，见丑为寡；
 * 巳午未人，见申为孤，见辰为寡；申酉戌人，见亥为孤，见未为寡
 */
function calculateSanmingGuasuGuchen(yearZhi, fourPillars) {
  const guasuMap = {
    '亥': { guchen: '寅', guasu: '戌' },
    '子': { guchen: '寅', guasu: '戌' },
    '丑': { guchen: '寅', guasu: '戌' },
    
    '寅': { guchen: '巳', guasu: '丑' },
    '卯': { guchen: '巳', guasu: '丑' },
    '辰': { guchen: '巳', guasu: '丑' },
    
    '巳': { guchen: '申', guasu: '辰' },
    '午': { guchen: '申', guasu: '辰' },
    '未': { guchen: '申', guasu: '辰' },
    
    '申': { guchen: '亥', guasu: '未' },
    '酉': { guchen: '亥', guasu: '未' },
    '戌': { guchen: '亥', guasu: '未' }
  };
  
  const config = guasuMap[yearZhi];
  if (!config) return [];
  
  const result = [];
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    const zhi = fourPillars[pillar].zhi;
    if (zhi === config.guchen) {
      result.push({ pillar, shensha: '孤辰' });
    }
    if (zhi === config.guasu) {
      result.push({ pillar, shensha: '寡宿' });
    }
  });
  
  return result;
}

/**
 * 综合《三命通会》神煞计算
 */
function calculateSanmingShenshaSystem(fourPillars, gender = 'male') {
  const results = [];
  
  // 1. 天乙贵人
  results.push(...calculateSanmingTianyiGuiren(fourPillars.day.gan, fourPillars));
  
  // 2. 德秀贵人
  results.push(...calculateSanmingDexiuGuiren(fourPillars.month.zhi, fourPillars));
  
  // 3. 元辰
  results.push(...calculateSanmingYuanchen(fourPillars.year.zhi, gender, fourPillars));
  
  // 4. 咸池桃花
  results.push(...calculateSanmingXianchi(fourPillars.year.zhi, fourPillars));
  
  // 5. 孤辰寡宿
  results.push(...calculateSanmingGuasuGuchen(fourPillars.year.zhi, fourPillars));
  
  return results;
}

// 测试《三命通会》神煞系统
console.log('=== 《三命通会》权威神煞计算系统 ===');
console.log('');

console.log('📊 测试数据：');
console.log(`年柱：${TEST_BAZI.year.gan}${TEST_BAZI.year.zhi}`);
console.log(`月柱：${TEST_BAZI.month.gan}${TEST_BAZI.month.zhi}`);
console.log(`日柱：${TEST_BAZI.day.gan}${TEST_BAZI.day.zhi}`);
console.log(`时柱：${TEST_BAZI.hour.gan}${TEST_BAZI.hour.zhi}`);
console.log('');

const sanmingResults = calculateSanmingShenshaSystem(TEST_BAZI, 'male');

console.log('🔮 《三命通会》神煞计算结果：');
const groupedResults = {};
sanmingResults.forEach(result => {
  if (!groupedResults[result.pillar]) {
    groupedResults[result.pillar] = [];
  }
  groupedResults[result.pillar].push(result.shensha);
});

['year', 'month', 'day', 'hour'].forEach(pillar => {
  const pillarName = { year: '年柱', month: '月柱', day: '日柱', hour: '时柱' }[pillar];
  const shenshas = groupedResults[pillar] || [];
  console.log(`${pillarName}：${shenshas.length > 0 ? shenshas.join('、') : '无'}`);
});

console.log('');
console.log('📋 与"问真八字"标准对比：');
['year', 'month', 'day', 'hour'].forEach(pillar => {
  const pillarName = { year: '年柱', month: '月柱', day: '日柱', hour: '时柱' }[pillar];
  const ourResults = groupedResults[pillar] || [];
  const standard = WENZHEN_STANDARD[pillar] || [];
  
  const matches = ourResults.filter(s => standard.includes(s));
  const missing = standard.filter(s => !ourResults.includes(s));
  
  console.log(`${pillarName}：`);
  console.log(`  ✅ 匹配：${matches.length > 0 ? matches.join('、') : '无'}`);
  console.log(`  ❌ 缺失：${missing.length > 0 ? missing.join('、') : '无'}`);
});

console.log('');
console.log('📈 准确率统计：');
const totalStandard = Object.values(WENZHEN_STANDARD).flat().length;
const totalMatches = Object.keys(groupedResults).reduce((sum, pillar) => {
  const ourResults = groupedResults[pillar] || [];
  const standard = WENZHEN_STANDARD[pillar] || [];
  return sum + ourResults.filter(s => standard.includes(s)).length;
}, 0);

console.log(`总标准神煞数：${totalStandard}`);
console.log(`成功匹配数：${totalMatches}`);
console.log(`准确率：${((totalMatches / totalStandard) * 100).toFixed(1)}%`);

console.log('');
console.log('🎯 《三命通会》系统优势：');
console.log('1. ✅ 天乙贵人：基于权威古籍，计算准确');
console.log('2. ✅ 德秀贵人：完整的月令对应表，覆盖全年');
console.log('3. ✅ 元辰：精确的阴阳男女计算规则');
console.log('4. ✅ 咸池桃花：传统三合局桃花计算');
console.log('5. ✅ 孤辰寡宿：完整的四季对应关系');

console.log('');
console.log('📚 需要补充的神煞（在其他古籍中查找）：');
const allMissing = Object.values(WENZHEN_STANDARD).flat().filter(s => 
  !Object.values(groupedResults).flat().includes(s)
);
allMissing.forEach(shensha => {
  console.log(`  - ${shensha}：需要在《千里命稿》或其他古籍中查找`);
});
