// true_solar_time_test.js
// 真太阳时修正系统测试套件

const TrueSolarTimeCorrector = require('./utils/true_solar_time_corrector.js');

/**
 * 真太阳时修正系统测试套件
 */
class TrueSolarTimeTestSuite {
  constructor() {
    this.corrector = new TrueSolarTimeCorrector();
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
  }
  
  /**
   * 运行单个测试
   */
  runTest(testName, testFunction) {
    this.totalTests++;
    console.log(`\n🧪 测试: ${testName}`);
    
    try {
      const result = testFunction();
      if (result) {
        console.log(`✅ 通过: ${testName}`);
        this.passedTests++;
        this.testResults.push({ name: testName, status: 'PASS', error: null });
      } else {
        console.log(`❌ 失败: ${testName}`);
        this.testResults.push({ name: testName, status: 'FAIL', error: 'Test returned false' });
      }
    } catch (error) {
      console.log(`❌ 错误: ${testName} - ${error.message}`);
      this.testResults.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }
  
  /**
   * 测试修正器初始化
   */
  testCorrectorInitialization() {
    return this.runTest('修正器初始化', () => {
      const status = this.corrector.getCorrectorStatus();
      console.log('修正器状态:', status);
      return status.name === 'TrueSolarTimeCorrector' && status.status === 'ready';
    });
  }
  
  /**
   * 测试基础经度修正
   */
  testBasicLongitudeCorrection() {
    return this.runTest('基础经度修正', () => {
      // 测试北京时间 2021-06-24 19:30
      const beijingTime = new Date(2021, 5, 24, 19, 30, 0);
      
      // 测试不同经度的修正
      const testCases = [
        { longitude: 120, expectedOffset: 0 }, // 标准时区，无修正
        { longitude: 116.4074, expectedOffset: -14.4 }, // 北京，西退14.4分钟
        { longitude: 126, expectedOffset: 24 }, // 东部，东进24分钟
        { longitude: 105, expectedOffset: -60 } // 西部，西退60分钟
      ];
      
      let allPassed = true;
      
      for (const testCase of testCases) {
        const result = this.corrector.calculateTrueSolarTime(beijingTime, testCase.longitude);
        const actualOffset = result.calculation.timeOffsetMinutes;
        
        console.log(`经度${testCase.longitude}°: 预期偏移${testCase.expectedOffset}分钟, 实际偏移${actualOffset.toFixed(1)}分钟`);
        
        if (Math.abs(actualOffset - testCase.expectedOffset) > 0.1) {
          allPassed = false;
          console.log(`❌ 偏移计算错误`);
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试城市快速查询
   */
  testCityQuickLookup() {
    return this.runTest('城市快速查询', () => {
      const beijingTime = new Date(2021, 6, 1, 12, 0, 0);
      
      // 测试主要城市
      const testCities = ['北京', '上海', '广州', '成都', '乌鲁木齐'];
      let allPassed = true;
      
      for (const city of testCities) {
        try {
          const result = this.corrector.calculateTrueSolarTimeByCity(beijingTime, city);
          console.log(`${city}: 经度${result.input.cityLongitude}°, 修正${result.calculation.timeOffsetMinutes.toFixed(1)}分钟`);
          
          if (!result.input.cityName || !result.input.cityLongitude) {
            allPassed = false;
          }
        } catch (error) {
          console.log(`❌ ${city}查询失败: ${error.message}`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试时辰判断
   */
  testTimeZhiDetermination() {
    return this.runTest('时辰判断', () => {
      // 测试不同时间的时辰判断
      const testCases = [
        { hour: 0, minute: 30, expectedZhi: '子' },
        { hour: 2, minute: 0, expectedZhi: '丑' },
        { hour: 6, minute: 0, expectedZhi: '卯' },
        { hour: 12, minute: 0, expectedZhi: '午' },
        { hour: 18, minute: 0, expectedZhi: '酉' },
        { hour: 23, minute: 30, expectedZhi: '子' }
      ];
      
      let allPassed = true;
      
      for (const testCase of testCases) {
        const testTime = new Date(2021, 6, 1, testCase.hour, testCase.minute, 0);
        const zhiInfo = this.corrector.determineTimeZhi(testTime);
        
        console.log(`${testCase.hour}:${testCase.minute.toString().padStart(2, '0')} → ${zhiInfo.zhi}时 (预期: ${testCase.expectedZhi}时)`);
        
        if (zhiInfo.zhi !== testCase.expectedZhi) {
          allPassed = false;
          console.log(`❌ 时辰判断错误`);
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试完整修正流程
   */
  testCompleteCorrection() {
    return this.runTest('完整修正流程', () => {
      // 测试可能导致时辰变化的情况
      const beijingTime = new Date(2021, 6, 1, 16, 50, 0); // 16:50，接近申酉时交界
      
      // 测试乌鲁木齐（经度差较大）
      const result = this.corrector.correctTimeZhi(beijingTime, '乌鲁木齐');
      
      console.log('原始时间:', result.input.beijingTimeString);
      console.log('修正时间:', result.result.trueSolarTimeString);
      console.log('原始时辰:', result.timeZhi.original.zhi);
      console.log('修正时辰:', result.timeZhi.corrected.zhi);
      console.log('时辰变化:', result.timeZhi.changeDescription);
      
      // 验证结果结构完整性
      return result.input && result.calculation && result.result && result.timeZhi;
    });
  }
  
  /**
   * 测试边界条件
   */
  testBoundaryConditions() {
    return this.runTest('边界条件测试', () => {
      const beijingTime = new Date(2021, 6, 1, 12, 0, 0);
      
      // 测试极端经度
      const extremeCases = [
        { longitude: -180, description: '西经180°' },
        { longitude: 180, description: '东经180°' },
        { longitude: 0, description: '本初子午线' },
        { longitude: 120, description: '标准时区' }
      ];
      
      let allPassed = true;
      
      for (const testCase of extremeCases) {
        try {
          const result = this.corrector.calculateTrueSolarTime(beijingTime, testCase.longitude);
          console.log(`${testCase.description}: 修正${result.calculation.timeOffsetMinutes.toFixed(1)}分钟`);
        } catch (error) {
          console.log(`❌ ${testCase.description}测试失败: ${error.message}`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试错误处理
   */
  testErrorHandling() {
    return this.runTest('错误处理测试', () => {
      const beijingTime = new Date(2021, 6, 1, 12, 0, 0);
      let errorsCaught = 0;
      
      // 测试无效经度
      try {
        this.corrector.calculateTrueSolarTime(beijingTime, 200);
      } catch (error) {
        console.log('正确捕获无效经度错误:', error.message);
        errorsCaught++;
      }
      
      // 测试无效城市
      try {
        this.corrector.calculateTrueSolarTimeByCity(beijingTime, '不存在的城市');
      } catch (error) {
        console.log('正确捕获无效城市错误:', error.message);
        errorsCaught++;
      }
      
      // 测试无效参数
      try {
        this.corrector.calculateTrueSolarTime(null, 120);
      } catch (error) {
        console.log('正确捕获无效参数错误:', error.message);
        errorsCaught++;
      }
      
      return errorsCaught === 3;
    });
  }
  
  /**
   * 测试性能
   */
  testPerformance() {
    return this.runTest('性能测试', () => {
      const beijingTime = new Date(2021, 6, 1, 12, 0, 0);
      const startTime = Date.now();
      
      // 执行1000次修正计算
      for (let i = 0; i < 1000; i++) {
        const longitude = 90 + Math.random() * 60; // 90-150度范围
        this.corrector.calculateTrueSolarTime(beijingTime, longitude);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`1000次计算耗时: ${duration}ms`);
      console.log(`平均每次: ${(duration / 1000).toFixed(2)}ms`);
      
      // 性能要求：平均每次计算不超过10ms
      return (duration / 1000) < 10;
    });
  }
  
  /**
   * 测试实际应用场景
   */
  testRealWorldScenarios() {
    return this.runTest('实际应用场景', () => {
      // 模拟真实的八字计算场景
      const scenarios = [
        {
          name: '北京出生',
          time: new Date(2021, 5, 24, 19, 30, 0),
          location: '北京',
          expectedSignificant: false // 北京修正不大
        },
        {
          name: '乌鲁木齐出生',
          time: new Date(2021, 5, 24, 16, 45, 0), // 接近时辰边界
          location: '乌鲁木齐',
          expectedSignificant: true // 乌鲁木齐修正较大
        },
        {
          name: '哈尔滨出生',
          time: new Date(2021, 5, 24, 17, 15, 0),
          location: '哈尔滨',
          expectedSignificant: false // 哈尔滨修正中等
        }
      ];
      
      let allPassed = true;
      
      for (const scenario of scenarios) {
        const result = this.corrector.correctTimeZhi(scenario.time, scenario.location);
        const isSignificant = Math.abs(result.calculation.timeOffsetMinutes) > 30;
        
        console.log(`\n${scenario.name}:`);
        console.log(`  原始: ${result.input.beijingTimeString} (${result.timeZhi.original.zhi}时)`);
        console.log(`  修正: ${result.result.trueSolarTimeString} (${result.timeZhi.corrected.zhi}时)`);
        console.log(`  偏移: ${result.calculation.timeOffsetMinutes.toFixed(1)}分钟`);
        console.log(`  ${result.timeZhi.changeDescription}`);
        
        if (isSignificant !== scenario.expectedSignificant) {
          console.log(`❌ 修正重要性判断与预期不符`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🌅 真太阳时修正系统测试套件');
    console.log('='.repeat(60));
    
    // 执行所有测试
    this.testCorrectorInitialization();
    this.testBasicLongitudeCorrection();
    this.testCityQuickLookup();
    this.testTimeZhiDetermination();
    this.testCompleteCorrection();
    this.testBoundaryConditions();
    this.testErrorHandling();
    this.testPerformance();
    this.testRealWorldScenarios();
    
    // 生成测试报告
    this.generateReport();
  }
  
  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.totalTests - this.passedTests}`);
    console.log(`成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
    
    // 详细结果
    console.log('\n📋 详细结果:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    // 评级
    const successRate = (this.passedTests / this.totalTests) * 100;
    let grade = '';
    if (successRate >= 95) grade = '🏆 优秀';
    else if (successRate >= 85) grade = '🌟 良好';
    else if (successRate >= 70) grade = '⚠️ 及格';
    else grade = '❌ 不及格';
    
    console.log(`\n🎯 测试评级: ${grade}`);
    console.log('='.repeat(60));
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new TrueSolarTimeTestSuite();
  testSuite.runAllTests();
}

module.exports = TrueSolarTimeTestSuite;
