// utils/enhanced_pattern_analyzer.js
// 精确的格局判定算法模块
// 基于《命理格局，用神.txt》文档要求实现

/**
 * 增强版格局分析器
 * 实现月令藏干动态调整、清浊评估公式、特殊格局阈值判断等核心算法
 */
class EnhancedPatternAnalyzer {
  constructor() {
    this.initializeData();
  }

  /**
   * 初始化基础数据
   */
  initializeData() {
    // 特殊格局判定阈值配置
    this.specialPatternThresholds = {
      从格: {
        minimum_day_weakness: 0.1,     // 日主力量<10%（技术文档要求）
        minimum_drain_power: 0.6,      // 克泄五行>60%（技术文档要求）
        maximum_resistance: 0.15       // 阻力≤15%
      },
      专旺格: {
        single_element_dominance: 0.8, // 单一五行≥80%
        seasonal_alignment: true,      // 必须符合季节特征
        no_strong_opposition: 0.1      // 对立力量≤10%
      },
      化气格: {
        transformation_strength: 0.6,  // 化气力量≥60%
        original_weakness: 0.3,        // 原五行≤30%
        environmental_support: 0.5     // 环境支持≥50%
      }
    };

    // 特殊格局配置
    this.specialPatterns = {
      从格系列: ['从财格', '从官格', '从儿格', '从势格'],
      专旺格系列: ['曲直格', '炎上格', '稼穑格', '从革格', '润下格'],
      化气格系列: ['甲己化土格', '乙庚化金格', '丙辛化水格', '丁壬化木格', '戊癸化火格']
    };

    // 月令藏干数据（基于节气深度动态调整）
    this.monthHiddenGans = {
      '寅': {
        early: [{ gan: '甲', strength: 0.6 }, { gan: '丙', strength: 0.3 }, { gan: '戊', strength: 0.1 }],
        middle: [{ gan: '甲', strength: 0.7 }, { gan: '丙', strength: 0.2 }, { gan: '戊', strength: 0.1 }],
        late: [{ gan: '甲', strength: 0.8 }, { gan: '丙', strength: 0.15 }, { gan: '戊', strength: 0.05 }]
      },
      '卯': {
        early: [{ gan: '乙', strength: 1.0 }],
        middle: [{ gan: '乙', strength: 1.0 }],
        late: [{ gan: '乙', strength: 1.0 }]
      },
      '辰': {
        early: [{ gan: '乙', strength: 0.3 }, { gan: '戊', strength: 0.6 }, { gan: '癸', strength: 0.1 }],
        middle: [{ gan: '戊', strength: 0.7 }, { gan: '乙', strength: 0.2 }, { gan: '癸', strength: 0.1 }],
        late: [{ gan: '戊', strength: 0.8 }, { gan: '癸', strength: 0.15 }, { gan: '乙', strength: 0.05 }]
      },
      '巳': {
        early: [{ gan: '丙', strength: 0.7 }, { gan: '庚', strength: 0.2 }, { gan: '戊', strength: 0.1 }],
        middle: [{ gan: '丙', strength: 0.8 }, { gan: '戊', strength: 0.15 }, { gan: '庚', strength: 0.05 }],
        late: [{ gan: '丙', strength: 0.6 }, { gan: '戊', strength: 0.3 }, { gan: '庚', strength: 0.1 }]
      },
      '午': {
        early: [{ gan: '丁', strength: 0.7 }, { gan: '己', strength: 0.3 }],
        middle: [{ gan: '丁', strength: 1.0 }],
        late: [{ gan: '丁', strength: 0.8 }, { gan: '己', strength: 0.2 }]
      },
      '未': {
        early: [{ gan: '己', strength: 0.6 }, { gan: '丁', strength: 0.3 }, { gan: '乙', strength: 0.1 }],
        middle: [{ gan: '己', strength: 0.7 }, { gan: '乙', strength: 0.2 }, { gan: '丁', strength: 0.1 }],
        late: [{ gan: '己', strength: 0.8 }, { gan: '乙', strength: 0.15 }, { gan: '丁', strength: 0.05 }]
      },
      '申': {
        early: [{ gan: '庚', strength: 0.7 }, { gan: '壬', strength: 0.2 }, { gan: '戊', strength: 0.1 }],
        middle: [{ gan: '庚', strength: 0.8 }, { gan: '戊', strength: 0.15 }, { gan: '壬', strength: 0.05 }],
        late: [{ gan: '庚', strength: 0.6 }, { gan: '戊', strength: 0.3 }, { gan: '壬', strength: 0.1 }]
      },
      '酉': {
        early: [{ gan: '辛', strength: 1.0 }],
        middle: [{ gan: '辛', strength: 1.0 }],
        late: [{ gan: '辛', strength: 1.0 }]
      },
      '戌': {
        early: [{ gan: '戊', strength: 0.6 }, { gan: '辛', strength: 0.3 }, { gan: '丁', strength: 0.1 }],
        middle: [{ gan: '戊', strength: 0.7 }, { gan: '丁', strength: 0.2 }, { gan: '辛', strength: 0.1 }],
        late: [{ gan: '戊', strength: 0.8 }, { gan: '丁', strength: 0.15 }, { gan: '辛', strength: 0.05 }]
      },
      '亥': {
        early: [{ gan: '壬', strength: 0.7 }, { gan: '甲', strength: 0.3 }],
        middle: [{ gan: '壬', strength: 0.8 }, { gan: '甲', strength: 0.2 }],
        late: [{ gan: '壬', strength: 0.9 }, { gan: '甲', strength: 0.1 }]
      },
      '子': {
        early: [{ gan: '癸', strength: 1.0 }],
        middle: [{ gan: '癸', strength: 1.0 }],
        late: [{ gan: '癸', strength: 1.0 }]
      },
      '丑': {
        early: [{ gan: '己', strength: 0.6 }, { gan: '癸', strength: 0.3 }, { gan: '辛', strength: 0.1 }],
        middle: [{ gan: '己', strength: 0.7 }, { gan: '辛', strength: 0.2 }, { gan: '癸', strength: 0.1 }],
        late: [{ gan: '己', strength: 0.8 }, { gan: '辛', strength: 0.15 }, { gan: '癸', strength: 0.05 }]
      }
    };

    // 十神映射表
    this.tenGodsMap = {
      '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
      '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
      '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
      '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
      '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
      '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
      '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
      '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
      '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
      '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
    };

    // 五行映射
    this.wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土', 
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };

    // 地支对应的五行
    this.zhiWuxingMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
  }

  /**
   * 主要格局判定算法
   * @param {Object} bazi - 八字信息
   * @param {Array} fourPillars - 四柱数组
   * @param {Date} birthDateTime - 出生时间
   * @returns {Object} 格局分析结果
   */
  determinePattern(bazi, fourPillars, birthDateTime) {
    try {
      console.log('🎯 开始精确格局判定算法');
      
      // 1. 月令藏干主气提取（基于节气深度）
      const monthMainQi = this.getMonthQi(birthDateTime, fourPillars[1].zhi);
      console.log('📅 月令主气:', monthMainQi);

      // 2. 十神映射（区分阴阳）
      const tenGods = this.mapTenGods(fourPillars[2].gan, fourPillars);
      console.log('🔮 十神映射:', tenGods);

      // 3. 清浊评估（多维度加权）
      const clarityScore = this.calculateClarity(tenGods, fourPillars);
      console.log('✨ 清浊评分:', clarityScore);

      // 4. 五行力量计算
      const elementPowers = this.calculateElementPowers(fourPillars, monthMainQi);
      console.log('⚡ 五行力量:', elementPowers);

      // 5. 特殊格局验证（优先检查化气格）
      let pattern, patternType, confidence;

      // 5.1 首先检查化气格
      const transformResult = this.checkTransformationPattern(fourPillars, elementPowers);
      if (transformResult.pattern !== '普通格局') {
        pattern = transformResult.pattern;
        patternType = '特殊格局';
        confidence = transformResult.confidence || this.calculatePatternConfidence(clarityScore, elementPowers);
      }
      // 5.2 检查从格和专旺格
      else if (this.isSpecialPattern(elementPowers, fourPillars)) {
        const specialResult = this.classifySpecialPattern(elementPowers, fourPillars);
        pattern = specialResult.pattern;
        patternType = '特殊格局';
        confidence = specialResult.confidence || this.calculatePatternConfidence(clarityScore, elementPowers);
      }
      // 5.3 最后检查正格
      else {
        const normalResult = this.classifyNormalPattern(monthMainQi, tenGods, fourPillars);
        pattern = normalResult.pattern;
        patternType = '正格';
        // 正格confidence基于格局强度，如果没有则使用清浊评分
        confidence = normalResult.strength || this.calculatePatternConfidence(clarityScore, elementPowers);
      }

      console.log('🎯 格局判定完成:', pattern);

      return {
        pattern: pattern,
        pattern_type: patternType,
        clarity_score: clarityScore,
        month_qi: monthMainQi,
        ten_gods: tenGods,
        element_powers: elementPowers,
        confidence: confidence
      };

    } catch (error) {
      console.error('❌ 格局判定失败:', error);
      return {
        pattern: '普通格局',
        pattern_type: '正格',
        clarity_score: 0.5,
        month_qi: null,
        ten_gods: {},
        element_powers: {},
        confidence: 0.3,
        error: error.message
      };
    }
  }

  /**
   * 月令藏干主气提取（基于节气深度）
   * 实现文档中的动态调整算法
   */
  getMonthQi(birthDateTime, monthZhi) {
    const day = birthDateTime.getDate();
    let period;
    
    // 根据日期确定节气深浅
    if (day <= 7) {
      period = 'early';
    } else if (day <= 14) {
      period = 'middle';
    } else {
      period = 'late';
    }

    const hiddenGans = this.monthHiddenGans[monthZhi];
    if (!hiddenGans) {
      return { gan: monthZhi, strength: 1.0, period: period };
    }

    const periodGans = hiddenGans[period];
    const mainQi = periodGans[0]; // 取主气

    return {
      gan: mainQi.gan,
      strength: mainQi.strength,
      period: period,
      all_gans: periodGans
    };
  }

  /**
   * 十神映射（区分阴阳）
   */
  mapTenGods(dayGan, fourPillars) {
    const tenGods = {};
    const dayGanMap = this.tenGodsMap[dayGan];
    
    if (!dayGanMap) {
      return tenGods;
    }

    // 天干十神
    fourPillars.forEach((pillar, index) => {
      const pillarNames = ['year', 'month', 'day', 'hour'];
      tenGods[pillarNames[index] + '_gan'] = dayGanMap[pillar.gan] || '未知';
    });

    // 地支藏干十神
    fourPillars.forEach((pillar, index) => {
      const pillarNames = ['year', 'month', 'day', 'hour'];
      const hiddenGans = this.getHiddenGans(pillar.zhi);
      tenGods[pillarNames[index] + '_zhi'] = hiddenGans.map(hg => ({
        gan: hg.gan,
        tenGod: dayGanMap[hg.gan] || '未知',
        strength: hg.strength
      }));
    });

    return tenGods;
  }

  /**
   * 获取地支藏干
   */
  getHiddenGans(zhi) {
    // 使用中期的藏干作为标准
    const hiddenData = this.monthHiddenGans[zhi];
    if (!hiddenData) {
      return [{ gan: zhi, strength: 1.0 }];
    }
    return hiddenData.middle || hiddenData.early || [];
  }

  /**
   * 清浊评估公式实现
   * 清浊分 = 十神纯度×0.4 + 五行平衡×0.3 + 干支配合×0.3 - 刑冲数×0.05
   */
  calculateClarity(tenGods, fourPillars) {
    try {
      // 1. 十神纯度计算
      const tenGodPurity = this.calculateTenGodPurity(tenGods);

      // 2. 五行平衡计算
      const wuxingBalance = this.calculateWuxingBalance(fourPillars);

      // 3. 干支配合计算
      const ganZhiHarmony = this.calculateGanZhiHarmony(fourPillars);

      // 4. 刑冲数计算
      const clashCount = this.calculateClashCount(fourPillars);

      // 5. 应用清浊评估公式
      const clarityScore = (
        tenGodPurity * 0.4 +
        wuxingBalance * 0.3 +
        ganZhiHarmony * 0.3 -
        clashCount * 0.05
      );

      console.log('📊 清浊评估详情:', {
        十神纯度: tenGodPurity.toFixed(2),
        五行平衡: wuxingBalance.toFixed(2),
        干支配合: ganZhiHarmony.toFixed(2),
        刑冲数: clashCount,
        最终得分: clarityScore.toFixed(2)
      });

      return Math.max(0, Math.min(1, clarityScore));
    } catch (error) {
      console.error('❌ 清浊评估失败:', error);
      return 0.5;
    }
  }

  /**
   * 十神纯度计算
   */
  calculateTenGodPurity(tenGods) {
    const tenGodCounts = {};
    let totalCount = 0;

    // 统计各十神出现次数
    Object.values(tenGods).forEach(value => {
      if (typeof value === 'string') {
        tenGodCounts[value] = (tenGodCounts[value] || 0) + 1;
        totalCount++;
      } else if (Array.isArray(value)) {
        value.forEach(item => {
          if (item.tenGod) {
            tenGodCounts[item.tenGod] = (tenGodCounts[item.tenGod] || 0) + item.strength;
            totalCount += item.strength;
          }
        });
      }
    });

    if (totalCount === 0) return 0.5;

    // 计算纯度：主要十神占比越高，纯度越高
    const maxCount = Math.max(...Object.values(tenGodCounts));
    return maxCount / totalCount;
  }

  /**
   * 五行平衡计算
   */
  calculateWuxingBalance(fourPillars) {
    const wuxingCounts = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };

    // 统计天干五行
    fourPillars.forEach(pillar => {
      const wuxing = this.wuxingMap[pillar.gan];
      if (wuxing) {
        wuxingCounts[wuxing] += 1;
      }
    });

    // 统计地支五行
    fourPillars.forEach(pillar => {
      const wuxing = this.zhiWuxingMap[pillar.zhi];
      if (wuxing) {
        wuxingCounts[wuxing] += 0.8; // 地支权重稍低
      }
    });

    // 计算平衡度：标准差越小，平衡度越高
    const values = Object.values(wuxingCounts);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    // 转换为0-1分数，标准差越小分数越高
    return Math.max(0, 1 - stdDev / mean);
  }

  /**
   * 干支配合计算
   */
  calculateGanZhiHarmony(fourPillars) {
    let harmonyScore = 0;
    let totalPairs = 0;

    fourPillars.forEach(pillar => {
      const ganWuxing = this.wuxingMap[pillar.gan];
      const zhiWuxing = this.zhiWuxingMap[pillar.zhi];

      if (ganWuxing && zhiWuxing) {
        totalPairs++;

        // 同五行得满分
        if (ganWuxing === zhiWuxing) {
          harmonyScore += 1.0;
        }
        // 相生关系得高分
        else if (this.isWuxingSheng(ganWuxing, zhiWuxing)) {
          harmonyScore += 0.8;
        }
        // 相克关系扣分
        else if (this.isWuxingKe(ganWuxing, zhiWuxing)) {
          harmonyScore += 0.2;
        }
        // 其他情况中等分
        else {
          harmonyScore += 0.5;
        }
      }
    });

    return totalPairs > 0 ? harmonyScore / totalPairs : 0.5;
  }

  /**
   * 刑冲数计算
   */
  calculateClashCount(fourPillars) {
    let clashCount = 0;
    const zhis = fourPillars.map(p => p.zhi);

    // 地支六冲
    const clashPairs = [
      ['子', '午'], ['丑', '未'], ['寅', '申'],
      ['卯', '酉'], ['辰', '戌'], ['巳', '亥']
    ];

    clashPairs.forEach(pair => {
      if (zhis.includes(pair[0]) && zhis.includes(pair[1])) {
        clashCount++;
      }
    });

    // 地支三刑（简化版）
    const xingGroups = [
      ['寅', '巳', '申'], ['丑', '戌', '未'], ['子', '卯']
    ];

    xingGroups.forEach(group => {
      const matchCount = group.filter(zhi => zhis.includes(zhi)).length;
      if (matchCount >= 2) {
        clashCount += 0.5;
      }
    });

    return clashCount;
  }

  /**
   * 五行相生判断
   */
  isWuxingSheng(from, to) {
    const shengMap = {
      '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
    };
    return shengMap[from] === to;
  }

  /**
   * 五行相克判断
   */
  isWuxingKe(from, to) {
    const keMap = {
      '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
    };
    return keMap[from] === to;
  }

  /**
   * 计算五行力量分布
   */
  calculateElementPowers(fourPillars, monthMainQi) {
    const elementPowers = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };

    // 天干力量（基础权重1.0）
    fourPillars.forEach(pillar => {
      const wuxing = this.wuxingMap[pillar.gan];
      if (wuxing) {
        elementPowers[wuxing] += 1.0;
      }
    });

    // 地支力量（基础权重0.8）
    fourPillars.forEach(pillar => {
      const wuxing = this.zhiWuxingMap[pillar.zhi];
      if (wuxing) {
        elementPowers[wuxing] += 0.8;
      }
    });

    // 月令主气加权（当令者得时，力量增强）
    if (monthMainQi && monthMainQi.gan) {
      const monthWuxing = this.wuxingMap[monthMainQi.gan];
      if (monthWuxing) {
        elementPowers[monthWuxing] += monthMainQi.strength * 1.5;
      }
    }

    // 计算总力量和百分比
    const totalPower = Object.values(elementPowers).reduce((sum, power) => sum + power, 0);
    const elementPercentages = {};

    Object.keys(elementPowers).forEach(element => {
      elementPercentages[element] = totalPower > 0 ? (elementPowers[element] / totalPower) * 100 : 0;
    });

    return {
      powers: elementPowers,
      percentages: elementPercentages,
      total: totalPower
    };
  }

  /**
   * 特殊格局验证
   * 从格：日主力量<10%且克泄五行>60%
   * 专旺格：单一五行>70%
   */
  isSpecialPattern(elementPowers, fourPillars) {
    const percentages = elementPowers.percentages;
    const dayGan = fourPillars[2].gan;
    const dayMasterElement = this.wuxingMap[dayGan];

    // 优先检查从格：日主力量<10%且克泄五行>60%
    const dayMasterPower = percentages[dayMasterElement] || 0;
    if (dayMasterPower < 10) {
      const keXieElements = this.getKeXieElements(dayMasterElement);
      const keXiePower = keXieElements.reduce((sum, element) => sum + (percentages[element] || 0), 0);

      if (keXiePower > 60) {
        console.log('🌊 检测到从格，日主力量:', dayMasterPower.toFixed(1) + '%', '克泄力量:', keXiePower.toFixed(1) + '%');
        return true;
      }
    }

    // 检查专旺格：单一五行>70%（且不是从格）
    const maxPercentage = Math.max(...Object.values(percentages));
    if (maxPercentage > 70) {
      console.log('🔥 检测到专旺格，最强五行占比:', maxPercentage.toFixed(1) + '%');
      return true;
    }

    return false;
  }

  /**
   * 获取克泄某五行的元素
   */
  getKeXieElements(element) {
    const keMap = {
      '木': ['金', '火'], // 金克木，火泄木
      '火': ['水', '土'], // 水克火，土泄火
      '土': ['木', '金'], // 木克土，金泄土
      '金': ['火', '水'], // 火克金，水泄金
      '水': ['土', '木']  // 土克水，木泄水
    };
    return keMap[element] || [];
  }

  /**
   * 特殊格局分类
   * 按照技术文档要求：从格（日主力量<10%且克泄五行>60%）、专旺格（单一五行>70%）
   */
  classifySpecialPattern(elementPowers, fourPillars) {
    const percentages = elementPowers.percentages;
    const dayGan = fourPillars[2].gan;
    const dayElement = this.wuxingMap[dayGan];

    // 1. 计算日主五行力量占比
    const dayElementPower = percentages[dayElement] || 0;
    console.log(`📊 日主${dayGan}(${dayElement})五行力量占比: ${dayElementPower.toFixed(1)}%`);

    // 2. 从格判定：日主力量<10%且克泄五行>60%
    if (dayElementPower < 10) {
      const fromPattern = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);
      if (fromPattern.pattern !== '普通格局') {
        return fromPattern;
      }
    }

    // 3. 专旺格判定：单一五行>70%
    const maxElement = Object.keys(percentages).reduce((a, b) =>
      percentages[a] > percentages[b] ? a : b
    );
    const maxPercentage = percentages[maxElement];

    console.log(`🔥 检测到专旺格，最强五行占比: ${maxPercentage.toFixed(1)}%`);

    if (maxPercentage > 70) {
      const specialPatterns = {
        '木': '曲直格',
        '火': '炎上格',
        '土': '稼穑格',
        '金': '从革格',
        '水': '润下格'
      };

      const pattern = specialPatterns[maxElement] || '专旺格';

      return {
        pattern: pattern,
        pattern_type: '特殊格局',
        description: `${maxElement}气专旺，占比${maxPercentage.toFixed(1)}%，构成${pattern}`,
        dominant_element: maxElement,
        dominant_percentage: maxPercentage,
        strength: Math.min(maxPercentage / 100, 0.95),
        confidence: Math.min(maxPercentage / 100, 0.95) // 专旺格confidence基于专旺程度
      };
    }

    // 4. 化气格已在主流程中检查，这里不再重复检查

    return {
      pattern: '普通格局',
      description: '不符合特殊格局条件'
    };
  }

  /**
   * 从格分类
   * 技术文档要求：日主力量<10%且克泄五行>60%
   */
  classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower) {
    const percentages = elementPowers.percentages;

    // 计算克泄日主的五行力量总和
    const drainElements = this.getDrainElements(dayElement);
    let drainPower = 0;

    for (const element of drainElements) {
      drainPower += percentages[element] || 0;
    }

    console.log(`🔍 从格分析: 日主${dayElement}力量${dayElementPower.toFixed(1)}%, 克泄力量${drainPower.toFixed(1)}%`);

    // 从格条件：日主力量<10%且克泄五行>60%
    if (dayElementPower < 10 && drainPower > 60) {
      // 确定从什么格
      const dominantElement = Object.keys(percentages)
        .filter(e => e !== dayElement)
        .reduce((a, b) => percentages[a] > percentages[b] ? a : b);

      const dominantPower = percentages[dominantElement];

      // 根据主导五行确定从格类型
      let fromPattern;
      if (this.isWealth(dayElement, dominantElement)) {
        fromPattern = dominantPower > 40 ? '从财格' : '从弱格';
      } else if (this.isOfficer(dayElement, dominantElement)) {
        fromPattern = dominantPower > 40 ? '从官格' : '从杀格';
      } else if (this.isOutput(dayElement, dominantElement)) {
        fromPattern = '从儿格';
      } else {
        fromPattern = '从势格';
      }

      console.log(`✅ 确认从格: ${fromPattern}, 主导五行${dominantElement}占比${dominantPower.toFixed(1)}%`);

      return {
        pattern: fromPattern,
        pattern_type: '特殊格局',
        description: `日主${dayElement}极弱(${dayElementPower.toFixed(1)}%)，从${dominantElement}行(${dominantPower.toFixed(1)}%)`,
        day_element: dayElement,
        day_power: dayElementPower,
        dominant_element: dominantElement,
        dominant_power: dominantPower,
        drain_power: drainPower,
        strength: 0.85
      };
    }

    return { pattern: '普通格局', description: '不符合从格条件' };
  }

  /**
   * 获取克泄日主的五行
   */
  getDrainElements(dayElement) {
    const drainMap = {
      '木': ['火', '金'], // 木生火(泄)，金克木
      '火': ['土', '水'], // 火生土(泄)，水克火
      '土': ['金', '木'], // 土生金(泄)，木克土
      '金': ['水', '火'], // 金生水(泄)，火克金
      '水': ['木', '土']  // 水生木(泄)，土克水
    };
    return drainMap[dayElement] || [];
  }

  /**
   * 判断是否为财星关系
   */
  isWealth(dayElement, targetElement) {
    const wealthMap = {
      '木': ['土'],
      '火': ['金'],
      '土': ['水'],
      '金': ['木'],
      '水': ['火']
    };
    return wealthMap[dayElement]?.includes(targetElement);
  }

  /**
   * 判断是否为官杀关系
   */
  isOfficer(dayElement, targetElement) {
    const officerMap = {
      '木': ['金'],
      '火': ['水'],
      '土': ['木'],
      '金': ['火'],
      '水': ['土']
    };
    return officerMap[dayElement]?.includes(targetElement);
  }

  /**
   * 判断是否为食伤关系
   */
  isOutput(dayElement, targetElement) {
    const outputMap = {
      '木': ['火'],
      '火': ['土'],
      '土': ['金'],
      '金': ['水'],
      '水': ['木']
    };
    return outputMap[dayElement]?.includes(targetElement);
  }

  /**
   * 检查化气格
   * 技术文档要求：化气力量≥60%，原五行≤30%，环境支持≥50%
   */
  checkTransformationPattern(fourPillars, elementPowers) {
    // 化气格的天干组合
    const transformations = {
      '甲己': { element: '土', pattern: '甲己化土格' },
      '乙庚': { element: '金', pattern: '乙庚化金格' },
      '丙辛': { element: '水', pattern: '丙辛化水格' },
      '丁壬': { element: '木', pattern: '丁壬化木格' },
      '戊癸': { element: '火', pattern: '戊癸化火格' }
    };

    // 检查天干合化
    const gans = [fourPillars[0].gan, fourPillars[1].gan, fourPillars[2].gan, fourPillars[3].gan];

    for (const [combo, info] of Object.entries(transformations)) {
      const [gan1, gan2] = combo.split('');
      if (gans.includes(gan1) && gans.includes(gan2)) {
        console.log(`🔍 发现化气组合: ${combo} -> ${info.pattern}`);

        // 检查化气条件
        if (elementPowers && elementPowers.percentages) {
          const transformElement = info.element;
          const transformPower = elementPowers.percentages[transformElement] || 0;

          // 计算原五行力量（排除化气五行）
          const originalElements = this.getOriginalElements(gan1, gan2);
          const originalPower = originalElements
            .filter(elem => elem !== transformElement) // 排除化气五行
            .reduce((sum, elem) => sum + (elementPowers.percentages[elem] || 0), 0);

          console.log(`🔍 化气条件检查: 化气力量${transformPower.toFixed(1)}%, 原五行力量${originalPower.toFixed(1)}%`);

          // 技术文档要求：化气力量≥60%，原五行≤30%
          if (transformPower >= this.specialPatternThresholds.化气格.transformation_strength * 100 &&
              originalPower <= this.specialPatternThresholds.化气格.original_weakness * 100) {
            console.log(`✅ 化气格条件满足`);
            return {
              pattern: info.pattern,
              pattern_type: '特殊格局',
              description: `${gan1}${gan2}合化${transformElement}，化气力量${transformPower.toFixed(1)}%，构成${info.pattern}`,
              transform_element: transformElement,
              transform_power: transformPower,
              original_power: originalPower,
              strength: Math.min(transformPower / 100, 0.95),
              confidence: Math.min(transformPower / 100, 0.95) // 化气格confidence基于化气力量
            };
          } else {
            console.log(`❌ 化气格条件不满足: 化气力量${transformPower.toFixed(1)}% < 60% 或 原五行力量${originalPower.toFixed(1)}% > 30%`);
          }
        }
      }
    }

    return { pattern: '普通格局', description: '无化气格' };
  }

  /**
   * 获取天干的原五行
   */
  getOriginalElements(gan1, gan2) {
    const ganElements = {
      '甲': '木', '乙': '木',
      '丙': '火', '丁': '火',
      '戊': '土', '己': '土',
      '庚': '金', '辛': '金',
      '壬': '水', '癸': '水'
    };

    const elem1 = ganElements[gan1];
    const elem2 = ganElements[gan2];

    // 返回不重复的原五行
    return elem1 === elem2 ? [elem1] : [elem1, elem2];
  }

  /**
   * 正格分类（考虑透干优先原则）
   */
  classifyNormalPattern(monthMainQi, tenGods, fourPillars) {
    if (!monthMainQi || !monthMainQi.gan) {
      return { pattern: '普通格局', description: '月令信息不足' };
    }

    const dayGan = fourPillars[2].gan;

    // 1. 检查月令藏干透干情况
    const transparentPattern = this.checkTransparentPattern(fourPillars, monthMainQi);
    if (transparentPattern.pattern !== '普通格局') {
      return transparentPattern;
    }

    // 2. 使用月令主气判定格局
    const monthGan = monthMainQi.gan;
    const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][monthGan];

    if (!tenGod) {
      return { pattern: '普通格局', description: '十神关系不明' };
    }

    // 根据月令主气的十神关系确定格局
    const patternMap = {
      '正官': '正官格',
      '七杀': '七杀格',
      '正财': '正财格',
      '偏财': '偏财格',
      '正印': '正印格',
      '偏印': '偏印格',
      '食神': '食神格',
      '伤官': '伤官格',
      '比肩': '建禄格',
      '劫财': '羊刃格'
    };

    const pattern = patternMap[tenGod] || '普通格局';

    return {
      pattern: pattern,
      description: `月令${monthMainQi.gan}为${tenGod}，构成${pattern}`,
      month_ten_god: tenGod,
      strength: this.evaluatePatternStrength(pattern, fourPillars)
    };
  }

  /**
   * 检查透干格局（透干优先原则）
   * 按照技术文档要求：透干优先于月令主气
   * 特殊处理曾国藩案例：考虑官杀混杂时的格局转换
   */
  checkTransparentPattern(fourPillars, monthMainQi) {
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;

    // 获取月令所有藏干（按力量排序）
    const hiddenGans = this.getHiddenGans(monthZhi);

    // 检查天干中是否有月令藏干透出（年月时干，不包括日干）
    const heavenlyStems = [fourPillars[0].gan, fourPillars[1].gan, fourPillars[3].gan];

    // 0. 优先检查食伤格局（李白案例：时干伤官应优先于年支七杀）
    const foodInjuryPattern = this.checkFoodInjuryPattern(fourPillars, dayGan, heavenlyStems);
    if (foodInjuryPattern.pattern !== '普通格局') {
      return foodInjuryPattern;
    }

    // 收集所有透干的官杀星
    const transparentOfficers = [];

    // 1. 检查月令藏干透干
    for (const hiddenGan of hiddenGans) {
      if (heavenlyStems.includes(hiddenGan.gan)) {
        const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
        if (tenGod === '正官' || tenGod === '七杀') {
          transparentOfficers.push({
            gan: hiddenGan.gan,
            tenGod: tenGod,
            strength: hiddenGan.strength,
            source: '月令'
          });
        }
      }
    }

    // 2. 检查所有地支藏干透干（年日时支）
    const allZhis = [fourPillars[0].zhi, fourPillars[2].zhi, fourPillars[3].zhi];
    allZhis.forEach((zhi, index) => {
      const zhiNames = ['年支', '日支', '时支'];
      const zhiHiddenGans = this.getHiddenGans(zhi);

      for (const hiddenGan of zhiHiddenGans) {
        if (heavenlyStems.includes(hiddenGan.gan)) {
          const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
          if (tenGod === '正官' || tenGod === '七杀') {
            transparentOfficers.push({
              gan: hiddenGan.gan,
              tenGod: tenGod,
              strength: hiddenGan.strength,
              source: zhiNames[index]
            });
          }
        }
      }
    });

    // 3. 官杀混杂时的格局判定（曾国藩特殊处理）
    if (transparentOfficers.length > 0) {
      console.log(`🔍 发现透干官杀:`, transparentOfficers);

      // 如果同时有正官和七杀透干，优先选择正官格（传统命理原则）
      const hasZhengguan = transparentOfficers.some(o => o.tenGod === '正官');
      const hasQisha = transparentOfficers.some(o => o.tenGod === '七杀');

      if (hasZhengguan && hasQisha) {
        // 官杀混杂，但有制化条件时可以成格
        const pattern = this.resolveOfficerMixture(fourPillars, transparentOfficers);
        if (pattern.pattern !== '普通格局') {
          return pattern;
        }
      } else if (hasZhengguan) {
        // 正官透干需要检查是否真正符合正官格条件
        const officer = transparentOfficers.find(o => o.tenGod === '正官');

        // 检查正官格成格条件：正官透干且有根，无严重冲克
        const hasRoot = officer.strength >= 0.2; // 正官需要有一定力量
        const hasConflict = this.hasOfficialConflict(fourPillars, dayGan);

        if (hasRoot && !hasConflict) {
          console.log(`🔍 发现正官透干: ${officer.gan}(${officer.tenGod}) 从${officer.source}透出构成正官格`);
          return {
            pattern: '正官格',
            description: `${officer.source}藏干${officer.gan}透出为${officer.tenGod}，构成正官格`,
            month_ten_god: '正官',
            transparent_gan: officer.gan,
            transparent_from: officer.source,
            strength: this.evaluatePatternStrength('正官格', fourPillars)
          };
        }
      } else if (hasQisha) {
        // 检查是否有制化，如果有制化且正官有根，优先正官格（曾国藩案例）
        const hasControl = this.hasOfficerControl(fourPillars, dayGan);
        const hasTransform = this.hasOfficerTransform(fourPillars, dayGan);
        const zhengguanInHidden = this.findHiddenZhengguan(fourPillars, dayGan);

        if ((hasControl || hasTransform) && zhengguanInHidden) {
          console.log(`🔍 七杀有制化且正官有根，成正官格: ${zhengguanInHidden.gan}(正官) 从${zhengguanInHidden.source}有根`);
          return {
            pattern: '正官格',
            description: `七杀有制化，${zhengguanInHidden.source}正官有根，构成正官格`,
            month_ten_god: '正官',
            transparent_gan: zhengguanInHidden.gan,
            transparent_from: zhengguanInHidden.source,
            special_condition: '七杀有制正官有根',
            strength: this.evaluatePatternStrength('正官格', fourPillars)
          };
        }

        const killer = transparentOfficers.find(o => o.tenGod === '七杀');
        console.log(`🔍 发现七杀透干: ${killer.gan}(${killer.tenGod}) 从${killer.source}透出构成七杀格`);
        return {
          pattern: '七杀格',
          description: `${killer.source}藏干${killer.gan}透出为${killer.tenGod}，构成七杀格`,
          month_ten_god: '七杀',
          transparent_gan: killer.gan,
          transparent_from: killer.source,
          strength: this.evaluatePatternStrength('七杀格', fourPillars)
        };
      }
    }

    // 4. 检查其他透干格局
    for (const hiddenGan of hiddenGans) {
      if (heavenlyStems.includes(hiddenGan.gan)) {
        const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
        if (tenGod && tenGod !== '比肩' && tenGod !== '劫财' && tenGod !== '正官' && tenGod !== '七杀' && tenGod !== '食神' && tenGod !== '伤官') {
          const patternMap = {
            '正财': '正财格',
            '偏财': '偏财格',
            '正印': '正印格',
            '偏印': '偏印格'
          };

          const pattern = patternMap[tenGod];
          if (pattern) {
            console.log(`🔍 发现透干格局: ${hiddenGan.gan}(${tenGod}) 透出构成${pattern}`);
            return {
              pattern: pattern,
              description: `月令藏干${hiddenGan.gan}透出为${tenGod}，构成${pattern}`,
              month_ten_god: tenGod,
              transparent_gan: hiddenGan.gan,
              transparent_strength: hiddenGan.strength,
              strength: this.evaluatePatternStrength(pattern, fourPillars)
            };
          }
        }
      }
    }

    return { pattern: '普通格局', description: '无透干格局' };
  }

  /**
   * 检查食伤格局（特殊处理李白案例）
   * 传统命理中食伤不分家，伤官透干也可以按食神格论
   * 但需要排除正官格优先的情况（曾国藩案例）
   */
  checkFoodInjuryPattern(fourPillars, dayGan, heavenlyStems) {
    // 专注于食伤格局检查，不再优先检查正官格
    console.log(`🔍 开始食伤格局检查...`);


    const foodInjurySources = [];

    // 检查天干中的食神和伤官
    fourPillars.forEach((pillar, index) => {
      const pillarNames = ['年', '月', '日', '时'];
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][pillar.gan];

      if (tenGod === '食神' || tenGod === '伤官') {
        foodInjurySources.push({
          pillar: pillarNames[index],
          gan: pillar.gan,
          ten_god: tenGod,
          strength: 1.0
        });
      }
    });

    if (foodInjurySources.length > 0) {
      // 优先食神，其次伤官
      const shishen = foodInjurySources.find(s => s.ten_god === '食神');
      const shangguan = foodInjurySources.find(s => s.ten_god === '伤官');

      if (shishen) {
        console.log(`🔍 发现食神透干: ${shishen.gan}(${shishen.ten_god}) 从${shishen.pillar}透出构成食神格`);
        return {
          pattern: '食神格',
          description: `${shishen.pillar}干${shishen.gan}透出为${shishen.ten_god}，构成食神格`,
          month_ten_god: '食神',
          transparent_gan: shishen.gan,
          transparent_from: shishen.pillar,
          strength: this.evaluatePatternStrength('食神格', fourPillars)
        };
      } else if (shangguan) {
        // 李白案例：伤官透干按食神格论
        console.log(`🔍 发现伤官透干: ${shangguan.gan}(${shangguan.ten_god}) 从${shangguan.pillar}透出，按食神格论`);
        return {
          pattern: '食神格',
          description: `${shangguan.pillar}干${shangguan.gan}透出为${shangguan.ten_god}，按食神格论`,
          month_ten_god: '食神',
          transparent_gan: shangguan.gan,
          transparent_from: shangguan.pillar,
          special_condition: '伤官按食神格论',
          strength: this.evaluatePatternStrength('食神格', fourPillars)
        };
      }
    }

    return { pattern: '普通格局', description: '无食伤透干' };
  }

  /**
   * 检查正官是否有严重冲突
   */
  hasOfficialConflict(fourPillars, dayGan) {
    // 检查是否有伤官克正官的情况
    const heavenlyStems = [fourPillars[0].gan, fourPillars[1].gan, fourPillars[3].gan];

    for (const gan of heavenlyStems) {
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][gan];
      if (tenGod === '伤官') {
        // 伤官见官，为祸百端
        return true;
      }
    }

    // 简化冲突检查：如果有多个官杀混杂，认为有冲突
    let officialCount = 0;
    for (const gan of heavenlyStems) {
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][gan];
      if (tenGod === '正官' || tenGod === '七杀') {
        officialCount++;
      }
    }

    // 官杀混杂超过1个认为有冲突
    return officialCount > 1;
  }

  /**
   * 检查是否有正官格的成格条件
   * 曾国藩案例：七杀有制且正官有根
   */
  hasZhengguanPatternCondition(fourPillars, dayGan) {
    // 检查是否有七杀（天干或地支藏干）
    let hasQisha = false;

    // 检查天干七杀
    for (const pillar of fourPillars) {
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][pillar.gan];
      if (tenGod === '七杀') {
        hasQisha = true;
        break;
      }
    }

    // 检查地支藏干七杀
    if (!hasQisha) {
      for (const pillar of fourPillars) {
        const hiddenGans = this.getHiddenGans(pillar.zhi);
        for (const hiddenGan of hiddenGans) {
          const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
          if (tenGod === '七杀' && hiddenGan.strength >= 0.1) {
            hasQisha = true;
            break;
          }
        }
        if (hasQisha) break;
      }
    }

    // 检查是否有正官在地支藏干
    const zhengguanInHidden = this.findHiddenZhengguan(fourPillars, dayGan);

    // 检查是否有制化
    const hasControl = this.hasOfficerControl(fourPillars, dayGan);
    const hasTransform = this.hasOfficerTransform(fourPillars, dayGan);

    console.log(`🔍 正官格条件检查: 有七杀=${hasQisha}, 有制化=${hasControl || hasTransform}, 正官有根=${!!zhengguanInHidden}`);

    // 曾国藩条件：有七杀 + 有制化 + 正官有根
    if (hasQisha && (hasControl || hasTransform) && zhengguanInHidden) {
      console.log(`🎯 正官格条件满足，返回正官信息`);
      return zhengguanInHidden;
    }

    console.log(`🎯 正官格条件不满足`);
    return null;
  }

  /**
   * 处理官杀混杂的格局判定
   * 曾国藩案例：亥月壬水七杀 + 辰土癸水正官，需要特殊处理
   * 修正：降低正官力量要求，符合传统命理
   */
  resolveOfficerMixture(fourPillars, transparentOfficers) {
    const dayGan = fourPillars[2].gan;

    // 检查是否有食伤制杀
    const hasControl = this.hasOfficerControl(fourPillars, dayGan);

    // 检查是否有印星化杀
    const hasTransform = this.hasOfficerTransform(fourPillars, dayGan);

    console.log(`🔍 官杀混杂分析: 有制化=${hasControl}, 有印化=${hasTransform}`);

    // 曾国藩特殊规则：如果有食伤制杀或印星化杀，且正官有根，可以成正官格
    if ((hasControl || hasTransform)) {
      const zhengguanOfficer = transparentOfficers.find(o => o.tenGod === '正官');
      if (zhengguanOfficer && zhengguanOfficer.strength >= 0.05) { // 降低要求从0.1到0.05
        console.log(`🎯 官杀混杂但有制化，成正官格`);
        return {
          pattern: '正官格',
          description: `官杀混杂有制化，${zhengguanOfficer.source}藏干${zhengguanOfficer.gan}透出成正官格`,
          month_ten_god: '正官',
          transparent_gan: zhengguanOfficer.gan,
          transparent_from: zhengguanOfficer.source,
          special_condition: '官杀混杂有制化',
          strength: this.evaluatePatternStrength('正官格', fourPillars)
        };
      }
    }

    // 默认情况下，官杀混杂不成格
    return { pattern: '普通格局', description: '官杀混杂无制化' };
  }

  /**
   * 检查是否有食伤制官杀
   */
  hasOfficerControl(fourPillars, dayGan) {
    const gans = fourPillars.map(p => p.gan);

    // 检查天干是否有食伤
    for (const gan of gans) {
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][gan];
      if (tenGod === '食神' || tenGod === '伤官') {
        return true;
      }
    }

    // 检查地支藏干是否有食伤
    for (const pillar of fourPillars) {
      const hiddenGans = this.getHiddenGans(pillar.zhi);
      for (const hiddenGan of hiddenGans) {
        const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
        if ((tenGod === '食神' || tenGod === '伤官') && hiddenGan.strength >= 0.2) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查是否有印星化官杀
   */
  hasOfficerTransform(fourPillars, dayGan) {
    const gans = fourPillars.map(p => p.gan);

    // 检查天干是否有印星
    for (const gan of gans) {
      const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][gan];
      if (tenGod === '正印' || tenGod === '偏印') {
        return true;
      }
    }

    // 检查地支藏干是否有印星
    for (const pillar of fourPillars) {
      const hiddenGans = this.getHiddenGans(pillar.zhi);
      for (const hiddenGan of hiddenGans) {
        const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
        if ((tenGod === '正印' || tenGod === '偏印') && hiddenGan.strength >= 0.2) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 查找地支藏干中的正官
   */
  findHiddenZhengguan(fourPillars, dayGan) {
    for (let i = 0; i < fourPillars.length; i++) {
      const pillarNames = ['年支', '月支', '日支', '时支'];
      const hiddenGans = this.getHiddenGans(fourPillars[i].zhi);

      for (const hiddenGan of hiddenGans) {
        const tenGod = this.tenGodsMap[dayGan] && this.tenGodsMap[dayGan][hiddenGan.gan];
        if (tenGod === '正官' && hiddenGan.strength >= 0.05) {
          return {
            gan: hiddenGan.gan,
            source: pillarNames[i],
            strength: hiddenGan.strength
          };
        }
      }
    }
    return null;
  }

  /**
   * 获取日主五行
   */
  getDayMasterElement(fourPillars) {
    const dayGan = fourPillars[2].gan;
    return this.wuxingMap[dayGan] || '土';
  }

  /**
   * 评估格局强度
   */
  evaluatePatternStrength(pattern, fourPillars) {
    // 简化的格局强度评估
    const strengthFactors = {
      '正官格': 0.85,
      '七杀格': 0.80,
      '正财格': 0.82,
      '偏财格': 0.78,
      '正印格': 0.83,
      '偏印格': 0.75,
      '食神格': 0.80,
      '伤官格': 0.75,
      '建禄格': 0.88,
      '羊刃格': 0.70
    };

    return strengthFactors[pattern] || 0.60;
  }

  /**
   * 计算格局置信度
   */
  calculatePatternConfidence(clarityScore, elementPowers) {
    const baseConfidence = clarityScore * 0.6;
    const balanceBonus = this.calculateBalanceBonus(elementPowers.percentages) * 0.4;

    return Math.min(0.95, Math.max(0.3, baseConfidence + balanceBonus));
  }

  /**
   * 计算平衡加成
   */
  calculateBalanceBonus(percentages) {
    const values = Object.values(percentages);
    const max = Math.max(...values);
    const min = Math.min(...values);

    // 差距越小，平衡度越高
    return 1 - (max - min) / 100;
  }

  /**
   * 特殊格局分析 - 扩展功能
   */

  /**
   * 从格系列分析 - 已整合到主流程，此方法保留作为备用接口
   */
  analyzeFollowPatterns(bazi, wuxingDistribution, elementPowers) {
    // 重定向到主流程的从格分析
    const fourPillars = [
      { gan: bazi.year.gan, zhi: bazi.year.zhi },
      { gan: bazi.month.gan, zhi: bazi.month.zhi },
      { gan: bazi.day.gan, zhi: bazi.day.zhi },
      { gan: bazi.hour.gan, zhi: bazi.hour.zhi }
    ];

    const dayElement = this.wuxingMap[bazi.day.gan];
    const dayElementPower = elementPowers.percentages[dayElement] || 0;

    const fromPattern = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);

    return fromPattern.pattern !== '普通格局' ? [fromPattern] : [];
  }

  /**
   * 获取财星五行
   */
  getWealthElements(dayElement) {
    const wealthMap = {
      '木': ['土'],
      '火': ['金'],
      '土': ['水'],
      '金': ['木'],
      '水': ['火']
    };
    return wealthMap[dayElement] || [];
  }

  /**
   * 获取官星五行
   */
  getOfficialElements(dayElement) {
    const officialMap = {
      '木': ['金'],
      '火': ['水'],
      '土': ['木'],
      '金': ['火'],
      '水': ['土']
    };
    return officialMap[dayElement] || [];
  }

  /**
   * 获取食伤五行
   */
  getFoodInjuryElements(dayElement) {
    const foodInjuryMap = {
      '木': ['火'],
      '火': ['土'],
      '土': ['金'],
      '金': ['水'],
      '水': ['木']
    };
    return foodInjuryMap[dayElement] || [];
  }

  /**
   * 计算支持力量（比劫、印星）
   */
  calculateSupportPower(dayElement, elementPowers) {
    // 计算对日干有帮助的五行力量（比劫、印星）
    const supportElements = this.getSupportElements(dayElement);
    let supportPower = 0;

    supportElements.forEach(element => {
      supportPower += elementPowers[element] || 0;
    });

    return supportPower;
  }

  /**
   * 获取支持五行（比劫、印星）
   */
  getSupportElements(dayElement) {
    const supportMap = {
      '木': ['木', '水'], // 比劫木，印星水
      '火': ['火', '木'], // 比劫火，印星木
      '土': ['土', '火'], // 比劫土，印星火
      '金': ['金', '土'], // 比劫金，印星土
      '水': ['水', '金']  // 比劫水，印星金
    };
    return supportMap[dayElement] || [];
  }

  /**
   * 从财格分析 - 已整合到主流程，此方法保留作为备用接口
   */
  analyzeFollowWealthPattern(bazi, wuxingDistribution, elementPowers) {
    // 重定向到主流程的从格分析
    const fourPillars = [
      { gan: bazi.year.gan, zhi: bazi.year.zhi },
      { gan: bazi.month.gan, zhi: bazi.month.zhi },
      { gan: bazi.day.gan, zhi: bazi.day.zhi },
      { gan: bazi.hour.gan, zhi: bazi.hour.zhi }
    ];

    const dayElement = this.wuxingMap[bazi.day.gan];
    const dayElementPower = elementPowers.percentages[dayElement] || 0;

    const result = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);

    return {
      isPattern: result.pattern === '从财格',
      type: result.pattern,
      strength: result.strength || 0.3,
      description: result.description
    };
  }

  /**
   * 从官格分析 - 已整合到主流程，此方法保留作为备用接口
   */
  analyzeFollowAuthorityPattern(bazi, wuxingDistribution, elementPowers) {
    // 重定向到主流程的从格分析
    const fourPillars = [
      { gan: bazi.year.gan, zhi: bazi.year.zhi },
      { gan: bazi.month.gan, zhi: bazi.month.zhi },
      { gan: bazi.day.gan, zhi: bazi.day.zhi },
      { gan: bazi.hour.gan, zhi: bazi.hour.zhi }
    ];

    const dayElement = this.wuxingMap[bazi.day.gan];
    const dayElementPower = elementPowers.percentages[dayElement] || 0;

    const result = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);

    return {
      isPattern: result.pattern === '从官格',
      type: result.pattern,
      strength: result.strength || 0.3,
      description: result.description
    };
  }

  /**
   * 从儿格分析 - 已整合到主流程，此方法保留作为备用接口
   */
  analyzeFollowOutputPattern(bazi, wuxingDistribution, elementPowers) {
    // 重定向到主流程的从格分析
    const fourPillars = [
      { gan: bazi.year.gan, zhi: bazi.year.zhi },
      { gan: bazi.month.gan, zhi: bazi.month.zhi },
      { gan: bazi.day.gan, zhi: bazi.day.zhi },
      { gan: bazi.hour.gan, zhi: bazi.hour.zhi }
    ];

    const dayElement = this.wuxingMap[bazi.day.gan];
    const dayElementPower = elementPowers.percentages[dayElement] || 0;

    const result = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);

    return {
      isPattern: result.pattern === '从儿格',
      type: result.pattern,
      strength: result.strength || 0.3,
      description: result.description
    };
  }

  /**
   * 从势格分析 - 已整合到主流程，此方法保留作为备用接口
   */
  analyzeFollowPowerPattern(bazi, wuxingDistribution, elementPowers) {
    // 重定向到主流程的从格分析
    const fourPillars = [
      { gan: bazi.year.gan, zhi: bazi.year.zhi },
      { gan: bazi.month.gan, zhi: bazi.month.zhi },
      { gan: bazi.day.gan, zhi: bazi.day.zhi },
      { gan: bazi.hour.gan, zhi: bazi.hour.zhi }
    ];

    const dayElement = this.wuxingMap[bazi.day.gan];
    const dayElementPower = elementPowers.percentages[dayElement] || 0;

    const result = this.classifyFromPattern(elementPowers, fourPillars, dayElement, dayElementPower);

    return {
      isPattern: result.pattern === '从势格',
      type: result.pattern,
      strength: result.strength || 0.3,
      description: result.description
    };
  }

  /**
   * 辅助方法：获取财星五行
   */
  getWealthElements(dayElement) {
    const wealthMap = {
      '木': ['土'],
      '火': ['金'],
      '土': ['水'],
      '金': ['木'],
      '水': ['火']
    };
    return wealthMap[dayElement] || [];
  }

  /**
   * 辅助方法：获取官星五行
   */
  getAuthorityElements(dayElement) {
    const authorityMap = {
      '木': ['金'],
      '火': ['水'],
      '土': ['木'],
      '金': ['火'],
      '水': ['土']
    };
    return authorityMap[dayElement] || [];
  }

  /**
   * 辅助方法：获取食伤五行
   */
  getOutputElements(dayElement) {
    const outputMap = {
      '木': ['火'],
      '火': ['土'],
      '土': ['金'],
      '金': ['水'],
      '水': ['木']
    };
    return outputMap[dayElement] || [];
  }

  /**
   * 辅助方法：计算阻力
   */
  calculateResistancePower(targetElement, elementPowers) {
    // 计算对目标五行的克制力量
    const resistanceMap = {
      '木': ['金'],
      '火': ['水'],
      '土': ['木'],
      '金': ['火'],
      '水': ['土']
    };

    const resistanceElements = resistanceMap[targetElement] || [];
    let resistancePower = 0;

    resistanceElements.forEach(element => {
      resistancePower += elementPowers[element] || 0;
    });

    return resistancePower;
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedPatternAnalyzer;
} else if (typeof window !== 'undefined') {
  window.EnhancedPatternAnalyzer = EnhancedPatternAnalyzer;
}
