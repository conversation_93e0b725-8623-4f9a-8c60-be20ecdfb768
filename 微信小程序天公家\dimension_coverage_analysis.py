#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字命理规则维度覆盖分析工具
分析当前规则数据的维度完整性和充足性
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Set, Tuple
from collections import defaultdict, Counter

class DimensionCoverageAnalyzer:
    def __init__(self):
        # 基于微信小程序系统需求的完整维度体系
        self.required_dimensions = {
            # 核心基础维度
            "基础排盘": {
                "四柱计算": ["年柱", "月柱", "日柱", "时柱"],
                "五行分析": ["五行统计", "五行强弱", "五行得分", "五行平衡"],
                "十神分析": ["十神配置", "十神关系", "十神作用"],
                "阴阳分析": ["阴阳配比", "阴阳平衡"]
            },
            
            # 格局分析维度
            "格局理论": {
                "正格分析": ["正官格", "偏官格", "正财格", "偏财格", "正印格", "偏印格", "食神格", "伤官格"],
                "特殊格局": ["建禄格", "月刃格", "曲直格", "炎上格", "稼穑格", "从革格", "润下格"],
                "从格分析": ["从财格", "从杀格", "从儿格", "从旺格", "从强格"],
                "化气格局": ["甲己化土", "乙庚化金", "丙辛化水", "丁壬化木", "戊癸化火"],
                "格局成败": ["成格条件", "破格因素", "格局等级"]
            },
            
            # 用神理论维度
            "用神体系": {
                "用神选择": ["扶抑用神", "调候用神", "通关用神", "专旺用神"],
                "喜忌分析": ["喜神配置", "忌神识别", "闲神作用"],
                "用神力量": ["用神得力", "用神受制", "用神变化"],
                "季节调候": ["春季调候", "夏季调候", "秋季调候", "冬季调候"]
            },
            
            # 强弱判断维度
            "强弱体系": {
                "日主强弱": ["身强判断", "身弱判断", "中和状态"],
                "月令分析": ["当令分析", "失令分析", "月令作用"],
                "帮扶分析": ["印比帮扶", "根气分析", "通根情况"],
                "克泄分析": ["官杀克身", "食伤泄身", "财星耗身"]
            },
            
            # 神煞系统维度
            "神煞体系": {
                "吉神分析": ["天乙贵人", "天德贵人", "月德贵人", "文昌贵人", "学堂词馆"],
                "凶煞分析": ["羊刃", "劫煞", "亡神", "灾煞", "孤辰寡宿"],
                "特殊神煞": ["华盖", "咸池", "驿马", "空亡", "十恶大败"],
                "神煞作用": ["神煞组合", "神煞制化", "神煞影响"]
            },
            
            # 大运流年维度
            "运程分析": {
                "大运分析": ["大运排列", "大运喜忌", "大运作用", "运程吉凶"],
                "流年分析": ["流年干支", "流年作用", "岁运并临", "三会三合"],
                "运势周期": ["十年大运", "流年变化", "运势起伏"],
                "关键年份": ["转运年份", "重要流年", "危险年份"]
            },
            
            # 六亲关系维度
            "六亲体系": {
                "父母分析": ["父亲信息", "母亲信息", "父母关系", "孝道情况"],
                "配偶分析": ["配偶特征", "婚姻状况", "感情运势", "婚姻时间"],
                "子女分析": ["子女信息", "子女缘分", "教育情况", "子女成就"],
                "兄弟分析": ["兄弟姐妹", "手足情深", "互助情况"],
                "长辈分析": ["祖辈信息", "长辈助力", "家族传承"]
            },
            
            # 事业财运维度
            "事业财运": {
                "事业分析": ["职业方向", "事业发展", "工作环境", "领导能力"],
                "财运分析": ["正财运势", "偏财运势", "投资理财", "财富积累"],
                "官运分析": ["仕途发展", "权力地位", "官场运势", "升迁机会"],
                "创业分析": ["创业时机", "创业方向", "合作伙伴", "成功概率"]
            },
            
            # 健康分析维度
            "健康体系": {
                "体质分析": ["先天体质", "五行体质", "阴阳体质"],
                "疾病预测": ["易患疾病", "健康隐患", "疾病时期"],
                "养生建议": ["饮食调理", "运动建议", "作息调整", "环境选择"],
                "医疗建议": ["就医时机", "治疗方向", "康复建议"]
            },
            
            # 性格心理维度
            "性格心理": {
                "性格特征": ["基本性格", "性格优势", "性格缺陷", "性格发展"],
                "心理分析": ["心理特点", "情绪管理", "压力应对", "心理健康"],
                "人际关系": ["社交能力", "人际和谐", "贵人运势", "小人防范"],
                "学习能力": ["学习天赋", "专业方向", "学术成就", "智慧发展"]
            },
            
            # 地理环境维度
            "地理环境": {
                "居住环境": ["住宅方位", "环境选择", "风水配合"],
                "工作地点": ["工作方位", "办公环境", "地域选择"],
                "出行方向": ["有利方位", "不利方位", "旅行建议"],
                "发展地域": ["适合城市", "发展方向", "迁移建议"]
            },
            
            # 时间选择维度
            "时间选择": {
                "重要决策": ["决策时机", "签约时间", "投资时机"],
                "人生节点": ["结婚时间", "生子时机", "创业时间", "退休时间"],
                "日常选择": ["出行时间", "搬家时机", "求职时间"],
                "修身养性": ["学习时机", "修行时间", "静养时期"]
            }
        }
        
        # 当前规则数据
        self.current_rules = []
        self.current_categories = set()
        self.dimension_coverage = defaultdict(list)
        
    def load_current_rules(self, filename: str = "classical_rules_advanced_complete.json"):
        """加载当前规则数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.current_rules = data.get('rules', [])
                
            # 分析当前分类
            for rule in self.current_rules:
                category = rule.get('category', '未分类')
                pattern_name = rule.get('pattern_name', '未知')
                self.current_categories.add(category)
                self.dimension_coverage[category].append(pattern_name)
                
            print(f"✅ 成功加载 {len(self.current_rules)} 条规则")
            print(f"📊 当前分类: {list(self.current_categories)}")
            
        except Exception as e:
            print(f"❌ 加载规则数据失败: {e}")
            
    def analyze_dimension_coverage(self) -> Dict:
        """分析维度覆盖情况"""
        analysis_result = {
            "覆盖情况": {},
            "缺失维度": {},
            "规则分布": {},
            "充足性评估": {},
            "优先级建议": {}
        }
        
        # 1. 分析覆盖情况
        total_required = 0
        covered_count = 0
        
        for main_dim, sub_dims in self.required_dimensions.items():
            analysis_result["覆盖情况"][main_dim] = {}
            analysis_result["缺失维度"][main_dim] = {}
            
            for sub_dim, items in sub_dims.items():
                total_required += len(items)
                covered_items = []
                missing_items = []
                
                for item in items:
                    # 检查是否有相关规则
                    if self._has_related_rule(item):
                        covered_items.append(item)
                        covered_count += 1
                    else:
                        missing_items.append(item)
                
                analysis_result["覆盖情况"][main_dim][sub_dim] = {
                    "总数": len(items),
                    "已覆盖": len(covered_items),
                    "覆盖率": f"{len(covered_items)/len(items)*100:.1f}%",
                    "已覆盖项目": covered_items
                }
                
                if missing_items:
                    analysis_result["缺失维度"][main_dim][sub_dim] = missing_items
        
        # 2. 总体覆盖率
        overall_coverage = covered_count / total_required * 100
        analysis_result["总体覆盖率"] = f"{overall_coverage:.1f}%"
        
        # 3. 规则分布分析
        category_distribution = Counter(rule.get('category', '未分类') for rule in self.current_rules)
        analysis_result["规则分布"] = dict(category_distribution)
        
        # 4. 充足性评估
        analysis_result["充足性评估"] = self._assess_sufficiency()
        
        # 5. 优先级建议
        analysis_result["优先级建议"] = self._generate_priority_suggestions()
        
        return analysis_result
    
    def _has_related_rule(self, item: str) -> bool:
        """检查是否有相关规则"""
        for rule in self.current_rules:
            pattern_name = rule.get('pattern_name', '')
            original_text = rule.get('original_text', '')
            
            # 检查格局名称匹配
            if item in pattern_name or pattern_name in item:
                return True
            
            # 检查文本内容匹配
            if item in original_text:
                return True
                
        return False
    
    def _assess_sufficiency(self) -> Dict:
        """评估规则数量充足性"""
        sufficiency = {}
        
        # 建议的最少规则数量标准
        min_rules_standard = {
            "用神理论": 15,  # 核心理论需要更多规则
            "正格": 24,      # 8个正格，每个至少3条规则
            "特殊格局": 20,  # 各种特殊格局
            "强弱判断": 12,  # 强弱判断方法
            "调候格局": 16,  # 四季调候
            "神煞格局": 30,  # 神煞种类繁多
            "大运流年": 20,  # 运程分析
            "六亲关系": 18,  # 六亲分析
            "事业财运": 16,  # 事业财运
            "健康体系": 12,  # 健康分析
            "性格心理": 10,  # 性格分析
            "地理环境": 8,   # 地理方位
            "时间选择": 8    # 时间选择
        }
        
        current_distribution = Counter(rule.get('category', '未分类') for rule in self.current_rules)
        
        for category, min_count in min_rules_standard.items():
            current_count = current_distribution.get(category, 0)
            sufficiency[category] = {
                "当前数量": current_count,
                "建议最少": min_count,
                "缺口": max(0, min_count - current_count),
                "充足性": "充足" if current_count >= min_count else "不足",
                "优先级": "低" if current_count >= min_count else ("高" if current_count == 0 else "中")
            }
        
        return sufficiency
    
    def _generate_priority_suggestions(self) -> List[Dict]:
        """生成优先级建议"""
        suggestions = []
        
        # 基于系统需求的优先级
        high_priority = [
            {
                "维度": "大运流年",
                "原因": "微信小程序需要运势分析功能",
                "建议规则数": 20,
                "提取来源": "千里命稿行运篇、流年篇"
            },
            {
                "维度": "六亲关系", 
                "原因": "用户关心家庭关系分析",
                "建议规则数": 18,
                "提取来源": "千里命稿六亲篇、渊海子平"
            },
            {
                "维度": "事业财运",
                "原因": "用户最关心的实用功能",
                "建议规则数": 16,
                "提取来源": "千里命稿富贵篇、三命通会"
            },
            {
                "维度": "神煞格局",
                "原因": "当前仅3条规则，严重不足",
                "建议规则数": 30,
                "提取来源": "三命通会神煞篇、滴天髓"
            }
        ]
        
        medium_priority = [
            {
                "维度": "健康体系",
                "原因": "现代用户关注健康",
                "建议规则数": 12,
                "提取来源": "五行精纪、医学古籍"
            },
            {
                "维度": "性格心理",
                "原因": "个性化分析需求",
                "建议规则数": 10,
                "提取来源": "千里命稿性情篇"
            }
        ]
        
        low_priority = [
            {
                "维度": "地理环境",
                "原因": "辅助功能",
                "建议规则数": 8,
                "提取来源": "风水古籍"
            },
            {
                "维度": "时间选择",
                "原因": "择日功能",
                "建议规则数": 8,
                "提取来源": "择日古籍"
            }
        ]
        
        return {
            "高优先级": high_priority,
            "中优先级": medium_priority,
            "低优先级": low_priority
        }
    
    def generate_expansion_plan(self) -> Dict:
        """生成扩展计划"""
        plan = {
            "阶段一：核心功能补强": {
                "目标": "补充系统核心功能所需规则",
                "时间": "1-2周",
                "任务": [
                    "从千里命稿提取大运流年规则20条",
                    "从千里命稿提取六亲关系规则18条", 
                    "从三命通会提取神煞规则30条",
                    "从千里命稿提取事业财运规则16条"
                ],
                "预期成果": "规则总数达到122条，覆盖核心分析功能"
            },
            
            "阶段二：分析维度完善": {
                "目标": "完善各分析维度",
                "时间": "2-3周",
                "任务": [
                    "补充健康体系规则12条",
                    "补充性格心理规则10条",
                    "完善调候格局规则至16条",
                    "扩展强弱判断规则至12条"
                ],
                "预期成果": "规则总数达到172条，分析维度基本完整"
            },
            
            "阶段三：高级功能扩展": {
                "目标": "增加高级分析功能",
                "时间": "3-4周", 
                "任务": [
                    "添加地理环境规则8条",
                    "添加时间选择规则8条",
                    "完善特殊格局规则",
                    "增加现代应用规则"
                ],
                "预期成果": "规则总数达到200条，功能全面完整"
            }
        }
        
        return plan
    
    def save_analysis_report(self, analysis_result: Dict, filename: str = "dimension_coverage_analysis_report.json"):
        """保存分析报告"""
        report = {
            "分析时间": datetime.now().isoformat(),
            "当前规则统计": {
                "总规则数": len(self.current_rules),
                "分类数": len(self.current_categories),
                "分类列表": list(self.current_categories)
            },
            "维度覆盖分析": analysis_result,
            "扩展计划": self.generate_expansion_plan()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 分析报告已保存到: {filename}")
        
        return report

def main():
    """主函数"""
    analyzer = DimensionCoverageAnalyzer()
    
    # 加载当前规则数据
    analyzer.load_current_rules()
    
    # 进行维度覆盖分析
    analysis_result = analyzer.analyze_dimension_coverage()
    
    # 保存分析报告
    report = analyzer.save_analysis_report(analysis_result)
    
    # 打印关键信息
    print("\n" + "="*80)
    print("📊 八字命理规则维度覆盖分析报告")
    print("="*80)
    
    print(f"\n📈 总体覆盖率: {analysis_result['总体覆盖率']}")
    print(f"📋 当前规则分布: {analysis_result['规则分布']}")
    
    print(f"\n🎯 充足性评估:")
    for category, assessment in analysis_result['充足性评估'].items():
        status = "✅" if assessment['充足性'] == "充足" else "❌"
        print(f"   {status} {category}: {assessment['当前数量']}/{assessment['建议最少']} (缺口: {assessment['缺口']})")
    
    print(f"\n🚀 优先级建议:")
    priorities = analysis_result['优先级建议']
    for priority_level, items in priorities.items():
        print(f"\n   {priority_level}:")
        for item in items:
            print(f"     • {item['维度']}: {item['原因']}")

if __name__ == "__main__":
    main()
