// pages/index/index.js - 首页逻辑
const api = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 随机占卜
    randomDivination: null,

    // 统计信息
    statistics: null,

    // API连接状态
    apiConnected: false
  },

  onLoad() {
    console.log('首页加载');
    wx.setNavigationBarTitle({
      title: '天公师兄'
    });
    this.initPage();
  },

  onShow() {
    // 更新API连接状态
    this.setData({
      apiConnected: app.globalData.apiConnected
    });

    // 如果API已连接，加载数据
    if (app.globalData.apiConnected) {
      this.loadPageData();
    }
  },

  // 初始化页面
  initPage() {
    console.log('初始化首页');

    // 检查API连接状态
    if (app.globalData.apiConnected) {
      this.loadPageData();
    } else {
      // 监听API连接状态变化
      this.checkApiConnection();
    }
  },

  // 检查API连接
  checkApiConnection() {
    const checkInterval = setInterval(() => {
      if (app.globalData.apiConnected) {
        clearInterval(checkInterval);
        this.setData({ apiConnected: true });
        this.loadPageData();
      }
    }, 1000);

    // 10秒后停止检查
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 10000);
  },

  // 加载页面数据
  async loadPageData() {
    try {
      // 加载统计信息
      if (app.globalData.statistics) {
        this.setData({
          statistics: app.globalData.statistics
        });
      }

      // 加载随机占卜
      await this.loadRandomDivination();

    } catch (error) {
      console.error('加载页面数据失败:', error);
    }
  },

  // 加载随机占卜
  async loadRandomDivination() {
    try {
      const result = await api.getRandomDivination();
      console.log('随机占卜:', result);

      this.setData({
        randomDivination: result.data
      });

    } catch (error) {
      console.error('加载随机占卜失败:', error);
    }
  },

  // 显示占卜选项
  showDivinationOptions() {
    wx.showActionSheet({
      itemList: ['李淳风六壬时课', '玉匣记八字排盘'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.goToLiurenDivination();
        } else if (res.tapIndex === 1) {
          this.goToBaziPaipan();
        }
      }
    });
  },

  // 导航到李淳风六壬时课占卜页面
  goToLiurenDivination(e) {
    const keyword = e?.currentTarget?.dataset?.keyword;
    let url = '/pages/divination/index';

    if (keyword) {
      url += `?keyword=${encodeURIComponent(keyword)}`;
    }

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到李淳风六壬时课页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 导航到八字排盘页面
  goToBaziPaipan() {
    wx.navigateTo({
      url: '/pages/bazi-input/index',
      success: () => {
        console.log('跳转到八字排盘页面');
      },
      fail: (err) => {
        console.error('跳转到八字排盘页面失败:', err);
        wx.showToast({
          title: '跳转失败，请检查页面是否存在',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 导航到占卜页面 (保持兼容性)
  goToDivination(e) {
    this.goToLiurenDivination(e);
  },

  // 导航到对话页面
  goToChat() {
    wx.navigateTo({
      url: '/pages/chat-hub/index'
    });
  },

  // 导航到测评页面
  goToAssessment() {
    wx.navigateTo({
      url: '/pages/assessment-hub/index'
    });
  },

  // 导航到个人中心
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  }
});