/**
 * 验证问真八字的命卦算法
 * 尝试找出得到震卦的正确算法
 */

// 方法1：标准算法（我们之前用的）
function standardMethod(year, gender = '男') {
  const yearLastTwo = year % 100;
  let remainder;
  
  if (gender === '男') {
    remainder = (100 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 5) % 9;
    if (remainder === 0) remainder = 9;
  }
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }
  
  return {
    method: '标准算法',
    calculation: gender === '男' ? `(100-${yearLastTwo})%9=${remainder}` : `(${yearLastTwo}+5)%9=${remainder}`,
    remainder: remainder,
    gua: gua
  };
}

// 方法2：可能的问真八字算法1 - 使用99而不是100
function wenzhenMethod1(year, gender = '男') {
  const yearLastTwo = year % 100;
  let remainder;
  
  if (gender === '男') {
    remainder = (99 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 6) % 9;
    if (remainder === 0) remainder = 9;
  }
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }
  
  return {
    method: '问真算法1（99基数）',
    calculation: gender === '男' ? `(99-${yearLastTwo})%9=${remainder}` : `(${yearLastTwo}+6)%9=${remainder}`,
    remainder: remainder,
    gua: gua
  };
}

// 方法3：可能的问真八字算法2 - 不同的卦位映射
function wenzhenMethod2(year, gender = '男') {
  const yearLastTwo = year % 100;
  let remainder;
  
  if (gender === '男') {
    remainder = (100 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 5) % 9;
    if (remainder === 0) remainder = 9;
  }
  
  // 可能的不同卦位映射
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '震', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  const gua = guaMap[remainder];
  
  return {
    method: '问真算法2（不同映射）',
    calculation: gender === '男' ? `(100-${yearLastTwo})%9=${remainder}` : `(${yearLastTwo}+5)%9=${remainder}`,
    remainder: remainder,
    gua: gua
  };
}

// 方法4：农历年份算法
function lunarYearMethod(year, gender = '男') {
  // 2015年农历年可能是2014年（立春前）或2015年
  // 假设使用农历2014年
  const lunarYear = 2014;
  const yearLastTwo = lunarYear % 100;
  let remainder;
  
  if (gender === '男') {
    remainder = (100 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 5) % 9;
    if (remainder === 0) remainder = 9;
  }
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }
  
  return {
    method: '农历年算法',
    lunarYear: lunarYear,
    calculation: gender === '男' ? `(100-${yearLastTwo})%9=${remainder}` : `(${yearLastTwo}+5)%9=${remainder}`,
    remainder: remainder,
    gua: gua
  };
}

// 方法5：立春分界算法
function lichunMethod(year, month, day, gender = '男') {
  // 2015年立春是2月4日，11月20日已过立春，应该用2015年
  // 但如果是2014年11月20日，则可能用2014年
  
  // 假设实际是2014年11月20日
  const actualYear = 2014;
  const yearLastTwo = actualYear % 100;
  let remainder;
  
  if (gender === '男') {
    remainder = (100 - yearLastTwo) % 9;
    if (remainder === 0) remainder = 9;
  } else {
    remainder = (yearLastTwo + 5) % 9;
    if (remainder === 0) remainder = 9;
  }
  
  const guaMap = {
    1: '坎', 2: '坤', 3: '震', 4: '巽', 5: '中宫', 6: '乾', 7: '兑', 8: '艮', 9: '离'
  };
  
  let gua = guaMap[remainder];
  if (remainder === 5) {
    gua = gender === '男' ? '坤' : '艮';
  }
  
  return {
    method: '立春分界算法',
    actualYear: actualYear,
    calculation: gender === '男' ? `(100-${yearLastTwo})%9=${remainder}` : `(${yearLastTwo}+5)%9=${remainder}`,
    remainder: remainder,
    gua: gua
  };
}

// 方法6：反推震卦的年份
function findZhenGuaYears() {
  console.log('🔍 反推：哪些年份的男性命卦是震卦？');
  
  const zhenGuaYears = [];
  
  // 震卦对应余数3
  // 男性公式：(100 - 年份后两位) % 9 = 3
  // 即：100 - 年份后两位 = 9k + 3
  // 年份后两位 = 100 - (9k + 3) = 97 - 9k
  
  for (let k = 0; k < 12; k++) {
    const yearLastTwo = 97 - 9 * k;
    if (yearLastTwo >= 0 && yearLastTwo <= 99) {
      // 计算对应的完整年份
      const year2000s = 2000 + yearLastTwo;
      const year1900s = 1900 + yearLastTwo;
      
      if (year2000s >= 2000 && year2000s <= 2099) {
        zhenGuaYears.push(year2000s);
      }
      if (year1900s >= 1900 && year1900s <= 1999) {
        zhenGuaYears.push(year1900s);
      }
    }
  }
  
  console.log('震卦年份（男性）:', zhenGuaYears.sort());
  
  // 检查2015附近的年份
  const nearby = zhenGuaYears.filter(y => Math.abs(y - 2015) <= 5);
  console.log('2015年附近的震卦年份:', nearby);
  
  return zhenGuaYears;
}

// 主验证函数
function verifyWenzhenMethod() {
  console.log('🔮 验证问真八字的命卦算法');
  console.log('='.repeat(60));
  
  const year = 2015;
  const month = 11;
  const day = 20;
  const gender = '男';
  
  console.log('📊 测试数据:');
  console.log('日期: 2015年11月20日');
  console.log('性别:', gender);
  console.log('期望结果: 震卦（来自问真八字）');
  console.log('');
  
  // 测试各种可能的算法
  const method1 = standardMethod(year, gender);
  const method2 = wenzhenMethod1(year, gender);
  const method3 = wenzhenMethod2(year, gender);
  const method4 = lunarYearMethod(year, gender);
  const method5 = lichunMethod(year, month, day, gender);
  
  console.log('📊 各算法结果:');
  console.log('='.repeat(40));
  console.log('1.', method1.method, ':', method1.gua, '卦');
  console.log('   计算:', method1.calculation);
  console.log('');
  console.log('2.', method2.method, ':', method2.gua, '卦');
  console.log('   计算:', method2.calculation);
  console.log('');
  console.log('3.', method3.method, ':', method3.gua, '卦');
  console.log('   计算:', method3.calculation);
  console.log('');
  console.log('4.', method4.method, ':', method4.gua, '卦');
  console.log('   计算:', method4.calculation, '（农历', method4.lunarYear, '年）');
  console.log('');
  console.log('5.', method5.method, ':', method5.gua, '卦');
  console.log('   计算:', method5.calculation, '（实际', method5.actualYear, '年）');
  console.log('');
  
  // 查找震卦匹配
  const methods = [method1, method2, method3, method4, method5];
  const zhenMethods = methods.filter(m => m.gua === '震');
  
  console.log('🎯 震卦匹配结果:');
  if (zhenMethods.length > 0) {
    console.log('✅ 找到匹配的算法:');
    zhenMethods.forEach(m => {
      console.log('-', m.method, ':', m.calculation);
    });
  } else {
    console.log('❌ 没有找到得出震卦的算法');
    console.log('可能的原因:');
    console.log('1. 问真八字使用了其他特殊算法');
    console.log('2. 可能考虑了其他因素（如时辰、地域等）');
    console.log('3. 可能是软件的特殊设置或版本差异');
  }
  
  console.log('');
  findZhenGuaYears();
  
  return {
    methods: methods,
    zhenMethods: zhenMethods,
    found: zhenMethods.length > 0
  };
}

// 运行验证
const result = verifyWenzhenMethod();

console.log('\n🤔 分析结论:');
if (result.found) {
  console.log('✅ 找到了可能的算法匹配');
  console.log('建议: 确认问真八字使用的具体算法');
} else {
  console.log('❌ 未找到标准算法的匹配');
  console.log('建议:');
  console.log('1. 确认问真八字的具体设置和版本');
  console.log('2. 检查是否有特殊的地域或流派设置');
  console.log('3. 确认输入的日期和性别是否正确');
  console.log('4. 可能问真八字使用了非标准的命卦算法');
}

console.log('\n📝 请提供更多信息:');
console.log('1. 问真八字软件的版本号');
console.log('2. 软件中显示的具体计算过程');
console.log('3. 是否有特殊的设置选项');
console.log('4. 完整的八字排盘结果截图（如果可能）');
