// verify_critical_fixes.js
// 验证关键修复：日期错误和月柱计算错误

console.log('🚨 验证关键修复效果...');

const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];

const correctWuhuDun = {
  '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
  '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
  '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
  '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
  '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
};

// 修复后的节气月份计算
function getSolarMonthByNodeQi_Fixed(month, day) {
  console.log(`\n🔍 修复后节气月份计算: ${month}月${day}日`);
  
  // 修正：7月节气边界精确判断（基于权威万年历验证）
  if (month === 7) {
    // 根据权威万年历：2024年7月30日仍是未月（辛未）
    // 说明申月开始时间在8月，不是7月
    if (day >= 7) {
      console.log('  7月7日小暑后 → 未月（6）（整个7月都是未月）');
      return 6;   // 7月7日小暑后 → 未月（整个7月都是未月）
    }
    console.log('  7月7日前 → 午月（5）');
    return 5;     // 7月7日前 → 午月
  }

  // 修正：8月的特殊处理（立秋边界）
  if (month === 8) {
    if (day >= 7) {
      console.log('  8月7日立秋后 → 申月（7）');
      return 7;   // 8月7日立秋后 → 申月开始
    }
    console.log('  8月7日前 → 未月（6）');
    return 6;     // 8月7日前 → 未月
  }

  // 修正：9月的特殊处理（白露边界）
  if (month === 9) {
    if (day > 7) {
      console.log('  9月7日白露后（第二天开始）→ 酉月（8）');
      return 8;    // 9月7日白露后（第二天开始）→ 酉月
    }
    console.log('  9月7日及之前 → 申月（7）');
    return 7;      // 9月7日及之前 → 申月
  }

  // 其他月份简化处理
  const simpleMap = {
    1: 12, 2: 1, 3: 2, 4: 3, 5: 4, 6: 5,
    10: 9, 11: 10, 12: 11
  };
  
  const result = simpleMap[month] || month;
  console.log(`  ${month}月 → 地支序号${result}`);
  return result;
}

// 计算月柱（修复后）
function calculateMonthPillar_Fixed(year, month, day, yearGan) {
  console.log(`\n🔧 修复后月柱计算: ${year}年${month}月${day}日，年干：${yearGan}`);
  
  // 获取节气月份
  const solarMonth = getSolarMonthByNodeQi_Fixed(month, day);
  
  // 五虎遁起始
  const monthGanStart = correctWuhuDun[yearGan];
  
  // 月干计算
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  
  // 月支
  const monthZhi = monthZhiMap[solarMonth - 1];
  const monthGan = tiangan[monthGanIndex];
  
  const result = monthGan + monthZhi;
  
  console.log(`  节气月份：${solarMonth}`);
  console.log(`  年干：${yearGan}，起始：${tiangan[monthGanStart]}（索引${monthGanStart}）`);
  console.log(`  月干计算：(${monthGanStart} + ${solarMonth} - 1) % 10 = ${monthGanIndex}`);
  console.log(`  月干：${monthGan}，月支：${monthZhi}`);
  console.log(`  月柱结果：${result}`);
  
  return result;
}

// 验证关键修复
function verifyKeyFixes() {
  console.log('\n📚 验证关键修复效果：');
  
  const keyTestCases = [
    { 
      date: '2024年7月30日', 
      yearGan: '甲', 
      expected: '辛未', 
      description: '用户报告的关键案例',
      priority: 'HIGH'
    },
    { 
      date: '2024年7月29日', 
      yearGan: '甲', 
      expected: '辛未', 
      description: '7月末仍应是未月'
    },
    { 
      date: '2024年8月1日', 
      yearGan: '甲', 
      expected: '辛未', 
      description: '8月初仍可能是未月'
    },
    { 
      date: '2024年8月7日', 
      yearGan: '甲', 
      expected: '壬申', 
      description: '立秋当日开始申月'
    },
    { 
      date: '2024年8月15日', 
      yearGan: '甲', 
      expected: '壬申', 
      description: '8月中旬确定是申月'
    }
  ];
  
  let allCorrect = true;
  
  keyTestCases.forEach(testCase => {
    const [year, month, day] = testCase.date.match(/(\d+)年(\d+)月(\d+)日/).slice(1).map(Number);
    const result = calculateMonthPillar_Fixed(year, month, day, testCase.yearGan);
    const isCorrect = result === testCase.expected;
    
    if (!isCorrect) allCorrect = false;
    
    const status = isCorrect ? '✅ 正确' : '❌ 错误';
    const priority = testCase.priority === 'HIGH' ? '🚨 高优先级' : '';
    
    console.log(`\n${testCase.date} ${priority} (${testCase.description}):`);
    console.log(`  期望：${testCase.expected}`);
    console.log(`  实际：${result}`);
    console.log(`  结果：${status}`);
    
    if (!isCorrect) {
      console.log(`  ⚠️ 修复失败！需要进一步调整`);
    }
  });
  
  return allCorrect;
}

// 分析修复效果
function analyzeFixResults() {
  console.log('\n📊 修复效果分析：');
  
  const allCorrect = verifyKeyFixes();
  
  if (allCorrect) {
    console.log('\n🎉 所有测试用例通过！');
    console.log('✅ 月柱计算修复成功');
    console.log('✅ 节气边界判断正确');
    console.log('✅ 五虎遁算法准确');
  } else {
    console.log('\n⚠️ 仍有测试用例失败');
    console.log('❌ 需要进一步调整算法');
    console.log('🔧 建议检查节气时间数据');
  }
}

// 提供使用指导
function provideUsageGuidance() {
  console.log('\n📋 使用指导：');
  
  console.log('\n修复后的预期效果：');
  console.log('1. 2024年7月30日月柱：辛未（不再是壬申）');
  console.log('2. 整个7月都是未月（小暑后）');
  console.log('3. 8月7日立秋后开始申月');
  console.log('4. 节气边界判断更加准确');
  
  console.log('\n测试步骤：');
  console.log('1. 重启微信开发者工具');
  console.log('2. 清理编译缓存');
  console.log('3. 输入2024年7月30日');
  console.log('4. 检查月柱是否显示为辛未');
  console.log('5. 验证日期是否正确显示为7月30日');
  
  console.log('\n如果仍有问题：');
  console.log('- 检查控制台日志中的节气月份计算');
  console.log('- 确认五虎遁起始天干');
  console.log('- 验证地支序号映射');
  console.log('- 对比权威万年历结果');
}

// 执行验证
analyzeFixResults();
provideUsageGuidance();

console.log('\n🏁 关键修复验证完成');
console.log('💡 重点：2024年7月30日月柱应该是辛未，不是壬申！');
