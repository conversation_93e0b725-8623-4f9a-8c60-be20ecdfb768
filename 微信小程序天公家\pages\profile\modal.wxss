/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.avatar-preview-content {
  width: 90%;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.avatar-preview-body {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
}

.nickname-input {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.nickname-limit {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
}

.cancel-btn {
  color: #999999;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #6C5CE7;
  font-weight: bold;
}

/* 充值套餐选项 */
.package-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.package-option {
  padding: 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e0e0e0;
  position: relative;
}

.package-option.selected {
  border-color: #6C5CE7;
  background-color: rgba(108, 92, 231, 0.05);
}

.package-option.selected::after {
  content: '';
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #6C5CE7;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
  background-size: 24rpx 24rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.package-option-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.package-option-price {
  font-size: 40rpx;
  color: #FF6B6B;
  margin-bottom: 10rpx;
}

.package-option-desc {
  font-size: 24rpx;
  color: #666666;
}