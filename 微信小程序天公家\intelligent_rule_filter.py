#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能规则筛选器 - 第一阶段实施工具
从4933条原始规则中智能筛选出2000条基础理论层规则
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Tuple
from collections import defaultdict

class IntelligentRuleFilter:
    def __init__(self):
        # 基础理论关键词分类
        self.theory_keywords = {
            "天干地支系统": {
                "keywords": ["天干", "地支", "甲乙丙丁", "子丑寅卯", "干支", "十干", "十二支"],
                "weight": 1.0,
                "target_count": 200
            },
            "五行生克理论": {
                "keywords": ["五行", "金木水火土", "生克", "相生", "相克", "制化", "旺相休囚死"],
                "weight": 1.2,
                "target_count": 300
            },
            "十神理论": {
                "keywords": ["十神", "正官", "偏官", "正财", "偏财", "正印", "偏印", "食神", "伤官", "比肩", "劫财"],
                "weight": 1.1,
                "target_count": 250
            },
            "格局理论": {
                "keywords": ["格局", "正格", "外格", "变格", "成格", "破格", "格局成败"],
                "weight": 1.0,
                "target_count": 200
            },
            "用神理论": {
                "keywords": ["用神", "喜神", "忌神", "闲神", "扶抑", "调候", "通关", "专旺"],
                "weight": 1.3,
                "target_count": 300
            },
            "神煞理论": {
                "keywords": ["神煞", "贵人", "凶煞", "吉神", "天乙", "文昌", "华盖", "桃花"],
                "weight": 0.8,
                "target_count": 150
            },
            "调候理论": {
                "keywords": ["调候", "寒暖", "燥湿", "春夏秋冬", "季节", "月令"],
                "weight": 1.0,
                "target_count": 200
            },
            "月令旺衰": {
                "keywords": ["月令", "当令", "失令", "旺相", "休囚", "死绝"],
                "weight": 0.9,
                "target_count": 150
            },
            "藏干理论": {
                "keywords": ["藏干", "本气", "中气", "余气", "地支藏干"],
                "weight": 0.8,
                "target_count": 100
            },
            "刑冲合害": {
                "keywords": ["刑", "冲", "合", "害", "三合", "三会", "六合", "六冲"],
                "weight": 0.9,
                "target_count": 150
            }
        }
        
        # 质量标准（基于38条高质量规则）
        self.quality_standards = {
            "min_confidence": 0.85,  # 最低置信度
            "min_text_length": 30,   # 最小文本长度
            "max_text_length": 1000, # 最大文本长度
            "required_fields": ["rule_id", "pattern_name", "category", "original_text"]
        }
        
        # OCR错误模式
        self.ocr_errors = ['氺', '灬', '釒', '本', '士', '沂水易士注', '例如：']
        
        # 低质量模式
        self.low_quality_patterns = [
            r'\.{3,}',  # 多个省略号
            r'^.{1,20}$',  # 过短文本
            r'[^\u4e00-\u9fff\w\s]{10,}',  # 过多特殊字符
        ]
    
    def load_original_rules(self, filename: str = "classical_rules_complete.json") -> List[Dict]:
        """加载4933条原始规则"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            print(f"✅ 成功加载 {len(rules)} 条原始规则")
            return rules
            
        except Exception as e:
            print(f"❌ 加载原始规则失败: {e}")
            return []
    
    def calculate_rule_score(self, rule: Dict) -> Tuple[float, Dict]:
        """计算规则的综合评分"""
        score_details = {
            "confidence_score": 0,
            "text_quality_score": 0,
            "theory_relevance_score": 0,
            "structure_score": 0,
            "total_score": 0
        }
        
        # 1. 置信度评分 (30%)
        confidence = rule.get('confidence', 0)
        if confidence >= 0.95:
            score_details["confidence_score"] = 30
        elif confidence >= 0.90:
            score_details["confidence_score"] = 25
        elif confidence >= 0.85:
            score_details["confidence_score"] = 20
        elif confidence >= 0.80:
            score_details["confidence_score"] = 15
        else:
            score_details["confidence_score"] = 0
        
        # 2. 文本质量评分 (25%)
        text = rule.get('original_text', '')
        text_score = 25
        
        # 检查OCR错误
        if any(error in text for error in self.ocr_errors):
            text_score -= 10
        
        # 检查低质量模式
        for pattern in self.low_quality_patterns:
            if re.search(pattern, text):
                text_score -= 5
        
        # 检查文本长度
        text_len = len(text)
        if text_len < 30:
            text_score -= 15
        elif text_len < 50:
            text_score -= 10
        elif text_len > 500:
            text_score += 5
        
        score_details["text_quality_score"] = max(0, text_score)
        
        # 3. 理论相关性评分 (30%)
        theory_score = 0
        matched_theories = []
        
        for theory_name, theory_config in self.theory_keywords.items():
            keywords = theory_config["keywords"]
            weight = theory_config["weight"]
            
            # 检查关键词匹配
            matches = sum(1 for keyword in keywords if keyword in text)
            if matches > 0:
                theory_score += matches * weight * 3
                matched_theories.append(theory_name)
        
        score_details["theory_relevance_score"] = min(30, theory_score)
        score_details["matched_theories"] = matched_theories
        
        # 4. 结构完整性评分 (15%)
        structure_score = 15
        for field in self.quality_standards["required_fields"]:
            if field not in rule or not rule[field]:
                structure_score -= 4
        
        score_details["structure_score"] = max(0, structure_score)
        
        # 计算总分
        total_score = (score_details["confidence_score"] + 
                      score_details["text_quality_score"] + 
                      score_details["theory_relevance_score"] + 
                      score_details["structure_score"])
        
        score_details["total_score"] = total_score
        
        return total_score, score_details
    
    def filter_rules_by_theory(self, rules: List[Dict]) -> Dict[str, List[Dict]]:
        """按理论分类筛选规则"""
        theory_rules = defaultdict(list)
        
        print("🔍 按理论分类筛选规则...")
        
        for rule in rules:
            score, score_details = self.calculate_rule_score(rule)
            
            # 只保留高质量规则
            if score >= 60:  # 总分100分，要求60分以上
                rule['filter_score'] = score
                rule['score_details'] = score_details
                
                # 分配到相关理论类别
                for theory in score_details.get('matched_theories', []):
                    theory_rules[theory].append(rule)
        
        # 按评分排序
        for theory in theory_rules:
            theory_rules[theory].sort(key=lambda x: x['filter_score'], reverse=True)
        
        return dict(theory_rules)
    
    def select_balanced_rules(self, theory_rules: Dict[str, List[Dict]]) -> List[Dict]:
        """平衡选择各理论的规则"""
        selected_rules = []
        selected_rule_ids = set()
        
        print("⚖️ 平衡选择各理论规则...")
        
        # 按目标数量选择每个理论的规则
        for theory_name, theory_config in self.theory_keywords.items():
            target_count = theory_config["target_count"]
            available_rules = theory_rules.get(theory_name, [])
            
            # 选择最高质量的规则
            selected_count = 0
            for rule in available_rules:
                rule_id = rule.get('rule_id', '')
                
                # 避免重复选择
                if rule_id not in selected_rule_ids and selected_count < target_count:
                    # 添加理论标签
                    rule['theory_category'] = theory_name
                    rule['selection_reason'] = f"高质量{theory_name}规则"
                    
                    selected_rules.append(rule)
                    selected_rule_ids.add(rule_id)
                    selected_count += 1
            
            print(f"  {theory_name}: 选择了 {selected_count}/{target_count} 条规则")
        
        return selected_rules
    
    def enhance_rule_quality(self, rules: List[Dict]) -> List[Dict]:
        """提升规则质量"""
        enhanced_rules = []
        
        print("✨ 提升规则质量...")
        
        for rule in rules:
            enhanced_rule = rule.copy()
            
            # 清理文本
            original_text = enhanced_rule.get('original_text', '')
            cleaned_text = self._clean_text(original_text)
            enhanced_rule['original_text'] = cleaned_text
            
            # 生成解释
            if not enhanced_rule.get('interpretations'):
                enhanced_rule['interpretations'] = self._generate_interpretation(enhanced_rule)
            
            # 更新元数据
            enhanced_rule['enhanced'] = True
            enhanced_rule['enhanced_at'] = datetime.now().isoformat()
            enhanced_rule['quality_level'] = 'foundation_theory'
            
            # 重新计算置信度
            if enhanced_rule.get('filter_score', 0) >= 80:
                enhanced_rule['confidence'] = min(0.95, enhanced_rule.get('confidence', 0.85) + 0.05)
            
            enhanced_rules.append(enhanced_rule)
        
        return enhanced_rules
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除OCR错误
        for error in self.ocr_errors:
            text = text.replace(error, '')
        
        # 标准化空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 标准化标点符号
        text = text.replace('，', '，').replace('。', '。')
        text = text.replace('；', '；').replace('：', '：')
        
        return text.strip()
    
    def _generate_interpretation(self, rule: Dict) -> str:
        """生成规则解释"""
        theory_category = rule.get('theory_category', '')
        pattern_name = rule.get('pattern_name', '')
        
        interpretation_templates = {
            "五行生克理论": f"{pattern_name}的五行生克关系和作用机制",
            "十神理论": f"{pattern_name}在十神体系中的作用和意义",
            "格局理论": f"{pattern_name}格局的成立条件和特征",
            "用神理论": f"{pattern_name}在用神选择中的应用",
            "神煞理论": f"{pattern_name}神煞的作用和影响",
            "调候理论": f"{pattern_name}在调候中的作用",
            "天干地支系统": f"{pattern_name}在干支体系中的基础理论",
            "月令旺衰": f"{pattern_name}与月令旺衰的关系",
            "藏干理论": f"{pattern_name}与地支藏干的关系",
            "刑冲合害": f"{pattern_name}的刑冲合害关系"
        }
        
        return interpretation_templates.get(theory_category, f"{pattern_name}的命理理论和应用")
    
    def generate_filter_report(self, original_count: int, selected_rules: List[Dict]) -> str:
        """生成筛选报告"""
        report = []
        report.append("=" * 80)
        report.append("智能规则筛选报告 - 第一阶段基础理论层")
        report.append("=" * 80)
        report.append(f"筛选时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"原始规则数: {original_count}")
        report.append(f"筛选规则数: {len(selected_rules)}")
        report.append(f"筛选率: {len(selected_rules)/original_count*100:.1f}%")
        report.append("")
        
        # 理论分布统计
        theory_distribution = defaultdict(int)
        score_distribution = defaultdict(int)
        
        for rule in selected_rules:
            theory = rule.get('theory_category', '未分类')
            theory_distribution[theory] += 1
            
            score = rule.get('filter_score', 0)
            if score >= 90:
                score_distribution["优秀(90+)"] += 1
            elif score >= 80:
                score_distribution["良好(80-89)"] += 1
            elif score >= 70:
                score_distribution["中等(70-79)"] += 1
            else:
                score_distribution["及格(60-69)"] += 1
        
        report.append("📊 理论分布统计:")
        for theory, count in sorted(theory_distribution.items()):
            target = self.theory_keywords.get(theory, {}).get('target_count', 0)
            completion = f"{count}/{target}" if target > 0 else str(count)
            report.append(f"  {theory}: {completion}")
        
        report.append(f"\n📈 质量分布统计:")
        for level, count in sorted(score_distribution.items()):
            percentage = count / len(selected_rules) * 100
            report.append(f"  {level}: {count} ({percentage:.1f}%)")
        
        # 平均质量指标
        avg_confidence = sum(rule.get('confidence', 0) for rule in selected_rules) / len(selected_rules)
        avg_text_length = sum(len(rule.get('original_text', '')) for rule in selected_rules) / len(selected_rules)
        
        report.append(f"\n📋 平均质量指标:")
        report.append(f"  平均置信度: {avg_confidence:.3f}")
        report.append(f"  平均文本长度: {avg_text_length:.1f} 字符")
        
        return "\n".join(report)
    
    def execute_filtering(self) -> Dict:
        """执行完整的筛选流程"""
        print("🚀 启动智能规则筛选...")
        
        # 1. 加载原始规则
        original_rules = self.load_original_rules()
        if not original_rules:
            return {"error": "无法加载原始规则"}
        
        # 2. 按理论分类筛选
        theory_rules = self.filter_rules_by_theory(original_rules)
        
        # 3. 平衡选择规则
        selected_rules = self.select_balanced_rules(theory_rules)
        
        # 4. 提升规则质量
        enhanced_rules = self.enhance_rule_quality(selected_rules)
        
        # 5. 生成报告
        report = self.generate_filter_report(len(original_rules), enhanced_rules)
        
        # 6. 准备输出数据
        output_data = {
            "metadata": {
                "phase": "第一阶段：基础理论层建设",
                "filter_date": datetime.now().isoformat(),
                "original_count": len(original_rules),
                "selected_count": len(enhanced_rules),
                "filter_rate": f"{len(enhanced_rules)/len(original_rules)*100:.1f}%",
                "quality_standards": self.quality_standards,
                "theory_targets": {k: v["target_count"] for k, v in self.theory_keywords.items()}
            },
            "rules": enhanced_rules
        }
        
        return {
            "success": True,
            "data": output_data,
            "report": report,
            "summary": {
                "原始规则": len(original_rules),
                "筛选规则": len(enhanced_rules),
                "筛选率": f"{len(enhanced_rules)/len(original_rules)*100:.1f}%"
            }
        }

def main():
    """主函数"""
    filter_engine = IntelligentRuleFilter()
    
    # 执行筛选
    result = filter_engine.execute_filtering()
    
    if result.get("success"):
        # 保存筛选结果
        output_filename = "classical_rules_phase1_foundation_theory.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        # 保存筛选报告
        report_filename = "phase1_filtering_report.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(result["report"])
        
        # 打印结果
        print(result["report"])
        print(f"\n✅ 第一阶段筛选完成!")
        print(f"📊 筛选结果: {result['summary']}")
        print(f"💾 数据已保存到: {output_filename}")
        print(f"📋 报告已保存到: {report_filename}")
        
    else:
        print(f"❌ 筛选失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
