/**
 * 小运系统异常检测测试
 * 专门检测前端小运计算系统可能存在的异常
 */

console.log('🔍 小运系统异常检测测试');
console.log('='.repeat(50));

// 模拟不同的测试场景
const testScenarios = [
  {
    name: '正常4岁男孩',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '辛', earthly: '丑' },
        monthPillar: { heavenly: '甲', earthly: '午' },
        dayPillar: { heavenly: '癸', earthly: '卯' },
        timePillar: { heavenly: '壬', earthly: '戌' }
      },
      userInfo: { gender: '男' },
      birthInfo: { year: 2021 }
    }
  },
  {
    name: '边界1岁女孩',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '甲', earthly: '子' },
        monthPillar: { heavenly: '丙', earthly: '寅' },
        dayPillar: { heavenly: '戊', earthly: '申' },
        timePillar: { heavenly: '庚', earthly: '午' }
      },
      userInfo: { gender: '女' },
      birthInfo: { year: 2024 }
    }
  },
  {
    name: '边界10岁男孩',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '乙', earthly: '亥' },
        monthPillar: { heavenly: '丁', earthly: '卯' },
        dayPillar: { heavenly: '己', earthly: '酉' },
        timePillar: { heavenly: '辛', earthly: '巳' }
      },
      userInfo: { gender: '男' },
      birthInfo: { year: 2015 }
    }
  },
  {
    name: '超龄15岁',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '戊', earthly: '戌' },
        monthPillar: { heavenly: '庚', earthly: '子' },
        dayPillar: { heavenly: '壬', earthly: '午' },
        timePillar: { heavenly: '甲', earthly: '辰' }
      },
      userInfo: { gender: '女' },
      birthInfo: { year: 2010 }
    }
  },
  {
    name: '异常数据-缺失时柱',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '丙', earthly: '申' },
        monthPillar: { heavenly: '戊', earthly: '戌' },
        dayPillar: { heavenly: '庚', earthly: '寅' },
        timePillar: null
      },
      userInfo: { gender: '男' },
      birthInfo: { year: 2020 }
    }
  },
  {
    name: '异常数据-无效性别',
    data: {
      baziInfo: {
        yearPillar: { heavenly: '丁', earthly: '未' },
        monthPillar: { heavenly: '己', earthly: '丑' },
        dayPillar: { heavenly: '辛', earthly: '未' },
        timePillar: { heavenly: '癸', earthly: '亥' }
      },
      userInfo: { gender: null },
      birthInfo: { year: 2019 }
    }
  }
];

// 测试小运计算器异常处理
function testMinorFortuneExceptions() {
  console.log('\n📋 测试1：小运计算器异常处理');
  console.log('-'.repeat(40));
  
  try {
    const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
    const calculator = new MinorFortuneCalculator();
    
    let passedTests = 0;
    let totalTests = testScenarios.length;
    
    for (const scenario of testScenarios) {
      console.log(`\n🧪 测试场景: ${scenario.name}`);
      
      try {
        // 构建八字数据格式
        const bazi = {
          yearPillar: scenario.data.baziInfo.yearPillar ? {
            gan: scenario.data.baziInfo.yearPillar.heavenly,
            zhi: scenario.data.baziInfo.yearPillar.earthly
          } : null,
          monthPillar: scenario.data.baziInfo.monthPillar ? {
            gan: scenario.data.baziInfo.monthPillar.heavenly,
            zhi: scenario.data.baziInfo.monthPillar.earthly
          } : null,
          dayPillar: scenario.data.baziInfo.dayPillar ? {
            gan: scenario.data.baziInfo.dayPillar.heavenly,
            zhi: scenario.data.baziInfo.dayPillar.earthly
          } : null,
          hourPillar: scenario.data.baziInfo.timePillar ? {
            gan: scenario.data.baziInfo.timePillar.heavenly,
            zhi: scenario.data.baziInfo.timePillar.earthly
          } : null,
          gender: scenario.data.userInfo.gender
        };
        
        // 计算当前年龄
        const currentYear = new Date().getFullYear();
        const birthYear = scenario.data.birthInfo.year;
        const currentAge = currentYear - birthYear;
        
        console.log(`   年龄: ${currentAge}岁`);
        console.log(`   八字数据:`, bazi);
        
        // 测试单个小运计算
        const result = calculator.calculate(bazi, currentAge);
        
        if (currentAge >= 1 && currentAge <= 10 && bazi.hourPillar && bazi.gender) {
          // 应该成功的情况
          if (result !== null) {
            console.log(`   ✅ 计算成功: ${result.pillar}`);
            passedTests++;
          } else {
            console.log(`   ❌ 计算失败: 应该成功但返回null`);
          }
        } else {
          // 应该失败的情况
          if (result === null) {
            console.log(`   ✅ 正确拒绝: 超出范围或数据异常`);
            passedTests++;
          } else {
            console.log(`   ❌ 异常通过: 应该拒绝但返回了结果`);
          }
        }
        
      } catch (error) {
        console.log(`   ⚠️  捕获异常: ${error.message}`);
        // 对于异常数据，捕获异常也算正确处理
        if (scenario.name.includes('异常数据')) {
          console.log(`   ✅ 异常处理正确`);
          passedTests++;
        }
      }
    }
    
    console.log(`\n📊 异常处理测试: ${passedTests}/${totalTests} 通过`);
    
    return {
      success: passedTests === totalTests,
      passedTests: passedTests,
      totalTests: totalTests
    };
    
  } catch (error) {
    console.error('❌ 小运计算器异常处理测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 测试前端集成异常
function testFrontendIntegrationExceptions() {
  console.log('\n📋 测试2：前端集成异常处理');
  console.log('-'.repeat(40));
  
  try {
    // 模拟前端计算小运数据的方法
    const calculateMinorFortune = function(baziData) {
      console.log('🔮 开始计算小运数据...');

      try {
        // 使用已导入的小运计算器
        const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
        const calculator = new MinorFortuneCalculator();

        // 构建八字数据格式
        const bazi = {
          yearPillar: {
            gan: baziData.baziInfo.yearPillar.heavenly,
            zhi: baziData.baziInfo.yearPillar.earthly
          },
          monthPillar: {
            gan: baziData.baziInfo.monthPillar.heavenly,
            zhi: baziData.baziInfo.monthPillar.earthly
          },
          dayPillar: {
            gan: baziData.baziInfo.dayPillar.heavenly,
            zhi: baziData.baziInfo.dayPillar.earthly
          },
          hourPillar: {
            gan: baziData.baziInfo.timePillar.heavenly,
            zhi: baziData.baziInfo.timePillar.earthly
          },
          gender: baziData.userInfo.gender === '男' ? '男' : '女'
        };

        // 计算当前年龄
        const currentYear = new Date().getFullYear();
        const birthYear = baziData.birthInfo.year;
        const currentAge = currentYear - birthYear;

        console.log(`👶 当前年龄: ${currentAge}岁`);

        // 如果年龄超出小运适用范围，返回提示信息
        if (currentAge > 10) {
          return {
            applicable: false,
            reason: '小运仅适用于1-10岁，当前年龄已超出范围',
            currentAge: currentAge,
            note: '《三命通会·卷八》：小运补大运之不足，未交大运前用之'
          };
        }

        // 计算所有小运
        const allMinorFortunes = calculator.calculateAllMinorFortunes(bazi);

        // 获取当前小运
        const currentMinorFortune = currentAge >= 1 && currentAge <= 10
          ? calculator.calculate(bazi, currentAge)
          : null;

        console.log('✅ 小运计算完成');

        return {
          applicable: true,
          currentAge: currentAge,
          currentMinorFortune: currentMinorFortune,
          allMinorFortunes: allMinorFortunes,
          basis: "《三命通会·卷八》小运起法",
          note: "小运补大运之不足，未交大运前用之，阳男阴女顺行，阴男阳女逆行"
        };

      } catch (error) {
        console.error('❌ 小运计算失败:', error);
        return {
          applicable: false,
          error: error.message,
          reason: '小运计算系统异常'
        };
      }
    };
    
    let passedTests = 0;
    let totalTests = testScenarios.length;
    
    for (const scenario of testScenarios) {
      console.log(`\n🧪 前端集成测试: ${scenario.name}`);
      
      try {
        const result = calculateMinorFortune(scenario.data);
        
        if (result.applicable !== undefined) {
          console.log(`   ✅ 前端集成正常: ${result.applicable ? '适用' : '不适用'}`);
          if (result.error) {
            console.log(`   ⚠️  错误信息: ${result.error}`);
          }
          passedTests++;
        } else {
          console.log(`   ❌ 前端集成异常: 返回格式错误`);
        }
        
      } catch (error) {
        console.log(`   ❌ 前端集成异常: ${error.message}`);
      }
    }
    
    console.log(`\n📊 前端集成测试: ${passedTests}/${totalTests} 通过`);
    
    return {
      success: passedTests === totalTests,
      passedTests: passedTests,
      totalTests: totalTests
    };
    
  } catch (error) {
    console.error('❌ 前端集成异常测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 测试内存泄漏和性能问题
function testPerformanceIssues() {
  console.log('\n📋 测试3：性能和内存问题检测');
  console.log('-'.repeat(40));
  
  try {
    const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
    
    const startMemory = process.memoryUsage().heapUsed;
    const startTime = Date.now();
    
    // 大量计算测试
    for (let i = 0; i < 100; i++) {
      const calculator = new MinorFortuneCalculator();
      
      const bazi = {
        yearPillar: { gan: '辛', zhi: '丑' },
        monthPillar: { gan: '甲', zhi: '午' },
        dayPillar: { gan: '癸', zhi: '卯' },
        hourPillar: { gan: '壬', zhi: '戌' },
        gender: '男'
      };
      
      calculator.calculate(bazi, 5);
      calculator.calculateAllMinorFortunes(bazi);
    }
    
    const endTime = Date.now();
    const endMemory = process.memoryUsage().heapUsed;
    
    const executionTime = endTime - startTime;
    const memoryIncrease = (endMemory - startMemory) / 1024 / 1024; // MB
    
    console.log(`⏱️  执行时间: ${executionTime}ms (100次计算)`);
    console.log(`💾 内存增长: ${memoryIncrease.toFixed(2)}MB`);
    
    const performanceOK = executionTime < 1000 && memoryIncrease < 10;
    console.log(`📊 性能评估: ${performanceOK ? '✅ 正常' : '❌ 异常'}`);
    
    return {
      success: performanceOK,
      executionTime: executionTime,
      memoryIncrease: memoryIncrease
    };
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行所有异常检测测试
function runAllExceptionTests() {
  console.log('🚀 开始运行小运系统异常检测测试...\n');
  
  const test1 = testMinorFortuneExceptions();
  const test2 = testFrontendIntegrationExceptions();
  const test3 = testPerformanceIssues();
  
  console.log('\n📊 异常检测总结:');
  console.log('='.repeat(50));
  console.log(`测试1 - 小运计算器异常处理: ${test1.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`测试2 - 前端集成异常处理: ${test2.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`测试3 - 性能和内存问题: ${test3.success ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = test1.success && test2.success && test3.success;
  console.log(`\n🎯 总体结果: ${allPassed ? '✅ 小运系统运行正常' : '❌ 发现异常问题'}`);
  
  if (!allPassed) {
    console.log('\n⚠️ 发现的问题:');
    if (!test1.success) console.log('- 小运计算器异常处理存在问题');
    if (!test2.success) console.log('- 前端集成异常处理存在问题');
    if (!test3.success) console.log('- 性能或内存使用存在问题');
  }
  
  return {
    allPassed: allPassed,
    test1: test1,
    test2: test2,
    test3: test3
  };
}

// 执行测试
const testResults = runAllExceptionTests();

// 导出测试结果
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testResults;
}
