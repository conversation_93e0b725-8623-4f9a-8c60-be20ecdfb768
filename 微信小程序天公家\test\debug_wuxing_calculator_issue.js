/**
 * 调试五行计算器问题
 * 深度分析为什么前端数据没有变化
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => ({}),
  setStorageSync: () => {},
  createSelectorQuery: () => ({
    in: () => ({
      select: () => ({
        node: () => ({
          exec: (callback) => callback([{ node: null }])
        })
      })
    })
  })
};

// 模拟require
function mockRequire(path) {
  if (path.includes('professional_wuxing_engine')) {
    // 模拟专业级引擎加载失败
    throw new Error('专业级引擎文件不存在或加载失败');
  }
  return {};
}

// 重新实现统一五行计算器（用于调试）
class DebugUnifiedWuxingCalculator {
  constructor() {
    this.engine = null;
    this.cache = new Map();
    this.version = '1.0.0';
    this.initialized = false;
    
    this.initializeEngine();
  }

  initializeEngine() {
    try {
      // 模拟专业级引擎加载
      console.log('🔄 尝试加载专业级引擎...');
      mockRequire('./professional_wuxing_engine.js');
      this.engine = { generateDetailedReport: () => ({}) };
      this.initialized = true;
      console.log('✅ 专业级引擎初始化成功');
    } catch (error) {
      console.warn('⚠️ 专业级引擎加载失败，使用降级模式:', error.message);
      this.engine = null;
      this.initialized = false;
    }
  }

  validateInput(baziData) {
    if (!baziData) return false;
    
    const requiredFields = ['year', 'month', 'day', 'hour'];
    return requiredFields.every(field => {
      return baziData[field] && 
             baziData[field].gan && 
             baziData[field].zhi;
    });
  }

  generateCacheKey(baziData, options) {
    const baziString = `${baziData.year.gan}${baziData.year.zhi}-${baziData.month.gan}${baziData.month.zhi}-${baziData.day.gan}${baziData.day.zhi}-${baziData.hour.gan}${baziData.hour.zhi}`;
    return `wuxing_${baziString}_${JSON.stringify(options)}`;
  }

  // 模拟旧的缓存行为
  simulateOldCacheBehavior() {
    // 添加固定的缓存数据（模拟问题状态）
    const fixedResult = {
      wood: 50, fire: 50, earth: 50, metal: 50, water: 50,
      wuxingStrength: {
        wood: { value: 50, percentage: 20, level: '中和' },
        fire: { value: 50, percentage: 20, level: '中和' },
        earth: { value: 50, percentage: 20, level: '中和' },
        metal: { value: 50, percentage: 20, level: '中和' },
        water: { value: 50, percentage: 20, level: '中和' }
      }
    };
    
    this.cache.set('test_cache_key', fixedResult);
    console.log('🔧 已添加固定缓存数据（模拟问题状态）');
  }

  calculate(baziData, options = {}) {
    try {
      console.log('\n🎯 ===== 调试五行计算过程 =====');
      
      // 参数验证
      if (!this.validateInput(baziData)) {
        throw new Error('八字数据格式不正确');
      }

      // 生成缓存键
      const cacheKey = this.generateCacheKey(baziData, options);
      console.log('🔑 缓存键:', cacheKey);
      
      // 检查缓存状态
      console.log('📋 缓存状态:');
      console.log('   - 缓存大小:', this.cache.size);
      console.log('   - 是否有此键:', this.cache.has(cacheKey));
      
      if (this.cache.has(cacheKey)) {
        const cachedResult = this.cache.get(cacheKey);
        console.log('📋 发现缓存数据:', cachedResult);
        console.log('❌ 这就是问题所在！直接返回了固定的缓存值');
        return cachedResult;
      }
      
      console.log('🔍 引擎状态检查:');
      console.log('   - initialized:', this.initialized);
      console.log('   - engine:', !!this.engine);
      console.log('   - version:', this.version);

      let result;
      
      if (this.initialized && this.engine) {
        console.log('✅ 使用专业级引擎计算');
        result = this.calculateWithProfessionalEngine(baziData);
      } else {
        console.log('⚠️ 专业级引擎不可用，使用改进的降级计算');
        result = this.calculateWithFallback(baziData);
      }
      
      console.log('🎯 最终计算结果:', {
        wood: result.wood,
        fire: result.fire,
        earth: result.earth,
        metal: result.metal,
        water: result.water
      });
      
      return result;
      
    } catch (error) {
      console.error('❌ 五行计算错误:', error.message);
      return this.handleCalculationError(error, baziData);
    }
  }

  calculateWithProfessionalEngine(baziData) {
    console.log('🔄 专业级引擎计算（模拟）...');
    // 模拟专业级计算
    return {
      wood: 48, fire: 31, earth: 60, metal: 35, water: 12,
      wuxingStrength: {
        wood: { value: 48, percentage: 26, level: '偏旺' },
        fire: { value: 31, percentage: 17, level: '中和' },
        earth: { value: 60, percentage: 32, level: '偏旺' },
        metal: { value: 35, percentage: 19, level: '中和' },
        water: { value: 12, percentage: 6, level: '极弱' }
      }
    };
  }

  calculateWithFallback(baziData) {
    console.log('🎯 使用改进的真实五行计算方法...');
    
    // 使用我们修复后的计算逻辑
    const wuxingPowers = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };
    const wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
      '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
      '戌': '土', '亥': '水'
    };

    // 月令调节系数
    const monthAdjustment = this.getMonthAdjustment(baziData.month.zhi);

    // 计算天干力量
    const pillars = [baziData.year, baziData.month, baziData.day, baziData.hour];
    pillars.forEach((pillar, index) => {
      if (pillar.gan && wuxingMap[pillar.gan]) {
        const element = wuxingMap[pillar.gan];
        const baseStrength = index === 2 ? 30 : (index === 1 ? 25 : 20);
        wuxingPowers[element] += baseStrength;
      }
    });

    // 计算地支力量
    pillars.forEach((pillar, index) => {
      if (pillar.zhi && wuxingMap[pillar.zhi]) {
        const element = wuxingMap[pillar.zhi];
        const baseStrength = index === 2 ? 25 : 20;
        wuxingPowers[element] += baseStrength;
        
        // 简化的藏干计算
        this.addCangganPower(wuxingPowers, pillar.zhi, baseStrength * 0.3, wuxingMap);
      }
    });

    // 应用月令调节
    Object.keys(wuxingPowers).forEach(element => {
      const englishElement = this.getEnglishElement(element);
      const adjustment = monthAdjustment[englishElement] || 1.0;
      wuxingPowers[element] = Math.round(wuxingPowers[element] * adjustment);
      wuxingPowers[element] = Math.max(5, wuxingPowers[element]);
    });

    // 转换为英文格式
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    const total = Object.values(wuxingPowers).reduce((sum, power) => sum + power, 0);
    
    const result = {
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'improved_calculation',
      algorithm: '改进的真实五行计算算法',
      wuxingStrength: {},
      calculationDetails: { totalStrength: total, confidence: 85 }
    };

    // 填充五行数据
    Object.entries(elementMap).forEach(([chineseName, englishName]) => {
      const power = wuxingPowers[chineseName];
      const percentage = total > 0 ? (power / total * 100) : 0;
      
      result.wuxingStrength[englishName] = {
        value: power,
        percentage: Math.round(percentage),
        level: this.getStrengthLevel(percentage),
        chineseName: chineseName
      };
      
      result[englishName] = power;
    });

    return result;
  }

  getMonthAdjustment(monthZhi) {
    const seasonAdjustment = {
      '未': { wood: 0.9, fire: 1.2, earth: 1.4, metal: 0.7, water: 0.6 }
    };
    return seasonAdjustment[monthZhi] || { wood: 1.0, fire: 1.0, earth: 1.0, metal: 1.0, water: 1.0 };
  }

  addCangganPower(wuxingPowers, zhi, baseStrength, wuxingMap) {
    const cangganMap = {
      '未': ['丁', '乙']
    };
    const cangganList = cangganMap[zhi];
    if (cangganList) {
      cangganList.forEach(gan => {
        const element = wuxingMap[gan];
        if (element) {
          wuxingPowers[element] += Math.round(baseStrength * 0.5);
        }
      });
    }
  }

  getEnglishElement(chineseElement) {
    const map = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    return map[chineseElement] || chineseElement;
  }

  getStrengthLevel(percentage) {
    if (percentage >= 35) return '极旺';
    if (percentage >= 25) return '偏旺';
    if (percentage >= 15) return '中和';
    if (percentage >= 8) return '偏弱';
    return '极弱';
  }

  handleCalculationError(error, baziData) {
    return {
      wood: 0, fire: 0, earth: 0, metal: 0, water: 0,
      error: error.message
    };
  }
}

// 测试函数
function debugWuxingCalculatorIssue() {
  console.log('🔍 ===== 五行计算器问题深度调试 =====\n');
  
  // 测试用例：戊寅年 辛未月 乙酉日 壬午时
  const testBazi = {
    year: { gan: '戊', zhi: '寅' },
    month: { gan: '辛', zhi: '未' },
    day: { gan: '乙', zhi: '酉' },
    hour: { gan: '壬', zhi: '午' }
  };
  
  console.log('📋 测试八字:', testBazi);
  
  // 创建调试计算器
  const calculator = new DebugUnifiedWuxingCalculator();
  
  console.log('\n🧪 测试1: 模拟问题状态（有缓存）');
  calculator.simulateOldCacheBehavior();
  const problemResult = calculator.calculate(testBazi);
  
  console.log('\n🧪 测试2: 清除缓存后重新计算');
  calculator.cache.clear();
  const fixedResult = calculator.calculate(testBazi);
  
  console.log('\n📊 结果对比:');
  console.log('问题状态（缓存）:', {
    wood: problemResult.wood,
    fire: problemResult.fire,
    earth: problemResult.earth,
    metal: problemResult.metal,
    water: problemResult.water
  });
  
  console.log('修复状态（重算）:', {
    wood: fixedResult.wood,
    fire: fixedResult.fire,
    earth: fixedResult.earth,
    metal: fixedResult.metal,
    water: fixedResult.water
  });
  
  console.log('\n🎯 问题分析:');
  const isProblem = problemResult.wood === 50 && problemResult.fire === 50;
  const isFixed = fixedResult.wood !== 50 || fixedResult.fire !== 50;
  
  console.log('   问题确认:', isProblem ? '✅ 确实存在固定值问题' : '❌ 未发现问题');
  console.log('   修复验证:', isFixed ? '✅ 修复成功，数据已个性化' : '❌ 修复失败');
  
  console.log('\n💡 解决方案:');
  console.log('   1. 清除所有缓存数据');
  console.log('   2. 强制使用改进的计算逻辑');
  console.log('   3. 确保专业级引擎正确初始化');
  console.log('   4. 添加数据验证机制');
}

// 运行调试
debugWuxingCalculatorIssue();
