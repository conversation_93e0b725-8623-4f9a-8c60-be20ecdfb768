// precise_solar_terms_test.js
// 精确节气计算引擎测试套件

// 引入依赖
const PreciseSolarTermsEngine = require('./utils/precise_solar_terms_engine.js');

// 模拟权威节气数据（测试用）
global.getAuthoritativeJieqiData = function(year) {
  // 2021年部分节气数据（用于测试）
  if (year === 2021) {
    return {
      '立春': { month: 2, day: 3, hour: 22, minute: 59 },
      '雨水': { month: 2, day: 18, hour: 18, minute: 44 },
      '惊蛰': { month: 3, day: 5, hour: 16, minute: 54 },
      '春分': { month: 3, day: 20, hour: 17, minute: 37 },
      '清明': { month: 4, day: 4, hour: 21, minute: 35 },
      '谷雨': { month: 4, day: 20, hour: 4, minute: 33 },
      '立夏': { month: 5, day: 5, hour: 14, minute: 47 },
      '小满': { month: 5, day: 21, hour: 3, minute: 37 },
      '芒种': { month: 6, day: 5, hour: 18, minute: 52 },
      '夏至': { month: 6, day: 21, hour: 11, minute: 32 },
      '小暑': { month: 7, day: 7, hour: 5, minute: 5 },
      '大暑': { month: 7, day: 22, hour: 22, minute: 26 },
      '立秋': { month: 8, day: 7, hour: 14, minute: 54 },
      '处暑': { month: 8, day: 23, hour: 5, minute: 35 },
      '白露': { month: 9, day: 7, hour: 17, minute: 53 },
      '秋分': { month: 9, day: 23, hour: 3, minute: 21 },
      '寒露': { month: 10, day: 8, hour: 9, minute: 39 },
      '霜降': { month: 10, day: 23, hour: 12, minute: 51 },
      '立冬': { month: 11, day: 7, hour: 12, minute: 59 },
      '小雪': { month: 11, day: 22, hour: 10, minute: 34 },
      '大雪': { month: 12, day: 7, hour: 5, minute: 57 },
      '冬至': { month: 12, day: 21, hour: 23, minute: 59 },
      '小寒': { month: 1, day: 5, hour: 11, minute: 23 },
      '大寒': { month: 1, day: 20, hour: 4, minute: 40 }
    };
  }
  throw new Error(`测试数据中不包含${year}年`);
};

/**
 * 精确节气计算引擎测试套件
 */
class PreciseSolarTermsTestSuite {
  constructor() {
    this.engine = new PreciseSolarTermsEngine();
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
  }
  
  /**
   * 运行单个测试
   */
  runTest(testName, testFunction) {
    this.totalTests++;
    console.log(`\n🧪 测试: ${testName}`);
    
    try {
      const result = testFunction();
      if (result) {
        console.log(`✅ 通过: ${testName}`);
        this.passedTests++;
        this.testResults.push({ name: testName, status: 'PASS', error: null });
      } else {
        console.log(`❌ 失败: ${testName}`);
        this.testResults.push({ name: testName, status: 'FAIL', error: 'Test returned false' });
      }
    } catch (error) {
      console.log(`❌ 错误: ${testName} - ${error.message}`);
      this.testResults.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }
  
  /**
   * 测试引擎初始化
   */
  testEngineInitialization() {
    return this.runTest('引擎初始化', () => {
      const status = this.engine.getEngineStatus();
      console.log('引擎状态:', status);
      return status.name === 'PreciseSolarTermsEngine' && status.authoritativeDataLoaded;
    });
  }
  
  /**
   * 测试年份节气数据获取
   */
  testYearDataRetrieval() {
    return this.runTest('2021年节气数据获取', () => {
      const yearData = this.engine.getYearSolarTerms(2021);
      console.log('节气数量:', Object.keys(yearData).length);
      console.log('立春时间:', yearData['立春']);
      return Object.keys(yearData).length === 24 && yearData['立春'];
    });
  }
  
  /**
   * 测试精确节气信息计算
   */
  testSolarTermCalculation() {
    return this.runTest('精确节气信息计算', () => {
      // 测试2021年6月24日19:30（测试案例）
      const testDate = new Date(2021, 5, 24, 19, 30); // 月份从0开始
      const result = this.engine.calculateSolarTermInfo(testDate);
      
      console.log('节气分析结果:', result.description);
      console.log('前一节气:', result.prevTerm?.name);
      console.log('后一节气:', result.nextTerm?.name);
      
      // 验证结果合理性
      return result.prevTerm && result.nextTerm && result.description.length > 0;
    });
  }
  
  /**
   * 测试大运目标节气计算
   */
  testDayunTargetTerm() {
    return this.runTest('大运目标节气计算', () => {
      const birthDate = new Date(2021, 5, 24, 19, 30);
      
      // 测试顺行
      const forwardTarget = this.engine.getTargetTermForDayun(birthDate, true);
      console.log('顺行目标节气:', forwardTarget.name, forwardTarget.date);
      
      // 测试逆行
      const backwardTarget = this.engine.getTargetTermForDayun(birthDate, false);
      console.log('逆行目标节气:', backwardTarget.name, backwardTarget.date);
      
      return forwardTarget && backwardTarget && forwardTarget.name !== backwardTarget.name;
    });
  }
  
  /**
   * 测试边界条件
   */
  testBoundaryConditions() {
    return this.runTest('边界条件测试', () => {
      // 测试年初
      const newYearDate = new Date(2021, 0, 1, 0, 0);
      const newYearResult = this.engine.calculateSolarTermInfo(newYearDate);
      console.log('年初节气:', newYearResult.description);
      
      // 测试年末
      const yearEndDate = new Date(2021, 11, 31, 23, 59);
      const yearEndResult = this.engine.calculateSolarTermInfo(yearEndDate);
      console.log('年末节气:', yearEndResult.description);
      
      return newYearResult.description && yearEndResult.description;
    });
  }
  
  /**
   * 测试错误处理
   */
  testErrorHandling() {
    return this.runTest('错误处理测试', () => {
      try {
        // 测试超出范围的年份
        this.engine.getYearSolarTerms(1800);
        return false; // 应该抛出错误
      } catch (error) {
        console.log('正确捕获错误:', error.message);
        return error.message.includes('超出支持范围');
      }
    });
  }
  
  /**
   * 测试性能
   */
  testPerformance() {
    return this.runTest('性能测试', () => {
      const startTime = Date.now();
      
      // 执行100次节气计算
      for (let i = 0; i < 100; i++) {
        const testDate = new Date(2021, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
        this.engine.calculateSolarTermInfo(testDate);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`100次计算耗时: ${duration}ms`);
      console.log(`平均每次: ${(duration / 100).toFixed(2)}ms`);
      
      // 性能要求：平均每次计算不超过50ms
      return (duration / 100) < 50;
    });
  }
  
  /**
   * 测试缓存机制
   */
  testCaching() {
    return this.runTest('缓存机制测试', () => {
      // 第一次获取数据
      const startTime1 = Date.now();
      this.engine.getYearSolarTerms(2021);
      const duration1 = Date.now() - startTime1;
      
      // 第二次获取数据（应该使用缓存）
      const startTime2 = Date.now();
      this.engine.getYearSolarTerms(2021);
      const duration2 = Date.now() - startTime2;
      
      console.log(`首次获取: ${duration1}ms`);
      console.log(`缓存获取: ${duration2}ms`);
      console.log(`缓存大小: ${this.engine.cache.size}`);
      
      // 缓存应该显著提高性能
      return duration2 < duration1 && this.engine.cache.size > 0;
    });
  }
  
  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🌸 精确节气计算引擎测试套件');
    console.log('='.repeat(60));
    
    // 执行所有测试
    this.testEngineInitialization();
    this.testYearDataRetrieval();
    this.testSolarTermCalculation();
    this.testDayunTargetTerm();
    this.testBoundaryConditions();
    this.testErrorHandling();
    this.testPerformance();
    this.testCaching();
    
    // 生成测试报告
    this.generateReport();
  }
  
  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.totalTests - this.passedTests}`);
    console.log(`成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
    
    // 详细结果
    console.log('\n📋 详细结果:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    // 评级
    const successRate = (this.passedTests / this.totalTests) * 100;
    let grade = '';
    if (successRate >= 95) grade = '🏆 优秀';
    else if (successRate >= 85) grade = '🌟 良好';
    else if (successRate >= 70) grade = '⚠️ 及格';
    else grade = '❌ 不及格';
    
    console.log(`\n🎯 测试评级: ${grade}`);
    console.log('='.repeat(60));
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new PreciseSolarTermsTestSuite();
  testSuite.runAllTests();
}

module.exports = PreciseSolarTermsTestSuite;
