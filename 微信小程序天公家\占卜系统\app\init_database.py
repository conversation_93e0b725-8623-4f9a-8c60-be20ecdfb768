#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化数据库脚本
"""

import sqlite3
import os
from pathlib import Path

def init_database():
    """初始化SQLite数据库"""
    
    # 确保data目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    db_path = data_dir / "yujiaji.db"
    
    print(f"🔧 初始化数据库: {db_path}")
    
    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建占卜条目表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS divination_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            original_text TEXT,
            god_name TEXT,
            content_type TEXT DEFAULT 'general',
            confidence_score REAL DEFAULT 0.5,
            semantic_score REAL DEFAULT 0.5,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入一些示例数据
    sample_data = [
        ("大安", "大安神煞，主平安吉祥", "大安神煞，主平安吉祥，诸事顺利", "大安", "general", 0.9, 0.8),
        ("留连", "留连神煞，主延迟阻滞", "留连神煞，主延迟阻滞，需要耐心", "留连", "general", 0.9, 0.8),
        ("速喜", "速喜神煞，主快速喜悦", "速喜神煞，主快速喜悦，有意外收获", "速喜", "general", 0.9, 0.8),
        ("赤口", "赤口神煞，主口舌是非", "赤口神煞，主口舌是非，需要谨慎", "赤口", "general", 0.9, 0.8),
        ("小吉", "小吉神煞，主小的吉利", "小吉神煞，主小的吉利，适合小事", "小吉", "general", 0.9, 0.8),
        ("空亡", "空亡神煞，主虚无缥缈", "空亡神煞，主虚无缥缈，不宜行动", "空亡", "general", 0.9, 0.8)
    ]
    
    cursor.executemany('''
        INSERT OR IGNORE INTO divination_entries 
        (title, description, original_text, god_name, content_type, confidence_score, semantic_score)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', sample_data)
    
    # 提交更改
    conn.commit()
    
    # 验证数据
    cursor.execute("SELECT COUNT(*) FROM divination_entries")
    count = cursor.fetchone()[0]
    
    print(f"✅ 数据库初始化完成，包含 {count} 条记录")
    
    # 关闭连接
    conn.close()
    
    return True

if __name__ == "__main__":
    try:
        init_database()
        print("🎉 数据库初始化成功！")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
