<!-- pages/index/index.wxml -->
<view class="container {{themeClass}}">
  <!-- 顶部导航区域 -->
  <view class="header-area">
    <!-- 个人资料图标 -->
    <view class="profile-icon" bindtap="goToProfile">
      <image src="/assets/icons/profile.png" mode="aspectFit"></image>
    </view>

    <!-- 标签选择器 -->
    <view class="tab-container">
      <view class="tab {{activeTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">今天</view>
      <view class="tab {{activeTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">历史</view>
    </view>

    <!-- 占位符，保持布局平衡 -->
    <view class="profile-icon" style="opacity: 0;"></view>
  </view>

  <!-- 日期显示 -->
  <view class="date-display">
    <text>{{currentDate}}</text>
    <text style="margin-left: 20rpx; color: rgba(255, 255, 255, 0.6);">{{todayLuck}}</text>
  </view>

  <!-- 滑动提示 -->
  <view class="swipe-tip">↑ 上下滑动切换卡片 ↓</view>

  <!-- 内容滚动区域 -->
  <swiper class="content-swiper" bindchange="onSwiperChange" current="{{currentIndex}}" duration="300" circular="true" display-multiple-items="1" easing-function="easeInOutCubic" vertical="true" previous-margin="0px" next-margin="0px">
    <!-- 玉匣记占卜 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true" enable-flex="true">
        <view class="role-card divination-card">
          <view class="role-title">
            <text>玉匣记占卜</text>
            <image class="role-icon" src="/assets/icons/divination.png"></image>
          </view>
          <view class="role-description">
            <text>古法占卜    趋吉避凶\n\n洞察先机    指引人生</text>
          </view>

          <!-- 占卜选项 -->
          <view class="divination-options">
            <view class="option-item" bindtap="goToLiurenDivination">
              <view class="option-icon">🔮</view>
              <view class="option-content">
                <view class="option-title">李淳风六壬时课</view>
                <view class="option-desc">事件占卜 · 时间选择</view>
              </view>
              <view class="option-arrow">></view>
            </view>

            <view class="option-item" bindtap="goToBaziPaipan">
              <view class="option-icon">🏛️</view>
              <view class="option-content">
                <view class="option-title">玉匣记八字排盘</view>
                <view class="option-desc">命理分析 · 人生指导</view>
              </view>
              <view class="option-arrow">></view>
            </view>
          </view>

          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="showDivinationOptions">
              <text>开始占卜</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 智能对话 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card chat-card" bindtap="goToChat">
          <view class="role-title">
            <text>智能对话</text>
            <image class="role-icon" src="/assets/icons/chat.png"></image>
          </view>
          <view class="role-description">
            <text>AI智慧助手    答疑解惑\n\n传统文化    现代科技</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="goToChat">
              <text>开始对话</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 心理测评 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card assessment-card" bindtap="goToAssessment">
          <view class="role-title">
            <text>心理测评</text>
            <image class="role-icon" src="/assets/icons/assessment.png"></image>
          </view>
          <view class="role-description">
            <text>专业测评    心理健康\n\n科学分析    个性洞察</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="goToAssessment">
              <text>开始测评</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 个人中心 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card profile-card" bindtap="goToProfile">
          <view class="role-title">
            <text>个人中心</text>
            <image class="role-icon" src="/assets/icons/profile-large.png"></image>
          </view>
          <view class="role-description">
            <text>设置管理    历史记录\n\n个人资料    偏好设置</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="goToProfile">
              <text>进入中心</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>

  <!-- 输入区域 -->
  <view class="input-area">
    <input class="message-input" placeholder="输入你的问题..." value="{{inputValue}}" bindinput="onInputChange" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="handleQuickInput">
      <image src="/assets/icons/send.png" mode="aspectFit"></image>
    </view>
    <view class="voice-button" bindtap="handleVoiceInput">
      <image src="/assets/icons/voice.png" mode="aspectFit"></image>
    </view>
  </view>
</view>