/**
 * 验证应期分析数据结构修复效果
 * 测试修复后的提取逻辑是否能正确处理实际的数据结构
 */

// 模拟实际的应期分析计算结果（基于实际代码结构）
const mockActualTimingResults = {
  marriage: {
    threshold_status: 'not_met',
    message: '当前能量阈值未达标，时机尚未成熟',
    confidence: 0.785, // 注意：这是小数形式
    energy_deficit: {
      percentage: 65.2,
      required: 0.75, // 注意：这是小数形式，需要*100
      met: false
    },
    estimated_year: '2026年3月'
  },
  promotion: {
    threshold_status: 'met',
    best_year: '2025年8月',
    confidence: 0.852,
    energy_analysis: {
      percentage: 82.3,
      required: 0.70,
      met: true
    }
  },
  wealth: {
    threshold_status: 'age_not_met',
    reason: '年龄不符合财运分析条件'
  },
  childbirth: {
    threshold_status: 'not_met',
    confidence: 0.623,
    energy_deficit: {
      percentage: 45.8,
      required: 0.60,
      met: false
    },
    estimated_year: '2027年5月'
  }
};

// 模拟修复后的提取方法
function extractEnergyThresholdsForUI(professionalResults) {
  console.log('🔍 应期分析数据结构调试:');
  console.log('  - professionalResults类型:', typeof professionalResults);
  console.log('  - professionalResults键:', Object.keys(professionalResults || {}));
  
  Object.keys(professionalResults || {}).forEach(key => {
    const result = professionalResults[key];
    console.log(`  - ${key}数据结构:`, {
      type: typeof result,
      keys: result ? Object.keys(result) : [],
      hasEnergyDeficit: !!(result && result.energy_deficit),
      hasEnergyAnalysis: !!(result && result.energy_analysis),
      hasRawAnalysis: !!(result && result.raw_analysis)
    });
  });
  
  const thresholds = {
    marriage_current_energy: 0,
    marriage_required_threshold: 0,
    marriage_met: false,
    marriage_estimated_year: '',
    promotion_current_energy: 0,
    promotion_required_threshold: 0,
    promotion_met: false,
    promotion_estimated_year: '',
    childbirth_current_energy: 0,
    childbirth_required_threshold: 0,
    childbirth_met: false,
    childbirth_estimated_year: '',
    wealth_current_energy: 0,
    wealth_required_threshold: 0,
    wealth_met: false,
    wealth_estimated_year: ''
  };

  // 从原始分析结果中提取能量阈值数据
  Object.keys(professionalResults).forEach(eventType => {
    const result = professionalResults[eventType];

    // 检查是否因年龄不符而无法分析
    if (result && result.threshold_status === 'age_not_met') {
      console.log(`⚠️ ${eventType}: 年龄不符，跳过分析`);
      thresholds[`${eventType}_current_energy`] = 0;
      thresholds[`${eventType}_required_threshold`] = 0;
      thresholds[`${eventType}_met`] = false;
      thresholds[`${eventType}_estimated_year`] = '';
      return;
    }

    if (result) {
      let userCurrentEnergy = 0;
      let requiredThreshold = 0;
      let actuallyMet = false;

      if (result.energy_deficit) {
        // 🔧 修复：适配实际的数据结构 - 未达标情况
        userCurrentEnergy = parseFloat(result.energy_deficit.percentage) || 0;
        requiredThreshold = parseFloat(result.energy_deficit.required * 100) || 0;
        actuallyMet = result.energy_deficit.met || false;

        console.log(`✅ ${eventType} energy_deficit数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
      } else if (result.energy_analysis) {
        // 🔧 修复：适配实际的数据结构 - 达标情况
        userCurrentEnergy = parseFloat(result.energy_analysis.percentage) || 0;
        requiredThreshold = parseFloat(result.energy_analysis.required * 100) || 0;
        actuallyMet = result.energy_analysis.met || false;

        console.log(`✅ ${eventType} energy_analysis数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
      } else {
        console.warn(`⚠️ ${eventType} 无法找到能量数据，使用默认值`);
      }

      // 确保数值在合理范围内
      const clampedUserEnergy = Math.min(Math.max(userCurrentEnergy, 0), 100);
      const clampedRequiredThreshold = Math.min(Math.max(requiredThreshold, 0), 100);

      thresholds[`${eventType}_current_energy`] = Math.round(clampedUserEnergy * 10) / 10;
      thresholds[`${eventType}_required_threshold`] = Math.round(clampedRequiredThreshold * 10) / 10;
      thresholds[`${eventType}_met`] = actuallyMet;

      // 🔧 设置预计年份（从真实计算结果中获取）
      if (result.best_year) {
        thresholds[`${eventType}_estimated_year`] = result.best_year;
      } else if (result.estimated_year) {
        thresholds[`${eventType}_estimated_year`] = result.estimated_year;
      }
    }
  });

  console.log('🎯 提取的能量阈值数据:', thresholds);
  return thresholds;
}

function extractTripleActivationForUI(professionalResults) {
  console.log('🔍 三重引动数据结构调试:');
  console.log('  - professionalResults:', professionalResults);
  
  const activation = {
    star_activation: '年龄阶段分析中...',
    palace_activation: '年龄阶段分析中...',
    shensha_activation: '年龄阶段分析中...',
    marriage_confidence: 0,
    promotion_confidence: 0,
    childbirth_confidence: 0,
    wealth_confidence: 0
  };

  // 检查是否有年龄不符的情况
  const hasAgeNotMet = Object.values(professionalResults).some(result =>
    result && result.threshold_status === 'age_not_met'
  );

  if (hasAgeNotMet) {
    activation.star_activation = '当前年龄阶段，星动分析暂不适用';
    activation.palace_activation = '当前年龄阶段，宫动分析暂不适用';
    activation.shensha_activation = '当前年龄阶段，神煞分析暂不适用';
    return activation;
  }

  // 提取置信度
  Object.keys(professionalResults).forEach(eventType => {
    const result = professionalResults[eventType];
    if (result) {
      let confidence = 70; // 默认置信度

      if (result.confidence !== undefined) {
        confidence = typeof result.confidence === 'number' ? result.confidence * 100 : parseFloat(result.confidence) || 70;
      }

      const finalConfidence = Math.round(confidence);
      activation[`${eventType}_confidence`] = finalConfidence;

      console.log(`✅ ${eventType} 置信度: ${finalConfidence}%`);
    }
  });

  // 生成真实的引动描述
  const hasMarriageData = professionalResults.marriage && (professionalResults.marriage.best_year || professionalResults.marriage.estimated_year);
  const hasPromotionData = professionalResults.promotion && professionalResults.promotion.best_year;
  
  if (hasMarriageData || hasPromotionData) {
    activation.star_activation = '天喜星、红鸾星引动，感情运势上升';
    activation.palace_activation = '夫妻宫得力，婚姻宫位活跃';
    activation.shensha_activation = '桃花煞化解，正缘神煞显现';
  }

  console.log('🎯 提取的三重引动数据:', activation);
  return activation;
}

// 测试函数
function testTimingAnalysisFix() {
  console.log('🧪 ===== 应期分析数据结构修复验证 =====\n');
  
  console.log('📋 模拟的实际应期分析结果:');
  console.log(JSON.stringify(mockActualTimingResults, null, 2));
  
  console.log('\n🧪 测试1: 提取能量阈值数据（修复后）');
  const energyThresholds = extractEnergyThresholdsForUI(mockActualTimingResults);
  
  console.log('\n🧪 测试2: 提取三重引动数据（修复后）');
  const tripleActivation = extractTripleActivationForUI(mockActualTimingResults);
  
  console.log('\n📊 修复效果分析:');
  
  // 分析能量阈值数据
  console.log('   能量阈值数据:');
  Object.keys(energyThresholds).forEach(key => {
    if (key.includes('_current_energy') || key.includes('_required_threshold')) {
      console.log(`     ${key}: ${energyThresholds[key]}`);
    }
  });
  
  // 分析预计年份
  console.log('   预计年份:');
  ['marriage', 'promotion', 'childbirth', 'wealth'].forEach(event => {
    const year = energyThresholds[`${event}_estimated_year`];
    console.log(`     ${event}: ${year || '无'}`);
  });
  
  // 分析置信度数据
  console.log('   置信度数据:');
  ['marriage', 'promotion', 'childbirth', 'wealth'].forEach(event => {
    const confidence = tripleActivation[`${event}_confidence`];
    console.log(`     ${event}: ${confidence}%`);
  });
  
  console.log('\n🎯 修复验证:');
  
  // 验证1: 能量数据是否被正确提取
  const hasRealEnergyData = energyThresholds.marriage_current_energy > 0 || 
                           energyThresholds.promotion_current_energy > 0;
  console.log('   能量数据提取:', hasRealEnergyData ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 置信度是否被正确提取
  const hasRealConfidenceData = tripleActivation.marriage_confidence > 0 || 
                               tripleActivation.promotion_confidence > 0;
  console.log('   置信度提取:', hasRealConfidenceData ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 预计年份是否被正确设置
  const hasEstimatedYears = energyThresholds.marriage_estimated_year || 
                           energyThresholds.promotion_estimated_year;
  console.log('   预计年份设置:', hasEstimatedYears ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 数据不再是全0
  const notAllZero = Object.values(energyThresholds).some(val => 
    typeof val === 'number' && val > 0
  );
  console.log('   摆脱全0数据:', notAllZero ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 修复效果总结:');
  const successCount = [hasRealEnergyData, hasRealConfidenceData, hasEstimatedYears, notAllZero].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/4`);
  console.log(`   修复状态: ${successCount >= 3 ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
  
  if (successCount >= 3) {
    console.log('\n✅ 应期分析数据结构修复成功！');
    console.log('💡 现在前端应该显示：');
    console.log('   - 真实的能量百分比（不再是0%）');
    console.log('   - 个性化的置信度（不再是固定值）');
    console.log('   - 真实的预计年份（不再是空白）');
    console.log('   - 基于实际计算的应期分析');
  } else {
    console.log('\n❌ 修复不完整，需要进一步调试');
  }
  
  return {
    success: successCount >= 3,
    energyThresholds,
    tripleActivation,
    details: {
      hasRealEnergyData,
      hasRealConfidenceData,
      hasEstimatedYears,
      notAllZero
    }
  };
}

// 运行测试
testTimingAnalysisFix();
