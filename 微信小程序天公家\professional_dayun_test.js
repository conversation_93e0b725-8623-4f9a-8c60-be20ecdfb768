// professional_dayun_test.js
// 专业级大运计算器测试套件

// 加载权威节气数据
const jieqiDataModule = require('./权威节气数据_前端就绪版.js');
global.FrontendReadyJieqiData = jieqiDataModule.FrontendReadyJieqiData;

const ProfessionalDayunCalculator = require('./utils/professional_dayun_calculator.js');

/**
 * 专业级大运计算器测试套件
 */
class ProfessionalDayunTestSuite {
  constructor() {
    this.calculator = new ProfessionalDayunCalculator();
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
  }
  
  /**
   * 运行单个测试
   */
  runTest(testName, testFunction) {
    this.totalTests++;
    console.log(`\n🧪 测试: ${testName}`);
    
    try {
      const result = testFunction();
      if (result) {
        console.log(`✅ 通过: ${testName}`);
        this.passedTests++;
        this.testResults.push({ name: testName, status: 'PASS', error: null });
      } else {
        console.log(`❌ 失败: ${testName}`);
        this.testResults.push({ name: testName, status: 'FAIL', error: 'Test returned false' });
      }
    } catch (error) {
      console.log(`❌ 错误: ${testName} - ${error.message}`);
      this.testResults.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }
  
  /**
   * 测试计算器初始化
   */
  testCalculatorInitialization() {
    return this.runTest('计算器初始化', () => {
      const status = this.calculator.getCalculatorStatus();
      console.log('计算器状态:', status);
      return status.name === 'ProfessionalDayunCalculator' && status.status === 'ready';
    });
  }
  
  /**
   * 测试大运方向判断
   */
  testDayunDirection() {
    return this.runTest('大运方向判断', () => {
      const testCases = [
        { gender: '男', yearGan: '甲', expected: true, rule: '阳男顺行' },
        { gender: '男', yearGan: '乙', expected: false, rule: '阴男逆行' },
        { gender: '女', yearGan: '甲', expected: false, rule: '阳女逆行' },
        { gender: '女', yearGan: '乙', expected: true, rule: '阴女顺行' }
      ];
      
      let allPassed = true;
      
      for (const testCase of testCases) {
        const result = this.calculator.determineDayunDirection(testCase.gender, testCase.yearGan);
        console.log(`${testCase.gender}命${testCase.yearGan}年: ${result.rule} (${result.isForward ? '顺行' : '逆行'})`);
        
        if (result.isForward !== testCase.expected) {
          console.log(`❌ 方向判断错误，预期: ${testCase.expected ? '顺行' : '逆行'}`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试大运序列生成
   */
  testDayunSequence() {
    return this.runTest('大运序列生成', () => {
      const monthPillar = { gan: '甲', zhi: '午' };
      
      // 测试顺行
      const forwardSequence = this.calculator.generateDayunSequence(monthPillar, true, 5);
      console.log('顺行序列:', forwardSequence.map(d => d.pillar).join(' → '));
      
      // 测试逆行
      const backwardSequence = this.calculator.generateDayunSequence(monthPillar, false, 5);
      console.log('逆行序列:', backwardSequence.map(d => d.pillar).join(' → '));
      
      // 验证序列正确性
      const expectedForward = ['乙未', '丙申', '丁酉', '戊戌', '己亥'];
      const expectedBackward = ['癸巳', '壬辰', '辛卯', '庚寅', '己丑'];
      
      const forwardCorrect = forwardSequence.every((d, i) => d.pillar === expectedForward[i]);
      const backwardCorrect = backwardSequence.every((d, i) => d.pillar === expectedBackward[i]);
      
      if (!forwardCorrect) {
        console.log('❌ 顺行序列错误');
      }
      if (!backwardCorrect) {
        console.log('❌ 逆行序列错误');
      }
      
      return forwardCorrect && backwardCorrect;
    });
  }
  
  /**
   * 测试完整大运计算
   */
  testCompleteDayunCalculation() {
    return this.runTest('完整大运计算', () => {
      // 使用测试数据：2021年6月24日19:30，北京
      const baziData = {
        gender: '男',
        yearPillar: { gan: '辛', zhi: '丑' },
        monthPillar: { gan: '甲', zhi: '午' },
        dayPillar: { gan: '癸', zhi: '卯' },
        timePillar: { gan: '壬', zhi: '戌' }
      };
      
      const birthInfo = {
        year: 2021,
        month: 6,
        day: 24,
        hour: 19,
        minute: 30
      };
      
      const result = this.calculator.calculateProfessionalDayun(baziData, birthInfo, '北京');
      
      console.log('起运年龄:', `${result.calculation.qiyunResult.qiyunAge.years}岁${result.calculation.qiyunResult.qiyunAge.months}个月`);
      console.log('大运方向:', result.calculation.direction.description);
      console.log('首步大运:', result.calculation.dayunSequence[0].pillar);
      
      // 验证结果结构完整性
      return result.input && result.calculation && result.analysis && result.metadata;
    });
  }
  
  /**
   * 测试起运时间精确计算
   */
  testQiyunTimeCalculation() {
    return this.runTest('起运时间精确计算', () => {
      // 测试不同出生时间的起运计算
      const testCases = [
        {
          name: '春季出生',
          birthTime: new Date(2021, 2, 15, 12, 0, 0), // 3月15日
          isForward: true
        },
        {
          name: '夏季出生',
          birthTime: new Date(2021, 5, 24, 19, 30, 0), // 6月24日
          isForward: true
        },
        {
          name: '秋季出生',
          birthTime: new Date(2021, 8, 15, 14, 0, 0), // 9月15日
          isForward: false
        }
      ];
      
      let allPassed = true;
      
      for (const testCase of testCases) {
        try {
          const result = this.calculator.calculateQiyunTime(testCase.birthTime, testCase.isForward);
          console.log(`${testCase.name}: ${result.qiyunAge.years}岁${result.qiyunAge.months}个月 (${result.daysDifference.toFixed(2)}天)`);
          
          // 验证起运年龄合理性（通常0-8岁之间）
          if (result.qiyunAge.years < 0 || result.qiyunAge.years > 10) {
            console.log(`❌ 起运年龄异常: ${result.qiyunAge.years}岁`);
            allPassed = false;
          }
        } catch (error) {
          console.log(`❌ ${testCase.name}计算失败: ${error.message}`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试当前大运分析
   */
  testCurrentDayunAnalysis() {
    return this.runTest('当前大运分析', () => {
      // 模拟一个已起运的情况
      const qiyunResult = {
        qiyunDate: new Date(2023, 0, 1), // 2023年1月1日起运
        qiyunAge: { years: 2, months: 0, days: 0, hours: 0 }
      };
      
      const dayunSequence = [
        { sequence: 1, pillar: '乙未', startAge: 0, endAge: 9 },
        { sequence: 2, pillar: '丙申', startAge: 10, endAge: 19 },
        { sequence: 3, pillar: '丁酉', startAge: 20, endAge: 29 }
      ];
      
      const timeline = this.calculator.calculateDayunTimeline(qiyunResult, dayunSequence);
      const currentDayun = this.calculator.analyzeCurrentDayun(timeline, new Date(2025, 6, 1)); // 2025年7月1日
      
      console.log('当前大运:', currentDayun.pillar);
      console.log('大运进度:', currentDayun.progressDescription);
      console.log('剩余时间:', currentDayun.remainingDescription);
      
      return currentDayun.status === 'current' && currentDayun.pillar && currentDayun.progress >= 0;
    });
  }
  
  /**
   * 测试真太阳时修正集成
   */
  testTrueSolarTimeIntegration() {
    return this.runTest('真太阳时修正集成', () => {
      const baziData = {
        gender: '女',
        yearPillar: { gan: '辛', zhi: '丑' },
        monthPillar: { gan: '甲', zhi: '午' },
        dayPillar: { gan: '癸', zhi: '卯' },
        timePillar: { gan: '壬', zhi: '戌' }
      };
      
      const birthInfo = {
        year: 2021,
        month: 6,
        day: 24,
        hour: 16,
        minute: 45
      };
      
      // 测试乌鲁木齐（经度差较大）
      const result = this.calculator.calculateProfessionalDayun(baziData, birthInfo, '乌鲁木齐');
      
      console.log('原始时间:', result.input.timeCorrection.input.beijingTimeString);
      console.log('修正时间:', result.input.timeCorrection.result.trueSolarTimeString);
      console.log('时间修正:', `${result.input.timeCorrection.calculation.timeOffsetMinutes.toFixed(1)}分钟`);
      
      // 验证时间修正是否生效
      const hasCorrection = Math.abs(result.input.timeCorrection.calculation.timeOffsetMinutes) > 1;
      const correctionApplied = result.input.originalTime.getTime() !== result.input.correctedTime.getTime();
      
      return hasCorrection && correctionApplied;
    });
  }
  
  /**
   * 测试边界条件
   */
  testBoundaryConditions() {
    return this.runTest('边界条件测试', () => {
      const baziData = {
        gender: '男',
        yearPillar: { gan: '甲', zhi: '子' },
        monthPillar: { gan: '丙', zhi: '寅' },
        dayPillar: { gan: '戊', zhi: '辰' },
        timePillar: { gan: '庚', zhi: '午' }
      };
      
      // 测试极端日期
      const extremeCases = [
        { year: 1900, month: 1, day: 1, hour: 0, minute: 0 },
        { year: 2025, month: 12, day: 31, hour: 23, minute: 59 },
        { year: 2021, month: 2, day: 4, hour: 12, minute: 0 } // 立春附近
      ];
      
      let allPassed = true;
      
      for (const birthInfo of extremeCases) {
        try {
          const result = this.calculator.calculateProfessionalDayun(baziData, birthInfo, 120);
          console.log(`${birthInfo.year}年测试: 起运${result.calculation.qiyunResult.qiyunAge.years}岁`);
        } catch (error) {
          console.log(`❌ ${birthInfo.year}年测试失败: ${error.message}`);
          allPassed = false;
        }
      }
      
      return allPassed;
    });
  }
  
  /**
   * 测试性能
   */
  testPerformance() {
    return this.runTest('性能测试', () => {
      const baziData = {
        gender: '男',
        yearPillar: { gan: '辛', zhi: '丑' },
        monthPillar: { gan: '甲', zhi: '午' },
        dayPillar: { gan: '癸', zhi: '卯' },
        timePillar: { gan: '壬', zhi: '戌' }
      };
      
      const birthInfo = {
        year: 2021,
        month: 6,
        day: 24,
        hour: 19,
        minute: 30
      };
      
      const startTime = Date.now();
      
      // 执行100次计算
      for (let i = 0; i < 100; i++) {
        this.calculator.calculateProfessionalDayun(baziData, birthInfo, '北京');
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`100次计算耗时: ${duration}ms`);
      console.log(`平均每次: ${(duration / 100).toFixed(2)}ms`);
      
      // 性能要求：平均每次计算不超过100ms
      return (duration / 100) < 100;
    });
  }
  
  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🔮 专业级大运计算器测试套件');
    console.log('='.repeat(60));
    
    // 执行所有测试
    this.testCalculatorInitialization();
    this.testDayunDirection();
    this.testDayunSequence();
    this.testCompleteDayunCalculation();
    this.testQiyunTimeCalculation();
    this.testCurrentDayunAnalysis();
    this.testTrueSolarTimeIntegration();
    this.testBoundaryConditions();
    this.testPerformance();
    
    // 生成测试报告
    this.generateReport();
  }
  
  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.totalTests - this.passedTests}`);
    console.log(`成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
    
    // 详细结果
    console.log('\n📋 详细结果:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    // 评级
    const successRate = (this.passedTests / this.totalTests) * 100;
    let grade = '';
    if (successRate >= 95) grade = '🏆 优秀';
    else if (successRate >= 85) grade = '🌟 良好';
    else if (successRate >= 70) grade = '⚠️ 及格';
    else grade = '❌ 不及格';
    
    console.log(`\n🎯 测试评级: ${grade}`);
    console.log('='.repeat(60));
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new ProfessionalDayunTestSuite();
  testSuite.runAllTests();
}

module.exports = ProfessionalDayunTestSuite;
