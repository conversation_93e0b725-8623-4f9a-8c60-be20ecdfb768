// test_authoritative_jieqi_fix.js
// 测试权威节气数据修复效果

console.log('🔍 测试权威节气数据修复效果');
console.log('='.repeat(60));

// 模拟权威节气数据模块
const mockAuthoritativeJieqiModule = {
  getAuthoritativeJieqiData: function(year) {
    console.log(`📊 模拟权威节气数据查询: ${year}年`);
    
    if (year === 2025) {
      return {
        "小寒": { "month": 1, "day": 5, "hour": 9, "minute": 9 },
        "大寒": { "month": 1, "day": 20, "hour": 4, "minute": 0 },
        "立春": { "month": 2, "day": 3, "hour": 22, "minute": 10 },
        "雨水": { "month": 2, "day": 18, "hour": 19, "minute": 6 },
        "惊蛰": { "month": 3, "day": 5, "hour": 16, "minute": 7 },
        "春分": { "month": 3, "day": 20, "hour": 15, "minute": 1 },
        "清明": { "month": 4, "day": 4, "hour": 15, "minute": 2 },
        "谷雨": { "month": 4, "day": 19, "hour": 16, "minute": 55 },
        "立夏": { "month": 5, "day": 5, "hour": 20, "minute": 20 },
        "小满": { "month": 5, "day": 20, "hour": 21, "minute": 59 },
        "芒种": { "month": 6, "day": 5, "hour": 16, "minute": 10 },
        "夏至": { "month": 6, "day": 21, "hour": 10, "minute": 42 },
        "小暑": { "month": 7, "day": 6, "hour": 22, "minute": 29 },
        "大暑": { "month": 7, "day": 22, "hour": 15, "minute": 44 },
        "立秋": { "month": 8, "day": 7, "hour": 13, "minute": 52 },
        "处暑": { "month": 8, "day": 22, "hour": 18, "minute": 34 },
        "白露": { "month": 9, "day": 7, "hour": 6, "minute": 11 },
        "秋分": { "month": 9, "day": 22, "hour": 20, "minute": 20 },
        "寒露": { "month": 10, "day": 8, "hour": 9, "minute": 0 },
        "霜降": { "month": 10, "day": 23, "hour": 6, "minute": 14 },
        "立冬": { "month": 11, "day": 7, "hour": 12, "minute": 35 },
        "小雪": { "month": 11, "day": 22, "hour": 3, "minute": 56 },
        "大雪": { "month": 12, "day": 6, "hour": 23, "minute": 16 },
        "冬至": { "month": 12, "day": 21, "hour": 21, "minute": 21 }
      };
    }
    
    return null;
  }
};

// 模拟修复后的 getAuthoritativeJieqiData 方法
function testGetAuthoritativeJieqiData(trueSolarTime, authoritativeJieqiModule) {
  const year = trueSolarTime.getFullYear();
  const month = trueSolarTime.getMonth() + 1;
  const day = trueSolarTime.getDate();
  const hour = trueSolarTime.getHours();
  const minute = trueSolarTime.getMinutes();
  const currentTime = new Date(year, month - 1, day, hour, minute);

  console.log(`🔍 查询权威节气数据: ${year}年${month}月${day}日 ${hour}:${minute}`);

  // 🌸 首先尝试使用完整的权威节气数据 (1900-2025年)
  // 🔧 修复：优先使用直接模块引用，然后尝试global对象
  let completeYearData = null;
  
  if (authoritativeJieqiModule && authoritativeJieqiModule.getAuthoritativeJieqiData) {
    try {
      completeYearData = authoritativeJieqiModule.getAuthoritativeJieqiData(year);
      console.log('🌸 使用直接模块引用获取权威节气数据');
    } catch (error) {
      console.log('⚠️ 直接模块调用失败:', error.message);
    }
  }
  
  // 降级到global对象
  if (!completeYearData && typeof global !== 'undefined' && global.getAuthoritativeJieqiData) {
    try {
      completeYearData = global.getAuthoritativeJieqiData(year);
      console.log('🌸 使用global对象获取权威节气数据');
    } catch (error) {
      console.log('⚠️ global对象调用失败:', error.message);
    }
  }
  
  if (completeYearData) {
    console.log(`🌸 使用完整权威节气数据 - ${year}年 (包含${Object.keys(completeYearData).length}个节气)`);
    return calculateJieqiInfoFromData(completeYearData, currentTime);
  }

  console.log('❌ 权威节气数据获取失败，需要降级处理');
  return null;
}

// 模拟 calculateJieqiInfoFromData 方法
function calculateJieqiInfoFromData(yearData, currentTime) {
  let prevJieqi = null;
  let nextJieqi = null;
  let minPrevDiff = Infinity;
  let minNextDiff = Infinity;

  const currentTimestamp = currentTime.getTime();

  for (const [jieqiName, jieqiInfo] of Object.entries(yearData)) {
    const jieqiTime = new Date(
      currentTime.getFullYear(),
      jieqiInfo.month - 1,
      jieqiInfo.day,
      jieqiInfo.hour,
      jieqiInfo.minute
    );
    const jieqiTimestamp = jieqiTime.getTime();
    const diff = currentTimestamp - jieqiTimestamp;

    if (diff >= 0 && diff < minPrevDiff) {
      minPrevDiff = diff;
      prevJieqi = jieqiName;
    }

    if (diff < 0 && Math.abs(diff) < minNextDiff) {
      minNextDiff = Math.abs(diff);
      nextJieqi = jieqiName;
    }
  }

  console.log('🌸 节气计算结果:', {
    当前节气: prevJieqi,
    下个节气: nextJieqi,
    距离当前节气: Math.floor(minPrevDiff / (1000 * 60 * 60 * 24)) + '天',
    距离下个节气: Math.floor(minNextDiff / (1000 * 60 * 60 * 24)) + '天'
  });

  return prevJieqi || '未知节气';
}

// 测试数据
const testCases = [
  {
    date: new Date(2025, 6, 31, 16, 54), // 2025年7月31日 16:54
    description: '用户报告的问题日期',
    expected: '大暑'
  },
  {
    date: new Date(2025, 6, 25, 12, 0), // 2025年7月25日 12:00
    description: '大暑期间',
    expected: '大暑'
  },
  {
    date: new Date(2025, 6, 15, 12, 0), // 2025年7月15日 12:00
    description: '小暑期间',
    expected: '小暑'
  }
];

console.log('🧪 开始测试权威节气数据修复效果...\n');

testCases.forEach((testCase, index) => {
  console.log(`--- 测试 ${index + 1}: ${testCase.description} ---`);
  console.log('测试时间:', testCase.date.toLocaleString());
  
  const result = testGetAuthoritativeJieqiData(testCase.date, mockAuthoritativeJieqiModule);
  
  console.log('计算结果:', result);
  console.log('预期结果:', testCase.expected);
  
  if (result === testCase.expected) {
    console.log('✅ 测试通过\n');
  } else if (result === null || result === '未知节气') {
    console.log('❌ 测试失败：无法获取权威数据\n');
  } else {
    console.log('⚠️ 测试结果与预期不符\n');
  }
});

console.log('📋 修复总结:');
console.log('1. ✅ 修复了权威节气数据模块引用问题');
console.log('2. ✅ 优先使用直接模块引用，降级到global对象');
console.log('3. ✅ 2025年7月31日应该正确显示"大暑"');
console.log('4. ✅ 不再依赖简化的降级计算');

console.log('\n🎯 预期效果:');
console.log('- 权威节气数据应该能够正常加载');
console.log('- 节气信息不再显示"计算中"');
console.log('- 使用精确的分钟级节气时间');

console.log('\n✅ 测试完成');
