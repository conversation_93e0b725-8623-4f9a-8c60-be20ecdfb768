// fix_data_flow.js
// 修复前端数据接收问题的完整解决方案

console.log('🔧 开始修复前端数据接收问题...');

const fs = require('fs');

function analyzeDataFlow() {
  console.log('\n📊 数据流分析：');
  console.log('==========================================');
  
  console.log('\n🔍 当前数据流设计：');
  console.log('1. 输入页面 (pages/bazi-input/index.js)');
  console.log('   ├── 用户输入出生信息');
  console.log('   ├── 前端计算八字');
  console.log('   ├── 调用增强分析API (可选)');
  console.log('   ├── 保存到本地存储 (bazi_frontend_result)');
  console.log('   └── 跳转到结果页面');
  console.log('');
  console.log('2. 结果页面 (pages/bazi-result/index.js)');
  console.log('   ├── 优先级1: 从URL参数获取数据');
  console.log('   ├── 优先级2: 从本地存储获取数据');
  console.log('   ├── 优先级3: 通过ID从本地存储获取');
  console.log('   └── 最后: 使用测试模式');
  
  console.log('\n⚠️  发现的问题：');
  console.log('1. API端口配置不统一 (已修复)');
  console.log('2. 可能存在数据保存/读取问题');
  console.log('3. 缺少详细的错误日志');
  console.log('4. 没有数据完整性验证');
}

function generateDataFlowFix() {
  console.log('\n🛠️  数据流修复方案：');
  console.log('==========================================');
  
  const fixes = [
    {
      file: 'pages/bazi-result/index.js',
      issue: '增强数据加载日志和错误处理',
      solution: '添加详细的数据检查和日志记录'
    },
    {
      file: 'pages/bazi-input/index.js', 
      issue: '确保数据正确保存到本地存储',
      solution: '添加数据保存验证和错误处理'
    },
    {
      file: 'utils/config.js',
      issue: 'API配置统一',
      solution: '已修复：统一使用8000端口'
    }
  ];
  
  fixes.forEach((fix, index) => {
    console.log(`\n${index + 1}. ${fix.file}`);
    console.log(`   问题: ${fix.issue}`);
    console.log(`   解决: ${fix.solution}`);
  });
}

function createDataValidationCode() {
  console.log('\n📝 生成数据验证代码：');
  
  const validationCode = `
// 数据验证和调试工具
const DataValidator = {
  // 验证八字数据完整性
  validateBaziData: function(data) {
    console.log('🔍 验证八字数据:', data);
    
    if (!data) {
      console.error('❌ 数据为空');
      return false;
    }
    
    const required = ['userInfo', 'baziInfo', 'fiveElements'];
    const missing = required.filter(key => !data[key]);
    
    if (missing.length > 0) {
      console.error('❌ 缺少必要字段:', missing);
      return false;
    }
    
    console.log('✅ 数据验证通过');
    return true;
  },
  
  // 验证本地存储数据
  validateStorageData: function() {
    console.log('🔍 验证本地存储数据:');
    
    const keys = [
      'bazi_frontend_result',
      'bazi_birth_info', 
      'bazi_analysis_mode',
      'bazi_result_id'
    ];
    
    const storage = {};
    keys.forEach(key => {
      try {
        storage[key] = wx.getStorageSync(key);
        console.log(\`  \${key}: \${storage[key] ? '存在' : '不存在'}\`);
      } catch (error) {
        console.error(\`  \${key}: 读取失败 - \${error.message}\`);
        storage[key] = null;
      }
    });
    
    return storage;
  },
  
  // 清理本地存储
  clearStorageData: function() {
    console.log('🧹 清理本地存储数据');
    const keys = [
      'bazi_frontend_result',
      'bazi_birth_info',
      'bazi_analysis_mode', 
      'bazi_result_id'
    ];
    
    keys.forEach(key => {
      try {
        wx.removeStorageSync(key);
        console.log(\`  已清理: \${key}\`);
      } catch (error) {
        console.error(\`  清理失败: \${key} - \${error.message}\`);
      }
    });
  }
};

// 导出验证工具
module.exports = DataValidator;
`;
  
  console.log('✅ 数据验证代码已生成');
  return validationCode;
}

function generateDebugInstructions() {
  console.log('\n🔍 调试指导：');
  console.log('==========================================');
  
  console.log('\n📱 在微信开发者工具中调试：');
  console.log('1. 打开控制台 (Console)');
  console.log('2. 清理本地存储：');
  console.log('   wx.clearStorageSync()');
  console.log('');
  console.log('3. 重新进行八字排盘：');
  console.log('   - 进入输入页面');
  console.log('   - 输入出生信息');
  console.log('   - 点击开始排盘');
  console.log('   - 观察控制台日志');
  console.log('');
  console.log('4. 检查数据保存：');
  console.log('   wx.getStorageSync("bazi_frontend_result")');
  console.log('   wx.getStorageSync("bazi_birth_info")');
  console.log('');
  console.log('5. 检查结果页面：');
  console.log('   - 观察页面加载日志');
  console.log('   - 检查数据绑定是否正确');
  
  console.log('\n🌐 检查API服务：');
  console.log('1. 确认天公师父服务运行在8000端口');
  console.log('2. 浏览器访问: http://localhost:8000/health');
  console.log('3. 检查API响应格式');
  
  console.log('\n📊 数据流追踪：');
  console.log('1. 输入页面保存数据时的日志');
  console.log('2. 结果页面读取数据时的日志');
  console.log('3. 数据传递过程中的错误信息');
}

function createFixSummary() {
  console.log('\n✅ 修复总结：');
  console.log('==========================================');
  
  console.log('\n🔧 已完成的修复：');
  console.log('✅ 1. 统一API端口配置 (8000端口)');
  console.log('✅ 2. 修复占卜系统中的硬编码端口');
  console.log('✅ 3. 删除5001端口的引用');
  
  console.log('\n🎯 下一步操作：');
  console.log('1. 重启微信开发者工具');
  console.log('2. 确保天公师父服务运行在8000端口');
  console.log('3. 清理本地存储数据');
  console.log('4. 重新进行八字排盘测试');
  console.log('5. 观察控制台日志，确认数据流正常');
  
  console.log('\n🚨 如果问题仍然存在：');
  console.log('1. 检查天公师父API服务是否正常运行');
  console.log('2. 验证API响应格式是否正确');
  console.log('3. 检查前端数据保存逻辑');
  console.log('4. 确认页面跳转参数传递正确');
}

// 执行修复流程
console.log('🚀 开始执行数据流修复...');
analyzeDataFlow();
generateDataFlowFix();
const validationCode = createDataValidationCode();
generateDebugInstructions();
createFixSummary();

console.log('\n🎉 数据流修复方案已生成！');
console.log('💡 建议按照上述步骤逐一验证和修复');

// 保存验证代码到文件
try {
  fs.writeFileSync('utils/data_validator.js', validationCode);
  console.log('✅ 数据验证工具已保存到 utils/data_validator.js');
} catch (error) {
  console.log('⚠️  无法保存验证工具文件:', error.message);
}
