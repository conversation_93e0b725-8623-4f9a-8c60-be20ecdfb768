/**
 * 验证WXML结构修复结果
 * 检查标签配对和结构层次是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证WXML结构修复结果');
console.log('='.repeat(50));
console.log('');

// 读取WXML文件内容
const wxmlPath = path.join(__dirname, 'pages/bazi-result/index.wxml');
let content = '';

try {
  content = fs.readFileSync(wxmlPath, 'utf8');
  console.log('✅ 成功读取WXML文件');
} catch (error) {
  console.log('❌ 读取WXML文件失败:', error.message);
  process.exit(1);
}

console.log('');

// 分析标签配对
function analyzeTagPairing(content) {
  console.log('📊 标签配对分析：');
  console.log('='.repeat(20));
  
  const lines = content.split('\n');
  const stack = [];
  const tagCounts = {};
  let errors = [];
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // 匹配开始标签
    const openTags = line.match(/<(\w+(?:-\w+)*)[^>]*(?<!\/)\s*>/g);
    if (openTags) {
      openTags.forEach(tag => {
        const tagName = tag.match(/<(\w+(?:-\w+)*)/)[1];
        stack.push({ tag: tagName, line: lineNum });
        tagCounts[tagName] = (tagCounts[tagName] || 0) + 1;
      });
    }
    
    // 匹配结束标签
    const closeTags = line.match(/<\/(\w+(?:-\w+)*)>/g);
    if (closeTags) {
      closeTags.forEach(tag => {
        const tagName = tag.match(/<\/(\w+(?:-\w+)*)/)[1];
        
        if (stack.length === 0) {
          errors.push(`第${lineNum}行: 多余的结束标签 </${tagName}>`);
          return;
        }
        
        const lastOpen = stack.pop();
        if (lastOpen.tag !== tagName) {
          errors.push(`第${lineNum}行: 标签不匹配，期望 </${lastOpen.tag}>，实际 </${tagName}>`);
        }
        
        tagCounts[tagName] = (tagCounts[tagName] || 0) - 1;
      });
    }
    
    // 匹配自闭合标签
    const selfClosingTags = line.match(/<(\w+(?:-\w+)*)[^>]*\/>/g);
    if (selfClosingTags) {
      selfClosingTags.forEach(tag => {
        const tagName = tag.match(/<(\w+(?:-\w+)*)/)[1];
        // 自闭合标签不影响配对
      });
    }
  });
  
  // 检查未闭合的标签
  if (stack.length > 0) {
    stack.forEach(item => {
      errors.push(`第${item.line}行: 未闭合的标签 <${item.tag}>`);
    });
  }
  
  // 显示结果
  if (errors.length === 0) {
    console.log('✅ 所有标签配对正确');
  } else {
    console.log('❌ 发现标签配对错误:');
    errors.forEach(error => {
      console.log(`   ${error}`);
    });
  }
  
  console.log('');
  console.log('📈 标签统计:');
  Object.entries(tagCounts).forEach(([tag, count]) => {
    const status = count === 0 ? '✅' : '❌';
    console.log(`   ${status} ${tag}: ${count === 0 ? '配对正确' : `不平衡(${count})`}`);
  });
  
  return errors.length === 0;
}

// 分析结构层次
function analyzeStructure(content) {
  console.log('');
  console.log('🏗️ 结构层次分析：');
  console.log('='.repeat(20));
  
  const lines = content.split('\n');
  let currentIndent = 0;
  let maxIndent = 0;
  let structureValid = true;
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    const trimmed = line.trim();
    
    if (trimmed.startsWith('<!--') || trimmed === '') {
      return; // 跳过注释和空行
    }
    
    const indent = line.length - line.trimStart().length;
    
    if (trimmed.startsWith('</')) {
      // 结束标签，缩进应该减少
      currentIndent -= 2;
    }
    
    if (Math.abs(indent - currentIndent) > 2) {
      console.log(`⚠️  第${lineNum}行: 缩进异常 (期望${currentIndent}, 实际${indent})`);
      structureValid = false;
    }
    
    if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.endsWith('/>')) {
      // 开始标签，缩进应该增加
      currentIndent += 2;
      maxIndent = Math.max(maxIndent, currentIndent);
    }
  });
  
  if (structureValid) {
    console.log('✅ 结构层次正确');
  } else {
    console.log('❌ 结构层次存在问题');
  }
  
  console.log(`📏 最大嵌套深度: ${maxIndent / 2} 层`);
  
  return structureValid;
}

// 检查关键结构
function checkKeyStructures(content) {
  console.log('');
  console.log('🔑 关键结构检查：');
  console.log('='.repeat(20));
  
  const checks = [
    {
      name: 'scroll-view 标签',
      pattern: /<scroll-view[\s\S]*?<\/scroll-view>/,
      required: true
    },
    {
      name: '四柱八字分析模块',
      pattern: /四柱八字分析[\s\S]*?<\/view>/,
      required: true
    },
    {
      name: '十神分析子模块',
      pattern: /十神分析[\s\S]*?<\/view>/,
      required: true
    },
    {
      name: '长生十二宫子模块',
      pattern: /长生十二宫[\s\S]*?<\/view>/,
      required: true
    },
    {
      name: 'tab-panel 结构',
      pattern: /class="tab-panel[\s\S]*?<\/view>/g,
      required: true
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    const matches = content.match(check.pattern);
    const passed = matches !== null;
    const status = passed ? '✅' : '❌';
    
    console.log(`   ${status} ${check.name}: ${passed ? '存在' : '缺失'}`);
    
    if (!passed && check.required) {
      allPassed = false;
    }
  });
  
  return allPassed;
}

// 执行所有检查
console.log('🚀 开始结构验证...');
console.log('');

const tagPairingValid = analyzeTagPairing(content);
const structureValid = analyzeStructure(content);
const keyStructuresValid = checkKeyStructures(content);

console.log('');
console.log('📋 验证总结：');
console.log('='.repeat(15));

console.log(`✅ 标签配对: ${tagPairingValid ? '通过' : '失败'}`);
console.log(`✅ 结构层次: ${structureValid ? '通过' : '失败'}`);
console.log(`✅ 关键结构: ${keyStructuresValid ? '通过' : '失败'}`);

const overallValid = tagPairingValid && structureValid && keyStructuresValid;

console.log('');
if (overallValid) {
  console.log('🎉 WXML结构验证通过！');
  console.log('✅ 所有标签配对正确');
  console.log('✅ 结构层次合理');
  console.log('✅ 关键模块完整');
  console.log('');
  console.log('🚀 修复成功，可以正常编译运行！');
} else {
  console.log('❌ WXML结构验证失败！');
  console.log('需要进一步修复结构问题');
}

console.log('');
console.log('📊 文件统计：');
console.log(`   总行数: ${content.split('\n').length}`);
console.log(`   文件大小: ${(content.length / 1024).toFixed(2)} KB`);

console.log('');
console.log('✅ 验证完成！');
