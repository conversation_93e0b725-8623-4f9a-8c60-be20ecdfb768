/**
 * 调试前端数据问题
 * 检查为什么WXML模板存在但模块不显示
 */

console.log('🔍 调试前端数据问题');
console.log('='.repeat(60));

// 1. 检查WXML模板条件
console.log('\n📋 1. WXML模板条件检查:');
console.log('-'.repeat(40));

const wxmlConditions = [
  { module: '纳音分析', condition: 'baziResult.nayin', line: 329 },
  { module: '自坐分析', condition: 'baziResult.selfSittingAnalysis', line: 355 },
  { module: '长生十二宫', condition: 'baziResult.changshengAnalysis', line: 442 },
  { module: '空亡分析', condition: 'baziResult.basicInfo.kong_wang', line: 480 },
  { module: '命卦分析', condition: 'baziResult.basicInfo.ming_gua', line: 508 }
];

wxmlConditions.forEach(item => {
  console.log(`✅ ${item.module}: wx:if="{{${item.condition}}}" (第${item.line}行)`);
});

// 2. 模拟实际数据结构检查
console.log('\n📊 2. 数据结构问题分析:');
console.log('-'.repeat(40));

// 模拟可能的数据问题
const possibleDataIssues = [
  {
    issue: 'nayin 数据为 null 或 undefined',
    check: 'baziResult.nayin',
    solution: '检查 calculateNayin 函数返回值'
  },
  {
    issue: 'selfSittingAnalysis 数据为空字符串',
    check: 'baziResult.selfSittingAnalysis',
    solution: '检查 calculateSelfSitting 函数返回值'
  },
  {
    issue: 'changshengAnalysis 数据结构错误',
    check: 'baziResult.changshengAnalysis',
    solution: '检查 calculateChangsheng 函数返回值'
  },
  {
    issue: 'basicInfo 对象不存在',
    check: 'baziResult.basicInfo',
    solution: '检查 basicInfo 对象是否正确创建'
  },
  {
    issue: 'kong_wang 数据为 null',
    check: 'baziResult.basicInfo.kong_wang',
    solution: '检查 calculateKongWang 函数返回值'
  },
  {
    issue: 'ming_gua 数据为 null',
    check: 'baziResult.basicInfo.ming_gua',
    solution: '检查 calculateMingGua 函数返回值'
  }
];

possibleDataIssues.forEach((issue, index) => {
  console.log(`${index + 1}. 问题: ${issue.issue}`);
  console.log(`   检查: ${issue.check}`);
  console.log(`   解决: ${issue.solution}\n`);
});

// 3. 创建测试数据验证
console.log('\n🧪 3. 测试数据验证:');
console.log('-'.repeat(40));

// 模拟正确的数据结构
const correctDataStructure = {
  // 基础数据（已显示的3个模块）
  formatted: {
    year: '丙戌',
    month: '乙未', 
    day: '癸丑',
    hour: '己未',
    full: '丙戌 乙未 癸丑 己未'
  },
  
  tenGods: {
    year_star: '正财',
    month_star: '食神',
    day_star: '日主',
    hour_star: '正官'
  },
  
  cangganAnalysis: {
    year_pillar: {
      main_qi: '戊',
      hidden_gan: '戊、辛、丁',
      ten_gods: '正官、偏印、偏财'
    }
  },
  
  // 缺失的5个模块数据
  nayin: {
    year_pillar: '屋上土',
    month_pillar: '砂中金',
    day_pillar: '桑柘木',
    hour_pillar: '天上火'
  },
  
  selfSittingAnalysis: '癸水坐丑土，正官坐支，品格高尚，事业有成，贵人相助',
  
  changshengAnalysis: {
    year_pillar: '衰',
    month_pillar: '墓',
    day_pillar: '冠带',
    hour_pillar: '墓',
    year_pillar_desc: '力量衰退，需要调养，主运势下降',
    month_pillar_desc: '收藏蓄积，潜伏等待，主蛰伏储备',
    day_pillar_desc: '成长发展，渐入佳境，主事业进步',
    hour_pillar_desc: '收藏蓄积，潜伏等待，主蛰伏储备'
  },
  
  basicInfo: {
    kong_wang: {
      empty_branches: '寅、卯',
      xun_name: '甲辰旬',
      affected_pillars: [],
      effect: '空亡为六甲旬中缺失的地支，主虚空、变化、不稳定。本命无空亡影响',
      strength: '无影响'
    },
    
    ming_gua: {
      gua_name: '震卦',
      gua_number: 3,
      category: '东四命',
      element: '木',
      lucky_directions: '东、南、北、东南',
      description: '震卦属东四命，五行属木，主导个人的先天能量场和风水方位喜忌'
    }
  }
};

// 4. 验证数据结构
console.log('\n✅ 4. 数据结构验证:');
console.log('-'.repeat(40));

const dataChecks = [
  { path: 'nayin', value: correctDataStructure.nayin, valid: !!correctDataStructure.nayin },
  { path: 'selfSittingAnalysis', value: correctDataStructure.selfSittingAnalysis, valid: !!correctDataStructure.selfSittingAnalysis },
  { path: 'changshengAnalysis', value: correctDataStructure.changshengAnalysis, valid: !!correctDataStructure.changshengAnalysis },
  { path: 'basicInfo.kong_wang', value: correctDataStructure.basicInfo.kong_wang, valid: !!correctDataStructure.basicInfo.kong_wang },
  { path: 'basicInfo.ming_gua', value: correctDataStructure.basicInfo.ming_gua, valid: !!correctDataStructure.basicInfo.ming_gua }
];

dataChecks.forEach(check => {
  const status = check.valid ? '✅ 有效' : '❌ 无效';
  console.log(`   ${check.path}: ${status}`);
  if (check.valid && typeof check.value === 'object') {
    console.log(`      数据类型: ${Array.isArray(check.value) ? 'Array' : 'Object'}`);
    console.log(`      键数量: ${Object.keys(check.value).length}`);
  }
});

// 5. 问题定位建议
console.log('\n🔧 5. 问题定位建议:');
console.log('-'.repeat(40));

console.log('请在微信开发者工具中执行以下调试步骤:');
console.log('');
console.log('1. 打开四柱排盘页面');
console.log('2. 输入出生信息并点击排盘');
console.log('3. 在控制台中输入以下命令检查数据:');
console.log('');
console.log('   // 检查完整数据结构');
console.log('   console.log("完整数据:", getCurrentPages()[0].data.baziResult);');
console.log('');
console.log('   // 检查各个模块数据');
console.log('   const data = getCurrentPages()[0].data.baziResult;');
console.log('   console.log("纳音:", data.nayin);');
console.log('   console.log("自坐:", data.selfSittingAnalysis);');
console.log('   console.log("长生:", data.changshengAnalysis);');
console.log('   console.log("空亡:", data.basicInfo?.kong_wang);');
console.log('   console.log("命卦:", data.basicInfo?.ming_gua);');

// 6. 可能的修复方案
console.log('\n🛠️ 6. 可能的修复方案:');
console.log('-'.repeat(40));

const fixSolutions = [
  '检查计算函数是否抛出异常导致数据为空',
  '验证 setData 调用是否正确传递了所有数据',
  '确认 WXML 条件判断的数据路径是否正确',
  '检查是否有异步计算导致数据丢失',
  '验证微信小程序的数据绑定是否正常工作'
];

fixSolutions.forEach((solution, index) => {
  console.log(`${index + 1}. ${solution}`);
});

console.log('\n🎯 预期结果:');
console.log('如果数据结构正确，应该看到所有5个新模块都有对应的数据');
console.log('如果某个模块的数据为 null/undefined，则该模块不会显示');

console.log('\n🏁 调试完成');
console.log('请按照上述步骤检查实际的数据结构！');

// 导出调试信息
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    wxmlConditions, 
    possibleDataIssues, 
    correctDataStructure, 
    dataChecks, 
    fixSolutions 
  };
}
