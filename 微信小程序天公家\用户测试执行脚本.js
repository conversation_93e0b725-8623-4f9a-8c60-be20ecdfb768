// 用户测试执行脚本
// 自动化执行测试用例，收集性能数据和准确性指标

const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer.js');
const EnhancedYongshenCalculator = require('./utils/enhanced_yongshen_calculator.js');
const EnhancedDynamicAnalyzer = require('./utils/enhanced_dynamic_analyzer.js');
const EnhancedAdviceGenerator = require('./utils/enhanced_advice_generator.js');

// 测试用例数据
const testCases = [
  {
    id: 'case_001',
    name: '经典八字案例',
    bazi: { year: { heavenly: '甲', earthly: '寅' }, month: { heavenly: '丁', earthly: '巳' }, day: { heavenly: '甲', earthly: '子' }, hour: { heavenly: '己', earthly: '未' } },
    personal: { gender: '男', age: 39, birth_year: 1985, birth_month: 5, birth_day: 15, birth_hour: 14, location: '北京市' },
    expected: { pattern: '食神格', yongshen: '财星', clarity_min: 40, clarity_max: 50 }
  },
  {
    id: 'case_002', 
    name: '特殊格局案例',
    bazi: { year: { heavenly: '庚', earthly: '申' }, month: { heavenly: '戊', earthly: '子' }, day: { heavenly: '庚', earthly: '辰' }, hour: { heavenly: '戊', earthly: '寅' } },
    personal: { gender: '女', age: 35, birth_year: 1989, birth_month: 12, birth_day: 8, birth_hour: 6, location: '上海市' },
    expected: { pattern_type: '特殊格局', special_pattern: true }
  },
  {
    id: 'case_003',
    name: '平衡格局案例', 
    bazi: { year: { heavenly: '乙', earthly: '卯' }, month: { heavenly: '己', earthly: '卯' }, day: { heavenly: '戊', earthly: '午' }, hour: { heavenly: '癸', earthly: '亥' } },
    personal: { gender: '男', age: 28, birth_year: 1996, birth_month: 3, birth_day: 20, birth_hour: 22, location: '广州市' },
    expected: { pattern: '正官格', balance_good: true }
  }
];

// 性能测试结果
let performanceResults = {
  pattern_analysis: [],
  yongshen_calculation: [],
  dynamic_analysis: [],
  advice_generation: [],
  total_time: []
};

// 准确性测试结果
let accuracyResults = {
  pattern_accuracy: 0,
  yongshen_accuracy: 0,
  advice_quality: 0,
  overall_score: 0
};

// 错误记录
let errorLog = [];

console.log('🧪 开始用户测试执行...');
console.log('📋 测试用例数量:', testCases.length);
console.log('=' .repeat(60));

// 初始化引擎
let engines;
try {
  engines = {
    patternAnalyzer: new EnhancedPatternAnalyzer(),
    yongshenCalculator: new EnhancedYongshenCalculator(),
    dynamicAnalyzer: new EnhancedDynamicAnalyzer(),
    adviceGenerator: new EnhancedAdviceGenerator()
  };

  // 验证引擎方法是否存在
  console.log('🔍 验证引擎方法:');
  console.log('  patternAnalyzer.determinePattern:', typeof engines.patternAnalyzer.determinePattern);
  console.log('  yongshenCalculator.calculateFavors:', typeof engines.yongshenCalculator.calculateFavors);
  console.log('  dynamicAnalyzer.analyzeDynamicTrends:', typeof engines.dynamicAnalyzer.analyzeDynamicTrends);
  console.log('  adviceGenerator.generateComprehensiveAdvice:', typeof engines.adviceGenerator.generateComprehensiveAdvice);

  console.log('✅ 所有引擎初始化成功');
} catch (error) {
  console.error('❌ 引擎初始化失败:', error);
  console.error('错误详情:', error.stack);
  process.exit(1);
}

// 执行测试用例
async function runTestCase(testCase) {
  console.log(`\n🎯 执行测试用例: ${testCase.name} (${testCase.id})`);
  
  const results = {
    id: testCase.id,
    name: testCase.name,
    success: true,
    performance: {},
    accuracy: {},
    errors: []
  };

  try {
    // 1. 格局分析测试
    console.log('  📊 测试格局分析...');
    const patternStart = Date.now();

    // 准备四柱数据和出生时间
    const fourPillars = [
      { gan: testCase.bazi.year.heavenly, zhi: testCase.bazi.year.earthly },
      { gan: testCase.bazi.month.heavenly, zhi: testCase.bazi.month.earthly },
      { gan: testCase.bazi.day.heavenly, zhi: testCase.bazi.day.earthly },
      { gan: testCase.bazi.hour.heavenly, zhi: testCase.bazi.hour.earthly }
    ];
    const birthDateTime = new Date(testCase.personal.birth_year, testCase.personal.birth_month - 1, testCase.personal.birth_day, testCase.personal.birth_hour);

    const patternResult = engines.patternAnalyzer.determinePattern(testCase.bazi, fourPillars, birthDateTime);
    const patternTime = Date.now() - patternStart;
    
    results.performance.pattern_time = patternTime;
    results.pattern_result = patternResult;
    performanceResults.pattern_analysis.push(patternTime);
    
    // 验证格局分析准确性
    if (testCase.expected.pattern) {
      const patternMatch = patternResult.pattern_name === testCase.expected.pattern;
      results.accuracy.pattern_match = patternMatch;
      console.log(`    格局匹配: ${patternMatch ? '✅' : '❌'} (预期: ${testCase.expected.pattern}, 实际: ${patternResult.pattern_name})`);
    }
    
    if (testCase.expected.clarity_min && testCase.expected.clarity_max) {
      const clarityScore = patternResult.clarity_score * 100;
      const clarityInRange = clarityScore >= testCase.expected.clarity_min && clarityScore <= testCase.expected.clarity_max;
      results.accuracy.clarity_in_range = clarityInRange;
      console.log(`    清浊评分: ${clarityInRange ? '✅' : '❌'} (${clarityScore.toFixed(1)}分, 预期: ${testCase.expected.clarity_min}-${testCase.expected.clarity_max})`);
    }

    // 2. 用神分析测试
    console.log('  ⚡ 测试用神分析...');
    const yongshenStart = Date.now();
    const yongshenResult = engines.yongshenCalculator.calculateFavors(testCase.bazi, patternResult, testCase.bazi, testCase.personal);
    const yongshenTime = Date.now() - yongshenStart;
    
    results.performance.yongshen_time = yongshenTime;
    results.yongshen_result = yongshenResult;
    performanceResults.yongshen_calculation.push(yongshenTime);
    
    // 验证用神分析准确性
    if (testCase.expected.yongshen) {
      const yongshenMatch = yongshenResult.yongshen === testCase.expected.yongshen;
      results.accuracy.yongshen_match = yongshenMatch;
      console.log(`    用神匹配: ${yongshenMatch ? '✅' : '❌'} (预期: ${testCase.expected.yongshen}, 实际: ${yongshenResult.yongshen})`);
    }

    // 3. 动态分析测试
    console.log('  🔄 测试动态分析...');
    const dynamicStart = Date.now();
    const dynamicResult = engines.dynamicAnalyzer.analyzeDynamicTrends(testCase.bazi, yongshenResult, testCase.personal, { dayun_years: 10, forecast_years: 5 });
    const dynamicTime = Date.now() - dynamicStart;
    
    results.performance.dynamic_time = dynamicTime;
    results.dynamic_result = dynamicResult;
    performanceResults.dynamic_analysis.push(dynamicTime);

    // 4. 建议生成测试
    console.log('  💡 测试建议生成...');
    const adviceStart = Date.now();
    const adviceResult = engines.adviceGenerator.generateComprehensiveAdvice(testCase.bazi, patternResult, yongshenResult, dynamicResult, testCase.personal);
    const adviceTime = Date.now() - adviceStart;
    
    results.performance.advice_time = adviceTime;
    results.advice_result = adviceResult;
    performanceResults.advice_generation.push(adviceTime);

    // 5. 总体性能
    const totalTime = patternTime + yongshenTime + dynamicTime + adviceTime;
    results.performance.total_time = totalTime;
    performanceResults.total_time.push(totalTime);
    
    console.log(`  ⏱️ 总执行时间: ${totalTime}ms`);
    console.log(`    - 格局分析: ${patternTime}ms`);
    console.log(`    - 用神计算: ${yongshenTime}ms`);
    console.log(`    - 动态分析: ${dynamicTime}ms`);
    console.log(`    - 建议生成: ${adviceTime}ms`);

  } catch (error) {
    console.error(`  ❌ 测试用例执行失败:`, error.message);
    results.success = false;
    results.errors.push(error.message);
    errorLog.push({ testCase: testCase.id, error: error.message, stack: error.stack });
  }

  return results;
}

// 计算统计数据
function calculateStats(data) {
  if (data.length === 0) return { avg: 0, min: 0, max: 0 };
  
  const avg = data.reduce((sum, val) => sum + val, 0) / data.length;
  const min = Math.min(...data);
  const max = Math.max(...data);
  
  return { avg: avg.toFixed(2), min, max };
}

// 主测试函数
async function runAllTests() {
  const testResults = [];
  
  for (const testCase of testCases) {
    const result = await runTestCase(testCase);
    testResults.push(result);
  }
  
  // 生成测试报告
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试报告生成');
  console.log('='.repeat(60));
  
  // 性能统计
  console.log('\n🚀 性能测试结果:');
  console.log('  格局分析:', calculateStats(performanceResults.pattern_analysis), 'ms');
  console.log('  用神计算:', calculateStats(performanceResults.yongshen_calculation), 'ms');
  console.log('  动态分析:', calculateStats(performanceResults.dynamic_analysis), 'ms');
  console.log('  建议生成:', calculateStats(performanceResults.advice_generation), 'ms');
  console.log('  总体时间:', calculateStats(performanceResults.total_time), 'ms');
  
  // 成功率统计
  const successCount = testResults.filter(r => r.success).length;
  const successRate = (successCount / testResults.length * 100).toFixed(1);
  console.log('\n✅ 成功率统计:');
  console.log(`  测试成功: ${successCount}/${testResults.length} (${successRate}%)`);
  
  // 准确性统计
  const patternMatches = testResults.filter(r => r.accuracy.pattern_match === true).length;
  const patternTests = testResults.filter(r => r.accuracy.pattern_match !== undefined).length;
  const patternAccuracy = patternTests > 0 ? (patternMatches / patternTests * 100).toFixed(1) : 'N/A';
  
  console.log('\n🎯 准确性统计:');
  console.log(`  格局判定准确率: ${patternAccuracy}% (${patternMatches}/${patternTests})`);
  
  // 错误统计
  if (errorLog.length > 0) {
    console.log('\n❌ 错误记录:');
    errorLog.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error.testCase}: ${error.error}`);
    });
  } else {
    console.log('\n✅ 无错误记录');
  }
  
  // 性能评估
  const avgTotalTime = parseFloat(calculateStats(performanceResults.total_time).avg);
  console.log('\n📈 性能评估:');
  console.log(`  平均响应时间: ${avgTotalTime}ms ${avgTotalTime <= 3000 ? '✅' : '⚠️'} (目标: ≤3000ms)`);
  console.log(`  测试成功率: ${successRate}% ${successRate >= 95 ? '✅' : '⚠️'} (目标: ≥95%)`);
  
  // 总体评价
  const overallScore = (parseFloat(successRate) * 0.4 + parseFloat(patternAccuracy || 0) * 0.6).toFixed(1);
  console.log('\n🏆 总体评价:');
  console.log(`  综合得分: ${overallScore}分 ${overallScore >= 80 ? '✅ 优秀' : overallScore >= 70 ? '⚠️ 良好' : '❌ 需改进'}`);
  
  return {
    testResults,
    performance: performanceResults,
    accuracy: { pattern_accuracy: patternAccuracy },
    errors: errorLog,
    overall_score: overallScore
  };
}

// 执行测试
runAllTests().then(results => {
  console.log('\n🎉 用户测试执行完成！');
  console.log('📋 详细结果已记录，可用于后续分析和改进');
}).catch(error => {
  console.error('❌ 测试执行失败:', error);
});
