/**
 * 检查重复计算问题
 * 分析哪些神煞有多个计算版本，避免前端重复显示
 */

console.log('🔄 检查重复计算问题');
console.log('='.repeat(60));
console.log('');

// 已实现的29个功能
const implementedFunctions = [
  'calculateShensha',
  'calculateTianyiGuiren', 'calculateWenchangGuiren', 'calculateFuxingGuiren',
  'calculateTaohua', 'calculateHuagai', 'calculateKongWang', 'calculateTaijiGuiren',
  'calculateLushen', 'calculateXuetang', 'calculateYangRen', 'calculateJiesha',
  'calculateGuchenGuasu', 'calculateYima', 'calculateTiande', 'calculateYuede',
  'calculateYuehe', 'calculateWebTianchuGuiren', 'calculateWebTongzisha',
  'calculateWebZaisha', 'calculateWebSangmen', 'calculateWebXueren', 'calculateWebPima',
  'calculateGuoyinGuiren', 'calculateDexiuGuiren', 'calculateJinyu',
  'calculateTianyi', 'calculateJiangxing', 'calculateQisha'
];

// 分析重复的神煞类型
const duplicateAnalysis = {
  '天乙贵人': {
    functions: ['calculateTianyiGuiren'],
    missing: ['calculateQianliTianyiGuiren'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '文昌贵人': {
    functions: ['calculateWenchangGuiren'],
    missing: ['calculateQianliWenchangGuiren'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '桃花': {
    functions: ['calculateTaohua'],
    missing: ['calculateQianliTaohua'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '羊刃': {
    functions: ['calculateYangRen'],
    missing: ['calculateQianliYangRen'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '驿马': {
    functions: ['calculateYima'],
    missing: ['calculateQianliYima'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '天德': {
    functions: ['calculateTiande'],
    missing: ['calculateQianliTiande'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '月德': {
    functions: ['calculateYuede'],
    missing: ['calculateQianliYuede'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  },
  
  '月德合': {
    functions: ['calculateYuehe'],
    missing: ['calculateQianliYuehe'],
    status: '基础版已实现，千里版未实现',
    recommendation: '保留基础版，添加千里版作为权威版本'
  }
};

console.log('📊 重复神煞类型分析：');
console.log('='.repeat(40));

Object.keys(duplicateAnalysis).forEach((shenshaType, index) => {
  const analysis = duplicateAnalysis[shenshaType];
  console.log(`${index + 1}. ${shenshaType}:`);
  console.log(`   已实现：${analysis.functions.join(', ')}`);
  console.log(`   缺失：${analysis.missing.join(', ')}`);
  console.log(`   状态：${analysis.status}`);
  console.log(`   建议：${analysis.recommendation}`);
  console.log('');
});

console.log('🚨 当前重复计算风险：');
console.log('='.repeat(30));
console.log('❌ 低风险 - 目前没有真正的重复计算');
console.log('   原因：千里版本都未实现，不存在重复');
console.log('   但是：一旦实现千里版本，就会出现重复');

console.log('');
console.log('⚠️ 潜在重复计算场景：');
console.log('='.repeat(30));
console.log('1. calculateTianyiGuiren + calculateQianliTianyiGuiren');
console.log('   → 可能产生2个"天乙贵人"结果');
console.log('');
console.log('2. calculateWenchangGuiren + calculateQianliWenchangGuiren');
console.log('   → 可能产生2个"文昌贵人"结果');
console.log('');
console.log('3. calculateTaohua + calculateQianliTaohua');
console.log('   → 可能产生2个"桃花"结果');

console.log('');
console.log('💡 解决重复计算的策略：');
console.log('='.repeat(40));

console.log('策略1：版本优先级 (推荐)');
console.log('  ✅ 保留千里命稿版本（更权威）');
console.log('  ❌ 禁用基础版本');
console.log('  📋 实现：在calculateAllShenshas中添加版本控制');

console.log('');
console.log('策略2：条件调用');
console.log('  ✅ 根据配置选择调用哪个版本');
console.log('  📋 实现：添加配置参数控制版本选择');

console.log('');
console.log('策略3：结果去重');
console.log('  ✅ 允许多版本计算，但在结果中去重');
console.log('  📋 实现：在categorizeShenshas中去重');

console.log('');
console.log('🎯 推荐的实现方案：');
console.log('='.repeat(30));
console.log('1. 实现千里命稿版本的8个函数');
console.log('2. 在calculateAllShenshas中添加版本控制：');
console.log('   - 优先调用千里版本（如果存在）');
console.log('   - 千里版本不存在时才调用基础版本');
console.log('3. 添加结果去重机制防止意外重复');

console.log('');
console.log('📝 版本控制代码示例：');
console.log('='.repeat(30));
console.log(`
// 在calculateAllShenshas中的版本控制逻辑
function callShenshaFunction(calculator, basicName, qianliName, ...params) {
  // 优先使用千里版本
  if (calculator[qianliName]) {
    return calculator[qianliName](...params) || [];
  }
  // 回退到基础版本
  if (calculator[basicName]) {
    return calculator[basicName](...params) || [];
  }
  return [];
}

// 使用示例
const tianyiResults = callShenshaFunction(
  calculator, 
  'calculateTianyiGuiren', 
  'calculateQianliTianyiGuiren', 
  dayGan, 
  fourPillars
);
`);

console.log('');
console.log('🔧 需要修改的函数调用：');
console.log('='.repeat(30));
const needVersionControl = [
  'calculateTianyiGuiren → calculateQianliTianyiGuiren',
  'calculateWenchangGuiren → calculateQianliWenchangGuiren',
  'calculateTaohua → calculateQianliTaohua',
  'calculateYangRen → calculateQianliYangRen',
  'calculateYima → calculateQianliYima',
  'calculateTiande → calculateQianliTiande',
  'calculateYuede → calculateQianliYuede',
  'calculateYuehe → calculateQianliYuehe'
];

needVersionControl.forEach((pair, index) => {
  console.log(`${index + 1}. ${pair}`);
});

console.log('');
console.log('✅ 重复计算问题检查完成！');
console.log('🎯 结论：目前无重复，但需要在实现千里版本时添加版本控制');
