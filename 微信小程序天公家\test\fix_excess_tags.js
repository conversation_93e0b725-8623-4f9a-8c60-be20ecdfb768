/**
 * 修复多余的结束标签
 * 根据分析结果删除特定行的多余标签
 */

const fs = require('fs');
const path = require('path');

function fixExcessTags() {
  console.log('🔧 修复多余的结束标签');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 根据最新分析结果，这些行有多余的</view>标签
  const problematicLines = [279, 287, 310, 1393, 1449, 1469];
  
  console.log(`📍 原始文件行数: ${lines.length}`);
  console.log(`📍 需要检查的行: ${problematicLines.join(', ')}`);
  
  // 检查这些行的内容
  console.log('\n🔍 检查问题行内容:');
  problematicLines.forEach(lineNum => {
    if (lineNum <= lines.length) {
      const line = lines[lineNum - 1]; // 转换为0基索引
      console.log(`第${lineNum}行: "${line}"`);
      
      // 检查是否确实是多余的</view>
      if (line.trim() === '</view>') {
        console.log(`  ✓ 确认是单独的</view>标签`);
      } else if (line.includes('</view>')) {
        console.log(`  ⚠️ 包含</view>但不是单独的标签`);
      } else {
        console.log(`  ❌ 不包含</view>标签`);
      }
    } else {
      console.log(`第${lineNum}行: (超出文件范围)`);
    }
  });
  
  // 创建一个简单的修复：删除明显多余的单独</view>行
  console.log('\n🔧 开始修复...');
  
  let fixedLines = [];
  let deletedCount = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const lineNum = i + 1;
    const line = lines[i];
    
    // 如果这行在问题列表中，且确实是单独的</view>，则跳过
    if (problematicLines.includes(lineNum) && line.trim() === '</view>') {
      console.log(`删除第${lineNum}行: "${line}"`);
      deletedCount++;
      continue;
    }
    
    fixedLines.push(line);
  }
  
  console.log(`\n📊 修复结果:`);
  console.log(`删除了 ${deletedCount} 行多余的</view>标签`);
  console.log(`修复后文件行数: ${fixedLines.length}`);
  
  // 写回文件
  const fixedContent = fixedLines.join('\n');
  fs.writeFileSync(wxmlPath, fixedContent, 'utf8');
  
  console.log('✅ 文件已保存');
  
  return deletedCount;
}

// 运行修复
if (require.main === module) {
  fixExcessTags();
}

module.exports = { fixExcessTags };
