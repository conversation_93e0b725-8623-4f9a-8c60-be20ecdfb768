/**
 * 高级社会环境分析模块
 * 提供更深入的社会环境因素分析
 */

class AdvancedSocialAnalyzer {
  constructor() {
    this.initializeEnvironmentalData();
  }

  /**
   * 初始化环境数据
   */
  initializeEnvironmentalData() {
    // 经济周期数据
    this.economicCycles = {
      2020: { phase: '衰退期', growth_rate: -2.3, inflation: 2.5 },
      2021: { phase: '复苏期', growth_rate: 8.1, inflation: 0.9 },
      2022: { phase: '增长期', growth_rate: 3.0, inflation: 2.0 },
      2023: { phase: '调整期', growth_rate: 5.2, inflation: 0.2 },
      2024: { phase: '稳定期', growth_rate: 4.8, inflation: 0.4 },
      2025: { phase: '增长期', growth_rate: 5.0, inflation: 1.2 }
    };

    // 行业趋势数据
    this.industryTrends = {
      '科技': { growth_potential: 0.9, stability: 0.7, competition: 0.8 },
      '金融': { growth_potential: 0.6, stability: 0.8, competition: 0.9 },
      '教育': { growth_potential: 0.8, stability: 0.9, competition: 0.6 },
      '医疗': { growth_potential: 0.9, stability: 0.8, competition: 0.7 },
      '制造': { growth_potential: 0.5, stability: 0.7, competition: 0.8 },
      '服务': { growth_potential: 0.7, stability: 0.6, competition: 0.7 },
      '文化': { growth_potential: 0.8, stability: 0.5, competition: 0.6 },
      '农业': { growth_potential: 0.4, stability: 0.8, competition: 0.5 }
    };

    // 地域发展指数
    this.regionalDevelopment = {
      '一线城市': { opportunity: 0.9, cost: 0.9, competition: 0.9 },
      '新一线城市': { opportunity: 0.8, cost: 0.7, competition: 0.8 },
      '二线城市': { opportunity: 0.7, cost: 0.6, competition: 0.6 },
      '三线城市': { opportunity: 0.5, cost: 0.4, competition: 0.4 },
      '四线城市': { opportunity: 0.3, cost: 0.3, competition: 0.3 }
    };

    // 年龄阶段特征
    this.ageStageCharacteristics = {
      '20-25': { energy: 0.9, experience: 0.2, stability: 0.3, learning: 0.9 },
      '26-30': { energy: 0.8, experience: 0.4, stability: 0.5, learning: 0.8 },
      '31-35': { energy: 0.7, experience: 0.6, stability: 0.7, learning: 0.6 },
      '36-40': { energy: 0.6, experience: 0.8, stability: 0.8, learning: 0.5 },
      '41-45': { energy: 0.5, experience: 0.9, stability: 0.9, learning: 0.4 },
      '46-50': { energy: 0.4, experience: 1.0, stability: 0.9, learning: 0.3 },
      '51-60': { energy: 0.3, experience: 1.0, stability: 1.0, learning: 0.2 }
    };
  }

  /**
   * 综合社会环境分析
   */
  analyzeComprehensiveSocialEnvironment(personalInfo, analysisOptions = {}) {
    console.log('🌍 开始高级社会环境分析...');

    try {
      const currentYear = new Date().getFullYear();
      
      // 1. 宏观环境分析
      const macroEnvironment = this.analyzeMacroEnvironment(personalInfo, currentYear);
      
      // 2. 微观环境分析
      const microEnvironment = this.analyzeMicroEnvironment(personalInfo);
      
      // 3. 个人发展阶段分析
      const developmentStage = this.analyzePersonalDevelopmentStage(personalInfo);
      
      // 4. 机遇与挑战分析
      const opportunitiesAndChallenges = this.analyzeOpportunitiesAndChallenges(
        macroEnvironment, microEnvironment, developmentStage
      );
      
      // 5. 环境适应性评估
      const adaptabilityAssessment = this.assessEnvironmentalAdaptability(
        personalInfo, macroEnvironment, microEnvironment
      );

      return {
        macro_environment: macroEnvironment,
        micro_environment: microEnvironment,
        development_stage: developmentStage,
        opportunities_challenges: opportunitiesAndChallenges,
        adaptability_assessment: adaptabilityAssessment,
        analysis_timestamp: new Date().toISOString(),
        confidence: this.calculateSocialAnalysisConfidence(macroEnvironment, microEnvironment)
      };

    } catch (error) {
      console.error('❌ 高级社会环境分析失败:', error);
      return {
        macro_environment: { error: '宏观环境分析失败' },
        micro_environment: { error: '微观环境分析失败' },
        development_stage: { error: '发展阶段分析失败' },
        opportunities_challenges: { error: '机遇挑战分析失败' },
        adaptability_assessment: { error: '适应性评估失败' },
        analysis_timestamp: new Date().toISOString(),
        confidence: 0.3,
        error: error.message
      };
    }
  }

  /**
   * 宏观环境分析
   */
  analyzeMacroEnvironment(personalInfo, currentYear) {
    // 经济周期分析
    const economicCycle = this.analyzeEconomicCycle(currentYear);
    
    // 行业趋势分析
    const industryTrends = this.analyzeIndustryTrends(personalInfo.industry || '服务');
    
    // 政策环境分析
    const policyEnvironment = this.analyzePolicyEnvironment(currentYear);
    
    // 社会趋势分析
    const socialTrends = this.analyzeSocialTrends(currentYear);

    return {
      economic_cycle: economicCycle,
      industry_trends: industryTrends,
      policy_environment: policyEnvironment,
      social_trends: socialTrends,
      overall_macro_score: this.calculateMacroScore(economicCycle, industryTrends, policyEnvironment)
    };
  }

  /**
   * 微观环境分析
   */
  analyzeMicroEnvironment(personalInfo) {
    // 家庭背景分析
    const familyBackground = this.analyzeFamilyBackground(personalInfo);
    
    // 教育背景分析
    const educationImpact = this.analyzeEducationImpact(personalInfo);
    
    // 职业发展阶段分析
    const careerStage = this.analyzeCareerStage(personalInfo);
    
    // 社会关系网络分析
    const socialNetwork = this.analyzeSocialNetwork(personalInfo);
    
    // 地域环境分析
    const regionalEnvironment = this.analyzeRegionalEnvironment(personalInfo.location || '二线城市');

    return {
      family_background: familyBackground,
      education_impact: educationImpact,
      career_stage: careerStage,
      social_network: socialNetwork,
      regional_environment: regionalEnvironment,
      overall_micro_score: this.calculateMicroScore(familyBackground, educationImpact, careerStage)
    };
  }

  /**
   * 个人发展阶段分析
   */
  analyzePersonalDevelopmentStage(personalInfo) {
    const age = personalInfo.age || 30;
    const ageGroup = this.getAgeGroup(age);
    const stageCharacteristics = this.ageStageCharacteristics[ageGroup];

    return {
      age_group: ageGroup,
      stage_characteristics: stageCharacteristics,
      development_focus: this.getDevelopmentFocus(ageGroup),
      key_challenges: this.getAgeGroupChallenges(ageGroup),
      opportunities: this.getAgeGroupOpportunities(ageGroup),
      recommended_strategies: this.getAgeGroupStrategies(ageGroup)
    };
  }

  /**
   * 机遇与挑战分析
   */
  analyzeOpportunitiesAndChallenges(macroEnvironment, microEnvironment, developmentStage) {
    const opportunities = [];
    const challenges = [];

    // 基于宏观环境的机遇和挑战
    if (macroEnvironment.economic_cycle.phase === '增长期') {
      opportunities.push({
        type: '经济机遇',
        description: '经济增长期，投资和创业机会增多',
        probability: 0.8,
        impact: 0.7
      });
    }

    if (macroEnvironment.industry_trends.growth_potential > 0.7) {
      opportunities.push({
        type: '行业机遇',
        description: '所在行业发展前景良好，职业发展空间大',
        probability: 0.7,
        impact: 0.8
      });
    }

    // 基于微观环境的机遇和挑战
    if (microEnvironment.education_impact.level === 'high') {
      opportunities.push({
        type: '教育优势',
        description: '教育背景优良，有利于职业发展',
        probability: 0.9,
        impact: 0.6
      });
    }

    // 基于发展阶段的机遇和挑战
    if (developmentStage.stage_characteristics.energy > 0.7) {
      opportunities.push({
        type: '年龄优势',
        description: '年富力强，精力充沛，适合拼搏',
        probability: 0.8,
        impact: 0.7
      });
    }

    return {
      opportunities: opportunities,
      challenges: challenges,
      opportunity_score: this.calculateOpportunityScore(opportunities),
      challenge_score: this.calculateChallengeScore(challenges)
    };
  }

  /**
   * 环境适应性评估
   */
  assessEnvironmentalAdaptability(personalInfo, macroEnvironment, microEnvironment) {
    const adaptabilityFactors = {
      flexibility: this.assessFlexibility(personalInfo),
      learning_ability: this.assessLearningAbility(personalInfo),
      social_skills: this.assessSocialSkills(personalInfo),
      stress_tolerance: this.assessStressTolerance(personalInfo),
      innovation_capacity: this.assessInnovationCapacity(personalInfo)
    };

    const overallAdaptability = Object.values(adaptabilityFactors).reduce((sum, score) => sum + score, 0) / 5;

    return {
      adaptability_factors: adaptabilityFactors,
      overall_adaptability: overallAdaptability,
      adaptability_level: this.getAdaptabilityLevel(overallAdaptability),
      improvement_suggestions: this.getAdaptabilityImprovementSuggestions(adaptabilityFactors)
    };
  }

  // 辅助方法
  analyzeEconomicCycle(year) {
    const cycleData = this.economicCycles[year] || this.economicCycles[2025];
    return {
      phase: cycleData.phase,
      growth_rate: cycleData.growth_rate,
      inflation: cycleData.inflation,
      impact_assessment: this.assessEconomicImpact(cycleData)
    };
  }

  analyzeIndustryTrends(industry) {
    const trends = this.industryTrends[industry] || this.industryTrends['服务'];
    return {
      industry: industry,
      growth_potential: trends.growth_potential,
      stability: trends.stability,
      competition: trends.competition,
      outlook: this.getIndustryOutlook(trends)
    };
  }

  analyzePolicyEnvironment(year) {
    return {
      policy_support: 0.7,
      regulatory_environment: 0.6,
      tax_policy: 0.5,
      employment_policy: 0.8,
      overall_policy_score: 0.65
    };
  }

  analyzeSocialTrends(year) {
    return {
      digitalization: 0.9,
      sustainability: 0.8,
      aging_society: 0.7,
      urbanization: 0.6,
      social_mobility: 0.5
    };
  }

  getAgeGroup(age) {
    if (age <= 25) return '20-25';
    if (age <= 30) return '26-30';
    if (age <= 35) return '31-35';
    if (age <= 40) return '36-40';
    if (age <= 45) return '41-45';
    if (age <= 50) return '46-50';
    return '51-60';
  }

  getDevelopmentFocus(ageGroup) {
    const focusMap = {
      '20-25': '技能学习和经验积累',
      '26-30': '职业定位和能力提升',
      '31-35': '事业发展和财富积累',
      '36-40': '管理能力和资源整合',
      '41-45': '经验传承和战略规划',
      '46-50': '稳定发展和风险控制',
      '51-60': '经验分享和平稳过渡'
    };
    return focusMap[ageGroup] || '全面发展';
  }

  calculateSocialAnalysisConfidence(macroEnvironment, microEnvironment) {
    let confidence = 0.7;
    
    if (macroEnvironment.overall_macro_score > 0.7) confidence += 0.1;
    if (microEnvironment.overall_micro_score > 0.7) confidence += 0.1;
    
    return Math.min(0.95, confidence);
  }

  calculateMacroScore(economicCycle, industryTrends, policyEnvironment) {
    return (economicCycle.growth_rate / 10 + industryTrends.growth_potential + policyEnvironment.overall_policy_score) / 3;
  }

  calculateMicroScore(familyBackground, educationImpact, careerStage) {
    return (familyBackground.support_level + educationImpact.advantage_score + careerStage.development_potential) / 3;
  }

  // 更多辅助方法...
  analyzeFamilyBackground(personalInfo) {
    return {
      economic_status: personalInfo.family_economic_status || 'middle',
      education_background: personalInfo.family_education || 'average',
      social_resources: personalInfo.family_resources || 'limited',
      support_level: 0.6
    };
  }

  analyzeEducationImpact(personalInfo) {
    const education = personalInfo.education || 'bachelor';
    const educationScores = {
      'phd': { level: 'very_high', advantage_score: 0.9 },
      'master': { level: 'high', advantage_score: 0.8 },
      'bachelor': { level: 'medium', advantage_score: 0.6 },
      'college': { level: 'low', advantage_score: 0.4 },
      'high_school': { level: 'very_low', advantage_score: 0.2 }
    };
    
    return educationScores[education] || educationScores['bachelor'];
  }

  analyzeCareerStage(personalInfo) {
    const age = personalInfo.age || 30;
    let stage, potential;
    
    if (age < 30) {
      stage = 'early_career';
      potential = 0.8;
    } else if (age < 40) {
      stage = 'mid_career';
      potential = 0.7;
    } else if (age < 50) {
      stage = 'senior_career';
      potential = 0.6;
    } else {
      stage = 'late_career';
      potential = 0.4;
    }
    
    return {
      stage: stage,
      development_potential: potential,
      experience_value: Math.min(1.0, age / 50)
    };
  }

  analyzeSocialNetwork(personalInfo) {
    return {
      network_size: personalInfo.social_network_size || 'medium',
      network_quality: personalInfo.social_network_quality || 'average',
      professional_connections: personalInfo.professional_connections || 'limited',
      network_score: 0.5
    };
  }

  analyzeRegionalEnvironment(location) {
    const regional = this.regionalDevelopment[location] || this.regionalDevelopment['二线城市'];
    return {
      location: location,
      opportunity_level: regional.opportunity,
      cost_level: regional.cost,
      competition_level: regional.competition,
      overall_score: (regional.opportunity + (1 - regional.cost) + (1 - regional.competition)) / 3
    };
  }

  // 适应性评估方法
  assessFlexibility(personalInfo) {
    return personalInfo.flexibility_score || 0.6;
  }

  assessLearningAbility(personalInfo) {
    return personalInfo.learning_ability || 0.7;
  }

  assessSocialSkills(personalInfo) {
    return personalInfo.social_skills || 0.6;
  }

  assessStressTolerance(personalInfo) {
    return personalInfo.stress_tolerance || 0.5;
  }

  assessInnovationCapacity(personalInfo) {
    return personalInfo.innovation_capacity || 0.5;
  }

  getAdaptabilityLevel(score) {
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'average';
    return 'needs_improvement';
  }

  getAdaptabilityImprovementSuggestions(factors) {
    const suggestions = [];
    
    Object.keys(factors).forEach(factor => {
      if (factors[factor] < 0.5) {
        suggestions.push(`提升${factor}能力`);
      }
    });
    
    return suggestions;
  }

  calculateOpportunityScore(opportunities) {
    if (opportunities.length === 0) return 0;
    return opportunities.reduce((sum, opp) => sum + opp.probability * opp.impact, 0) / opportunities.length;
  }

  calculateChallengeScore(challenges) {
    if (challenges.length === 0) return 0;
    return challenges.reduce((sum, challenge) => sum + challenge.probability * challenge.impact, 0) / challenges.length;
  }

  assessEconomicImpact(cycleData) {
    if (cycleData.growth_rate > 5) return 'positive';
    if (cycleData.growth_rate > 0) return 'neutral';
    return 'negative';
  }

  getIndustryOutlook(trends) {
    const score = (trends.growth_potential + trends.stability + (1 - trends.competition)) / 3;
    if (score > 0.7) return 'excellent';
    if (score > 0.5) return 'good';
    return 'challenging';
  }

  getAgeGroupChallenges(ageGroup) {
    const challengeMap = {
      '20-25': ['经验不足', '职业迷茫', '经济压力'],
      '26-30': ['职业选择', '技能提升', '生活平衡'],
      '31-35': ['事业压力', '家庭责任', '竞争激烈'],
      '36-40': ['中年危机', '健康问题', '上有老下有小'],
      '41-45': ['职业瓶颈', '体力下降', '子女教育'],
      '46-50': ['更新换代', '健康风险', '养老准备'],
      '51-60': ['退休规划', '健康管理', '代际关系']
    };
    return challengeMap[ageGroup] || ['通用挑战'];
  }

  getAgeGroupOpportunities(ageGroup) {
    const opportunityMap = {
      '20-25': ['学习机会', '创新思维', '时间充裕'],
      '26-30': ['能力提升', '网络建设', '职业发展'],
      '31-35': ['经验积累', '资源整合', '领导机会'],
      '36-40': ['管理经验', '人脉资源', '财富积累'],
      '41-45': ['行业专家', '指导他人', '战略思维'],
      '46-50': ['丰富经验', '稳定收入', '传承价值'],
      '51-60': ['智慧分享', '顾问角色', '享受生活']
    };
    return opportunityMap[ageGroup] || ['通用机会'];
  }

  getAgeGroupStrategies(ageGroup) {
    const strategyMap = {
      '20-25': ['积极学习', '多元尝试', '建立基础'],
      '26-30': ['专业深化', '网络拓展', '目标明确'],
      '31-35': ['稳步发展', '资源积累', '平衡兼顾'],
      '36-40': ['战略规划', '团队建设', '风险管理'],
      '41-45': ['经验传承', '价值创造', '健康管理'],
      '46-50': ['稳健发展', '风险控制', '退休准备'],
      '51-60': ['平稳过渡', '健康第一', '享受人生']
    };
    return strategyMap[ageGroup] || ['通用策略'];
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdvancedSocialAnalyzer;
} else if (typeof window !== 'undefined') {
  window.AdvancedSocialAnalyzer = AdvancedSocialAnalyzer;
}
