Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 选项文本
    text: {
      type: String,
      value: ''
    },
    // 选项索引
    index: {
      type: Number,
      value: 0
    },
    // 是否已选中
    selected: {
      type: Boolean,
      value: false
    },
    // 选项字母标签 (A/B/C/D)
    label: {
      type: String,
      value: ''
    },
    // 选项样式（适用于不同年级）
    gradeStyle: {
      type: String,
      value: 'default' // default, elementary, junior, senior
    },
    // 是否包含表情符号
    hasEmoji: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 字母标签数组
    labels: ['A', 'B', 'C', 'D', 'E'],
    // 动画
    animationData: null
  },

  /**
   * 在组件实例进入页面节点树时执行
   */
  attached() {
    // 设置选项的动画效果，错落显示各选项
    const animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
    });
    
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100 * this.properties.index);
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击选项
     */
    handleTap() {
      // 触发选中事件
      this.triggerEvent('select', { index: this.properties.index });
      
      // 创建点击动画
      const animation = wx.createAnimation({
        duration: 150,
        timingFunction: 'ease',
      });
      
      // 先缩小
      animation.scale(0.95).step();
      
      // 再恢复
      setTimeout(() => {
        animation.scale(1).step();
        this.setData({
          animationData: animation.export()
        });
      }, 150);
      
      this.setData({
        animationData: animation.export()
      });
    },
    
    /**
     * 解析文本，分离表情符号与文本
     */
    parseText(text) {
      // 简单的表情符号检测
      const emojiMatch = text.match(/^([\u{1F300}-\u{1F6FF}\u{2600}-\u{26FF}])/u);
      if (emojiMatch) {
        return {
          emoji: emojiMatch[1],
          text: text.substring(emojiMatch[1].length).trim()
        };
      }
      return { emoji: '', text: text };
    }
  }
}); 