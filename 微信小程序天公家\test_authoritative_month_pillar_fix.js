// test_authoritative_month_pillar_fix.js
// 测试基于权威节气数据的月柱修复效果

console.log('🧪 测试基于权威节气数据的月柱修复效果...');

// 模拟权威节气数据
const authoritativeJieqiData2024 = {
  "小暑": {
    "month": 7,
    "day": 6,
    "hour": 22,
    "minute": 29
  },
  "大暑": {
    "month": 7,
    "day": 22,
    "hour": 9,
    "minute": 44
  },
  "立秋": {
    "month": 8,
    "day": 7,
    "hour": 13,
    "minute": 52
  }
};

// 模拟修复后的节气月份计算
function getSolarMonthByAuthoritativeData(year, month, day, yearData) {
  const currentDate = new Date(year, month - 1, day);
  
  // 关键节气时间
  const xiaoshu = yearData['小暑'] ? new Date(year, yearData['小暑'].month - 1, yearData['小暑'].day, yearData['小暑'].hour, yearData['小暑'].minute) : null;
  const liqiu = yearData['立秋'] ? new Date(year, yearData['立秋'].month - 1, yearData['立秋'].day, yearData['立秋'].hour, yearData['立秋'].minute) : null;
  
  console.log(`\n🔍 精确节气判断: ${year}年${month}月${day}日`);
  console.log(`当前时间: ${currentDate.toLocaleString()}`);
  if (xiaoshu) console.log(`小暑时间: ${xiaoshu.toLocaleString()}`);
  if (liqiu) console.log(`立秋时间: ${liqiu.toLocaleString()}`);
  
  // 7-8月的精确判断
  if (month === 7) {
    if (xiaoshu && currentDate >= xiaoshu) {
      console.log(`✅ ${year}年${month}月${day}日 >= 小暑 → 未月(6)`);
      return 6; // 小暑后，未月
    } else {
      console.log(`✅ ${year}年${month}月${day}日 < 小暑 → 午月(5)`);
      return 5; // 小暑前，午月
    }
  }
  
  if (month === 8) {
    if (liqiu && currentDate >= liqiu) {
      console.log(`✅ ${year}年${month}月${day}日 >= 立秋 → 申月(7)`);
      return 7; // 立秋后，申月
    } else {
      console.log(`✅ ${year}年${month}月${day}日 < 立秋 → 未月(6)`);
      return 6; // 立秋前，未月
    }
  }
  
  // 其他月份使用简化映射
  const monthMap = {
    1: 12, 2: 1, 3: 2, 4: 3, 5: 4, 6: 5,
    9: 8, 10: 9, 11: 10, 12: 11
  };
  
  const result = monthMap[month] || month;
  console.log(`✅ ${year}年${month}月${day}日 → 月份${result}`);
  return result;
}

// 计算月柱
function calculateMonthPillar(year, month, day, yearGan) {
  const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
  const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
  
  // 获取节气月份
  const solarMonth = getSolarMonthByAuthoritativeData(year, month, day, authoritativeJieqiData2024);
  
  // 五虎遁
  const wuhuDun = {
    '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
    '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
    '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
    '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
    '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
  };
  
  const monthGanStart = wuhuDun[yearGan];
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  const monthZhi = monthZhiMap[solarMonth - 1];
  
  const result = monthGan + monthZhi;
  
  console.log(`\n🔧 月柱计算详情:`);
  console.log(`年干: ${yearGan}`);
  console.log(`节气月: ${solarMonth}`);
  console.log(`五虎遁起始: ${tiangan[monthGanStart]}(${monthGanStart})`);
  console.log(`月干计算: (${monthGanStart} + ${solarMonth} - 1) % 10 = ${monthGanIndex}`);
  console.log(`月干: ${monthGan}`);
  console.log(`月支: ${monthZhi}`);
  console.log(`月柱: ${result}`);
  
  return result;
}

// 测试关键案例
function testKeyCases() {
  console.log('\n📊 测试关键案例：');
  
  const testCases = [
    {
      date: '2024年7月5日',
      year: 2024, month: 7, day: 5,
      yearGan: '甲',
      expected: '己午',
      description: '小暑前，应该是午月'
    },
    {
      date: '2024年7月7日',
      year: 2024, month: 7, day: 7,
      yearGan: '甲',
      expected: '辛未',
      description: '小暑后，应该是未月'
    },
    {
      date: '2024年7月22日',
      year: 2024, month: 7, day: 22,
      yearGan: '甲',
      expected: '辛未',
      description: '大暑当日，仍是未月'
    },
    {
      date: '2024年7月30日',
      year: 2024, month: 7, day: 30,
      yearGan: '甲',
      expected: '辛未',
      description: '关键案例：7月30日应该是辛未'
    },
    {
      date: '2024年8月6日',
      year: 2024, month: 8, day: 6,
      yearGan: '甲',
      expected: '辛未',
      description: '立秋前，仍是未月'
    },
    {
      date: '2024年8月7日',
      year: 2024, month: 8, day: 7,
      yearGan: '甲',
      expected: '壬申',
      description: '立秋当日，申月开始'
    },
    {
      date: '2024年8月15日',
      year: 2024, month: 8, day: 15,
      yearGan: '甲',
      expected: '壬申',
      description: '立秋后，申月'
    }
  ];
  
  let allCorrect = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n=== 测试案例${index + 1}: ${testCase.date} ===`);
    console.log(`描述: ${testCase.description}`);
    
    const result = calculateMonthPillar(testCase.year, testCase.month, testCase.day, testCase.yearGan);
    const isCorrect = result === testCase.expected;
    
    if (!isCorrect) allCorrect = false;
    
    console.log(`期望: ${testCase.expected}`);
    console.log(`实际: ${result}`);
    console.log(`结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
    
    if (!isCorrect) {
      console.log(`⚠️ 测试失败！需要进一步检查`);
    }
  });
  
  return allCorrect;
}

// 对比修复前后的差异
function compareBeforeAfter() {
  console.log('\n📊 修复前后对比：');
  
  console.log('\n❌ 修复前的错误逻辑:');
  console.log('- 硬编码: 7月23日后就是申月');
  console.log('- 结果: 2024年7月30日 → 壬申 (错误)');
  
  console.log('\n✅ 修复后的正确逻辑:');
  console.log('- 权威数据: 基于精确的节气时间判断');
  console.log('- 小暑后(7月6日22:29后) → 未月');
  console.log('- 立秋后(8月7日13:52后) → 申月');
  console.log('- 结果: 2024年7月30日 → 辛未 (正确)');
  
  console.log('\n🔧 技术改进:');
  console.log('1. 不再硬编码日期');
  console.log('2. 使用权威节气数据进行精确计算');
  console.log('3. 降级机制：权威数据不可用时使用修正的简化逻辑');
  console.log('4. 详细的调试日志，便于问题追踪');
}

// 执行测试
const allTestsPassed = testKeyCases();
compareBeforeAfter();

console.log('\n🎯 测试结果总结：');
if (allTestsPassed) {
  console.log('🎉 所有测试用例通过！');
  console.log('✅ 月柱计算修复成功');
  console.log('✅ 基于权威节气数据的精确计算');
  console.log('✅ 2024年7月30日正确显示为辛未');
} else {
  console.log('⚠️ 部分测试用例失败');
  console.log('❌ 需要进一步调试和修复');
}

console.log('\n📋 使用指导：');
console.log('1. 确保权威节气数据文件已正确加载');
console.log('2. 检查控制台日志中的节气判断过程');
console.log('3. 验证2024年7月30日显示为辛未');
console.log('4. 测试其他关键日期的月柱计算');

console.log('\n🏁 权威节气数据月柱修复测试完成！');
