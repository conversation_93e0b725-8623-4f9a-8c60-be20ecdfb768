/**
 * 测试完整八字计算器
 */

const CompleteBaziCalculator = require('./utils/complete_bazi_calculator.js');

console.log('🧪 开始测试完整八字计算器');
console.log('='.repeat(60));

// 创建计算器实例
const calculator = new CompleteBaziCalculator();

// 测试出生信息
const testBirthInfo = {
  year: 2006,
  month: 7,
  day: 23,
  hour: 14,
  minute: 30,
  gender: '男',
  longitude: 116.4074,
  latitude: 39.9042
};

console.log('📋 测试出生信息:', testBirthInfo);
console.log('-'.repeat(40));

try {
  // 执行完整计算
  const result = calculator.calculateComplete(testBirthInfo);
  
  console.log('✅ 计算成功！');
  console.log('');
  
  // 显示基础四柱
  console.log('🔮 基础四柱:');
  result.fourPillars.forEach((pillar, index) => {
    const names = ['年柱', '月柱', '日柱', '时柱'];
    console.log(`  ${names[index]}: ${pillar.gan}${pillar.zhi}`);
  });
  console.log('');
  
  // 显示纳音五行
  console.log('🎵 纳音五行:');
  Object.entries(result.nayin).forEach(([pillar, nayin]) => {
    console.log(`  ${pillar}: ${nayin}`);
  });
  console.log('');
  
  // 显示十神分析
  console.log('⚖️ 十神分析:');
  console.log(`  日干: ${result.tenGods.day_gan}`);
  console.log(`  年干十神: ${result.tenGods.year_gan_tengod}`);
  console.log(`  月干十神: ${result.tenGods.month_gan_tengod}`);
  console.log(`  日干十神: ${result.tenGods.day_gan_tengod}`);
  console.log(`  时干十神: ${result.tenGods.hour_gan_tengod}`);
  console.log('');
  
  // 显示十神统计
  console.log('📊 十神统计:');
  Object.entries(result.tenGods.statistics).forEach(([tengod, count]) => {
    if (count > 0) {
      console.log(`  ${tengod}: ${count}`);
    }
  });
  console.log('');
  
  // 显示长生十二宫
  console.log('🔄 长生十二宫:');
  console.log(`  日干: ${result.changsheng.day_gan}`);
  console.log(`  年柱: ${result.changsheng.year_pillar}`);
  console.log(`  月柱: ${result.changsheng.month_pillar}`);
  console.log(`  日柱: ${result.changsheng.day_pillar}`);
  console.log(`  时柱: ${result.changsheng.hour_pillar}`);
  console.log('');
  
  // 显示五行分析
  console.log('🌟 五行分析:');
  Object.entries(result.wuxing.count).forEach(([wuxing, count]) => {
    console.log(`  ${wuxing}: ${count.toFixed(1)}`);
  });
  console.log(`  平衡指数: ${result.wuxing.balance_index}`);
  console.log(`  最强五行: ${result.wuxing.strongest.wuxing} (${result.wuxing.strongest.count.toFixed(1)})`);
  console.log(`  最弱五行: ${result.wuxing.weakest.wuxing} (${result.wuxing.weakest.count.toFixed(1)})`);
  console.log('');
  
  // 显示神煞分析
  console.log('🌟 神煞分析:');
  console.log(`  吉星数量: ${result.shensha.auspicious_stars.length}`);
  console.log(`  凶星数量: ${result.shensha.inauspicious_stars.length}`);
  
  if (result.shensha.auspicious_stars.length > 0) {
    console.log('  吉星列表:');
    result.shensha.auspicious_stars.forEach(star => {
      console.log(`    ${star.name} (${star.position}): ${star.description}`);
    });
  }
  
  if (result.shensha.inauspicious_stars.length > 0) {
    console.log('  凶星列表:');
    result.shensha.inauspicious_stars.forEach(star => {
      console.log(`    ${star.name} (${star.position}): ${star.description}`);
    });
  }
  console.log('');
  
  // 显示格局分析
  console.log('🔬 格局分析:');
  console.log(`  格局名称: ${result.pattern.pattern_name}`);
  console.log(`  格局强度: ${result.pattern.pattern_strength}`);
  console.log(`  格局描述: ${result.pattern.pattern_description}`);
  console.log(`  月干十神: ${result.pattern.month_tengod}`);
  console.log(`  强度分析: ${result.pattern.analysis.level} (${result.pattern.analysis.score}分)`);
  console.log('');
  
  // 显示自坐分析
  console.log('🎯 自坐分析:');
  console.log(`  日干: ${result.selfSitting.day_gan}`);
  console.log(`  日支: ${result.selfSitting.day_zhi}`);
  console.log(`  主藏干: ${result.selfSitting.main_canggan}`);
  console.log(`  主十神: ${result.selfSitting.main_tengod}`);
  console.log(`  长生状态: ${result.selfSitting.changsheng_state}`);
  console.log(`  分析: ${result.selfSitting.analysis.description}`);
  console.log(`  强度: ${result.selfSitting.analysis.strength}`);
  console.log('');
  
  // 显示空亡分析
  console.log('⭕ 空亡分析:');
  console.log(`  旬名: ${result.kongwang.xun_name}`);
  console.log(`  空亡地支: ${result.kongwang.empty_branches.join(', ')}`);
  console.log(`  影响柱位: ${result.kongwang.affected_pillars.join(', ') || '无'}`);
  console.log(`  影响: ${result.kongwang.effect}`);
  console.log('');
  
  // 显示命卦分析
  console.log('🧭 命卦分析:');
  console.log(`  卦名: ${result.mingGua.gua_name}`);
  console.log(`  卦数: ${result.mingGua.gua_number}`);
  console.log(`  卦类: ${result.mingGua.category}`);
  console.log(`  五行: ${result.mingGua.element}`);
  console.log(`  吉方: ${result.mingGua.lucky_directions}`);
  console.log(`  描述: ${result.mingGua.description}`);
  console.log('');
  
  // 显示版本信息
  console.log('ℹ️ 版本信息:');
  const versionInfo = calculator.getVersion();
  console.log(`  版本: ${versionInfo.version}`);
  console.log(`  缓存大小: ${versionInfo.cacheSize}`);
  console.log(`  功能模块: ${versionInfo.features.length}个`);
  versionInfo.features.forEach(feature => {
    console.log(`    - ${feature}`);
  });
  
  console.log('');
  console.log('🎉 测试完成！所有模块都正常工作');
  
} catch (error) {
  console.error('❌ 测试失败:', error);
  console.error('错误详情:', error.stack);
}

console.log('');
console.log('🏁 测试结束');

// 测试缓存功能
console.log('');
console.log('🧪 测试缓存功能...');

const startTime = Date.now();
const result1 = calculator.calculateComplete(testBirthInfo);
const firstTime = Date.now() - startTime;

const startTime2 = Date.now();
const result2 = calculator.calculateComplete(testBirthInfo);
const secondTime = Date.now() - startTime2;

console.log(`第一次计算耗时: ${firstTime}ms`);
console.log(`第二次计算耗时: ${secondTime}ms (使用缓存)`);
console.log(`缓存效果: ${secondTime < firstTime ? '✅ 有效' : '❌ 无效'}`);

// 清除缓存测试
calculator.clearCache();
console.log('✅ 缓存已清除');

console.log('');
console.log('🎯 完整八字计算器测试全部完成！');
