<!--pages/index/index.wxml-->
<view class="chat-container">
  <!-- 顶部导航栏 -->
  <view class="top-nav">
    <view class="settings-icon" bindtap="navigateToProfile" hover-class="icon-hover">
      <image src="/assets/icons/new/profile_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="tab-container">
      <view class="tab {{activeTab === 'today' ? 'active' : ''}}" bindtap="switchTab" data-tab="today">今天</view>
      <view class="tab {{activeTab === 'history' ? 'active' : ''}}" bindtap="navigateToHistory" data-tab="history">历史</view>
    </view>
  </view>
  
  <!-- u65e5u671fu663eu793a -->
  <view class="date-display">{{currentDate}}</view>
  
  <!-- 欢迎消息区域 -->
  <view class="welcome-message" wx:if="{{messages.length === 0}}">
    <view class="welcome-title">{{welcomeTitle}}</view>
    <view class="welcome-subtitle">{{welcomeSubtitle}}</view>
    <view class="welcome-hint">
      <image src="/assets/icons/time.svg" mode="aspectFit" class="hint-icon"></image>
      <text>大约需要5-10分钟</text>
    </view>
    <view class="welcome-start-button" bindtap="sendMessage">
      <text>开始对话</text>
      <image src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 消息区域 -->
  <scroll-view class="message-area" scroll-y scroll-into-view="{{lastMessageId}}" id="message-container" enhanced="true" show-scrollbar="{{false}}" bounces="{{true}}" enable-flex="true">
    <block wx:for="{{messages}}" wx:key="id">
      <view class="message-wrapper {{item.role === 'AI' ? 'ai-wrapper' : 'user-wrapper'}}" id="msg-{{item.id}}">
        <view wx:if="{{item.role === 'AI'}}" class="avatar-container">
          <image src="/assets/icons/tiangong-master.png" class="avatar" mode="aspectFill"></image>
          <view class="avatar-name">天公师兄</view>
        </view>
        <view class="message {{item.role === 'AI' ? 'ai-message' : 'user-message'}}">
          <view class="message-content">{{item.content}}</view>
        </view>
        <view class="message-time" wx:if="{{item.time}}">{{item.time}}</view>
      </view>
    </block>
    
    <!-- 正在输入指示器 -->
    <view class="typing-wrapper" wx:if="{{typing}}">
      <view class="avatar-container">
        <image src="/assets/icons/tiangong-master.svg" class="avatar" mode="aspectFill"></image>
        <view class="avatar-name">天公师兄</view>
      </view>
      <view class="typing-indicator">
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
        <view class="typing-dot"></view>
      </view>
    </view>
    
    <!-- 底部空白，确保滚动到底部有足够空间 -->
    <view class="message-bottom-space"></view>
  </scroll-view>
  
  <!-- 底部输入区域 -->
  <view class="input-area {{assessmentComplete ? 'input-completed' : ''}}">
    <input class="message-input" placeholder="输入你的回答..." value="{{inputValue}}" bindinput="onInputChange" 
      disabled="{{assessmentComplete || sending}}" focus="{{messages.length > 0 && !assessmentComplete && !sending}}" 
      confirm-type="send" bindconfirm="sendMessage" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage" hover-class="button-hover">
      <image src="/assets/icons/new/send_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="voice-button" hover-class="button-hover">
      <image src="/assets/icons/new/voice_icon.svg" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 评估完成浮动按钮 -->
  <view class="action-button-container" wx:if="{{assessmentComplete}}">
    <view class="action-button view-report" bindtap="viewFullReport">
      <image src="/assets/icons/report.svg" mode="aspectFit"></image>
      <text>查看完整报告</text>
    </view>
    <view class="action-button restart" bindtap="restartAssessment">
      <image src="/assets/icons/assessment.svg" mode="aspectFit"></image>
      <text>重新开始</text>
    </view>
  </view>
</view>

<!-- 引导提示 -->
<view class="guide-tooltip {{messages.length === 1 ? 'show' : ''}}" wx:if="{{messages.length === 1}}">
  <view class="tooltip-content">
    <text>输入你的回答，帮助我了解你的状态</text>
    <view class="tooltip-arrow"></view>
  </view>
</view>
    
    