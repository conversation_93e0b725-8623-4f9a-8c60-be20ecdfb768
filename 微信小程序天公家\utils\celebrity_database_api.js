/**
 * 历史名人数据库访问接口
 * 提供300位历史名人数据的搜索、筛选、相似度匹配等功能
 * 用于专业细盘标签页的历史验证功能
 *
 * 更新: 新增100位女性历史名人，女性比例从3%提升到35%
 */

// 导入300名人完整数据库(含100位女性)
const celebritiesDatabase = require('../data/celebrities_database_300_complete');

class CelebrityDatabaseAPI {
  constructor() {
    this.celebrities = celebritiesDatabase.celebrities || [];
    this.metadata = celebritiesDatabase.metadata || {};
    
    // 创建索引以提高查询性能
    this.createIndexes();
  }

  /**
   * 创建数据索引
   */
  createIndexes() {
    // 按朝代索引
    this.dynastyIndex = {};
    // 按职业索引
    this.occupationIndex = {};
    // 按格局索引
    this.patternIndex = {};
    // 按用神索引
    this.yongshenIndex = {};

    this.celebrities.forEach((celebrity, index) => {
      const dynasty = celebrity.basicInfo.dynasty;
      const occupations = celebrity.basicInfo.occupation || [];
      const pattern = celebrity.pattern.mainPattern;
      const yongshen = celebrity.pattern.yongshen;

      // 朝代索引
      if (!this.dynastyIndex[dynasty]) {
        this.dynastyIndex[dynasty] = [];
      }
      this.dynastyIndex[dynasty].push(index);

      // 职业索引
      occupations.forEach(occupation => {
        if (!this.occupationIndex[occupation]) {
          this.occupationIndex[occupation] = [];
        }
        this.occupationIndex[occupation].push(index);
      });

      // 格局索引
      if (!this.patternIndex[pattern]) {
        this.patternIndex[pattern] = [];
      }
      this.patternIndex[pattern].push(index);

      // 用神索引
      if (!this.yongshenIndex[yongshen]) {
        this.yongshenIndex[yongshen] = [];
      }
      this.yongshenIndex[yongshen].push(index);
    });
  }

  /**
   * 根据ID获取名人信息
   * @param {string} id - 名人ID
   * @returns {Object|null} 名人信息
   */
  getCelebrityById(id) {
    return this.celebrities.find(celebrity => celebrity.id === id) || null;
  }

  /**
   * 获取所有名人信息
   * @returns {Array} 所有名人列表
   */
  getAllCelebrities() {
    return this.celebrities;
  }

  /**
   * 搜索名人
   * @param {Object} criteria - 搜索条件
   * @returns {Array} 搜索结果
   */
  searchCelebrities(criteria = {}) {
    let results = [...this.celebrities];

    // 按姓名搜索
    if (criteria.name) {
      const nameKeyword = criteria.name.toLowerCase();
      results = results.filter(celebrity => 
        celebrity.basicInfo.name.toLowerCase().includes(nameKeyword) ||
        (celebrity.basicInfo.courtesy && celebrity.basicInfo.courtesy.toLowerCase().includes(nameKeyword)) ||
        (celebrity.basicInfo.nickname && celebrity.basicInfo.nickname.toLowerCase().includes(nameKeyword))
      );
    }

    // 按朝代筛选
    if (criteria.dynasty) {
      results = results.filter(celebrity => 
        celebrity.basicInfo.dynasty === criteria.dynasty
      );
    }

    // 按职业筛选
    if (criteria.occupation) {
      results = results.filter(celebrity => 
        celebrity.basicInfo.occupation && 
        celebrity.basicInfo.occupation.includes(criteria.occupation)
      );
    }

    // 按格局筛选
    if (criteria.pattern) {
      results = results.filter(celebrity => 
        celebrity.pattern.mainPattern === criteria.pattern
      );
    }

    // 按用神筛选
    if (criteria.yongshen) {
      results = results.filter(celebrity =>
        celebrity.pattern.yongshen === criteria.yongshen
      );
    }

    // 按性别筛选
    if (criteria.gender) {
      results = results.filter(celebrity =>
        celebrity.basicInfo.gender === criteria.gender
      );
    }

    // 按验证分数筛选
    if (criteria.minScore) {
      results = results.filter(celebrity => 
        celebrity.verification.algorithmMatch >= criteria.minScore
      );
    }

    // 按时期筛选
    if (criteria.period) {
      results = results.filter(celebrity => {
        const birthYear = celebrity.basicInfo.birthYear;
        switch (criteria.period) {
          case '先秦':
            return birthYear < -221;
          case '秦汉':
            return birthYear >= -221 && birthYear < 220;
          case '魏晋南北朝':
            return birthYear >= 220 && birthYear < 581;
          case '隋唐五代':
            return birthYear >= 581 && birthYear < 960;
          case '宋元':
            return birthYear >= 960 && birthYear < 1368;
          case '明清':
            return birthYear >= 1368 && birthYear < 1912;
          case '近现代':
            return birthYear >= 1840 && birthYear < 1949;
          case '当代':
            return birthYear >= 1949;
          default:
            return true;
        }
      });
    }

    // 限制结果数量
    if (criteria.limit) {
      results = results.slice(0, criteria.limit);
    }

    return results;
  }

  /**
   * 🔧 性别格式标准化：处理中英文性别格式转换
   * @param {string} gender - 性别字符串（支持"男"/"女"/"male"/"female"）
   * @returns {string} - 标准化后的性别（"male"/"female"）
   */
  normalizeGender(gender) {
    if (!gender) return null;

    const genderStr = gender.toString().toLowerCase().trim();

    // 中文转英文映射
    const genderMap = {
      '男': 'male',
      '女': 'female',
      'male': 'male',
      'female': 'female',
      'm': 'male',
      'f': 'female'
    };

    return genderMap[genderStr] || null;
  }

  /**
   * 计算八字相似度
   * @param {Object} userBazi - 用户八字
   * @param {Object} celebrityBazi - 名人八字
   * @returns {number} 相似度分数 (0-1)
   */
  calculateBaziSimilarity(userBazi, celebrityBazi) {
    let similarity = 0;
    let totalWeight = 0;

    // 天干相似度 (权重: 0.3)
    const ganSimilarity = this.calculateGanSimilarity(userBazi, celebrityBazi);
    similarity += ganSimilarity * 0.3;
    totalWeight += 0.3;

    // 地支相似度 (权重: 0.3)
    const zhiSimilarity = this.calculateZhiSimilarity(userBazi, celebrityBazi);
    similarity += zhiSimilarity * 0.3;
    totalWeight += 0.3;

    // 日主相似度 (权重: 0.4)
    const dayMasterSimilarity = userBazi.day.gan === celebrityBazi.day.gan ? 1 : 0;
    similarity += dayMasterSimilarity * 0.4;
    totalWeight += 0.4;

    return totalWeight > 0 ? similarity / totalWeight : 0;
  }

  /**
   * 计算天干相似度
   */
  calculateGanSimilarity(userBazi, celebrityBazi) {
    const userGans = [userBazi.year.gan, userBazi.month.gan, userBazi.day.gan, userBazi.hour.gan];
    const celebrityGans = [celebrityBazi.year.gan, celebrityBazi.month.gan, celebrityBazi.day.gan, celebrityBazi.hour.gan];
    
    let matches = 0;
    for (let i = 0; i < 4; i++) {
      if (userGans[i] === celebrityGans[i]) {
        matches++;
      }
    }
    
    return matches / 4;
  }

  /**
   * 计算地支相似度
   */
  calculateZhiSimilarity(userBazi, celebrityBazi) {
    const userZhis = [userBazi.year.zhi, userBazi.month.zhi, userBazi.day.zhi, userBazi.hour.zhi];
    const celebrityZhis = [celebrityBazi.year.zhi, celebrityBazi.month.zhi, celebrityBazi.day.zhi, celebrityBazi.hour.zhi];
    
    let matches = 0;
    for (let i = 0; i < 4; i++) {
      if (userZhis[i] === celebrityZhis[i]) {
        matches++;
      }
    }
    
    return matches / 4;
  }

  /**
   * 计算格局相似度
   * @param {Object} userPattern - 用户格局信息
   * @param {Object} celebrityPattern - 名人格局信息
   * @returns {number} 相似度分数 (0-1)
   */
  calculatePatternSimilarity(userPattern, celebrityPattern) {
    let similarity = 0;
    let totalWeight = 0;

    // 主格局相似度 (权重: 0.5)
    if (userPattern.mainPattern === celebrityPattern.mainPattern) {
      similarity += 0.5;
    }
    totalWeight += 0.5;

    // 用神相似度 (权重: 0.3)
    if (userPattern.yongshen === celebrityPattern.yongshen) {
      similarity += 0.3;
    }
    totalWeight += 0.3;

    // 日主相似度 (权重: 0.2)
    if (userPattern.dayMaster === celebrityPattern.dayMaster) {
      similarity += 0.2;
    }
    totalWeight += 0.2;

    return totalWeight > 0 ? similarity / totalWeight : 0;
  }

  /**
   * 查找相似名人
   * @param {Object} userInfo - 用户信息 (包含八字和格局)
   * @param {Object} options - 选项
   * @returns {Array} 相似名人列表
   */
  findSimilarCelebrities(userInfo, options = {}) {
    const { limit = 10, minSimilarity = 0.3, userGender = null } = options;

    // 🎯 性别过滤：优先匹配同性别名人
    let filteredCelebrities = this.celebrities;
    if (userGender) {
      // 🔧 修复：处理中英文性别格式转换
      const targetGender = this.normalizeGender(userGender);
      const sameGenderCelebrities = this.celebrities.filter(celebrity =>
        this.normalizeGender(celebrity.basicInfo.gender) === targetGender
      );

      // 如果同性别名人数量足够，优先使用同性别
      if (sameGenderCelebrities.length >= limit * 2) {
        filteredCelebrities = sameGenderCelebrities;
        console.log(`🎯 性别匹配: 用户${userGender}，筛选出${sameGenderCelebrities.length}位同性别名人`);
      } else {
        console.log(`⚠️ 同性别名人数量不足(${sameGenderCelebrities.length})，使用全部名人库`);
      }
    }

    const results = filteredCelebrities.map(celebrity => {
      // 🔧 数据结构转换：将数据库格式转换为算法期望格式
      const celebrityBaziFormatted = {
        yearPillar: { gan: celebrity.bazi.year.gan, zhi: celebrity.bazi.year.zhi },
        monthPillar: { gan: celebrity.bazi.month.gan, zhi: celebrity.bazi.month.zhi },
        dayPillar: { gan: celebrity.bazi.day.gan, zhi: celebrity.bazi.day.zhi },
        timePillar: { gan: celebrity.bazi.hour.gan, zhi: celebrity.bazi.hour.zhi }
      };

      // 🔍 增强八字相似度计算
      const baziSimilarity = this.calculateEnhancedBaziSimilarity(userInfo.bazi, celebrityBaziFormatted);

      // 🔍 增强格局相似度计算
      const patternSimilarity = this.calculateEnhancedPatternSimilarity(userInfo.pattern, celebrity.pattern);

      // 🎯 性别加分：同性别额外加10%相似度
      let genderBonus = 0;
      if (userGender && celebrity.basicInfo.gender === userGender) {
        genderBonus = 0.1;
      }

      // 📊 综合相似度计算 (八字权重0.5，格局权重0.4，性别加分0.1)
      const overallSimilarity = Math.min(1.0, baziSimilarity * 0.5 + patternSimilarity * 0.4 + genderBonus);

      return {
        celebrity,
        similarity: overallSimilarity,
        baziSimilarity,
        patternSimilarity,
        genderBonus,
        level: this.getSimilarityLevel(overallSimilarity)
      };
    })
    .filter(result => result.similarity >= minSimilarity)
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit);

    console.log(`✅ 相似度匹配完成: 找到${results.length}位相似名人，平均相似度${Math.round(results.reduce((sum, r) => sum + r.similarity, 0) / results.length * 100)}%`);
    return results;
  }

  /**
   * 🔍 增强八字相似度计算
   * @param {Object} userBazi - 用户八字
   * @param {Object} celebrityBazi - 名人八字
   * @returns {Number} 相似度分数 (0-1)
   */
  calculateEnhancedBaziSimilarity(userBazi, celebrityBazi) {
    if (!userBazi || !celebrityBazi) return 0;

    let totalScore = 0;
    let totalWeight = 0;

    // 日柱相似度 (权重最高 0.4)
    if (userBazi.dayPillar && celebrityBazi.dayPillar) {
      const dayScore = this.calculatePillarSimilarity(userBazi.dayPillar, celebrityBazi.dayPillar);
      totalScore += dayScore * 0.4;
      totalWeight += 0.4;
    }

    // 月柱相似度 (权重 0.3)
    if (userBazi.monthPillar && celebrityBazi.monthPillar) {
      const monthScore = this.calculatePillarSimilarity(userBazi.monthPillar, celebrityBazi.monthPillar);
      totalScore += monthScore * 0.3;
      totalWeight += 0.3;
    }

    // 年柱相似度 (权重 0.2)
    if (userBazi.yearPillar && celebrityBazi.yearPillar) {
      const yearScore = this.calculatePillarSimilarity(userBazi.yearPillar, celebrityBazi.yearPillar);
      totalScore += yearScore * 0.2;
      totalWeight += 0.2;
    }

    // 时柱相似度 (权重 0.1)
    if (userBazi.timePillar && celebrityBazi.timePillar) {
      const timeScore = this.calculatePillarSimilarity(userBazi.timePillar, celebrityBazi.timePillar);
      totalScore += timeScore * 0.1;
      totalWeight += 0.1;
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 🔍 增强格局相似度计算
   * @param {Object} userPattern - 用户格局
   * @param {Object} celebrityPattern - 名人格局
   * @returns {Number} 相似度分数 (0-1)
   */
  calculateEnhancedPatternSimilarity(userPattern, celebrityPattern) {
    if (!userPattern || !celebrityPattern) return 0;

    let totalScore = 0;
    let totalWeight = 0;

    // 主格局相似度 (权重 0.5)
    if (userPattern.mainPattern && celebrityPattern.mainPattern) {
      const mainScore = userPattern.mainPattern === celebrityPattern.mainPattern ? 1.0 : 0.3;
      totalScore += mainScore * 0.5;
      totalWeight += 0.5;
    }

    // 用神相似度 (权重 0.3)
    if (userPattern.yongshen && celebrityPattern.yongshen) {
      const yongshenScore = userPattern.yongshen === celebrityPattern.yongshen ? 1.0 : 0.2;
      totalScore += yongshenScore * 0.3;
      totalWeight += 0.3;
    }

    // 格局强度相似度 (权重 0.2)
    if (userPattern.strength && celebrityPattern.strength) {
      const strengthScore = userPattern.strength === celebrityPattern.strength ? 1.0 : 0.5;
      totalScore += strengthScore * 0.2;
      totalWeight += 0.2;
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 🔍 计算单柱相似度
   * @param {Object} userPillar - 用户柱 {gan, zhi}
   * @param {Object} celebrityPillar - 名人柱 {gan, zhi}
   * @returns {Number} 相似度分数 (0-1)
   */
  calculatePillarSimilarity(userPillar, celebrityPillar) {
    if (!userPillar || !celebrityPillar) return 0;

    let score = 0;

    // 天干相同得0.6分
    if (userPillar.gan === celebrityPillar.gan) {
      score += 0.6;
    } else {
      // 天干五行相同得0.3分
      const userGanWuxing = this.getGanWuxing(userPillar.gan);
      const celebrityGanWuxing = this.getGanWuxing(celebrityPillar.gan);
      if (userGanWuxing === celebrityGanWuxing) {
        score += 0.3;
      }
    }

    // 地支相同得0.4分
    if (userPillar.zhi === celebrityPillar.zhi) {
      score += 0.4;
    } else {
      // 地支五行相同得0.2分
      const userZhiWuxing = this.getZhiWuxing(userPillar.zhi);
      const celebrityZhiWuxing = this.getZhiWuxing(celebrityPillar.zhi);
      if (userZhiWuxing === celebrityZhiWuxing) {
        score += 0.2;
      }
    }

    return Math.min(1.0, score);
  }

  /**
   * 🌿 获取天干五行
   * @param {String} gan - 天干
   * @returns {String} 五行
   */
  getGanWuxing(gan) {
    const ganWuxingMap = {
      '甲': '木', '乙': '木',
      '丙': '火', '丁': '火',
      '戊': '土', '己': '土',
      '庚': '金', '辛': '金',
      '壬': '水', '癸': '水'
    };
    return ganWuxingMap[gan] || '未知';
  }

  /**
   * 🌍 获取地支五行
   * @param {String} zhi - 地支
   * @returns {String} 五行
   */
  getZhiWuxing(zhi) {
    const zhiWuxingMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木',
      '辰': '土', '巳': '火', '午': '火', '未': '土',
      '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
    return zhiWuxingMap[zhi] || '未知';
  }

  /**
   * 🎯 获取相似度等级
   * @param {Number} similarity - 相似度分数 (0-1)
   * @returns {String} 相似度等级
   */
  getSimilarityLevel(similarity) {
    if (similarity >= 0.8) return '极高相似';
    if (similarity >= 0.7) return '高度相似';
    if (similarity >= 0.6) return '较高相似';
    if (similarity >= 0.5) return '中等相似';
    if (similarity >= 0.4) return '一般相似';
    return '低度相似';
  }

  /**
   * 根据姓名搜索名人
   * @param {string} name - 姓名关键词
   * @returns {Array} 搜索结果
   */
  searchByName(name) {
    return this.searchCelebrities({ name: name });
  }

  /**
   * 获取数据库元数据
   * @returns {Object} 元数据信息
   */
  getMetadata() {
    return this.metadata || {
      totalRecords: this.celebrities.length || 300,
      averageVerificationScore: 0.946,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    // 计算性别分布
    const genderCounts = this.celebrities.reduce((acc, celebrity) => {
      const gender = celebrity.basicInfo.gender || '未知';
      acc[gender] = (acc[gender] || 0) + 1;
      return acc;
    }, {});

    return {
      totalCelebrities: this.celebrities.length,
      genderDistribution: {
        male: genderCounts['男'] || 0,
        female: genderCounts['女'] || 0,
        unknown: genderCounts['未知'] || 0
      },
      dynastyDistribution: this.dynastyIndex ? Object.keys(this.dynastyIndex).map(dynasty => ({
        dynasty,
        count: this.dynastyIndex[dynasty].length
      })) : [],
      occupationDistribution: this.occupationIndex ? Object.keys(this.occupationIndex).map(occupation => ({
        occupation,
        count: this.occupationIndex[occupation].length
      })) : [],
      patternDistribution: this.patternIndex ? Object.keys(this.patternIndex).map(pattern => ({
        pattern,
        count: this.patternIndex[pattern].length
      })) : [],
      averageVerificationScore: this.metadata?.averageVerificationScore || 0.95
    };
  }

  /**
   * 获取随机名人
   * @param {number} count - 数量
   * @returns {Array} 随机名人列表
   */
  getRandomCelebrities(count = 5) {
    const shuffled = [...this.celebrities].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * 获取推荐名人 (基于验证分数)
   * @param {number} count - 数量
   * @returns {Array} 推荐名人列表
   */
  getRecommendedCelebrities(count = 10) {
    return [...this.celebrities]
      .sort((a, b) => b.verification.algorithmMatch - a.verification.algorithmMatch)
      .slice(0, count);
  }
}

// 导出类而不是实例，以支持多实例化
module.exports = CelebrityDatabaseAPI;
