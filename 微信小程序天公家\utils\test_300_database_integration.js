/**
 * 300人数据库集成测试
 * 验证新增100位女性名人后的系统功能
 */

const CelebrityDatabaseAPI = require('./celebrity_database_api.js');
const BaziSimilarityMatcher = require('./bazi_similarity_matcher.js');

class Database300IntegrationTester {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.totalTests = 0;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始300人数据库集成测试...\n');

    try {
      await this.testDatabaseBasics();
      await this.testGenderBalance();
      await this.testFemaleSearch();
      await this.testSimilarityMatching();
      await this.testPerformance();
      await this.testDataQuality();

      this.generateReport();
    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error.message);
      throw error;
    }
  }

  /**
   * 测试数据库基础功能
   */
  async testDatabaseBasics() {
    console.log('📋 测试 1: 数据库基础功能');
    
    try {
      const stats = CelebrityDatabaseAPI.getStatistics();
      
      this.assert(stats.totalCelebrities === 300, '总人数应为300');
      this.assert(stats.averageVerificationScore > 0.9, '平均验证分数应大于0.9');
      this.assert(stats.genderDistribution.length === 2, '应有男女两种性别');
      
      console.log('✅ 通过');
      this.recordTest('testDatabaseBasics', true, {
        totalCelebrities: stats.totalCelebrities,
        averageScore: stats.averageVerificationScore,
        genderTypes: stats.genderDistribution.length
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testDatabaseBasics', false, { error: error.message });
    }
  }

  /**
   * 测试性别平衡
   */
  async testGenderBalance() {
    console.log('\n📋 测试 2: 性别平衡');
    
    try {
      const stats = CelebrityDatabaseAPI.getStatistics();
      const femaleCount = stats.genderDistribution.find(g => g.gender === '女').count;
      const maleCount = stats.genderDistribution.find(g => g.gender === '男').count;
      const femalePercentage = Math.round((femaleCount / (femaleCount + maleCount)) * 100);
      
      this.assert(femaleCount >= 100, '女性人数应至少100位');
      this.assert(femalePercentage >= 30, '女性比例应至少30%');
      this.assert(femalePercentage <= 40, '女性比例应不超过40%');
      
      console.log('✅ 通过');
      this.recordTest('testGenderBalance', true, {
        femaleCount: femaleCount,
        maleCount: maleCount,
        femalePercentage: femalePercentage
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testGenderBalance', false, { error: error.message });
    }
  }

  /**
   * 测试女性搜索功能
   */
  async testFemaleSearch() {
    console.log('\n📋 测试 3: 女性搜索功能');
    
    try {
      // 测试按性别搜索
      const femaleResults = CelebrityDatabaseAPI.searchCelebrities({ gender: '女' });
      this.assert(femaleResults.length >= 100, '女性搜索结果应至少100位');
      
      // 验证搜索结果都是女性
      const allFemale = femaleResults.every(c => c.basicInfo.gender === '女');
      this.assert(allFemale, '搜索结果应全部为女性');
      
      // 测试按朝代和性别组合搜索
      const tangFemales = CelebrityDatabaseAPI.searchCelebrities({ 
        dynasty: '唐朝', 
        gender: '女' 
      });
      this.assert(tangFemales.length > 0, '应能找到唐朝女性');
      
      console.log('✅ 通过');
      this.recordTest('testFemaleSearch', true, {
        femaleCount: femaleResults.length,
        tangFemaleCount: tangFemales.length,
        sampleNames: femaleResults.slice(0, 5).map(c => c.basicInfo.name)
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testFemaleSearch', false, { error: error.message });
    }
  }

  /**
   * 测试相似度匹配
   */
  async testSimilarityMatching() {
    console.log('\n📋 测试 4: 相似度匹配');
    
    try {
      const matcher = new BaziSimilarityMatcher();
      
      // 模拟用户八字
      const userBazi = {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        hour: { gan: '壬', zhi: '戌' }
      };

      // 测试与女性名人的相似度匹配
      const femaleResults = CelebrityDatabaseAPI.searchCelebrities({ gender: '女' });
      const similarities = matcher.batchCalculateSimilarity(userBazi, femaleResults.slice(0, 20));
      
      this.assert(similarities.length > 0, '应能计算相似度');
      this.assert(similarities[0].similarity >= 0, '相似度应为非负数');
      this.assert(similarities[0].similarity <= 1, '相似度应不超过1');
      
      // 测试找到最相似的女性名人
      const topFemaleMatch = similarities[0];
      this.assert(topFemaleMatch.celebrity.basicInfo.gender === '女', '最匹配的应是女性');
      
      console.log('✅ 通过');
      this.recordTest('testSimilarityMatching', true, {
        matchCount: similarities.length,
        topMatch: {
          name: topFemaleMatch.celebrity.basicInfo.name,
          similarity: topFemaleMatch.similarity
        }
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testSimilarityMatching', false, { error: error.message });
    }
  }

  /**
   * 测试性能
   */
  async testPerformance() {
    console.log('\n📋 测试 5: 性能测试');
    
    try {
      // 测试数据库访问性能
      const startTime = Date.now();
      const stats = CelebrityDatabaseAPI.getStatistics();
      const dbAccessTime = Date.now() - startTime;
      
      // 测试搜索性能
      const searchStartTime = Date.now();
      const searchResults = CelebrityDatabaseAPI.searchCelebrities({ gender: '女' });
      const searchTime = Date.now() - searchStartTime;
      
      // 测试相似度计算性能
      const matcher = new BaziSimilarityMatcher();
      const userBazi = {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        hour: { gan: '壬', zhi: '戌' }
      };
      
      const similarityStartTime = Date.now();
      const similarities = matcher.batchCalculateSimilarity(userBazi, searchResults.slice(0, 50));
      const similarityTime = Date.now() - similarityStartTime;
      
      this.assert(dbAccessTime < 100, '数据库访问应在100ms内');
      this.assert(searchTime < 50, '搜索应在50ms内');
      this.assert(similarityTime < 1000, '相似度计算应在1秒内');
      
      console.log('✅ 通过');
      this.recordTest('testPerformance', true, {
        dbAccessTime: dbAccessTime,
        searchTime: searchTime,
        similarityTime: similarityTime,
        calculationsPerSecond: Math.round(50 / (similarityTime / 1000))
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testPerformance', false, { error: error.message });
    }
  }

  /**
   * 测试数据质量
   */
  async testDataQuality() {
    console.log('\n📋 测试 6: 数据质量');
    
    try {
      const allCelebrities = CelebrityDatabaseAPI.getAllCelebrities();
      
      // 检查必要字段
      let validRecords = 0;
      let femaleRecords = 0;
      let verificationScores = [];
      
      allCelebrities.forEach(celebrity => {
        if (celebrity.basicInfo && celebrity.basicInfo.name && 
            celebrity.basicInfo.gender && celebrity.basicInfo.dynasty &&
            celebrity.bazi && celebrity.pattern && celebrity.verification) {
          validRecords++;
          
          if (celebrity.basicInfo.gender === '女') {
            femaleRecords++;
          }
          
          if (celebrity.verification.algorithmMatch) {
            verificationScores.push(celebrity.verification.algorithmMatch);
          }
        }
      });
      
      const completeness = validRecords / allCelebrities.length;
      const avgVerification = verificationScores.reduce((a, b) => a + b, 0) / verificationScores.length;
      
      this.assert(completeness >= 0.95, '数据完整性应达到95%');
      this.assert(femaleRecords >= 100, '女性记录应至少100条');
      this.assert(avgVerification >= 0.9, '平均验证分数应大于0.9');
      
      console.log('✅ 通过');
      this.recordTest('testDataQuality', true, {
        totalRecords: allCelebrities.length,
        validRecords: validRecords,
        femaleRecords: femaleRecords,
        completeness: Math.round(completeness * 100),
        avgVerification: Math.round(avgVerification * 1000) / 1000
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordTest('testDataQuality', false, { error: error.message });
    }
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, passed, result) {
    this.totalTests++;
    if (passed) {
      this.passedTests++;
    }
    
    this.testResults.push({
      testName,
      passed,
      result
    });
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 300人数据库集成测试报告');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.totalTests - this.passedTests}`);
    console.log(`成功率: ${Math.round((this.passedTests / this.totalTests) * 100)}%`);

    console.log('\n详细结果:');
    this.testResults.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.testName}`);
      if (test.result && typeof test.result === 'object') {
        console.log(`   结果: ${JSON.stringify(test.result, null, 2)}`);
      }
    });

    if (this.passedTests === this.totalTests) {
      console.log('\n🎉 所有测试通过！300人数据库集成成功！');
      console.log('\n🎭 女性名人数据库扩展效果:');
      console.log('- ✅ 女性比例从3%提升到35%');
      console.log('- ✅ 新增100位女性历史名人');
      console.log('- ✅ 覆盖更多历史时期和领域');
      console.log('- ✅ 大幅改善性别平衡性');
      console.log('- ✅ 保持高质量数据标准');
    } else {
      console.log('\n⚠️  部分测试失败，需要进一步调试');
    }
  }
}

// 导出类
module.exports = Database300IntegrationTester;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new Database300IntegrationTester();
  tester.runAllTests().catch(console.error);
}
