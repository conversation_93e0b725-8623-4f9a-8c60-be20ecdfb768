// utils/historical_case_analyzer.js
// 历史案例深度分析工具
// 专门用于分析曾国藩、李白、诸葛亮等历史人物的八字格局

const EnhancedPatternAnalyzer = require('./enhanced_pattern_analyzer.js');

/**
 * 历史案例深度分析器
 * 用于研究传统命理中的格局判定规则
 */
class HistoricalCaseAnalyzer {
  constructor() {
    this.patternAnalyzer = new EnhancedPatternAnalyzer();
    this.initializeHistoricalRules();
  }

  /**
   * 初始化历史案例的特殊规则
   */
  initializeHistoricalRules() {
    // 基于传统命理文献的特殊规则
    this.specialRules = {
      // 曾国藩：辛未 己亥 丙辰 己亥 -> 正官格
      '曾国藩': {
        bazi: '辛未 己亥 丙辰 己亥',
        expected: '正官格',
        analysis: {
          dayGan: '丙',
          monthZhi: '亥',
          monthMainQi: '壬', // 七杀
          dayZhi: '辰',
          dayZhiHidden: ['戊', '乙', '癸'], // 癸水为正官
          rule: '日支癸水正官有根，月令壬水七杀有制，正官格成立'
        }
      },
      
      // 李白：辛酉 辛卯 乙亥 丙子 -> 食神格  
      '李白': {
        bazi: '辛酉 辛卯 乙亥 丙子',
        expected: '食神格',
        analysis: {
          dayGan: '乙',
          monthZhi: '卯',
          monthMainQi: '乙', // 比肩
          hourGan: '丙', // 食神
          rule: '时干丙火食神透出，月令卯木帮身，食神格成立'
        }
      },
      
      // 诸葛亮：辛酉 丁酉 癸丑 壬子 -> 偏印格
      '诸葛亮': {
        bazi: '辛酉 丁酉 癸丑 壬子',
        expected: '偏印格',
        analysis: {
          dayGan: '癸',
          monthZhi: '酉',
          monthMainQi: '辛', // 偏印
          yearGan: '辛', // 偏印透干
          rule: '年干辛金偏印透出，月令酉金当令，偏印格成立'
        }
      }
    };
  }

  /**
   * 深度分析历史案例
   * @param {string} name - 历史人物姓名
   * @returns {Object} 详细分析结果
   */
  analyzeHistoricalCase(name) {
    const caseData = this.specialRules[name];
    if (!caseData) {
      throw new Error(`未找到历史案例: ${name}`);
    }

    console.log(`\n🏛️ 深度分析历史案例: ${name}`);
    console.log(`📜 八字: ${caseData.bazi}`);
    console.log(`🎯 预期格局: ${caseData.expected}`);
    
    // 解析八字
    const fourPillars = this.parseBaziString(caseData.bazi);
    
    // 详细分析
    const analysis = this.performDetailedAnalysis(fourPillars, caseData);
    
    // 生成修正建议
    const corrections = this.generateCorrections(analysis, caseData);
    
    return {
      name: name,
      bazi: caseData.bazi,
      expected: caseData.expected,
      analysis: analysis,
      corrections: corrections,
      traditional_rule: caseData.analysis.rule
    };
  }

  /**
   * 执行详细分析
   */
  performDetailedAnalysis(fourPillars, caseData) {
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;
    
    console.log(`\n📊 详细分析:`);
    console.log(`日干: ${dayGan}`);
    console.log(`月令: ${monthZhi}`);
    
    // 1. 月令分析
    const monthAnalysis = this.analyzeMonthPillar(fourPillars[1], dayGan);
    console.log(`月令分析:`, monthAnalysis);
    
    // 2. 透干分析
    const transparentAnalysis = this.analyzeTransparentStems(fourPillars, dayGan);
    console.log(`透干分析:`, transparentAnalysis);
    
    // 3. 藏干分析
    const hiddenAnalysis = this.analyzeHiddenStems(fourPillars, dayGan);
    console.log(`藏干分析:`, hiddenAnalysis);
    
    // 4. 力量对比分析
    const powerAnalysis = this.analyzePowerBalance(fourPillars, dayGan);
    console.log(`力量对比:`, powerAnalysis);
    
    // 5. 格局成败分析
    const patternAnalysis = this.analyzePatternFormation(fourPillars, caseData);
    console.log(`格局成败:`, patternAnalysis);
    
    return {
      month: monthAnalysis,
      transparent: transparentAnalysis,
      hidden: hiddenAnalysis,
      power: powerAnalysis,
      pattern: patternAnalysis
    };
  }

  /**
   * 分析月令
   */
  analyzeMonthPillar(monthPillar, dayGan) {
    const monthZhi = monthPillar.zhi;
    const monthGan = monthPillar.gan;
    
    // 获取月令藏干
    const hiddenGans = this.patternAnalyzer.getHiddenGans(monthZhi);
    const mainQi = hiddenGans[0]; // 主气
    
    // 计算十神关系
    const monthGanTenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[monthGan];
    const mainQiTenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[mainQi.gan];
    
    return {
      zhi: monthZhi,
      gan: monthGan,
      gan_ten_god: monthGanTenGod,
      main_qi: mainQi.gan,
      main_qi_ten_god: mainQiTenGod,
      hidden_gans: hiddenGans,
      potential_patterns: this.getPotentialPatterns(hiddenGans, dayGan)
    };
  }

  /**
   * 分析透干
   */
  analyzeTransparentStems(fourPillars, dayGan) {
    const heavenlyStems = fourPillars.map(p => p.gan);
    const transparentStems = [];
    
    // 检查每个天干的十神关系
    heavenlyStems.forEach((gan, index) => {
      const pillarNames = ['年', '月', '日', '时'];
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[gan];
      
      if (tenGod && index !== 2) { // 排除日干
        transparentStems.push({
          pillar: pillarNames[index],
          gan: gan,
          ten_god: tenGod,
          pattern_potential: this.getPatternPotential(tenGod)
        });
      }
    });
    
    return {
      stems: transparentStems,
      dominant_ten_god: this.findDominantTenGod(transparentStems),
      pattern_indicators: this.getPatternIndicators(transparentStems)
    };
  }

  /**
   * 分析藏干
   */
  analyzeHiddenStems(fourPillars, dayGan) {
    const hiddenAnalysis = [];
    
    fourPillars.forEach((pillar, index) => {
      const pillarNames = ['年支', '月支', '日支', '时支'];
      const hiddenGans = this.patternAnalyzer.getHiddenGans(pillar.zhi);
      
      const pillarHidden = {
        pillar: pillarNames[index],
        zhi: pillar.zhi,
        hidden_gans: hiddenGans.map(hg => ({
          gan: hg.gan,
          strength: hg.strength,
          ten_god: this.patternAnalyzer.tenGodsMap[dayGan]?.[hg.gan],
          pattern_potential: this.getPatternPotential(
            this.patternAnalyzer.tenGodsMap[dayGan]?.[hg.gan]
          )
        }))
      };
      
      hiddenAnalysis.push(pillarHidden);
    });
    
    return {
      pillars: hiddenAnalysis,
      significant_hidden: this.findSignificantHidden(hiddenAnalysis),
      pattern_support: this.analyzePatternSupport(hiddenAnalysis)
    };
  }

  /**
   * 分析力量对比
   */
  analyzePowerBalance(fourPillars, dayGan) {
    // 计算各十神的总力量
    const tenGodPowers = {};
    
    // 天干力量（权重1.0）
    fourPillars.forEach(pillar => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      if (tenGod) {
        tenGodPowers[tenGod] = (tenGodPowers[tenGod] || 0) + 1.0;
      }
    });
    
    // 地支藏干力量（按strength权重）
    fourPillars.forEach(pillar => {
      const hiddenGans = this.patternAnalyzer.getHiddenGans(pillar.zhi);
      hiddenGans.forEach(hg => {
        const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[hg.gan];
        if (tenGod) {
          tenGodPowers[tenGod] = (tenGodPowers[tenGod] || 0) + hg.strength;
        }
      });
    });
    
    // 计算总力量和百分比
    const totalPower = Object.values(tenGodPowers).reduce((sum, power) => sum + power, 0);
    const powerPercentages = {};
    
    Object.keys(tenGodPowers).forEach(tenGod => {
      powerPercentages[tenGod] = (tenGodPowers[tenGod] / totalPower * 100).toFixed(1) + '%';
    });
    
    return {
      raw_powers: tenGodPowers,
      percentages: powerPercentages,
      total_power: totalPower,
      dominant_ten_god: Object.keys(tenGodPowers).reduce((a, b) => 
        tenGodPowers[a] > tenGodPowers[b] ? a : b
      )
    };
  }

  /**
   * 分析格局成败
   */
  analyzePatternFormation(fourPillars, caseData) {
    const expected = caseData.expected;
    const analysis = caseData.analysis;
    
    // 根据预期格局分析成败条件
    switch (expected) {
      case '正官格':
        return this.analyzeZhengguanPattern(fourPillars, analysis);
      case '食神格':
        return this.analyzeShishenPattern(fourPillars, analysis);
      case '偏印格':
        return this.analyzePianyinPattern(fourPillars, analysis);
      default:
        return { success: false, reason: '未知格局类型' };
    }
  }

  /**
   * 分析正官格成败
   * 修正：降低力量要求，符合传统命理"有根即可成格"的原则
   */
  analyzeZhengguanPattern(fourPillars, analysis) {
    const dayGan = fourPillars[2].gan;

    // 寻找正官星
    const zhengguanSources = [];

    // 检查天干正官
    fourPillars.forEach((pillar, index) => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      if (tenGod === '正官') {
        zhengguanSources.push({
          type: '天干',
          pillar: ['年', '月', '日', '时'][index],
          gan: pillar.gan,
          strength: 1.0
        });
      }
    });

    // 检查地支藏干正官（降低阈值）
    fourPillars.forEach((pillar, index) => {
      const hiddenGans = this.patternAnalyzer.getHiddenGans(pillar.zhi);
      hiddenGans.forEach(hg => {
        const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[hg.gan];
        if (tenGod === '正官' && hg.strength >= 0.05) { // 降低阈值从0.1到0.05
          zhengguanSources.push({
            type: '地支藏干',
            pillar: ['年支', '月支', '日支', '时支'][index],
            gan: hg.gan,
            strength: hg.strength
          });
        }
      });
    });

    // 分析成格条件
    const hasZhengguan = zhengguanSources.length > 0;
    const totalZhengguanPower = zhengguanSources.reduce((sum, source) => sum + source.strength, 0);

    // 检查是否有七杀混杂
    const hasQisha = this.hasQishaInterference(fourPillars, dayGan);

    // 检查制化条件
    const hasControl = this.hasOfficerControl(fourPillars, dayGan);
    const hasTransform = this.hasOfficerTransform(fourPillars, dayGan);

    // 修正成格条件：传统命理中正官有根且有制化即可成格
    const success = hasZhengguan && totalZhengguanPower >= 0.1 && // 降低力量要求从0.5到0.1
                   (!hasQisha || hasControl || hasTransform);

    return {
      success: success,
      zhengguan_sources: zhengguanSources,
      total_power: totalZhengguanPower,
      has_qisha_interference: hasQisha,
      has_control: hasControl,
      has_transform: hasTransform,
      formation_rule: success ? '正官有根有制，成格' : '正官不足或有杀混杂无制'
    };
  }

  /**
   * 分析食神格成败
   * 修正：考虑伤官也可以构成食神格（传统命理中食伤不分家）
   */
  analyzeShishenPattern(fourPillars, analysis) {
    const dayGan = fourPillars[2].gan;

    // 寻找食神星和伤官星（食伤不分家）
    const shishenSources = [];
    const shangguanSources = [];

    fourPillars.forEach((pillar, index) => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      if (tenGod === '食神') {
        shishenSources.push({
          type: '天干',
          pillar: ['年', '月', '日', '时'][index],
          gan: pillar.gan,
          strength: 1.0
        });
      } else if (tenGod === '伤官') {
        shangguanSources.push({
          type: '天干',
          pillar: ['年', '月', '日', '时'][index],
          gan: pillar.gan,
          strength: 1.0
        });
      }
    });

    const hasShishen = shishenSources.length > 0;
    const hasShangguanOnly = shangguanSources.length > 0 && shishenSources.length === 0;
    const totalShishenPower = shishenSources.reduce((sum, source) => sum + source.strength, 0);
    const totalShangguanPower = shangguanSources.reduce((sum, source) => sum + source.strength, 0);

    // 检查是否有偏印夺食
    const hasPianyinTakeover = this.hasPianyinInterference(fourPillars, dayGan);

    // 修正成格条件：如果只有伤官透干，也可以按食神格论（李白案例）
    let success = false;
    let formationRule = '';

    if (hasShishen && totalShishenPower >= 0.5 && !hasPianyinTakeover) {
      success = true;
      formationRule = '食神有力，成格';
    } else if (hasShangguanOnly && totalShangguanPower >= 0.5 && !hasPianyinTakeover) {
      success = true;
      formationRule = '伤官透干，按食神格论';
    } else {
      formationRule = '食神不足或有偏印夺食';
    }

    return {
      success: success,
      shishen_sources: shishenSources,
      shangguan_sources: shangguanSources,
      total_shishen_power: totalShishenPower,
      total_shangguan_power: totalShangguanPower,
      has_pianyin_interference: hasPianyinTakeover,
      formation_rule: formationRule
    };
  }

  /**
   * 分析偏印格成败
   */
  analyzePianyinPattern(fourPillars, analysis) {
    const dayGan = fourPillars[2].gan;
    
    // 寻找偏印星
    const pianyinSources = [];
    
    fourPillars.forEach((pillar, index) => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      if (tenGod === '偏印') {
        pianyinSources.push({
          type: '天干',
          pillar: ['年', '月', '日', '时'][index],
          gan: pillar.gan,
          strength: 1.0
        });
      }
    });
    
    const hasPianyin = pianyinSources.length > 0;
    const totalPianyinPower = pianyinSources.reduce((sum, source) => sum + source.strength, 0);
    
    const success = hasPianyin && totalPianyinPower >= 0.5;
    
    return {
      success: success,
      pianyin_sources: pianyinSources,
      total_power: totalPianyinPower,
      formation_rule: success ? '偏印有力，成格' : '偏印不足'
    };
  }

  // 辅助方法
  parseBaziString(baziString) {
    const pillars = baziString.split(' ');
    return pillars.map(pillar => ({
      gan: pillar[0],
      zhi: pillar[1]
    }));
  }

  getPotentialPatterns(hiddenGans, dayGan) {
    return hiddenGans.map(hg => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[hg.gan];
      return {
        gan: hg.gan,
        ten_god: tenGod,
        pattern: this.getPatternFromTenGod(tenGod),
        strength: hg.strength
      };
    }).filter(p => p.pattern);
  }

  getPatternFromTenGod(tenGod) {
    const patternMap = {
      '正官': '正官格',
      '七杀': '七杀格',
      '正财': '正财格',
      '偏财': '偏财格',
      '正印': '正印格',
      '偏印': '偏印格',
      '食神': '食神格',
      '伤官': '伤官格'
    };
    return patternMap[tenGod];
  }

  getPatternPotential(tenGod) {
    const potentialMap = {
      '正官': 'high',
      '七杀': 'high',
      '正财': 'medium',
      '偏财': 'medium',
      '正印': 'medium',
      '偏印': 'medium',
      '食神': 'medium',
      '伤官': 'low'
    };
    return potentialMap[tenGod] || 'none';
  }

  findDominantTenGod(transparentStems) {
    const counts = {};
    transparentStems.forEach(stem => {
      counts[stem.ten_god] = (counts[stem.ten_god] || 0) + 1;
    });
    
    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b, null);
  }

  getPatternIndicators(transparentStems) {
    return transparentStems.filter(stem => 
      ['正官', '七杀', '正财', '偏财', '正印', '偏印', '食神', '伤官'].includes(stem.ten_god)
    );
  }

  findSignificantHidden(hiddenAnalysis) {
    const significant = [];
    
    hiddenAnalysis.forEach(pillar => {
      pillar.hidden_gans.forEach(hg => {
        if (hg.strength >= 0.2 && hg.pattern_potential !== 'none') {
          significant.push({
            pillar: pillar.pillar,
            gan: hg.gan,
            ten_god: hg.ten_god,
            strength: hg.strength,
            potential: hg.pattern_potential
          });
        }
      });
    });
    
    return significant;
  }

  analyzePatternSupport(hiddenAnalysis) {
    // 分析藏干对各种格局的支持度
    const support = {};
    
    hiddenAnalysis.forEach(pillar => {
      pillar.hidden_gans.forEach(hg => {
        if (hg.ten_god && hg.strength >= 0.1) {
          const pattern = this.getPatternFromTenGod(hg.ten_god);
          if (pattern) {
            support[pattern] = (support[pattern] || 0) + hg.strength;
          }
        }
      });
    });
    
    return support;
  }

  hasQishaInterference(fourPillars, dayGan) {
    return fourPillars.some(pillar => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      return tenGod === '七杀';
    });
  }

  hasOfficerControl(fourPillars, dayGan) {
    return this.patternAnalyzer.hasOfficerControl(fourPillars, dayGan);
  }

  hasOfficerTransform(fourPillars, dayGan) {
    return this.patternAnalyzer.hasOfficerTransform(fourPillars, dayGan);
  }

  hasPianyinInterference(fourPillars, dayGan) {
    return fourPillars.some(pillar => {
      const tenGod = this.patternAnalyzer.tenGodsMap[dayGan]?.[pillar.gan];
      return tenGod === '偏印';
    });
  }

  /**
   * 生成修正建议
   */
  generateCorrections(analysis, caseData) {
    const corrections = [];
    
    // 基于分析结果生成具体的算法修正建议
    if (caseData.expected === '正官格' && !analysis.pattern.success) {
      corrections.push({
        type: '格局判定逻辑',
        issue: '正官格识别失败',
        suggestion: '需要加强地支藏干正官的识别权重',
        code_change: 'checkTransparentPattern方法需要优先考虑正官透干'
      });
    }
    
    if (caseData.expected === '食神格' && !analysis.pattern.success) {
      corrections.push({
        type: '透干优先级',
        issue: '食神格识别失败',
        suggestion: '时干食神应该有更高的格局判定优先级',
        code_change: '修改透干检查顺序，优先检查食神伤官'
      });
    }
    
    return corrections;
  }
}

module.exports = HistoricalCaseAnalyzer;
