<!--pages/bazi-input/index.wxml-->
<!-- 八字排盘信息输入页面 -->

<view class="container">
  <!-- 师父身份显示 -->
  <view class="master-info">
    <image class="master-avatar" src="/assets/icons/tiangong-shifu.png"></image>
    <view class="master-text">
      <text class="master-name">{{master}}</text>
      <text class="master-subtitle">玉匣记八字排盘</text>
    </view>
  </view>

  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">🏛️</view>
    <view class="header-title">传承古典智慧，融合现代技术</view>
    <view class="header-subtitle" wx:if="{{question}}">您的问题：{{question}}</view>
  </view>

  <!-- 出生信息输入区域 -->
  <view class="input-section">
    <view class="section-title">
      <text class="title-icon">📅</text>
      <text class="title-text">出生信息</text>
      <text class="title-tip">请准确填写，时间越精确分析越准确</text>
    </view>

    <!-- 出生日期 -->
    <view class="input-group">
      <view class="input-label">出生日期</view>
      <view class="date-picker-group">
        <picker class="date-picker" mode="selector" range="{{years}}" value="{{yearIndex}}" bindchange="onYearChange">
          <view class="picker-display">{{years[yearIndex] || '选择年份'}}</view>
        </picker>
        <picker class="date-picker" mode="selector" range="{{months}}" value="{{monthIndex}}" bindchange="onMonthChange">
          <view class="picker-display">{{months[monthIndex]}}</view>
        </picker>
        <picker class="date-picker" mode="selector" range="{{days}}" value="{{dayIndex}}" bindchange="onDayChange">
          <view class="picker-display">{{days[dayIndex] || '选择日期'}}</view>
        </picker>
      </view>
    </view>

    <!-- 出生时间 -->
    <view class="input-group">
      <view class="input-label">出生时间</view>
      <view class="time-picker-group">
        <picker class="time-picker" mode="selector" range="{{hours}}" value="{{hourIndex}}" bindchange="onHourChange">
          <view class="picker-display">{{hours[hourIndex]}}</view>
        </picker>
        <picker class="time-picker" mode="selector" range="{{minutes}}" value="{{minuteIndex}}" bindchange="onMinuteChange">
          <view class="picker-display">{{minutes[minuteIndex]}}</view>
        </picker>
      </view>
    </view>

    <!-- 性别选择 -->
    <view class="input-group">
      <view class="input-label">性别</view>
      <picker class="gender-picker" mode="selector" range="{{genders}}" value="{{genderIndex}}" bindchange="onGenderChange">
        <view class="picker-display gender-display">
          <text class="gender-icon">{{birthInfo.gender === '男' ? '👨' : '👩'}}</text>
          <text>{{birthInfo.gender}}</text>
        </view>
      </picker>
    </view>

    <!-- 出生地点 -->
    <view class="input-group">
      <view class="input-label">出生地点</view>
      <view class="location-input" bindtap="showLocationInput">
        <text class="location-icon">📍</text>
        <text class="location-text">{{birthInfo.location}}</text>
        <text class="location-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 分析模式选择 -->
  <view class="mode-section">
    <view class="section-title">
      <text class="title-icon">🎯</text>
      <text class="title-text">分析模式</text>
      <text class="title-tip">选择适合的分析深度</text>
    </view>

    <picker class="mode-picker" mode="selector" range="{{analysisModes}}" range-key="name" value="{{selectedModeIndex}}" bindchange="onModeChange">
      <view class="mode-display">
        <view class="mode-name">{{analysisModes[selectedModeIndex].name}}</view>
        <view class="mode-desc">{{analysisModes[selectedModeIndex].desc}}</view>
        <text class="mode-arrow">></text>
      </view>
    </picker>
  </view>

  <!-- 操作按钮 -->
  <view class="button-section">
    <button class="help-button" bindtap="showHelp">
      <text class="button-icon">❓</text>
      <text>使用说明</text>
    </button>
    
    <button class="start-button" bindtap="startPaipan" loading="{{loading}}" disabled="{{loading}}">
      <text class="button-icon">🔮</text>
      <text>{{loading ? '排盘中...' : '开始排盘'}}</text>
    </button>
  </view>

  <!-- 地点输入弹窗 -->
  <view class="location-modal {{showLocationInput ? 'show' : ''}}" wx:if="{{showLocationInput}}">
    <view class="modal-mask" bindtap="cancelLocation"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">输入出生地点</text>
        <text class="modal-close" bindtap="cancelLocation">✕</text>
      </view>
      <view class="modal-body">
        <input class="location-input-field" 
               placeholder="请输入出生地点，如：北京、上海等" 
               value="{{customLocation}}" 
               bindinput="onLocationInput"
               maxlength="20" />
      </view>
      <view class="modal-footer">
        <button class="modal-button cancel" bindtap="cancelLocation">取消</button>
        <button class="modal-button confirm" bindtap="confirmLocation">确定</button>
      </view>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="footer-info">
    <view class="info-item">
      <text class="info-icon">🏛️</text>
      <text class="info-text">基于《玉匣记》等六部古籍理论</text>
    </view>
    <view class="info-item">
      <text class="info-icon">🔮</text>
      <text class="info-text">融合现代专业排盘技术</text>
    </view>
    <view class="info-item">
      <text class="info-icon">⚖️</text>
      <text class="info-text">仅供学习研究，理性对待结果</text>
    </view>
  </view>
</view>
