# 《玉匣记》项目环境变量配置示例
# 复制此文件为 .env 并填入实际值

# 应用配置
APP_NAME="玉匣记占卜系统"
APP_VERSION="1.0.0"
DEBUG=true
SECRET_KEY="your-secret-key-here"

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true

# 数据库配置
DATABASE_URL="mysql+aiomysql://yujiaji_user:yujiaji_pass@localhost:3306/yujiaji_db"
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=yujiaji_db
DATABASE_USER=yujiaji_user
DATABASE_PASSWORD=yujiaji_pass

# Redis配置
REDIS_URL="redis://localhost:6379/0"
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=""

# 缓存配置
CACHE_EXPIRE_SECONDS=3600
CACHE_PREFIX="yujiaji:"

# 日志配置
LOG_LEVEL=INFO
LOG_FILE="logs/app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# API配置
API_V1_PREFIX="/api/v1"
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
MAX_QUERY_RESULTS=100
DEFAULT_PAGE_SIZE=20

# 文件路径配置
DATA_DIR="./data"
UPLOAD_DIR="./uploads"
STATIC_DIR="./static"
TEMPLATE_DIR="./templates"

# 解析器配置
PARSER_CONFIDENCE_THRESHOLD=0.5
PARSER_BATCH_SIZE=1000
PARSER_TIMEOUT_SECONDS=300

# 搜索配置
SEARCH_ENGINE="mysql"  # mysql, elasticsearch
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_INDEX="yujiaji"

# 安全配置
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM="HS256"

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 开发配置
ENABLE_DOCS=true
ENABLE_REDOC=true
ENABLE_OPENAPI=true
