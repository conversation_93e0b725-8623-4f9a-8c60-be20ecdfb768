// components/ec-canvas/index.js
const WxCanvas = require('./wx-canvas');
const echarts = require('./echarts');

Component({
  properties: {
    canvasId: {
      type: String,
      value: 'ec-canvas'
    },
    ec: {
      type: Object,
      observer: function(newVal, oldVal) {
        console.log('📊 ECharts配置变化:', { newVal, oldVal });
        if (newVal && newVal.onInit && !newVal.lazyLoad) {
          console.log('🔄 配置变化，重新初始化图表');
          setTimeout(() => {
            this.init();
          }, 100);
        }
      }
    },
    forceUseOldCanvas: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isUseNewCanvas: false
  },

  ready: function () {
    console.log('📊 ECharts组件ready，canvasId:', this.data.canvasId);
    // 在组件实例进入页面节点树时执行
    if (!this.data.ec) {
      console.warn('组件需要绑定 ec 变量，例：<ec-canvas id="mychart-dom-bar" canvas-id="mychart-bar" ec="{{ ec }}"></ec-canvas>');
      return;
    }

    console.log('📊 ECharts配置:', this.data.ec);
    if (!this.data.ec.lazyLoad) {
      console.log('🚀 开始初始化ECharts');
      this.init();
    } else {
      console.log('⏳ 懒加载模式，等待手动初始化');
    }
  },

  methods: {
    init: function (callback) {
      console.log('🔧 ECharts init方法被调用，canvasId:', this.data.canvasId);

      const appBaseInfo = wx.getAppBaseInfo();
      const version = appBaseInfo.SDKVersion;
      const canUseNewCanvas = compareVersion(version, '2.9.0') >= 0;
      const forceUseOldCanvas = this.data.forceUseOldCanvas;
      const isUseNewCanvas = canUseNewCanvas && !forceUseOldCanvas;
      this.setData({ isUseNewCanvas });

      console.log('📱 Canvas版本信息:', {
        version,
        canUseNewCanvas,
        forceUseOldCanvas,
        isUseNewCanvas
      });

      if (forceUseOldCanvas && canUseNewCanvas) {
        console.warn('开发者强制使用旧canvas,建议关闭');
      }

      if (isUseNewCanvas) {
        console.log('✅ 使用新Canvas实现 (>= 2.9.0)');
        this.initByNewWay(callback);
      } else {
        const isValid = compareVersion(version, '1.9.91') >= 0;
        if (!isValid) {
          console.error('微信基础库版本过低，需大于等于 1.9.91。');
          return;
        }
        console.warn('⚠️ 使用旧Canvas实现，建议将微信基础库调整大于等于2.9.0版本');
        this.initByOldWay(callback);
      }
    },

    initByOldWay(callback) {
      // 1.9.91 <= version < 2.9.0：原来的方式初始化
      const ctx = wx.createCanvasContext(this.data.canvasId, this);
      const canvas = new WxCanvas(ctx, this.data.canvasId, false, this);

      echarts.setCanvasCreator(() => {
        return canvas;
      });
      // const canvasDpr = wx.getSystemInfoSync().pixelRatio // 微信旧的canvas不能传入dpr
      const canvasDpr = 1;
      var query = wx.createSelectorQuery().in(this);
      query.select('.ec-canvas').boundingClientRect(res => {
        if (typeof callback === 'function') {
          this.chart = callback(canvas, res.width, res.height, canvasDpr);
        } else if (this.data.ec && typeof this.data.ec.onInit === 'function') {
          this.chart = this.data.ec.onInit(canvas, res.width, res.height, canvasDpr);
        } else {
          this.triggerEvent('init', {
            canvas: canvas,
            width: res.width,
            height: res.height,
            canvasDpr: canvasDpr // 增加了dpr，可方便外面echarts.init
          });
        }
      }).exec();
    },

    initByNewWay(callback) {
      console.log('🆕 使用新Canvas方式初始化，canvasId:', this.data.canvasId);
      // version >= 2.9.0：使用新的方式初始化
      const query = wx.createSelectorQuery().in(this);
      query
        .select('.ec-canvas')
        .fields({ node: true, size: true })
        .exec(res => {
          console.log('📐 Canvas查询结果:', res);

          if (!res || !res[0] || !res[0].node) {
            console.error('❌ 无法找到Canvas节点');
            return;
          }

          const canvasNode = res[0].node;
          this.canvasNode = canvasNode;

          const windowInfo = wx.getWindowInfo();
          const canvasDpr = windowInfo.pixelRatio;
          const canvasWidth = res[0].width;
          const canvasHeight = res[0].height;

          console.log('📊 Canvas信息:', {
            canvasWidth,
            canvasHeight,
            canvasDpr,
            canvasNode
          });

          // 设置Canvas物理尺寸
          canvasNode.width = canvasWidth * canvasDpr;
          canvasNode.height = canvasHeight * canvasDpr;
          console.log('📐 设置Canvas物理尺寸:', {
            physicalWidth: canvasNode.width,
            physicalHeight: canvasNode.height
          });

          const ctx = canvasNode.getContext('2d');
          // 缩放上下文以适应设备像素比
          ctx.scale(canvasDpr, canvasDpr);
          console.log('🎨 Canvas上下文和缩放设置完成:', ctx);

          // 强制设置Canvas样式，防止被ECharts覆盖
          const canvasElement = canvasNode;
          if (canvasElement && canvasElement.style) {
            canvasElement.style.position = 'static';
            canvasElement.style.top = 'auto';
            canvasElement.style.left = 'auto';
            canvasElement.style.right = 'auto';
            canvasElement.style.bottom = 'auto';
            canvasElement.style.transform = 'none';
            canvasElement.style.width = '100%';
            canvasElement.style.height = '100%';
            console.log('🔧 强制设置Canvas样式完成');
          }

          const canvas = new WxCanvas(ctx, this.data.canvasId, true, canvasNode);
          console.log('🖼️ WxCanvas创建完成:', canvas);

          echarts.setCanvasCreator(() => {
            return canvas;
          });

          if (typeof callback === 'function') {
            console.log('📞 调用callback函数');
            this.chart = callback(canvas, canvasWidth, canvasHeight, canvasDpr);
          } else if (this.data.ec && typeof this.data.ec.onInit === 'function') {
            console.log('📞 调用ec.onInit函数');
            this.chart = this.data.ec.onInit(canvas, canvasWidth, canvasHeight, canvasDpr);
          } else {
            console.log('📞 触发init事件');
            this.triggerEvent('init', {
              canvas: canvas,
              width: canvasWidth,
              height: canvasHeight,
              dpr: canvasDpr
            });
          }

          // 图表初始化完成后，再次强制设置Canvas样式
          setTimeout(() => {
            if (canvasElement && canvasElement.style) {
              canvasElement.style.position = 'static';
              canvasElement.style.top = 'auto';
              canvasElement.style.left = 'auto';
              canvasElement.style.right = 'auto';
              canvasElement.style.bottom = 'auto';
              canvasElement.style.transform = 'none';
              canvasElement.style.width = '100%';
              canvasElement.style.height = '100%';
              console.log('🔧 图表初始化后再次强制设置Canvas样式');
            }
          }, 100);

          console.log('✅ 新Canvas初始化完成');
        });
    },
    
    canvasToTempFilePath(opt) {
      if (this.data.isUseNewCanvas) {
        // 新版
        const query = wx.createSelectorQuery().in(this);
        query
          .select('.ec-canvas')
          .fields({ node: true, size: true })
          .exec(res => {
            const canvasNode = res[0].node;
            opt.canvas = canvasNode;
            wx.canvasToTempFilePath(opt);
          });
      } else {
        // 旧的
        if (!opt.canvasId) {
          opt.canvasId = this.data.canvasId;
        }
        ctx.draw(true, () => {
          wx.canvasToTempFilePath(opt, this);
        });
      }
    },

    touchStart(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        // 确保chart.getZr()存在后再调用handler方法
        if (this.chart.getZr && typeof this.chart.getZr === 'function') {
          var handler = this.chart.getZr().handler;
          if (handler) {
            handler.dispatch('mousedown', {
              zrX: touch.x,
              zrY: touch.y
            });
            handler.dispatch('mousemove', {
              zrX: touch.x,
              zrY: touch.y
            });
            handler.processGesture(wrapTouch(e), 'start');
          }
        }
      }
    },

    touchMove(e) {
      if (this.chart && e.touches.length > 0) {
        var touch = e.touches[0];
        // 确保chart.getZr()存在后再调用handler方法
        if (this.chart.getZr && typeof this.chart.getZr === 'function') {
          var handler = this.chart.getZr().handler;
          if (handler) {
            handler.dispatch('mousemove', {
              zrX: touch.x,
              zrY: touch.y
            });
            handler.processGesture(wrapTouch(e), 'change');
          }
        }
      }
    },

    touchEnd(e) {
      if (this.chart) {
        const touch = e.changedTouches ? e.changedTouches[0] : {};
        // 确保chart.getZr()存在后再调用handler方法
        if (this.chart.getZr && typeof this.chart.getZr === 'function') {
          var handler = this.chart.getZr().handler;
          if (handler) {
            handler.dispatch('mouseup', {
              zrX: touch.x,
              zrY: touch.y
            });
            handler.dispatch('click', {
              zrX: touch.x,
              zrY: touch.y
            });
            handler.processGesture(wrapTouch(e), 'end');
          }
        }
      }
    }
  }
});

function compareVersion(v1, v2) {
  v1 = v1.split('.');
  v2 = v2.split('.');
  const len = Math.max(v1.length, v2.length);

  while (v1.length < len) {
    v1.push('0');
  }
  while (v2.length < len) {
    v2.push('0');
  }

  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i]);
    const num2 = parseInt(v2[i]);

    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }
  return 0;
}

function wrapTouch(event) {
  for (let i = 0; i < event.touches.length; ++i) {
    const touch = event.touches[i];
    touch.offsetX = touch.x;
    touch.offsetY = touch.y;
  }
  return event;
}