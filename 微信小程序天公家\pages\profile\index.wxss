/* pages/profile/index.wxss */

.profile-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #111111;
  color: white;
  padding-bottom: 60rpx;
}

/* 用户信息部分 */
.user-info-section {
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #333;
}

.edit-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #6236FF;
  display: flex;
  justify-content: center;
  align-items: center;
}

.edit-badge image {
  width: 20rpx;
  height: 20rpx;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 统计信息部分 */
.stats-section {
  margin: 30rpx;
  background-color: #222;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.divider {
  width: 2rpx;
  height: 60rpx;
  background-color: #333;
}

/* 菜单列表部分 */
.menu-list {
  margin: 30rpx;
}

.menu-section-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
  margin-top: 40rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #222;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 30rpx;
}

.menu-icon image {
  width: 100%;
  height: 100%;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
}

.menu-arrow {
  width: 30rpx;
  height: 30rpx;
}

.menu-arrow image {
  width: 100%;
  height: 100%;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  margin-top: 60rpx;
}