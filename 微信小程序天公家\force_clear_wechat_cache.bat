@echo off
echo 🔧 强制清理微信开发者工具缓存...

echo.
echo 1. 关闭微信开发者工具进程...
taskkill /f /im "微信开发者工具.exe" 2>nul
taskkill /f /im "wechatdevtools.exe" 2>nul
taskkill /f /im "WeChat Developer Tools.exe" 2>nul
timeout /t 3 >nul

echo.
echo 2. 清理项目缓存文件...
if exist ".wxcache" (
    rmdir /s /q ".wxcache"
    echo    ✅ 删除 .wxcache 文件夹
) else (
    echo    ℹ️ .wxcache 文件夹不存在
)

if exist "node_modules" (
    rmdir /s /q "node_modules"
    echo    ✅ 删除 node_modules 文件夹
) else (
    echo    ℹ️ node_modules 文件夹不存在
)

echo.
echo 3. 清理微信开发者工具用户缓存...
set CACHE_DIR1=%APPDATA%\微信开发者工具
set CACHE_DIR2=%APPDATA%\wechatdevtools
set CACHE_DIR3=%LOCALAPPDATA%\微信开发者工具

if exist "%CACHE_DIR1%" (
    rmdir /s /q "%CACHE_DIR1%"
    echo    ✅ 删除微信开发者工具缓存1
)

if exist "%CACHE_DIR2%" (
    rmdir /s /q "%CACHE_DIR2%"
    echo    ✅ 删除微信开发者工具缓存2
)

if exist "%CACHE_DIR3%" (
    rmdir /s /q "%CACHE_DIR3%"
    echo    ✅ 删除微信开发者工具缓存3
)

echo.
echo 4. 清理临时文件...
del /q /s "%TEMP%\wx*" 2>nul
del /q /s "%TEMP%\wechat*" 2>nul
echo    ✅ 清理临时文件完成

echo.
echo 5. 清理系统缓存...
del /q /s "%TEMP%\*.tmp" 2>nul
echo    ✅ 清理系统临时文件完成

echo.
echo 🎯 缓存清理完成！
echo.
echo 📋 接下来请手动执行：
echo    1. 重新启动微信开发者工具
echo    2. 重新打开项目
echo    3. 在工具菜单中选择 "工具" - "清缓存"
echo    4. 选择 "清除文件缓存" 和 "清除编译缓存"
echo    5. 点击 "确定" 并重新编译
echo.
echo ⚠️ 如果问题仍然存在，请重启电脑后再试
echo.
pause
