/**
 * 简单验证WXML标签配对
 * 只检查关键标签是否正确配对
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 简单验证WXML标签配对');
console.log('='.repeat(40));
console.log('');

// 读取WXML文件内容
const wxmlPath = path.join(__dirname, 'pages/bazi-result/index.wxml');
let content = '';

try {
  content = fs.readFileSync(wxmlPath, 'utf8');
  console.log('✅ 成功读取WXML文件');
} catch (error) {
  console.log('❌ 读取WXML文件失败:', error.message);
  process.exit(1);
}

console.log('');

// 简单的标签配对检查
function simpleTagCheck(content) {
  console.log('📊 关键标签配对检查：');
  console.log('='.repeat(25));
  
  const keyTags = [
    'view',
    'scroll-view',
    'text'
  ];
  
  let allPassed = true;
  
  keyTags.forEach(tag => {
    const openPattern = new RegExp(`<${tag}[^>]*(?<!/)>`, 'g');
    const closePattern = new RegExp(`</${tag}>`, 'g');
    
    const openMatches = content.match(openPattern) || [];
    const closeMatches = content.match(closePattern) || [];
    
    const openCount = openMatches.length;
    const closeCount = closeMatches.length;
    
    const status = openCount === closeCount ? '✅' : '❌';
    console.log(`   ${status} <${tag}>: 开始${openCount} 结束${closeCount} ${openCount === closeCount ? '配对' : '不配对'}`);
    
    if (openCount !== closeCount) {
      allPassed = false;
    }
  });
  
  return allPassed;
}

// 检查scroll-view特定问题
function checkScrollView(content) {
  console.log('');
  console.log('🔍 scroll-view 特殊检查：');
  console.log('='.repeat(25));
  
  const scrollViewOpen = content.match(/<scroll-view[^>]*>/g) || [];
  const scrollViewClose = content.match(/<\/scroll-view>/g) || [];
  
  console.log(`   scroll-view 开始标签: ${scrollViewOpen.length}`);
  console.log(`   scroll-view 结束标签: ${scrollViewClose.length}`);
  
  if (scrollViewOpen.length === 1 && scrollViewClose.length === 1) {
    console.log('   ✅ scroll-view 配对正确');
    return true;
  } else {
    console.log('   ❌ scroll-view 配对错误');
    return false;
  }
}

// 检查基本结构
function checkBasicStructure(content) {
  console.log('');
  console.log('🏗️ 基本结构检查：');
  console.log('='.repeat(20));
  
  const checks = [
    {
      name: '根容器',
      pattern: /<view class="tianggong-container">/,
      required: true
    },
    {
      name: '主内容区',
      pattern: /<view[^>]*class="tianggong-main-content">/,
      required: true
    },
    {
      name: '标签页导航',
      pattern: /<view[^>]*class="tianggong-tab-nav">/,
      required: true
    },
    {
      name: '标签页内容',
      pattern: /<scroll-view[^>]*class="tianggong-tab-content"/,
      required: true
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    const matches = content.match(check.pattern);
    const passed = matches !== null;
    const status = passed ? '✅' : '❌';
    
    console.log(`   ${status} ${check.name}: ${passed ? '存在' : '缺失'}`);
    
    if (!passed && check.required) {
      allPassed = false;
    }
  });
  
  return allPassed;
}

// 执行检查
console.log('🚀 开始验证...');
console.log('');

const tagPairingValid = simpleTagCheck(content);
const scrollViewValid = checkScrollView(content);
const structureValid = checkBasicStructure(content);

console.log('');
console.log('📋 验证总结：');
console.log('='.repeat(15));

console.log(`✅ 关键标签配对: ${tagPairingValid ? '通过' : '失败'}`);
console.log(`✅ scroll-view配对: ${scrollViewValid ? '通过' : '失败'}`);
console.log(`✅ 基本结构完整: ${structureValid ? '通过' : '失败'}`);

const overallValid = tagPairingValid && scrollViewValid && structureValid;

console.log('');
if (overallValid) {
  console.log('🎉 WXML基本结构验证通过！');
  console.log('✅ 关键标签配对正确');
  console.log('✅ scroll-view结构正确');
  console.log('✅ 基本页面结构完整');
  console.log('');
  console.log('🚀 应该可以正常编译运行！');
} else {
  console.log('❌ WXML结构验证失败！');
  console.log('需要进一步修复结构问题');
}

console.log('');
console.log('📊 文件统计：');
console.log(`   总行数: ${content.split('\n').length}`);
console.log(`   文件大小: ${(content.length / 1024).toFixed(2)} KB`);

console.log('');
console.log('✅ 验证完成！');
