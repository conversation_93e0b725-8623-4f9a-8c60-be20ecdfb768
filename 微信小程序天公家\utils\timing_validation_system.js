/**
 * 应期分析验证体系
 * 实现历史案例验证、动态调优机制、用户反馈闭环
 */

class TimingValidationSystem {
  constructor() {
    this.historicalCases = [];
    this.validationMetrics = this.initializeValidationMetrics();
    this.optimizationParameters = this.initializeOptimizationParameters();
    this.feedbackWeights = this.initializeFeedbackWeights();
    this.loadHistoricalCases();
  }

  /**
   * 初始化验证指标
   */
  initializeValidationMetrics() {
    return {
      accuracy_targets: {
        marriage: { target: 0.85, tolerance: 0.03 }, // 85%±3%
        promotion: { target: 0.82, tolerance: 0.05 }, // 82%±5%
        childbirth: { target: 0.78, tolerance: 0.04 }, // 78%±4%
        wealth: { target: 0.75, tolerance: 0.06 } // 75%±6%
      },
      time_precision: {
        high: { months: 3, weight: 1.0 },
        medium: { months: 6, weight: 0.8 },
        low: { months: 12, weight: 0.6 }
      },
      confidence_calibration: {
        over_confidence_penalty: 0.2,
        under_confidence_bonus: 0.1,
        calibration_threshold: 0.1
      }
    };
  }

  /**
   * 初始化优化参数
   */
  initializeOptimizationParameters() {
    return {
      disease_medicine_weights: {
        disease_severity_factor: 0.4,
        medicine_effectiveness_factor: 0.3,
        availability_factor: 0.3
      },
      activation_weights: {
        star_activation: 0.4,
        palace_activation: 0.35,
        god_activation: 0.25
      },
      energy_threshold_adjustments: {
        marriage: { base: 0.30, seasonal_modifier: 0.05 },
        promotion: { base: 0.50, seasonal_modifier: 0.03 },
        childbirth: { base: 0.25, seasonal_modifier: 0.04 },
        wealth: { base: 0.35, seasonal_modifier: 0.06 }
      },
      temporal_decay_factors: {
        recent_cases: 1.0, // 最近5年
        medium_cases: 0.8, // 5-15年前
        historical_cases: 0.6 // 15年以上
      }
    };
  }

  /**
   * 初始化反馈权重
   */
  initializeFeedbackWeights() {
    return {
      user_feedback: {
        exact_match: 1.0,
        close_match: 0.8, // ±6个月内
        approximate_match: 0.6, // ±1年内
        miss: 0.0
      },
      expert_validation: {
        master_level: 1.0,
        professional_level: 0.9,
        advanced_level: 0.7,
        intermediate_level: 0.5
      },
      historical_verification: {
        documented_cases: 1.0,
        oral_tradition: 0.8,
        inferred_cases: 0.6
      }
    };
  }

  /**
   * 加载历史案例数据
   */
  loadHistoricalCases() {
    // 这里应该从数据库或文件加载真实的历史案例
    // 现在使用示例数据
    this.historicalCases = [
      {
        case_id: 'HC001',
        bazi: '甲子 丙寅 戊午 癸亥',
        gender: 'male',
        event_type: 'marriage',
        predicted_year: 2018,
        actual_year: 2019,
        accuracy_score: 0.85,
        time_difference_months: 8,
        validation_source: 'user_feedback',
        confidence_level: 0.78,
        notes: '预测较为准确，时间略有偏差'
      },
      {
        case_id: 'HC002',
        bazi: '乙丑 己卯 辛未 庚寅',
        gender: 'female',
        event_type: 'promotion',
        predicted_year: 2020,
        actual_year: 2020,
        accuracy_score: 0.95,
        time_difference_months: 2,
        validation_source: 'expert_validation',
        confidence_level: 0.82,
        notes: '预测非常准确，专家验证通过'
      },
      {
        case_id: 'HC003',
        bazi: '丙辰 庚子 壬申 丁未',
        gender: 'female',
        event_type: 'childbirth',
        predicted_year: 2021,
        actual_year: 2022,
        accuracy_score: 0.72,
        time_difference_months: 14,
        validation_source: 'historical_verification',
        confidence_level: 0.65,
        notes: '预测偏差较大，需要调优'
      }
    ];
  }

  /**
   * 验证应期预测准确性
   * @param {Object} prediction - 预测结果
   * @param {Object} actualOutcome - 实际结果
   * @returns {Object} 验证结果
   */
  validatePrediction(prediction, actualOutcome) {
    console.log('🔍 开始验证应期预测准确性...');

    const validation = {
      case_id: this.generateCaseId(),
      prediction: prediction,
      actual_outcome: actualOutcome,
      accuracy_metrics: {},
      calibration_analysis: {},
      improvement_suggestions: []
    };

    try {
      // 1. 时间准确性验证
      const timeAccuracy = this.validateTimeAccuracy(
        prediction.period,
        actualOutcome.actual_year,
        actualOutcome.actual_month
      );
      validation.accuracy_metrics.time_accuracy = timeAccuracy;

      // 2. 置信度校准验证
      const confidenceCalibration = this.validateConfidenceCalibration(
        prediction.confidence,
        timeAccuracy.accuracy_score
      );
      validation.calibration_analysis = confidenceCalibration;

      // 3. 事件特征验证
      const featureValidation = this.validateEventFeatures(
        prediction,
        actualOutcome
      );
      validation.accuracy_metrics.feature_validation = featureValidation;

      // 4. 综合准确性评分
      validation.overall_accuracy = this.calculateOverallAccuracy(
        timeAccuracy,
        confidenceCalibration,
        featureValidation
      );

      // 5. 生成改进建议
      validation.improvement_suggestions = this.generateImprovementSuggestions(
        validation
      );

      // 6. 更新历史案例库
      this.addToHistoricalCases(validation);

      console.log(`✅ 验证完成，总体准确性: ${(validation.overall_accuracy * 100).toFixed(1)}%`);
      return validation;

    } catch (error) {
      console.error('❌ 验证过程失败:', error);
      validation.error = error.message;
      return validation;
    }
  }

  /**
   * 验证时间准确性
   */
  validateTimeAccuracy(predictedYear, actualYear, actualMonth = 6) {
    const predictedDate = new Date(predictedYear, 6, 1); // 假设预测年份中期
    const actualDate = new Date(actualYear, actualMonth - 1, 1);
    
    const timeDifferenceMs = Math.abs(actualDate - predictedDate);
    const timeDifferenceMonths = timeDifferenceMs / (1000 * 60 * 60 * 24 * 30.44);

    let accuracyScore = 0;
    let precisionLevel = 'low';

    if (timeDifferenceMonths <= 3) {
      accuracyScore = 1.0;
      precisionLevel = 'high';
    } else if (timeDifferenceMonths <= 6) {
      accuracyScore = 0.8;
      precisionLevel = 'medium';
    } else if (timeDifferenceMonths <= 12) {
      accuracyScore = 0.6;
      precisionLevel = 'low';
    } else {
      accuracyScore = Math.max(0.2, 1 - (timeDifferenceMonths - 12) / 24);
      precisionLevel = 'poor';
    }

    return {
      predicted_year: predictedYear,
      actual_year: actualYear,
      time_difference_months: timeDifferenceMonths,
      accuracy_score: accuracyScore,
      precision_level: precisionLevel,
      meets_target: accuracyScore >= 0.75
    };
  }

  /**
   * 验证置信度校准
   */
  validateConfidenceCalibration(predictedConfidence, actualAccuracy) {
    const calibrationError = Math.abs(predictedConfidence - actualAccuracy);
    const isWellCalibrated = calibrationError <= this.validationMetrics.confidence_calibration.calibration_threshold;

    let calibrationScore = 1.0 - calibrationError;
    let calibrationType = 'well_calibrated';

    if (predictedConfidence > actualAccuracy + 0.1) {
      calibrationType = 'over_confident';
      calibrationScore *= (1 - this.validationMetrics.confidence_calibration.over_confidence_penalty);
    } else if (predictedConfidence < actualAccuracy - 0.1) {
      calibrationType = 'under_confident';
      calibrationScore *= (1 + this.validationMetrics.confidence_calibration.under_confidence_bonus);
    }

    return {
      predicted_confidence: predictedConfidence,
      actual_accuracy: actualAccuracy,
      calibration_error: calibrationError,
      calibration_score: Math.max(0, calibrationScore),
      calibration_type: calibrationType,
      is_well_calibrated: isWellCalibrated
    };
  }

  /**
   * 验证事件特征
   */
  validateEventFeatures(prediction, actualOutcome) {
    const featureMatches = {
      triggers_accuracy: 0,
      reason_relevance: 0,
      ancient_basis_validity: 0
    };

    // 验证触发因素准确性
    if (actualOutcome.actual_triggers) {
      const triggerMatches = this.compareTriggers(
        prediction.triggers,
        actualOutcome.actual_triggers
      );
      featureMatches.triggers_accuracy = triggerMatches;
    }

    // 验证原因相关性
    if (actualOutcome.actual_reasons) {
      const reasonRelevance = this.compareReasons(
        prediction.reason,
        actualOutcome.actual_reasons
      );
      featureMatches.reason_relevance = reasonRelevance;
    }

    // 验证古籍依据有效性
    featureMatches.ancient_basis_validity = this.validateAncientBasis(
      prediction.ancient_basis
    );

    const averageFeatureScore = Object.values(featureMatches)
      .reduce((sum, score) => sum + score, 0) / Object.keys(featureMatches).length;

    return {
      feature_matches: featureMatches,
      average_feature_score: averageFeatureScore,
      feature_validation_passed: averageFeatureScore >= 0.6
    };
  }

  /**
   * 计算总体准确性
   */
  calculateOverallAccuracy(timeAccuracy, confidenceCalibration, featureValidation) {
    const weights = {
      time_accuracy: 0.5,
      confidence_calibration: 0.3,
      feature_validation: 0.2
    };

    return (
      timeAccuracy.accuracy_score * weights.time_accuracy +
      confidenceCalibration.calibration_score * weights.confidence_calibration +
      featureValidation.average_feature_score * weights.feature_validation
    );
  }

  /**
   * 生成改进建议
   */
  generateImprovementSuggestions(validation) {
    const suggestions = [];

    // 时间准确性改进
    if (validation.accuracy_metrics.time_accuracy.accuracy_score < 0.8) {
      suggestions.push({
        category: 'time_accuracy',
        priority: 'high',
        suggestion: '需要调整时间预测算法，考虑增加月份级别的精确计算',
        target_improvement: '提升时间准确性至80%以上'
      });
    }

    // 置信度校准改进
    if (!validation.calibration_analysis.is_well_calibrated) {
      const calibrationType = validation.calibration_analysis.calibration_type;
      if (calibrationType === 'over_confident') {
        suggestions.push({
          category: 'confidence_calibration',
          priority: 'medium',
          suggestion: '降低置信度评估，增加不确定性因素考虑',
          target_improvement: '减少过度自信偏差'
        });
      } else if (calibrationType === 'under_confident') {
        suggestions.push({
          category: 'confidence_calibration',
          priority: 'low',
          suggestion: '适当提升置信度评估，增强预测信心',
          target_improvement: '减少过度谨慎偏差'
        });
      }
    }

    // 特征验证改进
    if (validation.accuracy_metrics.feature_validation.average_feature_score < 0.6) {
      suggestions.push({
        category: 'feature_validation',
        priority: 'medium',
        suggestion: '改进事件特征分析算法，增强触发因素识别准确性',
        target_improvement: '提升特征匹配度至60%以上'
      });
    }

    return suggestions;
  }

  /**
   * 动态调优系统
   * @param {Array} recentValidations - 最近的验证结果
   * @returns {Object} 调优建议
   */
  dynamicOptimization(recentValidations) {
    console.log('🔧 开始动态调优分析...');

    const optimization = {
      parameter_adjustments: {},
      weight_modifications: {},
      threshold_updates: {},
      performance_trends: {}
    };

    try {
      // 1. 分析性能趋势
      optimization.performance_trends = this.analyzePerformanceTrends(recentValidations);

      // 2. 参数调整建议
      optimization.parameter_adjustments = this.suggestParameterAdjustments(recentValidations);

      // 3. 权重修正建议
      optimization.weight_modifications = this.suggestWeightModifications(recentValidations);

      // 4. 阈值更新建议
      optimization.threshold_updates = this.suggestThresholdUpdates(recentValidations);

      // 5. 应用调优
      this.applyOptimizations(optimization);

      console.log('✅ 动态调优完成');
      return optimization;

    } catch (error) {
      console.error('❌ 动态调优失败:', error);
      optimization.error = error.message;
      return optimization;
    }
  }

  /**
   * 分析性能趋势
   */
  analyzePerformanceTrends(validations) {
    const trends = {
      accuracy_trend: 'stable',
      confidence_trend: 'stable',
      time_precision_trend: 'stable',
      overall_performance: 'stable'
    };

    if (validations.length < 3) return trends;

    // 计算准确性趋势
    const accuracies = validations.map(v => v.overall_accuracy);
    const accuracyTrend = this.calculateTrend(accuracies);
    trends.accuracy_trend = accuracyTrend;

    // 计算置信度校准趋势
    const calibrationScores = validations.map(v => v.calibration_analysis.calibration_score);
    const calibrationTrend = this.calculateTrend(calibrationScores);
    trends.confidence_trend = calibrationTrend;

    return trends;
  }

  /**
   * 计算趋势
   */
  calculateTrend(values) {
    if (values.length < 2) return 'stable';

    const recent = values.slice(-3);
    const earlier = values.slice(0, -3);

    if (earlier.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, v) => sum + v, 0) / earlier.length;

    const difference = recentAvg - earlierAvg;

    if (difference > 0.05) return 'improving';
    if (difference < -0.05) return 'declining';
    return 'stable';
  }

  /**
   * 生成案例ID
   */
  generateCaseId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `VC_${timestamp}_${random}`.toUpperCase();
  }

  /**
   * 添加到历史案例库
   */
  addToHistoricalCases(validation) {
    const historicalCase = {
      case_id: validation.case_id,
      bazi: validation.prediction.bazi_summary || 'unknown',
      gender: validation.prediction.gender || 'unknown',
      event_type: validation.prediction.event_type || 'unknown',
      predicted_year: validation.prediction.period,
      actual_year: validation.actual_outcome.actual_year,
      accuracy_score: validation.overall_accuracy,
      time_difference_months: validation.accuracy_metrics.time_accuracy.time_difference_months,
      validation_source: 'system_validation',
      confidence_level: validation.prediction.confidence,
      notes: `系统验证案例，总体准确性${(validation.overall_accuracy * 100).toFixed(1)}%`,
      timestamp: new Date().toISOString()
    };

    this.historicalCases.push(historicalCase);

    // 保持案例库大小在合理范围内
    if (this.historicalCases.length > 1000) {
      this.historicalCases = this.historicalCases.slice(-800); // 保留最近800个案例
    }
  }

  /**
   * 获取验证统计
   */
  getValidationStatistics() {
    const stats = {
      total_cases: this.historicalCases.length,
      accuracy_by_event: {},
      average_accuracy: 0,
      calibration_quality: 'unknown',
      recent_performance: 'unknown'
    };

    if (this.historicalCases.length === 0) return stats;

    // 按事件类型统计准确性
    const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
    eventTypes.forEach(eventType => {
      const eventCases = this.historicalCases.filter(c => c.event_type === eventType);
      if (eventCases.length > 0) {
        const avgAccuracy = eventCases.reduce((sum, c) => sum + c.accuracy_score, 0) / eventCases.length;
        stats.accuracy_by_event[eventType] = {
          cases: eventCases.length,
          average_accuracy: avgAccuracy,
          meets_target: avgAccuracy >= this.validationMetrics.accuracy_targets[eventType]?.target
        };
      }
    });

    // 总体平均准确性
    stats.average_accuracy = this.historicalCases.reduce((sum, c) => sum + c.accuracy_score, 0) / this.historicalCases.length;

    return stats;
  }

  // 辅助方法的简化实现
  compareTriggers(predicted, actual) { return 0.7; }
  compareReasons(predicted, actual) { return 0.8; }
  validateAncientBasis(basis) { return 0.9; }
  suggestParameterAdjustments(validations) { return {}; }
  suggestWeightModifications(validations) { return {}; }
  suggestThresholdUpdates(validations) { return {}; }
  applyOptimizations(optimization) { return true; }
}

module.exports = TimingValidationSystem;
