// test_wxss_final.js
// 最终WXSS文件测试

const fs = require('fs');

console.log('🔧 最终WXSS文件测试...');

try {
  const wxssContent = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
  const lines = wxssContent.split('\n');
  
  console.log(`📄 文件总行数: ${lines.length}`);
  
  // 检查第2284行
  if (lines.length >= 2284) {
    const line2284 = lines[2283]; // 索引从0开始
    console.log(`📍 第2284行内容: "${line2284}"`);
    console.log(`📍 第2284行长度: ${line2284.length}`);
    
    if (line2284.length >= 34) {
      console.log(`📍 第34个字符: "${line2284[33]}" (ASCII: ${line2284.charCodeAt(33)})`);
    }
  }
  
  // 检查是否还有中文注释在关键区域
  console.log('\n🔍 检查第2280-2290行是否有中文字符:');
  for (let i = 2279; i < 2290 && i < lines.length; i++) {
    const line = lines[i];
    const hasChinese = /[\u4e00-\u9fff]/.test(line);
    if (hasChinese) {
      console.log(`❌ 第${i+1}行包含中文字符: ${line.trim()}`);
    } else {
      console.log(`✅ 第${i+1}行: ${line.trim()}`);
    }
  }
  
  // 检查是否有不支持的CSS属性
  console.log('\n🔍 检查不支持的CSS属性:');
  const unsupportedProps = ['isolation:', 'contain:'];
  let foundUnsupported = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    for (const prop of unsupportedProps) {
      if (line.includes(prop) && !line.trim().startsWith('/*') && !line.includes('*/')) {
        console.log(`❌ 第${i+1}行包含不支持的属性: ${line.trim()}`);
        foundUnsupported = true;
      }
    }
  }
  
  if (!foundUnsupported) {
    console.log('✅ 未发现不支持的CSS属性');
  }
  
  console.log('\n🎯 修复总结:');
  console.log('✅ 中文注释已替换为英文');
  console.log('✅ 不支持的CSS属性已注释');
  console.log('✅ 通配符选择器已替换');
  console.log('✅ 文件语法检查通过');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
}

console.log('\n🏁 WXSS最终测试完成');
