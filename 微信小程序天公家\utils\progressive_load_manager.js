/**
 * 渐进式加载管理器
 * 分步骤加载专业分析内容，优化用户体验
 * 第三阶段：专业解读功能优化 - 第四步
 */

class ProgressiveLoadManager {
  constructor() {
    this.isInitialized = false;
    this.currentStep = 0;
    this.totalSteps = 0;
    this.loadingState = 'idle'; // idle, loading, completed, error
    
    // 加载步骤定义
    this.loadSteps = [
      {
        id: 'basic_info',
        name: '基础信息处理',
        description: '解析八字基础数据',
        weight: 10,
        dependencies: [],
        estimatedTime: 200,
        critical: true
      },
      {
        id: 'wuxing_analysis',
        name: '五行分析',
        description: '计算五行力量分布',
        weight: 20,
        dependencies: ['basic_info'],
        estimatedTime: 500,
        critical: true
      },
      {
        id: 'balance_calculation',
        name: '平衡指数计算',
        description: '分析五行平衡状态',
        weight: 15,
        dependencies: ['wuxing_analysis'],
        estimatedTime: 300,
        critical: true
      },
      {
        id: 'rules_matching',
        name: '规则匹配',
        description: '匹配古籍规则库',
        weight: 25,
        dependencies: ['basic_info'],
        estimatedTime: 800,
        critical: false
      },
      {
        id: 'enhanced_analysis',
        name: '增强分析',
        description: '深度分析和解读',
        weight: 20,
        dependencies: ['wuxing_analysis', 'rules_matching'],
        estimatedTime: 600,
        critical: false
      },
      {
        id: 'report_generation',
        name: '报告生成',
        description: '生成综合分析报告',
        weight: 10,
        dependencies: ['balance_calculation', 'enhanced_analysis'],
        estimatedTime: 400,
        critical: false
      }
    ];

    // 加载策略配置
    this.loadingStrategy = {
      mode: 'adaptive', // progressive, parallel, adaptive
      maxConcurrent: 2,
      prioritizeCritical: true,
      enablePreloading: true,
      cacheResults: true,
      retryAttempts: 3,
      timeoutMs: 10000
    };

    // 加载状态跟踪
    this.stepStates = new Map();
    this.loadResults = new Map();
    this.loadTimes = new Map();
    this.errorLog = [];
    
    // 性能统计
    this.performanceStats = {
      totalLoadTime: 0,
      stepLoadTimes: {},
      cacheHitRate: 0,
      errorRate: 0,
      userWaitTime: 0,
      interactionDelay: 0
    };

    // 用户体验优化
    this.uxOptimization = {
      showSkeletonLoading: true,
      enableProgressAnimation: true,
      provideFeedback: true,
      allowInterruption: true,
      smartPrioritization: true
    };

    // 缓存管理
    this.cache = new Map();
    this.cacheConfig = {
      maxSize: 50,
      ttl: 300000, // 5分钟
      enableCompression: true
    };

    // 事件监听器
    this.eventListeners = new Map();
    
    // 预加载队列
    this.preloadQueue = [];
    this.isPreloading = false;
  }

  /**
   * 初始化渐进式加载管理器
   */
  async initialize(dependencies = {}) {
    if (this.isInitialized) {
      console.log('⚠️ 渐进式加载管理器已经初始化');
      return true;
    }

    try {
      console.log('🚀 开始初始化渐进式加载管理器...');

      // 保存依赖引用
      this.layeredRulesManager = dependencies.layeredRulesManager;
      this.versionSwitchManager = dependencies.versionSwitchManager;
      this.isolationManager = dependencies.isolationManager;

      // 初始化步骤状态
      this.initializeStepStates();

      // 验证依赖关系
      this.validateDependencies();

      // 计算总步骤数
      this.totalSteps = this.loadSteps.length;

      // 注册事件监听器
      this.registerEventListeners();

      // 初始化缓存
      this.initializeCache();

      this.isInitialized = true;

      console.log('✅ 渐进式加载管理器初始化完成');
      console.log(`   📊 总步骤数: ${this.totalSteps}`);
      console.log(`   📊 加载策略: ${this.loadingStrategy.mode}`);

      return true;
    } catch (error) {
      console.error('❌ 渐进式加载管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 初始化步骤状态
   */
  initializeStepStates() {
    this.loadSteps.forEach(step => {
      this.stepStates.set(step.id, {
        status: 'pending', // pending, loading, completed, error, skipped
        progress: 0,
        startTime: null,
        endTime: null,
        error: null,
        retryCount: 0,
        cached: false
      });
    });
  }

  /**
   * 验证依赖关系
   */
  validateDependencies() {
    const stepIds = new Set(this.loadSteps.map(step => step.id));
    
    this.loadSteps.forEach(step => {
      step.dependencies.forEach(depId => {
        if (!stepIds.has(depId)) {
          throw new Error(`步骤 ${step.id} 的依赖 ${depId} 不存在`);
        }
      });
    });

    console.log('✅ 依赖关系验证通过');
  }

  /**
   * 注册事件监听器
   */
  registerEventListeners() {
    // 步骤开始事件
    this.on('stepStarted', (data) => {
      console.log(`🔄 步骤开始: ${data.stepId} - ${data.stepName}`);
    });

    // 步骤完成事件
    this.on('stepCompleted', (data) => {
      console.log(`✅ 步骤完成: ${data.stepId} (耗时: ${data.duration}ms)`);
    });

    // 步骤错误事件
    this.on('stepError', (data) => {
      console.error(`❌ 步骤错误: ${data.stepId} - ${data.error}`);
    });

    // 加载完成事件
    this.on('loadCompleted', (data) => {
      console.log(`🎉 加载完成: 总耗时 ${data.totalTime}ms`);
    });
  }

  /**
   * 初始化缓存
   */
  initializeCache() {
    // 从隔离存储中恢复缓存
    if (this.isolationManager) {
      const cachedData = this.isolationManager.isolatedStorage.getLocal('progressiveLoadCache');
      if (cachedData) {
        try {
          const parsed = JSON.parse(cachedData);
          Object.entries(parsed).forEach(([key, value]) => {
            if (this.isCacheValid(value)) {
              this.cache.set(key, value);
            }
          });
          console.log(`✅ 恢复缓存数据: ${this.cache.size}项`);
        } catch (error) {
          console.warn('⚠️ 缓存数据恢复失败:', error);
        }
      }
    }
  }

  /**
   * 开始渐进式加载
   */
  async startProgressiveLoad(context = {}) {
    if (this.loadingState === 'loading') {
      console.log('⚠️ 加载已在进行中');
      return { success: false, message: '加载已在进行中' };
    }

    try {
      console.log('🚀 开始渐进式加载...');

      // 重置状态
      this.resetLoadingState();
      this.loadingState = 'loading';
      this.currentStep = 0;

      const startTime = Date.now();

      // 触发加载开始事件
      this.emit('loadStarted', {
        totalSteps: this.totalSteps,
        strategy: this.loadingStrategy.mode,
        context: context
      });

      // 根据策略执行加载
      let result;
      switch (this.loadingStrategy.mode) {
        case 'progressive':
          result = await this.executeProgressiveLoad(context);
          break;
        case 'parallel':
          result = await this.executeParallelLoad(context);
          break;
        case 'adaptive':
          result = await this.executeAdaptiveLoad(context);
          break;
        default:
          throw new Error(`不支持的加载策略: ${this.loadingStrategy.mode}`);
      }

      const totalTime = Date.now() - startTime;
      this.performanceStats.totalLoadTime = totalTime;

      if (result.success) {
        this.loadingState = 'completed';
        
        // 触发加载完成事件
        this.emit('loadCompleted', {
          totalTime: totalTime,
          completedSteps: result.completedSteps,
          results: result.results
        });

        // 保存缓存
        this.saveCache();

        console.log(`🎉 渐进式加载完成: 总耗时 ${totalTime}ms`);
      } else {
        this.loadingState = 'error';
        console.error(`❌ 渐进式加载失败: ${result.message}`);
      }

      return result;

    } catch (error) {
      this.loadingState = 'error';
      console.error('❌ 渐进式加载异常:', error);
      
      return {
        success: false,
        message: error.message,
        error: error
      };
    }
  }

  /**
   * 执行渐进式加载（顺序加载）
   */
  async executeProgressiveLoad(context) {
    const results = new Map();
    const completedSteps = [];

    try {
      for (const step of this.loadSteps) {
        // 检查依赖是否满足
        if (!this.areDependenciesMet(step)) {
          console.warn(`⚠️ 步骤 ${step.id} 依赖未满足，跳过`);
          this.updateStepState(step.id, { status: 'skipped' });
          continue;
        }

        // 执行步骤
        const stepResult = await this.executeStep(step, context);
        
        if (stepResult.success) {
          results.set(step.id, stepResult.data);
          completedSteps.push(step.id);
          this.currentStep++;
        } else if (step.critical) {
          // 关键步骤失败，终止加载
          throw new Error(`关键步骤 ${step.id} 失败: ${stepResult.error}`);
        } else {
          // 非关键步骤失败，继续加载
          console.warn(`⚠️ 非关键步骤 ${step.id} 失败: ${stepResult.error}`);
          this.currentStep++;
        }
      }

      return {
        success: true,
        completedSteps: completedSteps,
        results: results
      };

    } catch (error) {
      return {
        success: false,
        message: error.message,
        completedSteps: completedSteps,
        results: results
      };
    }
  }

  /**
   * 执行并行加载
   */
  async executeParallelLoad(context) {
    const results = new Map();
    const completedSteps = [];
    const loadPromises = [];

    try {
      // 创建所有步骤的加载Promise
      this.loadSteps.forEach(step => {
        const promise = this.executeStepWithDependencyWait(step, context)
          .then(result => {
            if (result.success) {
              results.set(step.id, result.data);
              completedSteps.push(step.id);
            }
            return { stepId: step.id, result: result };
          });
        
        loadPromises.push(promise);
      });

      // 等待所有步骤完成
      const stepResults = await Promise.allSettled(loadPromises);

      // 检查关键步骤是否都成功
      const criticalSteps = this.loadSteps.filter(step => step.critical);
      const failedCriticalSteps = criticalSteps.filter(step => {
        const stepResult = stepResults.find(r => 
          r.status === 'fulfilled' && r.value.stepId === step.id
        );
        return !stepResult || !stepResult.value.result.success;
      });

      if (failedCriticalSteps.length > 0) {
        throw new Error(`关键步骤失败: ${failedCriticalSteps.map(s => s.id).join(', ')}`);
      }

      return {
        success: true,
        completedSteps: completedSteps,
        results: results
      };

    } catch (error) {
      return {
        success: false,
        message: error.message,
        completedSteps: completedSteps,
        results: results
      };
    }
  }

  /**
   * 执行自适应加载
   */
  async executeAdaptiveLoad(context) {
    // 自适应策略：优先加载关键步骤，然后并行加载非关键步骤
    const results = new Map();
    const completedSteps = [];

    try {
      // 第一阶段：顺序加载关键步骤
      const criticalSteps = this.loadSteps.filter(step => step.critical);
      
      for (const step of criticalSteps) {
        if (!this.areDependenciesMet(step)) {
          continue;
        }

        const stepResult = await this.executeStep(step, context);
        
        if (stepResult.success) {
          results.set(step.id, stepResult.data);
          completedSteps.push(step.id);
          this.currentStep++;
        } else {
          throw new Error(`关键步骤 ${step.id} 失败: ${stepResult.error}`);
        }
      }

      // 第二阶段：并行加载非关键步骤
      const nonCriticalSteps = this.loadSteps.filter(step => !step.critical);
      const nonCriticalPromises = nonCriticalSteps.map(step => 
        this.executeStepWithDependencyWait(step, context)
          .then(result => {
            if (result.success) {
              results.set(step.id, result.data);
              completedSteps.push(step.id);
            }
            return { stepId: step.id, result: result };
          })
      );

      await Promise.allSettled(nonCriticalPromises);

      return {
        success: true,
        completedSteps: completedSteps,
        results: results
      };

    } catch (error) {
      return {
        success: false,
        message: error.message,
        completedSteps: completedSteps,
        results: results
      };
    }
  }

  /**
   * 执行单个步骤
   */
  async executeStep(step, context) {
    const stepState = this.stepStates.get(step.id);
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(step.id, context);
      if (this.cache.has(cacheKey)) {
        const cachedResult = this.cache.get(cacheKey);
        if (this.isCacheValid(cachedResult)) {
          console.log(`📦 使用缓存: ${step.id}`);
          this.updateStepState(step.id, { 
            status: 'completed', 
            progress: 100,
            cached: true 
          });
          return { success: true, data: cachedResult.data };
        }
      }

      // 更新步骤状态
      this.updateStepState(step.id, { 
        status: 'loading', 
        startTime: Date.now() 
      });

      // 触发步骤开始事件
      this.emit('stepStarted', {
        stepId: step.id,
        stepName: step.name,
        description: step.description
      });

      // 执行步骤逻辑
      const stepResult = await this.executeStepLogic(step, context);

      // 更新步骤状态
      const endTime = Date.now();
      const duration = endTime - stepState.startTime;
      
      this.updateStepState(step.id, {
        status: 'completed',
        progress: 100,
        endTime: endTime
      });

      // 缓存结果
      if (this.loadingStrategy.cacheResults) {
        this.cache.set(cacheKey, {
          data: stepResult,
          timestamp: Date.now(),
          ttl: this.cacheConfig.ttl
        });
      }

      // 记录性能数据
      this.performanceStats.stepLoadTimes[step.id] = duration;

      // 触发步骤完成事件
      this.emit('stepCompleted', {
        stepId: step.id,
        stepName: step.name,
        duration: duration,
        result: stepResult
      });

      return { success: true, data: stepResult };

    } catch (error) {
      // 更新错误状态
      this.updateStepState(step.id, {
        status: 'error',
        error: error,
        retryCount: stepState.retryCount + 1
      });

      // 记录错误
      this.errorLog.push({
        stepId: step.id,
        error: error.message,
        timestamp: Date.now()
      });

      // 触发步骤错误事件
      this.emit('stepError', {
        stepId: step.id,
        stepName: step.name,
        error: error.message
      });

      // 重试逻辑
      if (stepState.retryCount < this.loadingStrategy.retryAttempts) {
        console.log(`🔄 重试步骤 ${step.id} (第${stepState.retryCount + 1}次)`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒重试
        return this.executeStep(step, context);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * 执行步骤具体逻辑
   */
  async executeStepLogic(step, context) {
    // 模拟步骤执行时间
    await new Promise(resolve => setTimeout(resolve, step.estimatedTime));

    switch (step.id) {
      case 'basic_info':
        return this.processBasicInfo(context);
      
      case 'wuxing_analysis':
        return this.analyzeWuxing(context);
      
      case 'balance_calculation':
        return this.calculateBalance(context);
      
      case 'rules_matching':
        return this.matchRules(context);
      
      case 'enhanced_analysis':
        return this.performEnhancedAnalysis(context);
      
      case 'report_generation':
        return this.generateReport(context);
      
      default:
        throw new Error(`未知步骤: ${step.id}`);
    }
  }

  /**
   * 处理基础信息
   */
  async processBasicInfo(context) {
    const { fourPillars, birthInfo } = context;
    
    if (!fourPillars || fourPillars.length !== 4) {
      throw new Error('八字数据不完整');
    }

    return {
      fourPillars: fourPillars,
      birthInfo: birthInfo,
      processedAt: Date.now()
    };
  }

  /**
   * 分析五行
   */
  async analyzeWuxing(context) {
    const basicInfo = this.loadResults.get('basic_info');
    if (!basicInfo) {
      throw new Error('基础信息未处理');
    }

    // 简化的五行分析逻辑
    const wuxingScores = { wood: 0, fire: 0, earth: 0, metal: 0, water: 0 };
    
    const ganWuxing = {
      '甲': 'wood', '乙': 'wood',
      '丙': 'fire', '丁': 'fire',
      '戊': 'earth', '己': 'earth',
      '庚': 'metal', '辛': 'metal',
      '壬': 'water', '癸': 'water'
    };

    basicInfo.fourPillars.forEach(pillar => {
      const ganElement = ganWuxing[pillar.gan];
      if (ganElement) {
        wuxingScores[ganElement] += 20;
      }
    });

    return {
      wuxingScores: wuxingScores,
      dominantElement: Object.keys(wuxingScores).reduce((a, b) => 
        wuxingScores[a] > wuxingScores[b] ? a : b
      ),
      analyzedAt: Date.now()
    };
  }

  /**
   * 计算平衡指数
   */
  async calculateBalance(context) {
    const wuxingAnalysis = this.loadResults.get('wuxing_analysis');
    if (!wuxingAnalysis) {
      throw new Error('五行分析未完成');
    }

    const values = Object.values(wuxingAnalysis.wuxingScores);
    const avg = values.reduce((a, b) => a + b) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    const balanceIndex = Math.max(0, Math.min(100, 100 - (stdDev / 30) * 100));

    return {
      balanceIndex: Math.round(balanceIndex),
      variance: variance,
      standardDeviation: stdDev,
      calculatedAt: Date.now()
    };
  }

  /**
   * 匹配规则
   */
  async matchRules(context) {
    if (!this.layeredRulesManager) {
      throw new Error('分层规则管理器未初始化');
    }

    const basicInfo = this.loadResults.get('basic_info');
    if (!basicInfo) {
      throw new Error('基础信息未处理');
    }

    const layeredResults = await this.layeredRulesManager.matchRulesInLayers(
      basicInfo.fourPillars,
      'professional'
    );

    return {
      layeredResults: layeredResults,
      totalMatched: layeredResults.totalMatched,
      confidence: layeredResults.confidence,
      matchedAt: Date.now()
    };
  }

  /**
   * 执行增强分析
   */
  async performEnhancedAnalysis(context) {
    const wuxingAnalysis = this.loadResults.get('wuxing_analysis');
    const rulesMatching = this.loadResults.get('rules_matching');

    if (!wuxingAnalysis || !rulesMatching) {
      throw new Error('依赖分析未完成');
    }

    return {
      enhancedInsights: [
        '基于五行分析的深度解读',
        '结合古籍规则的专业建议',
        '个性化的命理指导'
      ],
      confidence: (wuxingAnalysis.balanceIndex + rulesMatching.confidence * 100) / 2,
      analyzedAt: Date.now()
    };
  }

  /**
   * 生成报告
   */
  async generateReport(context) {
    const basicInfo = this.loadResults.get('basic_info');
    const wuxingAnalysis = this.loadResults.get('wuxing_analysis');
    const balanceCalculation = this.loadResults.get('balance_calculation');
    const rulesMatching = this.loadResults.get('rules_matching');
    const enhancedAnalysis = this.loadResults.get('enhanced_analysis');

    if (!balanceCalculation || !enhancedAnalysis) {
      throw new Error('分析数据不完整');
    }

    // 生成详细的专业分析报告
    const report = this.generateDetailedReport({
      basicInfo,
      wuxingAnalysis,
      balanceCalculation,
      rulesMatching,
      enhancedAnalysis,
      context
    });

    return {
      report: report,
      summary: {
        balanceIndex: balanceCalculation.balanceIndex,
        confidence: enhancedAnalysis.confidence,
        insights: enhancedAnalysis.enhancedInsights,
        dominantElement: wuxingAnalysis?.dominantElement,
        wuxingScores: wuxingAnalysis?.wuxingScores
      },
      sections: this.generateReportSections({
        basicInfo,
        wuxingAnalysis,
        balanceCalculation,
        rulesMatching,
        enhancedAnalysis
      }),
      generatedAt: Date.now()
    };
  }

  /**
   * 生成详细报告内容
   */
  generateDetailedReport(data) {
    const { basicInfo, wuxingAnalysis, balanceCalculation, rulesMatching, enhancedAnalysis, context } = data;

    let report = `📊 专业八字分析报告\n\n`;

    // 基础信息部分
    report += `👤 基础信息\n`;
    report += `姓名：${context.birthInfo?.name || '未提供'}\n`;
    report += `性别：${context.birthInfo?.gender || '未提供'}\n`;
    report += `八字：${basicInfo.fourPillars.map(p => p.gan + p.zhi).join(' ')}\n\n`;

    // 五行分析部分
    if (wuxingAnalysis) {
      report += `🎯 五行力量分析\n`;
      report += `主导五行：${this.getElementName(wuxingAnalysis.dominantElement)}\n`;
      report += `五行分布：\n`;
      Object.entries(wuxingAnalysis.wuxingScores).forEach(([element, score]) => {
        const elementName = this.getElementName(element);
        const strength = this.getStrengthLevel(score);
        report += `  ${elementName}：${score}分 (${strength})\n`;
      });
      report += `\n`;
    }

    // 平衡指数部分
    report += `⚖️ 五行平衡评估\n`;
    report += `平衡指数：${balanceCalculation.balanceIndex}分\n`;
    report += `评价：${this.getBalanceEvaluation(balanceCalculation.balanceIndex)}\n`;
    report += `标准差：${balanceCalculation.standardDeviation.toFixed(2)}\n\n`;

    // 古籍规则匹配部分
    if (rulesMatching) {
      report += `📚 古籍规则匹配\n`;
      report += `匹配规则总数：${rulesMatching.totalMatched}条\n`;
      report += `整体置信度：${(rulesMatching.confidence * 100).toFixed(1)}%\n`;

      if (rulesMatching.layeredResults.bestMatches?.length > 0) {
        report += `\n🎯 核心匹配规则：\n`;
        rulesMatching.layeredResults.bestMatches.slice(0, 5).forEach((rule, index) => {
          report += `${index + 1}. ${rule.pattern_name} (${rule.book_source})\n`;
          report += `   置信度：${(rule.confidence * 100).toFixed(1)}%\n`;
          report += `   解释：${rule.interpretations.substring(0, 50)}...\n\n`;
        });
      }
    }

    // 深度分析部分
    report += `🔍 深度分析洞察\n`;
    if (enhancedAnalysis.enhancedInsights) {
      enhancedAnalysis.enhancedInsights.forEach((insight, index) => {
        report += `${index + 1}. ${insight}\n`;
      });
    }
    report += `\n`;

    // 命理建议部分
    report += `💡 命理建议\n`;
    report += this.generateLifeAdvice(data);
    report += `\n`;

    // 用神分析部分
    report += `🎭 用神分析\n`;
    report += this.generateYongshenAnalysis(data);
    report += `\n`;

    // 流年提醒部分
    report += `📅 流年提醒\n`;
    report += this.generateLiunianAdvice(data);
    report += `\n`;

    // 报告尾部
    report += `📊 分析完成时间：${new Date().toLocaleString()}\n`;
    report += `🔬 分析引擎：专业八字分析系统 v3.0\n`;
    report += `📚 数据来源：古籍规则库 (${rulesMatching?.totalMatched || 0}条规则)\n`;

    return report;
  }

  /**
   * 生成报告分段结构
   */
  generateReportSections(data) {
    const { wuxingAnalysis, balanceCalculation, rulesMatching, enhancedAnalysis } = data;

    return {
      basicInfo: {
        title: '基础信息',
        icon: '👤',
        content: '八字基础数据和排盘信息'
      },
      wuxingAnalysis: {
        title: '五行分析',
        icon: '🎯',
        content: wuxingAnalysis ? `主导五行：${this.getElementName(wuxingAnalysis.dominantElement)}` : '五行分析数据'
      },
      balanceEvaluation: {
        title: '平衡评估',
        icon: '⚖️',
        content: `平衡指数：${balanceCalculation.balanceIndex}分`
      },
      rulesMatching: {
        title: '古籍匹配',
        icon: '📚',
        content: rulesMatching ? `匹配${rulesMatching.totalMatched}条规则` : '古籍规则匹配'
      },
      insights: {
        title: '深度洞察',
        icon: '🔍',
        content: enhancedAnalysis?.enhancedInsights?.length ? `${enhancedAnalysis.enhancedInsights.length}条专业洞察` : '深度分析洞察'
      },
      advice: {
        title: '命理建议',
        icon: '💡',
        content: '个性化人生建议和指导'
      }
    };
  }

  /**
   * 获取五行名称
   */
  getElementName(element) {
    const names = {
      wood: '木',
      fire: '火',
      earth: '土',
      metal: '金',
      water: '水'
    };
    return names[element] || element;
  }

  /**
   * 获取强度等级
   */
  getStrengthLevel(score) {
    if (score >= 80) return '极强';
    if (score >= 60) return '较强';
    if (score >= 40) return '中等';
    if (score >= 20) return '较弱';
    return '极弱';
  }

  /**
   * 获取平衡评价
   */
  getBalanceEvaluation(balanceIndex) {
    if (balanceIndex >= 90) return '五行极度平衡，命局非常稳定';
    if (balanceIndex >= 80) return '五行很平衡，命局稳定';
    if (balanceIndex >= 70) return '五行较平衡，运势平稳';
    if (balanceIndex >= 60) return '五行基本平衡，略有偏颇';
    if (balanceIndex >= 50) return '五行有所偏颇，需要调和';
    if (balanceIndex >= 40) return '五行偏颇较明显，需要重点调理';
    return '五行严重失衡，需要全面调整';
  }

  /**
   * 生成深度命理解读
   */
  generateDeepMingliAnalysis(data) {
    const { basicInfo, wuxingAnalysis, balanceCalculation, rulesMatching, context } = data;
    let analysis = '';

    // 1. 命局格局分析
    analysis += this.analyzeGeju(basicInfo.fourPillars, wuxingAnalysis);
    analysis += '\n\n';

    // 2. 十神关系分析
    analysis += this.analyzeShishen(basicInfo.fourPillars);
    analysis += '\n\n';

    // 3. 神煞分析
    analysis += this.analyzeShensha(basicInfo.fourPillars);
    analysis += '\n\n';

    // 4. 纳音分析
    analysis += this.analyzeNayin(basicInfo.fourPillars);
    analysis += '\n\n';

    // 5. 空亡分析
    analysis += this.analyzeKongwang(basicInfo.fourPillars);

    return analysis;
  }

  /**
   * 分析命局格局
   */
  analyzeGeju(fourPillars, wuxingAnalysis) {
    const dayGan = fourPillars[2].gan; // 日干
    const monthZhi = fourPillars[1].zhi; // 月支

    let analysis = '🎯 命局格局分析：\n';

    // 判断格局类型
    const geju = this.determineGeju(dayGan, monthZhi, wuxingAnalysis);
    analysis += `命局格局：${geju.name}\n`;
    analysis += `格局特点：${geju.characteristics}\n`;
    analysis += `格局优势：${geju.advantages}\n`;
    analysis += `注意事项：${geju.warnings}\n`;

    return analysis;
  }

  /**
   * 确定格局类型
   */
  determineGeju(dayGan, monthZhi, wuxingAnalysis) {
    // 简化的格局判断逻辑
    const ganWuxing = this.getGanWuxing(dayGan);
    const zhiWuxing = this.getZhiWuxing(monthZhi);

    // 正格判断
    if (this.isZhengcai(ganWuxing, zhiWuxing)) {
      return {
        name: '正财格',
        characteristics: '财星当令，主财运亨通，善于理财',
        advantages: '财运佳，适合经商投资，物质生活富足',
        warnings: '忌比劫夺财，需防小人破财'
      };
    } else if (this.isZhengguan(ganWuxing, zhiWuxing)) {
      return {
        name: '正官格',
        characteristics: '官星当令，主贵气，适合仕途',
        advantages: '有领导才能，适合管理工作，社会地位高',
        warnings: '忌伤官见官，需注意言行谨慎'
      };
    } else if (this.isYinxing(ganWuxing, zhiWuxing)) {
      return {
        name: '印绶格',
        characteristics: '印星当令，主智慧，学业有成',
        advantages: '聪明好学，适合文教工作，有贵人相助',
        warnings: '忌财星坏印，需防物质诱惑'
      };
    } else {
      return {
        name: '比劫格',
        characteristics: '比劫当令，主自立，性格坚强',
        advantages: '意志坚定，适合创业，朋友众多',
        warnings: '忌财多身弱，需防朋友拖累'
      };
    }
  }

  /**
   * 分析十神关系
   */
  analyzeShishen(fourPillars) {
    const dayGan = fourPillars[2].gan;
    let analysis = '⚖️ 十神关系分析：\n';

    const shishenCount = this.countShishen(fourPillars, dayGan);

    analysis += `十神分布：\n`;
    Object.entries(shishenCount).forEach(([shishen, count]) => {
      if (count > 0) {
        analysis += `${shishen}：${count}个 - ${this.getShishenMeaning(shishen)}\n`;
      }
    });

    // 分析十神组合
    analysis += '\n十神组合特点：\n';
    if (shishenCount['正官'] > 0 && shishenCount['正印'] > 0) {
      analysis += '• 官印相生：贵气十足，适合从政或管理工作\n';
    }
    if (shishenCount['正财'] > 0 && shishenCount['食神'] > 0) {
      analysis += '• 食神生财：财源广进，适合经商或技术工作\n';
    }
    if (shishenCount['伤官'] > 0 && shishenCount['正官'] > 0) {
      analysis += '• 伤官见官：需注意言行，避免官非是非\n';
    }

    return analysis;
  }

  /**
   * 分析神煞
   */
  analyzeShensha(fourPillars) {
    let analysis = '🌟 神煞分析：\n';

    const shensha = this.findShensha(fourPillars);

    if (shensha.length === 0) {
      analysis += '命局中无明显神煞，平稳发展。\n';
    } else {
      shensha.forEach(sha => {
        analysis += `• ${sha.name}：${sha.meaning}\n`;
      });
    }

    return analysis;
  }

  /**
   * 分析纳音
   */
  analyzeNayin(fourPillars) {
    let analysis = '🎵 纳音分析：\n';

    const yearNayin = this.getNayin(fourPillars[0].gan, fourPillars[0].zhi);
    const dayNayin = this.getNayin(fourPillars[2].gan, fourPillars[2].zhi);

    analysis += `年柱纳音：${yearNayin} - ${this.getNayinMeaning(yearNayin)}\n`;
    analysis += `日柱纳音：${dayNayin} - ${this.getNayinMeaning(dayNayin)}\n`;

    // 纳音相生相克分析
    const relation = this.getNayinRelation(yearNayin, dayNayin);
    analysis += `年日纳音关系：${relation}\n`;

    return analysis;
  }

  /**
   * 分析空亡
   */
  analyzeKongwang(fourPillars) {
    let analysis = '🕳️ 空亡分析：\n';

    const kongwang = this.findKongwang(fourPillars);

    if (kongwang.length === 0) {
      analysis += '命局无空亡，实在稳重。\n';
    } else {
      analysis += '空亡位置：\n';
      kongwang.forEach(kw => {
        analysis += `• ${kw.position}空亡：${kw.meaning}\n`;
      });
    }

    return analysis;
  }

  /**
   * 生成个性化人生建议
   */
  generateLifeAdvice(data) {
    const { basicInfo, wuxingAnalysis, balanceCalculation, rulesMatching, context } = data;
    let advice = '';

    // 1. 事业发展建议
    advice += '💼 事业发展建议：\n';
    advice += this.generateCareerAdvice(data);
    advice += '\n\n';

    // 2. 财运理财建议
    advice += '💰 财运理财建议：\n';
    advice += this.generateWealthAdvice(data);
    advice += '\n\n';

    // 3. 感情婚姻建议
    advice += '💕 感情婚姻建议：\n';
    advice += this.generateLoveAdvice(data);
    advice += '\n\n';

    // 4. 健康养生建议
    advice += '🏥 健康养生建议：\n';
    advice += this.generateHealthAdvice(data);
    advice += '\n\n';

    // 5. 性格修养建议
    advice += '🧘 性格修养建议：\n';
    advice += this.generatePersonalityAdvice(data);

    return advice;
  }

  /**
   * 生成事业建议
   */
  generateCareerAdvice(data) {
    const { wuxingAnalysis, balanceCalculation } = data;
    let advice = '';

    const dominantElement = wuxingAnalysis?.dominantElement;

    if (dominantElement === 'wood') {
      advice += '• 适合行业：教育、文化、林业、纺织、医药\n';
      advice += '• 发展方向：东方、东南方向有利\n';
      advice += '• 合作伙伴：火性、水性人士\n';
    } else if (dominantElement === 'fire') {
      advice += '• 适合行业：能源、电子、娱乐、餐饮、化工\n';
      advice += '• 发展方向：南方向有利\n';
      advice += '• 合作伙伴：木性、土性人士\n';
    } else if (dominantElement === 'earth') {
      advice += '• 适合行业：房地产、建筑、农业、陶瓷、中介\n';
      advice += '• 发展方向：中央、西南、东北方向有利\n';
      advice += '• 合作伙伴：火性、金性人士\n';
    } else if (dominantElement === 'metal') {
      advice += '• 适合行业：金融、机械、汽车、珠宝、军警\n';
      advice += '• 发展方向：西方、西北方向有利\n';
      advice += '• 合作伙伴：土性、水性人士\n';
    } else if (dominantElement === 'water') {
      advice += '• 适合行业：贸易、运输、水产、旅游、信息\n';
      advice += '• 发展方向：北方向有利\n';
      advice += '• 合作伙伴：金性、木性人士\n';
    }

    if (balanceCalculation.balanceIndex >= 70) {
      advice += '• 事业发展：稳步上升，适合长期规划\n';
    } else {
      advice += '• 事业发展：需要耐心积累，避免急功近利\n';
    }

    return advice;
  }

  /**
   * 生成财运建议
   */
  generateWealthAdvice(data) {
    const { wuxingAnalysis, balanceCalculation } = data;
    let advice = '';

    advice += '• 理财方式：';
    if (balanceCalculation.balanceIndex >= 70) {
      advice += '适合稳健投资，如基金、债券\n';
    } else {
      advice += '宜保守理财，避免高风险投资\n';
    }

    advice += '• 财运周期：';
    const dominantElement = wuxingAnalysis?.dominantElement;
    if (dominantElement === 'wood') {
      advice += '春季财运较佳，3-5月注意把握机会\n';
    } else if (dominantElement === 'fire') {
      advice += '夏季财运较佳，6-8月注意把握机会\n';
    } else if (dominantElement === 'earth') {
      advice += '四季末月财运较佳，注意季节转换时机\n';
    } else if (dominantElement === 'metal') {
      advice += '秋季财运较佳，9-11月注意把握机会\n';
    } else if (dominantElement === 'water') {
      advice += '冬季财运较佳，12-2月注意把握机会\n';
    }

    advice += '• 投资禁忌：避免与命局相冲的行业投资\n';

    return advice;
  }

  /**
   * 生成感情建议
   */
  generateLoveAdvice(data) {
    const { context, wuxingAnalysis, balanceCalculation } = data;
    let advice = '';

    const gender = context.birthInfo?.gender;

    if (gender === '男') {
      advice += '• 配偶特征：宜选择温柔贤淑、善解人意的伴侣\n';
    } else {
      advice += '• 配偶特征：宜选择成熟稳重、有责任心的伴侣\n';
    }

    advice += '• 婚姻时机：';
    if (balanceCalculation.balanceIndex >= 70) {
      advice += '感情运势平稳，适合早婚\n';
    } else {
      advice += '感情需要磨合，建议晚婚更佳\n';
    }

    advice += '• 相处之道：互相包容理解，避免争执\n';
    advice += '• 婚姻宫位：注意配偶宫的变化，影响婚姻稳定\n';

    return advice;
  }

  /**
   * 生成健康建议
   */
  generateHealthAdvice(data) {
    const { wuxingAnalysis } = data;
    let advice = '';

    const dominantElement = wuxingAnalysis?.dominantElement;

    advice += '• 体质特点：';
    if (dominantElement === 'wood') {
      advice += '肝胆功能较强，注意情绪调节\n';
      advice += '• 易患疾病：肝胆疾病、眼疾、筋骨问题\n';
      advice += '• 养生建议：多食绿色蔬菜，适量运动\n';
    } else if (dominantElement === 'fire') {
      advice += '心血管功能活跃，注意心脏保养\n';
      advice += '• 易患疾病：心血管疾病、失眠、血压问题\n';
      advice += '• 养生建议：清淡饮食，避免过度兴奋\n';
    } else if (dominantElement === 'earth') {
      advice += '脾胃功能较好，注意消化系统\n';
      advice += '• 易患疾病：脾胃疾病、糖尿病、肥胖\n';
      advice += '• 养生建议：规律饮食，适量甜食\n';
    } else if (dominantElement === 'metal') {
      advice += '肺部功能较强，注意呼吸系统\n';
      advice += '• 易患疾病：肺部疾病、皮肤问题、大肠疾病\n';
      advice += '• 养生建议：多食白色食物，注意空气质量\n';
    } else if (dominantElement === 'water') {
      advice += '肾脏功能活跃，注意泌尿系统\n';
      advice += '• 易患疾病：肾脏疾病、泌尿系统、骨骼问题\n';
      advice += '• 养生建议：多食黑色食物，注意保暖\n';
    }

    return advice;
  }

  /**
   * 生成性格建议
   */
  generatePersonalityAdvice(data) {
    const { wuxingAnalysis, balanceCalculation } = data;
    let advice = '';

    advice += '• 性格优势：';
    if (balanceCalculation.balanceIndex >= 70) {
      advice += '性格平和，处事稳重，人际关系和谐\n';
    } else {
      advice += '个性鲜明，有独特见解，需要包容心\n';
    }

    advice += '• 修养方向：';
    const dominantElement = wuxingAnalysis?.dominantElement;
    if (dominantElement === 'wood') {
      advice += '培养耐心，避免急躁，多读书修身\n';
    } else if (dominantElement === 'fire') {
      advice += '控制情绪，避免冲动，学会冷静思考\n';
    } else if (dominantElement === 'earth') {
      advice += '增强决断力，避免犹豫，提高执行力\n';
    } else if (dominantElement === 'metal') {
      advice += '培养灵活性，避免固执，学会变通\n';
    } else if (dominantElement === 'water') {
      advice += '增强稳定性，避免多变，坚持目标\n';
    }

    advice += '• 人际交往：真诚待人，建立良好的社交圈\n';

    return advice;
  }

  /**
   * 生成用神分析
   */
  generateYongshenAnalysis(data) {
    const { wuxingAnalysis, balanceCalculation } = data;
    let analysis = '';

    if (!wuxingAnalysis) {
      return '用神分析需要完整的五行数据';
    }

    const scores = wuxingAnalysis.wuxingScores;
    const maxElement = Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
    const minElement = Object.keys(scores).reduce((a, b) => scores[a] < scores[b] ? a : b);

    analysis += `• 最强五行：${this.getElementName(maxElement)} (${scores[maxElement]}分)\n`;
    analysis += `• 最弱五行：${this.getElementName(minElement)} (${scores[minElement]}分)\n`;

    if (balanceCalculation.balanceIndex < 60) {
      analysis += `• 建议用神：${this.getElementName(minElement)}，宜补强\n`;
      analysis += `• 忌神：${this.getElementName(maxElement)}，宜抑制\n`;
    } else {
      analysis += `• 五行相对平衡，宜维持现状\n`;
      analysis += `• 可适当调节最弱的${this.getElementName(minElement)}\n`;
    }

    return analysis;
  }

  /**
   * 生成流年建议
   */
  generateLiunianAdvice(data) {
    const currentYear = new Date().getFullYear();
    const { wuxingAnalysis } = data;

    let advice = `• ${currentYear}年流年提醒：\n`;

    // 简化的流年分析（实际应该根据具体的流年干支）
    const yearElement = this.getYearElement(currentYear);

    if (wuxingAnalysis?.dominantElement) {
      const relation = this.getElementRelation(yearElement, wuxingAnalysis.dominantElement);
      advice += `• 今年主气为${this.getElementName(yearElement)}，与命局${relation}\n`;

      if (relation === '相生') {
        advice += '• 今年运势较好，宜积极进取\n';
      } else if (relation === '相克') {
        advice += '• 今年需要谨慎，避免冲动决策\n';
      } else {
        advice += '• 今年运势平稳，宜稳步发展\n';
      }
    }

    advice += `• 下半年重点关注月份：农历三、六、九、十二月\n`;
    advice += `• 建议定期回顾和调整人生规划\n`;

    return advice;
  }

  /**
   * 获取年份对应的五行
   */
  getYearElement(year) {
    const elements = ['metal', 'water', 'wood', 'fire', 'earth'];
    return elements[year % 5];
  }

  /**
   * 获取五行关系
   */
  getElementRelation(element1, element2) {
    const shengRelations = {
      wood: 'fire',
      fire: 'earth',
      earth: 'metal',
      metal: 'water',
      water: 'wood'
    };

    const keRelations = {
      wood: 'earth',
      fire: 'metal',
      earth: 'water',
      metal: 'wood',
      water: 'fire'
    };

    if (shengRelations[element1] === element2 || shengRelations[element2] === element1) {
      return '相生';
    } else if (keRelations[element1] === element2 || keRelations[element2] === element1) {
      return '相克';
    } else {
      return '平和';
    }
  }

  // ===== 命理分析辅助函数 ===== //

  /**
   * 获取天干五行
   */
  getGanWuxing(gan) {
    const ganWuxing = {
      '甲': 'wood', '乙': 'wood',
      '丙': 'fire', '丁': 'fire',
      '戊': 'earth', '己': 'earth',
      '庚': 'metal', '辛': 'metal',
      '壬': 'water', '癸': 'water'
    };
    return ganWuxing[gan];
  }

  /**
   * 获取地支五行
   */
  getZhiWuxing(zhi) {
    const zhiWuxing = {
      '子': 'water', '丑': 'earth', '寅': 'wood', '卯': 'wood',
      '辰': 'earth', '巳': 'fire', '午': 'fire', '未': 'earth',
      '申': 'metal', '酉': 'metal', '戌': 'earth', '亥': 'water'
    };
    return zhiWuxing[zhi];
  }

  /**
   * 判断是否为正财格
   */
  isZhengcai(dayGanWuxing, monthZhiWuxing) {
    const zhengcaiRelations = {
      'wood': 'earth',
      'fire': 'metal',
      'earth': 'water',
      'metal': 'wood',
      'water': 'fire'
    };
    return zhengcaiRelations[dayGanWuxing] === monthZhiWuxing;
  }

  /**
   * 判断是否为正官格
   */
  isZhengguan(dayGanWuxing, monthZhiWuxing) {
    const zhengguanRelations = {
      'wood': 'metal',
      'fire': 'water',
      'earth': 'wood',
      'metal': 'fire',
      'water': 'earth'
    };
    return zhengguanRelations[dayGanWuxing] === monthZhiWuxing;
  }

  /**
   * 判断是否为印绶格
   */
  isYinxing(dayGanWuxing, monthZhiWuxing) {
    const yinxingRelations = {
      'wood': 'water',
      'fire': 'wood',
      'earth': 'fire',
      'metal': 'earth',
      'water': 'metal'
    };
    return yinxingRelations[dayGanWuxing] === monthZhiWuxing;
  }

  /**
   * 统计十神数量
   */
  countShishen(fourPillars, dayGan) {
    const shishenCount = {
      '比肩': 0, '劫财': 0, '食神': 0, '伤官': 0,
      '正财': 0, '偏财': 0, '正官': 0, '七杀': 0,
      '正印': 0, '偏印': 0
    };

    fourPillars.forEach((pillar, index) => {
      if (index === 2) return; // 跳过日柱天干（自己）

      const shishen = this.getShishen(dayGan, pillar.gan);
      if (shishen && shishenCount.hasOwnProperty(shishen)) {
        shishenCount[shishen]++;
      }
    });

    return shishenCount;
  }

  /**
   * 获取十神关系
   */
  getShishen(dayGan, otherGan) {
    const dayWuxing = this.getGanWuxing(dayGan);
    const otherWuxing = this.getGanWuxing(otherGan);
    const dayYinYang = this.getGanYinYang(dayGan);
    const otherYinYang = this.getGanYinYang(otherGan);

    if (dayWuxing === otherWuxing) {
      return dayYinYang === otherYinYang ? '比肩' : '劫财';
    }

    const relation = this.getElementRelation(dayWuxing, otherWuxing);

    if (relation === '相生') {
      if (this.isShengOther(dayWuxing, otherWuxing)) {
        return dayYinYang === otherYinYang ? '食神' : '伤官';
      } else {
        return dayYinYang === otherYinYang ? '正印' : '偏印';
      }
    } else if (relation === '相克') {
      if (this.isKeOther(dayWuxing, otherWuxing)) {
        return dayYinYang === otherYinYang ? '正财' : '偏财';
      } else {
        return dayYinYang === otherYinYang ? '正官' : '七杀';
      }
    }

    return null;
  }

  /**
   * 获取天干阴阳
   */
  getGanYinYang(gan) {
    const yangGan = ['甲', '丙', '戊', '庚', '壬'];
    return yangGan.includes(gan) ? 'yang' : 'yin';
  }

  /**
   * 判断是否生其他五行
   */
  isShengOther(element1, element2) {
    const shengRelations = {
      wood: 'fire',
      fire: 'earth',
      earth: 'metal',
      metal: 'water',
      water: 'wood'
    };
    return shengRelations[element1] === element2;
  }

  /**
   * 判断是否克其他五行
   */
  isKeOther(element1, element2) {
    const keRelations = {
      wood: 'earth',
      fire: 'metal',
      earth: 'water',
      metal: 'wood',
      water: 'fire'
    };
    return keRelations[element1] === element2;
  }

  /**
   * 获取十神含义
   */
  getShishenMeaning(shishen) {
    const meanings = {
      '比肩': '自我意识强，独立自主',
      '劫财': '竞争意识强，善于合作',
      '食神': '才华横溢，享受生活',
      '伤官': '聪明机智，表达能力强',
      '正财': '理财有道，物质富足',
      '偏财': '机会财运，善于投机',
      '正官': '正直守法，适合管理',
      '七杀': '果断决绝，有领导力',
      '正印': '学识渊博，有贵人助',
      '偏印': '思维独特，多才多艺'
    };
    return meanings[shishen] || '';
  }

  /**
   * 查找神煞
   */
  findShensha(fourPillars) {
    const shensha = [];

    // 简化的神煞查找
    const dayZhi = fourPillars[2].zhi;
    const yearZhi = fourPillars[0].zhi;

    // 桃花煞
    if (['子', '午', '卯', '酉'].includes(dayZhi)) {
      shensha.push({
        name: '桃花煞',
        meaning: '异性缘佳，但需注意感情纠纷'
      });
    }

    // 天乙贵人
    if (this.hasTianyiGuiren(fourPillars)) {
      shensha.push({
        name: '天乙贵人',
        meaning: '有贵人相助，遇难呈祥'
      });
    }

    // 华盖星
    if (['辰', '戌', '丑', '未'].includes(dayZhi)) {
      shensha.push({
        name: '华盖星',
        meaning: '聪明好学，但略显孤独'
      });
    }

    return shensha;
  }

  /**
   * 检查是否有天乙贵人
   */
  hasTianyiGuiren(fourPillars) {
    // 简化判断
    return Math.random() > 0.7; // 30%概率有天乙贵人
  }

  /**
   * 获取纳音
   */
  getNayin(gan, zhi) {
    const nayinTable = {
      '甲子': '海中金', '乙丑': '海中金',
      '丙寅': '炉中火', '丁卯': '炉中火',
      '戊辰': '大林木', '己巳': '大林木',
      '庚午': '路旁土', '辛未': '路旁土',
      '壬申': '剑锋金', '癸酉': '剑锋金',
      '甲戌': '山头火', '乙亥': '山头火'
      // ... 更多纳音对应关系
    };

    const ganzhi = gan + zhi;
    return nayinTable[ganzhi] || '未知纳音';
  }

  /**
   * 获取纳音含义
   */
  getNayinMeaning(nayin) {
    const meanings = {
      '海中金': '深藏不露，大器晚成',
      '炉中火': '热情奔放，事业有成',
      '大林木': '根基深厚，稳步发展',
      '路旁土': '包容宽厚，助人为乐',
      '剑锋金': '锋芒毕露，勇往直前',
      '山头火': '光明磊落，声名远播'
    };
    return meanings[nayin] || '性格独特，命运多变';
  }

  /**
   * 获取纳音关系
   */
  getNayinRelation(nayin1, nayin2) {
    // 简化的纳音关系判断
    if (nayin1 === nayin2) {
      return '同类相助';
    } else if (nayin1.includes('金') && nayin2.includes('水')) {
      return '金水相生';
    } else if (nayin1.includes('水') && nayin2.includes('木')) {
      return '水木相生';
    } else {
      return '关系平和';
    }
  }

  /**
   * 查找空亡
   */
  findKongwang(fourPillars) {
    const kongwang = [];

    // 简化的空亡查找
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;

    // 根据日柱查找空亡
    if (['甲', '乙'].includes(dayGan) && ['戌', '亥'].includes(dayZhi)) {
      kongwang.push({
        position: '日柱',
        meaning: '主观能动性强，但有时显得不切实际'
      });
    }

    return kongwang;
  }

  /**
   * 检查依赖是否满足
   */
  areDependenciesMet(step) {
    return step.dependencies.every(depId => {
      const depState = this.stepStates.get(depId);
      return depState && depState.status === 'completed';
    });
  }

  /**
   * 等待依赖完成后执行步骤
   */
  async executeStepWithDependencyWait(step, context) {
    // 等待依赖完成
    while (!this.areDependenciesMet(step)) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return this.executeStep(step, context);
  }

  /**
   * 更新步骤状态
   */
  updateStepState(stepId, updates) {
    const currentState = this.stepStates.get(stepId);
    this.stepStates.set(stepId, { ...currentState, ...updates });
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(stepId, context) {
    const contextHash = JSON.stringify(context).slice(0, 50);
    return `${stepId}_${contextHash}`;
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cachedItem) {
    if (!cachedItem || !cachedItem.timestamp) {
      return false;
    }
    
    const now = Date.now();
    return (now - cachedItem.timestamp) < cachedItem.ttl;
  }

  /**
   * 保存缓存
   */
  saveCache() {
    if (this.isolationManager && this.cache.size > 0) {
      try {
        const cacheData = {};
        this.cache.forEach((value, key) => {
          cacheData[key] = value;
        });
        
        this.isolationManager.isolatedStorage.setLocal(
          'progressiveLoadCache', 
          JSON.stringify(cacheData)
        );
        
        console.log(`💾 缓存已保存: ${this.cache.size}项`);
      } catch (error) {
        console.warn('⚠️ 缓存保存失败:', error);
      }
    }
  }

  /**
   * 重置加载状态
   */
  resetLoadingState() {
    this.currentStep = 0;
    this.loadingState = 'idle';
    this.loadResults.clear();
    this.errorLog = [];
    
    // 重置步骤状态
    this.stepStates.forEach((state, stepId) => {
      this.stepStates.set(stepId, {
        ...state,
        status: 'pending',
        progress: 0,
        startTime: null,
        endTime: null,
        error: null,
        retryCount: 0
      });
    });
  }

  /**
   * 获取加载进度
   */
  getLoadingProgress() {
    const completedSteps = Array.from(this.stepStates.values())
      .filter(state => state.status === 'completed').length;
    
    const totalWeight = this.loadSteps.reduce((sum, step) => sum + step.weight, 0);
    const completedWeight = this.loadSteps
      .filter(step => this.stepStates.get(step.id).status === 'completed')
      .reduce((sum, step) => sum + step.weight, 0);

    return {
      currentStep: this.currentStep,
      totalSteps: this.totalSteps,
      completedSteps: completedSteps,
      progressPercentage: Math.round((completedWeight / totalWeight) * 100),
      loadingState: this.loadingState,
      stepStates: Array.from(this.stepStates.entries())
    };
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performanceStats,
      cacheHitRate: this.calculateCacheHitRate(),
      errorRate: this.calculateErrorRate(),
      currentCacheSize: this.cache.size
    };
  }

  /**
   * 计算缓存命中率
   */
  calculateCacheHitRate() {
    const totalRequests = Array.from(this.stepStates.values()).length;
    const cacheHits = Array.from(this.stepStates.values())
      .filter(state => state.cached).length;
    
    return totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
  }

  /**
   * 计算错误率
   */
  calculateErrorRate() {
    const totalSteps = this.stepStates.size;
    const errorSteps = Array.from(this.stepStates.values())
      .filter(state => state.status === 'error').length;
    
    return totalSteps > 0 ? (errorSteps / totalSteps) * 100 : 0;
  }

  /**
   * 事件系统
   */
  on(event, handler) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(handler);
  }

  emit(event, data) {
    const handlers = this.eventListeners.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * 清理渐进式加载管理器
   */
  cleanup() {
    console.log('🧹 开始清理渐进式加载管理器...');
    
    // 清理事件监听器
    this.eventListeners.clear();
    
    // 清理缓存
    this.cache.clear();
    
    // 清理状态
    this.stepStates.clear();
    this.loadResults.clear();
    
    // 重置状态
    this.isInitialized = false;
    this.loadingState = 'idle';
    this.currentStep = 0;
    
    console.log('✅ 渐进式加载管理器清理完成');
  }
}

// 导出渐进式加载管理器
module.exports = ProgressiveLoadManager;
