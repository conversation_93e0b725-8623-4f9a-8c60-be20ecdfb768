#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出古籍规则数据库为JSON格式
查找和导出261条核心古籍规则
"""

import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional

def export_classical_rules_to_json():
    """导出古籍规则数据库为JSON格式"""
    print("🔍 查找261条古籍规则数据")
    print("=" * 60)
    
    # 连接数据库
    db_path = "占卜系统/data/bazi_classical_complete.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看数据库结构
        print("📊 数据库结构分析:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"   数据库表: {[table[0] for table in tables]}")
        
        # 查看规则总数
        cursor.execute("SELECT COUNT(*) FROM classical_rules")
        total_count = cursor.fetchone()[0]
        print(f"   规则总数: {total_count}")
        
        # 查看数据结构
        cursor.execute("PRAGMA table_info(classical_rules)")
        columns = cursor.fetchall()
        print(f"   表结构: {[col[1] for col in columns]}")
        
        # 查询所有规则数据
        print(f"\n📚 导出古籍规则数据...")
        cursor.execute("""
            SELECT
                rule_id, pattern_name, category, book_source,
                original_text, interpretations, confidence,
                conditions, created_at
            FROM classical_rules
            ORDER BY confidence DESC, book_source, pattern_name
        """)
        
        rules_data = cursor.fetchall()
        
        # 转换为JSON格式
        json_rules = []
        categories = set()
        book_sources = set()
        
        for rule in rules_data:
            rule_dict = {
                "rule_id": rule[0],
                "pattern_name": rule[1],
                "category": rule[2],
                "book_source": rule[3],
                "original_text": rule[4],
                "interpretations": rule[5],
                "confidence": rule[6],
                "conditions": rule[7],
                "created_at": rule[8]
            }
            
            json_rules.append(rule_dict)
            categories.add(rule[2])
            book_sources.add(rule[3])
        
        # 创建完整的JSON数据结构
        export_data = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "total_rules": len(json_rules),
                "database_path": db_path,
                "categories": sorted(list(categories)),
                "book_sources": sorted(list(book_sources)),
                "description": "古籍八字命理规则数据库完整导出"
            },
            "rules": json_rules
        }
        
        # 保存为JSON文件
        output_file = "classical_rules_complete.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据导出完成!")
        print(f"   📁 输出文件: {output_file}")
        print(f"   📊 规则总数: {len(json_rules)}")
        print(f"   📚 古籍来源: {len(book_sources)}部")
        print(f"   🏷️ 分类数量: {len(categories)}个")
        
        # 统计各古籍的规则数量
        print(f"\n📖 各古籍规则统计:")
        book_stats = {}
        for rule in json_rules:
            book = rule['book_source']
            book_stats[book] = book_stats.get(book, 0) + 1
        
        for book, count in sorted(book_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {book}: {count}条规则")
        
        # 统计各分类的规则数量
        print(f"\n🏷️ 各分类规则统计:")
        category_stats = {}
        for rule in json_rules:
            category = rule['category']
            category_stats[category] = category_stats.get(category, 0) + 1
        
        for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {category}: {count}条规则")
        
        # 查找高质量规则（261条核心规则）
        print(f"\n🌟 高质量规则筛选:")
        high_quality_rules = [rule for rule in json_rules if rule['confidence'] >= 0.8]
        print(f"   置信度≥0.8: {len(high_quality_rules)}条")
        
        core_rules = [rule for rule in json_rules if rule['confidence'] >= 0.9]
        print(f"   置信度≥0.9: {len(core_rules)}条")
        
        # 导出核心规则
        if len(core_rules) >= 200:
            core_export_data = {
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "total_rules": len(core_rules),
                    "selection_criteria": "置信度≥0.9的核心规则",
                    "description": "古籍八字命理核心规则集（高质量）"
                },
                "rules": core_rules[:261] if len(core_rules) >= 261 else core_rules
            }
            
            core_output_file = "classical_rules_core_261.json"
            with open(core_output_file, 'w', encoding='utf-8') as f:
                json.dump(core_export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 核心规则导出完成!")
            print(f"   📁 核心规则文件: {core_output_file}")
            print(f"   📊 核心规则数量: {len(core_export_data['rules'])}")
        
        conn.close()
        
        return export_data
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return None

def analyze_rule_quality():
    """分析规则质量分布"""
    print("\n🔬 规则质量分析:")
    
    try:
        conn = sqlite3.connect("占卜系统/data/bazi_classical_complete.db")
        cursor = conn.cursor()
        
        # 置信度分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN confidence >= 0.9 THEN '优秀(≥0.9)'
                    WHEN confidence >= 0.8 THEN '良好(0.8-0.9)'
                    WHEN confidence >= 0.7 THEN '中等(0.7-0.8)'
                    WHEN confidence >= 0.6 THEN '一般(0.6-0.7)'
                    ELSE '较差(<0.6)'
                END as quality_level,
                COUNT(*) as count
            FROM classical_rules 
            GROUP BY quality_level
            ORDER BY MIN(confidence) DESC
        """)
        
        quality_stats = cursor.fetchall()
        
        print("   置信度分布:")
        for level, count in quality_stats:
            print(f"     {level}: {count}条")
        
        # 查找最高质量的规则示例
        cursor.execute("""
            SELECT pattern_name, book_source, confidence, original_text
            FROM classical_rules 
            WHERE confidence >= 0.95
            ORDER BY confidence DESC
            LIMIT 5
        """)
        
        top_rules = cursor.fetchall()
        
        print("\n   最高质量规则示例:")
        for i, (name, book, conf, text) in enumerate(top_rules, 1):
            print(f"     {i}. {name} ({book}, 置信度:{conf})")
            print(f"        {text[:50]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🚀 古籍规则数据导出工具")
    print("=" * 80)
    
    # 导出完整数据
    export_data = export_classical_rules_to_json()
    
    if export_data:
        # 分析规则质量
        analyze_rule_quality()
        
        print(f"\n📋 导出总结:")
        print(f"✅ 完整数据库: classical_rules_complete.json")
        print(f"✅ 核心规则集: classical_rules_core_261.json")
        print(f"✅ 数据质量: 已分析并筛选高质量规则")
        print(f"✅ 格式标准: JSON格式，便于前端调用")
        
        print(f"\n🎯 使用建议:")
        print(f"1. 前端可直接加载JSON文件使用")
        print(f"2. 核心规则集包含最高质量的261条规则")
        print(f"3. 每条规则包含置信度，可按需筛选")
        print(f"4. 支持按古籍来源和分类查询")
    
    print(f"\n🎉 数据导出完成！")

if __name__ == "__main__":
    main()
