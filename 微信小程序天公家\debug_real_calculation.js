// debug_real_calculation.js
// 模拟真实的前端计算过程，找出"计算中"的根本原因

console.log('🔍 模拟真实前端计算过程');
console.log('='.repeat(60));

// 模拟真实的计算过程
function simulateRealCalculation() {
  console.log('🎯 开始模拟前端八字计算...');
  
  // 模拟输入数据
  const birthInfo = {
    year: 2025,
    month: 7,
    day: 31,
    hour: 17,
    minute: 15,
    longitude: 116.4074,
    gender: '男'
  };
  
  console.log('📋 输入数据:', birthInfo);
  
  // 1. 真太阳时计算
  const birthDateTime = new Date(birthInfo.year, birthInfo.month - 1, birthInfo.day, birthInfo.hour, birthInfo.minute);
  const trueSolarTime = new Date(birthInfo.year, birthInfo.month - 1, birthInfo.day, 16, 54); // 模拟真太阳时结果
  
  console.log('🌞 真太阳时计算:');
  console.log('  原始时间:', birthDateTime.toLocaleString());
  console.log('  真太阳时:', trueSolarTime.toLocaleString());
  
  // 2. 四柱计算
  const fourPillars = [
    { gan: '乙', zhi: '巳' }, // 年柱
    { gan: '癸', zhi: '未' }, // 月柱  
    { gan: '辛', zhi: '丑' }, // 日柱
    { gan: '丁', zhi: '酉' }  // 时柱
  ];
  
  console.log('🎯 四柱计算结果:', fourPillars.map(p => p.gan + p.zhi).join(' '));
  
  // 3. 基本信息计算
  console.log('\n🔧 开始基本信息计算...');
  
  const basicInfo = {};
  
  // 3.1 节气计算
  console.log('计算节气...');
  try {
    basicInfo.birth_solar_term = calculateJieqi(trueSolarTime);
    console.log('✅ 节气计算完成:', basicInfo.birth_solar_term);
  } catch (error) {
    console.error('❌ 节气计算失败:', error);
    basicInfo.birth_solar_term = '计算中...';
  }
  
  // 3.2 空亡计算
  console.log('计算空亡...');
  try {
    basicInfo.kong_wang = calculateKongWang(fourPillars);
    console.log('✅ 空亡计算完成:', basicInfo.kong_wang);
  } catch (error) {
    console.error('❌ 空亡计算失败:', error);
    basicInfo.kong_wang = '计算中...';
  }
  
  // 3.3 命卦计算
  console.log('计算命卦...');
  try {
    basicInfo.ming_gua = calculateMingGua(birthInfo.year, fourPillars, birthInfo.gender);
    console.log('✅ 命卦计算完成:', basicInfo.ming_gua);
  } catch (error) {
    console.error('❌ 命卦计算失败:', error);
    basicInfo.ming_gua = '计算中...';
  }
  
  // 3.4 人元司令计算
  console.log('计算人元司令...');
  try {
    basicInfo.renyuan_siling = calculateRenyuanSiling(trueSolarTime, fourPillars);
    console.log('✅ 人元司令计算完成:', basicInfo.renyuan_siling);
  } catch (error) {
    console.error('❌ 人元司令计算失败:', error);
    basicInfo.renyuan_siling = '计算中...';
  }
  
  // 3.5 辅助星计算
  console.log('计算辅助星...');
  try {
    basicInfo.auxiliary_stars = calculateAuxiliaryStars(fourPillars);
    console.log('✅ 辅助星计算完成:', basicInfo.auxiliary_stars);
  } catch (error) {
    console.error('❌ 辅助星计算失败:', error);
    basicInfo.auxiliary_stars = [];
  }
  
  console.log('\n📊 最终基本信息结果:');
  console.log(JSON.stringify(basicInfo, null, 2));
  
  return basicInfo;
}

// 辅助计算函数
function calculateJieqi(trueSolarTime) {
  const month = trueSolarTime.getMonth() + 1;
  const day = trueSolarTime.getDate();
  
  if (month === 7) {
    if (day >= 22) return '大暑';
    if (day >= 7) return '小暑';
  }
  
  return '未知节气';
}

function calculateKongWang(fourPillars) {
  if (!fourPillars || fourPillars.length !== 4) {
    console.log('❌ 四柱数据不完整:', fourPillars);
    return '计算中...';
  }

  const dayGan = fourPillars[2].gan;
  const dayZhi = fourPillars[2].zhi;
  const dayGanzhi = dayGan + dayZhi;

  console.log('  日柱:', dayGanzhi);

  // 简化的空亡计算
  const xunMap = {
    '辛丑': '甲午旬'
  };

  const kongwangMap = {
    '甲午旬': ['辰', '巳']
  };

  const xunName = xunMap[dayGanzhi];
  if (!xunName) {
    console.log('❌ 无法确定所属旬:', dayGanzhi);
    return '计算中...';
  }

  const kongwangZhi = kongwangMap[xunName];
  return kongwangZhi.join('');
}

function calculateMingGua(year, fourPillars, gender) {
  const yearNum = parseInt(year);
  const yearLastTwo = yearNum % 100;
  
  if (gender === '男') {
    return '震卦';
  } else {
    return '坤卦';
  }
}

function calculateRenyuanSiling(trueSolarTime, fourPillars) {
  const month = trueSolarTime.getMonth() + 1;
  
  // 7月未月，土旺
  if (month === 7) {
    return '己土司令';
  }
  
  return '未知司令';
}

function calculateAuxiliaryStars(fourPillars) {
  // 简化的辅助星计算
  return ['天乙贵人', '太极贵人'];
}

// 执行模拟
const result = simulateRealCalculation();

console.log('\n🔍 问题分析:');
const hasCalculating = Object.values(result).some(value => 
  typeof value === 'string' && value.includes('计算中')
);

if (hasCalculating) {
  console.log('❌ 发现"计算中"状态，可能原因:');
  console.log('1. 四柱数据传递有问题');
  console.log('2. 计算方法内部有异常');
  console.log('3. 数据格式不匹配');
} else {
  console.log('✅ 模拟计算正常，问题可能在实际代码中');
}

console.log('\n✅ 调试完成');
