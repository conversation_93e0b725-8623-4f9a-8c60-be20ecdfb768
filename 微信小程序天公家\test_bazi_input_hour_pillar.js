// test_bazi_input_hour_pillar.js
// 测试pages/bazi-input/index.js中的时柱计算

console.log('🔍 测试pages/bazi-input/index.js中的时柱计算');
console.log('='.repeat(60));

// 基础数据
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 获取时辰地支
function getHourZhi(hour) {
  if (hour >= 23 || hour < 1) return '子';
  if (hour >= 1 && hour < 3) return '丑';
  if (hour >= 3 && hour < 5) return '寅';
  if (hour >= 5 && hour < 7) return '卯';
  if (hour >= 7 && hour < 9) return '辰';
  if (hour >= 9 && hour < 11) return '巳';
  if (hour >= 11 && hour < 13) return '午';
  if (hour >= 13 && hour < 15) return '未';
  if (hour >= 15 && hour < 17) return '申';
  if (hour >= 17 && hour < 19) return '酉';
  if (hour >= 19 && hour < 21) return '戌';
  if (hour >= 21 && hour < 23) return '亥';
  return '子';
}

// 模拟pages/bazi-input/index.js中的算法
function calculatePreciseHourPillarBaziInput(dayGan, hour) {
  const hourZhi = getHourZhi(hour);
  const hourZhiIndex = dizhi.indexOf(hourZhi);

  // pages/bazi-input/index.js中的五鼠遁映射
  const wushuDunTimeMap = {
    '甲': 0, '己': 0, // 甲己还加甲
    '乙': 2, '庚': 2, // 乙庚丙作初
    '丙': 4, '辛': 4, // 丙辛从戊起
    '丁': 6, '壬': 6, // 丁壬庚子居
    '戊': 8, '癸': 8  // 戊癸何方发，壬子是真途
  };

  const hourGanStart = wushuDunTimeMap[dayGan];
  const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;

  console.log('📊 bazi-input算法计算:', {
    日干: dayGan,
    时辰: hour,
    时支: hourZhi,
    时支索引: hourZhiIndex,
    起始干索引: hourGanStart,
    时干索引: hourGanIndex,
    时干: tiangan[hourGanIndex],
    结果: tiangan[hourGanIndex] + hourZhi
  });

  return {
    gan: tiangan[hourGanIndex],
    zhi: hourZhi
  };
}

// 正确的五鼠遁算法（使用天干名称）
function calculateHourPillarCorrect(dayGan, hour) {
  const hourZhi = getHourZhi(hour);
  const hourZhiIndex = dizhi.indexOf(hourZhi);
  
  // 正确的五鼠遁表（日上起时）
  const wushuDunMap = {
    '甲': '甲', '己': '甲',  // 甲己还加甲
    '乙': '丙', '庚': '丙',  // 乙庚丙作初
    '丙': '戊', '辛': '戊',  // 丙辛从戊起
    '丁': '庚', '壬': '庚',  // 丁壬庚子居
    '戊': '壬', '癸': '壬'   // 戊癸何方发，壬子是真途
  };
  
  const baseGan = wushuDunMap[dayGan];
  const baseGanIndex = tiangan.indexOf(baseGan);
  const hourGanIndex = (baseGanIndex + hourZhiIndex) % 10;
  
  console.log('✅ 正确算法计算:', {
    日干: dayGan,
    时辰: hour,
    时支: hourZhi,
    时支索引: hourZhiIndex,
    起始天干: baseGan,
    起始天干索引: baseGanIndex,
    时干索引: hourGanIndex,
    时干: tiangan[hourGanIndex],
    结果: tiangan[hourGanIndex] + hourZhi
  });
  
  return {
    gan: tiangan[hourGanIndex],
    zhi: hourZhi
  };
}

// 测试不同日干的时柱计算
console.log('\n🧪 测试不同日干的时柱计算:');

const testCases = [
  { dayGan: '甲', hour: 17, expected: '癸酉' },
  { dayGan: '乙', hour: 17, expected: '乙酉' },
  { dayGan: '丙', hour: 17, expected: '丁酉' },
  { dayGan: '丁', hour: 17, expected: '己酉' },
  { dayGan: '戊', hour: 17, expected: '辛酉' },
  { dayGan: '己', hour: 17, expected: '癸酉' },
  { dayGan: '庚', hour: 17, expected: '乙酉' },
  { dayGan: '辛', hour: 17, expected: '丁酉' },
  { dayGan: '壬', hour: 17, expected: '己酉' },
  { dayGan: '癸', hour: 17, expected: '辛酉' }
];

console.log('日干\t时辰\tbazi-input算法\t正确算法\t期望结果\t是否正确');
console.log('-'.repeat(70));

testCases.forEach(testCase => {
  const baziInputResult = calculatePreciseHourPillarBaziInput(testCase.dayGan, testCase.hour);
  const correctResult = calculateHourPillarCorrect(testCase.dayGan, testCase.hour);
  
  const baziInputPillar = baziInputResult.gan + baziInputResult.zhi;
  const correctPillar = correctResult.gan + correctResult.zhi;
  
  const isCorrect = correctPillar === testCase.expected ? '✅' : '❌';
  const isBaziInputCorrect = baziInputPillar === testCase.expected ? '✅' : '❌';
  
  console.log(`${testCase.dayGan}\t${testCase.hour}:00\t${baziInputPillar}\t\t${correctPillar}\t\t${testCase.expected}\t\t${isBaziInputCorrect}`);
});

// 重点测试用户提到的情况
console.log('\n🎯 重点测试2025年7月31日（辛丑日）17-19点:');

// 2025年7月31日是辛丑日
const dayGan = '辛';
for (let hour = 17; hour <= 19; hour++) {
  console.log(`\n--- ${hour}:00 ---`);
  const baziInputResult = calculatePreciseHourPillarBaziInput(dayGan, hour);
  const correctResult = calculateHourPillarCorrect(dayGan, hour);
  
  console.log(`bazi-input算法: ${baziInputResult.gan}${baziInputResult.zhi}`);
  console.log(`正确算法: ${correctResult.gan}${correctResult.zhi}`);
  
  if (hour >= 17 && hour < 19) {
    console.log(`期望结果: 丁酉 (17-19点酉时)`);
    console.log(`bazi-input是否正确: ${baziInputResult.gan + baziInputResult.zhi === '丁酉' ? '✅' : '❌'}`);
  }
}

console.log('\n🔍 问题分析:');
console.log('如果bazi-input算法显示错误结果，说明五鼠遁映射表有问题');
console.log('正确的五鼠遁应该使用天干名称映射，而不是数字索引');
