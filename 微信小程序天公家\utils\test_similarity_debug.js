/**
 * 相似度计算调试测试
 * 分析为什么相似度还是只有38%
 */

console.log('🔍 开始相似度计算调试分析...\n');

try {
  // 导入模块
  const CelebrityDatabaseAPI = require('./celebrity_database_api.js');
  
  console.log('✅ 模块导入成功');

  // 模拟一个具体的用户八字
  const testUserBazi = {
    bazi: {
      yearPillar: { gan: '甲', zhi: '子' },
      monthPillar: { gan: '丙', zhi: '寅' },
      dayPillar: { gan: '戊', zhi: '午' },
      timePillar: { gan: '壬', zhi: '戌' }
    },
    pattern: {
      mainPattern: '正官格',
      dayMaster: '戊',
      yongshen: '水',
      strength: '中等'
    }
  };

  console.log('📊 测试用户八字:');
  console.log(`年柱: ${testUserBazi.bazi.yearPillar.gan}${testUserBazi.bazi.yearPillar.zhi}`);
  console.log(`月柱: ${testUserBazi.bazi.monthPillar.gan}${testUserBazi.bazi.monthPillar.zhi}`);
  console.log(`日柱: ${testUserBazi.bazi.dayPillar.gan}${testUserBazi.bazi.dayPillar.zhi}`);
  console.log(`时柱: ${testUserBazi.bazi.timePillar.gan}${testUserBazi.bazi.timePillar.zhi}`);
  console.log(`格局: ${testUserBazi.pattern.mainPattern}`);
  console.log(`用神: ${testUserBazi.pattern.yongshen}`);

  // 获取数据库统计
  const stats = CelebrityDatabaseAPI.getStatistics();
  console.log(`\n📈 数据库统计:`);
  console.log(`总名人数: ${stats.totalCelebrities}`);
  console.log(`男性: ${stats.genderDistribution.male}人 (${Math.round(stats.genderDistribution.male/stats.totalCelebrities*100)}%)`);
  console.log(`女性: ${stats.genderDistribution.female}人 (${Math.round(stats.genderDistribution.female/stats.totalCelebrities*100)}%)`);

  // 测试男性匹配
  console.log('\n🔍 测试男性用户匹配...');
  const maleResults = CelebrityDatabaseAPI.findSimilarCelebrities(testUserBazi, {
    limit: 5,
    minSimilarity: 0.1, // 降低阈值看所有结果
    userGender: '男'
  });

  console.log(`找到 ${maleResults.length} 个匹配结果:`);
  
  maleResults.forEach((result, index) => {
    const celebrity = result.celebrity;
    console.log(`\n${index + 1}. ${celebrity.basicInfo.name} (${celebrity.basicInfo.gender})`);
    console.log(`   综合相似度: ${Math.round(result.similarity * 100)}% (${result.level})`);
    console.log(`   八字相似度: ${Math.round(result.baziSimilarity * 100)}%`);
    console.log(`   格局相似度: ${Math.round(result.patternSimilarity * 100)}%`);
    console.log(`   性别加分: ${Math.round(result.genderBonus * 100)}%`);
    
    // 详细八字对比 (使用数据库的实际结构)
    console.log(`   八字对比:`);
    console.log(`     年柱: ${celebrity.bazi.year.gan}${celebrity.bazi.year.zhi} vs ${testUserBazi.bazi.yearPillar.gan}${testUserBazi.bazi.yearPillar.zhi}`);
    console.log(`     月柱: ${celebrity.bazi.month.gan}${celebrity.bazi.month.zhi} vs ${testUserBazi.bazi.monthPillar.gan}${testUserBazi.bazi.monthPillar.zhi}`);
    console.log(`     日柱: ${celebrity.bazi.day.gan}${celebrity.bazi.day.zhi} vs ${testUserBazi.bazi.dayPillar.gan}${testUserBazi.bazi.dayPillar.zhi}`);
    console.log(`     时柱: ${celebrity.bazi.hour.gan}${celebrity.bazi.hour.zhi} vs ${testUserBazi.bazi.timePillar.gan}${testUserBazi.bazi.timePillar.zhi}`);
    
    // 格局对比
    console.log(`   格局对比:`);
    console.log(`     主格局: ${celebrity.pattern.mainPattern} vs ${testUserBazi.pattern.mainPattern}`);
    console.log(`     用神: ${celebrity.pattern.yongshen} vs ${testUserBazi.pattern.yongshen}`);
  });

  // 测试具体的相似度计算
  if (maleResults.length > 0) {
    const topResult = maleResults[0];
    const celebrity = topResult.celebrity;
    
    console.log(`\n🔬 详细分析最高相似度名人: ${celebrity.basicInfo.name}`);
    
    // 手动计算八字相似度
    console.log('\n📊 八字相似度详细计算:');
    
    // 年柱
    const yearScore = CelebrityDatabaseAPI.calculatePillarSimilarity(
      testUserBazi.bazi.yearPillar, 
      celebrity.bazi.yearPillar
    );
    console.log(`年柱相似度: ${Math.round(yearScore * 100)}% (权重20%)`);
    
    // 月柱
    const monthScore = CelebrityDatabaseAPI.calculatePillarSimilarity(
      testUserBazi.bazi.monthPillar, 
      celebrity.bazi.monthPillar
    );
    console.log(`月柱相似度: ${Math.round(monthScore * 100)}% (权重30%)`);
    
    // 日柱
    const dayScore = CelebrityDatabaseAPI.calculatePillarSimilarity(
      testUserBazi.bazi.dayPillar, 
      celebrity.bazi.dayPillar
    );
    console.log(`日柱相似度: ${Math.round(dayScore * 100)}% (权重40%)`);
    
    // 时柱
    const timeScore = CelebrityDatabaseAPI.calculatePillarSimilarity(
      testUserBazi.bazi.timePillar, 
      celebrity.bazi.timePillar
    );
    console.log(`时柱相似度: ${Math.round(timeScore * 100)}% (权重10%)`);
    
    // 计算加权平均
    const weightedBaziScore = yearScore * 0.2 + monthScore * 0.3 + dayScore * 0.4 + timeScore * 0.1;
    console.log(`八字加权平均: ${Math.round(weightedBaziScore * 100)}%`);
    
    // 格局相似度详细计算
    console.log('\n📊 格局相似度详细计算:');
    
    const mainPatternMatch = testUserBazi.pattern.mainPattern === celebrity.pattern.mainPattern;
    const yongshenMatch = testUserBazi.pattern.yongshen === celebrity.pattern.yongshen;
    const strengthMatch = testUserBazi.pattern.strength === celebrity.pattern.strength;
    
    console.log(`主格局匹配: ${mainPatternMatch ? '✅' : '❌'} (${testUserBazi.pattern.mainPattern} vs ${celebrity.pattern.mainPattern})`);
    console.log(`用神匹配: ${yongshenMatch ? '✅' : '❌'} (${testUserBazi.pattern.yongshen} vs ${celebrity.pattern.yongshen})`);
    console.log(`强度匹配: ${strengthMatch ? '✅' : '❌'} (${testUserBazi.pattern.strength} vs ${celebrity.pattern.strength})`);
    
    const patternScore = (mainPatternMatch ? 1.0 : 0.3) * 0.5 + 
                        (yongshenMatch ? 1.0 : 0.2) * 0.3 + 
                        (strengthMatch ? 1.0 : 0.5) * 0.2;
    console.log(`格局加权平均: ${Math.round(patternScore * 100)}%`);
    
    // 最终综合相似度
    const genderBonus = celebrity.basicInfo.gender === '男' ? 0.1 : 0;
    const finalScore = Math.min(1.0, weightedBaziScore * 0.5 + patternScore * 0.4 + genderBonus);
    console.log(`\n🎯 最终综合相似度: ${Math.round(finalScore * 100)}%`);
    console.log(`   八字部分: ${Math.round(weightedBaziScore * 0.5 * 100)}%`);
    console.log(`   格局部分: ${Math.round(patternScore * 0.4 * 100)}%`);
    console.log(`   性别加分: ${Math.round(genderBonus * 100)}%`);
  }

  // 分析数据库中的八字分布
  console.log('\n📈 分析数据库八字分布...');
  
  const allCelebrities = CelebrityDatabaseAPI.celebrities;
  
  // 统计日柱分布
  const dayPillarStats = {};
  const mainPatternStats = {};
  const yongshenStats = {};
  
  allCelebrities.forEach(celebrity => {
    // 日柱统计
    const dayPillar = `${celebrity.bazi.dayPillar.gan}${celebrity.bazi.dayPillar.zhi}`;
    dayPillarStats[dayPillar] = (dayPillarStats[dayPillar] || 0) + 1;
    
    // 格局统计
    const pattern = celebrity.pattern.mainPattern;
    mainPatternStats[pattern] = (mainPatternStats[pattern] || 0) + 1;
    
    // 用神统计
    const yongshen = celebrity.pattern.yongshen;
    yongshenStats[yongshen] = (yongshenStats[yongshen] || 0) + 1;
  });
  
  console.log('\n📊 日柱分布 (前10):');
  Object.entries(dayPillarStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([pillar, count]) => {
      console.log(`${pillar}: ${count}人`);
    });
  
  console.log('\n📊 格局分布:');
  Object.entries(mainPatternStats)
    .sort((a, b) => b[1] - a[1])
    .forEach(([pattern, count]) => {
      console.log(`${pattern}: ${count}人`);
    });
  
  console.log('\n📊 用神分布:');
  Object.entries(yongshenStats)
    .sort((a, b) => b[1] - a[1])
    .forEach(([yongshen, count]) => {
      console.log(`${yongshen}: ${count}人`);
    });

  // 检查是否有与测试用户完全相同的八字
  console.log('\n🔍 检查是否有相同八字...');
  const testDayPillar = `${testUserBazi.bazi.dayPillar.gan}${testUserBazi.bazi.dayPillar.zhi}`;
  const sameDayPillar = allCelebrities.filter(celebrity =>
    `${celebrity.bazi.day.gan}${celebrity.bazi.day.zhi}` === testDayPillar
  );

  console.log(`相同日柱(${testDayPillar})的名人: ${sameDayPillar.length}人`);
  if (sameDayPillar.length > 0) {
    sameDayPillar.forEach(celebrity => {
      console.log(`- ${celebrity.basicInfo.name}: ${celebrity.pattern.mainPattern}, 用神${celebrity.pattern.yongshen}`);
    });
  }

  // 🔍 深度分析：为什么相似度这么低？
  console.log('\n🔬 深度分析相似度计算...');

  if (maleResults.length > 0) {
    const topCelebrity = maleResults[0].celebrity;

    console.log(`\n分析最高相似度名人: ${topCelebrity.basicInfo.name}`);

    // 转换数据格式进行手动计算
    const celebrityBaziFormatted = {
      yearPillar: { gan: topCelebrity.bazi.year.gan, zhi: topCelebrity.bazi.year.zhi },
      monthPillar: { gan: topCelebrity.bazi.month.gan, zhi: topCelebrity.bazi.month.zhi },
      dayPillar: { gan: topCelebrity.bazi.day.gan, zhi: topCelebrity.bazi.day.zhi },
      timePillar: { gan: topCelebrity.bazi.hour.gan, zhi: topCelebrity.bazi.hour.zhi }
    };

    console.log('\n📊 逐柱详细对比:');

    // 年柱对比
    const yearMatch = testUserBazi.bazi.yearPillar.gan === celebrityBaziFormatted.yearPillar.gan &&
                     testUserBazi.bazi.yearPillar.zhi === celebrityBaziFormatted.yearPillar.zhi;
    console.log(`年柱: ${testUserBazi.bazi.yearPillar.gan}${testUserBazi.bazi.yearPillar.zhi} vs ${celebrityBaziFormatted.yearPillar.gan}${celebrityBaziFormatted.yearPillar.zhi} ${yearMatch ? '✅完全相同' : '❌不同'}`);

    // 月柱对比
    const monthMatch = testUserBazi.bazi.monthPillar.gan === celebrityBaziFormatted.monthPillar.gan &&
                      testUserBazi.bazi.monthPillar.zhi === celebrityBaziFormatted.monthPillar.zhi;
    console.log(`月柱: ${testUserBazi.bazi.monthPillar.gan}${testUserBazi.bazi.monthPillar.zhi} vs ${celebrityBaziFormatted.monthPillar.gan}${celebrityBaziFormatted.monthPillar.zhi} ${monthMatch ? '✅完全相同' : '❌不同'}`);

    // 日柱对比
    const dayMatch = testUserBazi.bazi.dayPillar.gan === celebrityBaziFormatted.dayPillar.gan &&
                    testUserBazi.bazi.dayPillar.zhi === celebrityBaziFormatted.dayPillar.zhi;
    console.log(`日柱: ${testUserBazi.bazi.dayPillar.gan}${testUserBazi.bazi.dayPillar.zhi} vs ${celebrityBaziFormatted.dayPillar.gan}${celebrityBaziFormatted.dayPillar.zhi} ${dayMatch ? '✅完全相同' : '❌不同'}`);

    // 时柱对比
    const timeMatch = testUserBazi.bazi.timePillar.gan === celebrityBaziFormatted.timePillar.gan &&
                     testUserBazi.bazi.timePillar.zhi === celebrityBaziFormatted.timePillar.zhi;
    console.log(`时柱: ${testUserBazi.bazi.timePillar.gan}${testUserBazi.bazi.timePillar.zhi} vs ${celebrityBaziFormatted.timePillar.gan}${celebrityBaziFormatted.timePillar.zhi} ${timeMatch ? '✅完全相同' : '❌不同'}`);

    // 格局对比
    console.log('\n📊 格局详细对比:');
    const patternMatch = testUserBazi.pattern.mainPattern === topCelebrity.pattern.mainPattern;
    const yongshenMatch = testUserBazi.pattern.yongshen === topCelebrity.pattern.yongshen;

    console.log(`主格局: ${testUserBazi.pattern.mainPattern} vs ${topCelebrity.pattern.mainPattern} ${patternMatch ? '✅相同' : '❌不同'}`);
    console.log(`用神: ${testUserBazi.pattern.yongshen} vs ${topCelebrity.pattern.yongshen} ${yongshenMatch ? '✅相同' : '❌不同'}`);

    // 手动计算相似度验证
    const manualBaziSim = CelebrityDatabaseAPI.calculateEnhancedBaziSimilarity(testUserBazi.bazi, celebrityBaziFormatted);
    const manualPatternSim = CelebrityDatabaseAPI.calculateEnhancedPatternSimilarity(testUserBazi.pattern, topCelebrity.pattern);
    const manualOverall = Math.min(1.0, manualBaziSim * 0.5 + manualPatternSim * 0.4 + 0.1);

    console.log(`\n🧮 手动计算验证:`);
    console.log(`八字相似度: ${Math.round(manualBaziSim * 100)}%`);
    console.log(`格局相似度: ${Math.round(manualPatternSim * 100)}%`);
    console.log(`综合相似度: ${Math.round(manualOverall * 100)}%`);
    console.log(`API返回结果: ${Math.round(maleResults[0].similarity * 100)}%`);
  }

  // 结论分析
  console.log('\n🎯 相似度低的原因分析:');
  
  if (maleResults.length === 0) {
    console.log('❌ 没有找到任何匹配结果，可能是:');
    console.log('   1. minSimilarity阈值设置过高');
    console.log('   2. 数据库中没有相似的八字组合');
    console.log('   3. 相似度计算算法过于严格');
  } else {
    const maxSimilarity = Math.max(...maleResults.map(r => r.similarity));
    console.log(`✅ 最高相似度: ${Math.round(maxSimilarity * 100)}%`);
    
    if (maxSimilarity < 0.5) {
      console.log('📊 相似度偏低的可能原因:');
      console.log('   1. 数据库中缺少相似的八字组合');
      console.log('   2. 八字完全匹配的概率本身就很低');
      console.log('   3. 格局和用神的多样性导致匹配困难');
      console.log('   4. 权重分配可能需要调整');
      
      console.log('\n💡 建议解决方案:');
      console.log('   1. 增加更多不同八字组合的历史名人');
      console.log('   2. 调整相似度计算权重');
      console.log('   3. 降低完全匹配的要求，增加部分匹配的分数');
      console.log('   4. 考虑五行相生相克关系的相似性');
    }
  }

} catch (error) {
  console.error('❌ 测试过程中出现错误:', error);
  console.log('错误详情:', error.stack);
}
