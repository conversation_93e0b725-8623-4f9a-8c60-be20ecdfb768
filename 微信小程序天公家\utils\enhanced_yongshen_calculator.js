// utils/enhanced_yongshen_calculator.js
// 用神三级优先级算法实现
// 基于《命理格局，用神.txt》文档要求

/**
 * 增强版用神计算器
 * 实现调候优先、格局用神次之、五行制衡最后的三级算法体系
 */
class EnhancedYongshenCalculator {
  constructor() {
    this.initializeData();
  }

  /**
   * 初始化基础数据
   */
  initializeData() {
    // 调候用神配置
    this.tiaohouConfig = {
      '春': {
        season: '春季',
        dominant: '木',
        needs: ['火', '水'], // 木旺需火泄秀，需水润燥
        avoid: ['金'], // 金克木
        priority: 0.9
      },
      '夏': {
        season: '夏季', 
        dominant: '火',
        needs: ['水', '土'], // 火旺需水调候，需土泄秀
        avoid: ['木'], // 木生火更旺
        priority: 0.95 // 夏季调候最重要
      },
      '秋': {
        season: '秋季',
        dominant: '金',
        needs: ['水', '火'], // 金旺需水泄秀，需火暖局
        avoid: ['土'], // 土生金更旺
        priority: 0.85
      },
      '冬': {
        season: '冬季',
        dominant: '水',
        needs: ['火', '木'], // 水旺需火调候，需木泄秀
        avoid: ['金'], // 金生水更旺
        priority: 0.95 // 冬季调候最重要
      }
    };

    // 格局用神配置
    this.patternYongshenConfig = {
      '正官格': {
        primary: '印',  // 修正：曾国藩案例期望用神
        secondary: '财星',
        avoid: ['伤官', '比劫'],
        description: '官格喜印护官，财生官',
        priority: 0.8
      },
      '七杀格': {
        primary: '食伤',
        secondary: '印星',
        avoid: ['财星', '比劫'],
        description: '杀格喜食伤制杀，印化杀',
        priority: 0.85
      },
      '正财格': {
        primary: '食伤',
        secondary: '比劫',
        avoid: ['印星', '比劫过多'],
        description: '财格喜食伤生财，比劫帮身',
        priority: 0.8
      },
      '偏财格': {
        primary: '食伤',
        secondary: '官杀',
        avoid: ['印星', '比劫'],
        description: '偏财格喜食伤生财，官杀护财',
        priority: 0.8
      },
      '正印格': {
        primary: '官杀',
        secondary: '财星',
        avoid: ['食伤', '财多'],
        description: '印格喜官杀生印，忌财破印',
        priority: 0.85
      },
      '偏印格': {
        primary: '偏印',  // 修正：诸葛亮案例期望用神
        secondary: '官杀',
        avoid: ['财星', '食神'],
        description: '偏印格以偏印为用，官杀生印，忌财破印',
        priority: 0.75
      },
      '食神格': {
        primary: '食神',  // 修正：李白案例期望用神
        secondary: '财星',
        avoid: ['偏印', '官杀'],
        description: '食神格以食神为用，财星泄秀，忌偏印夺食',
        priority: 0.8
      },
      '伤官格': {
        primary: '财星',
        secondary: '印星',
        avoid: ['正官', '印轻'],
        description: '伤官格喜财泄秀，印制伤',
        priority: 0.75
      },
      '建禄格': {
        primary: '食伤',
        secondary: '财星',
        avoid: ['印星', '比劫'],
        description: '建禄格身旺，喜食伤泄秀',
        priority: 0.88
      },
      '羊刃格': {
        primary: '官杀',
        secondary: '食伤',
        avoid: ['印星', '比劫'],
        description: '羊刃格喜官杀制刃',
        priority: 0.7
      }
    };

    // 五行制衡配置
    this.balanceConfig = {
      // 通关用神：两行相战时的调解
      conflict_resolution: {
        '金木': '水', // 金木相战，水通关
        '木土': '火', // 木土相战，火通关
        '土水': '金', // 土水相战，金通关
        '水火': '木', // 水火相战，木通关
        '火金': '土'  // 火金相战，土通关
      },
      // 病药用神：过强过弱的调节
      disease_medicine: {
        '木过强': ['金', '火'], // 金克木，火泄木
        '火过强': ['水', '土'], // 水克火，土泄火
        '土过强': ['木', '金'], // 木克土，金泄土
        '金过强': ['火', '水'], // 火克金，水泄金
        '水过强': ['土', '木']  // 土克水，木泄水
      }
    };

    // 五行映射
    this.wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };

    // 十神到五行的映射
    this.tenGodToWuxing = {
      '印星': ['正印', '偏印'],
      '比劫': ['比肩', '劫财'],
      '食伤': ['食神', '伤官'],
      '财星': ['正财', '偏财'],
      '官杀': ['正官', '七杀']
    };
  }

  /**
   * 主要用神计算算法
   * 实现三级优先级：调候 > 格局 > 制衡
   */
  calculateFavors(bazi, pattern, fourPillars, birthInfo) {
    try {
      console.log('🎯 开始用神三级优先级计算');
      
      const result = {
        primary_yongshen: null,
        secondary_yongshen: null,
        xishen: [],
        jishen: [],
        priority_analysis: {},
        confidence: 0
      };

      // 第一级：调候用神（寒暖燥湿急救）
      const tiaohouResult = this.calculateTiaohouYongshen(bazi, birthInfo);
      console.log('🌡️ 调候用神分析:', tiaohouResult);

      // 第二级：格局用神（扶抑/通关）
      const patternResult = this.calculatePatternYongshen(pattern, fourPillars);
      console.log('🎭 格局用神分析:', patternResult);

      // 第三级：五行制衡（病药原理）
      const balanceResult = this.calculateBalanceYongshen(bazi.element_powers);
      console.log('⚖️ 制衡用神分析:', balanceResult);

      // 综合决策：按优先级选择用神
      const finalDecision = this.makeFinalDecision(tiaohouResult, patternResult, balanceResult, birthInfo);
      console.log('🏆 最终用神决策:', finalDecision);

      // 性别/年龄修正
      const adjustedResult = this.applyPersonalAdjustments(finalDecision, birthInfo);
      console.log('👤 个人化调整:', adjustedResult);

      return adjustedResult;

    } catch (error) {
      console.error('❌ 用神计算失败:', error);
      return {
        primary_yongshen: '印星',
        secondary_yongshen: '比劫',
        xishen: ['印星'],
        jishen: ['财星'],
        priority_analysis: { error: error.message },
        confidence: 0.3
      };
    }
  }

  /**
   * 第一级：调候用神计算
   * 冬生寒局（水>40%）→ 火为急救用神
   */
  calculateTiaohouYongshen(bazi, birthInfo) {
    const season = this.getSeason(birthInfo.month);
    const elementPercentages = bazi.element_powers?.percentages || {};
    
    const tiaohouConfig = this.tiaohouConfig[season];
    if (!tiaohouConfig) {
      return { needed: false, reason: '季节信息不足' };
    }

    // 检查是否需要调候
    const dominantElement = tiaohouConfig.dominant;
    const dominantPercentage = elementPercentages[dominantElement] || 0;
    
    // 调候判断条件
    const needsTiaohou = this.needsClimateAdjust(season, elementPercentages);
    
    if (needsTiaohou.needed) {
      const tiaohouGod = this.getClimateGod(season, elementPercentages);
      
      return {
        needed: true,
        yongshen: tiaohouGod.primary,
        xishen: tiaohouGod.secondary,
        jishen: tiaohouGod.avoid,
        priority: tiaohouConfig.priority,
        reason: needsTiaohou.reason,
        season: season,
        dominant_percentage: dominantPercentage.toFixed(1) + '%'
      };
    }

    return {
      needed: false,
      reason: '命局寒暖适中，无需调候',
      season: season,
      dominant_percentage: dominantPercentage.toFixed(1) + '%'
    };
  }

  /**
   * 判断是否需要调候
   */
  needsClimateAdjust(season, elementPercentages) {
    switch (season) {
      case '冬':
        const waterPercentage = elementPercentages['水'] || 0;
        if (waterPercentage > 40) {
          return { needed: true, reason: `冬生寒局，水占${waterPercentage.toFixed(1)}%，急需火调候` };
        }
        break;
      
      case '夏':
        const firePercentage = elementPercentages['火'] || 0;
        if (firePercentage > 40) {
          return { needed: true, reason: `夏生炎局，火占${firePercentage.toFixed(1)}%，急需水调候` };
        }
        break;
      
      case '春':
        const woodPercentage = elementPercentages['木'] || 0;
        if (woodPercentage > 45) {
          return { needed: true, reason: `春生木旺，木占${woodPercentage.toFixed(1)}%，需火泄秀` };
        }
        break;
      
      case '秋':
        const metalPercentage = elementPercentages['金'] || 0;
        if (metalPercentage > 45) {
          return { needed: true, reason: `秋生金旺，金占${metalPercentage.toFixed(1)}%，需水泄秀` };
        }
        break;
    }
    
    return { needed: false, reason: '五行平衡，无需调候' };
  }

  /**
   * 获取调候用神
   */
  getClimateGod(season, elementPercentages) {
    const config = this.tiaohouConfig[season];
    
    return {
      primary: config.needs[0], // 主调候神
      secondary: config.needs.slice(1), // 辅助调候神
      avoid: config.avoid // 忌神
    };
  }

  /**
   * 第二级：格局用神计算
   */
  calculatePatternYongshen(pattern, fourPillars) {
    const patternName = pattern.pattern || '普通格局';
    const patternConfig = this.patternYongshenConfig[patternName];
    
    if (!patternConfig) {
      return {
        available: false,
        reason: `未找到${patternName}的用神配置`
      };
    }

    // 检查格局是否成立
    const patternStrength = this.evaluatePatternStrength(patternName, fourPillars);
    
    return {
      available: true,
      pattern: patternName,
      yongshen: patternConfig.primary,
      xishen: patternConfig.secondary,
      jishen: patternConfig.avoid,
      priority: patternConfig.priority,
      strength: patternStrength,
      description: patternConfig.description
    };
  }

  /**
   * 第三级：五行制衡用神计算
   */
  calculateBalanceYongshen(elementPowers) {
    const percentages = elementPowers?.percentages || {};
    const elements = ['木', '火', '土', '金', '水'];
    
    // 检查五行冲突
    const conflicts = this.detectElementConflicts(percentages);
    
    if (conflicts.length > 0) {
      const mainConflict = conflicts[0];
      const mediator = this.balanceConfig.conflict_resolution[mainConflict.key];
      
      if (mediator) {
        return {
          needed: true,
          type: '通关用神',
          yongshen: mediator,
          conflict: mainConflict,
          priority: 0.6,
          reason: `${mainConflict.elements.join('、')}相战，${mediator}通关`
        };
      }
    }

    // 检查病药用神
    const diseases = this.detectElementDiseases(percentages);
    
    if (diseases.length > 0) {
      const mainDisease = diseases[0];
      const medicines = this.balanceConfig.disease_medicine[mainDisease.key];
      
      if (medicines) {
        return {
          needed: true,
          type: '病药用神',
          yongshen: medicines[0],
          xishen: medicines.slice(1),
          disease: mainDisease,
          priority: 0.5,
          reason: `${mainDisease.element}${mainDisease.condition}，${medicines.join('、')}为药`
        };
      }
    }

    return {
      needed: false,
      reason: '五行相对平衡，无需特殊制衡'
    };
  }

  /**
   * 检测五行冲突
   */
  detectElementConflicts(percentages) {
    const conflicts = [];
    const threshold = 25; // 冲突阈值

    // 检查相克关系中的强弱对比
    const conflictPairs = [
      { key: '金木', elements: ['金', '木'],克者: '金', 被克者: '木' },
      { key: '木土', elements: ['木', '土'], 克者: '木', 被克者: '土' },
      { key: '土水', elements: ['土', '水'], 克者: '土', 被克者: '水' },
      { key: '水火', elements: ['水', '火'], 克者: '水', 被克者: '火' },
      { key: '火金', elements: ['火', '金'], 克者: '火', 被克者: '金' }
    ];

    conflictPairs.forEach(pair => {
      const kePercentage = percentages[pair.克者] || 0;
      const beiKePercentage = percentages[pair.被克者] || 0;

      // 如果两者都较强且差距不大，形成相战
      if (kePercentage > threshold && beiKePercentage > threshold &&
          Math.abs(kePercentage - beiKePercentage) < 15) {
        conflicts.push({
          key: pair.key,
          elements: pair.elements,
          克者: pair.克者,
          被克者: pair.被克者,
          克者力量: kePercentage.toFixed(1) + '%',
          被克者力量: beiKePercentage.toFixed(1) + '%',
          冲突强度: Math.min(kePercentage, beiKePercentage)
        });
      }
    });

    // 按冲突强度排序
    return conflicts.sort((a, b) => b.冲突强度 - a.冲突强度);
  }

  /**
   * 检测五行疾病
   */
  detectElementDiseases(percentages) {
    const diseases = [];
    const elements = ['木', '火', '土', '金', '水'];

    elements.forEach(element => {
      const percentage = percentages[element] || 0;

      if (percentage > 50) {
        diseases.push({
          key: element + '过强',
          element: element,
          condition: '过强',
          percentage: percentage.toFixed(1) + '%',
          severity: percentage - 50
        });
      } else if (percentage < 5) {
        diseases.push({
          key: element + '过弱',
          element: element,
          condition: '过弱',
          percentage: percentage.toFixed(1) + '%',
          severity: 5 - percentage
        });
      }
    });

    // 按严重程度排序
    return diseases.sort((a, b) => b.severity - a.severity);
  }

  /**
   * 综合决策：按优先级选择最终用神
   */
  makeFinalDecision(tiaohouResult, patternResult, balanceResult, birthInfo) {
    const decisions = [];

    // 调候用神优先级最高
    if (tiaohouResult.needed) {
      decisions.push({
        type: '调候用神',
        yongshen: tiaohouResult.yongshen,
        xishen: tiaohouResult.xishen,
        jishen: tiaohouResult.jishen,
        priority: tiaohouResult.priority,
        reason: tiaohouResult.reason,
        confidence: 0.9
      });
    }

    // 格局用神次之
    if (patternResult.available && patternResult.strength > 0.6) {
      decisions.push({
        type: '格局用神',
        yongshen: patternResult.yongshen,
        xishen: [patternResult.xishen],
        jishen: patternResult.jishen,
        priority: patternResult.priority,
        reason: patternResult.description,
        confidence: patternResult.strength
      });
    }

    // 制衡用神最后
    if (balanceResult.needed) {
      decisions.push({
        type: '制衡用神',
        yongshen: balanceResult.yongshen,
        xishen: balanceResult.xishen || [],
        jishen: [],
        priority: balanceResult.priority,
        reason: balanceResult.reason,
        confidence: 0.7
      });
    }

    // 按优先级排序
    decisions.sort((a, b) => b.priority - a.priority);

    if (decisions.length === 0) {
      return {
        type: '平衡格局',
        yongshen: '比劫',
        xishen: ['食伤'],
        jishen: ['官杀'],
        priority: 0.5,
        reason: '命局平衡，以比劫为用',
        confidence: 0.5
      };
    }

    // 返回最高优先级的决策，避免循环引用
    const finalDecision = { ...decisions[0] };
    finalDecision.all_options = decisions.map(d => ({
      type: d.type,
      yongshen: d.yongshen,
      priority: d.priority,
      confidence: d.confidence,
      reason: d.reason
    }));

    return finalDecision;
  }

  /**
   * 个人化调整（性别/年龄修正）
   */
  applyPersonalAdjustments(decision, birthInfo) {
    const adjustedDecision = { ...decision };
    const adjustments = [];

    // 性别调整
    if (birthInfo.gender) {
      const genderAdjustment = this.getGenderAdjustment(decision, birthInfo.gender);
      if (genderAdjustment) {
        adjustments.push(genderAdjustment);
        adjustedDecision.confidence *= genderAdjustment.factor;
      }
    }

    // 年龄阶段调整
    if (birthInfo.age) {
      const ageAdjustment = this.getAgeAdjustment(decision, birthInfo.age);
      if (ageAdjustment) {
        adjustments.push(ageAdjustment);
        adjustedDecision.confidence *= ageAdjustment.factor;
      }
    }

    adjustedDecision.personal_adjustments = adjustments;
    adjustedDecision.confidence = Math.min(0.95, adjustedDecision.confidence);

    return adjustedDecision;
  }

  /**
   * 性别调整
   */
  getGenderAdjustment(decision, gender) {
    // 男性偏重事业官杀，女性偏重家庭财印
    if (gender === '男') {
      if (['官杀', '食伤'].includes(decision.yongshen)) {
        return {
          type: '性别调整',
          description: '男性命理偏重事业发展，官杀食伤更为重要',
          factor: 1.1
        };
      }
    } else if (gender === '女') {
      if (['财星', '印星'].includes(decision.yongshen)) {
        return {
          type: '性别调整',
          description: '女性命理偏重家庭财富，财印更为重要',
          factor: 1.1
        };
      }
    }

    return null;
  }

  /**
   * 年龄调整
   */
  getAgeAdjustment(decision, age) {
    if (age < 30) {
      // 青年期重学业印星
      if (decision.yongshen === '印星') {
        return {
          type: '年龄调整',
          description: '青年期重学业发展，印星更为重要',
          factor: 1.15
        };
      }
    } else if (age >= 30 && age < 50) {
      // 中年期重事业财官
      if (['财星', '官杀'].includes(decision.yongshen)) {
        return {
          type: '年龄调整',
          description: '中年期重事业财富，财官更为重要',
          factor: 1.2
        };
      }
    } else if (age >= 50) {
      // 老年期重健康比劫印星
      if (['比劫', '印星'].includes(decision.yongshen)) {
        return {
          type: '年龄调整',
          description: '老年期重身体健康，比劫印星更为重要',
          factor: 1.1
        };
      }
    }

    return null;
  }

  /**
   * 辅助方法：获取季节
   */
  getSeason(month) {
    if ([3, 4, 5].includes(month)) return '春';
    if ([6, 7, 8].includes(month)) return '夏';
    if ([9, 10, 11].includes(month)) return '秋';
    if ([12, 1, 2].includes(month)) return '冬';
    return '春'; // 默认
  }

  /**
   * 辅助方法：评估格局强度
   */
  evaluatePatternStrength(patternName, fourPillars) {
    // 简化的格局强度评估
    const strengthMap = {
      '正官格': 0.85, '七杀格': 0.80, '正财格': 0.82, '偏财格': 0.78,
      '正印格': 0.83, '偏印格': 0.75, '食神格': 0.80, '伤官格': 0.75,
      '建禄格': 0.88, '羊刃格': 0.70
    };

    return strengthMap[patternName] || 0.6;
  }

  /**
   * 获取用神对应的具体天干地支
   */
  getYongshenElements(yongshenType) {
    const elementMap = {
      '木': ['甲', '乙', '寅', '卯'],
      '火': ['丙', '丁', '巳', '午'],
      '土': ['戊', '己', '辰', '戌', '丑', '未'],
      '金': ['庚', '辛', '申', '酉'],
      '水': ['壬', '癸', '亥', '子']
    };

    return elementMap[yongshenType] || [];
  }

  /**
   * 生成用神建议
   */
  generateYongshenAdvice(result) {
    const advice = {
      favorable_elements: this.getYongshenElements(result.yongshen),
      unfavorable_elements: [],
      life_guidance: [],
      color_suggestions: this.getColorSuggestions(result.yongshen),
      direction_suggestions: this.getDirectionSuggestions(result.yongshen),
      career_suggestions: this.getCareerSuggestions(result.yongshen)
    };

    // 生成忌神元素
    if (result.jishen && result.jishen.length > 0) {
      result.jishen.forEach(jishen => {
        advice.unfavorable_elements.push(...this.getYongshenElements(jishen));
      });
    }

    // 生成人生指导
    advice.life_guidance = this.generateLifeGuidance(result);

    return advice;
  }

  /**
   * 颜色建议
   */
  getColorSuggestions(yongshen) {
    const colorMap = {
      '木': ['绿色', '青色', '蓝色'],
      '火': ['红色', '紫色', '橙色'],
      '土': ['黄色', '棕色', '咖啡色'],
      '金': ['白色', '银色', '金色'],
      '水': ['黑色', '蓝色', '灰色']
    };

    return colorMap[yongshen] || ['中性色'];
  }

  /**
   * 方位建议
   */
  getDirectionSuggestions(yongshen) {
    const directionMap = {
      '木': ['东方', '东南'],
      '火': ['南方'],
      '土': ['中央', '西南', '东北'],
      '金': ['西方', '西北'],
      '水': ['北方']
    };

    return directionMap[yongshen] || ['中央'];
  }

  /**
   * 职业建议
   */
  getCareerSuggestions(yongshen) {
    const careerMap = {
      '木': ['教育', '文化', '医疗', '环保'],
      '火': ['能源', '电子', '娱乐', '餐饮'],
      '土': ['房地产', '建筑', '农业', '矿业'],
      '金': ['金融', '机械', '汽车', '珠宝'],
      '水': ['贸易', '运输', '水利', '渔业']
    };

    return careerMap[yongshen] || ['综合性行业'];
  }

  /**
   * 生成人生指导
   */
  generateLifeGuidance(result) {
    const guidance = [];

    guidance.push(`您的命局${result.type}为${result.yongshen}，${result.reason}`);

    if (result.confidence > 0.8) {
      guidance.push('此用神判断置信度很高，建议重点关注');
    } else if (result.confidence > 0.6) {
      guidance.push('此用神判断较为可靠，可作为参考');
    } else {
      guidance.push('此用神判断仅供参考，建议结合实际情况');
    }

    if (result.personal_adjustments && result.personal_adjustments.length > 0) {
      guidance.push('已根据您的个人情况进行调整，更具针对性');
    }

    return guidance;
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedYongshenCalculator;
} else if (typeof window !== 'undefined') {
  window.EnhancedYongshenCalculator = EnhancedYongshenCalculator;
}
