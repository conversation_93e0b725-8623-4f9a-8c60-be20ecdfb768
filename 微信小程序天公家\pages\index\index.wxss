/* pages/index/index.wxss */

/* 整体容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #8B4513, #CD853F);
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

/* 添加背景装饰元素 */
.chat-container::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: 
    radial-gradient(circle at 10% 20%, rgba(137, 124, 255, 0.15) 0%, transparent 30%), 
    radial-gradient(circle at 90% 80%, rgba(255, 145, 218, 0.12) 0%, transparent 40%);
  z-index: 0;
}

.chat-container::after {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 248, 220, 0.05) 0%, transparent 60%);
  z-index: 0;
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  padding: 50rpx 30rpx 20rpx;
  align-items: center;
  position: relative;
  z-index: 10;
}

.settings-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.settings-icon:active, .icon-hover {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.15);
}

.settings-icon image {
  width: 40rpx;
  height: 40rpx;
}

.tab-container {
  flex: 1;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 40rpx;
  padding: 8rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
  color: rgba(255, 255, 255, 0.7);
}

.tab.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.4);
}

/* 日期显示 */
.date-display {
  text-align: center;
  padding: 30rpx 0;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  letter-spacing: 1px;
}

/* 欢迎消息 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 60rpx 40rpx;
  position: relative;
}

.welcome-message::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(139, 69, 19, 0.06) 0%, transparent 60%);
  z-index: -1;
}

.welcome-title {
  font-size: 54rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: white;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.8s ease-out;
}

.welcome-subtitle {
  font-size: 34rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  animation: fadeInUp 1s ease-out;
  margin-bottom: 40rpx;
}

.welcome-hint {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  animation: fadeInUp 1.2s ease-out;
}

.hint-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.welcome-start-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50rpx;
  width: 80%;
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.4);
  transition: all 0.3s ease;
  animation: fadeInUp 1.4s ease-out;
  margin-top: 20rpx;
}

.welcome-start-button:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 10rpx rgba(93, 107, 248, 0.3);
}

.welcome-start-button text {
  font-size: 32rpx;
  font-weight: 500;
}

.welcome-start-button image {
  width: 36rpx;
  height: 36rpx;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息区域 */
.message-area {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.message-wrapper {
  margin-bottom: 30rpx;
  animation: fadeIn 0.5s ease-out;
  position: relative;
  clear: both;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.ai-wrapper {
  align-items: flex-start;
}

.user-wrapper {
  align-items: flex-end;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.avatar-container {
  width: 80rpx; /* 稍微增大聊天头像容器 */
  height: 80rpx; /* 稍微增大聊天头像容器 */
  border-radius: 50%;
  overflow: visible;
  margin-bottom: 10rpx;
  margin-right: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  position: relative;
}

.avatar {
  width: 70rpx; /* 稍微增大聊天头像 */
  height: 70rpx; /* 稍微增大聊天头像 */
  border-radius: 50%;
  border: 2px solid rgba(139, 69, 19, 0.5);
  background: rgba(255, 255, 255, 0.1);
  object-fit: cover; /* 确保图片填充方式一致 */
}

.avatar-name {
  position: absolute;
  left: 90rpx; /* 调整位置适应更大的头像 */
  top: 50%;
  transform: translateY(-50%);
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  font-weight: 500;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.message {
  display: inline-block;
  padding: 24rpx 30rpx;
  border-radius: 24rpx;
  font-size: 30rpx;
  line-height: 1.6;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  word-break: break-all;
  word-wrap: break-word;
  box-sizing: border-box;
}

.ai-message {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  color: white;
  border-radius: 4rpx 24rpx 24rpx 24rpx;
  border-left: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-message {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 24rpx 4rpx 24rpx 24rpx;
  min-width: 60rpx;
  max-width: 85%;
  align-self: flex-end;
  text-align: left;
  margin-left: auto;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.4);
}

.message-content {
  word-wrap: break-word;
  width: 100%;
  overflow: visible;
}

.message-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 8rpx;
  padding: 0 10rpx;
}

.message-bottom-space {
  height: 120rpx;
  width: 100%;
}

/* 输入指示器 */
.typing-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 30rpx;
  animation: fadeIn 0.3s ease-out;
}

.typing-indicator {
  display: flex;
  padding: 16rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 30rpx;
  width: fit-content;
  align-self: flex-start;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  margin: 0 6rpx;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10rpx); }
}

/* 底部输入区域 */
.input-area {
  padding: 20rpx 30rpx 36rpx;
  display: flex;
  align-items: center;
  background: rgba(26, 26, 46, 0.8);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 5;
  transition: all 0.3s ease;
}

.input-completed {
  opacity: 0.7;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  padding: 24rpx 30rpx;
  margin-right: 20rpx;
  color: white;
  font-size: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2), inset 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.message-input:focus {
  background: rgba(255, 255, 255, 0.15);
}

.send-button, .voice-button {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.send-button {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  margin-right: 16rpx;
  transform: translateY(0);
}

.send-button:active, .button-hover {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}

.send-button.active {
  background: linear-gradient(135deg, #A0522D, #D2691E);
}

.voice-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.voice-button:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}

.send-button image, .voice-button image {
  width: 44rpx;
  height: 44rpx;
}

/* 评估完成浮动按钮 */
.action-button-container {
  position: fixed;
  bottom: 120rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.5s ease-out;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  margin: 0 10rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.action-button:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.action-button image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.action-button text {
  font-size: 28rpx;
  font-weight: 500;
}

.view-report {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.restart {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  color: white;
}

/* 引导提示 */
.guide-tooltip {
  position: fixed;
  bottom: 160rpx;
  left: 50%;
  transform: translateX(-50%) translateY(20rpx);
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s ease;
  z-index: 200;
}

.guide-tooltip.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  animation: pulseTooltip 2s infinite;
}

.tooltip-content {
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
  position: relative;
}

.tooltip-content text {
  color: #16213e;
  font-size: 28rpx;
  font-weight: 500;
}

.tooltip-arrow {
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  margin-left: -16rpx;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid rgba(255, 255, 255, 0.9);
}

@keyframes pulseTooltip {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-8rpx); }
}

/* 媒体查询 */
@media screen and (min-height: 700px) {
  .welcome-title {
    font-size: 60rpx;
  }
  
  .welcome-subtitle {
    font-size: 36rpx;
  }
}

@media screen and (max-height: 600px) {
  .top-nav {
    padding-top: 30rpx;
  }
  
  .welcome-title {
    font-size: 48rpx;
  }
}