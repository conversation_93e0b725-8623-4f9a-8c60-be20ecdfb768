#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的数据库分析工具
分析包含9807条规则的完整数据库
"""

import json
from datetime import datetime

class CorrectDatabaseAnalyzer:
    def __init__(self):
        self.target_goals = {
            "数字化分析": 1150,
            "每日指南": 1500,
            "匹配分析": 2320,
            "专业分析": 1650
        }
    
    def analyze_complete_database(self):
        """分析完整数据库"""
        print("🔍 分析完整数据库...")
        
        # 加载完整数据库
        try:
            with open("ultimate_professional_database_20250730_182918.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            metadata = data.get('metadata', {})
            rules = data.get('rules', [])
            
            print(f"✅ 成功加载完整数据库")
            print(f"📊 总规则数: {metadata.get('total_rules', 0):,}条")
            
            # 分析各维度
            professional_dimensions = metadata.get('professional_dimensions', {})
            
            print(f"\n📈 各维度分布:")
            for dimension, count in professional_dimensions.items():
                if dimension in self.target_goals:
                    target = self.target_goals[dimension]
                    completion = (count / target) * 100
                    status = "✅ 完成" if count >= target else f"⚠️ 还需{target - count}条"
                    print(f"  {dimension}: {count:,}/{target:,} ({completion:.1f}%) {status}")
                else:
                    print(f"  {dimension}: {count:,}条")
            
            # 质量分析
            quality_metrics = metadata.get('quality_metrics', {})
            print(f"\n📊 质量指标:")
            print(f"  平均置信度: {quality_metrics.get('平均置信度', 0):.3f}")
            print(f"  高质量比例: {quality_metrics.get('高质量规则比例', 0):.1%}")
            print(f"  古籍规则比例: {quality_metrics.get('古籍规则比例', 0):.1%}")
            
            return {
                "total_rules": metadata.get('total_rules', 0),
                "dimensions": professional_dimensions,
                "quality": quality_metrics,
                "completion_analysis": self._analyze_completion(professional_dimensions)
            }
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def _analyze_completion(self, dimensions):
        """分析完成情况"""
        completion_analysis = {}
        
        for dimension, target in self.target_goals.items():
            actual = dimensions.get(dimension, 0)
            completion_rate = (actual / target) * 100
            gap = target - actual
            
            completion_analysis[dimension] = {
                "target": target,
                "actual": actual,
                "completion_rate": completion_rate,
                "gap": gap,
                "status": "完成" if actual >= target else "不足"
            }
        
        return completion_analysis

def main():
    analyzer = CorrectDatabaseAnalyzer()
    result = analyzer.analyze_complete_database()
    
    if result:
        print(f"\n🎉 数据库分析完成！")
        print(f"总规则数: {result['total_rules']:,}条")
        
        print(f"\n📊 完成情况总结:")
        for dimension, analysis in result['completion_analysis'].items():
            print(f"  {dimension}: {analysis['status']} ({analysis['completion_rate']:.1f}%)")

if __name__ == "__main__":
    main()
