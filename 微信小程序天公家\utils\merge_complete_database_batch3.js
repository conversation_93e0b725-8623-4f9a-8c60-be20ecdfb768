/**
 * 完整数据库合并工具 - 第三批次
 * 将第三批次数据与现有数据库合并，生成v3.0版本
 */

const existingDatabase = require('../data/complete_celebrities_database_v2');
const batch3Data = require('../data/batch3_complete_celebrities');
const fs = require('fs');

class CompleteDatabaseMergerBatch3 {
  constructor() {
    this.finalDatabase = {
      metadata: {
        version: "3.0",
        totalBatches: 3,
        lastUpdated: new Date().toISOString(),
        totalRecords: 0,
        averageVerificationScore: 0,
        dataQuality: "优秀",
        coveragePeriods: ["先秦", "秦汉", "三国", "魏晋", "南北朝", "隋唐", "五代", "宋", "元", "明", "清"],
        verificationStandard: "专家交叉校验+古籍依据双重认证"
      },
      celebrities: []
    };
  }

  /**
   * 合并现有数据库和第三批次数据
   */
  mergeData() {
    console.log('🔄 开始合并完整数据库...');
    
    // 添加现有数据库中的名人
    console.log(`📚 添加现有数据库: ${existingDatabase.celebrities.length} 位名人`);
    this.finalDatabase.celebrities.push(...existingDatabase.celebrities);
    
    // 添加第三批次数据
    console.log(`📚 添加第三批次数据: ${batch3Data.celebrities.length} 位名人`);
    this.finalDatabase.celebrities.push(...batch3Data.celebrities);
    
    this.finalDatabase.metadata.totalRecords = this.finalDatabase.celebrities.length;
    
    console.log(`✅ 合并完成，总计: ${this.finalDatabase.metadata.totalRecords} 位名人`);
  }

  /**
   * 计算平均验证分数
   */
  calculateAverageVerification() {
    console.log('📊 计算平均验证分数...');
    
    let totalScore = 0;
    let validCount = 0;
    
    this.finalDatabase.celebrities.forEach(celebrity => {
      if (celebrity.verification && celebrity.verification.algorithmMatch) {
        totalScore += celebrity.verification.algorithmMatch;
        validCount++;
      }
    });
    
    const averageScore = validCount > 0 ? totalScore / validCount : 0;
    this.finalDatabase.metadata.averageVerificationScore = Math.round(averageScore * 1000) / 1000;
    
    console.log(`📈 平均验证分数: ${this.finalDatabase.metadata.averageVerificationScore}`);
    
    return averageScore;
  }

  /**
   * 生成详细的朝代分布统计
   */
  generateDetailedStats() {
    console.log('📈 生成详细统计信息...');
    
    const dynastyStats = {};
    const periodStats = {};
    const categoryStats = {};
    
    this.finalDatabase.celebrities.forEach(celebrity => {
      // 朝代统计
      const dynasty = celebrity.basicInfo.dynasty;
      dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
      
      // 时期统计
      let period = this.getPeriodFromDynasty(dynasty);
      periodStats[period] = (periodStats[period] || 0) + 1;
      
      // 职业类别统计
      if (celebrity.basicInfo.occupation && Array.isArray(celebrity.basicInfo.occupation)) {
        celebrity.basicInfo.occupation.forEach(occupation => {
          categoryStats[occupation] = (categoryStats[occupation] || 0) + 1;
        });
      }
    });
    
    console.log('📊 朝代分布 (前10位):');
    Object.entries(dynastyStats)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([dynasty, count]) => {
        const percentage = ((count / this.finalDatabase.metadata.totalRecords) * 100).toFixed(1);
        console.log(`   - ${dynasty}: ${count}位 (${percentage}%)`);
      });
    
    console.log('📊 时期分布:');
    Object.entries(periodStats)
      .sort((a, b) => b[1] - a[1])
      .forEach(([period, count]) => {
        const percentage = ((count / this.finalDatabase.metadata.totalRecords) * 100).toFixed(1);
        console.log(`   - ${period}: ${count}位 (${percentage}%)`);
      });
    
    console.log('📊 职业类别分布 (前8位):');
    Object.entries(categoryStats)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 8)
      .forEach(([category, count]) => {
        const percentage = ((count / this.finalDatabase.metadata.totalRecords) * 100).toFixed(1);
        console.log(`   - ${category}: ${count}位 (${percentage}%)`);
      });
    
    return {
      dynastyStats,
      periodStats,
      categoryStats
    };
  }

  /**
   * 根据朝代确定历史时期
   */
  getPeriodFromDynasty(dynasty) {
    const periodMap = {
      '春秋': '先秦时期',
      '战国': '先秦时期',
      '秦朝': '秦汉时期',
      '西汉': '秦汉时期',
      '东汉': '秦汉时期',
      '三国': '魏晋南北朝',
      '东晋': '魏晋南北朝',
      '南朝': '魏晋南北朝',
      '北朝': '魏晋南北朝',
      '隋朝': '隋唐时期',
      '唐朝': '隋唐时期',
      '五代': '五代十国',
      '北宋': '宋元时期',
      '南宋': '宋元时期',
      '元朝': '宋元时期',
      '明朝': '明清时期',
      '清朝': '明清时期'
    };
    
    return periodMap[dynasty] || '其他时期';
  }

  /**
   * 数据完整性检查
   */
  validateIntegrity() {
    console.log('🔍 进行数据完整性检查...');
    
    const issues = [];
    const idSet = new Set();
    
    this.finalDatabase.celebrities.forEach((celebrity, index) => {
      // 检查ID唯一性
      if (idSet.has(celebrity.id)) {
        issues.push(`重复ID: ${celebrity.id}`);
      } else {
        idSet.add(celebrity.id);
      }
      
      // 检查必要字段完整性
      if (!celebrity.basicInfo?.name) {
        issues.push(`第${index + 1}位名人缺少姓名`);
      }
      
      if (!celebrity.bazi?.fullBazi) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少完整八字`);
      }
      
      if (!celebrity.pattern?.mainPattern) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少主格局`);
      }
      
      if (!celebrity.verification?.algorithmMatch) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少算法匹配度`);
      }
    });
    
    console.log(`📋 完整性检查结果:`);
    console.log(`   - 检查记录数: ${this.finalDatabase.celebrities.length}`);
    console.log(`   - 发现问题: ${issues.length} 个`);
    
    if (issues.length > 0) {
      console.log(`⚠️  问题详情:`);
      issues.slice(0, 5).forEach(issue => console.log(`     - ${issue}`));
      if (issues.length > 5) {
        console.log(`     ... 还有 ${issues.length - 5} 个问题`);
      }
    }
    
    return {
      totalChecked: this.finalDatabase.celebrities.length,
      issuesFound: issues.length,
      issues: issues
    };
  }

  /**
   * 保存最终数据库
   */
  saveDatabase() {
    const outputPath = 'data/complete_celebrities_database_v3.js';
    
    const fileContent = `/**
 * 历史名人数据库完整版 v3.0
 * 包含${this.finalDatabase.metadata.totalRecords}位历史名人
 * 覆盖从先秦到清朝的完整历史时期
 * 
 * 版本信息:
 * - v1.0: 基础数据库 (37位名人)
 * - v2.0: 第一、二批次扩展 (87位名人)
 * - v3.0: 第三批次扩展 (${this.finalDatabase.metadata.totalRecords}位名人)
 * 
 * 最后更新: ${this.finalDatabase.metadata.lastUpdated}
 * 平均验证分数: ${this.finalDatabase.metadata.averageVerificationScore}
 */

const completeCelebritiesDatabase = ${JSON.stringify(this.finalDatabase, null, 2)};

module.exports = completeCelebritiesDatabase;`;
    
    fs.writeFileSync(outputPath, fileContent, 'utf8');
    console.log(`💾 完整数据库已保存到: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 执行完整的合并流程
   */
  execute() {
    console.log('🚀 开始完整数据库合并流程 (第三批次)');
    console.log('============================================================');
    
    try {
      // 1. 合并数据
      this.mergeData();
      
      // 2. 计算平均验证分数
      const avgScore = this.calculateAverageVerification();
      
      // 3. 生成统计信息
      const stats = this.generateDetailedStats();
      
      // 4. 完整性检查
      const integrityReport = this.validateIntegrity();
      
      // 5. 保存数据库
      const outputPath = this.saveDatabase();
      
      console.log('============================================================');
      console.log('🎉 完整数据库合并完成！');
      console.log(`📊 数据库版本: v3.0`);
      console.log(`📈 总名人数: ${this.finalDatabase.metadata.totalRecords} 位`);
      console.log(`📊 平均验证分数: ${this.finalDatabase.metadata.averageVerificationScore}`);
      console.log(`🔍 数据完整性: ${integrityReport.issuesFound === 0 ? '完美' : '需要关注'}`);
      console.log(`📁 输出文件: ${outputPath}`);
      
      return {
        success: true,
        outputPath: outputPath,
        totalRecords: this.finalDatabase.metadata.totalRecords,
        averageScore: avgScore,
        stats: stats,
        integrityReport: integrityReport
      };
      
    } catch (error) {
      console.error('❌ 合并过程中发生错误:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 如果直接运行此文件，执行合并
if (require.main === module) {
  const merger = new CompleteDatabaseMergerBatch3();
  merger.execute();
}

module.exports = CompleteDatabaseMergerBatch3;
