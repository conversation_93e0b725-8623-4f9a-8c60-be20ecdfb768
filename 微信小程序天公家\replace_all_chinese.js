// replace_all_chinese.js
// 替换所有剩余的中文注释

const fs = require('fs');

console.log('🔄 替换所有剩余的中文注释...');

try {
  let wxssContent = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
  
  // 更多中文注释替换映射
  const moreReplacements = [
    ['/* 吉星分析样式 */', '/* Lucky star analysis styles */'],
    ['/* 神煞综合评价样式 */', '/* Spiritual comprehensive evaluation styles */'],
    ['/* 副星信息样式 */', '/* Secondary star information styles */'],
    ['/* 星运信息样式 */', '/* Star fortune information styles */'],
    ['/* 大运流年页面样式 */', '/* Great fortune fleeting year page styles */'],
    ['/* 当前大运样式 */', '/* Current great fortune styles */'],
    ['/* 大运序列样式 */', '/* Great fortune sequence styles */'],
    ['/* 流年分析样式 */', '/* Fleeting year analysis styles */'],
    ['/* 月运势样式 */', '/* Monthly fortune styles */'],
    ['/* 三年趋势样式 */', '/* Three year trend styles */'],
    ['/* 人生阶段分析样式 */', '/* Life stage analysis styles */'],
    ['/* 具体建议系统样式 */', '/* Specific recommendation system styles */'],
    ['/* 适合行业样式 */', '/* Suitable industry styles */'],
    ['/* 财运信息样式 */', '/* Wealth fortune information styles */'],
    ['/* 感情信息样式 */', '/* Relationship information styles */'],
    ['/* 健康建议样式 */', '/* Health recommendation styles */'],
    ['/* 综合评分系统样式 */', '/* Comprehensive scoring system styles */'],
    ['/* 响应式设计优化 */', '/* Responsive design optimization */'],
    ['/* 基本信息响应式 */', '/* Basic information responsive */'],
    ['/* 其他组件响应式 */', '/* Other components responsive */'],
    ['/* 头部压缩在小屏幕上进一步优化 */', '/* Header compression further optimized on small screens */'],
    ['padding: 20rpx 30rpx 10rpx; /* 小屏幕上更加紧凑 */', 'padding: 20rpx 30rpx 10rpx; /* More compact on small screens */'],
    ['margin-top: 15rpx; /* 小屏幕上进一步减少间距 */', 'margin-top: 15rpx; /* Further reduced spacing on small screens */'],
    ['margin-top: -30rpx; /* 小屏幕上标签页导航进一步上移 */', 'margin-top: -30rpx; /* Tab navigation further moved up on small screens */'],
    ['/* 标签页导航优化 */', '/* Tab navigation optimization */'],
    ['/* 🚀 综合分析报告样式 */', '/* Comprehensive analysis report styles */'],
    ['/* 报告摘要样式 */', '/* Report summary styles */'],
    ['/* 分段报告样式 */', '/* Segmented report styles */'],
    ['/* 报告操作区样式 */', '/* Report operation area styles */'],
    ['/* 卡片操作按钮样式 */', '/* Card operation button styles */'],
    ['/* 完整报告内容样式 */', '/* Complete report content styles */'],
    ['/* 响应式适配 */', '/* Responsive adaptation */'],
    ['/* 数字化分析组件样式 */', '/* Digital analysis component styles */'],
    ['/* 增强平衡指标卡片样式 */', '/* Enhanced balance indicator card styles */'],
    ['/* 平衡指标样式 */', '/* Balance indicator styles */'],
    ['/* 五行力量分布样式 */', '/* Five elements power distribution styles */'],
    ['/* 增强规则匹配样式 */', '/* Enhanced rule matching styles */'],
    ['/* 综合分析报告样式 */', '/* Comprehensive analysis report styles */'],
    ['/* 渐进式加载状态样式 */', '/* Progressive loading state styles */'],
    ['/* 渐进式加载步骤指示器 */', '/* Progressive loading step indicator */'],
    ['/* 加载控制按钮 */', '/* Loading control buttons */'],
    ['/* 功能介绍样式 */', '/* Feature introduction styles */'],
    ['/* 专业分析按钮样式 */', '/* Professional analysis button styles */']
  ];
  
  let replacedCount = 0;
  
  // 执行替换
  for (const [chinese, english] of moreReplacements) {
    if (wxssContent.includes(chinese)) {
      wxssContent = wxssContent.replace(new RegExp(chinese.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), english);
      replacedCount++;
      console.log(`✅ 替换: ${chinese}`);
    }
  }
  
  // 写回文件
  fs.writeFileSync('pages/bazi-result/index.wxss', wxssContent, 'utf8');
  
  console.log(`\n🎯 替换完成: 共替换了 ${replacedCount} 个中文注释`);
  
} catch (error) {
  console.error('❌ 替换失败:', error.message);
}

console.log('🏁 批量替换完成');
