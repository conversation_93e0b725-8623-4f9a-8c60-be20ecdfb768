/**
 * 分析2020年8月1日神煞差异
 * 对比我们的系统与"问真八字"的神煞计算结果
 */

console.log('🔍 分析2020年8月1日神煞差异');
console.log('='.repeat(60));
console.log('');

// 测试用例：2020年8月1日 14:07
const testBaziData = {
  year_gan: '庚', year_zhi: '子',
  month_gan: '癸', month_zhi: '未',
  day_gan: '丙', day_zhi: '子',
  hour_gan: '乙', hour_zhi: '未'
};

console.log('📋 测试用例信息：');
console.log('='.repeat(30));
console.log(`四柱：${testBaziData.year_gan}${testBaziData.year_zhi} ${testBaziData.month_gan}${testBaziData.month_zhi} ${testBaziData.day_gan}${testBaziData.day_zhi} ${testBaziData.hour_gan}${testBaziData.hour_zhi}`);
console.log('出生时间：2020年8月1日 14:07');
console.log('');

// 构建四柱数据
const fourPillars = [
  { gan: testBaziData.year_gan, zhi: testBaziData.year_zhi },   // 年柱
  { gan: testBaziData.month_gan, zhi: testBaziData.month_zhi }, // 月柱
  { gan: testBaziData.day_gan, zhi: testBaziData.day_zhi },     // 日柱
  { gan: testBaziData.hour_gan, zhi: testBaziData.hour_zhi }    // 时柱
];

// "问真八字"标准结果（从图片中提取）
const wenzhenBaziStandard = {
  神煞: [
    // 吉星神煞
    { name: '金舆', position: '月柱', type: 'auspicious' },
    { name: '金舆', position: '时柱', type: 'auspicious' },
    
    // 凶煞化解
    { name: '童子煞', position: '月柱', type: 'inauspicious' },
    { name: '童子煞', position: '时柱', type: 'inauspicious' },
    
    // 从图片中可以看到的其他神煞
    { name: '天乙贵人', position: '未知', type: 'auspicious' },
    { name: '福星贵人', position: '未知', type: 'auspicious' },
    { name: '飞刃', position: '未知', type: 'inauspicious' },
    { name: '将星', position: '未知', type: 'auspicious' },
    { name: '元辰', position: '未知', type: 'inauspicious' },
    { name: '洞下水', position: '未知', type: 'neutral' },
    { name: '沙中金', position: '未知', type: 'neutral' }
  ]
};

// 我们系统的当前结果（从日志中提取）
const ourSystemResult = {
  神煞: [
    { name: '福星贵人', position: '年柱', type: 'auspicious' },
    { name: '福星贵人', position: '日柱', type: 'auspicious' },
    { name: '童子煞', position: '日柱', type: 'inauspicious' }
  ]
};

console.log('📊 对比分析：');
console.log('='.repeat(30));

console.log('🌟 "问真八字"标准结果：');
wenzhenBaziStandard.神煞.forEach((shensha, index) => {
  const icon = shensha.type === 'auspicious' ? '🌟' : shensha.type === 'inauspicious' ? '⚡' : '⚖️';
  console.log(`   ${index + 1}. ${icon} ${shensha.name} - ${shensha.position}`);
});

console.log('\n🔧 我们系统当前结果：');
ourSystemResult.神煞.forEach((shensha, index) => {
  const icon = shensha.type === 'auspicious' ? '🌟' : shensha.type === 'inauspicious' ? '⚡' : '⚖️';
  console.log(`   ${index + 1}. ${icon} ${shensha.name} - ${shensha.position}`);
});

// 详细神煞计算验证
const detailedCalculation = {
  // 验证天乙贵人
  verifyTianyiGuiren: function() {
    console.log('\n🔍 验证天乙贵人：');
    console.log('='.repeat(20));
    
    const dayGan = fourPillars[2].gan; // 丙
    console.log(`日干：${dayGan}`);
    
    // 天乙贵人查法：丙丁猪鸡位
    const tianyiMap = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };
    
    const tianyiTargets = tianyiMap[dayGan] || [];
    console.log(`丙日干的天乙贵人：${tianyiTargets.join('、')}`);
    
    let found = false;
    fourPillars.forEach((pillar, index) => {
      if (tianyiTargets.includes(pillar.zhi)) {
        console.log(`✅ 发现天乙贵人在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
        found = true;
      }
    });
    
    if (!found) {
      console.log('❌ 未发现天乙贵人');
    }
    
    return found;
  },

  // 验证金舆
  verifyJinyu: function() {
    console.log('\n🔍 验证金舆：');
    console.log('='.repeat(15));
    
    const yearZhi = fourPillars[0].zhi; // 子
    console.log(`年支：${yearZhi}`);
    
    // 金舆查法：甲龙乙蛇丙戊羊，丁己猴歌庚犬方，辛猪壬鼠癸逢牛
    const jinyuMap = {
      '甲': '辰', '乙': '巳', '丙': '未', '丁': '申',
      '戊': '未', '己': '申', '庚': '戌', '辛': '亥',
      '壬': '子', '癸': '丑'
    };
    
    let found = false;
    fourPillars.forEach((pillar, index) => {
      const jinyuTarget = jinyuMap[pillar.gan];
      if (jinyuTarget) {
        console.log(`${pillar.gan}干的金舆：${jinyuTarget}`);
        fourPillars.forEach((checkPillar, checkIndex) => {
          if (checkPillar.zhi === jinyuTarget) {
            console.log(`✅ 发现金舆：${pillar.gan}干在${['年柱', '月柱', '日柱', '时柱'][checkIndex]}找到${jinyuTarget}`);
            found = true;
          }
        });
      }
    });
    
    if (!found) {
      console.log('❌ 未发现金舆');
    }
    
    return found;
  },

  // 验证飞刃
  verifyFeiren: function() {
    console.log('\n🔍 验证飞刃：');
    console.log('='.repeat(15));
    
    const dayGan = fourPillars[2].gan; // 丙
    console.log(`日干：${dayGan}`);
    
    // 飞刃是羊刃的对冲
    const yangrenMap = {
      '甲': '卯', '乙': '辰', '丙': '午', '丁': '未',
      '戊': '午', '己': '未', '庚': '酉', '辛': '戌',
      '壬': '子', '癸': '丑'
    };
    
    const chongMap = {
      '子': '午', '丑': '未', '寅': '申', '卯': '酉',
      '辰': '戌', '巳': '亥', '午': '子', '未': '丑',
      '申': '寅', '酉': '卯', '戌': '辰', '亥': '巳'
    };
    
    const yangrenTarget = yangrenMap[dayGan];
    const feirenTarget = chongMap[yangrenTarget];
    
    console.log(`${dayGan}日干的羊刃：${yangrenTarget}`);
    console.log(`飞刃（羊刃对冲）：${feirenTarget}`);
    
    let found = false;
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === feirenTarget) {
        console.log(`✅ 发现飞刃在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
        found = true;
      }
    });
    
    if (!found) {
      console.log('❌ 未发现飞刃');
    }
    
    return found;
  },

  // 验证将星
  verifyJiangxing: function() {
    console.log('\n🔍 验证将星：');
    console.log('='.repeat(15));
    
    const yearZhi = fourPillars[0].zhi; // 子
    console.log(`年支：${yearZhi}`);
    
    // 将星查法：寅午戌见午，申子辰见子，巳酉丑见酉，亥卯未见卯
    const jiangxingMap = {
      '寅': '午', '午': '午', '戌': '午',
      '申': '子', '子': '子', '辰': '子',
      '巳': '酉', '酉': '酉', '丑': '酉',
      '亥': '卯', '卯': '卯', '未': '卯'
    };
    
    const jiangxingTarget = jiangxingMap[yearZhi];
    console.log(`年支${yearZhi}的将星：${jiangxingTarget}`);
    
    let found = false;
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === jiangxingTarget) {
        console.log(`✅ 发现将星在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
        found = true;
      }
    });
    
    if (!found) {
      console.log('❌ 未发现将星');
    }
    
    return found;
  },

  // 验证元辰
  verifyYuanchen: function() {
    console.log('\n🔍 验证元辰：');
    console.log('='.repeat(15));
    
    const yearZhi = fourPillars[0].zhi; // 子
    console.log(`年支：${yearZhi}`);
    
    // 元辰查法：阳男阴女，年支的对冲地支后一位；阴男阳女，年支的对冲地支前一位
    const chongMap = {
      '子': '午', '丑': '未', '寅': '申', '卯': '酉',
      '辰': '戌', '巳': '亥', '午': '子', '未': '丑',
      '申': '寅', '酉': '卯', '戌': '辰', '亥': '巳'
    };
    
    const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const chongZhi = chongMap[yearZhi];
    const chongIndex = zhiOrder.indexOf(chongZhi);
    
    // 假设阳男，取对冲后一位
    const yuanchenTarget = zhiOrder[(chongIndex + 1) % 12];
    
    console.log(`年支${yearZhi}的对冲：${chongZhi}`);
    console.log(`元辰（阳男）：${yuanchenTarget}`);
    
    let found = false;
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === yuanchenTarget) {
        console.log(`✅ 发现元辰在${['年柱', '月柱', '日柱', '时柱'][index]}：${pillar.gan}${pillar.zhi}`);
        found = true;
      }
    });
    
    if (!found) {
      console.log('❌ 未发现元辰');
    }
    
    return found;
  },

  // 运行所有验证
  runAllVerifications: function() {
    console.log('\n🧪 运行所有神煞验证：');
    console.log('='.repeat(30));
    
    const results = {
      天乙贵人: this.verifyTianyiGuiren(),
      金舆: this.verifyJinyu(),
      飞刃: this.verifyFeiren(),
      将星: this.verifyJiangxing(),
      元辰: this.verifyYuanchen()
    };
    
    console.log('\n📊 验证结果汇总：');
    console.log('='.repeat(20));
    
    Object.entries(results).forEach(([name, found]) => {
      const icon = found ? '✅' : '❌';
      console.log(`   ${icon} ${name}：${found ? '发现' : '未发现'}`);
    });
    
    const foundCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n📊 发现率：${foundCount}/${totalCount} (${(foundCount/totalCount*100).toFixed(1)}%)`);
    
    return results;
  }
};

// 运行详细验证
const verificationResults = detailedCalculation.runAllVerifications();

console.log('\n🎯 问题分析：');
console.log('='.repeat(15));

console.log('❌ 主要问题：');
console.log('   1. 我们的神煞计算函数可能有算法错误');
console.log('   2. 某些重要神煞（如天乙贵人、金舆、将星）完全没有发现');
console.log('   3. 我们的计算结果与"问真八字"差异巨大');

console.log('\n🔧 需要修复的神煞：');
const missingShenshas = Object.entries(verificationResults)
  .filter(([name, found]) => !found)
  .map(([name]) => name);

if (missingShenshas.length > 0) {
  missingShenshas.forEach((name, index) => {
    console.log(`   ${index + 1}. ${name}`);
  });
} else {
  console.log('   ✅ 所有验证的神煞都已发现');
}

console.log('\n📋 下一步行动：');
console.log('='.repeat(15));
console.log('1. 修复天乙贵人计算算法');
console.log('2. 实现金舆计算功能');
console.log('3. 实现飞刃计算功能');
console.log('4. 修复将星计算算法');
console.log('5. 修复元辰计算算法');
console.log('6. 重新测试验证所有神煞');

console.log('\n✅ 2020年8月1日神煞差异分析完成！');
console.log('🎯 结论：我们的神煞计算系统存在严重算法错误，需要全面修复！');
