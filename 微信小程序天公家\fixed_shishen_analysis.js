// fixed_shishen_analysis.js
// 修复后的十神分析系统，对标"问真八字"

console.log('🌟 修复后的十神分析系统');
console.log('='.repeat(80));

// 测试数据：辛丑 甲午 癸卯 壬戌
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' }, // 年柱
    { gan: '甲', zhi: '午' }, // 月柱  
    { gan: '癸', zhi: '卯' }, // 日柱
    { gan: '壬', zhi: '戌' }  // 时柱
  ],
  dayGan: '癸'
};

// "问真八字"的标准结果
const wenZhenStandard = {
  mainStars: ['偏印', '伤官', '元男', '劫财'],
  cangGan: {
    year: ['己土', '癸水', '辛金'],
    month: ['丁火', '己土'],
    day: ['乙木'],
    hour: ['戊土', '辛金', '丁火']
  },
  deputyStars: {
    year: ['七杀', '比肩', '偏印'],
    month: ['偏财', '七杀'],
    day: ['食神'],
    hour: ['正官', '偏印', '偏财']
  }
};

// 基础数据
const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const wuxing = ['木', '木', '火', '火', '土', '土', '金', '金', '水', '水'];

// 地支藏干表
const cangganMap = {
  '子': ['癸'], 
  '丑': ['己', '癸', '辛'], 
  '寅': ['甲', '丙', '戊'],
  '卯': ['乙'], 
  '辰': ['戊', '乙', '癸'], 
  '巳': ['丙', '戊', '庚'],
  '午': ['丁', '己'], 
  '未': ['己', '丁', '乙'], 
  '申': ['庚', '壬', '戊'],
  '酉': ['辛'], 
  '戌': ['戊', '辛', '丁'], 
  '亥': ['壬', '甲']
};

// 十神计算函数
function calculateTenGod(dayGan, otherGan) {
  if (dayGan === otherGan) {
    return '比肩';
  }
  
  const dayIndex = tiangan.indexOf(dayGan);
  const otherIndex = tiangan.indexOf(otherGan);
  
  const dayWuxing = wuxing[dayIndex];
  const otherWuxing = wuxing[otherIndex];
  
  const dayYinYang = dayIndex % 2; // 0=阳, 1=阴
  const otherYinYang = otherIndex % 2;
  
  // 同五行不同阴阳
  if (dayWuxing === otherWuxing) {
    return dayYinYang === otherYinYang ? '比肩' : '劫财';
  }
  
  // 生克关系
  const shengke = {
    '木': { sheng: '火', ke: '土', beisheng: '水', beike: '金' },
    '火': { sheng: '土', ke: '金', beisheng: '木', beike: '水' },
    '土': { sheng: '金', ke: '水', beisheng: '火', beike: '木' },
    '金': { sheng: '水', ke: '木', beisheng: '土', beike: '火' },
    '水': { sheng: '木', ke: '火', beisheng: '金', beike: '土' }
  };
  
  if (shengke[dayWuxing].sheng === otherWuxing) {
    // 日干生他干
    return dayYinYang === otherYinYang ? '食神' : '伤官';
  }
  if (shengke[dayWuxing].ke === otherWuxing) {
    // 日干克他干
    return dayYinYang === otherYinYang ? '偏财' : '正财';
  }
  if (shengke[dayWuxing].beisheng === otherWuxing) {
    // 他干生日干
    return dayYinYang === otherYinYang ? '偏印' : '正印';
  }
  if (shengke[dayWuxing].beike === otherWuxing) {
    // 他干克日干
    return dayYinYang === otherYinYang ? '七杀' : '正官';
  }
  
  return '未知';
}

// 分析主星（天干十神）
function analyzeMainStars() {
  console.log('\n🌟 主星分析（天干十神）:');
  console.log('='.repeat(40));
  
  const positions = ['年柱', '月柱', '日柱', '时柱'];
  const calculatedMainStars = [];
  
  testData.fourPillars.forEach((pillar, index) => {
    let tenGod;
    if (index === 2) { // 日柱
      tenGod = '元男'; // 日主本身
    } else {
      tenGod = calculateTenGod(testData.dayGan, pillar.gan);
    }
    
    calculatedMainStars.push(tenGod);
    
    const expected = wenZhenStandard.mainStars[index];
    const match = tenGod === expected;
    
    console.log(`${positions[index]}: ${pillar.gan} → ${tenGod} ${match ? '✅' : '❌'} (期望: ${expected})`);
  });
  
  return calculatedMainStars;
}

// 分析藏干
function analyzeCangGan() {
  console.log('\n🏺 藏干分析:');
  console.log('='.repeat(40));
  
  const positions = ['year', 'month', 'day', 'hour'];
  const positionNames = ['年柱', '月柱', '日柱', '时柱'];
  
  testData.fourPillars.forEach((pillar, index) => {
    const canggan = cangganMap[pillar.zhi];
    const expectedCanggan = wenZhenStandard.cangGan[positions[index]];
    
    console.log(`\n${positionNames[index]} ${pillar.zhi}:`);
    console.log(`  计算藏干: ${canggan.join(', ')}`);
    console.log(`  期望藏干: ${expectedCanggan.join(', ')}`);
    
    // 检查藏干是否匹配
    const cangganMatch = canggan.length === expectedCanggan.length && 
                        canggan.every((gan, i) => {
                          const expectedGan = expectedCanggan[i].charAt(0); // 取天干部分
                          return gan === expectedGan;
                        });
    
    console.log(`  匹配度: ${cangganMatch ? '✅ 完全匹配' : '⚠️ 需要检查'}`);
  });
}

// 分析副星（藏干十神）
function analyzeDeputyStars() {
  console.log('\n⭐ 副星分析（藏干十神）:');
  console.log('='.repeat(40));
  
  const positions = ['year', 'month', 'day', 'hour'];
  const positionNames = ['年柱', '月柱', '日柱', '时柱'];
  
  testData.fourPillars.forEach((pillar, index) => {
    const canggan = cangganMap[pillar.zhi];
    const calculatedDeputyStars = [];
    
    console.log(`\n${positionNames[index]} ${pillar.zhi}:`);
    
    canggan.forEach(gan => {
      const tenGod = calculateTenGod(testData.dayGan, gan);
      calculatedDeputyStars.push(tenGod);
      console.log(`  ${gan} → ${tenGod}`);
    });
    
    const expectedDeputyStars = wenZhenStandard.deputyStars[positions[index]];
    console.log(`  期望副星: ${expectedDeputyStars.join(', ')}`);
    
    // 检查副星匹配度
    const deputyMatch = calculatedDeputyStars.length === expectedDeputyStars.length &&
                       calculatedDeputyStars.every((star, i) => star === expectedDeputyStars[i]);
    
    console.log(`  匹配度: ${deputyMatch ? '✅ 完全匹配' : '⚠️ 需要检查'}`);
  });
}

// 验证十神计算逻辑
function verifyTenGodLogic() {
  console.log('\n🔍 十神计算逻辑验证:');
  console.log('='.repeat(40));
  
  const dayGan = testData.dayGan; // 癸
  console.log(`日主: ${dayGan} (癸水)`);
  
  // 验证各个天干的十神
  const testCases = [
    { gan: '辛', expected: '偏印', reason: '辛金生癸水，同阴阳' },
    { gan: '甲', expected: '伤官', reason: '癸水生甲木，异阴阳' },
    { gan: '壬', expected: '劫财', reason: '壬癸同水，异阴阳' },
    { gan: '己', expected: '七杀', reason: '己土克癸水，同阴阳' },
    { gan: '丁', expected: '偏财', reason: '癸水克丁火，同阴阳' },
    { gan: '乙', expected: '食神', reason: '癸水生乙木，同阴阳' },
    { gan: '戊', expected: '正官', reason: '戊土克癸水，异阴阳' }
  ];
  
  testCases.forEach(testCase => {
    const calculated = calculateTenGod(dayGan, testCase.gan);
    const match = calculated === testCase.expected;
    
    console.log(`${testCase.gan} → ${calculated} ${match ? '✅' : '❌'} (期望: ${testCase.expected})`);
    console.log(`  理由: ${testCase.reason}`);
    
    if (!match) {
      console.log(`  ⚠️ 计算错误，需要检查算法`);
    }
  });
}

// 执行所有分析
console.log(`\n📋 测试数据: ${testData.fourPillars.map(p => p.gan + p.zhi).join(' ')}`);
console.log(`日主: ${testData.dayGan}`);

const mainStars = analyzeMainStars();
analyzeCangGan();
analyzeDeputyStars();
verifyTenGodLogic();

// 总结
console.log('\n📊 修复总结:');
console.log('='.repeat(40));
console.log('1. 主星（天干十神）计算验证');
console.log('2. 藏干对照"问真八字"标准');
console.log('3. 副星（藏干十神）计算验证');
console.log('4. 十神计算逻辑全面验证');

console.log('\n💡 如果存在不匹配，需要检查:');
console.log('- 十神计算的阴阳判断逻辑');
console.log('- 五行生克关系的实现');
console.log('- 藏干表的准确性');
console.log('- 特殊情况的处理（如日主本身）');
