/* pages/bazi-input/index.wxss */
/* 八字排盘信息输入页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 师父身份信息 */
.master-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.2);
}

.master-avatar {
  width: 120rpx; /* 统一头像尺寸 */
  height: 120rpx; /* 统一头像尺寸 */
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(218, 165, 32, 0.3);
  object-fit: cover; /* 确保图片填充方式一致 */
}

.master-text {
  display: flex;
  flex-direction: column;
}

.master-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
}

.master-subtitle {
  font-size: 24rpx;
  color: #A0522D;
  opacity: 0.8;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 输入区域 */
.input-section, .mode-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-right: 20rpx;
}

.title-tip {
  font-size: 24rpx;
  color: #95a5a6;
  flex: 1;
  min-width: 100%;
  margin-top: 8rpx;
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 15rpx;
  font-weight: 500;
}

/* 日期选择器 */
.date-picker-group {
  display: flex;
  gap: 15rpx;
}

.date-picker {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-display {
  padding: 20rpx 15rpx;
  text-align: center;
  font-size: 28rpx;
  color: #495057;
}

/* 时间选择器 */
.time-picker-group {
  display: flex;
  gap: 15rpx;
}

.time-picker {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

/* 性别选择器 */
.gender-picker {
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.gender-display {
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  font-size: 28rpx;
  color: #495057;
}

.gender-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

/* 地点输入 */
.location-input {
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.location-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #495057;
}

.location-arrow {
  font-size: 24rpx;
  color: #adb5bd;
}

/* 分析模式选择 */
.mode-picker {
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.mode-display {
  padding: 25rpx 20rpx;
  position: relative;
}

.mode-name {
  font-size: 30rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

.mode-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #adb5bd;
}

/* 按钮区域 */
.button-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.help-button {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.start-button {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.start-button[disabled] {
  opacity: 0.6;
}

.button-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 地点输入弹窗 */
.location-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.location-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 500rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}

.modal-close {
  font-size: 32rpx;
  color: #adb5bd;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.location-input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #e9ecef;
}

.modal-button {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 28rpx;
  background: white;
}

.modal-button.cancel {
  color: #6c757d;
  border-right: 1rpx solid #e9ecef;
}

.modal-button.confirm {
  color: #667eea;
  font-weight: 500;
}

/* 底部信息 */
.footer-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.info-text {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.4;
}
