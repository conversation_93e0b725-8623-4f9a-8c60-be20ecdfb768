// utils/precise_solar_terms_engine.js
// 精确节气计算引擎 - 基于权威天文台数据
// 依据：《大小运，流年.txt》技术文档要求

/**
 * 精确节气计算引擎
 * 
 * 功能特点：
 * 1. 基于权威天文台数据（1900-2025年）
 * 2. 分钟级精度节气时间计算
 * 3. 支持大运起运时间精确计算
 * 4. 完全替换简化的月份判断方法
 * 
 * 技术依据：
 * - 《三命通会·卷四》"起运之法"
 * - 现代天文算法精确计算
 * - 权威节气数据验证
 */
class PreciseSolarTermsEngine {
  constructor() {
    this.name = 'PreciseSolarTermsEngine';
    this.version = '1.0.0';
    this.dataSource = '权威天文台计算数据';
    
    // 24节气名称（按顺序）
    this.solarTerms = [
      '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
      '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
      '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
      '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
    ];
    
    // 12个"节"（用于大运计算）
    this.majorTerms = [
      '立春', '惊蛰', '清明', '立夏', '芒种', '小暑',
      '立秋', '白露', '寒露', '立冬', '大雪', '小寒'
    ];
    
    // 数据缓存
    this.cache = new Map();
    
    console.log('🌸 精确节气计算引擎初始化完成');
    console.log(`📊 支持年份范围: 1900-2025年`);
    console.log(`🎯 数据精度: 分钟级`);
  }
  
  /**
   * 获取指定年份的所有节气数据
   * @param {number} year - 年份
   * @returns {Object} 节气数据对象
   */
  getYearSolarTerms(year) {
    // 检查缓存
    if (this.cache.has(year)) {
      return this.cache.get(year);
    }
    
    // 检查年份范围
    if (year < 1900 || year > 2025) {
      throw new Error(`年份 ${year} 超出支持范围 (1900-2025)`);
    }
    
    try {
      // 尝试加载权威节气数据
      if (!this.dataLoader) {
        this.loadAuthoritativeData();
      }

      // 使用dataLoader获取年份数据
      const yearData = this.dataLoader.getYearData(year);
      if (!yearData) {
        throw new Error(`未找到${year}年的节气数据`);
      }

      this.cache.set(year, yearData);
      console.log(`🌸 获取${year}年权威节气数据成功 (${Object.keys(yearData).length}个节气)`);
      return yearData;

    } catch (error) {
      console.error(`❌ 获取${year}年节气数据失败:`, error);
      throw error;
    }
  }

  /**
   * 加载权威节气数据
   */
  loadAuthoritativeData() {
    try {
      // 尝试加载前端就绪版数据
      if (typeof FrontendReadyJieqiData !== 'undefined') {
        this.dataLoader = new FrontendReadyJieqiData();
        this.authoritative_data = {}; // 初始化为空对象，按需加载
        console.log('✅ 成功加载权威节气数据（前端就绪版）');
        return;
      }

      // 尝试require方式加载（Node.js环境）
      try {
        const jieqiModule = require('../权威节气数据_前端就绪版.js');
        this.dataLoader = new jieqiModule.FrontendReadyJieqiData();
        this.authoritative_data = {}; // 初始化为空对象，按需加载
        console.log('✅ 成功加载权威节气数据（Node.js环境）');
        return;
      } catch (requireError) {
        // require失败，继续尝试其他方式
      }

      // 如果都失败了，抛出错误
      throw new Error('权威节气数据未加载，请确保引入权威节气数据_前端就绪版.js');

    } catch (error) {
      console.error('❌ 加载权威节气数据失败:', error);
      throw error;
    }
  }

  /**
   * 计算指定时间的精确节气信息
   * @param {Date} datetime - 日期时间对象
   * @returns {Object} 节气分析结果
   */
  calculateSolarTermInfo(datetime) {
    const year = datetime.getFullYear();
    const yearData = this.getYearSolarTerms(year);
    
    // 转换所有节气为Date对象并排序
    const termDates = [];
    for (const [termName, termData] of Object.entries(yearData)) {
      const termDate = new Date(year, termData.month - 1, termData.day, termData.hour, termData.minute);
      termDates.push({
        name: termName,
        date: termDate,
        data: termData
      });
    }
    
    // 按时间排序
    termDates.sort((a, b) => a.date.getTime() - b.date.getTime());
    
    // 查找前后节气
    let prevTerm = null;
    let nextTerm = null;
    
    for (let i = 0; i < termDates.length; i++) {
      const term = termDates[i];
      if (datetime.getTime() >= term.date.getTime()) {
        prevTerm = term;
      } else {
        nextTerm = term;
        break;
      }
    }
    
    // 处理跨年情况
    if (!prevTerm && termDates.length > 0) {
      // 当前时间在年初，查找上一年最后一个节气
      try {
        const prevYearData = this.getYearSolarTerms(year - 1);
        const prevYearTerms = [];
        for (const [termName, termData] of Object.entries(prevYearData)) {
          const termDate = new Date(year - 1, termData.month - 1, termData.day, termData.hour, termData.minute);
          prevYearTerms.push({ name: termName, date: termDate, data: termData });
        }
        prevYearTerms.sort((a, b) => a.date.getTime() - b.date.getTime());
        prevTerm = prevYearTerms[prevYearTerms.length - 1];
      } catch (error) {
        console.warn('无法获取上一年节气数据:', error);
      }
    }
    
    if (!nextTerm && termDates.length > 0) {
      // 当前时间在年末，查找下一年第一个节气
      try {
        const nextYearData = this.getYearSolarTerms(year + 1);
        const nextYearTerms = [];
        for (const [termName, termData] of Object.entries(nextYearData)) {
          const termDate = new Date(year + 1, termData.month - 1, termData.day, termData.hour, termData.minute);
          nextYearTerms.push({ name: termName, date: termDate, data: termData });
        }
        nextYearTerms.sort((a, b) => a.date.getTime() - b.date.getTime());
        nextTerm = nextYearTerms[0];
      } catch (error) {
        console.warn('无法获取下一年节气数据:', error);
      }
    }
    
    // 计算时间差
    const result = {
      year: year,
      datetime: datetime,
      prevTerm: null,
      nextTerm: null,
      daysSincePrev: 0,
      daysToNext: 0,
      hoursSincePrev: 0,
      hoursToNext: 0,
      description: ''
    };
    
    if (prevTerm) {
      const timeDiff = datetime.getTime() - prevTerm.date.getTime();
      result.prevTerm = prevTerm;
      result.daysSincePrev = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      result.hoursSincePrev = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    }
    
    if (nextTerm) {
      const timeDiff = nextTerm.date.getTime() - datetime.getTime();
      result.nextTerm = nextTerm;
      result.daysToNext = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      result.hoursToNext = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    }
    
    // 生成描述
    if (prevTerm && nextTerm) {
      result.description = `出生于${prevTerm.name}后${result.daysSincePrev}天${result.hoursSincePrev}小时，${nextTerm.name}前${result.daysToNext}天${result.hoursToNext}小时`;
    } else if (prevTerm) {
      result.description = `出生于${prevTerm.name}后${result.daysSincePrev}天${result.hoursSincePrev}小时`;
    } else if (nextTerm) {
      result.description = `${nextTerm.name}前${result.daysToNext}天${result.hoursToNext}小时`;
    }
    
    console.log('🌸 精确节气信息计算完成:', result.description);
    return result;
  }
  
  /**
   * 获取大运起运计算所需的目标节气
   * @param {Date} birthDatetime - 出生时间
   * @param {boolean} isForward - 是否顺行
   * @returns {Object} 目标节气信息
   */
  getTargetTermForDayun(birthDatetime, isForward) {
    const year = birthDatetime.getFullYear();
    const yearData = this.getYearSolarTerms(year);
    
    // 获取所有"节"的时间
    const majorTermDates = [];
    for (const termName of this.majorTerms) {
      if (yearData[termName]) {
        const termData = yearData[termName];
        const termDate = new Date(year, termData.month - 1, termData.day, termData.hour, termData.minute);
        majorTermDates.push({
          name: termName,
          date: termDate,
          data: termData
        });
      }
    }
    
    // 按时间排序
    majorTermDates.sort((a, b) => a.date.getTime() - b.date.getTime());
    
    let targetTerm = null;
    
    if (isForward) {
      // 顺行：找下一个节
      for (const term of majorTermDates) {
        if (term.date.getTime() > birthDatetime.getTime()) {
          targetTerm = term;
          break;
        }
      }
      
      // 如果没找到，查找下一年的第一个节
      if (!targetTerm) {
        try {
          const nextYearData = this.getYearSolarTerms(year + 1);
          for (const termName of this.majorTerms) {
            if (nextYearData[termName]) {
              const termData = nextYearData[termName];
              const termDate = new Date(year + 1, termData.month - 1, termData.day, termData.hour, termData.minute);
              targetTerm = { name: termName, date: termDate, data: termData };
              break;
            }
          }
        } catch (error) {
          console.warn('无法获取下一年节气数据:', error);
        }
      }
    } else {
      // 逆行：找上一个节
      for (let i = majorTermDates.length - 1; i >= 0; i--) {
        const term = majorTermDates[i];
        if (term.date.getTime() < birthDatetime.getTime()) {
          targetTerm = term;
          break;
        }
      }
      
      // 如果没找到，查找上一年的最后一个节
      if (!targetTerm) {
        try {
          const prevYearData = this.getYearSolarTerms(year - 1);
          for (let i = this.majorTerms.length - 1; i >= 0; i--) {
            const termName = this.majorTerms[i];
            if (prevYearData[termName]) {
              const termData = prevYearData[termName];
              const termDate = new Date(year - 1, termData.month - 1, termData.day, termData.hour, termData.minute);
              targetTerm = { name: termName, date: termDate, data: termData };
              break;
            }
          }
        } catch (error) {
          console.warn('无法获取上一年节气数据:', error);
        }
      }
    }
    
    if (!targetTerm) {
      throw new Error('无法找到目标节气');
    }
    
    console.log(`🎯 大运目标节气: ${targetTerm.name} (${isForward ? '顺行' : '逆行'})`);
    return targetTerm;
  }
  
  /**
   * 检查引擎状态
   * @returns {Object} 状态信息
   */
  getEngineStatus() {
    const hasAuthData = typeof getAuthoritativeJieqiData === 'function';
    
    return {
      name: this.name,
      version: this.version,
      dataSource: this.dataSource,
      authoritativeDataLoaded: hasAuthData,
      cacheSize: this.cache.size,
      supportedYears: '1900-2025',
      precision: '分钟级',
      status: hasAuthData ? 'ready' : 'missing_data'
    };
  }
}

// 导出引擎
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PreciseSolarTermsEngine;
} else if (typeof window !== 'undefined') {
  window.PreciseSolarTermsEngine = PreciseSolarTermsEngine;
}

console.log('🌸 精确节气计算引擎模块加载完成');
