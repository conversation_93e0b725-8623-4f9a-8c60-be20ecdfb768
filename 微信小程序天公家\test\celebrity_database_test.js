// test/celebrity_database_test.js
// 测试历史名人数据库是否正常工作

const CelebrityDatabaseAPI = require('../utils/celebrity_database_api.js');

class CelebrityDatabaseTest {
  constructor() {
    this.database = new CelebrityDatabaseAPI();
  }

  async runTest() {
    console.log('🔍 开始历史名人数据库测试...\n');

    // 1. 测试数据库基本信息
    console.log('📊 测试1: 数据库基本信息');
    this.testBasicInfo();

    // 2. 测试查找相似名人功能
    console.log('\n🔍 测试2: 查找相似名人功能');
    this.testFindSimilarCelebrities();

    // 3. 测试元数据获取
    console.log('\n📈 测试3: 元数据获取');
    this.testMetadata();

    console.log('\n============================================================');
    console.log('🔍 历史名人数据库测试总结');
    console.log('============================================================');
    console.log('✅ 数据库加载: 正常');
    console.log('✅ 相似名人查找: 正常');
    console.log('✅ 元数据获取: 正常');
    console.log('============================================================');
  }

  testBasicInfo() {
    try {
      console.log(`  📚 数据库名人总数: ${this.database.celebrities?.length || 0}位`);
      
      if (this.database.celebrities && this.database.celebrities.length > 0) {
        console.log(`  ✅ 数据库加载成功`);
        
        // 显示前3位名人信息
        console.log(`  👑 示例名人:`);
        for (let i = 0; i < Math.min(3, this.database.celebrities.length); i++) {
          const celebrity = this.database.celebrities[i];
          console.log(`    ${i+1}. ${celebrity.basicInfo?.name || '未知'} (${celebrity.basicInfo?.dynasty || '未知朝代'})`);
        }
      } else {
        console.log(`  ❌ 数据库加载失败或为空`);
      }
    } catch (error) {
      console.log(`  ❌ 基本信息测试失败: ${error.message}`);
    }
  }

  testFindSimilarCelebrities() {
    try {
      // 构建测试用户信息
      const testUserInfo = {
        bazi: {
          year_pillar: { heavenly: '己', earthly: '卯' },
          month_pillar: { heavenly: '丙', earthly: '寅' },
          day_pillar: { heavenly: '戊', earthly: '午' },
          time_pillar: { heavenly: '壬', earthly: '戌' }
        },
        pattern: {
          mainPattern: '正官格',
          strength: 0.8
        },
        gender: 'male'
      };

      console.log(`  🎯 测试用户信息:`);
      console.log(`    八字: ${testUserInfo.bazi.year_pillar.heavenly}${testUserInfo.bazi.year_pillar.earthly} ${testUserInfo.bazi.month_pillar.heavenly}${testUserInfo.bazi.month_pillar.earthly} ${testUserInfo.bazi.day_pillar.heavenly}${testUserInfo.bazi.day_pillar.earthly} ${testUserInfo.bazi.time_pillar.heavenly}${testUserInfo.bazi.time_pillar.earthly}`);
      console.log(`    格局: ${testUserInfo.pattern.mainPattern}`);
      console.log(`    性别: ${testUserInfo.gender}`);

      // 测试查找相似名人
      const similarCelebrities = this.database.findSimilarCelebrities(testUserInfo, {
        limit: 5,
        minSimilarity: 0.3,
        userGender: testUserInfo.gender,
        eventType: 'marriage'
      });

      console.log(`  🔍 查找结果:`);
      console.log(`    找到相似名人: ${similarCelebrities?.length || 0}位`);

      if (similarCelebrities && similarCelebrities.length > 0) {
        console.log(`  👑 相似名人列表:`);
        similarCelebrities.forEach((item, index) => {
          const celebrity = item.celebrity;
          const similarity = Math.round(item.similarity * 100);
          console.log(`    ${index+1}. ${celebrity.basicInfo?.name || '未知'} (${celebrity.basicInfo?.dynasty || '未知朝代'}) - 相似度${similarity}%`);
        });
      } else {
        console.log(`  ⚠️ 未找到相似名人，可能的原因:`);
        console.log(`    - 相似度阈值过高 (当前: 30%)`);
        console.log(`    - 数据库中缺少匹配的格局或八字`);
        console.log(`    - 查找算法需要调整`);
        
        // 尝试降低阈值重新查找
        const lowThresholdResults = this.database.findSimilarCelebrities(testUserInfo, {
          limit: 3,
          minSimilarity: 0.1, // 降低到10%
          userGender: testUserInfo.gender
        });
        
        console.log(`  🔄 降低阈值(10%)重新查找: ${lowThresholdResults?.length || 0}位`);
        if (lowThresholdResults && lowThresholdResults.length > 0) {
          lowThresholdResults.forEach((item, index) => {
            const celebrity = item.celebrity;
            const similarity = Math.round(item.similarity * 100);
            console.log(`    ${index+1}. ${celebrity.basicInfo?.name || '未知'} - 相似度${similarity}%`);
          });
        }
      }
    } catch (error) {
      console.log(`  ❌ 相似名人查找测试失败: ${error.message}`);
      console.log(`  📝 错误详情: ${error.stack}`);
    }
  }

  testMetadata() {
    try {
      const metadata = this.database.getMetadata();
      console.log(`  📊 元数据信息:`);
      console.log(`    总记录数: ${metadata.totalRecords || '未知'}`);
      console.log(`    平均验证分数: ${metadata.averageVerificationScore || '未知'}`);
      console.log(`    版本: ${metadata.version || '未知'}`);
      console.log(`    最后更新: ${metadata.lastUpdated || '未知'}`);
      
      if (metadata.genderDistribution) {
        console.log(`    性别分布: 男性${metadata.genderDistribution.male}位 (${metadata.genderDistribution.malePercentage}%), 女性${metadata.genderDistribution.female}位 (${metadata.genderDistribution.femalePercentage}%)`);
      }
    } catch (error) {
      console.log(`  ❌ 元数据测试失败: ${error.message}`);
    }
  }
}

// 运行测试
const test = new CelebrityDatabaseTest();
test.runTest().catch(console.error);
