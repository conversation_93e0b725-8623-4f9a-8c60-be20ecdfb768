<!--pages/divination-input/index.wxml-->
<!-- 🔮 李淳风六壬时课专业系统 - 独立占卜界面 -->
<!-- 用途：专业占卜工具界面，纯功能导向 -->
<!-- 特点：专业精准，独立部署，算法验证 -->
<scroll-view class="scroll-container" scroll-y="{{true}}" enhanced="{{false}}" show-scrollbar="{{false}}" enable-flex="{{true}}" scroll-with-animation="{{false}}" paging-enabled="{{false}}" fast-deceleration="{{false}}">
  <view class="container {{themeClass}}">
    <!-- 背景装饰 -->
    <view class="background-decoration"></view>
  
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="title-container">
      <image class="master-avatar" src="/assets/icons/tiangong-master.svg"></image>
    </view>
    <view class="description">
      <text>{{master}}\n无事不占，一事一占</text>
    </view>
  </view>

  <!-- 占卜方式选择 -->
  <view class="method-selection">
    <view class="section-title">
      <text>请选择占卜方式</text>
    </view>
    
    <view class="method-options">
      <view class="method-card {{selectedMethod === 'time' ? 'selected' : ''}}" 
            bindtap="selectMethod" data-method="time">
        <view class="method-icon">⏰</view>
        <view class="method-info">
          <text class="method-name">按时间占卜</text>
          <text class="method-desc">传统月日时起卦法</text>
        </view>
        <view class="method-badge">推荐</view>
      </view>
      
      <view class="method-card {{selectedMethod === 'number' ? 'selected' : ''}}" 
            bindtap="selectMethod" data-method="number">
        <view class="method-icon">🔢</view>
        <view class="method-info">
          <text class="method-name">按数字占卜</text>
          <text class="method-desc">数字取数变通法</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 问题输入区域 -->
  <view class="question-section">
    <view class="section-title">
      <text>请详细描述您的问题</text>
    </view>
    
    <view class="question-types">
      <view class="type-tag {{selectedType === item.value ? 'selected' : ''}}"
            wx:for="{{questionTypes}}" wx:key="value"
            bindtap="selectQuestionType" data-type="{{item.value}}">
        <text>{{item.label}}</text>
      </view>
    </view>
    
    <textarea class="question-input" 
              placeholder="请详细描述您遇到的问题或困惑..."
              value="{{questionText}}"
              bindinput="onQuestionInput"
              maxlength="200"
              show-confirm-bar="{{false}}">
    </textarea>
    <view class="input-counter">
      <text>{{questionText.length}}/200</text>
    </view>
  </view>

  <!-- 数字输入区域（仅数字占卜时显示） -->
  <view class="number-section" wx:if="{{selectedMethod === 'number'}}">
    <view class="section-title">
      <text>请输入三个数字（1-9）</text>
    </view>
    <view class="number-inputs">
      <input class="number-input"
             type="number"
             placeholder="第一个数字"
             value="{{numbers[0]}}"
             bindinput="onNumberInput"
             data-index="0"
             maxlength="1" />
      <input class="number-input"
             type="number"
             placeholder="第二个数字"
             value="{{numbers[1]}}"
             bindinput="onNumberInput"
             data-index="1"
             maxlength="1" />
      <input class="number-input"
             type="number"
             placeholder="第三个数字"
             value="{{numbers[2]}}"
             bindinput="onNumberInput"
             data-index="2"
             maxlength="1" />
    </view>
    <view class="number-hint">
      <text>💡 可根据直觉选择，或使用生日、幸运数字等</text>
    </view>
  </view>

  <!-- 事件发生时间选择区域（仅时间占卜时显示） -->
  <view class="time-section" wx:if="{{selectedMethod === 'time'}}">
    <view class="section-title">
      <text>事件发生时间</text>
    </view>

    <!-- 时间选择器 -->
    <view class="time-picker-section">
      <view class="picker-item">
        <text class="picker-label">发生日期：</text>
        <picker mode="date"
                value="{{eventDate}}"
                bindchange="onEventDateChange"
                class="picker-input">
          <view class="picker-display">
            {{eventDate || '请选择日期'}}
          </view>
        </picker>
      </view>

      <view class="picker-item">
        <text class="picker-label">发生时间：</text>
        <picker mode="time"
                value="{{eventTime}}"
                bindchange="onEventTimeChange"
                class="picker-input">
          <view class="picker-display">
            {{eventTime || '请选择时间'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 时间选择提示 -->
    <view class="time-hint">
      <text>💡 请选择所问事情发生的时间</text>
    </view>

    <!-- 计算后的时间信息 -->
    <view class="time-info" wx:if="{{eventDateTime}}">
      <view class="time-item">
        <text class="time-label">发生时间：</text>
        <text class="time-value">{{eventDateTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">真太阳时：</text>
        <text class="time-value">{{eventTrueSolarTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">农历时间：</text>
        <text class="time-value">{{eventLunarTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">发生时辰：</text>
        <text class="time-value">{{eventShichen}}</text>
      </view>
    </view>

    <view class="location-info" wx:if="{{locationInfo}}">
      <text class="location-text">📍 {{locationInfo}}</text>
    </view>

    <view class="time-note">
      <text>⚠️ 李淳风六壬时课使用农历时间和真太阳时计算</text>
    </view>

    <!-- GPS授权说明 -->
    <view class="gps-notice">
      <view class="notice-icon">📍</view>
      <view class="notice-content">
        <text class="notice-title">为什么需要位置授权？</text>
        <text class="notice-desc">传统占卜需要精确的真太阳时，根据您的地理位置校正时间偏差，确保占卜结果准确。</text>
        <text class="notice-formula">计算公式：真太阳时 = 北京时间 + 经度差</text>
      </view>
    </view>
  </view>

  <!-- API功能选择区域 -->
  <view class="api-section" wx:if="{{canStartDivination}}">
    <view class="section-title">
      <text>占卜方式增强</text>
    </view>

    <view class="api-options">
      <view class="api-option {{useApiEnhancement ? 'selected' : ''}}"
            bindtap="toggleApiEnhancement">
        <view class="option-icon">🤖</view>
        <view class="option-info">
          <text class="option-name">AI智能增强</text>
          <text class="option-desc">结合传统占卜与现代AI分析</text>
        </view>
        <view class="option-toggle">
          <text>{{useApiEnhancement ? '已启用' : '点击启用'}}</text>
        </view>
      </view>

      <view class="api-option {{!useApiEnhancement ? 'selected' : ''}}"
            bindtap="toggleApiEnhancement">
        <view class="option-icon">📜</view>
        <view class="option-info">
          <text class="option-name">传统占卜</text>
          <text class="option-desc">纯正古法，李淳风六壬时课</text>
        </view>
        <view class="option-toggle">
          <text>{{!useApiEnhancement ? '已选择' : '传统方式'}}</text>
        </view>
      </view>
    </view>

    <view class="api-notice" wx:if="{{useApiEnhancement}}">
      <text class="notice-text">💡 AI增强模式将结合《玉匣记》等古籍数据库，提供更详细的占卜解析</text>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="action-section">
    <view class="divination-rules">
      <text>🔮 无事不占，一事一占，诚心为要</text>
    </view>

    <button class="start-divination-btn {{canStartDivination ? 'active' : 'disabled'}}"
            bindtap="startDivination"
            disabled="{{!canStartDivination}}">
      <text>{{useApiEnhancement ? 'AI增强占卜' : '开始占卜'}}</text>
      <view class="btn-decoration"></view>
    </button>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-overlay" wx:if="{{isCalculating}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">天公师兄正在为您推演...</text>
    </view>
  </view>
  </view>
</scroll-view>
