/**
 * 专业级动态交互分析引擎
 * 基于《五行计算.txt》文档标准，实现合会冲刑关系计算和动态力量调整
 * 
 * 核心功能：
 * 1. 地支三会方检测 (寅卯辰会木等)
 * 2. 地支三合局检测 (申子辰合水等)
 * 3. 地支六合检测 (子丑合土等)
 * 4. 天干五合检测 (甲己合土等)
 * 5. 地支六冲检测 (子午冲等)
 * 6. 地支三刑检测 (子卯刑等)
 * 7. 动态力量调整算法
 */

class DynamicInteractionEngine {
  constructor() {
    this.initializeRelationshipTables();
    this.initializeAdjustmentCoefficients();
  }

  /**
   * 初始化关系映射表
   */
  initializeRelationshipTables() {
    // 地支三会方 (方位会局，力量最强)
    this.THREE_DIRECTIONAL_COMBINATIONS = {
      '木': ['寅', '卯', '辰'],  // 东方木局
      '火': ['巳', '午', '未'],  // 南方火局
      '金': ['申', '酉', '戌'],  // 西方金局
      '水': ['亥', '子', '丑']   // 北方水局
    };

    // 地支三合局 (生旺墓三合)
    this.THREE_HARMONY_COMBINATIONS = {
      '木': ['亥', '卯', '未'],  // 亥卯未合木
      '火': ['寅', '午', '戌'],  // 寅午戌合火
      '金': ['巳', '酉', '丑'],  // 巳酉丑合金
      '水': ['申', '子', '辰']   // 申子辰合水
    };

    // 地支六合 (阴阳相合)
    this.SIX_COMBINATIONS = {
      '子': { partner: '丑', element: '土' },  // 子丑合土
      '丑': { partner: '子', element: '土' },
      '寅': { partner: '亥', element: '木' },  // 寅亥合木
      '亥': { partner: '寅', element: '木' },
      '卯': { partner: '戌', element: '火' },  // 卯戌合火
      '戌': { partner: '卯', element: '火' },
      '辰': { partner: '酉', element: '金' },  // 辰酉合金
      '酉': { partner: '辰', element: '金' },
      '巳': { partner: '申', element: '水' },  // 巳申合水
      '申': { partner: '巳', element: '水' },
      '午': { partner: '未', element: '土' },  // 午未合土
      '未': { partner: '午', element: '土' }
    };

    // 天干五合
    this.FIVE_COMBINATIONS = {
      '甲': { partner: '己', element: '土' },  // 甲己合土
      '己': { partner: '甲', element: '土' },
      '乙': { partner: '庚', element: '金' },  // 乙庚合金
      '庚': { partner: '乙', element: '金' },
      '丙': { partner: '辛', element: '水' },  // 丙辛合水
      '辛': { partner: '丙', element: '水' },
      '丁': { partner: '壬', element: '木' },  // 丁壬合木
      '壬': { partner: '丁', element: '木' },
      '戊': { partner: '癸', element: '火' },  // 戊癸合火
      '癸': { partner: '戊', element: '火' }
    };

    // 地支六冲 (对冲关系)
    this.SIX_CLASHES = {
      '子': '午', '午': '子',  // 子午冲
      '丑': '未', '未': '丑',  // 丑未冲
      '寅': '申', '申': '寅',  // 寅申冲
      '卯': '酉', '酉': '卯',  // 卯酉冲
      '辰': '戌', '戌': '辰',  // 辰戌冲
      '巳': '亥', '亥': '巳'   // 巳亥冲
    };

    // 地支三刑 (刑害关系)
    this.THREE_PUNISHMENTS = {
      // 无恩之刑
      '子': ['卯'], '卯': ['子'],
      // 无礼之刑
      '寅': ['巳'], '巳': ['申'], '申': ['寅'],
      // 恃势之刑
      '丑': ['戌'], '戌': ['未'], '未': ['丑'],
      // 自刑
      '辰': ['辰'], '午': ['午'], '酉': ['酉'], '亥': ['亥']
    };

    // 五行对应表
    this.ELEMENT_MAP = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
      '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
      '戌': '土', '亥': '水'
    };
  }

  /**
   * 初始化调整系数
   */
  initializeAdjustmentCoefficients() {
    // 基于《五行计算.txt》文档标准的调整系数
    this.ADJUSTMENT_COEFFICIENTS = {
      // 三会方 - 力量最强，能改变全局五行格局
      threeDirectional: {
        boost: 3.0,        // 会局五行力量提升3倍
        suppress: 0.2      // 参与地支其他藏干力量削弱至20%
      },
      
      // 三合局 - 力量仅次于三会
      threeHarmony: {
        boost: 2.5,        // 合局五行力量提升2.5倍
        suppress: 0.3      // 参与地支其他藏干力量削弱至30%
      },
      
      // 六合/五合 - 合绊效应
      combinations: {
        boost: 1.2,        // 合化成功时提升20%
        suppress: 0.8      // 合绊时双方力量削弱至80%
      },
      
      // 六冲 - 两败俱伤
      clashes: {
        suppress: 0.6,     // 相冲双方力量削弱至60%
        nearClash: 0.4,    // 近冲(月日、日时)削弱至40%
        farClash: 0.7      // 远冲(年时)削弱至70%
      },
      
      // 三刑 - 刑伤效应
      punishments: {
        suppress: 0.7      // 相刑双方力量削弱至70%
      }
    };
  }

  /**
   * 主要分析方法：检测所有动态交互关系
   * @param {Array} fourPillars - 四柱八字
   * @returns {Object} 动态交互分析结果
   */
  analyzeAllInteractions(fourPillars) {
    console.log('🔄 开始动态交互分析...');
    console.log('📋 输入四柱:', fourPillars.map(p => p.gan + p.zhi).join(' '));

    const interactions = {
      threeDirectional: this.detectThreeDirectionalCombinations(fourPillars),
      threeHarmony: this.detectThreeHarmonyCombinations(fourPillars),
      sixCombinations: this.detectSixCombinations(fourPillars),
      fiveCombinations: this.detectFiveCombinations(fourPillars),
      sixClashes: this.detectSixClashes(fourPillars),
      threePunishments: this.detectThreePunishments(fourPillars)
    };

    console.log('📊 检测到的交互关系:');
    Object.entries(interactions).forEach(([type, relations]) => {
      if (relations.length > 0) {
        console.log(`  ${type}: ${relations.length}个`);
        relations.forEach(relation => {
          console.log(`    ${relation.description}`);
        });
      }
    });

    return interactions;
  }

  /**
   * 检测地支三会方
   */
  detectThreeDirectionalCombinations(fourPillars) {
    const zhis = fourPillars.map(p => p.zhi);
    const results = [];

    Object.entries(this.THREE_DIRECTIONAL_COMBINATIONS).forEach(([element, pattern]) => {
      const foundZhis = pattern.filter(zhi => zhis.includes(zhi));
      if (foundZhis.length === 3) {
        results.push({
          type: 'threeDirectional',
          element: element,
          pattern: pattern,
          foundZhis: foundZhis,
          positions: foundZhis.map(zhi => zhis.indexOf(zhi)),
          description: `${pattern.join('')}会${element}局`,
          strength: 'strongest'
        });
      }
    });

    return results;
  }

  /**
   * 检测地支三合局
   */
  detectThreeHarmonyCombinations(fourPillars) {
    const zhis = fourPillars.map(p => p.zhi);
    const results = [];

    Object.entries(this.THREE_HARMONY_COMBINATIONS).forEach(([element, pattern]) => {
      const foundZhis = pattern.filter(zhi => zhis.includes(zhi));
      if (foundZhis.length === 3) {
        results.push({
          type: 'threeHarmony',
          element: element,
          pattern: pattern,
          foundZhis: foundZhis,
          positions: foundZhis.map(zhi => zhis.indexOf(zhi)),
          description: `${pattern.join('')}合${element}局`,
          strength: 'strong'
        });
      }
    });

    return results;
  }

  /**
   * 检测地支六合
   */
  detectSixCombinations(fourPillars) {
    const zhis = fourPillars.map(p => p.zhi);
    const results = [];

    for (let i = 0; i < zhis.length; i++) {
      for (let j = i + 1; j < zhis.length; j++) {
        const zhi1 = zhis[i];
        const zhi2 = zhis[j];
        const combo1 = this.SIX_COMBINATIONS[zhi1];
        
        if (combo1 && combo1.partner === zhi2) {
          results.push({
            type: 'sixCombination',
            element: combo1.element,
            pair: [zhi1, zhi2],
            positions: [i, j],
            description: `${zhi1}${zhi2}合${combo1.element}`,
            strength: 'medium'
          });
        }
      }
    }

    return results;
  }

  /**
   * 检测天干五合
   */
  detectFiveCombinations(fourPillars) {
    const gans = fourPillars.map(p => p.gan);
    const results = [];

    for (let i = 0; i < gans.length; i++) {
      for (let j = i + 1; j < gans.length; j++) {
        const gan1 = gans[i];
        const gan2 = gans[j];
        const combo1 = this.FIVE_COMBINATIONS[gan1];
        
        if (combo1 && combo1.partner === gan2) {
          results.push({
            type: 'fiveCombination',
            element: combo1.element,
            pair: [gan1, gan2],
            positions: [i, j],
            description: `${gan1}${gan2}合${combo1.element}`,
            strength: 'medium'
          });
        }
      }
    }

    return results;
  }

  /**
   * 检测地支六冲
   */
  detectSixClashes(fourPillars) {
    const zhis = fourPillars.map(p => p.zhi);
    const results = [];

    for (let i = 0; i < zhis.length; i++) {
      for (let j = i + 1; j < zhis.length; j++) {
        const zhi1 = zhis[i];
        const zhi2 = zhis[j];
        
        if (this.SIX_CLASHES[zhi1] === zhi2) {
          const distance = Math.abs(i - j);
          const clashType = distance === 1 ? 'near' : 'far';
          
          results.push({
            type: 'sixClash',
            pair: [zhi1, zhi2],
            positions: [i, j],
            distance: distance,
            clashType: clashType,
            description: `${zhi1}${zhi2}冲 (${clashType === 'near' ? '近冲' : '远冲'})`,
            strength: 'destructive'
          });
        }
      }
    }

    return results;
  }

  /**
   * 检测地支三刑
   */
  detectThreePunishments(fourPillars) {
    const zhis = fourPillars.map(p => p.zhi);
    const results = [];

    for (let i = 0; i < zhis.length; i++) {
      for (let j = i + 1; j < zhis.length; j++) {
        const zhi1 = zhis[i];
        const zhi2 = zhis[j];
        const punishments1 = this.THREE_PUNISHMENTS[zhi1];
        
        if (punishments1 && punishments1.includes(zhi2)) {
          results.push({
            type: 'threePunishment',
            pair: [zhi1, zhi2],
            positions: [i, j],
            description: `${zhi1}${zhi2}刑`,
            strength: 'harmful'
          });
        }
      }
    }

    return results;
  }

  /**
   * 应用动态力量调整
   * @param {Object} staticPowers - 静态五行力量
   * @param {Object} interactions - 动态交互关系
   * @returns {Object} 调整后的五行力量
   */
  applyDynamicAdjustments(staticPowers, interactions) {
    console.log('\n🔧 应用动态力量调整...');
    console.log('📊 调整前静态力量:', staticPowers);
    console.log('🔍 检测到的交互关系:', {
      三会方: interactions.threeDirectional.length,
      三合局: interactions.threeHarmony.length,
      六合: interactions.sixCombinations.length,
      五合: interactions.fiveCombinations.length,
      六冲: interactions.sixClashes.length,
      三刑: interactions.threePunishments.length
    });

    // 深拷贝静态力量作为基础
    const adjustedPowers = JSON.parse(JSON.stringify(staticPowers));
    const adjustmentLog = [];

    // 按优先级应用调整：会 > 合 > 冲 > 刑
    
    // 1. 三会方调整 (最高优先级)
    interactions.threeDirectional.forEach(combo => {
      const element = this.getElementKey(combo.element);
      const originalPower = adjustedPowers[element];
      adjustedPowers[element] *= this.ADJUSTMENT_COEFFICIENTS.threeDirectional.boost;
      
      adjustmentLog.push({
        type: '三会方',
        description: combo.description,
        element: combo.element,
        adjustment: `${originalPower.toFixed(1)} → ${adjustedPowers[element].toFixed(1)} (×${this.ADJUSTMENT_COEFFICIENTS.threeDirectional.boost})`
      });
    });

    // 2. 三合局调整
    interactions.threeHarmony.forEach(combo => {
      const element = this.getElementKey(combo.element);
      const originalPower = adjustedPowers[element];
      adjustedPowers[element] *= this.ADJUSTMENT_COEFFICIENTS.threeHarmony.boost;
      
      adjustmentLog.push({
        type: '三合局',
        description: combo.description,
        element: combo.element,
        adjustment: `${originalPower.toFixed(1)} → ${adjustedPowers[element].toFixed(1)} (×${this.ADJUSTMENT_COEFFICIENTS.threeHarmony.boost})`
      });
    });

    // 3. 六合调整 (简化为合绊处理)
    interactions.sixCombinations.forEach(combo => {
      combo.pair.forEach(zhi => {
        const element = this.getElementKey(this.ELEMENT_MAP[zhi]);
        const originalPower = adjustedPowers[element];
        adjustedPowers[element] *= this.ADJUSTMENT_COEFFICIENTS.combinations.suppress;
        
        adjustmentLog.push({
          type: '六合合绊',
          description: combo.description,
          element: this.ELEMENT_MAP[zhi],
          adjustment: `${originalPower.toFixed(1)} → ${adjustedPowers[element].toFixed(1)} (×${this.ADJUSTMENT_COEFFICIENTS.combinations.suppress})`
        });
      });
    });

    // 4. 六冲调整
    interactions.sixClashes.forEach(clash => {
      const coefficient = clash.clashType === 'near' 
        ? this.ADJUSTMENT_COEFFICIENTS.clashes.nearClash
        : this.ADJUSTMENT_COEFFICIENTS.clashes.farClash;
      
      clash.pair.forEach(zhi => {
        const element = this.getElementKey(this.ELEMENT_MAP[zhi]);
        const originalPower = adjustedPowers[element];
        adjustedPowers[element] *= coefficient;
        
        adjustmentLog.push({
          type: '六冲',
          description: clash.description,
          element: this.ELEMENT_MAP[zhi],
          adjustment: `${originalPower.toFixed(1)} → ${adjustedPowers[element].toFixed(1)} (×${coefficient})`
        });
      });
    });

    // 5. 三刑调整
    interactions.threePunishments.forEach(punishment => {
      punishment.pair.forEach(zhi => {
        const element = this.getElementKey(this.ELEMENT_MAP[zhi]);
        const originalPower = adjustedPowers[element];
        adjustedPowers[element] *= this.ADJUSTMENT_COEFFICIENTS.punishments.suppress;
        
        adjustmentLog.push({
          type: '三刑',
          description: punishment.description,
          element: this.ELEMENT_MAP[zhi],
          adjustment: `${originalPower.toFixed(1)} → ${adjustedPowers[element].toFixed(1)} (×${this.ADJUSTMENT_COEFFICIENTS.punishments.suppress})`
        });
      });
    });

    console.log('📋 动态调整记录:');
    adjustmentLog.forEach(log => {
      console.log(`  ${log.type}: ${log.description} - ${log.element} ${log.adjustment}`);
    });

    console.log('📊 调整后动态力量:', adjustedPowers);

    // 🔧 计算并显示变化总结
    const changesSummary = Object.keys(staticPowers).map(element => {
      const staticValue = staticPowers[element];
      const dynamicValue = adjustedPowers[element];
      const change = dynamicValue - staticValue;
      const changePercent = staticValue > 0 ? (change / staticValue * 100) : 0;
      return {
        element,
        static: staticValue,
        dynamic: dynamicValue,
        change: change,
        changePercent: changePercent.toFixed(1)
      };
    });

    console.log('📈 力量变化总结:');
    changesSummary.forEach(summary => {
      if (Math.abs(summary.change) > 0.1) {
        console.log(`  ${summary.element}: ${summary.static.toFixed(1)} → ${summary.dynamic.toFixed(1)} (${summary.changePercent > 0 ? '+' : ''}${summary.changePercent}%)`);
      }
    });

    const hasSignificantChanges = changesSummary.some(s => Math.abs(s.change) > 0.1);
    console.log(`🎯 动态调整结果: ${hasSignificantChanges ? '发现显著变化' : '无显著变化'}`);

    return {
      adjustedPowers,
      adjustmentLog,
      totalAdjustments: adjustmentLog.length,
      changesSummary,
      hasSignificantChanges
    };
  }

  /**
   * 辅助方法：获取五行键名
   */
  getElementKey(element) {
    const elementKeyMap = {
      '木': '木', '火': '火', '土': '土', '金': '金', '水': '水'
    };
    return elementKeyMap[element] || element;
  }

  /**
   * 生成动态交互分析报告
   */
  generateInteractionReport(fourPillars, staticPowers) {
    const interactions = this.analyzeAllInteractions(fourPillars);
    const dynamicResult = this.applyDynamicAdjustments(staticPowers, interactions);

    return {
      algorithm: '专业级动态交互分析引擎',
      version: 'V1.0 - 基于《五行计算.txt》文档标准',
      inputData: {
        fourPillars: fourPillars.map(p => p.gan + p.zhi).join(' '),
        staticPowers: staticPowers
      },
      interactions: interactions,
      dynamicAdjustment: dynamicResult,
      summary: {
        totalInteractions: Object.values(interactions).reduce((sum, arr) => sum + arr.length, 0),
        strongestInteraction: this.findStrongestInteraction(interactions),
        finalPowers: dynamicResult.adjustedPowers,
        powerChangeRate: this.calculatePowerChangeRate(staticPowers, dynamicResult.adjustedPowers)
      }
    };
  }

  /**
   * 找到最强的交互关系
   */
  findStrongestInteraction(interactions) {
    if (interactions.threeDirectional.length > 0) {
      return { type: '三会方', detail: interactions.threeDirectional[0] };
    }
    if (interactions.threeHarmony.length > 0) {
      return { type: '三合局', detail: interactions.threeHarmony[0] };
    }
    if (interactions.sixClashes.length > 0) {
      return { type: '六冲', detail: interactions.sixClashes[0] };
    }
    if (interactions.threePunishments.length > 0) {
      return { type: '三刑', detail: interactions.threePunishments[0] };
    }
    if (interactions.sixCombinations.length > 0) {
      return { type: '六合', detail: interactions.sixCombinations[0] };
    }
    return { type: '无显著交互', detail: null };
  }

  /**
   * 计算力量变化率
   */
  calculatePowerChangeRate(staticPowers, dynamicPowers) {
    const changes = {};
    Object.keys(staticPowers).forEach(element => {
      const staticValue = staticPowers[element];
      const dynamicValue = dynamicPowers[element];
      const changeRate = staticValue > 0 ? ((dynamicValue - staticValue) / staticValue * 100) : 0;
      changes[element] = {
        static: staticValue,
        dynamic: dynamicValue,
        changeRate: changeRate.toFixed(1) + '%'
      };
    });
    return changes;
  }
}

// 导出模块
module.exports = DynamicInteractionEngine;
