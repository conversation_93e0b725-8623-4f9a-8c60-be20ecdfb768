/**
 * 🧪 调试历史名人库集成问题
 * 详细检查数据结构和匹配过程
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  request: () => {}
};

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');
const CelebrityDatabaseAPI = require('../utils/celebrity_database_api.js');

async function debugCelebrityIntegration() {
  console.log('🔍 开始调试历史名人库集成问题...\n');

  try {
    // 1. 直接测试CelebrityDatabaseAPI
    console.log('📊 步骤1: 直接测试CelebrityDatabaseAPI');
    const celebrityDB = new CelebrityDatabaseAPI();
    
    const testUserInfo = {
      bazi: {
        yearPillar: { gan: '甲', zhi: '子' },
        monthPillar: { gan: '丙', zhi: '寅' },
        dayPillar: { gan: '戊', zhi: '午' },
        timePillar: { gan: '庚', zhi: '申' }
      },
      pattern: {
        mainPattern: '正财格',
        yongshen: '木',
        dayMaster: '戊土'
      }
    };

    const directResults = celebrityDB.findSimilarCelebrities(testUserInfo, {
      limit: 3,
      minSimilarity: 0.1,
      userGender: 'male'
    });

    console.log(`✅ 直接调用结果: 找到${directResults.length}位相似名人`);
    if (directResults.length > 0) {
      directResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.celebrity.basicInfo.name} - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
      });
    }

    // 2. 测试ProfessionalTimingEngine的buildUserBaziInfo方法
    console.log('\n📊 步骤2: 测试ProfessionalTimingEngine的buildUserBaziInfo方法');
    const timingEngine = new ProfessionalTimingEngine();
    
    const testBazi = {
      year: { gan: '甲', zhi: '子' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '午' },
      time: { gan: '庚', zhi: '申' }
    };

    const userBaziInfo = timingEngine.buildUserBaziInfo(testBazi, 'male');
    console.log('🔧 buildUserBaziInfo 结果:');
    console.log(JSON.stringify(userBaziInfo, null, 2));

    // 3. 使用buildUserBaziInfo的结果测试CelebrityDatabaseAPI
    console.log('\n📊 步骤3: 使用buildUserBaziInfo结果测试CelebrityDatabaseAPI');
    const engineResults = celebrityDB.findSimilarCelebrities(userBaziInfo, {
      limit: 3,
      minSimilarity: 0.1,
      userGender: 'male'
    });

    console.log(`✅ 引擎格式调用结果: 找到${engineResults.length}位相似名人`);
    if (engineResults.length > 0) {
      engineResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.celebrity.basicInfo.name} - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
      });
    }

    // 4. 检查数据库中的名人数据格式
    console.log('\n📊 步骤4: 检查数据库中的名人数据格式');
    const firstCelebrity = celebrityDB.celebrities[0];
    console.log('🔧 第一位名人数据结构:');
    console.log('- 基本信息:', JSON.stringify(firstCelebrity.basicInfo, null, 2));
    console.log('- 八字信息:', JSON.stringify(firstCelebrity.bazi, null, 2));
    console.log('- 格局信息:', JSON.stringify(firstCelebrity.pattern, null, 2));

    // 5. 测试相似度计算方法
    console.log('\n📊 步骤5: 测试相似度计算方法');
    
    // 转换名人八字格式
    const celebrityBaziFormatted = {
      yearPillar: { gan: firstCelebrity.bazi.year.gan, zhi: firstCelebrity.bazi.year.zhi },
      monthPillar: { gan: firstCelebrity.bazi.month.gan, zhi: firstCelebrity.bazi.month.zhi },
      dayPillar: { gan: firstCelebrity.bazi.day.gan, zhi: firstCelebrity.bazi.day.zhi },
      timePillar: { gan: firstCelebrity.bazi.hour.gan, zhi: firstCelebrity.bazi.hour.zhi }
    };

    const baziSimilarity = celebrityDB.calculateEnhancedBaziSimilarity(testUserInfo.bazi, celebrityBaziFormatted);
    const patternSimilarity = celebrityDB.calculateEnhancedPatternSimilarity(testUserInfo.pattern, firstCelebrity.pattern);
    
    console.log(`🔧 与${firstCelebrity.basicInfo.name}的相似度:`);
    console.log(`- 八字相似度: ${(baziSimilarity * 100).toFixed(1)}%`);
    console.log(`- 格局相似度: ${(patternSimilarity * 100).toFixed(1)}%`);
    console.log(`- 综合相似度: ${((baziSimilarity * 0.5 + patternSimilarity * 0.4) * 100).toFixed(1)}%`);

    // 6. 检查性别过滤
    console.log('\n📊 步骤6: 检查性别过滤');
    const maleCount = celebrityDB.celebrities.filter(c => 
      celebrityDB.normalizeGender(c.basicInfo.gender) === 'male'
    ).length;
    const femaleCount = celebrityDB.celebrities.filter(c => 
      celebrityDB.normalizeGender(c.basicInfo.gender) === 'female'
    ).length;
    
    console.log(`🔧 性别分布: 男性${maleCount}位, 女性${femaleCount}位`);

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行调试
debugCelebrityIntegration();
