/**
 * 完整历史名人数据库合并工具 - 第二批次
 * 将第二批次数据与现有数据库合并，从62位扩展到87位名人
 */

const existingDatabase = require('../data/complete_celebrities_database.js');
const batch2Data = require('../data/song_yuan_ming_qing_batch2_complete.js');
const fs = require('fs');
const path = require('path');

class CompleteDatabaseBatch2Merger {
  constructor() {
    this.mergedData = {
      metadata: {
        version: "2.0",
        totalRecords: 0,
        description: "中国历史名人命理数据库完整版 - 包含第二批次扩展",
        dynasties: [],
        period: "前221年-1912年",
        dataSources: [],
        creationDate: new Date().toISOString().split('T')[0],
        lastUpdate: new Date().toISOString().split('T')[0],
        verificationStandard: "专家交叉校验+古籍依据双重认证",
        batches: [
          {
            batchNumber: 1,
            description: "隋唐五代时期",
            recordCount: 25,
            addedDate: "2025-01-01"
          },
          {
            batchNumber: 2,
            description: "宋元明清时期",
            recordCount: 25,
            addedDate: "2025-01-02"
          }
        ]
      },
      celebrities: []
    };
  }

  /**
   * 合并数据库
   */
  mergeDatabase() {
    console.log('🔄 开始合并完整数据库...');
    console.log(`   - 现有数据库: ${existingDatabase.celebrities.length} 位名人`);
    console.log(`   - 第二批次: ${batch2Data.celebrities.length} 位名人`);
    
    // 合并名人数据
    this.mergedData.celebrities = [
      ...existingDatabase.celebrities,
      ...batch2Data.celebrities
    ];
    
    // 更新元数据
    this.mergedData.metadata.totalRecords = this.mergedData.celebrities.length;
    
    // 合并朝代信息
    const existingDynasties = existingDatabase.metadata.dynasties || [];
    const newDynasties = batch2Data.metadata.dynasties || [];
    this.mergedData.metadata.dynasties = [...new Set([...existingDynasties, ...newDynasties])];
    
    // 合并数据源
    const existingSources = existingDatabase.metadata.dataSources || [];
    const newSources = batch2Data.metadata.dataSources || [];
    this.mergedData.metadata.dataSources = [...new Set([...existingSources, ...newSources])];
    
    console.log(`✅ 合并完成: ${this.mergedData.celebrities.length} 位名人`);
  }

  /**
   * 验证数据完整性
   */
  validateDatabase() {
    console.log('🔍 验证数据库完整性...');
    
    let totalVerification = 0;
    let verificationCount = 0;
    let issues = [];
    let dynastyStats = {};
    
    this.mergedData.celebrities.forEach((celebrity, index) => {
      // 统计朝代分布
      const dynasty = celebrity.basicInfo?.dynasty || '未知';
      dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
      
      // 检查数据完整性
      if (!celebrity.id) {
        issues.push(`第${index + 1}位名人缺少ID`);
      }
      
      if (!celebrity.basicInfo?.name) {
        issues.push(`第${index + 1}位名人缺少姓名`);
      }
      
      if (!celebrity.bazi?.fullBazi) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少完整八字`);
      }
      
      if (!celebrity.pattern?.mainPattern) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少主格局`);
      }
      
      if (!celebrity.verification?.algorithmMatch) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}缺少算法匹配度`);
      } else {
        totalVerification += celebrity.verification.algorithmMatch;
        verificationCount++;
      }
      
      // 检查ID唯一性
      const duplicateIds = this.mergedData.celebrities.filter(c => c.id === celebrity.id);
      if (duplicateIds.length > 1) {
        issues.push(`ID重复: ${celebrity.id}`);
      }
    });
    
    const averageVerification = verificationCount > 0 ? totalVerification / verificationCount : 0;
    const qualityLevel = averageVerification >= 0.9 ? '优秀' : averageVerification >= 0.8 ? '良好' : '需改进';
    
    console.log(`📊 数据库统计报告:`);
    console.log(`   - 总名人数: ${this.mergedData.celebrities.length}`);
    console.log(`   - 平均验证度: ${averageVerification.toFixed(3)}`);
    console.log(`   - 数据质量: ${qualityLevel}`);
    console.log(`   - 发现问题: ${issues.length} 个`);
    
    console.log(`📈 朝代分布:`);
    Object.entries(dynastyStats).sort((a, b) => b[1] - a[1]).forEach(([dynasty, count]) => {
      console.log(`   - ${dynasty}: ${count} 位`);
    });
    
    if (issues.length > 0) {
      console.log(`⚠️  问题详情:`);
      issues.slice(0, 10).forEach(issue => console.log(`     - ${issue}`));
      if (issues.length > 10) {
        console.log(`     ... 还有 ${issues.length - 10} 个问题`);
      }
    }
    
    return {
      averageScore: averageVerification,
      issues: issues,
      qualityLevel: qualityLevel,
      dynastyStats: dynastyStats
    };
  }

  /**
   * 保存完整数据库
   */
  saveDatabase() {
    const outputPath = path.join(__dirname, '../data/complete_celebrities_database_v2.js');
    
    const fileContent = `/**
 * 中国历史名人命理数据库完整版 v2.0
 * 自动生成于 ${new Date().toISOString().split('T')[0]}
 * 包含87位历史名人的完整命理分析数据
 * 
 * 数据来源：
 * - 第一批次：隋唐五代时期 25位名人
 * - 第二批次：宋元明清时期 25位名人
 * - 基础数据：秦汉三国等早期 37位名人
 */

const completeCelebritiesDatabase = ${JSON.stringify(this.mergedData, null, 2)};

module.exports = completeCelebritiesDatabase;`;

    fs.writeFileSync(outputPath, fileContent, 'utf8');
    console.log(`💾 完整数据库已保存到: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 执行完整的合并流程
   */
  async execute() {
    try {
      console.log('🚀 开始完整数据库合并流程 v2.0');
      console.log('============================================================');
      
      // 1. 合并数据库
      this.mergeDatabase();
      
      // 2. 验证数据完整性
      const validation = this.validateDatabase();
      
      // 3. 保存完整数据库
      const outputPath = this.saveDatabase();
      
      console.log('\n🎉 数据库合并完成!');
      console.log('============================================================');
      console.log('📊 最终统计:');
      console.log(`   - 总名人数: ${this.mergedData.celebrities.length}`);
      console.log(`   - 平均验证度: ${validation.averageScore.toFixed(3)}`);
      console.log(`   - 数据质量: ${validation.qualityLevel}`);
      console.log(`   - 输出文件: ${outputPath}`);
      console.log(`   - 数据库版本: v2.0`);
      
      return {
        success: true,
        data: this.mergedData,
        validation: validation,
        outputPath: outputPath
      };
      
    } catch (error) {
      console.error('❌ 合并流程失败:', error);
      throw error;
    }
  }
}

module.exports = CompleteDatabaseBatch2Merger;
