<!--pages/bazi-result/index.wxml-->
<!-- 八字排盘结果展示页面 -->

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon">🔮</view>
    <view class="loading-text">正在加载分析结果...</view>
  </view>

  <!-- 结果内容 -->
  <view class="result-content" wx:else>
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-icon">🏛️</view>
      <view class="header-title">八字排盘结果</view>
      <view class="confidence-badge">可信度 {{(confidence * 100).toFixed(0)}}%</view>
    </view>

    <!-- 基本信息卡片 -->
    <view class="basic-card">
      <view class="card-header">
        <text class="card-icon">📊</text>
        <text class="card-title">基本信息</text>
      </view>
      
      <view class="birth-info" wx:if="{{birthInfo}}">
        <view class="info-row">
          <text class="info-label">出生时间</text>
          <text class="info-value">{{birthInfo.year}}年{{birthInfo.month}}月{{birthInfo.day}}日 {{birthInfo.hour}}:{{birthInfo.minute < 10 ? '0' + birthInfo.minute : birthInfo.minute}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">性别地点</text>
          <text class="info-value">{{birthInfo.gender}} · {{birthInfo.location}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">分析模式</text>
          <text class="info-value">{{analysisMode.name}}</text>
        </view>
      </view>

      <view class="four-pillars" wx:if="{{fourPillars}}">
        <view class="pillars-label">四柱排盘</view>
        <view class="pillars-content">{{fourPillars}}</view>
      </view>

      <view class="basic-analysis" wx:if="{{basicInfo}}">
        <view class="analysis-item" wx:if="{{basicInfo.日主}}">
          <text class="item-label">日主</text>
          <text class="item-value">{{basicInfo.日主}} ({{basicInfo.日主五行}}{{basicInfo.日主阴阳}})</text>
        </view>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view class="tab-item {{activeTab === index ? 'active' : ''}}" 
            wx:for="{{tabs}}" 
            wx:key="key"
            wx:if="{{item.visible !== false}}"
            data-index="{{index}}"
            bindtap="switchTab">
        <text class="tab-icon">{{item.icon}}</text>
        <text class="tab-name">{{item.name}}</text>
      </view>
    </view>

    <!-- 标签页内容 -->
    <view class="tab-content">
      <!-- 基础信息标签页 -->
      <view class="tab-panel" wx:if="{{activeTab === 0 && analysisResult['基础分析']}}">
        <view class="analysis-section" wx:if="{{analysisResult['基础分析']['五行分析']}}">
          <view class="section-header" bindtap="showDetail" data-type="wuxing">
            <text class="section-title">五行分析</text>
            <text class="section-arrow">></text>
          </view>

          <!-- 数字化五行分析组件 -->
          <view class="digital-analysis-container" wx:if="{{digitalAnalysis.enabled}}">
            <view class="digital-header">
              <text class="digital-title">📊 数字化五行分析</text>
              <text class="digital-subtitle">基于{{digitalAnalysis.rulesCount || 1148}}条古籍规则的智能分析</text>
            </view>

            <!-- 五行雷达图 -->
            <wuxing-radar
              wuxing-scores="{{digitalAnalysis.wuxingScores}}"
              show-details="{{true}}"
              animation="{{true}}" />

            <!-- 增强平衡指标 -->
            <enhanced-balance-meter
              balance-index="{{digitalAnalysis.balanceIndex}}"
              wuxing-scores="{{digitalAnalysis.wuxingScores}}"
              show-details-default="{{false}}"
              bind:share="onShareDigitalAnalysis" />
          </view>

          <!-- 传统五行统计 -->
          <view class="wuxing-stats">
            <view class="wuxing-item" wx:for="{{analysisResult['基础分析']['五行分析']['五行统计']}}" wx:key="index">
              <text class="wuxing-name">{{index}}</text>
              <text class="wuxing-count">{{item}}</text>
            </view>
          </view>
        </view>

        <view class="analysis-section" wx:if="{{analysisResult['基础分析']['十神分析']}}">
          <view class="section-header" bindtap="showDetail" data-type="shishen">
            <text class="section-title">十神分析</text>
            <text class="section-arrow">></text>
          </view>
          <view class="shishen-summary">
            <text class="summary-text">点击查看详细十神关系分析</text>
          </view>
        </view>
      </view>

      <!-- 专业分析标签页 -->
      <view class="tab-panel" wx:if="{{activeTab === 1 && analysisResult['专业分析']}}">
        <view class="professional-summary" bindtap="showDetail" data-type="professional">
          <view class="summary-header">
            <text class="summary-icon">🔬</text>
            <text class="summary-title">专业细盘分析</text>
            <text class="summary-arrow">></text>
          </view>
          <view class="summary-desc">包含强弱分析、格局判断、用神选择等专业内容</view>
        </view>
      </view>

      <!-- 古籍理论标签页 -->
      <view class="tab-panel" wx:if="{{activeTab === 2 && analysisResult['古籍分析']}}">
        <view class="classical-summary" bindtap="showDetail" data-type="classical">
          <view class="summary-header">
            <text class="summary-icon">📚</text>
            <text class="summary-title">古籍理论分析</text>
            <text class="summary-arrow">></text>
          </view>
          <view class="summary-desc">基于《穷通宝鉴》《渊海子平》等六部古籍的传统分析</view>
        </view>
      </view>

      <!-- 综合评价标签页 -->
      <view class="tab-panel" wx:if="{{activeTab === 3 && analysisResult['综合评价']}}">
        <view class="comprehensive-summary" bindtap="showDetail" data-type="comprehensive">
          <view class="summary-header">
            <text class="summary-icon">🎯</text>
            <text class="summary-title">综合评价</text>
            <text class="summary-arrow">></text>
          </view>
          <view class="evaluation-items" wx:if="{{analysisResult['综合评价']}}">
            <view class="eval-item" wx:if="{{analysisResult['综合评价']['命局层次']}}">
              <text class="eval-label">命局层次</text>
              <text class="eval-value">{{analysisResult['综合评价']['命局层次']}}</text>
            </view>
            <view class="eval-item" wx:if="{{analysisResult['综合评价']['发展潜力']}}">
              <text class="eval-label">发展潜力</text>
              <text class="eval-value">{{analysisResult['综合评价']['发展潜力']}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="shareResult">
        <text class="btn-icon">📤</text>
        <text>分享结果</text>
      </button>
      
      <button class="action-btn secondary" bindtap="exportReport" loading="{{loadingDetail}}">
        <text class="btn-icon">📄</text>
        <text>{{loadingDetail ? '生成中...' : '导出报告'}}</text>
      </button>
      
      <button class="action-btn primary" bindtap="newPaipan">
        <text class="btn-icon">🔮</text>
        <text>重新排盘</text>
      </button>
    </view>
  </view>

  <!-- 详情弹窗 -->
  <view class="detail-modal {{showDetailModal ? 'show' : ''}}" wx:if="{{showDetailModal}}">
    <view class="modal-mask" bindtap="closeDetailModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{currentDetailType}}</text>
        <text class="modal-close" bindtap="closeDetailModal">✕</text>
      </view>
      <scroll-view class="modal-body" scroll-y>
        <view class="detail-content">
          <text class="detail-text">{{formatObjectToText(currentDetailData)}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 报告弹窗 -->
  <view class="report-modal {{showReportModal ? 'show' : ''}}" wx:if="{{showReportModal}}">
    <view class="modal-mask" bindtap="closeReportModal"></view>
    <view class="modal-content large">
      <view class="modal-header">
        <text class="modal-title">分析报告</text>
        <view class="modal-actions">
          <text class="modal-action" bindtap="copyReport">复制</text>
          <text class="modal-close" bindtap="closeReportModal">✕</text>
        </view>
      </view>
      <scroll-view class="modal-body" scroll-y>
        <view class="report-content">
          <text class="report-text">{{reportContent}}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
