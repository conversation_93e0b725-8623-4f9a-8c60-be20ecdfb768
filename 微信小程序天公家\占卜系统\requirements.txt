# 《玉匣记》项目依赖文件
# Python 3.11+ 推荐

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1
aiomysql==0.2.0
pymysql==1.1.0
redis==5.0.1
aioredis==2.0.1

# 数据处理
pandas==2.1.3
numpy==1.25.2
python-dateutil==2.8.2

# 文本处理和正则表达式
regex==2023.10.3
jieba==0.42.1

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 配置管理
python-dotenv==1.0.0
pydantic-settings==2.1.0

# 日志和监控
loguru==0.7.2
structlog==23.2.0

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 开发工具
ipython==8.17.2
jupyter==1.0.0

# 部署相关
gunicorn==21.2.0
docker==6.1.3

# 可选：搜索引擎支持
# elasticsearch==8.11.0
# elasticsearch-dsl==8.11.0

# 可选：机器学习支持
# scikit-learn==1.3.2
# transformers==4.35.2
