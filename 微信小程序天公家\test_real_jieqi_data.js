// test_real_jieqi_data.js
// 使用真实的权威节气数据测试月柱计算

console.log('🧪 使用真实权威节气数据测试月柱计算...');

// 2024年真实的权威节气数据
const real2024JieqiData = {
  "小暑": {
    "month": 7,
    "day": 6,
    "hour": 22,
    "minute": 29
  },
  "大暑": {
    "month": 7,
    "day": 22,
    "hour": 15,
    "minute": 50
  },
  "立秋": {
    "month": 8,
    "day": 7,
    "hour": 8,
    "minute": 18
  }
};

console.log('\n📊 2024年真实权威节气数据：');
console.log('小暑：7月6日22:29');
console.log('大暑：7月22日15:50');
console.log('立秋：8月7日08:18');

// 精确的节气月份计算
function getSolarMonthByRealData(year, month, day) {
  // 使用当天中午12:00作为比较基准
  const currentDate = new Date(year, month - 1, day, 12, 0, 0);
  
  // 关键节气时间
  const xiaoshu = new Date(2024, 6, 6, 22, 29); // 小暑：7月6日22:29
  const dashu = new Date(2024, 6, 22, 15, 50);  // 大暑：7月22日15:50
  const liqiu = new Date(2024, 7, 7, 8, 18);    // 立秋：8月7日08:18
  
  console.log(`\n🔍 精确节气判断: ${year}年${month}月${day}日`);
  console.log(`当前时间: ${currentDate.toLocaleString()}`);
  console.log(`小暑时间: ${xiaoshu.toLocaleString()}`);
  console.log(`大暑时间: ${dashu.toLocaleString()}`);
  console.log(`立秋时间: ${liqiu.toLocaleString()}`);
  
  // 7-8月的精确判断
  if (month === 7) {
    if (currentDate >= xiaoshu) {
      console.log(`✅ ${year}年${month}月${day}日 >= 小暑 → 未月(6)`);
      return 6; // 小暑后，未月
    } else {
      console.log(`✅ ${year}年${month}月${day}日 < 小暑 → 午月(5)`);
      return 5; // 小暑前，午月
    }
  }
  
  if (month === 8) {
    if (currentDate >= liqiu) {
      console.log(`✅ ${year}年${month}月${day}日 >= 立秋 → 申月(7)`);
      return 7; // 立秋后，申月
    } else {
      console.log(`✅ ${year}年${month}月${day}日 < 立秋 → 未月(6)`);
      return 6; // 立秋前，未月
    }
  }
  
  // 其他月份
  const monthMap = {
    1: 12, 2: 1, 3: 2, 4: 3, 5: 4, 6: 5,
    9: 8, 10: 9, 11: 10, 12: 11
  };
  
  const result = monthMap[month] || month;
  console.log(`✅ ${year}年${month}月${day}日 → 月份${result}`);
  return result;
}

// 计算月柱
function calculateMonthPillar(year, month, day, yearGan) {
  const tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
  const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
  
  // 获取节气月份
  const solarMonth = getSolarMonthByRealData(year, month, day);
  
  // 五虎遁
  const wuhuDun = {
    '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
    '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
    '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
    '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
    '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
  };
  
  const monthGanStart = wuhuDun[yearGan];
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  const monthZhi = monthZhiMap[solarMonth - 1];
  
  const result = monthGan + monthZhi;
  
  console.log(`\n🔧 月柱计算详情:`);
  console.log(`年干: ${yearGan}`);
  console.log(`节气月: ${solarMonth}`);
  console.log(`五虎遁起始: ${tiangan[monthGanStart]}(${monthGanStart})`);
  console.log(`月干计算: (${monthGanStart} + ${solarMonth} - 1) % 10 = ${monthGanIndex}`);
  console.log(`月干: ${monthGan}`);
  console.log(`月支: ${monthZhi}`);
  console.log(`月柱: ${result}`);
  
  return result;
}

// 测试关键案例
function testWithRealData() {
  console.log('\n📊 使用真实权威节气数据测试：');
  
  const testCases = [
    {
      date: '2024年7月6日',
      year: 2024, month: 7, day: 6,
      yearGan: '甲',
      expected: '辛未',
      description: '小暑当日，应该是未月（小暑22:29，中午12:00 < 22:29）'
    },
    {
      date: '2024年7月7日',
      year: 2024, month: 7, day: 7,
      yearGan: '甲',
      expected: '辛未',
      description: '小暑后第一天，应该是未月'
    },
    {
      date: '2024年7月22日',
      year: 2024, month: 7, day: 22,
      yearGan: '甲',
      expected: '辛未',
      description: '大暑当日，仍是未月（大暑15:50，中午12:00 < 15:50）'
    },
    {
      date: '2024年7月30日',
      year: 2024, month: 7, day: 30,
      yearGan: '甲',
      expected: '辛未',
      description: '关键案例：7月30日应该是辛未'
    },
    {
      date: '2024年8月7日',
      year: 2024, month: 8, day: 7,
      yearGan: '甲',
      expected: '辛未',
      description: '立秋当日，应该仍是未月（立秋08:18，中午12:00 > 08:18）'
    },
    {
      date: '2024年8月8日',
      year: 2024, month: 8, day: 8,
      yearGan: '甲',
      expected: '壬申',
      description: '立秋后第一天，应该是申月'
    }
  ];
  
  let allCorrect = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n=== 测试案例${index + 1}: ${testCase.date} ===`);
    console.log(`描述: ${testCase.description}`);
    
    const result = calculateMonthPillar(testCase.year, testCase.month, testCase.day, testCase.yearGan);
    const isCorrect = result === testCase.expected;
    
    if (!isCorrect) allCorrect = false;
    
    console.log(`期望: ${testCase.expected}`);
    console.log(`实际: ${result}`);
    console.log(`结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
    
    if (!isCorrect) {
      console.log(`⚠️ 测试失败！需要进一步检查`);
    }
  });
  
  return allCorrect;
}

// 分析为什么数据不会不可用
function analyzeDataAvailability() {
  console.log('\n🔍 分析数据可用性问题：');
  
  console.log('\n✅ 您说得对，前端本地计算不应该出现数据不可用：');
  console.log('1. 权威节气数据文件已经在本地');
  console.log('2. 文件大小合理（1628行），不会加载失败');
  console.log('3. 数据格式标准，解析不会出错');
  console.log('4. 微信小程序环境稳定，require不会失败');
  
  console.log('\n❌ 我之前的设计问题：');
  console.log('1. 添加了不必要的 try-catch');
  console.log('2. 设计了多余的降级逻辑');
  console.log('3. 增加了代码复杂性');
  console.log('4. 可能掩盖真正的问题');
  
  console.log('\n✅ 正确的设计应该是：');
  console.log('1. 直接使用本地权威节气数据');
  console.log('2. 如果真的加载失败，应该明确报错');
  console.log('3. 不需要复杂的降级机制');
  console.log('4. 代码简洁明了');
}

// 执行测试
const allTestsPassed = testWithRealData();
analyzeDataAvailability();

console.log('\n🎯 测试结果总结：');
if (allTestsPassed) {
  console.log('🎉 所有测试用例通过！');
  console.log('✅ 使用真实权威节气数据计算正确');
  console.log('✅ 2024年7月30日正确显示为辛未');
  console.log('✅ 数据本地可用，不需要降级逻辑');
} else {
  console.log('⚠️ 部分测试用例失败');
  console.log('❌ 需要进一步调试');
}

console.log('\n📋 关键发现：');
console.log('1. 2024年大暑时间是15:50，不是我之前测试的09:44');
console.log('2. 7月22日大暑当日中午仍是未月（12:00 < 15:50）');
console.log('3. 8月7日立秋当日中午已是申月（12:00 > 08:18）');
console.log('4. 权威数据完全可用，不需要复杂的降级逻辑');

console.log('\n🏁 真实权威节气数据测试完成！');
