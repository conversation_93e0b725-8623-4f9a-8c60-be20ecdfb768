// diagnose_shensha_errors.js
// 诊断神煞分析系统的错误

console.log('🔍 诊断神煞分析系统错误');
console.log('='.repeat(80));

// "问真八字"标准神煞结果
const wenZhenShenshaStandard = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  birthMonth: 6,
  birthInfo: {
    year: 2021,
    month: 6,
    day: 24,
    hour: 19,
    minute: 30,
    gender: '男'
  }
};

// 从日志中提取的当前系统结果
const currentSystemResult = {
  吉星数量: 5,
  凶星数量: 3, // 注意：日志显示数量不一致，有时是4，有时是3
  吉星: [], // 需要从具体实现中获取
  凶星: []  // 需要从具体实现中获取
};

// 分析神煞计算规则
function analyzeShenshaRules() {
  console.log('\n📚 分析神煞计算规则:');
  console.log('='.repeat(50));
  
  console.log('基于"问真八字"标准，需要实现的神煞:');
  
  console.log('\n年柱神煞:');
  wenZhenShenshaStandard.year.forEach(shensha => {
    console.log(`  - ${shensha}`);
  });
  
  console.log('\n月柱神煞:');
  wenZhenShenshaStandard.month.forEach(shensha => {
    console.log(`  - ${shensha}`);
  });
  
  console.log('\n日柱神煞:');
  wenZhenShenshaStandard.day.forEach(shensha => {
    console.log(`  - ${shensha}`);
  });
  
  console.log('\n时柱神煞:');
  wenZhenShenshaStandard.hour.forEach(shensha => {
    console.log(`  - ${shensha}`);
  });
  
  // 统计总数
  const totalShensha = [
    ...wenZhenShenshaStandard.year,
    ...wenZhenShenshaStandard.month,
    ...wenZhenShenshaStandard.day,
    ...wenZhenShenshaStandard.hour
  ];
  
  console.log(`\n总计神煞数量: ${totalShensha.length}`);
  console.log(`去重后数量: ${[...new Set(totalShensha)].length}`);
}

// 检查天乙贵人计算
function checkTianyiGuiren() {
  console.log('\n🌟 检查天乙贵人计算:');
  console.log('='.repeat(50));
  
  const dayGan = testData.fourPillars[2].gan; // 癸
  
  // 天乙贵人查表
  const tianyiGuirenTable = {
    '甲': ['丑', '未'], '戊': ['丑', '未'],
    '乙': ['子', '申'], '己': ['子', '申'],
    '丙': ['亥', '酉'], '丁': ['亥', '酉'],
    '庚': ['丑', '未'], '辛': ['寅', '午'],
    '壬': ['卯', '巳'], '癸': ['卯', '巳']
  };
  
  const expectedZhi = tianyiGuirenTable[dayGan];
  console.log(`日干${dayGan}的天乙贵人地支: ${expectedZhi.join(', ')}`);
  
  const foundPositions = [];
  testData.fourPillars.forEach((pillar, index) => {
    if (expectedZhi.includes(pillar.zhi)) {
      const positions = ['年柱', '月柱', '日柱', '时柱'];
      foundPositions.push(positions[index]);
      console.log(`  ${positions[index]} ${pillar.zhi}: ✅ 天乙贵人`);
    }
  });
  
  console.log(`期望位置: 月柱、日柱 (根据"问真八字")`);
  console.log(`计算位置: ${foundPositions.join(', ')}`);
  
  const match = foundPositions.includes('月柱') && foundPositions.includes('日柱');
  console.log(`匹配度: ${match ? '✅ 正确' : '❌ 需要检查'}`);
}

// 检查桃花计算
function checkTaohua() {
  console.log('\n🌸 检查桃花计算:');
  console.log('='.repeat(50));
  
  const dayZhi = testData.fourPillars[2].zhi; // 卯
  
  // 桃花查表（咸池）
  const taohuaTable = {
    '申子辰': '酉',
    '寅午戌': '卯',
    '巳酉丑': '午',
    '亥卯未': '子'
  };
  
  let expectedTaohua = null;
  for (const [group, taohua] of Object.entries(taohuaTable)) {
    if (group.includes(dayZhi)) {
      expectedTaohua = taohua;
      break;
    }
  }
  
  console.log(`日支${dayZhi}的桃花: ${expectedTaohua}`);
  
  const foundPositions = [];
  testData.fourPillars.forEach((pillar, index) => {
    if (pillar.zhi === expectedTaohua) {
      const positions = ['年柱', '月柱', '日柱', '时柱'];
      foundPositions.push(positions[index]);
      console.log(`  ${positions[index]} ${pillar.zhi}: ✅ 桃花`);
    }
  });
  
  console.log(`期望位置: 月柱 (根据"问真八字")`);
  console.log(`计算位置: ${foundPositions.join(', ')}`);
  
  const match = foundPositions.includes('月柱');
  console.log(`匹配度: ${match ? '✅ 正确' : '❌ 需要检查'}`);
}

// 检查文昌贵人计算
function checkWenchangGuiren() {
  console.log('\n📚 检查文昌贵人计算:');
  console.log('='.repeat(50));
  
  const dayGan = testData.fourPillars[2].gan; // 癸
  
  // 文昌贵人查表
  const wenchangTable = {
    '甲': '巳', '乙': '午', '丙': '申', '丁': '酉',
    '戊': '申', '己': '酉', '庚': '亥', '辛': '子',
    '壬': '寅', '癸': '卯'
  };
  
  const expectedWenchang = wenchangTable[dayGan];
  console.log(`日干${dayGan}的文昌贵人: ${expectedWenchang}`);
  
  const foundPositions = [];
  testData.fourPillars.forEach((pillar, index) => {
    if (pillar.zhi === expectedWenchang) {
      const positions = ['年柱', '月柱', '日柱', '时柱'];
      foundPositions.push(positions[index]);
      console.log(`  ${positions[index]} ${pillar.zhi}: ✅ 文昌贵人`);
    }
  });
  
  console.log(`期望位置: 日柱 (根据"问真八字")`);
  console.log(`计算位置: ${foundPositions.join(', ')}`);
  
  const match = foundPositions.includes('日柱');
  console.log(`匹配度: ${match ? '✅ 正确' : '❌ 需要检查'}`);
}

// 检查福星贵人计算
function checkFuxingGuiren() {
  console.log('\n⭐ 检查福星贵人计算:');
  console.log('='.repeat(50));
  
  // 福星贵人通常基于年支或日支计算
  const yearZhi = testData.fourPillars[0].zhi; // 丑
  const dayZhi = testData.fourPillars[2].zhi;  // 卯
  
  console.log(`年支: ${yearZhi}, 日支: ${dayZhi}`);
  console.log('期望位置: 年柱、日柱 (根据"问真八字")');
  console.log('需要查阅具体的福星贵人计算规则');
}

// 检查凶煞计算
function checkXiongsha() {
  console.log('\n💀 检查凶煞计算:');
  console.log('='.repeat(50));
  
  console.log('期望凶煞:');
  console.log('- 童子煞 (日柱)');
  console.log('- 灾煞 (日柱)');
  console.log('- 丧门 (日柱)');
  console.log('- 血刃 (日柱)');
  console.log('- 元辰 (月柱)');
  console.log('- 寡宿 (时柱)');
  console.log('- 披麻 (时柱)');
  
  console.log('\n需要检查的凶煞计算规则:');
  console.log('1. 童子煞 - 通常基于日时组合');
  console.log('2. 灾煞 - 基于年支查表');
  console.log('3. 丧门 - 基于年支查表');
  console.log('4. 血刃 - 基于日干查表');
  console.log('5. 元辰 - 基于年支查表');
  console.log('6. 寡宿 - 基于年支查表');
  console.log('7. 披麻 - 基于年支查表');
}

// 分析系统问题
function analyzeSystemProblems() {
  console.log('\n🔧 分析系统问题:');
  console.log('='.repeat(50));
  
  console.log('发现的问题:');
  console.log('1. ❌ 神煞数量不匹配');
  console.log('   - 当前系统: 吉星5个，凶星3-4个');
  console.log('   - "问真八字": 总计约15个神煞');
  
  console.log('\n2. ❌ 缺失的重要神煞:');
  console.log('   - 月德合、天厨贵人、德秀贵人');
  console.log('   - 童子煞、血刃、元辰、披麻');
  
  console.log('\n3. ❌ 可能的计算错误:');
  console.log('   - 神煞查表规则不准确');
  console.log('   - 计算基准选择错误（年支vs日支vs日干）');
  console.log('   - 神煞分类不正确');
  
  console.log('\n4. ❌ 显示格式问题:');
  console.log('   - 未按柱位分组显示');
  console.log('   - 缺少神煞详细说明');
  console.log('   - 强度等级显示为英文');
}

// 生成修复方案
function generateFixPlan() {
  console.log('\n🎯 神煞系统修复方案:');
  console.log('='.repeat(50));
  
  console.log('优先级1 - 核心神煞修复:');
  console.log('1. 修正天乙贵人计算规则');
  console.log('2. 修正桃花（咸池）计算规则');
  console.log('3. 修正文昌贵人计算规则');
  console.log('4. 实现福星贵人计算');
  
  console.log('\n优先级2 - 缺失神煞实现:');
  console.log('1. 实现月德合计算');
  console.log('2. 实现天厨贵人计算');
  console.log('3. 实现德秀贵人计算');
  console.log('4. 实现童子煞计算');
  console.log('5. 实现血刃计算');
  console.log('6. 实现元辰计算');
  console.log('7. 实现寡宿、披麻计算');
  
  console.log('\n优先级3 - 显示优化:');
  console.log('1. 按柱位分组显示神煞');
  console.log('2. 添加神煞详细说明');
  console.log('3. 修正强度等级中文化');
  console.log('4. 优化神煞星曜标签页显示');
  
  console.log('\n推荐实施步骤:');
  console.log('1. 基于"问真八字"标准重建神煞查表');
  console.log('2. 逐个验证神煞计算准确性');
  console.log('3. 完善神煞显示格式');
  console.log('4. 建立神煞测试验证体系');
}

// 执行诊断
console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('参考标准: "问真八字"权威软件');
console.log('出生时间: 2021年6月24日19:30');

analyzeShenshaRules();
checkTianyiGuiren();
checkTaohua();
checkWenchangGuiren();
checkFuxingGuiren();
checkXiongsha();
analyzeSystemProblems();
generateFixPlan();

console.log('\n📊 诊断总结:');
console.log('='.repeat(40));
console.log('❌ 神煞计算存在重大缺陷');
console.log('❌ 缺失多个重要神煞');
console.log('❌ 计算规则需要重新验证');
console.log('❌ 显示格式需要优化');

console.log('\n🚀 下一步行动:');
console.log('1. 重建神煞计算查表系统');
console.log('2. 实现缺失的神煞计算');
console.log('3. 验证所有神煞计算准确性');
console.log('4. 优化神煞显示格式');
