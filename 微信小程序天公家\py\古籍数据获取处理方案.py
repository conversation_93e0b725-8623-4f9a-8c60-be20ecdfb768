#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
古籍数据获取和处理方案
基于DeepSeek AI建议的数据基础建设计划
"""

import json
import sqlite3
import re
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class ClassicalSource:
    """古籍来源数据结构"""
    book_name: str
    volume: str
    page: str
    original_text: str
    confidence: float
    verified: bool = False

@dataclass
class PatternRule:
    """格局规则数据结构"""
    pattern_id: str
    pattern_name: str
    category: str
    trigger_conditions: List[Dict]
    classical_sources: List[ClassicalSource]
    modern_interpretation: Dict
    compatibility_score: float
    created_at: datetime
    updated_at: datetime

class ClassicalTextProcessor:
    """古籍文本处理器"""
    
    def __init__(self):
        self.books_info = {
            "渊海子平": {
                "author": "宋·徐大升",
                "priority": 1,
                "specialties": ["格局判断", "正格理论", "基础八字"],
                "estimated_records": 800
            },
            "三命通会": {
                "author": "明·万民英", 
                "priority": 2,
                "specialties": ["全面理论", "变格分析", "神煞应用"],
                "estimated_records": 1200
            },
            "穷通宝鉴": {
                "author": "清·余春台",
                "priority": 1,
                "specialties": ["用神取用", "调候理论", "月令分析"],
                "estimated_records": 600
            }
        }
        
        self.conflict_resolver = ConflictResolver()
        self.data_validator = DataValidator()
    
    def process_book_data(self, book_name: str, raw_text: str) -> List[PatternRule]:
        """处理单本古籍数据"""
        print(f"📚 开始处理《{book_name}》")
        
        # 1. 文本预处理
        cleaned_text = self._preprocess_text(raw_text)
        
        # 2. 章节分割
        chapters = self._split_chapters(cleaned_text, book_name)
        
        # 3. 规则提取
        pattern_rules = []
        for chapter in chapters:
            rules = self._extract_pattern_rules(chapter, book_name)
            pattern_rules.extend(rules)
        
        # 4. 数据验证
        validated_rules = []
        for rule in pattern_rules:
            if self.data_validator.validate_pattern_rule(rule):
                validated_rules.append(rule)
            else:
                print(f"⚠️ 规则验证失败: {rule.pattern_name}")
        
        print(f"✅ 《{book_name}》处理完成，提取{len(validated_rules)}条有效规则")
        return validated_rules
    
    def _preprocess_text(self, raw_text: str) -> str:
        """文本预处理"""
        # 去除多余空白
        text = re.sub(r'\s+', ' ', raw_text)
        
        # 标准化标点符号
        text = text.replace('，', ',').replace('。', '.')
        text = text.replace('：', ':').replace('；', ';')
        
        # 去除页码和注释
        text = re.sub(r'\[页码:\d+\]', '', text)
        text = re.sub(r'\[注:\s*.*?\]', '', text)
        
        return text.strip()
    
    def _split_chapters(self, text: str, book_name: str) -> List[str]:
        """章节分割"""
        if book_name == "渊海子平":
            # 按卷分割
            chapters = re.split(r'卷[一二三四五六七八九十]+', text)
        elif book_name == "三命通会":
            # 按章分割
            chapters = re.split(r'第[一二三四五六七八九十]+章', text)
        elif book_name == "穷通宝鉴":
            # 按月份分割
            chapters = re.split(r'[正二三四五六七八九十冬腊]+月', text)
        else:
            # 默认按段落分割
            chapters = text.split('\n\n')
        
        return [ch.strip() for ch in chapters if ch.strip()]
    
    def _extract_pattern_rules(self, chapter_text: str, book_name: str) -> List[PatternRule]:
        """从章节中提取格局规则"""
        rules = []
        
        # 查找格局相关的文本段落
        pattern_paragraphs = self._find_pattern_paragraphs(chapter_text)
        
        for paragraph in pattern_paragraphs:
            rule = self._parse_pattern_paragraph(paragraph, book_name)
            if rule:
                rules.append(rule)
        
        return rules
    
    def _find_pattern_paragraphs(self, text: str) -> List[str]:
        """查找包含格局信息的段落"""
        pattern_keywords = [
            "格", "正官", "偏官", "正财", "偏财", "正印", "偏印",
            "食神", "伤官", "比肩", "劫财", "用神", "忌神",
            "从格", "化格", "专旺", "润下", "炎上", "稼穑"
        ]
        
        paragraphs = []
        for line in text.split('.'):
            line = line.strip()
            if any(keyword in line for keyword in pattern_keywords):
                if len(line) > 10:  # 过滤太短的句子
                    paragraphs.append(line)
        
        return paragraphs
    
    def _parse_pattern_paragraph(self, paragraph: str, book_name: str) -> Optional[PatternRule]:
        """解析格局段落"""
        try:
            # 提取格局名称
            pattern_name = self._extract_pattern_name(paragraph)
            if not pattern_name:
                return None
            
            # 提取触发条件
            conditions = self._extract_conditions(paragraph)
            
            # 提取解释内容
            interpretation = self._extract_interpretation(paragraph)
            
            # 创建古籍来源
            source = ClassicalSource(
                book_name=book_name,
                volume="待确定",
                page="待确定", 
                original_text=paragraph,
                confidence=0.8  # 初始置信度
            )
            
            # 创建格局规则
            rule = PatternRule(
                pattern_id=f"{book_name}_{pattern_name}_{hash(paragraph) % 10000}",
                pattern_name=pattern_name,
                category=self._classify_pattern(pattern_name),
                trigger_conditions=conditions,
                classical_sources=[source],
                modern_interpretation=interpretation,
                compatibility_score=0.8,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return rule
            
        except Exception as e:
            print(f"❌ 解析段落失败: {e}")
            return None
    
    def _extract_pattern_name(self, text: str) -> Optional[str]:
        """提取格局名称"""
        # 常见格局名称模式
        pattern_names = [
            "正官格", "偏官格", "正财格", "偏财格", "正印格", "偏印格",
            "食神格", "伤官格", "建禄格", "羊刃格", "从财格", "从官格",
            "从儿格", "化气格", "炎上格", "润下格", "稼穑格", "金白水清",
            "木火通明", "伤官配印", "财官双美", "杀印相生"
        ]
        
        for name in pattern_names:
            if name in text:
                return name
        
        # 使用正则表达式提取可能的格局名称
        pattern = r'([一-龥]{2,4}格)'
        matches = re.findall(pattern, text)
        if matches:
            return matches[0]
        
        return None
    
    def _extract_conditions(self, text: str) -> List[Dict]:
        """提取触发条件"""
        conditions = []
        
        # 查找条件关键词
        condition_patterns = [
            (r'日主([强弱])', '日主强弱'),
            (r'([正偏]?[官财印食伤比劫])([透藏])', '十神状态'),
            (r'([春夏秋冬])月', '季节条件'),
            (r'([甲乙丙丁戊己庚辛壬癸])日', '日干条件')
        ]
        
        for pattern, condition_type in condition_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    condition_text = ''.join(match)
                else:
                    condition_text = match
                
                conditions.append({
                    "condition": condition_text,
                    "type": condition_type,
                    "weight": 0.3,  # 默认权重
                    "validation_method": "pattern_matching"
                })
        
        return conditions
    
    def _extract_interpretation(self, text: str) -> Dict:
        """提取现代解释"""
        interpretation = {
            "personality": "",
            "career": "",
            "wealth": "",
            "relationships": "",
            "health": "",
            "general": text[:100] + "..." if len(text) > 100 else text
        }
        
        # 根据关键词提取不同方面的解释
        if "聪明" in text or "智慧" in text:
            interpretation["personality"] = "聪明智慧，思维敏捷"
        
        if "官" in text or "贵" in text:
            interpretation["career"] = "适合从政或管理工作"
        
        if "财" in text or "富" in text:
            interpretation["wealth"] = "财运较好，有聚财能力"
        
        return interpretation
    
    def _classify_pattern(self, pattern_name: str) -> str:
        """分类格局"""
        if "格" in pattern_name:
            if any(x in pattern_name for x in ["正官", "偏官", "正财", "偏财", "正印", "偏印", "食神", "伤官"]):
                return "正格"
            elif any(x in pattern_name for x in ["从", "化", "专"]):
                return "变格"
            else:
                return "特殊格局"
        return "其他"

class ConflictResolver:
    """古籍冲突解决器"""
    
    def __init__(self):
        self.priority_rules = {
            "格局判断": {
                "正格": ["渊海子平", "三命通会", "穷通宝鉴"],
                "变格": ["三命通会", "渊海子平", "穷通宝鉴"],
                "特殊格局": ["渊海子平", "三命通会", "穷通宝鉴"]
            },
            "用神取用": {
                "调候用神": ["穷通宝鉴", "渊海子平"],
                "扶抑用神": ["渊海子平", "三命通会"],
                "通关用神": ["三命通会", "穷通宝鉴"]
            }
        }
    
    def resolve_conflicts(self, conflicting_rules: List[PatternRule]) -> PatternRule:
        """解决规则冲突"""
        if len(conflicting_rules) <= 1:
            return conflicting_rules[0] if conflicting_rules else None
        
        # 按优先级排序
        sorted_rules = sorted(conflicting_rules, key=self._get_priority_score, reverse=True)
        
        # 合并规则
        merged_rule = self._merge_rules(sorted_rules)
        
        return merged_rule
    
    def _get_priority_score(self, rule: PatternRule) -> float:
        """获取规则优先级分数"""
        score = 0.0
        
        for source in rule.classical_sources:
            book_name = source.book_name
            category = rule.category
            
            # 根据书籍和类别确定优先级
            priority_list = self.priority_rules.get("格局判断", {}).get(category, [])
            
            if book_name in priority_list:
                position = priority_list.index(book_name)
                score += (len(priority_list) - position) * 10
            
            # 加上置信度分数
            score += source.confidence * 5
        
        return score
    
    def _merge_rules(self, rules: List[PatternRule]) -> PatternRule:
        """合并多个规则"""
        primary_rule = rules[0]
        
        # 合并古籍来源
        all_sources = []
        for rule in rules:
            all_sources.extend(rule.classical_sources)
        
        # 合并触发条件
        all_conditions = []
        for rule in rules:
            all_conditions.extend(rule.trigger_conditions)
        
        # 去重条件
        unique_conditions = []
        seen_conditions = set()
        for condition in all_conditions:
            condition_key = condition["condition"]
            if condition_key not in seen_conditions:
                unique_conditions.append(condition)
                seen_conditions.add(condition_key)
        
        # 创建合并后的规则
        merged_rule = PatternRule(
            pattern_id=f"merged_{primary_rule.pattern_name}_{datetime.now().timestamp()}",
            pattern_name=primary_rule.pattern_name,
            category=primary_rule.category,
            trigger_conditions=unique_conditions,
            classical_sources=all_sources,
            modern_interpretation=primary_rule.modern_interpretation,
            compatibility_score=sum(r.compatibility_score for r in rules) / len(rules),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        return merged_rule

class DataValidator:
    """数据验证器"""
    
    def validate_pattern_rule(self, rule: PatternRule) -> bool:
        """验证格局规则的有效性"""
        try:
            # 检查必要字段
            if not rule.pattern_name or not rule.pattern_id:
                return False
            
            # 检查触发条件
            if not rule.trigger_conditions:
                return False
            
            # 检查古籍来源
            if not rule.classical_sources:
                return False
            
            # 检查置信度
            for source in rule.classical_sources:
                if source.confidence < 0.1 or source.confidence > 1.0:
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 验证规则时出错: {e}")
            return False

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "占卜系统/data/bazi_classical.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建格局规则表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pattern_rules (
                pattern_id TEXT PRIMARY KEY,
                pattern_name TEXT NOT NULL,
                category TEXT,
                trigger_conditions TEXT,
                classical_sources TEXT,
                modern_interpretation TEXT,
                compatibility_score REAL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ''')
        
        # 创建古籍来源表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS classical_sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_id TEXT,
                book_name TEXT,
                volume TEXT,
                page TEXT,
                original_text TEXT,
                confidence REAL,
                verified BOOLEAN,
                FOREIGN KEY (pattern_id) REFERENCES pattern_rules (pattern_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_pattern_rules(self, rules: List[PatternRule]):
        """保存格局规则到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for rule in rules:
            # 保存主规则
            cursor.execute('''
                INSERT OR REPLACE INTO pattern_rules 
                (pattern_id, pattern_name, category, trigger_conditions, 
                 classical_sources, modern_interpretation, compatibility_score, 
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                rule.pattern_id,
                rule.pattern_name,
                rule.category,
                json.dumps(rule.trigger_conditions, ensure_ascii=False),
                json.dumps([{
                    'book_name': s.book_name,
                    'volume': s.volume,
                    'page': s.page,
                    'original_text': s.original_text,
                    'confidence': s.confidence,
                    'verified': s.verified
                } for s in rule.classical_sources], ensure_ascii=False),
                json.dumps(rule.modern_interpretation, ensure_ascii=False),
                rule.compatibility_score,
                rule.created_at.isoformat(),
                rule.updated_at.isoformat()
            ))
            
            # 保存古籍来源
            for source in rule.classical_sources:
                cursor.execute('''
                    INSERT INTO classical_sources 
                    (pattern_id, book_name, volume, page, original_text, confidence, verified)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    rule.pattern_id,
                    source.book_name,
                    source.volume,
                    source.page,
                    source.original_text,
                    source.confidence,
                    source.verified
                ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已保存 {len(rules)} 条格局规则到数据库")

def main():
    """主处理流程"""
    print("📚 古籍数据处理系统启动")
    print("=" * 50)
    
    # 初始化处理器
    processor = ClassicalTextProcessor()
    db_manager = DatabaseManager()
    
    # 模拟处理流程（实际使用时需要真实的古籍文本）
    print("\n⚠️  注意：当前为演示模式，需要真实古籍数据")
    print("📋 数据获取计划：")
    print("   1. 采购《渊海子平》《三命通会》《穷通宝鉴》正版电子版")
    print("   2. 建立专业数据录入团队")
    print("   3. 开发AI辅助校验工具")
    print("   4. 建立数据质量保证体系")
    
    # 创建示例数据结构
    example_rule = PatternRule(
        pattern_id="example_001",
        pattern_name="伤官配印",
        category="正格",
        trigger_conditions=[
            {
                "condition": "日主弱",
                "type": "日主强弱",
                "weight": 0.4,
                "validation_method": "wuxing_strength_analysis"
            }
        ],
        classical_sources=[
            ClassicalSource(
                book_name="渊海子平",
                volume="卷三",
                page="45",
                original_text="伤官配印，利官利贵，文章技艺，无不精微",
                confidence=0.95,
                verified=True
            )
        ],
        modern_interpretation={
            "personality": "艺术天赋突出，创造力强",
            "career": "适合创意、设计、文化等行业"
        },
        compatibility_score=0.85,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 保存示例数据
    db_manager.save_pattern_rules([example_rule])
    
    print("\n✅ 数据处理系统初始化完成")
    print("🎯 下一步：获取真实古籍数据并开始处理")

if __name__ == "__main__":
    main()
