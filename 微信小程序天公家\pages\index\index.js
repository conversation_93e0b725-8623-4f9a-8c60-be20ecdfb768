// pages/index/index.js
const app = getApp();

Page({
  data: {
    // 简化的数据结构，只保留必要的占卜相关数据
    currentDate: '',
    greeting: '欢迎来到天公师兄占卜'
  },

  onLoad() {
    console.log('首页加载');
    console.log('✅ npm依赖清理完成，项目运行正常');
    this.setCurrentDate();
  },

  onShow() {
    console.log('首页显示');
  },

  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const currentDate = `${year}-${month}-${day}`;

    this.setData({
      currentDate: currentDate
    });
  },

  // 跳转到占卜中心
  navigateToAssessmentHub() {
    wx.navigateTo({
      url: '/pages/assessment-hub/index',
      success: () => {
        console.log('成功跳转到占卜中心');
      },
      fail: (err) => {
        console.error('跳转到占卜中心失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '天公师兄占卜',
      path: '/pages/index/index'
    };
  }
});
    data: {
        // 简化的数据结构，只保留必要的占卜相关数据
        currentDate: '',
        greeting: '欢迎来到天公师兄占卜'
        currentDate: '', // 当前日期
        lastMessageId: '', // 最后一条消息ID
        welcomeTitle: '你好，让我们聊聊吧', // 欢迎标题
        welcomeSubtitle: '我们可以聊你最近的感受、学习或任何你想分享的事情', // 欢迎副标题
        questions: [], // 评估问题列表
        userInfo: null,
        hasUserInfo: false,
        userReady: false, // 添加用户准备状态字段
        waitingForReadyConfirmation: false, // 是否正在等待用户确认准备状态
        askingForIdentity: false, // 新增字段，表示正在询问身份
        askingForStudentInfo: false, // 新增字段，表示正在询问学生信息
        userRole: null, // 新增字段，表示用户角色
        currentOptionLabels: [], // 当前问题的选项标签
        waitingForModifyConfirmation: false // 新增字段，表示正在等待用户确认修改信息类型
    },

    onLoad() {
        // 设置当前日期
        this.setCurrentDate();

        // 初始化评估问题
        this.initQuestions();

        // 获取用户信息
        this.getUserInfo();

        // 根据目标用户年龄段设置欢迎语
        this.setupWelcomeMessage();

        // 发送欢迎消息
        setTimeout(() => {
            this.addMessage("AI", "你好！我是学生（三年级--高三）的心理测评助手。准备开始聊天了吗？", "greeting");

            // 设置等待用户确认状态
            this.setData({
                waitingForReadyConfirmation: true
            });
        }, 500);
    },

    onShow() {
        // 每次显示页面时检查用户信息状态
        this.getUserInfo();

        // 新增：检查是否从身份确认页面返回，并处理可能的状态恢复
        // 获取全局数据中是否有修改完成的标记
        const app = getApp();
        if (app.globalData && app.globalData.hasOwnProperty('modifyInfoCompleted')) {
            // 如果有标记，说明用户已经从身份确认页面返回
            // 如果用户没有更新信息，我们仍然需要重置状态
            if (app.globalData.modifyInfoCompleted === true) {
                console.log('用户已完成信息修改');
                // 修改已完成，保持当前状态
            } else {
                console.log('用户取消了信息修改');
                // 用户取消修改，重置状态
                this.setData({
                    sending: false,
                    waitingForModifyConfirmation: false,
                    askingForIdentity: false,
                    askingForStudentInfo: false
                });
            }

            // 清理全局标记
            delete app.globalData.modifyInfoCompleted;
        }
    },

    // 获取用户信息
    getUserInfo() {
        // 尝试从缓存获取用户信息
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
            this.setData({
                userInfo: userInfo,
                hasUserInfo: true
            });
        }
    },

    // 设置当前日期
    setCurrentDate() {
        const now = new Date();
        const month = now.getMonth() + 1; // 月份从0开始，所以要加1
        const day = now.getDate();

        // 简化为纯中文日期格式：X月X日
        const formattedDate = `${month}月${day}日`;
        this.setData({
            currentDate: formattedDate
        });
    },

    // 根据用户年龄段设置欢迎语
    setupWelcomeMessage() {
        // 不同年龄段的欢迎语
        const welcomeMessages = {
            lowerGrade: { // 小学3-6年级
                title: '你好呀，一起来聊天吧！',
                subtitle: '我们可以聊聊你最近的感受、学校生活，或者任何想分享的事情'
            },
            middleGrade: { // 初中生
                title: '嗨，让我们开始聊天吧',
                subtitle: '可以分享你的想法、学习生活、朋友关系，或者任何你关心的话题'
            },
            highGrade: { // 高中生
                title: '你好，欢迎来和我聊天',
                subtitle: '我们可以聊聊你最近的状态、学业压力、未来规划，或者其他任何话题'
            }
        };

        // 默认使用中等年龄段的欢迎语
        let welcomeType = 'middleGrade';

        // 如果有用户信息，尝试根据用户信息确定年龄段
        const { userInfo } = this.data;
        if (userInfo && userInfo.grade) {
            // 根据年级判断
            if (userInfo.grade <= 6) {
                welcomeType = 'lowerGrade';
            } else if (userInfo.grade <= 9) {
                welcomeType = 'middleGrade';
            } else {
                welcomeType = 'highGrade';
            }
        }

        // 设置欢迎语
        this.setData({
            welcomeTitle: welcomeMessages[welcomeType].title,
            welcomeSubtitle: welcomeMessages[welcomeType].subtitle
        });
    },

    // 初始化评估问题 - 为不同年龄段准备不同措辞的问题
    initQuestions() {
        // 建立基础问题库
        const baseQuestions = [
            {
                id: "mood",
                text: "最近一周，你的心情如何？有没有感到特别低落或者情绪波动很大的时候？",
                simplifiedText: "最近你心情怎么样？有没有特别不开心的时候？",
                dimension: "emotion",
                followUp: "能具体描述一下是什么让你感到{response}的吗？",
                simplifiedFollowUp: "是什么事情让你有这种感觉的呢？"
            },
            {
                id: "sleep",
                text: "你最近的睡眠质量怎么样？入睡困难或者早醒的情况多吗？",
                simplifiedText: "你晚上睡得好吗？容易睡着吗？",
                dimension: "physical",
                followUp: "你觉得是什么原因导致你{response}的？",
                simplifiedFollowUp: "你觉得是什么让你睡不好的？"
            },
            {
                id: "concentration",
                text: "在学习时，你能集中注意力的时间大概有多长？有没有注意力不集中的情况？",
                simplifiedText: "你做作业或听课的时候，能专心多久？会不会走神？",
                dimension: "attention",
                followUp: "这种注意力{response}的情况会影响你的学习吗？",
                simplifiedFollowUp: "这种情况会影响你的学习吗？"
            },
            {
                id: "social",
                text: "你最近的社交状况如何？是否经常与朋友或家人交流？",
                simplifiedText: "你最近和朋友、家人聊天多吗？",
                dimension: "social",
                followUp: "你觉得自己在和别人相处时最大的困难是什么？",
                simplifiedFollowUp: "和别人相处时，你觉得最难的是什么？"
            },
            {
                id: "stress",
                text: "你现在面临着哪些压力？对于这些压力，你通常会如何应对？",
                simplifiedText: "有什么事情让你感到紧张或压力大吗？你怎么处理这些感觉？",
                dimension: "stress",
                followUp: "当压力很大时，你通常会采取什么方式来缓解？",
                simplifiedFollowUp: "当你感到压力大的时候，你会怎么做让自己舒服一些？"
            },
            {
                id: "selfworth",
                text: "你如何看待自己？对自己的评价是积极的还是消极的？",
                simplifiedText: "你觉得自己是个什么样的人？喜欢自己吗？",
                dimension: "self",
                followUp: "是什么让你形成了这样{response}的自我评价？",
                simplifiedFollowUp: "是什么让你这样看自己的？"
            },
            {
                id: "future",
                text: "谈谈你对未来的看法，你对自己的未来有什么期待或担忧吗？",
                simplifiedText: "你对将来有什么想法？有什么期待或者担心的事吗？",
                dimension: "cognition",
                followUp: "你认为实现这些期待或克服这些担忧需要做出哪些努力？",
                simplifiedFollowUp: "你觉得要达成这些想法需要做些什么？"
            },
            {
                id: "academic",
                text: "你对自己的学习表现满意吗？遇到学习困难时，你会怎么做？",
                simplifiedText: "你对自己的学习满意吗？遇到不会的题目怎么办？",
                dimension: "academic",
                followUp: "你认为自己在学习方面的优势和不足分别是什么？",
                simplifiedFollowUp: "你觉得自己学习上最擅长什么？最不擅长什么？"
            },
            {
                id: "family",
                text: "你和家人的关系如何？有什么值得分享的快乐或困扰吗？",
                simplifiedText: "你和爸爸妈妈关系好吗？有什么开心或不开心的事？",
                dimension: "family",
                followUp: "在家庭关系中，你认为最重要的是什么？",
                simplifiedFollowUp: "你觉得家人之间最重要的是什么？"
            },
            {
                id: "coping",
                text: "当你感到难过或压力大时，你通常会做些什么来让自己感觉好一些？",
                simplifiedText: "当你难过的时候，你会做什么让自己开心起来？",
                dimension: "coping",
                followUp: "这些方法对缓解你的{response}有效吗？为什么？",
                simplifiedFollowUp: "这些办法有用吗？为什么？"
            }
        ];

        this.setData({
            questions: baseQuestions
        });
    },

    // 添加消息到对话列表
    addMessage(role, content, type = "normal") {
        const { messages } = this.data;
        const id = Date.now();

        messages.push({
            id,
            role,
            content,
            type,
            time: new Date().toLocaleTimeString()
        });

        this.setData({
            messages,
            typing: role === "AI"
        });

        // 设置最后一条消息ID，用于滚动到底部
        this.setData({
            lastMessageId: `msg-${id}`
        });

        // 如果是AI消息，模拟打字效果
        if (role === "AI") {
            setTimeout(() => {
                this.setData({
                    typing: false
                });
            }, 1000);
        }
    },

    // 输入框内容变化
    onInputChange(e) {
        this.setData({
            inputValue: e.detail.value
        });
    },

    // 发送消息
    sendMessage() {
        const { inputValue, sending, currentQuestionIndex, questions, waitingForReadyConfirmation, userReady, askingForIdentity, askingForStudentInfo, currentGradeQuestions, waitingForModifyConfirmation } = this.data;
        if (inputValue.trim() === '' || sending) return;

        this.setData({ sending: true });

        // 添加用户消息
        this.addMessage("User", inputValue);

        // 清空输入框
        this.setData({
            inputValue: ''
        });

        // 如果正在等待用户确认修改信息类型
        if (waitingForModifyConfirmation) {
            // 处理修改信息的确认
            this.handleModifyConfirmation(inputValue);
            return;
        }

        // 优先检查：如果正在进行年级测评且有题目，处理选择题答案
        if (currentGradeQuestions && currentGradeQuestions.length > 0 &&
            currentQuestionIndex < currentGradeQuestions.length) {

            // 检查是否有修改信息的意图
            if (this.detectModifyInfoIntent(inputValue)) {
                console.log('检测到用户想要修改信息的意图');
                this.handleModifyInfoRequest();
                return;
            }

            // 处理选择题答案
            console.log("处理测评选项:", inputValue);
            this.handleOptionSelection(inputValue);
            return;
        }

        // 如果还在等待用户准备确认
        if (waitingForReadyConfirmation) {
            // 检查用户是否准备好了
            const readyState = this.checkIfUserReady(inputValue);

            if (readyState === 'ready') {
                // 用户准备好了，开始评测
                this.handleUserReady();
            } else if (readyState === 'not_ready') {
                // 用户还没准备好
                this.handleUserNotReady();
            } else {
                // 用户回答不明确，再次询问
                this.askForReadinessAgain();
            }

            return;
        }

        // 如果正在询问身份
        if (askingForIdentity) {
            // 处理用户的身份回答
            this.handleIdentityResponse(inputValue);
            return;
        }

        // 如果正在询问学生信息
        if (askingForStudentInfo) {
            // 处理用户提供的学生信息
            this.handleProvidedStudentInfo(inputValue);
            return;
        }

        // 如果用户已经准备好，且评测还没开始或者正在进行中
        if (userReady) {
            // 检查是否有修改信息的意图
            if (this.detectModifyInfoIntent(inputValue)) {
                console.log('检测到用户想要修改信息的意图');
                this.handleModifyInfoRequest();
                return;
            }

            // 处理用户回答
            this.processUserResponse(inputValue, currentQuestionIndex);

            // 改为使用更智能的回答处理
            if (currentQuestionIndex + 1 < questions.length) {
                // 智能处理后续对话流程
                this.intelligentFollowUp(inputValue, currentQuestionIndex);
            } else {
                // 评估完成，生成结果
                this.completeAssessment();
            }
        } else {
            // 如果用户既不在准备阶段，也不在测评阶段，检查是否是修改信息的意图
            if (this.detectModifyInfoIntent(inputValue)) {
                console.log('检测到用户想要修改信息的意图');
                this.handleModifyInfoRequest();
                return;
            }

            // 其他情况，给予默认回复
            setTimeout(() => {
                this.addMessage("AI", "我没太理解您的意思。您是想开始评测，还是有其他问题？");
                this.setData({ sending: false });
            }, 1000);
        }
    },

    // 检查用户是否准备好开始评测
    checkIfUserReady(response) {
        // 表示准备好的关键词
        const readyKeywords = ['准备好', '可以开始', '开始吧', '好的', '可以', '是的', '好啊', '嗯', '好', '开始', '没问题'];

        // 表示没准备好的关键词
        const notReadyKeywords = ['还没', '不', '等等', '等一下', '稍等', '不行', '不可以', '不好', '不是', '没准备好', '不开始'];

        // 检查用户回答中是否包含准备好的关键词
        for (const keyword of readyKeywords) {
            if (response.includes(keyword)) {
                return 'ready';
            }
        }

        // 检查用户回答中是否包含没准备好的关键词
        for (const keyword of notReadyKeywords) {
            if (response.includes(keyword)) {
                return 'not_ready';
            }
        }

        // 无法确定用户是否准备好
        return 'unclear';
    },

    // 处理用户准备好的情况
    handleUserReady() {
        setTimeout(() => {
            this.addMessage("AI", "太好了！让我们开始吧。我需要了解一下您的身份，以便提供更适合的评测。请在即将打开的个人信息页面中填写您的信息，完成后点击\"开始测评\"按钮。");

            setTimeout(() => {
                // 设置用户已准备好状态，但暂不开始提问
                this.setData({
                    userReady: true,
                    waitingForReadyConfirmation: false,
                    sending: false,
                    askingForIdentity: true // 新增字段，表示正在询问身份
                });

                // 询问身份 - 延迟4秒后才跳转，给用户足够时间阅读消息
                setTimeout(() => {
                    this.addMessage("AI", "请问您是学生、家长还是老师？");
                }, 1000);
            }, 1000);
        }, 1000);
    },

    // 处理用户身份回答 (新增函数)
    handleIdentityResponse(response) {
        const lowerResponse = response.toLowerCase();

        // 根据关键词判断身份
        if (lowerResponse.includes('学生') || lowerResponse.includes('孩子') ||
            lowerResponse.includes('我是学生') || lowerResponse.includes('自己')) {
            // 学生身份
            setTimeout(() => {
                this.addMessage("AI", "了解了，您是学生。为了提供更准确的评测，请先完成个人信息填写。请在即将打开的页面中填写您的个人信息，完成后点击\"开始测评\"按钮。");

                // 设置用户角色
                this.setData({
                    userRole: 'student',
                    sending: false
                });

                // 延迟4秒后跳转，给用户足够的时间阅读消息
                setTimeout(() => {
                    // 在全局存储当前页面实例，以便页面返回时能调用回调函数
                    getApp().globalData = getApp().globalData || {};
                    getApp().globalData.indexPage = this;

                    // 跳转到学生身份确认页面
                    wx.navigateTo({
                        url: '/pages/assessment/identity_confirm_enhanced_v2',
                        success: () => {
                            console.log('成功跳转到身份确认页面');
                            // 不再在这里添加消息，因为用户已经跳转到新页面看不到
                        },
                        fail: (err) => {
                            console.error('跳转到身份确认页面失败:', err);
                            // 跳转失败时提示用户
                            this.addMessage("AI", "抱歉，无法打开身份信息页面。我们将直接开始测评。");
                            // 直接开始提问
                            this.startFirstQuestion();
                        }
                    });
                }, 4000); // 延迟4秒
            }, 1000);
        } else if (lowerResponse.includes('老师') || lowerResponse.includes('教师')) {
            // 老师身份
            setTimeout(() => {
                this.addMessage("AI", "了解了，您是老师。请告诉我您想评测的学生信息。");

                setTimeout(() => {
                    this.addMessage("AI", "请提供学生的姓名、性别、年龄和年级，这将帮助我提供更准确的评测。");

                    // 设置状态为等待教师输入学生信息
                    this.setData({
                        askingForIdentity: false,
                        askingForStudentInfo: true,
                        userRole: 'teacher'
                    });
                }, 1000);
            }, 1000);
        } else if (lowerResponse.includes('家长') || lowerResponse.includes('父母') ||
                   lowerResponse.includes('爸爸') || lowerResponse.includes('妈妈')) {
            // 家长身份
            setTimeout(() => {
                this.addMessage("AI", "了解了，您是家长。请告诉我您孩子的信息。");

                setTimeout(() => {
                    this.addMessage("AI", "请提供您孩子的姓名、性别、年龄和年级，这将帮助我提供更准确的评测。");

                    // 设置状态为等待家长输入孩子信息
                    this.setData({
                        askingForIdentity: false,
                        askingForStudentInfo: true,
                        userRole: 'parent'
                    });
                }, 1000);
            }, 1000);
        } else {
            // 无法判断身份，默认为学生
            setTimeout(() => {
                this.addMessage("AI", "抱歉，我没能清楚理解您的身份。为了提供更准确的评测，我将默认您为学生，请先完成个人信息填写。请在即将打开的页面中填写您的个人信息，完成后点击\"开始测评\"按钮。");

                // 设置用户角色
                this.setData({
                    userRole: 'student',
                    sending: false
                });

                // 延迟4秒后跳转，给用户足够的时间阅读消息
                setTimeout(() => {
                    // 在全局存储当前页面实例
                    getApp().globalData = getApp().globalData || {};
                    getApp().globalData.indexPage = this;

                    // 跳转到学生身份确认页面
                    wx.navigateTo({
                        url: '/pages/assessment/identity_confirm_enhanced_v2',
                        success: () => {
                            console.log('成功跳转到身份确认页面');
                            // 不再在这里添加消息，因为用户已经跳转到新页面看不到
                        },
                        fail: (err) => {
                            console.error('跳转到身份确认页面失败:', err);
                            // 跳转失败时提示用户
                            this.addMessage("AI", "抱歉，无法打开身份信息页面。我们将直接开始测评。");
                            // 直接开始提问
                            this.startFirstQuestion();
                        }
                    });
                }, 4000); // 延迟4秒
            }, 1000);
        }
    },

    // 处理学生身份确认后的回调 (修改后的版本)
    handleStudentInfoConfirmed(studentInfo) {
        // 保存学生信息
        if (studentInfo) {
            wx.setStorageSync('userInfo', studentInfo);
            this.setData({
                userInfo: studentInfo,
                hasUserInfo: true
            });
        }

        // 加载对应年级的题库
        const grade = studentInfo && studentInfo.grade ? studentInfo.grade : '初一';
        console.log('收到的年级信息:', grade, '类型:', typeof grade);

        // 确保年级识别正确
        const gradeNumber = this.extractGradeNumber(grade);
        console.log('识别的年级数字:', gradeNumber);

        const gradeQuestions = this.loadGradeQuestions(grade);

        this.setData({
            currentGradeQuestions: gradeQuestions
        });

        // 开始测评
        this.startGradeAssessment();
    },

    // 新增：直接开始第一个问题（当无法跳转到身份确认页面时使用）
    startFirstQuestion() {
        console.log('直接开始第一个问题流程');

        // 处理路由问题 - 使用安全的方式
        try {
            const currentRoute = 'pages/index/index';
            const app = getApp();
            if (app && typeof app._fixRouteIssue === 'function') {
                app._fixRouteIssue(currentRoute);
            }
            console.log('页面路径设置为:', currentRoute);
        } catch (e) {
            console.log('修复路由问题尝试失败，忽略此错误', e);
        }

        // 创建默认用户信息
        const defaultUserInfo = {
            name: '用户',
            gender: '未知',
            grade: '初一',
            age: ''
        };

        // 保存默认用户信息
        wx.setStorageSync('userInfo', defaultUserInfo);
        this.setData({
            userInfo: defaultUserInfo,
            hasUserInfo: true
        });

        // 加载对应年级的题库
        const defaultGradeQuestions = this.loadGradeQuestions('初一');

        this.setData({
            currentGradeQuestions: defaultGradeQuestions
        });

        // 开始测评
        this.startGradeAssessment();
    },

    // 开始年级测评
    startGradeAssessment() {
        console.log('开始年级测评，使用题库:', this.data.currentGradeQuestions);

        // 检查题库是否加载
        if (!this.data.currentGradeQuestions || this.data.currentGradeQuestions.length === 0) {
            console.error('测评题库为空，无法开始测评');
            this.addMessage("AI", "抱歉，未能加载测评题库，请稍后再试。");
            return;
        }

        this.setData({
            currentQuestionIndex: 0,
            scores: {},
            dimensions: {},
            assessmentComplete: false,
            // 重要：确保清除身份询问和学生信息询问的状态
            askingForIdentity: false,
            askingForStudentInfo: false,
            // 确保设置用户准备状态为true
            userReady: true,
            waitingForReadyConfirmation: false
        });

        // 添加测评开始提示
        this.addMessage("AI", "谢谢您提供的信息。现在我们开始针对您年级的心理健康测评。");

        setTimeout(() => {
            this.addMessage("AI", "请认真回答每个问题，选择最符合您实际情况的选项（输入选项字母A/B/C/D/E或对应数字1/2/3/4/5）。");

            setTimeout(() => {
                // 提问第一个问题
                this.askCurrentQuestion();
            }, 1000);
        }, 1000);
    },

    // 提问当前问题
    askCurrentQuestion() {
        const { currentGradeQuestions, currentQuestionIndex } = this.data;

        console.log(`提问问题 ${currentQuestionIndex + 1}/${currentGradeQuestions.length}`);

        // 修复：确保currentGradeQuestions存在且不为空
        if (!currentGradeQuestions || currentGradeQuestions.length === 0) {
            console.error('评估题库为空，无法继续测评');
            this.addMessage("AI", "抱歉，测评题库出现问题，请尝试重新开始测评。");
            this.setData({ sending: false });
            return;
        }

        if (currentQuestionIndex >= currentGradeQuestions.length) {
            // 所有问题已经完成
            console.log('所有问题已完成，生成评估结果');
            this.completeAssessment();
            return;
        }

        const currentQuestion = currentGradeQuestions[currentQuestionIndex];

        if (!currentQuestion) {
            console.error('当前问题为空');
            this.completeAssessment();
            return;
        }

        // 重要：确保在问题阶段正确设置状态
        this.setData({
            askingForIdentity: false,
            askingForStudentInfo: false,
            userReady: true,
            waitingForReadyConfirmation: false
        });

        // 获取问题内容
        let questionText = '';

        // 尝试从不同字段获取问题内容
        if (currentQuestion.content) {
            questionText = currentQuestion.content;
        } else if (currentQuestion.text) {
            questionText = currentQuestion.text;
        } else if (currentQuestion.question) {
            questionText = currentQuestion.question;
        } else {
            questionText = "此题缺少问题内容";
            console.error('问题缺少内容:', currentQuestion);
        }

        // 获取选项
        let options = [];
        if (currentQuestion.options && Array.isArray(currentQuestion.options)) {
            // 选项可能是字符串数组或对象数组
            options = currentQuestion.options.map(option => {
                if (typeof option === 'string') {
                    return option;
                } else if (option.text) {
                    return option.text;
                } else if (option.content) {
                    return option.content;
                } else if (option.label) {
                    return option.label;
                } else {
                    return "选项内容缺失";
                }
            });
        } else {
            console.error('问题缺少选项:', currentQuestion);
            options = ["选项A", "选项B", "选项C"]; // 提供默认选项以防止崩溃
        }

        // 生成带字母标签的选项
        const optionLabels = ['A', 'B', 'C', 'D', 'E'];

        // 构建带选项的问题文本
        let fullQuestionText = `问题 ${currentQuestionIndex + 1}/${currentGradeQuestions.length}：${questionText}\n\n`;

        options.forEach((option, index) => {
            if (index < optionLabels.length) {
                fullQuestionText += `${optionLabels[index]}. ${option}\n`;
            }
        });

        // 保存当前选项对应的标签
        this.setData({
            currentOptionLabels: optionLabels.slice(0, options.length)
        });

        console.log('发送问题:', fullQuestionText);

        // 发送问题
        this.addMessage("AI", fullQuestionText);

        // 设置可以输入回答
        this.setData({ sending: false });
    },

    // 处理用户对选择题的回答
    handleOptionSelection(response) {
        const { currentGradeQuestions, currentQuestionIndex, currentOptionLabels } = this.data;

        // 修复：确保题库和当前问题索引有效
        if (!currentGradeQuestions || currentGradeQuestions.length === 0) {
            console.error('评估题库为空，无法处理选项');
            this.addMessage("AI", "抱歉，测评遇到问题，请尝试重新开始。");
            this.setData({ sending: false });
            return;
        }

        if (currentQuestionIndex >= currentGradeQuestions.length) {
            console.error('问题索引超出范围，无法处理选项');
            this.completeAssessment();
            return;
        }

        const currentQuestion = currentGradeQuestions[currentQuestionIndex];

        if (!currentQuestion) {
            console.error('当前问题为空，无法处理选项');
            return;
        }

        console.log('处理选项回答:', response);
        console.log('当前问题:', currentQuestion);
        console.log('当前问题选项标签:', currentOptionLabels);

        // 尝试找出用户选择的选项
        const upperResponse = response.toUpperCase().trim();
        let selectedOptionIndex = -1;

        // 添加更全面的选项匹配逻辑
        // 1. 直接匹配字母（A, B, C...）和小写字母（a, b, c...）
        // 2. 匹配"选A", "选B"等格式
        // 3. 匹配完整的选项文本
        for (let i = 0; i < currentOptionLabels.length; i++) {
            if (upperResponse === currentOptionLabels[i] ||
                upperResponse.startsWith(currentOptionLabels[i] + '.') ||
                upperResponse.startsWith(currentOptionLabels[i] + '、') ||
                upperResponse.includes(currentOptionLabels[i]) ||
                upperResponse.includes('选' + currentOptionLabels[i]) ||
                upperResponse.includes('选择' + currentOptionLabels[i])) {

                selectedOptionIndex = i;
                console.log('匹配到选项:', currentOptionLabels[i], '索引:', i);
                break;
            }
        }

        // 如果没有找到匹配的选项，但回答只有一个字符，尝试按顺序解释
        if (selectedOptionIndex === -1 && response.length === 1) {
            const lowerChar = response.toLowerCase();
            const upperChar = response.toUpperCase();

            // 匹配字母 a-e 或 A-E
            const letterIndex = 'ABCDE'.indexOf(upperChar);
            if (letterIndex >= 0 && letterIndex < currentOptionLabels.length) {
                selectedOptionIndex = letterIndex;
                console.log('通过字母匹配到选项:', upperChar, '索引:', letterIndex);
            }
        }

        // 如果没有找到匹配的选项，尝试匹配数字 1-5 对应 A-E
        if (selectedOptionIndex === -1 && response.length === 1) {
            const index = '12345'.indexOf(response);
            if (index >= 0 && index < currentOptionLabels.length) {
                selectedOptionIndex = index;
                console.log('通过数字匹配到选项:', response, '索引:', index);
            }
        }

        // 如果没有找到匹配的选项
        if (selectedOptionIndex === -1) {
            // 提示用户重新选择
            console.log('未匹配到选项，请求用户重新选择');
            this.addMessage("AI", "请选择一个有效的选项（输入选项前的字母A/B/C/D/E或a/b/c/d/e，或者数字1/2/3/4/5）。");
            this.setData({ sending: false });
            return;
        }

        // 获取选中选项的分数
        let score = 0;

        // 根据题目类型获取分数
        if (currentQuestion.scores && Array.isArray(currentQuestion.scores)) {
            // 使用题目直接提供的分数数组
            if (selectedOptionIndex < currentQuestion.scores.length) {
                score = currentQuestion.scores[selectedOptionIndex];
                console.log('直接使用题目分数:', score);
            }
        } else if (currentQuestion.options && Array.isArray(currentQuestion.options)) {
            // 检查选项是否包含分数信息
            const selectedOption = currentQuestion.options[selectedOptionIndex];
            if (selectedOption && selectedOption.score !== undefined) {
                score = selectedOption.score;
                console.log('使用选项中的分数:', score);
            }
        }

        // 兜底：如果无法获取分数，使用默认分数
        if (score === 0 && currentQuestion.defaultScores) {
            score = currentQuestion.defaultScores[selectedOptionIndex] || 0;
            console.log('使用默认分数:', score);
        }

        console.log('最终使用的分数:', score);

        // 记录分数
        const { scores, dimensions } = this.data;
        scores[currentQuestion.id || `q_${currentQuestionIndex}`] = score;

        // 更新维度得分
        const dimension = currentQuestion.dimension || 'general';
        if (!dimensions[dimension]) {
            dimensions[dimension] = {
                scores: [score],
                total: score,
                count: 1
            };
        } else {
            dimensions[dimension].scores.push(score);
            dimensions[dimension].total += score;
            dimensions[dimension].count += 1;
        }

        this.setData({
            scores,
            dimensions
        });

        // 确认用户选择
        this.addMessage("AI", `您选择了选项 ${currentOptionLabels[selectedOptionIndex]}。`);

        // 移动到下一题
        setTimeout(() => {
            this.setData({
                currentQuestionIndex: currentQuestionIndex + 1
            });

            // 提问下一题
            setTimeout(() => {
                this.askCurrentQuestion();
            }, 1000);
        }, 1000);
    },

    // 处理用户没准备好的情况
    handleUserNotReady() {
        setTimeout(() => {
            this.addMessage("AI", "没关系，我们可以等你准备好再开始。等你准备好了，可以告诉我\"我准备好了\"或\"开始吧\"。");

            // 保持等待用户确认状态
            this.setData({
                sending: false
            });
        }, 1000);
    },

    // 再次询问用户是否准备好
    askForReadinessAgain() {
        setTimeout(() => {
            this.addMessage("AI", "不太确定你是否准备好了。如果准备好了，请告诉我\"开始\"或\"准备好了\"；如果还没准备好，可以说\"还没准备好\"或\"等一下\"。");

            this.setData({
                sending: false
            });
        }, 1000);
    },

    // 智能后续对话处理
    intelligentFollowUp(response, questionIndex) {
        const sentimentResult = this.analyzeSentiment(response);
        const mainKeyword = this.extractKeywords(response);

        setTimeout(() => {
            // 生成更自然的跟进反馈
            const feedback = this.generatePersonalizedFeedback(response, sentimentResult, mainKeyword, questionIndex);
            this.addMessage("AI", feedback);

            setTimeout(() => {
                // 进入下一个问题
                this.moveToNextQuestion(questionIndex);
            }, 2000);
        }, 1500);
    },

    // 生成个性化反馈
    generatePersonalizedFeedback(response, sentiment, keyword, questionIndex) {
        const { questions } = this.data;
        const question = questions[questionIndex];

        // 基于情感的反馈模板
        const feedbackTemplates = {
            positive: [
                "谢谢你的分享！听到你有这种积极的体验真不错。",
                "很高兴听到这个！你的积极态度很棒。",
                "这种积极的想法很有帮助，谢谢你愿意分享。"
            ],
            negative: [
                "我理解这对你来说可能不容易，谢谢你的坦诚分享。",
                "听起来你遇到了一些挑战，希望通过聊天能帮你梳理一下想法。",
                "这确实可能会让人感到困扰，你能分享出来很勇敢。"
            ],
            neutral: [
                "谢谢你的回答，这有助于我更好地了解你的情况。",
                "明白了，你的分享对我们的对话很有帮助。",
                "谢谢你的想法，这让我们的对话更有意义。"
            ]
        };

        // 根据维度添加专业建议
        const adviceByDimension = {
            emotion: "情绪起伏是很正常的事情，重要的是学会识别和接纳它们。",
            physical: "良好的睡眠和身体健康是心理健康的基础。",
            attention: "集中注意力有时候确实不容易，可以尝试把大任务分解成小步骤。",
            social: "人际关系中，表达自己的同时尊重他人很重要。",
            stress: "学习一些应对压力的方法，比如深呼吸或与朋友交流，都会很有帮助。",
            self: "每个人都是独特且有价值的，发现并欣赏自己的优点很重要。",
            academic: "学习是一个过程，找到适合自己的学习方法比结果更重要。",
            family: "家人之间有时会有不理解，坦诚沟通通常能解决很多问题。",
            coping: "当面对困难时，寻求帮助是明智和勇敢的选择。"
        };

        // 根据情感状态选择模板
        let emotionType = "neutral";
        if (sentiment > 0) emotionType = "positive";
        if (sentiment < 0) emotionType = "negative";

        const templates = feedbackTemplates[emotionType];
        const randomFeedback = templates[Math.floor(Math.random() * templates.length)];

        // 如果有关键词和维度建议，添加相应建议
        let dimensionAdvice = "";
        if (question && question.dimension && adviceByDimension[question.dimension]) {
            dimensionAdvice = " " + adviceByDimension[question.dimension];
        }

        // 返回组合后的反馈
        return randomFeedback + dimensionAdvice;
    },

    // 移动到下一个问题 (更新版)
    moveToNextQuestion(currentIndex) {
        const { questions, userInfo } = this.data;

        // 更新问题索引
        this.setData({
            currentQuestionIndex: currentIndex + 1,
            sending: false
        });

        // 提出下一个问题
        setTimeout(() => {
            // 修复：检查下一个问题是否存在
            if (currentIndex + 1 >= questions.length) {
                console.log('问题索引超出范围，转为使用评估问题');
                this.askCurrentQuestion(); // 使用测评问题流程
                return;
            }

            const nextQuestion = questions[currentIndex + 1];

            if (!nextQuestion) {
                console.log('下一个问题对象不存在，转为使用评估问题');
                this.askCurrentQuestion(); // 使用测评问题流程
                return;
            }

            // 根据用户年龄段选择问题措辞
            let questionText = nextQuestion.text; // 默认使用标准问题

            // 如果有用户信息，判断年龄段
            if (userInfo && userInfo.grade) {
                // 对低年级学生使用简化版问题
                if (userInfo.grade <= 6) {
                    questionText = nextQuestion.simplifiedText || nextQuestion.text;
                }
            } else {
                // 没有用户信息时，尝试通过对话内容判断年龄段
                // 这里可以添加简单的文本分析逻辑
                const { messages } = this.data;
                let userTexts = '';

                // 收集用户的所有回复
                messages.forEach(msg => {
                    if (msg.role === 'User') {
                        userTexts += msg.content + ' ';
                    }
                });

                // 简单检测用户是否是低年级学生
                const lowerGradeIndicators = ['小学', '三年级', '四年级', '五年级', '六年级', '语文', '数学', '英语', '老师', '爸爸妈妈'];
                let isLikelyLowerGrade = false;

                for (const indicator of lowerGradeIndicators) {
                    if (userTexts.includes(indicator)) {
                        isLikelyLowerGrade = true;
                        break;
                    }
                }

                // 如果可能是低年级学生，使用简化版问题
                if (isLikelyLowerGrade) {
                    questionText = nextQuestion.simplifiedText || nextQuestion.text;
                }
            }

            this.addMessage("AI", questionText);
        }, 1500);
    },

    // 完成评估对话
    completeAssessment() {
        console.log('完成测评，开始生成结果');
        console.log('评分情况:', this.data.scores);
        console.log('维度得分:', this.data.dimensions);

        // 确保状态正确设置
        this.setData({
            askingForIdentity: false,
            askingForStudentInfo: false,
            userReady: true,
            waitingForReadyConfirmation: false,
            assessmentComplete: true
        });

        setTimeout(() => {
            this.addMessage("AI", "谢谢你的回答！我已经收集到足够的信息了。");

            setTimeout(() => {
                this.addMessage("AI", "我正在分析你的回答，请稍等...");

                setTimeout(() => {
                    this.generateAssessmentResult();
                    this.setData({
                        sending: false
                    });
                }, 2000);
            }, 1500);
        }, 1500);
    },

    // 处理用户回答 - 改进版
    processUserResponse(response, questionIndex) {
        const { questions, scores, dimensions } = this.data;
        const question = questions[questionIndex];

        if (!question) return;

        // 计算这个回答的得分
        const sentiment = this.analyzeSentiment(response);
        const score = sentiment > 0 ? 8 : sentiment < 0 ? 3 : 5;

        // 保存问题得分
        scores[question.id] = score;

        // 更新维度得分
        if (!dimensions[question.dimension]) {
            dimensions[question.dimension] = {
                scores: [score],
                total: score,
                count: 1
            };
        } else {
            dimensions[question.dimension].scores.push(score);
            dimensions[question.dimension].total += score;
            dimensions[question.dimension].count += 1;
        }

        this.setData({
            scores,
            dimensions
        });
    },

    // 改进的情感分析函数 - 针对10-18岁学生
    analyzeSentiment(text) {
        // 更丰富的情感词汇库，适合青少年学生表达方式
        const emotionWords = {
            positive: ['好', '开心', '快乐', '满意', '愉快', '舒适', '顺利', '轻松', '感谢', '幸福', '喜欢', '期待',
                      '高兴', '酷', '赞', '棒', '厉害', '奖励', '成功', '有趣', '开玩笑', '兴奋', '哈哈'],
            negative: ['差', '不好', '困难', '痛苦', '焦虑', '压力', '紧张', '失眠', '担心', '烦恼', '害怕', '疲惫',
                      '沮丧', '难过', '讨厌', '烦', '枯燥', '无聊', '生气', '难受', '失望', '伤心', '惩罚', '挂科', '考砸'],
            neutral: ['一般', '还行', '普通', '平常', '正常', '凑合', '还好', '马马虎虎', '将就', '一般般']
        };

        // 计算各情感分值
        let scores = {
            positive: 0,
            negative: 0,
            neutral: 0
        };

        // 匹配词汇，计算得分
        for (const [emotion, words] of Object.entries(emotionWords)) {
            for (const word of words) {
                if (text.includes(word)) {
                    scores[emotion] += 1;
                }
            }
        }

        // 如果只需要简单的分数，返回以下结果
        if (scores.positive > scores.negative) return 1;
        if (scores.negative > scores.positive) return -1;
        return 0;
    },

    // 改进的关键词提取 - 针对学生关注的话题
    extractKeywords(text) {
        // 学生关键词库
        const keywordCategories = {
            // 学习相关
            academic: ['学习', '考试', '作业', '成绩', '课程', '老师', '学校', '分数', '补习', '课外班',
                      '竞赛', '考砸', '挂科', '不及格', '及格', '优秀', '良好', '不会', '难题', '题目'],
            // 情绪类
            emotion: ['情绪', '心情', '感受', '感觉', '喜欢', '讨厌', '开心', '难过', '焦虑', '压力',
                     '烦躁', '烦恼', '生气', '愤怒', '失望', '伤心', '紧张', '害怕'],
            // 睡眠与健康
            health: ['睡眠', '失眠', '睡不着', '早醒', '做梦', '疲劳', '休息', '生病', '头痛', '不舒服',
                     '熬夜', '困', '累'],
            // 社交关系
            social: ['朋友', '同学', '好友', '关系', '交流', '孤独', '沟通', '吵架', '误会', '和好',
                    '玩耍', '游戏', '聚会'],
            // 家庭关系
            family: ['家人', '父母', '爸爸', '妈妈', '爷爷', '奶奶', '姐姐', '哥哥', '弟弟', '妹妹',
                    '家庭', '管教', '约束', '责骂']
        };

        // 提取出现的关键词和类别
        for (const [category, keywords] of Object.entries(keywordCategories)) {
            for (const keyword of keywords) {
                if (text.includes(keyword)) {
                    return keyword; // 返回找到的第一个关键词
                }
            }
        }

        return null; // 没有找到关键词
    },

    // 生成评估结果
    generateAssessmentResult() {
        const { dimensions } = this.data;
        const dimensionResults = {};
        let totalScore = 0;
        let dimensionCount = 0;

        // 计算每个维度的得分
        Object.keys(dimensions).forEach(key => {
            const dimension = dimensions[key];
            const averageScore = dimension.total / dimension.count;
            const normalizedScore = Math.round(averageScore * 10); // 0-100分

            dimensionResults[key] = {
                key,
                name: this.getDimensionName(key),
                score: normalizedScore,
                level: this.determineRiskLevel(normalizedScore),
                color: this.getColorForScore(normalizedScore)
            };

            totalScore += normalizedScore;
            dimensionCount++;
        });

        // 计算总分
        const overallScore = Math.round(totalScore / dimensionCount);
        const alertLevel = this.generateAlertLevel(overallScore);

        // 生成改善建议
        const suggestions = this.generateSuggestions(dimensionResults, overallScore);

        const result = {
            dimensions: Object.values(dimensionResults),
            totalScore: overallScore,
            alertLevel,
            alertColor: this.getColorForScore(overallScore),
            suggestions,
            timestamp: new Date().toISOString()
        };

        // 保存结果
        this.saveAssessmentResult(result);

        // 向用户展示结果摘要
        this.presentResultSummary(result);
    },

    // 向用户展示结果摘要
    presentResultSummary(result) {
        // 总体情况
        const overallMessage = `根据我们的对话，你的总体心理健康评分为${result.totalScore}分，属于"${result.alertLevel.name}"级别。`;
        this.addMessage("AI", overallMessage);

        // 维度分析
        setTimeout(() => {
            // 找出得分最高和最低的维度
            const sortedDimensions = [...result.dimensions].sort((a, b) => b.score - a.score);
            const highest = sortedDimensions[0];
            const lowest = sortedDimensions[sortedDimensions.length - 1];

            const strengthMessage = `你在${highest.name}方面表现良好，得分为${highest.score}分。`;
            const improvementMessage = `但在${lowest.name}方面可能需要一些关注，得分为${lowest.score}分。`;

            this.addMessage("AI", strengthMessage + " " + improvementMessage);

            // 建议
            setTimeout(() => {
                const suggestionIntro = "以下是一些可能对你有帮助的建议:";
                this.addMessage("AI", suggestionIntro);

                // 发送首条建议
                setTimeout(() => {
                    if (result.suggestions.student && result.suggestions.student.length > 0) {
                        this.addMessage("AI", "• " + result.suggestions.student[0].content);
                    }

                    // 提供完整报告选项
                    setTimeout(() => {
                        this.addMessage("AI", "我可以为你生成一份完整的报告，或者我们可以继续聊聊你关心的特定问题。你想了解更多关于哪个方面的信息？");
                    }, 1500);
                }, 1500);
            }, 2000);
        }, 2000);
    },

    // 获取维度名称
    getDimensionName(dimensionKey) {
        const dimensionNames = {
            'emotion': '情绪状态',
            'physical': '身体健康',
            'attention': '注意力',
            'social': '社交关系',
            'stress': '压力管理',
            'self': '自我认知',
            'cognition': '认知能力',
            'academic': '学业表现',
            'family': '家庭关系',
            'coping': '应对能力'
        };

        return dimensionNames[dimensionKey] || dimensionKey;
    },

    // 确定风险等级
    determineRiskLevel(score) {
        if (score >= 80) return 'normal'; // 正常
        if (score >= 60) return 'mild'; // 轻度
        if (score >= 40) return 'moderate'; // 中度
        return 'severe'; // 严重
    },

    // 获取分数对应的颜色
    getColorForScore(score) {
        if (score >= 80) return '#4CAF50'; // 绿色，正常
        if (score >= 60) return '#8BC34A'; // 黄绿色，轻度
        if (score >= 40) return '#FFC107'; // 黄色，中度
        if (score >= 20) return '#FF5722'; // 橙色，高度
        return '#F44336'; // 红色，严重
    },

    // 生成警告级别
    generateAlertLevel(score) {
        const levels = {
            'normal': {
                name: '正常',
                description: '你的心理健康状况良好，能够有效应对日常生活中的压力和挑战。'
            },
            'mild': {
                name: '轻度风险',
                description: '你可能正在经历一些轻微的压力或情绪波动，但总体上能够保持平衡。'
            },
            'moderate': {
                name: '中度风险',
                description: '你目前面临的心理困扰可能影响到了日常生活和表现，建议寻求一些支持。'
            },
            'severe': {
                name: '严重风险',
                description: '你可能正在经历较为严重的心理困扰，建议尽快寻求专业心理咨询师的帮助。'
            }
        };

        const level = this.determineRiskLevel(score);
        return levels[level];
    },

    // 生成维度分析
    generateAnalysis(dimensionKey, level, score) {
        const analysisTemplates = {
            'emotion': {
                'normal': '你的情绪状态良好，能够保持积极的心态和情绪稳定性。',
                'mild': '你的情绪大体稳定，偶尔会有一些波动，但不影响日常生活。',
                'moderate': '你的情绪波动较大，有时可能感到沮丧或焦虑，这可能影响你的日常体验。',
                'severe': '你目前的情绪状态可能较为低落，经常感到沮丧、焦虑或无助。'
            },
            'physical': {
                'normal': '你拥有良好的睡眠质量和身体健康状况。',
                'mild': '你的身体状况总体良好，可能偶尔有些睡眠问题。',
                'moderate': '你的睡眠质量和身体状况受到一定影响，可能感到疲惫或精力不足。',
                'severe': '你可能经常感到疲惫，有持续的睡眠问题或其他身体不适。'
            },
            'attention': {
                'normal': '你的注意力和专注能力表现良好，能够有效地完成任务。',
                'mild': '你的注意力大多数时候能够保持集中，偶尔会分心。',
                'moderate': '你的注意力容易分散，可能影响学习和工作效率。',
                'severe': '你很难保持注意力集中，这可能严重影响你的学习和工作表现。'
            },
            'social': {
                'normal': '你拥有健康的社交关系，能够与他人建立良好的连接。',
                'mild': '你的社交状况总体良好，可能在某些社交场合感到不适。',
                'moderate': '你的社交互动可能存在一些困难，感到社交压力或孤独感。',
                'severe': '你可能经常感到孤独或与他人隔离，社交互动存在较大困难。'
            },
            'stress': {
                'normal': '你能够有效地管理压力，保持心理平衡。',
                'mild': '你的压力水平在可控范围内，偶尔会感到压力较大。',
                'moderate': '你面临较多压力，有时可能感到难以应对。',
                'severe': '你可能经常感到高度紧张和压力，难以找到有效的缓解方法。'
            }
        };

        // 如果没有特定模板，使用通用模板
        if (!analysisTemplates[dimensionKey]) {
            const genericTemplates = {
                'normal': `你在${this.getDimensionName(dimensionKey)}方面表现良好。`,
                'mild': `你在${this.getDimensionName(dimensionKey)}方面的状况总体良好，有一些小的改进空间。`,
                'moderate': `你在${this.getDimensionName(dimensionKey)}方面可能面临一些挑战，值得关注和改进。`,
                'severe': `你在${this.getDimensionName(dimensionKey)}方面可能存在较大困难，建议寻求相关支持。`
            };
            return genericTemplates[level];
        }

        return analysisTemplates[dimensionKey][level];
    },

    // 生成改善建议
    generateImprovement(dimensionKey, level, score) {
        const improvementTemplates = {
            'emotion': {
                'normal': '继续保持积极的心态，定期进行自我反思和情绪觉察。',
                'mild': '尝试通过冥想、深呼吸等放松技巧来保持情绪稳定。注意识别可能引发情绪波动的因素。',
                'moderate': '规律地进行记录情绪日记，识别情绪触发因素。尝试寻找健康的情绪宣泄渠道，如运动、艺术创作等。',
                'severe': '建议寻求专业心理咨询师的帮助，学习情绪调节技巧。制定日常情绪管理计划，包括放松练习和正念冥想。'
            },
            'physical': {
                'normal': '继续保持健康的生活方式，包括规律的睡眠和适当的运动。',
                'mild': '优化你的睡眠环境和习惯，如固定的睡眠时间、减少睡前使用电子设备等。',
                'moderate': '制定规律的睡眠计划，白天适当增加身体活动。考虑使用放松技巧来改善睡眠质量。',
                'severe': '咨询医生了解改善睡眠和身体状况的方法。建立健康的生活习惯，包括均衡饮食、规律运动和充足休息。'
            },
            'attention': {
                'normal': '使用时间管理技巧，如番茄工作法，来进一步提高工作和学习效率。',
                'mild': '尝试通过创建专注的环境来减少分心，如移除干扰源、设置特定的工作时段等。',
                'moderate': '运用专注力训练技巧，如短时间高质量的工作会话、适当休息等。考虑将大任务分解为小步骤。',
                'severe': '咨询专业人士了解注意力训练方法。建立结构化的日常工作或学习计划，逐步培养专注能力。'
            },
            'social': {
                'normal': '继续培养和维护你的社交关系，尝试拓展社交圈子。',
                'mild': '识别并参与你感兴趣的社交活动，逐步扩大你的社交圈。',
                'moderate': '练习社交技巧，如积极倾听、表达自己的需求和感受。通过小团体活动增加社交互动。',
                'severe': '从小步骤开始改善社交状况，如与一两个亲近的人深入交流。考虑参加社交技能训练或支持小组。'
            },
            'stress': {
                'normal': '继续使用有效的压力管理策略，如运动、爱好和社交活动。',
                'mild': '学习并实践放松技巧，如深呼吸、渐进式肌肉放松等。注意保持工作和生活的平衡。',
                'moderate': '识别主要压力源，制定应对策略。建立健康的自我照顾习惯，包括休息、运动和社交支持。',
                'severe': '寻求专业帮助制定压力管理计划。学习认知重构技巧来改变对压力事件的理解和反应。'
            }
        };

        // 如果没有特定模板，使用通用模板
        if (!improvementTemplates[dimensionKey]) {
            const genericTemplates = {
                'normal': `继续保持你在${this.getDimensionName(dimensionKey)}方面的良好状态。`,
                'mild': `考虑一些小的调整来进一步改善你的${this.getDimensionName(dimensionKey)}。`,
                'moderate': `制定具体计划来改善你的${this.getDimensionName(dimensionKey)}，可能需要一些额外的支持。`,
                'severe': `建议寻求专业帮助来改善你的${this.getDimensionName(dimensionKey)}状况。`
            };
            return genericTemplates[level];
        }

        return improvementTemplates[dimensionKey][level];
    },

    // 生成建议
    generateSuggestions(dimensions, totalScore) {
        const suggestions = {
            student: [],
            parent: [],
            teacher: []
        };

        // 获取得分最低的三个维度
        const dimensionList = Object.values(dimensions);
        const sortedDimensions = [...dimensionList].sort((a, b) => a.score - b.score);
        const lowestDimensions = sortedDimensions.slice(0, 3);

        // 为每个维度生成建议
        lowestDimensions.forEach(dimension => {
            suggestions.student.push({
                title: `改善${dimension.name}`,
                content: this.generateImprovement(dimension.key, dimension.level, dimension.score)
            });
        });

        // 添加一个总体建议
        const overallLevel = this.determineRiskLevel(totalScore);
        let overallSuggestion = '';

        if (overallLevel === 'normal') {
            overallSuggestion = '整体而言，你的心理健康状况良好。继续保持积极的生活态度，定期进行自我反思和调整。';
        } else if (overallLevel === 'mild') {
            overallSuggestion = '你可能正在经历一些压力或挑战。尝试保持规律的生活习惯，增加放松和休息的时间，与亲友分享你的感受。';
        } else if (overallLevel === 'moderate') {
            overallSuggestion = '你目前的心理健康状况需要一些关注。建议制定自我照顾计划，包括健康饮食、规律运动、充足睡眠和社交活动。考虑寻求亲友或专业人士的支持。';
        } else {
            overallSuggestion = '你的心理健康状况需要认真对待。强烈建议寻求专业心理咨询师的帮助，他们可以提供针对性的支持和干预。同时，告知你信任的亲友，获取他们的理解和帮助。';
        }

        suggestions.student.unshift({
            title: '总体建议',
            content: overallSuggestion
        });

        return suggestions;
    },

    // 保存评估结果
    saveAssessmentResult(result) {
        // 保存到本地
        this.saveResultToLocal(result);
        console.log('占卜结果已保存到本地存储');
    },

    // 保存结果到本地
    saveResultToLocal(result) {
        const history = wx.getStorageSync('assessment_history') || [];
        history.unshift(result);
        wx.setStorageSync('assessment_history', history);
    },

    // 重新开始评估
    restartAssessment() {
        this.setData({
            messages: [],
            inputValue: '',
            sending: false,
            typing: false,
            assessmentComplete: false,
            currentQuestionIndex: 0,
            scores: {},
            dimensions: {},
            lastMessageId: ''
        });

        setTimeout(() => {
            this.addMessage("AI", "让我们重新开始。你最近感觉怎么样？", "greeting");
        }, 500);
    },

    // 导航到个人资料页面 ("我"的页面)
    navigateToProfile() {
        wx.navigateTo({
            url: '/pages/profile/index',
            success: () => {
                console.log('成功跳转到个人中心页面');
            },
            fail: (err) => {
                console.error('跳转到个人中心页面失败:', err);
                wx.showToast({
                    title: '跳转失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },

    // 导航到历史评估报告页面
    navigateToHistory() {
        wx.navigateTo({
            url: '/pages/assessment-history/index',
            success: () => {
                console.log('成功跳转到历史评估报告页面');
            },
            fail: (err) => {
                console.error('跳转到历史评估报告页面失败:', err);
                wx.showToast({
                    title: '跳转失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },

    // 切换标签
    switchTab(e) {
        const tab = e.currentTarget.dataset.tab;

        // 如果切换到历史标签，直接跳转到历史评估报告页面
        if (tab === 'history') {
            this.navigateToHistory();
            return;
        }

        // 更新当前活动标签
        this.setData({
            activeTab: tab
        });
    },

    // 查看完整报告
    viewFullReport() {
        wx.navigateTo({
            url: '/pages/assessment-report/index'
        });
    },

    // 页面分享
    onShareAppMessage() {
        return {
            title: '心理健康评估助手',
            path: '/pages/index/index'
        };
    },

    // 根据用户年级获取对应题库中的问题
    loadGradeQuestions(grade) {
        let gradeNumber = this.extractGradeNumber(grade);
        let questions = [];
        let assessmentEngine = null;

        // 安全处理completeAssessmentData的函数，避免修改只读对象
        const safelyProcessData = (data) => {
            if (!data) {
                return [];
            }

            // 创建新的数组或使用现有数组
            if (typeof data === 'object' && !Array.isArray(data)) {
                return [data];
            } else if (Array.isArray(data)) {
                return [...data];
            }

            // 默认返回空数组
            return [];
        };

        console.log(`正在加载${grade}(${gradeNumber})年级的评估问题`);

        // 确保年级在有效范围内 (4-12)
        if (gradeNumber < 4) {
            console.warn(`年级${gradeNumber}低于支持的最小年级，使用4年级题目`);
            gradeNumber = 4;
        } else if (gradeNumber > 12) {
            console.warn(`年级${gradeNumber}高于支持的最大年级，使用12年级题目`);
            gradeNumber = 12;
        }

        // 重要：明确记录最终选择的年级
        console.log(`最终确定使用${gradeNumber}年级题库`);

        // 使用动态题库加载器获取完整题库
        const completeAssessmentData = assessmentDataDynamic.getPsychologicalAssessmentForGrade(gradeNumber);
        if (completeAssessmentData) {
            console.log(`成功加载${gradeNumber}年级的完整题库数据`);
        } else {
            console.error(`无法加载${gradeNumber}年级的完整题库数据，将尝试使用传统方法`);
        }

        // 根据年级选择对应测评引擎
        switch (gradeNumber) {
            case 4:
                assessmentEngine = grade4Engine;
                break;
            case 5:
                assessmentEngine = grade5Engine;
                break;
            case 6:
                assessmentEngine = grade6Engine;
                break;
            case 7:
                assessmentEngine = grade7Engine;
                break;
            case 8:
                assessmentEngine = grade8Engine;
                break;
            case 9:
                assessmentEngine = grade9Engine;
                break;
            case 10:
                assessmentEngine = grade10Engine;
                break;
            case 11:
                assessmentEngine = grade11Engine;
                break;
            case 12:
                assessmentEngine = grade12Engine;
                break;
            default:
                console.error(`无法获取年级${gradeNumber}的评估引擎`);
                return [];
        }

        // 确保引擎已加载
        if (!assessmentEngine) {
            console.error(`未找到${gradeNumber}年级的评估引擎`);
            return [];
        }

        // 使用引擎生成测评试卷
        try {
            console.log('测评引擎类型:', typeof assessmentEngine);
            console.log('测评引擎方法:', Object.keys(assessmentEngine));

            // 如果引擎是对象且有generateAssessment方法
            if (assessmentEngine.generateAssessment) {
                // 修正参数格式，确保externalAssessmentData是正确的格式
                // 检查completeAssessmentData的格式
                if (completeAssessmentData) {
                    console.log('completeAssessmentData类型:', typeof completeAssessmentData);
                    let processedData = completeAssessmentData;
                    if (typeof completeAssessmentData === 'object' && !Array.isArray(completeAssessmentData)) {
                        // 如果是单个对象，包装成数组
                        processedData = [completeAssessmentData];
                        console.log('将completeAssessmentData包装为数组');
                    }

                    // 使用测评引擎的generateAssessment方法生成测评试卷，传入完整题库
                    console.log('调用generateAssessment方法，传入参数:', {
                        season: '学期中',
                        totalQuestions: 50, // 修改：增加题目数量，确保有足够的题目
                        includeValidityQuestions: true,
                        externalAssessmentData: processedData
                    });

                    const assessment = assessmentEngine.generateAssessment({
                        season: '学期中', // 可以根据实际时间动态设置
                        totalQuestions: 50, // 修改：增加到50题，确保测评足够长
                        includeValidityQuestions: true, // 是否包含效度题
                        externalAssessmentData: processedData // 传入处理后的题库数据
                    });

                    if (assessment && assessment.questions && assessment.questions.length > 0) {
                        console.log(`成功使用引擎生成${gradeNumber}年级题库，共${assessment.questions.length}题`);
                        console.log('题目示例:', assessment.questions[0]);
                        return assessment.questions;
                    } else {
                        console.error(`引擎生成的题目为空，尝试使用备选方案`);
                    }
                }
                // 兼容处理：如果引擎是函数本身
                else if (typeof assessmentEngine === 'function') {
                    // 修正参数格式
                    let processedInnerData = completeAssessmentData;
                    if (completeAssessmentData) {
                        if (typeof completeAssessmentData === 'object' && !Array.isArray(completeAssessmentData)) {
                            processedInnerData = [completeAssessmentData]; // 使用新变量而不是直接修改
                        }
                    }

                    const assessment = assessmentEngine({
                        season: '学期中',
                        totalQuestions: 50,
                        includeValidityQuestions: true,
                        externalAssessmentData: processedInnerData // 使用新变量
                    });

                    if (assessment && assessment.questions && assessment.questions.length > 0) {
                        console.log(`成功调用引擎函数生成${gradeNumber}年级题库，共${assessment.questions.length}题`);
                        return assessment.questions;
                    } else {
                        console.error(`引擎函数生成的题目为空，尝试使用备选方案`);
                    }
                }
                else {
                    console.error(`引擎${gradeNumber}没有generateAssessment方法，尝试使用备选方案`);
                }
            }
            // 兼容处理：如果引擎是函数本身
            else if (typeof assessmentEngine === 'function') {
                // 修正参数格式
                let processedFuncData = completeAssessmentData;
                if (completeAssessmentData) {
                    if (typeof completeAssessmentData === 'object' && !Array.isArray(completeAssessmentData)) {
                        processedFuncData = [completeAssessmentData];
                    }
                }

                const assessment = assessmentEngine({
                    season: '学期中',
                    totalQuestions: 50,
                    includeValidityQuestions: true,
                    externalAssessmentData: processedFuncData // 传入处理后的题库数据
                });

                if (assessment && assessment.questions && assessment.questions.length > 0) {
                    console.log(`成功调用引擎函数生成${gradeNumber}年级题库，共${assessment.questions.length}题`);
                    return assessment.questions;
                } else {
                    console.error(`引擎函数生成的题目为空，尝试使用备选方案`);
                }
            }
            else {
                console.error(`引擎${gradeNumber}没有generateAssessment方法，尝试使用备选方案`);
            }
        } catch (error) {
            console.error(`使用评估引擎生成题目失败:`, error);
        }

        // 如果引擎生成失败，尝试原始方式获取题目
        console.log(`尝试使用原始方式加载题目`);
        return this.loadGradeQuestionsOriginal(grade);
    },

    // 原始的加载题目方法作为备选
    loadGradeQuestionsOriginal(grade) {
        let gradeNumber = this.extractGradeNumber(grade);
        let questions = [];
        let assessmentModule = null;

        // 根据年级选择对应模块
        switch (gradeNumber) {
            case 4:
                assessmentModule = grade4Module;
                break;
            case 5:
                assessmentModule = grade5Module;
                break;
            case 6:
                assessmentModule = grade6Module;
                break;
            case 7:
                assessmentModule = grade7Module;
                break;
            case 8:
                assessmentModule = grade8Module;
                break;
            case 9:
                assessmentModule = grade9Module;
                break;
            case 10:
                assessmentModule = grade10Module;
                break;
            case 11:
                assessmentModule = grade11Module;
                break;
            case 12:
                assessmentModule = grade12Module;
                break;
            default:
                console.error(`无法获取年级${gradeNumber}的评估数据`);
                return [];
        }

        // 确保模块已加载
        if (!assessmentModule) {
            console.error(`未找到${gradeNumber}年级的评估模块`);
            return [];
        }

        console.log(`正在尝试从${gradeNumber}年级数据模块加载题目`);

        // 尝试不同的数据结构格式获取题目
        const gradeKey = `GRADE${gradeNumber}_ASSESSMENT`;

        // 新增：处理直接导出ASSESSMENT对象的情况
        if (assessmentModule && typeof assessmentModule === 'object') {
            // 直接检查模块结构

            // 1. 新增: 检查dimensions数组中的questions
            if (assessmentModule.dimensions && Array.isArray(assessmentModule.dimensions)) {
                console.log(`使用直接导出的dimensions数组获取题目`);

                assessmentModule.dimensions.forEach(dimension => {
                    if (dimension.questions && Array.isArray(dimension.questions)) {
                        console.log(`从维度${dimension.id}加载${dimension.questions.length}个题目`);
                        const dimensionQuestions = dimension.questions.map(q => ({
                            ...q,
                            dimension: dimension.id || q.dimension || 'general'
                        }));
                        questions = questions.concat(dimensionQuestions);
                    }
                });
            }

            // 2. 直接检查modules结构（直接导出的情况）
            else if (assessmentModule.modules && Array.isArray(assessmentModule.modules)) {
                console.log(`使用直接导出的modules格式获取题目`);

                assessmentModule.modules.forEach(module => {
                    if (module.questions && Array.isArray(module.questions)) {
                        const moduleQuestions = module.questions.map(q => ({
                            ...q,
                            dimension: module.id || module.dimension || 'general'
                        }));
                        questions = questions.concat(moduleQuestions);
                    }
                });
            }

            // 3. 直接检查gradeConfig.modules结构（直接导出的情况）
            else if (assessmentModule.gradeConfig && assessmentModule.gradeConfig.modules) {
                console.log(`使用直接导出的gradeConfig.modules格式获取题目`);

                assessmentModule.gradeConfig.modules.forEach(module => {
                    if (module.questions && Array.isArray(module.questions)) {
                        const moduleQuestions = module.questions.map(q => ({
                            ...q,
                            dimension: module.id || module.dimension || 'general'
                        }));
                        questions = questions.concat(moduleQuestions);
                    }
                });
            }

            // 4. 直接检查questions数组（直接导出的情况）
            else if (assessmentModule.questions && Array.isArray(assessmentModule.questions)) {
                console.log(`使用直接导出的questions数组获取题目`);
                questions = [...assessmentModule.questions];
            }

            // 5. 新增: 检查并添加效度题
            if (assessmentModule.validityQuestions && Array.isArray(assessmentModule.validityQuestions)) {
                console.log(`添加${assessmentModule.validityQuestions.length}个效度测试题目`);
                const validityQuestions = assessmentModule.validityQuestions.map(q => ({
                    ...q,
                    dimension: 'validity', // 标记为效度维度
                    isValidityQuestion: true // 添加效度题标识
                }));
                questions = questions.concat(validityQuestions);
            }
        }

        // 如果已经找到题目，返回
        if (questions.length > 0) {
            console.log(`已通过直接导出方式找到${questions.length}个题目`);
            return questions;
        }

        // 传统的命名导出格式（非直接导出）
        // 核心修复: 处理 GRADEX_ASSESSMENT.gradeConfig.modules 结构
        // 这是7年级题库使用的结构
        if (assessmentModule[gradeKey] && assessmentModule[gradeKey].gradeConfig && assessmentModule[gradeKey].gradeConfig.modules) {
            console.log(`使用${gradeKey}.gradeConfig.modules格式获取题目`);

            assessmentModule[gradeKey].gradeConfig.modules.forEach(module => {
                if (module.questions && Array.isArray(module.questions)) {
                    const moduleQuestions = module.questions.map(q => ({
                        ...q,
                        dimension: module.id || module.dimension || 'general'
                    }));
                    questions = questions.concat(moduleQuestions);
                }
            });
        }
        // 2. 检查GRADEX_ASSESSMENT.modules格式
        else if (assessmentModule[gradeKey] && assessmentModule[gradeKey].modules) {
            console.log(`使用${gradeKey}.modules格式获取题目`);

            // 遍历所有模块收集题目
            assessmentModule[gradeKey].modules.forEach(module => {
                if (module.questions && Array.isArray(module.questions)) {
                    const moduleQuestions = module.questions.map(q => ({
                        ...q,
                        dimension: module.id || module.dimension || 'general'
                    }));
                    questions = questions.concat(moduleQuestions);
                }
            });
        }

        // 3. 新增：检查GRADEX_ASSESSMENT.dimensions格式
        else if (assessmentModule[gradeKey] && assessmentModule[gradeKey].dimensions) {
            console.log(`使用${gradeKey}.dimensions格式获取题目`);

            assessmentModule[gradeKey].dimensions.forEach(dimension => {
                if (dimension.questions && Array.isArray(dimension.questions)) {
                    console.log(`从${gradeKey}.dimensions.${dimension.id}加载${dimension.questions.length}个题目`);
                    const dimensionQuestions = dimension.questions.map(q => ({
                        ...q,
                        dimension: dimension.id || q.dimension || 'general'
                    }));
                    questions = questions.concat(dimensionQuestions);
                }
            });
        }

        // 4. 新增: 检查GRADEX_ASSESSMENT.validityQuestions格式
        if (assessmentModule[gradeKey] && assessmentModule[gradeKey].validityQuestions) {
            console.log(`添加${gradeKey}.validityQuestions中的效度题`);
            const validityQuestions = assessmentModule[gradeKey].validityQuestions.map(q => ({
                ...q,
                dimension: 'validity', // 标记为效度维度
                isValidityQuestion: true // 添加效度题标识
            }));
            questions = questions.concat(validityQuestions);
        }

        console.log(`原始方式加载题库，共${questions.length}个问题`);
        return questions;
    },

    // 从年级文本中提取数字
    extractGradeNumber(gradeText) {
        // 打印原始年级文本，便于调试
        console.log('提取年级数字，原始文本:', gradeText);

        // 针对不同格式的年级文本提取数字
        if (!gradeText) {
            console.log('年级文本为空，使用默认值7(初一)');
            return 7; // 默认初一
        }

        // 检查是否包含"小学"关键词
        const isPrimary = gradeText.includes('小学');

        // 直接是数字的情况
        if (!isNaN(gradeText)) {
            const num = parseInt(gradeText);
            console.log(`数字格式年级: ${num}`);
            return num;
        }

        // 处理"x年级"格式
        const gradeMatch = gradeText.match(/(\d+)年级/);
        if (gradeMatch) {
            const num = parseInt(gradeMatch[1]);

            // 确保小于7的数字被识别为小学年级
            if (num <= 6) {
                console.log(`匹配到${num}年级，识别为小学${num}年级`);
                return num; // 直接返回数字，表示小学年级
            } else {
                console.log(`匹配到${num}年级，识别为中学年级`);
                return num;
            }
        }

        // 处理"小学x年级"格式
        const primaryMatch = gradeText.match(/小学(\d+)年级/);
        if (primaryMatch) {
            const num = parseInt(primaryMatch[1]);
            console.log(`匹配到小学${num}年级`);
            return num;
        }

        // 处理"六年级"等含中文数字的年级
        const chineseNumbers = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9};
        for (const [chinese, number] of Object.entries(chineseNumbers)) {
            // 匹配"六年级"这种格式
            if (gradeText.includes(`${chinese}年级`)) {
                if (isPrimary || number <= 6) {
                    console.log(`匹配到${chinese}年级，识别为小学${number}年级`);
                    return number; // 返回数字表示小学年级
                }
            }
        }

        // 处理"初x"或"高x"格式
        const juniorMatch = gradeText.match(/初(\d+)/);
        if (juniorMatch) {
            const num = 6 + parseInt(juniorMatch[1]);
            console.log(`匹配到初${juniorMatch[1]}，识别为${num}年级`);
            return num; // 初一 = 7年级
        }

        const seniorMatch = gradeText.match(/高(\d+)/);
        if (seniorMatch) {
            const num = 9 + parseInt(seniorMatch[1]);
            console.log(`匹配到高${seniorMatch[1]}，识别为${num}年级`);
            return num; // 高一 = 10年级
        }

        // 处理中文数字(初中/高中)
        for (const [chinese, number] of Object.entries(chineseNumbers)) {
            if (gradeText.includes(`小学${chinese}年级`)) {
                console.log(`匹配到小学${chinese}年级，识别为${number}年级`);
                return number;
            }
            if (gradeText.includes(`初${chinese}`)) {
                const num = 6 + number;
                console.log(`匹配到初${chinese}，识别为${num}年级`);
                return num;
            }
            if (gradeText.includes(`高${chinese}`)) {
                const num = 9 + number;
                console.log(`匹配到高${chinese}，识别为${num}年级`);
                return num;
            }
        }

        // 默认返回初一
        console.log('未匹配到任何年级格式，使用默认值7(初一)');
        return 7;
    },

    // 处理老师/家长提供的学生信息 (新增函数)
    handleProvidedStudentInfo(response) {
        // 尝试从回答中提取学生信息
        const studentInfo = this.extractStudentInfo(response);

        if (studentInfo) {
            // 保存学生信息
            wx.setStorageSync('targetStudentInfo', studentInfo);

            // 加载对应年级的题库
            const grade = studentInfo.grade || '初一';
            const gradeQuestions = this.loadGradeQuestions(grade);

            this.setData({
                currentGradeQuestions: gradeQuestions,
                userInfo: studentInfo
            });

            setTimeout(() => {
                this.addMessage("AI", `谢谢您提供${this.data.userRole === 'teacher' ? '学生' : '孩子'}的信息。现在我们开始评测。`);

                // 开始年级测评
                setTimeout(() => {
                    this.startGradeAssessment();
                }, 1000);
            }, 1000);
        } else {
            // 无法提取完整信息，再次询问
            setTimeout(() => {
                this.addMessage("AI", `抱歉，我没能完全理解${this.data.userRole === 'teacher' ? '学生' : '孩子'}的信息。请按照以下格式提供：姓名、性别、年龄、年级。例如："张三，男，12岁，六年级"`);

                // 保持询问学生信息的状态
                this.setData({
                    sending: false
                });
            }, 1000);
        }
    },

    // 从文本中提取学生信息 (新增函数)
    extractStudentInfo(text) {
        // 尝试提取姓名
        const nameMatch = text.match(/[\u4e00-\u9fa5]{1,5}(?=，|,|\s)/);
        const name = nameMatch ? nameMatch[0] : null;

        // 尝试提取性别
        const gender = text.includes('男') ? '男' : (text.includes('女') ? '女' : null);

        // 尝试提取年龄
        const ageMatch = text.match(/(\d{1,2})[岁|歲]/);
        const age = ageMatch ? ageMatch[1] : null;

        // 尝试提取年级
        const gradePatterns = [
            /[一二三四五六七八九十]年级/,
            /\d年级/,
            /小学[一二三四五六]年级/,
            /初[一二三]/,
            /高[一二三]/
        ];

        let grade = null;
        for (const pattern of gradePatterns) {
            const match = text.match(pattern);
            if (match) {
                grade = match[0];
                break;
            }
        }

        // 如果至少有姓名和年龄或年级，则认为信息足够
        if (name && (age || grade) && gender) {
            return {
                name,
                gender,
                age: age || '',
                grade: grade || ''
            };
        }

        return null;
    },

    // 检测用户是否想修改个人信息的意图
    detectModifyInfoIntent(userMessage) {
        // 定义可能表达修改信息意图的关键词和短语
        const modifyIntentKeywords = [
            // 年级相关
            '年级选错', '年级填错', '年级不对', '不是这个年级', '换个年级', '年级写错',
            // 身份信息相关
            '信息填错', '个人信息', '重新填写', '修改信息', '改信息', '信息错误',
            '姓名填错', '名字错了', '名字填错', '名字输错',
            '性别选错', '性别错误', '不是男生', '不是女生',
            '学校选错', '学校填错', '学校不对', '不是这个学校',
            '年龄填错', '年龄不对', '不是这个年龄',
            // 返回相关
            '返回', '重填', '重新输入', '重新选择', '返回上一步', '重新开始',
            '想改', '能改吗', '可以改', '修改', '想修改', '能不能改'
        ];

        // 转为小写以进行大小写不敏感匹配
        const lowerMessage = userMessage.toLowerCase();

        // 检查是否包含修改意图关键词
        for (const keyword of modifyIntentKeywords) {
            if (lowerMessage.includes(keyword)) {
                return true;
            }
        }

        // 检查复杂表达方式（例如"我想重新填写信息"）
        const complexPatterns = [
            /想.*重新.*(填|选|输入|写)/,
            /可以.*(改|修改|重填|重新)/,
            /重新.*(开始|选择|输入)/,
            /填错.*(信息|年级|学校|名字|姓名|年龄|性别)/,
            /不是.*(年级|学校|班级)/,
            /错.*(信息|年级|学校|名字|姓名|年龄|性别)/
        ];

        for (const pattern of complexPatterns) {
            if (pattern.test(lowerMessage)) {
                return true;
            }
        }

        return false;
    },

    // 处理用户修改信息的请求
    handleModifyInfoRequest() {
        // 向用户确认是否要修改信息
        this.addMessage("AI", "您是想修改之前填写的个人信息吗？");

        setTimeout(() => {
            this.addMessage("AI", "请确认您想要修改的是：\n1. 年级信息\n2. 个人基本信息(姓名、性别等)\n3. 全部重新填写\n\n请输入对应的数字或告诉我您想修改的具体信息。");

            // 设置状态，标记正在等待用户确认修改信息类型
            this.setData({
                waitingForModifyConfirmation: true,
                sending: false
            });
        }, 1000);
    },

    // 处理用户确认要修改哪种信息
    handleModifyConfirmation(response) {
        // 转为小写处理
        const lowerResponse = response.toLowerCase();

        // 默认重新填写全部信息
        let modifyType = 'all';

        // 判断用户想修改的内容
        if (lowerResponse.includes('1') ||
            lowerResponse.includes('年级') ||
            lowerResponse.includes('班级')) {
            modifyType = 'grade';
        } else if (lowerResponse.includes('2') ||
                   lowerResponse.includes('基本') ||
                   lowerResponse.includes('姓名') ||
                   lowerResponse.includes('性别') ||
                   lowerResponse.includes('名字') ||
                   lowerResponse.includes('学校')) {
            modifyType = 'basic';
        } else if (lowerResponse.includes('3') ||
                   lowerResponse.includes('全部') ||
                   lowerResponse.includes('重新') ||
                   lowerResponse.includes('所有')) {
            modifyType = 'all';
        }

        // 清除等待修改确认的状态
        this.setData({
            waitingForModifyConfirmation: false
        });

        // 根据类型跳转到相应页面
        this.navigateToInfoEditPage(modifyType);
    },

    // 跳转到信息编辑页面
    navigateToInfoEditPage(modifyType) {
        this.addMessage("AI", "好的，我将带您去修改信息页面。");

        setTimeout(() => {
            // 在全局对象中保存当前页面实例和修改类型
            getApp().globalData = getApp().globalData || {};
            getApp().globalData.indexPage = this;
            getApp().globalData.modifyInfoType = modifyType;

            // 跳转到身份确认页面
            wx.navigateTo({
                url: '/pages/assessment/identity_confirm_enhanced_v2?modify=' + modifyType,
                success: () => {
                    console.log('成功跳转到身份信息修改页面');
                    this.addMessage("AI", "请在打开的页面中修改您的信息，完成后点击\"开始测评\"按钮。");
                },
                fail: (err) => {
                    console.error('跳转到身份信息修改页面失败:', err);
                    this.addMessage("AI", "抱歉，无法打开身份信息修改页面。请稍后再试或联系客服。");
                    this.setData({ sending: false });
                }
            });
        }, 1000);
    }
});