/**
 * 从古籍中提取缺失神煞的计算规则
 * 基于《千里命稿》等权威古籍
 */

console.log('📚 从古籍中提取缺失神煞计算规则');
console.log('================================================================================');

// 测试数据
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  dayGan: '癸',
  dayZhi: '卯',
  yearZhi: '丑',
  monthZhi: '午',
  lunarMonth: 5
};

// 问真八字标准结果
const wenZhenStandard = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

console.log('📋 测试数据: 辛丑 甲午 癸卯 壬戌');
console.log('农历: 辛丑年五月十五日戌时');

console.log('\n🔍 从《千里命稿》提取的神煞规则:');
console.log('='.repeat(60));

// 1. 羊刃（血刃）计算 - 基于《千里命稿》
console.log('\n⚔️ 羊刃（血刃）计算（《千里命稿》版本）:');
console.log('-'.repeat(40));

function calculateClassicalYangblade(dayGan, fourPillars) {
  // 基于《千里命稿》的羊刃表
  const yangbladeMap = {
    '甲': '卯',  // 甲刃在卯
    '乙': '寅',  // 乙刃在寅（阴逆）
    '丙': '午',  // 丙戊刃在午
    '丁': '巳',  // 丁己刃在巳（阴逆）
    '戊': '午',  // 丙戊刃在午
    '己': '巳',  // 丁己刃在巳（阴逆）
    '庚': '酉',  // 庚刃在酉
    '辛': '申',  // 辛刃在申（阴逆）
    '壬': '子',  // 壬刃在子
    '癸': '亥'   // 癸刃在亥（阴逆）
  };

  const target = yangbladeMap[dayGan];
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`日干${dayGan}的羊刃: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '血刃',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '刚烈暴戾，旺而越过其分',
          source: '《千里命稿》'
        });
      }
    });
  }

  return result;
}

const yangbladeResult = calculateClassicalYangblade(testData.dayGan, testData.fourPillars);
console.log('羊刃（血刃）计算结果:', yangbladeResult);

// 2. 灾煞计算 - 基于古籍推算
console.log('\n💥 灾煞计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalZaisha(yearZhi, fourPillars) {
  // 基于古籍的灾煞表（与年支相关）
  const zaishaMap = {
    '子': '午', '丑': '未', '寅': '申', '卯': '酉',
    '辰': '戌', '巳': '亥', '午': '子', '未': '丑',
    '申': '寅', '酉': '卯', '戌': '辰', '亥': '巳'
  };

  const target = zaishaMap[yearZhi];
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`年支${yearZhi}的灾煞: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '灾煞',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '主灾祸横事，意外之灾',
          source: '古籍推算'
        });
      }
    });
  }

  return result;
}

const zaishaResult = calculateClassicalZaisha(testData.yearZhi, testData.fourPillars);
console.log('灾煞计算结果:', zaishaResult);

// 3. 丧门计算 - 基于古籍推算
console.log('\n🚪 丧门计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalSangmen(yearZhi, fourPillars) {
  // 基于古籍的丧门表（年支顺数三位）
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const yearIndex = zhiOrder.indexOf(yearZhi);
  const targetIndex = (yearIndex + 3) % 12;  // 顺数三位
  const target = zhiOrder[targetIndex];

  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`年支${yearZhi}的丧门: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '丧门',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '主孝服丧事，悲伤之事',
          source: '古籍推算'
        });
      }
    });
  }

  return result;
}

const sangmenResult = calculateClassicalSangmen(testData.yearZhi, testData.fourPillars);
console.log('丧门计算结果:', sangmenResult);

// 4. 童子煞计算 - 基于古籍推算
console.log('\n👶 童子煞计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalTongzisha(dayGan, dayZhi, fourPillars) {
  // 基于古籍的童子煞表（多种计算方法）
  const tongziMap = {
    // 方法1：基于日干
    gan: {
      '甲': ['子', '寅'], '乙': ['丑', '卯'], '丙': ['寅', '辰'], '丁': ['卯', '巳'],
      '戊': ['辰', '午'], '己': ['巳', '未'], '庚': ['午', '申'], '辛': ['未', '酉'],
      '壬': ['申', '戌'], '癸': ['酉', '亥']
    },
    // 方法2：基于日支
    zhi: {
      '子': ['甲', '庚'], '丑': ['乙', '辛'], '寅': ['丙', '壬'], '卯': ['丁', '癸'],
      '辰': ['戊', '甲'], '巳': ['己', '乙'], '午': ['庚', '丙'], '未': ['辛', '丁'],
      '申': ['壬', '戊'], '酉': ['癸', '己'], '戌': ['甲', '庚'], '亥': ['乙', '辛']
    }
  };

  const ganTargets = tongziMap.gan[dayGan] || [];
  const zhiTargets = tongziMap.zhi[dayZhi] || [];
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`日干${dayGan}的童子煞: [${ganTargets.join(', ')}]`);
  console.log(`日支${dayZhi}的童子煞: [${zhiTargets.join(', ')}]`);

  // 检查日干法
  ganTargets.forEach(target => {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '童子煞',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          method: '日干法',
          effect: '主童年多病，性格孤僻',
          source: '古籍推算'
        });
      }
    });
  });

  // 检查日支法
  zhiTargets.forEach(target => {
    fourPillars.forEach((pillar, index) => {
      if (pillar.gan === target) {
        // 避免重复
        const exists = result.some(r => 
          r.position === pillarNames[index] && r.name === '童子煞'
        );
        if (!exists) {
          result.push({
            name: '童子煞',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            method: '日支法',
            effect: '主童年多病，性格孤僻',
            source: '古籍推算'
          });
        }
      }
    });
  });

  return result;
}

const tongzishaResult = calculateClassicalTongzisha(testData.dayGan, testData.dayZhi, testData.fourPillars);
console.log('童子煞计算结果:', tongzishaResult);

// 5. 寡宿计算 - 基于古籍推算
console.log('\n🏚️ 寡宿计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalGuasu(yearZhi, fourPillars) {
  // 基于古籍的寡宿表
  const guasuMap = {
    '子': '戌', '丑': '亥', '寅': '子', '卯': '丑',
    '辰': '寅', '巳': '卯', '午': '辰', '未': '巳',
    '申': '午', '酉': '未', '戌': '申', '亥': '酉'
  };

  const target = guasuMap[yearZhi];
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`年支${yearZhi}的寡宿: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '寡宿',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '主孤独寡居，六亲缘薄',
          source: '古籍推算'
        });
      }
    });
  }

  return result;
}

const guasuResult = calculateClassicalGuasu(testData.yearZhi, testData.fourPillars);
console.log('寡宿计算结果:', guasuResult);

// 6. 披麻计算 - 基于古籍推算
console.log('\n🧵 披麻计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalPima(yearZhi, fourPillars) {
  // 基于古籍的披麻表（与丧门相关）
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const yearIndex = zhiOrder.indexOf(yearZhi);
  const targetIndex = (yearIndex + 9) % 12;  // 顺数九位
  const target = zhiOrder[targetIndex];

  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`年支${yearZhi}的披麻: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '披麻',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '主孝服披麻，丧事缠身',
          source: '古籍推算'
        });
      }
    });
  }

  return result;
}

const pimaResult = calculateClassicalPima(testData.yearZhi, testData.fourPillars);
console.log('披麻计算结果:', pimaResult);

// 7. 元辰计算 - 基于古籍推算
console.log('\n🌀 元辰计算（古籍推算版本）:');
console.log('-'.repeat(40));

function calculateClassicalYuanchen(dayGan, fourPillars) {
  // 基于古籍的元辰表
  const yuanchenMap = {
    '甲': '戌', '乙': '亥', '丙': '丑', '丁': '子',
    '戊': '戌', '己': '亥', '庚': '辰', '辛': '卯',
    '壬': '午', '癸': '未'
  };

  const target = yuanchenMap[dayGan];
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

  console.log(`日干${dayGan}的元辰: ${target || '无'}`);

  if (target) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === target) {
        result.push({
          name: '元辰',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '主精神恍惚，多疑多虑',
          source: '古籍推算'
        });
      }
    });
  }

  return result;
}

const yuanchenResult = calculateClassicalYuanchen(testData.dayGan, testData.fourPillars);
console.log('元辰计算结果:', yuanchenResult);

// 8. 综合验证
console.log('\n📊 古籍缺失神煞验证结果:');
console.log('='.repeat(60));

const allMissingResults = [
  ...yangbladeResult, ...zaishaResult, ...sangmenResult, 
  ...tongzishaResult, ...guasuResult, ...pimaResult, ...yuanchenResult
];

// 按柱位分组
const missingByPillar = { year: [], month: [], day: [], hour: [] };
allMissingResults.forEach(result => {
  const pillarMap = { '年柱': 'year', '月柱': 'month', '日柱': 'day', '时柱': 'hour' };
  const pillar = pillarMap[result.position];
  if (pillar) {
    missingByPillar[pillar].push(result.name);
  }
});

['year', 'month', 'day', 'hour'].forEach(position => {
  const positionName = { year: '年柱', month: '月柱', day: '日柱', hour: '时柱' }[position];
  const calculated = missingByPillar[position];
  const expected = wenZhenStandard[position];
  
  console.log(`\n${positionName}:`);
  console.log(`  古籍计算: ${calculated.join(', ') || '无'}`);
  console.log(`  问真标准: ${expected.join(', ')}`);
  
  const matches = expected.filter(e => calculated.includes(e));
  const matchRate = expected.length > 0 ? (matches.length / expected.length * 100).toFixed(1) : '0';
  console.log(`  匹配度: ${matches.length}/${expected.length} (${matchRate}%)`);
  
  if (matches.length > 0) {
    console.log(`  ✅ 匹配: ${matches.join(', ')}`);
  }
  if (matches.length < expected.length) {
    const missing = expected.filter(e => !calculated.includes(e));
    console.log(`  ❌ 缺失: ${missing.join(', ')}`);
  }
});

// 总体匹配度
const totalExpected = Object.values(wenZhenStandard).flat().length;
const totalMatched = Object.entries(wenZhenStandard).reduce((acc, [position, expected]) => {
  const calculated = missingByPillar[position];
  const matches = expected.filter(e => calculated.includes(e));
  return acc + matches.length;
}, 0);

const overallMatchRate = (totalMatched / totalExpected * 100).toFixed(1);

console.log('\n🎯 古籍缺失神煞验证总结:');
console.log('='.repeat(40));
console.log(`缺失神煞匹配度: ${totalMatched}/${totalExpected} (${overallMatchRate}%)`);

if (parseFloat(overallMatchRate) >= 30) {
  console.log('✅ 古籍缺失神煞研究取得进展！');
} else {
  console.log('🔄 仍需进一步研究古籍中的神煞计算细节');
}

console.log('\n📚 古籍缺失神煞研究总结:');
console.log('1. 羊刃（血刃）：基于《千里命稿》的阴阳顺逆规则');
console.log('2. 灾煞：基于年支对冲的古籍计算方法');
console.log('3. 丧门：基于年支顺数三位的古籍规则');
console.log('4. 童子煞：基于日干日支的多重计算方法');
console.log('5. 寡宿：基于年支的古籍孤独星计算');
console.log('6. 披麻：基于年支顺数九位的古籍规则');
console.log('7. 元辰：基于日干的古籍精神星计算');

console.log('\n💡 下一步工作:');
console.log('1. 将这些古籍神煞规则整合到前端系统中');
console.log('2. 优化计算精度，提高与"问真八字"的匹配度');
console.log('3. 建立完整的古籍神煞验证体系');
console.log('4. 完善神煞的吉凶判断和效应描述');
