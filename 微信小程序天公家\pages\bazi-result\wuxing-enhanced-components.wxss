/* 增强版五行分析组件样式 */

/* 通用组件样式 */
.section-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2C1810;
  flex: 1;
}

.section-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.toggle-icon {
  font-size: 24rpx;
  color: #999;
  margin-left: 15rpx;
  transition: transform 0.3s ease;
}

/* 静态vs动态对比样式 */
.static-dynamic-comparison {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  border-left: 4rpx solid #007bff;
}

.comparison-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.comparison-content {
  padding: 30rpx;
}

.comparison-grid {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.element-comparison {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.element-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2C1810;
}

.change-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 5rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.change-indicator.change-minimal {
  background: #e9ecef;
  color: #6c757d;
}

.change-indicator.change-low {
  background: #d1ecf1;
  color: #0c5460;
}

.change-indicator.change-medium {
  background: #fff3cd;
  color: #856404;
}

.change-indicator.change-high {
  background: #f8d7da;
  color: #721c24;
}

.change-indicator.change-extreme {
  background: #d4edda;
  color: #155724;
}

.power-row {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.power-label {
  width: 60rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.power-bar {
  flex: 1;
  height: 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
}

.power-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.5s ease;
  min-width: 2rpx;
}

.power-fill.static {
  background: linear-gradient(90deg, #6c757d, #adb5bd);
}

.power-fill.dynamic {
  background: linear-gradient(90deg, #007bff, #0056b3);
}

.power-value {
  width: 60rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #2C1810;
  text-align: center;
}

/* 交互关系详情样式 */
.interaction-details {
  background: #ffffff;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border-left: 4rpx solid #28a745;
}

.interaction-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.interaction-content {
  padding: 30rpx;
}

.interaction-category {
  margin-bottom: 25rpx;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.category-icon {
  font-size: 28rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2C1810;
  flex: 1;
}

.category-count {
  font-size: 22rpx;
  color: #666;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.interaction-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 3rpx solid #dee2e6;
}

.interaction-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
}

.interaction-icon {
  font-size: 24rpx;
}

.interaction-desc {
  font-size: 26rpx;
  color: #2C1810;
  font-weight: 500;
}

.interaction-effect {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  text-align: right;
}

.strength-indicator {
  width: 8rpx;
  height: 30rpx;
  border-radius: 4rpx;
}

.strength-indicator.strength-low {
  background: #6c757d;
}

.strength-indicator.strength-medium {
  background: #ffc107;
}

.strength-indicator.strength-high {
  background: #dc3545;
}

/* 影响评估样式 */
.impact-evaluation {
  background: #ffffff;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border-left: 4rpx solid #ffc107;
}

.evaluation-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.evaluation-content {
  padding: 30rpx;
}

.impact-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.metric-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 25rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-top: 3rpx solid #007bff;
}

.metric-icon {
  font-size: 32rpx;
  margin-bottom: 10rpx;
  display: block;
}

.metric-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.metric-value {
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.metric-value.impact-微弱影响 { color: #6c757d; }
.metric-value.impact-轻微影响 { color: #17a2b8; }
.metric-value.impact-中等影响 { color: #ffc107; }
.metric-value.impact-强影响 { color: #fd7e14; }
.metric-value.impact-极强影响 { color: #dc3545; }

.metric-value.direction-正面影响 { color: #28a745; }
.metric-value.direction-负面影响 { color: #dc3545; }
.metric-value.direction-中性影响 { color: #6c757d; }

.metric-value.trend-吉利趋势 { color: #28a745; }
.metric-value.trend-中性趋势 { color: #ffc107; }
.metric-value.trend-不利趋势 { color: #dc3545; }

.metric-bar {
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.metric-detail {
  font-size: 20rpx;
  color: #999;
}

.detailed-metrics {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e9ecef;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
}

.detail-value {
  font-size: 24rpx;
  font-weight: bold;
}

.detail-value.balance-improved { color: #28a745; }
.detail-value.balance-declined { color: #dc3545; }
.detail-value.stability-stable { color: #28a745; }
.detail-value.stability-affected { color: #ffc107; }
.detail-value.stability-damaged { color: #dc3545; }

/* 个性化建议样式 */
.personalized-recommendations {
  background: #ffffff;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border-left: 4rpx solid #17a2b8;
}

.recommendations-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: linear-gradient(135deg, #d1ecf1, #bee5eb);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.recommendations-content {
  padding: 30rpx;
}

.recommendation-card {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #f39c12;
  margin-bottom: 20rpx;
}

.recommendation-icon {
  font-size: 32rpx;
  margin-bottom: 10rpx;
  display: block;
}

.recommendation-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2C1810;
  margin-bottom: 15rpx;
  display: block;
}

.recommendation-content {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.confidence-indicator {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.confidence-label {
  font-size: 22rpx;
  color: #666;
}

.confidence-bar {
  flex: 1;
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.confidence-value {
  font-size: 22rpx;
  font-weight: bold;
  color: #28a745;
}

.recommendations-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.item-icon {
  font-size: 20rpx;
  margin-top: 2rpx;
}

.item-content {
  font-size: 24rpx;
  color: #495057;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮样式 */
.wuxing-actions {
  margin-top: 30rpx;
  padding: 20rpx 0;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.refresh-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.share-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 数据状态指示器样式 */
.data-status-indicator {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  padding: 15rpx;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 15rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 22rpx;
  color: #2C1810;
  font-weight: 500;
}
