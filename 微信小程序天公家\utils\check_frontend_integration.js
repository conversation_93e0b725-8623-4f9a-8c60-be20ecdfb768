/**
 * 前端集成状态检查工具
 * 验证300人数据库和女性名人功能是否完全集成到前端
 */

const fs = require('fs');
const path = require('path');

class FrontendIntegrationChecker {
  constructor() {
    this.checkResults = [];
    this.passedChecks = 0;
    this.totalChecks = 0;
  }

  /**
   * 执行所有检查
   */
  async checkAll() {
    console.log('🔍 开始检查前端集成状态...\n');

    try {
      await this.checkDatabaseIntegration();
      await this.checkAPIIntegration();
      await this.checkWXMLIntegration();
      await this.checkJSIntegration();
      await this.checkCSSIntegration();
      await this.checkFunctionalIntegration();

      this.generateReport();
    } catch (error) {
      console.error('❌ 检查过程中出现错误:', error.message);
      throw error;
    }
  }

  /**
   * 检查数据库集成
   */
  async checkDatabaseIntegration() {
    console.log('📋 检查 1: 数据库集成状态');
    
    try {
      // 检查300人数据库文件是否存在
      const db300Path = path.join(__dirname, '../data/celebrities_database_300_complete.js');
      const db300Exists = fs.existsSync(db300Path);
      this.assert(db300Exists, '300人完整数据库文件应存在');

      const db300SimplifiedPath = path.join(__dirname, '../data/celebrities_database_300_simplified.js');
      const db300SimplifiedExists = fs.existsSync(db300SimplifiedPath);
      this.assert(db300SimplifiedExists, '300人简化数据库文件应存在');

      // 检查女性数据库文件
      const femaleDbPath = path.join(__dirname, '../data/female_celebrities_100_complete.js');
      const femaleDbExists = fs.existsSync(femaleDbPath);
      this.assert(femaleDbExists, '100位女性数据库文件应存在');

      // 验证数据库内容
      if (db300Exists) {
        const db300 = require('../data/celebrities_database_300_complete.js');
        this.assert(db300.metadata.totalRecords === 300, '数据库应包含300位名人');
        this.assert(db300.metadata.genderDistribution.female >= 100, '女性应至少100位');
      }

      console.log('✅ 通过');
      this.recordCheck('checkDatabaseIntegration', true, {
        db300Complete: db300Exists,
        db300Simplified: db300SimplifiedExists,
        femaleDb: femaleDbExists
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkDatabaseIntegration', false, { error: error.message });
    }
  }

  /**
   * 检查API集成
   */
  async checkAPIIntegration() {
    console.log('\n📋 检查 2: API集成状态');
    
    try {
      // 检查API文件是否更新
      const apiPath = path.join(__dirname, '../utils/celebrity_database_api.js');
      const apiExists = fs.existsSync(apiPath);
      this.assert(apiExists, 'API文件应存在');

      if (apiExists) {
        const apiContent = fs.readFileSync(apiPath, 'utf8');
        this.assert(apiContent.includes('celebrities_database_300_complete'), 'API应引用300人数据库');
        this.assert(apiContent.includes('300位历史名人'), 'API注释应更新为300人');
      }

      // 测试API功能
      const CelebrityDatabaseAPI = require('../utils/celebrity_database_api.js');
      const stats = CelebrityDatabaseAPI.getStatistics();
      this.assert(stats.totalCelebrities === 300, 'API应返回300位名人');

      const femaleResults = CelebrityDatabaseAPI.searchCelebrities({ gender: '女' });
      this.assert(femaleResults.length >= 100, 'API应能搜索到至少100位女性');

      console.log('✅ 通过');
      this.recordCheck('checkAPIIntegration', true, {
        apiExists: apiExists,
        totalCelebrities: stats.totalCelebrities,
        femaleCount: femaleResults.length
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkAPIIntegration', false, { error: error.message });
    }
  }

  /**
   * 检查WXML集成
   */
  async checkWXMLIntegration() {
    console.log('\n📋 检查 3: WXML模板集成');
    
    try {
      const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
      const wxmlExists = fs.existsSync(wxmlPath);
      this.assert(wxmlExists, 'WXML文件应存在');

      if (wxmlExists) {
        const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');
        
        // 检查历史验证模块
        this.assert(wxmlContent.includes('historical-verification-card'), '应包含历史验证卡片');
        this.assert(wxmlContent.includes('similarCelebrities'), '应包含相似名人数据绑定');
        this.assert(wxmlContent.includes('200名人库'), '应显示数据库规模标识');
        
        // 检查是否修复了toFixed错误
        this.assert(!wxmlContent.includes('.toFixed('), '不应包含toFixed方法调用');
        this.assert(wxmlContent.includes('similarity_display'), '应使用预处理的显示值');
      }

      console.log('✅ 通过');
      this.recordCheck('checkWXMLIntegration', true, {
        wxmlExists: wxmlExists,
        hasHistoricalCard: true,
        fixedToFixed: true
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkWXMLIntegration', false, { error: error.message });
    }
  }

  /**
   * 检查JavaScript集成
   */
  async checkJSIntegration() {
    console.log('\n📋 检查 4: JavaScript集成');
    
    try {
      const jsPath = path.join(__dirname, '../pages/bazi-result/index.js');
      const jsExists = fs.existsSync(jsPath);
      this.assert(jsExists, 'JavaScript文件应存在');

      if (jsExists) {
        const jsContent = fs.readFileSync(jsPath, 'utf8');
        
        // 检查导入
        this.assert(jsContent.includes('CelebrityDatabaseAPI'), '应导入名人数据库API');
        this.assert(jsContent.includes('BaziSimilarityMatcher'), '应导入相似度匹配器');
        
        // 检查方法
        this.assert(jsContent.includes('performHistoricalVerification'), '应包含历史验证方法');
        this.assert(jsContent.includes('similarity_display'), '应处理相似度显示值');
        this.assert(jsContent.includes('confidence_display'), '应处理置信度显示值');
        
        // 检查数据属性
        this.assert(jsContent.includes('similarCelebrities'), '应包含相似名人数据属性');
        this.assert(jsContent.includes('historicalStats'), '应包含历史统计数据属性');
      }

      console.log('✅ 通过');
      this.recordCheck('checkJSIntegration', true, {
        jsExists: jsExists,
        hasImports: true,
        hasMethods: true,
        hasDataProps: true
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkJSIntegration', false, { error: error.message });
    }
  }

  /**
   * 检查CSS集成
   */
  async checkCSSIntegration() {
    console.log('\n📋 检查 5: CSS样式集成');
    
    try {
      const cssPath = path.join(__dirname, '../pages/bazi-result/index.wxss');
      const cssExists = fs.existsSync(cssPath);
      this.assert(cssExists, 'CSS文件应存在');

      if (cssExists) {
        const cssContent = fs.readFileSync(cssPath, 'utf8');
        
        // 检查历史验证相关样式
        this.assert(cssContent.includes('historical-verification-card'), '应包含历史验证卡片样式');
        this.assert(cssContent.includes('celebrity-list'), '应包含名人列表样式');
        this.assert(cssContent.includes('similarity-score'), '应包含相似度分数样式');
      }

      console.log('✅ 通过');
      this.recordCheck('checkCSSIntegration', true, {
        cssExists: cssExists,
        hasHistoricalStyles: true
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkCSSIntegration', false, { error: error.message });
    }
  }

  /**
   * 检查功能集成
   */
  async checkFunctionalIntegration() {
    console.log('\n📋 检查 6: 功能集成测试');
    
    try {
      // 模拟功能测试
      const CelebrityDatabaseAPI = require('../utils/celebrity_database_api.js');
      const BaziSimilarityMatcher = require('../utils/bazi_similarity_matcher.js');
      
      // 测试API功能
      const stats = CelebrityDatabaseAPI.getStatistics();
      this.assert(stats.totalCelebrities === 300, '统计功能应正常');
      
      // 测试搜索功能
      const femaleResults = CelebrityDatabaseAPI.searchCelebrities({ gender: '女' });
      this.assert(femaleResults.length >= 100, '女性搜索功能应正常');
      
      // 测试相似度匹配
      const matcher = new BaziSimilarityMatcher();
      const userBazi = {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        hour: { gan: '壬', zhi: '戌' }
      };
      
      const similarities = matcher.batchCalculateSimilarity(userBazi, femaleResults.slice(0, 5));
      this.assert(similarities.length > 0, '相似度匹配功能应正常');

      console.log('✅ 通过');
      this.recordCheck('checkFunctionalIntegration', true, {
        apiWorking: true,
        searchWorking: true,
        similarityWorking: true,
        femaleMatches: similarities.length
      });
    } catch (error) {
      console.log('❌ 失败:', error.message);
      this.recordCheck('checkFunctionalIntegration', false, { error: error.message });
    }
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * 记录检查结果
   */
  recordCheck(checkName, passed, result) {
    this.totalChecks++;
    if (passed) {
      this.passedChecks++;
    }
    
    this.checkResults.push({
      checkName,
      passed,
      result
    });
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📊 前端集成状态检查报告');
    console.log('='.repeat(60));
    console.log(`总检查项: ${this.totalChecks}`);
    console.log(`通过: ${this.passedChecks}`);
    console.log(`失败: ${this.totalChecks - this.passedChecks}`);
    console.log(`集成完成度: ${Math.round((this.passedChecks / this.totalChecks) * 100)}%`);

    console.log('\n详细结果:');
    this.checkResults.forEach(check => {
      const status = check.passed ? '✅' : '❌';
      console.log(`${status} ${check.checkName}`);
      if (check.result && typeof check.result === 'object') {
        console.log(`   结果: ${JSON.stringify(check.result, null, 2)}`);
      }
    });

    if (this.passedChecks === this.totalChecks) {
      console.log('\n🎉 前端集成完全成功！');
      console.log('\n✨ 集成功能清单:');
      console.log('- ✅ 300人历史名人数据库已集成');
      console.log('- ✅ 100位女性名人已添加');
      console.log('- ✅ 性别筛选功能已实现');
      console.log('- ✅ 历史验证模块已集成到专业细盘页面');
      console.log('- ✅ 相似度匹配算法已部署');
      console.log('- ✅ WXML编译错误已修复');
      console.log('- ✅ 用户界面已更新');
      console.log('- ✅ 所有功能正常运行');
      
      console.log('\n🎯 用户可以使用的功能:');
      console.log('1. 在专业细盘页面查看历史名人验证结果');
      console.log('2. 查看与自己八字相似的历史名人');
      console.log('3. 浏览300位历史名人数据库');
      console.log('4. 按性别筛选历史名人');
      console.log('5. 查看详细的相似度分析');
    } else {
      console.log('\n⚠️  部分集成未完成，需要进一步处理');
    }
  }
}

// 导出类
module.exports = FrontendIntegrationChecker;

// 如果直接运行此文件，执行检查
if (require.main === module) {
  const checker = new FrontendIntegrationChecker();
  checker.checkAll().catch(console.error);
}
