// components/enhanced-balance-meter/index.js
// 增强的五行平衡指标组件
Component({
  properties: {
    // 平衡指数 (0-100)
    balanceIndex: {
      type: Number,
      value: 50,
      observer: 'onBalanceIndexChange'
    },
    
    // 五行分数数据
    wuxingScores: {
      type: Object,
      value: {
        wood: 50,
        fire: 50,
        earth: 50,
        metal: 50,
        water: 50
      },
      observer: 'onWuxingScoresChange'
    },
    
    // 是否显示详细信息
    showDetailsDefault: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 平衡状态
    balanceStatus: '一般平衡',
    balanceDescription: '您的五行有一定失衡，某些方面可能需要特别关注。',
    statusColor: '#FFC107',
    progressColor: '#FFC107',
    
    // 详情显示控制
    showDetails: false,
    
    // 五行配置
    wuxingConfig: {
      wood: { name: '木', color: '#4CAF50' },
      fire: { name: '火', color: '#FF5722' },
      earth: { name: '土', color: '#795548' },
      metal: { name: '金', color: '#9E9E9E' },
      water: { name: '水', color: '#2196F3' }
    },
    
    // 分析数据
    strongestElement: { name: '木', score: 50, color: '#4CAF50' },
    weakestElement: { name: '水', score: 50, color: '#2196F3' },
    deviationValue: '0',
    deviationLevel: '无偏差',
    
    // 改善建议
    suggestions: []
  },

  lifetimes: {
    attached() {
      this.setData({
        showDetails: this.properties.showDetailsDefault
      });
      this.updateBalanceAnalysis();
    }
  },

  methods: {
    // 平衡指数变化处理
    onBalanceIndexChange(newIndex) {
      this.updateBalanceStatus(newIndex);
    },

    // 五行分数变化处理
    onWuxingScoresChange(newScores) {
      this.updateElementAnalysis(newScores);
      this.updateSuggestions(newScores);
    },

    // 更新平衡状态
    updateBalanceStatus(index) {
      let status, description, color;
      
      if (index >= 85) {
        status = '完美平衡';
        description = '您的五行达到了完美平衡，各方面发展极为均衡，人生道路非常顺畅。';
        color = '#4CAF50';
      } else if (index >= 70) {
        status = '非常平衡';
        description = '您的五行非常平衡，各方面发展均衡，人生道路相对顺畅。';
        color = '#8BC34A';
      } else if (index >= 55) {
        status = '基本平衡';
        description = '您的五行基本平衡，有轻微偏重，但整体运势良好。';
        color = '#FFC107';
      } else if (index >= 40) {
        status = '轻度失衡';
        description = '您的五行有一定失衡，某些方面可能需要特别关注。';
        color = '#FF9800';
      } else if (index >= 25) {
        status = '明显失衡';
        description = '您的五行失衡较为明显，建议针对性调整以改善运势。';
        color = '#FF5722';
      } else {
        status = '严重失衡';
        description = '您的五行严重失衡，需要系统性的调整和改善。';
        color = '#F44336';
      }
      
      this.setData({
        balanceStatus: status,
        balanceDescription: description,
        statusColor: color,
        progressColor: color
      });
    },

    // 更新元素分析
    updateElementAnalysis(scores) {
      const config = this.data.wuxingConfig;
      const elements = Object.keys(scores).map(key => ({
        key: key,
        name: config[key].name,
        color: config[key].color,
        score: scores[key] || 0
      }));
      
      // 找出最强和最弱元素
      const strongest = elements.reduce((max, current) => 
        current.score > max.score ? current : max
      );
      const weakest = elements.reduce((min, current) => 
        current.score < min.score ? current : min
      );
      
      // 计算偏差
      const deviation = strongest.score - weakest.score;
      let deviationLevel;
      
      if (deviation <= 10) {
        deviationLevel = '极小偏差';
      } else if (deviation <= 20) {
        deviationLevel = '轻微偏差';
      } else if (deviation <= 35) {
        deviationLevel = '中等偏差';
      } else if (deviation <= 50) {
        deviationLevel = '较大偏差';
      } else {
        deviationLevel = '极大偏差';
      }
      
      this.setData({
        strongestElement: strongest,
        weakestElement: weakest,
        deviationValue: deviation.toFixed(1),
        deviationLevel: deviationLevel
      });
    },

    // 更新改善建议
    updateSuggestions(scores) {
      const suggestions = [];
      const config = this.data.wuxingConfig;
      
      // 分析各元素并给出建议
      Object.keys(scores).forEach(element => {
        const score = scores[element];
        const elementConfig = config[element];
        
        if (score < 30) {
          suggestions.push({
            icon: this.getElementIcon(element),
            title: `增强${elementConfig.name}元素`,
            description: this.getEnhancementSuggestion(element)
          });
        } else if (score > 80) {
          suggestions.push({
            icon: '⚖️',
            title: `平衡${elementConfig.name}元素`,
            description: this.getBalanceSuggestion(element)
          });
        }
      });
      
      // 如果没有特殊建议，给出通用建议
      if (suggestions.length === 0) {
        suggestions.push({
          icon: '✨',
          title: '保持现状',
          description: '您的五行相对平衡，建议保持当前的生活方式。'
        });
      }
      
      this.setData({ suggestions });
    },

    // 获取元素图标
    getElementIcon(element) {
      const icons = {
        wood: '🌿',
        fire: '🔥',
        earth: '🏔️',
        metal: '🔸',
        water: '💧'
      };
      return icons[element] || '⭐';
    },

    // 获取增强建议
    getEnhancementSuggestion(element) {
      const suggestions = {
        wood: '多接触绿色植物，培养创造力，适当运动',
        fire: '增加社交活动，提升行动力，保持热情',
        earth: '注重稳定发展，培养耐心，加强基础',
        metal: '锻炼意志力，提高执行力，保持原则',
        water: '增强学习能力，提高适应性，保持灵活'
      };
      return suggestions[element] || '需要针对性调整';
    },

    // 获取平衡建议
    getBalanceSuggestion(element) {
      const suggestions = {
        wood: '适当控制创新冲动，注重实际执行',
        fire: '控制过度热情，保持冷静思考',
        earth: '避免过于保守，适当接受变化',
        metal: '减少过度严格，增加灵活性',
        water: '避免过度变化，保持一定稳定'
      };
      return suggestions[element] || '需要适当调节';
    },

    // 更新完整分析
    updateBalanceAnalysis() {
      this.updateBalanceStatus(this.properties.balanceIndex);
      this.updateElementAnalysis(this.properties.wuxingScores);
      this.updateSuggestions(this.properties.wuxingScores);
    },

    // 切换详情显示
    toggleDetails() {
      this.setData({
        showDetails: !this.data.showDetails
      });
    },

    // 分享平衡结果
    shareBalance() {
      const { balanceIndex, balanceStatus } = this.data;
      
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      
      // 触发父组件的分享事件
      this.triggerEvent('share', {
        balanceIndex,
        balanceStatus,
        type: 'balance'
      });
    }
  }
});
