// test_complete_data_flow.js
// 测试完整的数据流程，从前端计算到结果页面显示

console.log('🔍 测试完整数据流程');
console.log('='.repeat(60));

// 模拟前端计算结果（包含调试信息）
const mockFrontendResult = {
  bazi: {
    year: { gan: '乙', zhi: '巳' },
    month: { gan: '癸', zhi: '未' },
    day: { gan: '辛', zhi: '丑' },
    hour: { gan: '丁', zhi: '酉' }
  },
  formatted: {
    year: '乙巳',
    month: '癸未',
    day: '辛丑',
    hour: '丁酉'
  },
  trueSolarTimeInfo: {
    originalTime: '2025/7/31 17:15:00',
    trueSolarTime: '2025/7/31 16:54:00',
    timeDifference: -21
  },
  basicInfo: {
    birth_solar_term: '大暑',
    kong_wang: '辰巳',
    ming_gua: '震卦',
    renyuan_siling: '己土司令',
    auxiliary_stars: ['天乙贵人', '太极贵人'],
    shen_sha: ['桃花', '驿马'],
    classical_analysis: '辛金生于未月，土旺金相，身强财弱...'
  },
  debugBasicInfo: {
    fourPillarsData: [
      { gan: '乙', zhi: '巳' },
      { gan: '癸', zhi: '未' },
      { gan: '辛', zhi: '丑' },
      { gan: '丁', zhi: '酉' }
    ],
    birth_solar_term_result: '大暑',
    renyuan_siling_result: '己土司令',
    kong_wang_result: '辰巳',
    ming_gua_result: '震卦'
  },
  jieqiInfo: '大暑'
};

const mockBirthInfo = {
  year: 2025,
  month: 7,
  day: 31,
  hour: 17,
  minute: 15,
  gender: '男',
  birthCity: '北京'
};

console.log('📊 模拟数据:');
console.log('前端计算结果 basicInfo:', mockFrontendResult.basicInfo);
console.log('前端计算结果 debugBasicInfo:', mockFrontendResult.debugBasicInfo);

// 模拟 convertFrontendDataToDisplayFormat 方法
function testConvertFrontendDataToDisplayFormat(frontendResult, birthInfo) {
  console.log('\n🔄 开始数据格式转换...');

  // 转换出生信息格式
  const convertedUserInfo = {
    name: birthInfo.name || '用户',
    gender: birthInfo.gender || '未知',
    birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
    birthTime: `${birthInfo.hour}:${birthInfo.minute}`,
    location: birthInfo.birthCity || birthInfo.location || '未知',
    solar_time: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日 ${birthInfo.hour}:${birthInfo.minute}`,
    lunar_time: frontendResult.lunar_date || frontendResult.lunarFormatted || frontendResult.formatted_lunar || '未知',
    true_solar_time: `${birthInfo.hour}:${birthInfo.minute}`,
    longitude: '未知',
    latitude: '未知',
    timezone: 'UTC+8',
    solar_term: frontendResult.solar_term || '未知'
  };

  // 转换八字信息格式
  const convertedBaziInfo = {
    yearPillar: {
      heavenly: frontendResult.bazi?.year?.gan || '甲',
      earthly: frontendResult.bazi?.year?.zhi || '子',
      nayin: frontendResult.nayin?.year || '海中金'
    },
    monthPillar: {
      heavenly: frontendResult.bazi?.month?.gan || '丙',
      earthly: frontendResult.bazi?.month?.zhi || '寅',
      nayin: frontendResult.nayin?.month || '炉中火'
    },
    dayPillar: {
      heavenly: frontendResult.bazi?.day?.gan || '戊',
      earthly: frontendResult.bazi?.day?.zhi || '午',
      nayin: frontendResult.nayin?.day || '天上火'
    },
    timePillar: {
      heavenly: frontendResult.bazi?.hour?.gan || '庚',
      earthly: frontendResult.bazi?.hour?.zhi || '申',
      nayin: frontendResult.nayin?.hour || '石榴木'
    }
  };

  // 🔧 修复：提取基本信息（节气、空亡等）
  const basicInfo = frontendResult.basicInfo || {};
  
  console.log('🔧 提取的基本信息:', basicInfo);
  
  // 组装完整数据
  const convertedData = {
    userInfo: convertedUserInfo,
    baziInfo: convertedBaziInfo,
    analysisMode: 'comprehensive',
    dataSource: 'converted_frontend_result',
    originalData: frontendResult,
    // 🔧 新增：基本命理信息
    birth_solar_term: basicInfo.birth_solar_term || '未知',
    kong_wang: basicInfo.kong_wang || '未知',
    ming_gua: basicInfo.ming_gua || '未知',
    renyuan_siling: basicInfo.renyuan_siling || '未知',
    auxiliary_stars: basicInfo.auxiliary_stars || [],
    shen_sha: basicInfo.shen_sha || [],
    classical_analysis: basicInfo.classical_analysis || '计算中...',
    // 🔧 新增：节气信息
    jieqiInfo: frontendResult.jieqiInfo || basicInfo.birth_solar_term || '未知'
  };

  console.log('✅ 数据格式转换完成');
  console.log('🔧 基本信息提取:', {
    节气: convertedData.birth_solar_term,
    空亡: convertedData.kong_wang,
    命卦: convertedData.ming_gua,
    人元司令: convertedData.renyuan_siling
  });
  
  // 🔧 调试：检查前端计算的调试信息
  if (frontendResult.debugBasicInfo) {
    console.log('🔍 前端计算调试信息:', frontendResult.debugBasicInfo);
  } else {
    console.log('⚠️ 前端计算结果中没有调试信息');
  }
  
  return convertedData;
}

// 执行测试
const convertedData = testConvertFrontendDataToDisplayFormat(mockFrontendResult, mockBirthInfo);

console.log('\n📋 最终转换结果:');
console.log('节气:', convertedData.birth_solar_term);
console.log('空亡:', convertedData.kong_wang);
console.log('命卦:', convertedData.ming_gua);
console.log('人元司令:', convertedData.renyuan_siling);
console.log('辅助星:', convertedData.auxiliary_stars);

// 检查是否还有"计算中"或"未知"
const hasIssues = [
  convertedData.birth_solar_term,
  convertedData.kong_wang,
  convertedData.ming_gua,
  convertedData.renyuan_siling
].some(value => value === '计算中...' || value === '未知');

console.log('\n🔍 结果分析:');
if (hasIssues) {
  console.log('❌ 仍有数据显示"计算中"或"未知"');
  console.log('问题可能在于:');
  console.log('1. 前端计算结果中的 basicInfo 为空或格式不正确');
  console.log('2. 数据提取逻辑有问题');
} else {
  console.log('✅ 所有数据都正常提取，不再显示"计算中"');
  console.log('如果实际页面仍显示"计算中"，问题可能在于:');
  console.log('1. 前端计算本身没有正确执行');
  console.log('2. 数据保存到本地存储时有问题');
  console.log('3. 页面加载数据时有问题');
}

console.log('\n🎯 下一步调试建议:');
console.log('1. 检查实际的前端计算是否正确执行');
console.log('2. 检查 wx.setStorageSync 保存的数据是否完整');
console.log('3. 检查结果页面加载数据的逻辑');

console.log('\n✅ 测试完成');
