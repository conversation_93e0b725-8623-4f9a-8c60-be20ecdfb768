// 个性化建议"千人千面"特性验证测试
const UnifiedWuxingAPI = require('./utils/unified_wuxing_api.js');

console.log('🎯 开始验证个性化建议"千人千面"特性...');

// 创建多样化的测试样本
const diverseTestSamples = [
  {
    name: '强木格局',
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丙', zhi: '寅' },
      { gan: '甲', zhi: '子' },
      { gan: '乙', zhi: '卯' }
    ]
  },
  {
    name: '水火冲突',
    fourPillars: [
      { gan: '壬', zhi: '子' },
      { gan: '丁', zhi: '午' },
      { gan: '癸', zhi: '亥' },
      { gan: '丙', zhi: '午' }
    ]
  },
  {
    name: '金水相生',
    fourPillars: [
      { gan: '庚', zhi: '申' },
      { gan: '壬', zhi: '子' },
      { gan: '辛', zhi: '酉' },
      { gan: '癸', zhi: '亥' }
    ]
  },
  {
    name: '土旺格局',
    fourPillars: [
      { gan: '戊', zhi: '辰' },
      { gan: '己', zhi: '未' },
      { gan: '戊', zhi: '戌' },
      { gan: '己', zhi: '丑' }
    ]
  },
  {
    name: '火炎土燥',
    fourPillars: [
      { gan: '丙', zhi: '午' },
      { gan: '丁', zhi: '未' },
      { gan: '丙', zhi: '午' },
      { gan: '丁', zhi: '巳' }
    ]
  },
  {
    name: '平衡格局',
    fourPillars: [
      { gan: '甲', zhi: '子' },
      { gan: '丙', zhi: '午' },
      { gan: '戊', zhi: '辰' },
      { gan: '庚', zhi: '申' }
    ]
  },
  {
    name: '三合水局',
    fourPillars: [
      { gan: '壬', zhi: '申' },
      { gan: '癸', zhi: '子' },
      { gan: '甲', zhi: '辰' },
      { gan: '乙', zhi: '卯' }
    ]
  },
  {
    name: '六冲格局',
    fourPillars: [
      { gan: '甲', zhi: '子' },
      { gan: '丙', zhi: '午' },
      { gan: '戊', zhi: '卯' },
      { gan: '庚', zhi: '酉' }
    ]
  }
];

async function runPersonalizationTest() {
  const api = new UnifiedWuxingAPI();
  const results = [];
  
  console.log('📊 开始分析多样化样本...\n');
  
  for (let i = 0; i < diverseTestSamples.length; i++) {
    const sample = diverseTestSamples[i];
    console.log(`🔍 分析样本 ${i + 1}: ${sample.name}`);
    
    try {
      const analysis = await api.performCompleteAnalysis(sample.fourPillars);
      const recommendations = analysis.unified.professionalData.frontendData.recommendations;
      
      // 提取个性化特征
      const personalizedFeatures = {
        sampleName: sample.name,
        recommendationCount: recommendations.length,
        recommendationTypes: recommendations.map(r => r.type),
        uniqueTypes: [...new Set(recommendations.map(r => r.type))].length,
        primaryRecommendation: recommendations[0]?.title || '无',
        confidenceRange: {
          min: Math.min(...recommendations.map(r => r.confidence)),
          max: Math.max(...recommendations.map(r => r.confidence)),
          avg: Math.round(recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length)
        },
        priorityDistribution: {
          high: recommendations.filter(r => r.priority === 'high').length,
          medium: recommendations.filter(r => r.priority === 'medium').length,
          low: recommendations.filter(r => r.priority === 'low').length
        },
        contentKeywords: extractKeywords(recommendations.map(r => r.content).join(' ')),
        impactLevel: analysis.unified.professionalData.impactEvaluation.overallLevel,
        fortuneTrend: analysis.unified.professionalData.impactEvaluation.fortuneTrendText
      };
      
      results.push(personalizedFeatures);
      
      console.log(`   ✅ 建议数量: ${personalizedFeatures.recommendationCount}`);
      console.log(`   🎯 主要建议: ${personalizedFeatures.primaryRecommendation}`);
      console.log(`   📈 置信度范围: ${personalizedFeatures.confidenceRange.min}-${personalizedFeatures.confidenceRange.max}%`);
      console.log(`   🔮 吉凶趋势: ${personalizedFeatures.fortuneTrend}\n`);
      
    } catch (error) {
      console.error(`   ❌ 分析失败: ${error.message}\n`);
    }
  }
  
  return results;
}

// 提取关键词
function extractKeywords(text) {
  const keywords = text.match(/[\u4e00-\u9fa5]{2,4}/g) || [];
  const keywordCount = {};
  keywords.forEach(word => {
    keywordCount[word] = (keywordCount[word] || 0) + 1;
  });
  return Object.keys(keywordCount).slice(0, 5); // 返回前5个关键词
}

// 分析个性化程度
function analyzePersonalizationLevel(results) {
  console.log('🎯 个性化程度分析报告');
  console.log('=' .repeat(50));
  
  // 1. 建议数量多样性
  const recommendationCounts = results.map(r => r.recommendationCount);
  const countVariance = calculateVariance(recommendationCounts);
  console.log(`📊 建议数量多样性: ${countVariance.toFixed(2)} (方差)`);
  console.log(`   范围: ${Math.min(...recommendationCounts)} - ${Math.max(...recommendationCounts)} 个建议`);
  
  // 2. 建议类型多样性
  const allTypes = results.flatMap(r => r.recommendationTypes);
  const uniqueTypes = [...new Set(allTypes)];
  console.log(`🏷️ 建议类型多样性: ${uniqueTypes.length} 种不同类型`);
  console.log(`   类型: ${uniqueTypes.join(', ')}`);
  
  // 3. 主要建议唯一性
  const primaryRecommendations = results.map(r => r.primaryRecommendation);
  const uniquePrimary = [...new Set(primaryRecommendations)];
  const primaryUniqueness = (uniquePrimary.length / primaryRecommendations.length * 100).toFixed(1);
  console.log(`🎯 主要建议唯一性: ${primaryUniqueness}%`);
  
  // 4. 置信度分布
  const avgConfidences = results.map(r => r.confidenceRange.avg);
  const confidenceVariance = calculateVariance(avgConfidences);
  console.log(`📈 置信度分布: ${confidenceVariance.toFixed(2)} (方差)`);
  
  // 5. 内容关键词多样性
  const allKeywords = results.flatMap(r => r.contentKeywords);
  const uniqueKeywords = [...new Set(allKeywords)];
  console.log(`🔤 内容关键词多样性: ${uniqueKeywords.length} 个不同关键词`);
  
  // 6. 吉凶趋势分布
  const fortuneTrends = results.map(r => r.fortuneTrend);
  const trendDistribution = {};
  fortuneTrends.forEach(trend => {
    trendDistribution[trend] = (trendDistribution[trend] || 0) + 1;
  });
  console.log(`🔮 吉凶趋势分布:`, trendDistribution);
  
  // 综合个性化评分
  const personalizationScore = calculatePersonalizationScore({
    countVariance,
    typeCount: uniqueTypes.length,
    primaryUniqueness: parseFloat(primaryUniqueness),
    confidenceVariance,
    keywordCount: uniqueKeywords.length,
    trendVariety: Object.keys(trendDistribution).length
  });
  
  console.log(`\n🏆 综合个性化评分: ${personalizationScore}/100`);
  
  if (personalizationScore >= 85) {
    console.log('🌟 评级: 优秀 - 真正做到了千人千面');
  } else if (personalizationScore >= 70) {
    console.log('🎯 评级: 良好 - 个性化程度较高');
  } else if (personalizationScore >= 55) {
    console.log('⚠️ 评级: 一般 - 个性化程度中等');
  } else {
    console.log('❌ 评级: 不足 - 个性化程度较低');
  }
  
  return personalizationScore;
}

// 计算方差
function calculateVariance(numbers) {
  const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
  return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
}

// 计算个性化评分
function calculatePersonalizationScore(metrics) {
  let score = 0;
  
  // 建议数量多样性 (20分)
  score += Math.min(metrics.countVariance * 10, 20);
  
  // 建议类型多样性 (25分)
  score += Math.min(metrics.typeCount * 3, 25);
  
  // 主要建议唯一性 (20分)
  score += metrics.primaryUniqueness * 0.2;
  
  // 置信度分布 (15分)
  score += Math.min(metrics.confidenceVariance * 0.5, 15);
  
  // 关键词多样性 (15分)
  score += Math.min(metrics.keywordCount * 0.5, 15);
  
  // 趋势多样性 (5分)
  score += Math.min(metrics.trendVariety * 1.67, 5);
  
  return Math.round(Math.min(score, 100));
}

// 执行测试
runPersonalizationTest().then(results => {
  console.log('\n📋 详细结果摘要:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.sampleName}:`);
    console.log(`   建议: ${result.recommendationCount}个 | 主要: ${result.primaryRecommendation}`);
    console.log(`   趋势: ${result.fortuneTrend} | 影响: ${result.impactLevel}`);
  });
  
  console.log('\n');
  analyzePersonalizationLevel(results);
}).catch(error => {
  console.error('❌ 测试执行失败:', error);
});
