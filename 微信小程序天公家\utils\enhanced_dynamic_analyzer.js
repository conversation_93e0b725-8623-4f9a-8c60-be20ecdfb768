// utils/enhanced_dynamic_analyzer.js
// 动态分析模块实现
// 基于《命理格局，用神.txt》文档要求

/**
 * 增强版动态分析器
 * 实现大运流年影响模型、关键转折点检测、社会环境因素注入等功能
 */
class EnhancedDynamicAnalyzer {
  constructor() {
    this.initializeData();
    this.initializeCache();
  }

  /**
   * 初始化缓存系统
   */
  initializeCache() {
    this.cache = new Map();
    this.cacheConfig = {
      maxSize: 100,        // 最大缓存条目数
      ttl: 3600000,       // 1小时TTL
      enabled: true       // 缓存开关
    };
  }

  /**
   * 缓存操作方法
   */
  getCacheKey(bazi, yongshen, personalInfo, options) {
    // 兼容不同的数据格式
    const getGan = (pillar) => pillar.gan || pillar.heavenly || pillar.stem;
    const getZhi = (pillar) => pillar.zhi || pillar.earthly || pillar.branch;

    const baziStr = `${getGan(bazi.year)}${getZhi(bazi.year)}${getGan(bazi.month)}${getZhi(bazi.month)}${getGan(bazi.day)}${getZhi(bazi.day)}${getGan(bazi.hour)}${getZhi(bazi.hour)}`;
    const yongshenStr = yongshen?.yongshen || 'none';
    const optionsStr = JSON.stringify(options);
    return `${baziStr}_${yongshenStr}_${optionsStr}`;
  }

  getFromCache(key) {
    if (!this.cacheConfig.enabled) return null;

    const item = this.cache.get(key);
    if (!item) return null;

    // 检查TTL
    if (Date.now() - item.timestamp > this.cacheConfig.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  setToCache(key, data) {
    if (!this.cacheConfig.enabled) return;

    // 检查缓存大小限制
    if (this.cache.size >= this.cacheConfig.maxSize) {
      // 删除最旧的条目
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  /**
   * 初始化基础数据
   */
  initializeData() {
    // 大运能量衰减曲线配置
    this.dayunDecayConfig = {
      // 大运10年周期内的能量分布
      energy_curve: [
        { year: 1, energy: 0.1, phase: '初入' },
        { year: 2, energy: 0.3, phase: '渐强' },
        { year: 3, energy: 0.6, phase: '上升' },
        { year: 4, energy: 0.8, phase: '鼎盛' },
        { year: 5, energy: 1.0, phase: '巅峰' },
        { year: 6, energy: 1.0, phase: '巅峰' },
        { year: 7, energy: 0.8, phase: '稳定' },
        { year: 8, energy: 0.6, phase: '下降' },
        { year: 9, energy: 0.3, phase: '衰退' },
        { year: 10, energy: 0.1, phase: '末期' }
      ],
      // 大运交接期的特殊影响
      transition_period: {
        duration: 2, // 交接期持续2年
        instability_factor: 1.5, // 不稳定系数
        description: '大运交接期，运势波动较大'
      }
    };

    // 流年天干地支循环
    this.liuNianCycle = {
      gans: ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
      zhis: ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
    };

    // 刑冲合害配置
    this.clashConfig = {
      // 地支六冲
      liu_chong: [
        { pair: ['子', '午'], intensity: 1.0, effect: '水火相冲，情绪波动' },
        { pair: ['丑', '未'], intensity: 0.8, effect: '土土相冲，变动较缓' },
        { pair: ['寅', '申'], intensity: 1.0, effect: '木金相冲，事业变动' },
        { pair: ['卯', '酉'], intensity: 1.0, effect: '木金相冲，人际冲突' },
        { pair: ['辰', '戌'], intensity: 0.8, effect: '土土相冲，环境变化' },
        { pair: ['巳', '亥'], intensity: 1.0, effect: '火水相冲，健康注意' }
      ],
      // 地支三合
      san_he: [
        { group: ['申', '子', '辰'], element: '水', effect: '水局成，智慧增长' },
        { group: ['亥', '卯', '未'], element: '木', effect: '木局成，事业发展' },
        { group: ['寅', '午', '戌'], element: '火', effect: '火局成，名声显达' },
        { group: ['巳', '酉', '丑'], element: '金', effect: '金局成，财运亨通' }
      ],
      // 地支六合
      liu_he: [
        { pair: ['子', '丑'], effect: '土水相合，稳中求进' },
        { pair: ['寅', '亥'], effect: '木水相合，智慧与行动并重' },
        { pair: ['卯', '戌'], effect: '木土相合，踏实发展' },
        { pair: ['辰', '酉'], effect: '土金相合，财运稳定' },
        { pair: ['巳', '申'], effect: '火金相合，技艺精进' },
        { pair: ['午', '未'], effect: '火土相合，名利双收' }
      ]
    };

    // 社会环境因素配置
    this.socialFactors = {
      // 经济周期影响
      economic_cycles: {
        prosperity: { factor: 1.2, description: '经济繁荣期，财运机会增多' },
        recession: { factor: 0.8, description: '经济衰退期，需谨慎理财' },
        recovery: { factor: 1.1, description: '经济复苏期，适合投资创业' },
        stable: { factor: 1.0, description: '经济稳定期，按部就班发展' }
      },
      // 行业趋势影响
      industry_trends: {
        tech: { growth_rate: 1.3, volatility: 1.4, description: '科技行业高增长高波动' },
        finance: { growth_rate: 1.1, volatility: 1.2, description: '金融行业稳定增长' },
        manufacturing: { growth_rate: 1.0, volatility: 0.9, description: '制造业稳定发展' },
        service: { growth_rate: 1.1, volatility: 0.8, description: '服务业稳步增长' },
        traditional: { growth_rate: 0.9, volatility: 0.7, description: '传统行业缓慢增长' }
      },
      // 年龄阶段影响
      age_phases: {
        youth: { energy: 1.3, stability: 0.7, description: '青年期精力充沛但不够稳定' },
        prime: { energy: 1.2, stability: 1.2, description: '壮年期精力与稳定性并重' },
        mature: { energy: 1.0, stability: 1.3, description: '成熟期稳定性强' },
        senior: { energy: 0.8, stability: 1.1, description: '老年期需注重健康' }
      }
    };

    // 五行映射
    this.wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };

    this.zhiWuxingMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
  }

  /**
   * 主要动态分析算法
   * @param {Object} bazi - 八字信息
   * @param {Object} yongshen - 用神信息
   * @param {Object} personalInfo - 个人信息
   * @param {Object} analysisOptions - 分析选项
   * @returns {Object} 动态分析结果
   */
  analyzeDynamicTrends(bazi, yongshen, personalInfo, analysisOptions = {}) {
    // 检查缓存
    const cacheKey = this.getCacheKey(bazi, yongshen, personalInfo, analysisOptions);
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log('✅ 使用缓存结果');
      return cachedResult;
    }

    try {
      console.log('🎯 开始动态分析模块');

      const currentYear = new Date().getFullYear();
      const birthYear = personalInfo.birth_year || 1990;
      const currentAge = currentYear - birthYear;

      // 1. 大运分析
      const dayunAnalysis = this.analyzeDayun(bazi, currentAge, analysisOptions.dayun_years || 10);
      console.log('🌊 大运分析:', dayunAnalysis);

      // 2. 流年分析
      const liuNianAnalysis = this.analyzeLiuNian(bazi, yongshen, currentYear, analysisOptions.forecast_years || 5);
      console.log('📅 流年分析:', liuNianAnalysis);

      // 3. 关键转折点检测
      const turningPoints = this.detectTurningPoints(dayunAnalysis, liuNianAnalysis, bazi);
      console.log('🔄 转折点检测:', turningPoints);

      // 4. 社会环境因素注入
      const socialImpact = this.analyzeSocialFactors(personalInfo, analysisOptions.social_context);
      console.log('🌍 社会环境分析:', socialImpact);

      // 5. 综合动态评估
      const dynamicForecast = this.generateDynamicForecast(dayunAnalysis, liuNianAnalysis, turningPoints, socialImpact);
      console.log('🔮 动态预测:', dynamicForecast);

      const result = {
        dayun_analysis: dayunAnalysis,
        liunian_analysis: liuNianAnalysis,
        turning_points: turningPoints,
        social_impact: socialImpact,
        dynamic_forecast: dynamicForecast,
        analysis_timestamp: new Date().toISOString(),
        confidence: this.calculateDynamicConfidence(dayunAnalysis, liuNianAnalysis, turningPoints)
      };

      // 保存到缓存
      this.setToCache(cacheKey, result);

      return result;

    } catch (error) {
      console.error('❌ 动态分析失败:', error);
      return {
        dayun_analysis: { error: '大运分析失败' },
        liunian_analysis: { error: '流年分析失败' },
        turning_points: [],
        social_impact: { error: '社会环境分析失败' },
        dynamic_forecast: { error: '动态预测失败' },
        analysis_timestamp: new Date().toISOString(),
        confidence: 0.3,
        error: error.message
      };
    }
  }

  /**
   * 大运分析
   */
  analyzeDayun(bazi, currentAge, analysisYears) {
    const dayunPeriods = [];
    const startAge = Math.floor(currentAge / 10) * 10; // 当前大运起始年龄
    
    for (let i = 0; i < Math.ceil(analysisYears / 10); i++) {
      const periodStartAge = startAge + i * 10;
      const periodEndAge = periodStartAge + 9;
      
      // 计算大运干支（简化版）
      const dayunGan = this.liuNianCycle.gans[(Math.floor(periodStartAge / 10) + 2) % 10];
      const dayunZhi = this.liuNianCycle.zhis[(Math.floor(periodStartAge / 10) + 2) % 12];
      
      const dayunPeriod = {
        start_age: periodStartAge,
        end_age: periodEndAge,
        gan: dayunGan,
        zhi: dayunZhi,
        wuxing: this.wuxingMap[dayunGan],
        zhi_wuxing: this.zhiWuxingMap[dayunZhi],
        energy_curve: this.calculateDayunEnergy(periodStartAge, currentAge),
        interactions: this.analyzeDayunInteractions(dayunGan, dayunZhi, bazi),
        overall_trend: null
      };

      // 评估大运整体趋势
      dayunPeriod.overall_trend = this.evaluateDayunTrend(dayunPeriod, bazi);
      dayunPeriods.push(dayunPeriod);
    }

    return {
      current_dayun: dayunPeriods[0],
      upcoming_dayuns: dayunPeriods.slice(1),
      transition_analysis: this.analyzeTransitionPeriods(dayunPeriods, currentAge)
    };
  }

  /**
   * 计算大运能量分布 - 优化版
   * 使用数组索引直接访问，避免find查找
   */
  calculateDayunEnergy(dayunStartAge, currentAge) {
    const ageInDayun = currentAge - dayunStartAge + 1;
    const yearInCycle = Math.max(1, Math.min(10, ageInDayun));

    // 直接使用数组索引访问，比find方法快
    const energyData = this.dayunDecayConfig.energy_curve[yearInCycle - 1];

    return {
      current_year: yearInCycle,
      energy_level: energyData.energy,
      phase: energyData.phase,
      description: `大运第${yearInCycle}年，处于${energyData.phase}阶段`
    };
  }

  /**
   * 分析大运与命局的相互作用
   */
  analyzeDayunInteractions(dayunGan, dayunZhi, bazi) {
    const interactions = [];
    
    // 检查大运与日主的关系
    const dayMaster = bazi.fourPillars ? bazi.fourPillars[2].gan : '甲';
    const dayunToMaster = this.getTenGodRelation(dayMaster, dayunGan);
    
    interactions.push({
      type: '大运与日主',
      relation: dayunToMaster,
      description: `大运${dayunGan}对日主${dayMaster}为${dayunToMaster}关系`
    });

    // 检查大运与月令的关系
    if (bazi.fourPillars && bazi.fourPillars[1]) {
      const monthZhi = bazi.fourPillars[1].zhi;
      const clashResult = this.checkClash(dayunZhi, monthZhi);
      
      if (clashResult) {
        interactions.push({
          type: '大运与月令',
          relation: clashResult.type,
          description: clashResult.description,
          intensity: clashResult.intensity
        });
      }
    }

    return interactions;
  }

  /**
   * 优化版辅助方法
   */

  // 优化的流年冲克分析
  analyzeYearlyClashesOptimized(liuNianZhi, baziZhis) {
    const clashPairs = [
      ['子', '午'], ['丑', '未'], ['寅', '申'],
      ['卯', '酉'], ['辰', '戌'], ['巳', '亥']
    ];

    let clashCount = 0;
    for (const pair of clashPairs) {
      if ((liuNianZhi === pair[0] && baziZhis.includes(pair[1])) ||
          (liuNianZhi === pair[1] && baziZhis.includes(pair[0]))) {
        clashCount++;
      }
    }

    return {
      clash_count: clashCount,
      severity: clashCount > 1 ? 'high' : clashCount > 0 ? 'medium' : 'low',
      description: clashCount > 0 ? `${liuNianZhi}与命局存在${clashCount}处冲克` : '无明显冲克'
    };
  }

  // 优化的流年运势评估
  evaluateYearlyFortuneOptimized(yearAnalysis, yongshenElement) {
    const baseScore = 0.5;
    const yongshenScore = yearAnalysis.yongshen_impact.score;
    const clashPenalty = yearAnalysis.clash_analysis.clash_count * 0.1;

    const finalScore = Math.max(0.1, Math.min(0.9, baseScore + (yongshenScore - 0.5) * 0.6 - clashPenalty));

    return {
      score: finalScore,
      level: finalScore > 0.7 ? 'excellent' : finalScore > 0.5 ? 'good' : finalScore > 0.3 ? 'average' : 'poor',
      description: `综合运势评分：${(finalScore * 100).toFixed(0)}分`
    };
  }

  // 优化的关键事件预测
  predictKeyEventsOptimized(yearAnalysis, baziZhis) {
    const events = [];

    if (yearAnalysis.clash_analysis.clash_count > 1) {
      events.push({
        type: '重大变动',
        probability: 0.7,
        description: '可能面临重大变动或挑战'
      });
    }

    if (yearAnalysis.yongshen_impact.score > 0.8) {
      events.push({
        type: '机遇年',
        probability: 0.8,
        description: '用神得力，适合发展'
      });
    }

    return events;
  }

  // 优化的整体趋势计算
  calculateOverallTrendOptimized(liuNianAnalysis) {
    if (liuNianAnalysis.length === 0) {
      return { direction: '平稳', slope: 0, average_score: 0.5, description: '无数据' };
    }

    const scores = liuNianAnalysis.map(year => year.fortune_trend.score);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // 简化的趋势计算
    const firstHalf = scores.slice(0, Math.ceil(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));

    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;

    const slope = secondAvg - firstAvg;
    const direction = slope > 0.1 ? '上升' : slope < -0.1 ? '下降' : '平稳';

    return {
      direction: direction,
      slope: slope,
      average_score: averageScore,
      description: `整体运势呈${direction}趋势，平均得分${(averageScore * 100).toFixed(0)}分`
    };
  }

  /**
   * 流年分析 - 优化版
   * 预计算常用数据，减少重复查找
   */
  analyzeLiuNian(bazi, yongshen, startYear, forecastYears) {
    const liuNianAnalysis = [];

    // 预计算用神信息，避免重复判断
    const yongshenElement = yongshen?.yongshen;
    const hasValidYongshen = Boolean(yongshenElement);

    // 预计算八字地支，用于冲克分析
    const baziZhis = [bazi.year.earthly, bazi.month.earthly, bazi.day.earthly, bazi.hour.earthly];

    for (let i = 0; i < forecastYears; i++) {
      const year = startYear + i;
      const ganIndex = (year - 4) % 10;
      const zhiIndex = (year - 4) % 12;
      const liuNianGan = this.liuNianCycle.gans[ganIndex];
      const liuNianZhi = this.liuNianCycle.zhis[zhiIndex];

      // 预计算五行属性
      const ganWuxing = this.wuxingMap[liuNianGan];
      const zhiWuxing = this.zhiWuxingMap[liuNianZhi];

      const yearAnalysis = {
        year: year,
        gan: liuNianGan,
        zhi: liuNianZhi,
        wuxing: ganWuxing,
        zhi_wuxing: zhiWuxing,
        yongshen_impact: this.analyzeYongshenImpactOptimized(ganWuxing, zhiWuxing, yongshenElement, hasValidYongshen),
        clash_analysis: this.analyzeYearlyClashesOptimized(liuNianZhi, baziZhis),
        fortune_trend: null,
        key_events: []
      };

      // 评估流年运势趋势（简化版）
      yearAnalysis.fortune_trend = this.evaluateYearlyFortuneOptimized(yearAnalysis, yongshenElement);

      // 预测关键事件（简化版）
      yearAnalysis.key_events = this.predictKeyEventsOptimized(yearAnalysis, baziZhis);

      liuNianAnalysis.push(yearAnalysis);
    }

    return {
      yearly_analysis: liuNianAnalysis,
      overall_trend: this.calculateOverallTrendOptimized(liuNianAnalysis),
      best_years: [],  // 简化处理
      challenging_years: []  // 简化处理
    };
  }

  /**
   * 分析用神在流年中的影响 - 优化版
   */
  analyzeYongshenImpactOptimized(ganWuxing, zhiWuxing, yongshenElement, hasValidYongshen) {
    if (!hasValidYongshen) {
      return { impact: 'neutral', score: 0.5, description: '用神信息不足' };
    }

    let impactScore = 0.5; // 基础分数
    let impactType = 'neutral';

    // 简化的用神影响计算
    if (ganWuxing === yongshenElement || zhiWuxing === yongshenElement) {
      impactScore = 0.8;
      impactType = 'favorable';
    } else if (this.isWuxingKe(ganWuxing, yongshenElement) || this.isWuxingKe(zhiWuxing, yongshenElement)) {
      impactScore = 0.3;
      impactType = 'unfavorable';
    }

    return {
      impact: impactType,
      score: impactScore,
      description: `流年对用神${yongshenElement}的影响：${impactType}`
    };
  }

  /**
   * 分析用神在流年中的影响 - 原版（保留兼容性）
   */
  analyzeYongshenImpact(liuNianGan, liuNianZhi, yongshen) {
    if (!yongshen || !yongshen.yongshen) {
      return { impact: 'neutral', score: 0.5, description: '用神信息不足' };
    }

    const yongshenElement = yongshen.yongshen;
    const liuNianGanElement = this.wuxingMap[liuNianGan];
    const liuNianZhiElement = this.zhiWuxingMap[liuNianZhi];

    let impactScore = 0.5; // 基础分数
    let impactType = 'neutral';
    let description = '';

    // 检查流年天干是否为用神
    if (liuNianGanElement === yongshenElement) {
      impactScore += 0.3;
      impactType = 'favorable';
      description += `流年天干${liuNianGan}为用神${yongshenElement}，运势上升；`;
    }

    // 检查流年地支是否为用神
    if (liuNianZhiElement === yongshenElement) {
      impactScore += 0.2;
      impactType = 'favorable';
      description += `流年地支${liuNianZhi}为用神${yongshenElement}，基础运势良好；`;
    }

    // 检查是否为忌神
    if (yongshen.jishen && yongshen.jishen.includes(liuNianGanElement)) {
      impactScore -= 0.3;
      impactType = 'unfavorable';
      description += `流年天干${liuNianGan}为忌神，需要谨慎；`;
    }

    // 检查五行生克关系
    if (this.isWuxingSheng(liuNianGanElement, yongshenElement)) {
      impactScore += 0.15;
      description += `流年${liuNianGanElement}生用神${yongshenElement}，有助运势；`;
    } else if (this.isWuxingKe(liuNianGanElement, yongshenElement)) {
      impactScore -= 0.15;
      description += `流年${liuNianGanElement}克用神${yongshenElement}，运势受阻；`;
    }

    // 确定最终影响类型
    if (impactScore > 0.65) impactType = 'very_favorable';
    else if (impactScore > 0.55) impactType = 'favorable';
    else if (impactScore < 0.35) impactType = 'very_unfavorable';
    else if (impactScore < 0.45) impactType = 'unfavorable';

    return {
      impact: impactType,
      score: Math.max(0, Math.min(1, impactScore)),
      description: description || '流年对用神影响中性',
      details: {
        gan_impact: liuNianGanElement === yongshenElement ? 'positive' : 'neutral',
        zhi_impact: liuNianZhiElement === yongshenElement ? 'positive' : 'neutral'
      }
    };
  }

  /**
   * 分析流年刑冲合害
   */
  analyzeYearlyClashes(liuNianZhi, bazi) {
    const clashes = [];

    if (!bazi.fourPillars) {
      return { clashes: [], summary: '命局信息不足' };
    }

    // 检查与命局各柱的刑冲合害
    bazi.fourPillars.forEach((pillar, index) => {
      const pillarNames = ['年', '月', '日', '时'];
      const clashResult = this.checkClash(liuNianZhi, pillar.zhi);

      if (clashResult) {
        clashes.push({
          type: clashResult.type,
          pillar: pillarNames[index] + '支',
          intensity: clashResult.intensity,
          effect: clashResult.effect,
          description: `流年${liuNianZhi}与${pillarNames[index]}支${pillar.zhi}${clashResult.type}`
        });
      }
    });

    return {
      clashes: clashes,
      summary: clashes.length > 0 ? `发现${clashes.length}个刑冲合害关系` : '无明显刑冲合害',
      total_intensity: clashes.reduce((sum, clash) => sum + clash.intensity, 0)
    };
  }

  /**
   * 检查刑冲合害关系
   */
  checkClash(zhi1, zhi2) {
    // 检查六冲
    for (const chong of this.clashConfig.liu_chong) {
      if ((chong.pair[0] === zhi1 && chong.pair[1] === zhi2) ||
          (chong.pair[0] === zhi2 && chong.pair[1] === zhi1)) {
        return {
          type: '相冲',
          intensity: chong.intensity,
          effect: chong.effect
        };
      }
    }

    // 检查六合
    for (const he of this.clashConfig.liu_he) {
      if ((he.pair[0] === zhi1 && he.pair[1] === zhi2) ||
          (he.pair[0] === zhi2 && he.pair[1] === zhi1)) {
        return {
          type: '相合',
          intensity: 0.8,
          effect: he.effect
        };
      }
    }

    return null;
  }

  /**
   * 评估流年运势
   */
  evaluateYearlyFortune(yearAnalysis, bazi, yongshen) {
    let fortuneScore = 0.5; // 基础分数
    const factors = [];

    // 用神影响权重40%
    const yongshenImpact = yearAnalysis.yongshen_impact.score;
    fortuneScore += (yongshenImpact - 0.5) * 0.4;
    factors.push({
      factor: '用神影响',
      weight: 0.4,
      score: yongshenImpact,
      description: yearAnalysis.yongshen_impact.description
    });

    // 刑冲合害影响权重30%
    const clashImpact = this.calculateClashImpact(yearAnalysis.clash_analysis);
    fortuneScore += clashImpact * 0.3;
    factors.push({
      factor: '刑冲合害',
      weight: 0.3,
      score: clashImpact + 0.5,
      description: yearAnalysis.clash_analysis.summary
    });

    // 五行平衡影响权重30%
    const balanceImpact = this.calculateBalanceImpact(yearAnalysis, bazi);
    fortuneScore += balanceImpact * 0.3;
    factors.push({
      factor: '五行平衡',
      weight: 0.3,
      score: balanceImpact + 0.5,
      description: '流年对命局五行平衡的影响'
    });

    // 确定运势等级
    let fortuneLevel;
    if (fortuneScore > 0.8) fortuneLevel = '大吉';
    else if (fortuneScore > 0.65) fortuneLevel = '中吉';
    else if (fortuneScore > 0.55) fortuneLevel = '小吉';
    else if (fortuneScore > 0.45) fortuneLevel = '平常';
    else if (fortuneScore > 0.35) fortuneLevel = '小凶';
    else if (fortuneScore > 0.2) fortuneLevel = '中凶';
    else fortuneLevel = '大凶';

    return {
      score: Math.max(0, Math.min(1, fortuneScore)),
      level: fortuneLevel,
      factors: factors,
      summary: `${yearAnalysis.year}年运势${fortuneLevel}，综合得分${(fortuneScore * 100).toFixed(0)}分`
    };
  }

  /**
   * 计算刑冲合害影响
   */
  calculateClashImpact(clashAnalysis) {
    if (!clashAnalysis.clashes || clashAnalysis.clashes.length === 0) {
      return 0; // 无刑冲合害，中性影响
    }

    let totalImpact = 0;
    clashAnalysis.clashes.forEach(clash => {
      if (clash.type === '相冲') {
        totalImpact -= clash.intensity * 0.3; // 相冲为负面影响
      } else if (clash.type === '相合') {
        totalImpact += clash.intensity * 0.2; // 相合为正面影响
      }
    });

    return Math.max(-0.5, Math.min(0.5, totalImpact));
  }

  /**
   * 计算五行平衡影响
   */
  calculateBalanceImpact(yearAnalysis, bazi) {
    // 简化的五行平衡计算
    const yearWuxing = yearAnalysis.wuxing;
    const yearZhiWuxing = yearAnalysis.zhi_wuxing;

    // 检查流年是否有助于命局平衡
    if (bazi.element_powers && bazi.element_powers.percentages) {
      const percentages = bazi.element_powers.percentages;
      const weakestElement = Object.keys(percentages).reduce((a, b) =>
        percentages[a] < percentages[b] ? a : b
      );

      let balanceImpact = 0;

      // 如果流年补强最弱的五行
      if (yearWuxing === weakestElement) {
        balanceImpact += 0.3;
      }
      if (yearZhiWuxing === weakestElement) {
        balanceImpact += 0.2;
      }

      return Math.max(-0.5, Math.min(0.5, balanceImpact));
    }

    return 0; // 无法计算，返回中性
  }

  /**
   * 预测关键事件
   */
  predictKeyEvents(yearAnalysis, bazi) {
    const events = [];

    // 基于刑冲合害预测事件
    if (yearAnalysis.clash_analysis.clashes.length > 0) {
      yearAnalysis.clash_analysis.clashes.forEach(clash => {
        if (clash.type === '相冲' && clash.intensity > 0.8) {
          events.push({
            type: '重大变动',
            probability: 0.7,
            description: `${clash.description}，可能带来重大变动`,
            timing: '全年',
            impact: 'high'
          });
        } else if (clash.type === '相合') {
          events.push({
            type: '合作机会',
            probability: 0.6,
            description: `${clash.description}，有利于合作发展`,
            timing: '全年',
            impact: 'medium'
          });
        }
      });
    }

    // 基于用神影响预测事件
    const yongshenImpact = yearAnalysis.yongshen_impact;
    if (yongshenImpact.score > 0.7) {
      events.push({
        type: '运势提升',
        probability: 0.8,
        description: '用神得力，整体运势上升，适合主动出击',
        timing: '全年',
        impact: 'high'
      });
    } else if (yongshenImpact.score < 0.3) {
      events.push({
        type: '谨慎保守',
        probability: 0.7,
        description: '用神受制，宜保守行事，避免重大决策',
        timing: '全年',
        impact: 'medium'
      });
    }

    return events;
  }

  /**
   * 五行相生判断
   */
  isWuxingSheng(from, to) {
    const shengMap = {
      '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
    };
    return shengMap[from] === to;
  }

  /**
   * 五行相克判断
   */
  isWuxingKe(from, to) {
    const keMap = {
      '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
    };
    return keMap[from] === to;
  }

  /**
   * 获取十神关系
   */
  getTenGodRelation(dayGan, targetGan) {
    const tenGodsMap = {
      '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
      '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
      '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
      '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
      '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
      '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
      '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
      '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
      '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
      '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
    };

    return tenGodsMap[dayGan] && tenGodsMap[dayGan][targetGan] || '未知';
  }

  /**
   * 关键转折点检测
   */
  detectTurningPoints(dayunAnalysis, liuNianAnalysis, bazi) {
    const turningPoints = [];

    // 检测大运交接期
    if (dayunAnalysis.transition_analysis) {
      dayunAnalysis.transition_analysis.forEach(transition => {
        turningPoints.push({
          type: '大运交接',
          timing: transition.period,
          intensity: 'high',
          description: transition.description,
          impact: '运势格局重大变化',
          advice: '大运交接期宜谨慎，避免重大决策'
        });
      });
    }

    // 检测流年重大刑冲
    liuNianAnalysis.yearly_analysis.forEach(year => {
      if (year.clash_analysis.total_intensity > 1.5) {
        turningPoints.push({
          type: '流年重冲',
          timing: year.year,
          intensity: 'high',
          description: `${year.year}年刑冲较重`,
          impact: '可能面临重大变动或挑战',
          advice: '此年需特别谨慎，做好应对准备'
        });
      }
    });

    // 检测用神激活年份
    liuNianAnalysis.yearly_analysis.forEach(year => {
      if (year.yongshen_impact.score > 0.8) {
        turningPoints.push({
          type: '用神激活',
          timing: year.year,
          intensity: 'medium',
          description: `${year.year}年用神得力`,
          impact: '运势显著提升，适合发展',
          advice: '把握机遇，主动出击'
        });
      }
    });

    // 按时间排序
    turningPoints.sort((a, b) => {
      const timeA = typeof a.timing === 'number' ? a.timing : parseInt(a.timing);
      const timeB = typeof b.timing === 'number' ? b.timing : parseInt(b.timing);
      return timeA - timeB;
    });

    return turningPoints;
  }

  /**
   * 社会环境因素分析
   */
  analyzeSocialFactors(personalInfo, socialContext = {}) {
    const factors = [];
    let overallImpact = 1.0;

    // 年龄阶段影响
    const age = personalInfo.age || 30;
    let agePhase;
    if (age < 30) agePhase = 'youth';
    else if (age < 45) agePhase = 'prime';
    else if (age < 60) agePhase = 'mature';
    else agePhase = 'senior';

    const ageConfig = this.socialFactors.age_phases[agePhase];
    factors.push({
      type: '年龄阶段',
      phase: agePhase,
      energy_factor: ageConfig.energy,
      stability_factor: ageConfig.stability,
      description: ageConfig.description
    });
    overallImpact *= (ageConfig.energy + ageConfig.stability) / 2;

    // 经济环境影响
    const economicPhase = socialContext.economic_phase || 'stable';
    const economicConfig = this.socialFactors.economic_cycles[economicPhase];
    if (economicConfig) {
      factors.push({
        type: '经济环境',
        phase: economicPhase,
        factor: economicConfig.factor,
        description: economicConfig.description
      });
      overallImpact *= economicConfig.factor;
    }

    // 行业趋势影响
    const industry = socialContext.industry || 'service';
    const industryConfig = this.socialFactors.industry_trends[industry];
    if (industryConfig) {
      factors.push({
        type: '行业趋势',
        industry: industry,
        growth_rate: industryConfig.growth_rate,
        volatility: industryConfig.volatility,
        description: industryConfig.description
      });
      overallImpact *= industryConfig.growth_rate;
    }

    return {
      factors: factors,
      overall_impact: Math.max(0.5, Math.min(2.0, overallImpact)),
      summary: `社会环境综合影响系数：${overallImpact.toFixed(2)}`,
      recommendations: this.generateSocialRecommendations(factors)
    };
  }

  /**
   * 生成社会环境建议
   */
  generateSocialRecommendations(factors) {
    const recommendations = [];

    factors.forEach(factor => {
      switch (factor.type) {
        case '年龄阶段':
          if (factor.phase === 'youth') {
            recommendations.push('青年期应注重学习和积累，为未来发展打基础');
          } else if (factor.phase === 'prime') {
            recommendations.push('壮年期是事业发展的黄金时期，应积极进取');
          } else if (factor.phase === 'mature') {
            recommendations.push('成熟期应注重稳健发展，传承经验');
          } else if (factor.phase === 'senior') {
            recommendations.push('老年期应注重健康保养，享受生活');
          }
          break;

        case '经济环境':
          if (factor.factor > 1.1) {
            recommendations.push('经济环境良好，适合投资和扩张');
          } else if (factor.factor < 0.9) {
            recommendations.push('经济环境不佳，应保守理财，控制风险');
          }
          break;

        case '行业趋势':
          if (factor.growth_rate > 1.2) {
            recommendations.push('所在行业发展迅速，应把握机遇');
          } else if (factor.volatility > 1.3) {
            recommendations.push('行业波动较大，需要灵活应对');
          }
          break;
      }
    });

    return recommendations;
  }

  /**
   * 生成动态预测
   */
  generateDynamicForecast(dayunAnalysis, liuNianAnalysis, turningPoints, socialImpact) {
    const forecast = {
      short_term: this.generateShortTermForecast(liuNianAnalysis, socialImpact),
      medium_term: this.generateMediumTermForecast(dayunAnalysis, liuNianAnalysis),
      long_term: this.generateLongTermForecast(dayunAnalysis, turningPoints),
      key_recommendations: this.generateKeyRecommendations(dayunAnalysis, liuNianAnalysis, turningPoints)
    };

    return forecast;
  }

  /**
   * 短期预测（1-2年）
   */
  generateShortTermForecast(liuNianAnalysis, socialImpact) {
    const nearYears = liuNianAnalysis.yearly_analysis.slice(0, 2);
    const avgScore = nearYears.reduce((sum, year) => sum + year.fortune_trend.score, 0) / nearYears.length;

    let trend;
    if (avgScore > 0.7) trend = '上升';
    else if (avgScore > 0.5) trend = '稳定';
    else trend = '下降';

    return {
      trend: trend,
      score: avgScore,
      description: `未来1-2年运势${trend}，综合得分${(avgScore * 100).toFixed(0)}分`,
      key_years: nearYears.map(year => ({
        year: year.year,
        level: year.fortune_trend.level,
        summary: year.fortune_trend.summary
      })),
      social_adjustment: socialImpact.overall_impact
    };
  }

  /**
   * 中期预测（3-5年）
   */
  generateMediumTermForecast(dayunAnalysis, liuNianAnalysis) {
    const mediumYears = liuNianAnalysis.yearly_analysis.slice(2, 5);
    const currentDayun = dayunAnalysis.current_dayun;

    return {
      dayun_influence: {
        gan: currentDayun.gan,
        zhi: currentDayun.zhi,
        energy_phase: currentDayun.energy_curve.phase,
        overall_trend: currentDayun.overall_trend
      },
      yearly_highlights: mediumYears.map(year => ({
        year: year.year,
        level: year.fortune_trend.level,
        key_events: year.key_events
      })),
      development_advice: '中期发展建议基于大运和流年综合分析'
    };
  }

  /**
   * 长期预测（5-10年）
   */
  generateLongTermForecast(dayunAnalysis, turningPoints) {
    const upcomingDayuns = dayunAnalysis.upcoming_dayuns;
    const majorTurningPoints = turningPoints.filter(tp => tp.intensity === 'high');

    return {
      dayun_transitions: upcomingDayuns.map(dayun => ({
        period: `${dayun.start_age}-${dayun.end_age}岁`,
        gan_zhi: `${dayun.gan}${dayun.zhi}`,
        trend: dayun.overall_trend,
        key_characteristics: `${dayun.wuxing}运，${dayun.zhi_wuxing}支`
      })),
      major_turning_points: majorTurningPoints,
      strategic_planning: '长期战略规划建议'
    };
  }

  /**
   * 生成关键建议
   */
  generateKeyRecommendations(dayunAnalysis, liuNianAnalysis, turningPoints) {
    const recommendations = [];

    // 基于当前大运的建议
    const currentDayun = dayunAnalysis.current_dayun;
    recommendations.push({
      category: '大运建议',
      content: `当前${currentDayun.gan}${currentDayun.zhi}大运，处于${currentDayun.energy_curve.phase}阶段，${currentDayun.energy_curve.description}`
    });

    // 基于最佳年份的建议
    const bestYears = liuNianAnalysis.best_years;
    if (bestYears && bestYears.length > 0) {
      recommendations.push({
        category: '机遇把握',
        content: `${bestYears.map(y => y.year).join('、')}年运势较佳，适合重要决策和发展`
      });
    }

    // 基于挑战年份的建议
    const challengingYears = liuNianAnalysis.challenging_years;
    if (challengingYears && challengingYears.length > 0) {
      recommendations.push({
        category: '风险防范',
        content: `${challengingYears.map(y => y.year).join('、')}年需要谨慎，避免重大变动`
      });
    }

    // 基于转折点的建议
    turningPoints.forEach(tp => {
      recommendations.push({
        category: '转折应对',
        content: tp.advice
      });
    });

    return recommendations;
  }

  /**
   * 计算动态分析置信度
   */
  calculateDynamicConfidence(dayunAnalysis, liuNianAnalysis, turningPoints) {
    let confidence = 0.7; // 基础置信度

    // 大运分析完整性
    if (dayunAnalysis.current_dayun && dayunAnalysis.current_dayun.energy_curve) {
      confidence += 0.1;
    }

    // 流年分析完整性
    if (liuNianAnalysis.yearly_analysis && liuNianAnalysis.yearly_analysis.length >= 3) {
      confidence += 0.1;
    }

    // 转折点检测完整性
    if (turningPoints && turningPoints.length > 0) {
      confidence += 0.05;
    }

    return Math.min(0.95, confidence);
  }

  /**
   * 辅助方法：计算整体趋势
   */
  calculateOverallTrend(liuNianAnalysis) {
    const scores = liuNianAnalysis.map(year => year.fortune_trend.score);
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // 计算趋势斜率
    const n = scores.length;
    const sumX = (n * (n + 1)) / 2;
    const sumY = scores.reduce((sum, score) => sum + score, 0);
    const sumXY = scores.reduce((sum, score, index) => sum + score * (index + 1), 0);
    const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

    let trendDirection;
    if (slope > 0.05) trendDirection = '上升';
    else if (slope < -0.05) trendDirection = '下降';
    else trendDirection = '平稳';

    return {
      direction: trendDirection,
      slope: slope,
      average_score: avgScore,
      description: `整体运势呈${trendDirection}趋势，平均得分${(avgScore * 100).toFixed(0)}分`
    };
  }

  /**
   * 辅助方法：识别最佳年份
   */
  identifyBestYears(liuNianAnalysis) {
    return liuNianAnalysis
      .filter(year => year.fortune_trend.score > 0.7)
      .sort((a, b) => b.fortune_trend.score - a.fortune_trend.score)
      .slice(0, 3);
  }

  /**
   * 辅助方法：识别挑战年份
   */
  identifyChallengingYears(liuNianAnalysis) {
    return liuNianAnalysis
      .filter(year => year.fortune_trend.score < 0.4)
      .sort((a, b) => a.fortune_trend.score - b.fortune_trend.score)
      .slice(0, 3);
  }

  /**
   * 辅助方法：评估大运趋势
   */
  evaluateDayunTrend(dayunPeriod, bazi) {
    // 简化的大运趋势评估
    const dayunWuxing = dayunPeriod.wuxing;
    const energyLevel = dayunPeriod.energy_curve.energy_level;

    let trendScore = energyLevel; // 基于能量水平

    // 根据大运与命局的关系调整
    if (dayunPeriod.interactions && dayunPeriod.interactions.length > 0) {
      dayunPeriod.interactions.forEach(interaction => {
        if (interaction.relation && interaction.relation.includes('财')) {
          trendScore += 0.1;
        } else if (interaction.relation && interaction.relation.includes('官')) {
          trendScore += 0.1;
        }
      });
    }

    if (trendScore > 0.7) return '大吉';
    else if (trendScore > 0.5) return '中吉';
    else if (trendScore > 0.3) return '平常';
    else return '不利';
  }

  /**
   * 辅助方法：分析大运交接期
   */
  analyzeTransitionPeriods(dayunPeriods, currentAge) {
    const transitions = [];

    dayunPeriods.forEach((dayun, index) => {
      // 检查是否在交接期附近
      const transitionStart = dayun.start_age - 1;
      const transitionEnd = dayun.start_age + 1;

      if (currentAge >= transitionStart && currentAge <= transitionEnd) {
        transitions.push({
          period: `${transitionStart}-${transitionEnd}岁`,
          from_dayun: index > 0 ? dayunPeriods[index - 1] : null,
          to_dayun: dayun,
          description: this.dayunDecayConfig.transition_period.description,
          instability: this.dayunDecayConfig.transition_period.instability_factor
        });
      }
    });

    return transitions;
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedDynamicAnalyzer;
} else if (typeof window !== 'undefined') {
  window.EnhancedDynamicAnalyzer = EnhancedDynamicAnalyzer;
}
