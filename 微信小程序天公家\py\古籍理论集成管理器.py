#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
古籍理论集成管理器
统一管理《穷通宝鉴》、《渊海子平》、《滴天髓》、《三命通会》、《五行精纪》、《千里命稿》
六部经典古籍的理论体系，实现优势互补和冲突解决
"""

from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import json

class TheoryPriority(Enum):
    """理论优先级"""
    HIGHEST = 1    # 最高优先级
    HIGH = 2       # 高优先级  
    MEDIUM = 3     # 中等优先级
    LOW = 4        # 低优先级

class ConflictResolution(Enum):
    """冲突解决策略"""
    PRIORITY_BASED = "priority"      # 基于优先级
    WEIGHTED_AVERAGE = "weighted"    # 加权平均
    CONSENSUS = "consensus"          # 共识优先
    CONTEXT_BASED = "context"       # 基于上下文

@dataclass
class ClassicalTheory:
    """古籍理论"""
    book_name: str                   # 古籍名称
    theory_id: str                   # 理论ID
    category: str                    # 理论分类
    title: str                       # 理论标题
    content: str                     # 理论内容
    application: str                 # 应用场景
    priority: TheoryPriority         # 优先级
    confidence: float                # 可信度 0-1
    specialty: List[str]             # 专长领域
    examples: List[str]              # 应用示例
    conflicts_with: List[str]        # 冲突理论ID

@dataclass
class TheoryConflict:
    """理论冲突"""
    conflict_id: str                 # 冲突ID
    theories: List[str]              # 冲突理论ID列表
    conflict_type: str               # 冲突类型
    description: str                 # 冲突描述
    resolution_strategy: ConflictResolution  # 解决策略
    resolved_result: Any             # 解决结果

class ClassicalTheoryManager:
    """古籍理论集成管理器"""
    
    def __init__(self):
        """初始化古籍理论管理器"""
        print("📚 初始化古籍理论集成管理器...")
        
        # 初始化六部古籍理论数据库
        self.classical_theories = {}
        self.theory_weights = {}
        self.conflict_rules = {}
        
        # 加载各古籍理论
        self._load_qiongtong_baojian()      # 《穷通宝鉴》
        self._load_yuanhai_ziping()         # 《渊海子平》
        self._load_ditian_sui()             # 《滴天髓》
        self._load_sanming_tonghui()        # 《三命通会》
        self._load_wuxing_jingji()          # 《五行精纪》
        self._load_qianli_minggao()         # 《千里命稿》
        
        # 建立理论权重体系
        self._establish_theory_weights()
        
        # 建立冲突解决规则
        self._establish_conflict_rules()
        
        print(f"✅ 古籍理论管理器初始化完成")
        print(f"   📖 集成古籍: 6部经典")
        print(f"   🎯 理论总数: {len(self.classical_theories)}")
        print(f"   ⚖️ 权重规则: {len(self.theory_weights)}")
        print(f"   🔧 冲突规则: {len(self.conflict_rules)}")
    
    def _load_qiongtong_baojian(self):
        """加载《穷通宝鉴》理论"""
        theories = [
            ClassicalTheory(
                book_name="穷通宝鉴",
                theory_id="QT001",
                category="调候理论",
                title="春木调候要诀",
                content="甲木生于春月，余寒犹存，得丙火以暖，得癸水以润，方能发荣滋长",
                application="春季出生甲乙木日干的调候用神选择",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["调候", "季节用神", "木性分析"],
                examples=["甲寅月生用丙癸", "乙卯月生喜丙火"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="穷通宝鉴",
                theory_id="QT002", 
                category="调候理论",
                title="夏火调候要诀",
                content="丙火生于夏月，火势炎烈，急需壬癸水以济，方免焦枯之患",
                application="夏季出生丙丁火日干的调候用神选择",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["调候", "季节用神", "火性分析"],
                examples=["丙午月生用壬水", "丁未月生喜癸水"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="穷通宝鉴",
                theory_id="QT003",
                category="调候理论", 
                title="秋金调候要诀",
                content="庚金生于秋月，金寒水冷，必须丁火锻炼，方能成器",
                application="秋季出生庚辛金日干的调候用神选择",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["调候", "季节用神", "金性分析"],
                examples=["庚申月生用丁火", "辛酉月生喜丙火"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="穷通宝鉴",
                theory_id="QT004",
                category="调候理论",
                title="冬水调候要诀", 
                content="壬水生于冬月，水势汪洋，必须戊土以堤防，丙火以暖局",
                application="冬季出生壬癸水日干的调候用神选择",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["调候", "季节用神", "水性分析"],
                examples=["壬子月生用戊丙", "癸亥月生喜丁火"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="穷通宝鉴",
                theory_id="QT005",
                category="调候理论",
                title="四季土调候要诀",
                content="戊土生于四季月，土性燥湿不同，春夏喜水润，秋冬喜火暖",
                application="四季出生戊己土日干的调候用神选择",
                priority=TheoryPriority.HIGH,
                confidence=0.90,
                specialty=["调候", "季节用神", "土性分析"],
                examples=["戊辰月生用甲癸", "己未月生喜壬水"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _load_yuanhai_ziping(self):
        """加载《渊海子平》理论"""
        theories = [
            ClassicalTheory(
                book_name="渊海子平",
                theory_id="YH001",
                category="格局理论",
                title="正官格成败",
                content="正官格以月令正官为用，喜财生官，印护官，忌伤官见官，官杀混杂",
                application="正官格局的成败判断和富贵层次评估",
                priority=TheoryPriority.HIGH,
                confidence=0.92,
                specialty=["格局成败", "富贵判断", "官星分析"],
                examples=["甲生酉月透辛为正官格", "财官印三奇得配"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="渊海子平",
                theory_id="YH002",
                category="格局理论", 
                title="财格成败",
                content="财格以月令财星为用，喜身强任财，官星护财，忌比劫夺财，财多身弱",
                application="正财偏财格局的成败判断",
                priority=TheoryPriority.HIGH,
                confidence=0.92,
                specialty=["格局成败", "财运分析", "财星配置"],
                examples=["乙生辰月透戊为正财格", "身强财旺必富"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="渊海子平",
                theory_id="YH003",
                category="富贵贫贱",
                title="富贵贫贱总论",
                content="富者财气通门户，贵者官星得地位，贫者财官俱不真，贱者伤官见官星",
                application="命局富贵贫贱层次的综合判断",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["富贵判断", "贫贱识别", "层次评估"],
                examples=["财官双美必富贵", "伤官见官祸百端"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="渊海子平",
                theory_id="YH004",
                category="格局理论",
                title="印绶格成败",
                content="印绶格以月令印星为用，喜官生印，印生身，忌财坏印，印多身弱",
                application="正印偏印格局的成败判断",
                priority=TheoryPriority.HIGH,
                confidence=0.90,
                specialty=["格局成败", "印星分析", "学业文化"],
                examples=["甲生亥月透壬为偏印格", "官印相生贵格"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _load_ditian_sui(self):
        """加载《滴天髓》理论"""
        theories = [
            ClassicalTheory(
                book_name="滴天髓",
                theory_id="DT001",
                category="精神气质",
                title="天干精神论",
                content="甲木参天，脱胎要火；乙木花草，雨露是宝；丙火太阳，辉光普照",
                application="基于天干分析人的精神气质和性格特征",
                priority=TheoryPriority.HIGH,
                confidence=0.88,
                specialty=["性格分析", "精神气质", "天干象意"],
                examples=["甲木之人刚直不阿", "乙木之人柔韧坚强"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="滴天髓",
                theory_id="DT002",
                category="精神气质",
                title="地支性情论", 
                content="子水阳刚，丑土阴柔，寅木生发，卯木秀丽，辰土厚重",
                application="基于地支分析人的性情特点和行为模式",
                priority=TheoryPriority.HIGH,
                confidence=0.88,
                specialty=["性格分析", "行为模式", "地支象意"],
                examples=["子水之人机智灵活", "丑土之人稳重踏实"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="滴天髓",
                theory_id="DT003",
                category="用神理论",
                title="真神得用论",
                content="真神得用，富贵双全；真神失用，贫贱可知；假神得用，亦有小成",
                application="用神真假的判断和对命运层次的影响",
                priority=TheoryPriority.HIGHEST,
                confidence=0.93,
                specialty=["用神判断", "命运层次", "真假神辨"],
                examples=["真官得用必贵", "假财得用小富"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="滴天髓",
                theory_id="DT004",
                category="精神气质",
                title="通根透干论",
                content="天干透出，地支有根，方为有力；根深叶茂，自然发达",
                application="分析天干地支配合对性格和能力的影响",
                priority=TheoryPriority.MEDIUM,
                confidence=0.85,
                specialty=["干支配合", "能力分析", "发展潜力"],
                examples=["甲寅配合力量强", "透干无根力量弱"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _load_sanming_tonghui(self):
        """加载《三命通会》理论"""
        theories = [
            ClassicalTheory(
                book_name="三命通会",
                theory_id="SM001",
                category="神煞理论",
                title="天乙贵人",
                content="甲戊见牛羊，乙己鼠猴乡，丙丁猪鸡位，壬癸兔蛇藏，庚辛逢虎马",
                application="天乙贵人的查法和吉凶作用",
                priority=TheoryPriority.MEDIUM,
                confidence=0.80,
                specialty=["神煞查法", "贵人助力", "吉凶判断"],
                examples=["甲日见丑未为贵人", "贵人临身多助力"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="三命通会",
                theory_id="SM002",
                category="神煞理论",
                title="文昌贵人",
                content="甲乙巳午报君知，丙戊申宫丁己鸡，庚猪辛鼠壬逢虎，癸人见卯入云梯",
                application="文昌贵人的查法和对学业文化的影响",
                priority=TheoryPriority.MEDIUM,
                confidence=0.78,
                specialty=["神煞查法", "学业文化", "智慧才华"],
                examples=["甲日见巳为文昌", "文昌入命利考试"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="三命通会",
                theory_id="SM003",
                category="神煞理论",
                title="桃花煞",
                content="申子辰见酉，亥卯未见子，寅午戌见卯，巳酉丑见午",
                application="桃花煞的查法和对感情婚姻的影响",
                priority=TheoryPriority.LOW,
                confidence=0.70,
                specialty=["神煞查法", "感情婚姻", "异性缘分"],
                examples=["申子辰年见酉为桃花", "桃花旺者异性缘佳"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="三命通会",
                theory_id="SM004",
                category="吉凶判断",
                title="十恶大败",
                content="甲戌乙酉与壬申，丙申丁未及戊午，癸酉庚辰与辛巳，己丑都来十恶数",
                application="十恶大败日的查法和凶险程度判断",
                priority=TheoryPriority.LOW,
                confidence=0.65,
                specialty=["凶煞识别", "日柱吉凶", "灾祸预测"],
                examples=["甲戌日为十恶大败", "大败日生多波折"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _load_wuxing_jingji(self):
        """加载《五行精纪》理论"""
        theories = [
            ClassicalTheory(
                book_name="五行精纪",
                theory_id="WX001",
                category="纳音理论",
                title="纳音五行总论",
                content="甲子乙丑海中金，丙寅丁卯炉中火，戊辰己巳大林木",
                application="六十甲子纳音五行的查法和象意分析",
                priority=TheoryPriority.MEDIUM,
                confidence=0.82,
                specialty=["纳音查法", "象意分析", "古典理论"],
                examples=["甲子纳音海中金", "纳音相生主和谐"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="五行精纪",
                theory_id="WX002",
                category="古典排盘",
                title="古法排盘要诀",
                content="年上起月，日上起时，节气为准，不可差池",
                application="传统排盘方法和节气界定原则",
                priority=TheoryPriority.HIGH,
                confidence=0.90,
                specialty=["排盘方法", "节气界定", "传统技法"],
                examples=["立春换年柱", "节气定月令"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _load_qianli_minggao(self):
        """加载《千里命稿》理论"""
        theories = [
            ClassicalTheory(
                book_name="千里命稿",
                theory_id="QL001",
                category="强弱判断",
                title="日干强弱判断法",
                content="月令旺相、多帮扶、支得气，三者具备为最强；反之为最弱",
                application="精确的日干强弱判断标准",
                priority=TheoryPriority.HIGHEST,
                confidence=0.96,
                specialty=["强弱判断", "量化分析", "现代方法"],
                examples=["甲木春生多水木为强", "庚金夏生多火土为弱"],
                conflicts_with=[]
            ),
            ClassicalTheory(
                book_name="千里命稿",
                theory_id="QL002",
                category="用神理论",
                title="用神选取法",
                content="身强用克泄耗，身弱用生扶助，调候为先，通关次之",
                application="科学的用神选取方法和优先级",
                priority=TheoryPriority.HIGHEST,
                confidence=0.95,
                specialty=["用神选取", "科学方法", "优先级"],
                examples=["身强财弱用官杀", "身弱官重用印绶"],
                conflicts_with=[]
            )
        ]
        
        for theory in theories:
            self.classical_theories[theory.theory_id] = theory
    
    def _establish_theory_weights(self):
        """建立理论权重体系"""
        # 各古籍在不同领域的权重分配
        self.theory_weights = {
            "调候理论": {
                "穷通宝鉴": 0.40,    # 调候理论的权威
                "千里命稿": 0.25,    # 现代化应用
                "滴天髓": 0.15,      # 精神层面
                "渊海子平": 0.10,    # 格局配合
                "三命通会": 0.05,    # 神煞影响
                "五行精纪": 0.05     # 古典基础
            },
            "格局理论": {
                "渊海子平": 0.35,    # 格局理论鼻祖
                "千里命稿": 0.30,    # 现代化完善
                "滴天髓": 0.20,      # 精神升华
                "穷通宝鉴": 0.10,    # 调候配合
                "三命通会": 0.03,    # 神煞补充
                "五行精纪": 0.02     # 古典基础
            },
            "强弱判断": {
                "千里命稿": 0.50,    # 现代化标准
                "滴天髓": 0.25,      # 理论深度
                "渊海子平": 0.15,    # 传统方法
                "穷通宝鉴": 0.05,    # 调候影响
                "三命通会": 0.03,    # 神煞影响
                "五行精纪": 0.02     # 古典基础
            },
            "精神气质": {
                "滴天髓": 0.45,      # 精神分析权威
                "千里命稿": 0.25,    # 现代心理学
                "渊海子平": 0.15,    # 格局性格
                "穷通宝鉴": 0.10,    # 季节性格
                "三命通会": 0.03,    # 神煞性格
                "五行精纪": 0.02     # 古典基础
            },
            "神煞理论": {
                "三命通会": 0.50,    # 神煞大全
                "五行精纪": 0.25,    # 古典神煞
                "渊海子平": 0.15,    # 格局神煞
                "滴天髓": 0.05,      # 精神神煞
                "穷通宝鉴": 0.03,    # 调候神煞
                "千里命稿": 0.02     # 现代应用
            },
            "纳音理论": {
                "五行精纪": 0.50,    # 纳音权威
                "三命通会": 0.30,    # 纳音应用
                "渊海子平": 0.10,    # 格局纳音
                "滴天髓": 0.05,      # 精神纳音
                "穷通宝鉴": 0.03,    # 调候纳音
                "千里命稿": 0.02     # 现代应用
            }
        }
    
    def _establish_conflict_rules(self):
        """建立冲突解决规则"""
        self.conflict_rules = {
            "强弱判断冲突": {
                "strategy": ConflictResolution.PRIORITY_BASED,
                "priority_order": ["千里命稿", "滴天髓", "渊海子平"],
                "description": "强弱判断以现代化标准为主"
            },
            "用神选择冲突": {
                "strategy": ConflictResolution.WEIGHTED_AVERAGE,
                "weights": {"调候": 0.4, "扶抑": 0.4, "通关": 0.2},
                "description": "用神选择综合考虑多种因素"
            },
            "格局成败冲突": {
                "strategy": ConflictResolution.CONSENSUS,
                "consensus_threshold": 0.6,
                "description": "格局成败需要多数理论一致"
            },
            "神煞影响冲突": {
                "strategy": ConflictResolution.CONTEXT_BASED,
                "context_factors": ["命局强弱", "格局高低", "用神得失"],
                "description": "神煞影响根据具体命局判断"
            }
        }
    
    def get_theory_by_category(self, category: str) -> List[ClassicalTheory]:
        """根据分类获取理论"""
        return [theory for theory in self.classical_theories.values() 
                if theory.category == category]
    
    def get_theory_by_book(self, book_name: str) -> List[ClassicalTheory]:
        """根据古籍获取理论"""
        return [theory for theory in self.classical_theories.values() 
                if theory.book_name == book_name]
    
    def resolve_theory_conflict(self, conflicting_theories: List[str], 
                               context: Dict = None) -> Any:
        """解决理论冲突"""
        # 识别冲突类型
        conflict_type = self._identify_conflict_type(conflicting_theories)
        
        # 获取解决策略
        if conflict_type in self.conflict_rules:
            rule = self.conflict_rules[conflict_type]
            strategy = rule["strategy"]
            
            if strategy == ConflictResolution.PRIORITY_BASED:
                return self._resolve_by_priority(conflicting_theories, rule)
            elif strategy == ConflictResolution.WEIGHTED_AVERAGE:
                return self._resolve_by_weighted_average(conflicting_theories, rule)
            elif strategy == ConflictResolution.CONSENSUS:
                return self._resolve_by_consensus(conflicting_theories, rule)
            elif strategy == ConflictResolution.CONTEXT_BASED:
                return self._resolve_by_context(conflicting_theories, rule, context)
        
        # 默认使用优先级解决
        return self._resolve_by_default_priority(conflicting_theories)
    
    def _identify_conflict_type(self, theories: List[str]) -> str:
        """识别冲突类型"""
        # 简化实现，根据理论ID前缀判断
        categories = set()
        for theory_id in theories:
            if theory_id in self.classical_theories:
                categories.add(self.classical_theories[theory_id].category)
        
        if "强弱判断" in categories:
            return "强弱判断冲突"
        elif "用神理论" in categories:
            return "用神选择冲突"
        elif "格局理论" in categories:
            return "格局成败冲突"
        elif "神煞理论" in categories:
            return "神煞影响冲突"
        else:
            return "一般冲突"
    
    def _resolve_by_priority(self, theories: List[str], rule: Dict) -> str:
        """基于优先级解决冲突"""
        priority_order = rule.get("priority_order", [])
        
        for book in priority_order:
            for theory_id in theories:
                if theory_id in self.classical_theories:
                    theory = self.classical_theories[theory_id]
                    if theory.book_name == book:
                        return theory_id
        
        # 如果没有找到，返回第一个
        return theories[0] if theories else None
    
    def _resolve_by_weighted_average(self, theories: List[str], rule: Dict) -> Dict:
        """基于加权平均解决冲突"""
        weights = rule.get("weights", {})
        result = {}
        
        for theory_id in theories:
            if theory_id in self.classical_theories:
                theory = self.classical_theories[theory_id]
                category = theory.category
                weight = weights.get(category, 0.1)
                result[theory_id] = {
                    "theory": theory,
                    "weight": weight,
                    "confidence": theory.confidence * weight
                }
        
        return result
    
    def _resolve_by_consensus(self, theories: List[str], rule: Dict) -> Optional[str]:
        """基于共识解决冲突"""
        threshold = rule.get("consensus_threshold", 0.6)
        
        # 简化实现：如果大多数理论一致，则采用
        if len(theories) >= 2:
            consensus_count = len(theories) * threshold
            if consensus_count >= 1:
                return theories[0]  # 简化返回第一个
        
        return None
    
    def _resolve_by_context(self, theories: List[str], rule: Dict, context: Dict) -> str:
        """基于上下文解决冲突"""
        context_factors = rule.get("context_factors", [])
        
        # 根据上下文选择最适合的理论
        # 这里需要根据具体的上下文信息进行判断
        # 简化实现
        return theories[0] if theories else None
    
    def _resolve_by_default_priority(self, theories: List[str]) -> str:
        """默认优先级解决"""
        # 按照古籍的综合权威性排序
        book_priority = ["千里命稿", "滴天髓", "渊海子平", "穷通宝鉴", "三命通会", "五行精纪"]
        
        for book in book_priority:
            for theory_id in theories:
                if theory_id in self.classical_theories:
                    theory = self.classical_theories[theory_id]
                    if theory.book_name == book:
                        return theory_id
        
        return theories[0] if theories else None
    
    def get_integrated_analysis(self, analysis_type: str, context: Dict) -> Dict:
        """获取集成分析结果"""
        relevant_theories = self.get_theory_by_category(analysis_type)
        
        if not relevant_theories:
            return {"error": f"未找到{analysis_type}相关理论"}
        
        # 检查是否有冲突
        theory_ids = [theory.theory_id for theory in relevant_theories]
        conflicts = self._detect_conflicts(theory_ids)
        
        if conflicts:
            # 解决冲突
            resolved = self.resolve_theory_conflict(conflicts, context)
            return {
                "analysis_type": analysis_type,
                "theories": relevant_theories,
                "conflicts": conflicts,
                "resolution": resolved,
                "final_result": self._generate_final_result(resolved, context)
            }
        else:
            # 无冲突，直接整合
            return {
                "analysis_type": analysis_type,
                "theories": relevant_theories,
                "conflicts": None,
                "final_result": self._integrate_theories(relevant_theories, context)
            }
    
    def _detect_conflicts(self, theory_ids: List[str]) -> List[str]:
        """检测理论冲突"""
        conflicts = []
        
        for theory_id in theory_ids:
            if theory_id in self.classical_theories:
                theory = self.classical_theories[theory_id]
                for conflict_id in theory.conflicts_with:
                    if conflict_id in theory_ids and conflict_id not in conflicts:
                        conflicts.extend([theory_id, conflict_id])
        
        return list(set(conflicts))
    
    def _generate_final_result(self, resolution: Any, context: Dict) -> str:
        """生成最终结果"""
        if isinstance(resolution, str) and resolution in self.classical_theories:
            theory = self.classical_theories[resolution]
            return f"采用《{theory.book_name}》的{theory.title}：{theory.content}"
        elif isinstance(resolution, dict):
            # 加权平均结果
            results = []
            for theory_id, info in resolution.items():
                theory = info["theory"]
                weight = info["weight"]
                results.append(f"《{theory.book_name}》({weight:.1%}): {theory.title}")
            return "综合分析：" + "；".join(results)
        else:
            return "理论整合中，请稍候..."
    
    def _integrate_theories(self, theories: List[ClassicalTheory], context: Dict) -> str:
        """整合无冲突理论"""
        if not theories:
            return "暂无相关理论"
        
        # 按优先级排序
        sorted_theories = sorted(theories, key=lambda t: t.priority.value)
        
        results = []
        for theory in sorted_theories:
            results.append(f"《{theory.book_name}》{theory.title}：{theory.content}")
        
        return "；".join(results)


# 测试和使用示例
def main():
    """测试古籍理论集成管理器"""
    print("📚 古籍理论集成管理器测试")
    print("=" * 80)
    
    # 创建管理器
    manager = ClassicalTheoryManager()
    
    # 测试理论查询
    print(f"\n🔍 调候理论查询:")
    tiaohou_theories = manager.get_theory_by_category("调候理论")
    for theory in tiaohou_theories[:3]:
        print(f"   《{theory.book_name}》{theory.title}")
    
    # 测试冲突解决
    print(f"\n⚖️ 冲突解决测试:")
    context = {"日干": "甲", "月令": "寅", "季节": "春"}
    result = manager.get_integrated_analysis("调候理论", context)
    print(f"   分析类型: {result.get('analysis_type')}")
    print(f"   理论数量: {len(result.get('theories', []))}")
    print(f"   最终结果: {result.get('final_result', '')[:100]}...")
    
    print(f"\n✅ 古籍理论集成管理器测试完成")


if __name__ == "__main__":
    main()
