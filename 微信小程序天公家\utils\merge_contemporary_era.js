/**
 * 当代人物数据合并工具
 * 将3个当代数据文件合并为完整的当代人物数据库
 */

const contemporaryEraPart1 = require('../data/contemporary_era_part1');
const contemporaryEraPart2 = require('../data/contemporary_era_part2');
const contemporaryEraPart3 = require('../data/contemporary_era_part3');

function mergeContemporaryEraData() {
  console.log('开始合并当代人物数据...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...contemporaryEraPart1.celebrities,
    ...contemporaryEraPart2.celebrities,
    ...contemporaryEraPart3.celebrities
  ];

  // 统计类别分布
  const categoryStats = {
    "开国领袖": contemporaryEraPart1.celebrities.length,
    "科学家": contemporaryEraPart2.celebrities.length,
    "文化艺术": 3, // 前3位是文化艺术
    "体育英雄": 3  // 后3位是体育英雄
  };

  // 统计职业分布
  const occupationStats = {};
  allCelebrities.forEach(celebrity => {
    celebrity.basicInfo.occupation.forEach(occupation => {
      occupationStats[occupation] = (occupationStats[occupation] || 0) + 1;
    });
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建合并后的数据结构
  const mergedData = {
    metadata: {
      title: "当代人物数据库",
      description: "第四批次当代人物数据，包含1893-2021年重要历史人物",
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      timeRange: "1893-2021AD",
      dataSource: "《毛泽东选集》《周恩来选集》《钱学森传》《袁隆平传》《梅兰芳传》等",
      verificationStandard: "专家交叉校验+历史文献双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      categoryDistribution: categoryStats,
      occupationDistribution: occupationStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按类别分类的统计信息
    categoryAnalysis: {
      开国领袖: {
        timeRange: "1893-1976",
        count: categoryStats["开国领袖"],
        representatives: ["毛泽东", "周恩来", "朱德", "刘少奇", "邓小平"],
        characteristics: "建国伟业、革命领袖、政治智慧、改革开放"
      },
      科学家: {
        timeRange: "1889-2021",
        count: categoryStats["科学家"],
        representatives: ["钱学森", "袁隆平", "钱三强", "邓稼先", "李四光", "华罗庚", "屠呦呦", "吴文俊"],
        characteristics: "科技创新、两弹一星、农业突破、医学贡献"
      },
      文化艺术: {
        timeRange: "1894-2005",
        count: categoryStats["文化艺术"],
        representatives: ["梅兰芳", "老舍", "巴金"],
        characteristics: "艺术传承、文学创作、人民情怀、文化传播"
      },
      体育英雄: {
        timeRange: "1937-1984",
        count: categoryStats["体育英雄"],
        representatives: ["容国团", "许海峰", "郎平"],
        characteristics: "体育突破、奥运首金、为国争光、女排精神"
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 当代人物数据合并完成 ===');
  console.log(`总计名人数量: ${mergedData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${mergedData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${mergedData.metadata.qualityGrade}`);
  console.log('\n类别分布:');
  Object.entries(categoryStats).forEach(([category, count]) => {
    console.log(`  ${category}: ${count}位`);
  });
  console.log('\n主要职业分布:');
  Object.entries(occupationStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([occupation, count]) => {
      console.log(`  ${occupation}: ${count}位`);
    });

  return mergedData;
}

// 数据质量检查
function validateContemporaryEraData(data) {
  console.log('\n开始数据质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查验证分数范围
  celebrities.forEach((celebrity, index) => {
    const score = celebrity.verification?.algorithmMatch;
    if (score && (score < 0 || score > 1)) {
      issues.push(`验证分数超出范围: ${celebrity.basicInfo?.name || index} (${score})`);
    }
  });

  // 检查时间范围
  celebrities.forEach((celebrity, index) => {
    const birthYear = celebrity.basicInfo?.birthYear;
    const deathYear = celebrity.basicInfo?.deathYear;
    if (birthYear && (birthYear < 1850 || birthYear > 2000)) {
      issues.push(`出生年份超出当代范围: ${celebrity.basicInfo?.name || index} (${birthYear})`);
    }
    if (deathYear && birthYear && deathYear <= birthYear) {
      issues.push(`死亡年份早于出生年份: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查古籍依据格式
  celebrities.forEach((celebrity, index) => {
    const evidence = celebrity.verification?.ancientTextEvidence;
    if (!evidence || !Array.isArray(evidence) || evidence.length === 0) {
      issues.push(`缺少古籍依据: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`数据质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const mergedData = mergeContemporaryEraData();
    const validation = validateContemporaryEraData(mergedData);
    
    if (validation.isValid) {
      console.log('\n✅ 数据质量检查通过！');
      
      // 保存合并后的数据
      const fs = require('fs');
      const outputPath = 'data/contemporary_era_complete.js';
      const fileContent = `/**
 * 当代人物完整数据库
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 3个当代数据文件合并
 */

const contemporaryEraComplete = ${JSON.stringify(mergedData, null, 2)};

module.exports = contemporaryEraComplete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 合并数据已保存到: ${outputPath}`);
    } else {
      console.log('\n❌ 数据质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeContemporaryEraData,
  validateContemporaryEraData
};
