/**
 * 简单的 WXML 测试
 * 专门检查微信开发者工具报告的错误
 */

const fs = require('fs');
const path = require('path');

function checkSpecificArea() {
  console.log('🔍 检查 WXML 文件第2660行附近的问题');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 检查第2660行附近
  console.log('\n📍 第2655-2664行内容:');
  for (let i = 2654; i < Math.min(2664, lines.length); i++) {
    const lineNum = i + 1;
    const line = lines[i];
    console.log(`${lineNum}: ${line}`);
  }
  
  // 检查六亲分析页面的开始和结束
  console.log('\n📍 六亲分析页面标签匹配:');
  
  let liuqinStart = -1;
  let liuqinEnd = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('wx:elif') && line.includes('liuqin')) {
      liuqinStart = i + 1;
      console.log(`✅ 六亲分析页面开始: 第${liuqinStart}行`);
      console.log(`   ${line.trim()}`);
    }
  }
  
  // 从六亲分析开始位置往后找对应的结束标签
  if (liuqinStart > 0) {
    let viewCount = 0;
    let foundEnd = false;
    
    for (let i = liuqinStart - 1; i < lines.length; i++) {
      const line = lines[i];
      
      // 计算view标签
      const openViews = (line.match(/<view[^>]*>/g) || []).length;
      const closeViews = (line.match(/<\/view>/g) || []).length;
      
      viewCount += openViews - closeViews;
      
      if (i >= liuqinStart && viewCount === 0 && closeViews > 0) {
        liuqinEnd = i + 1;
        console.log(`✅ 六亲分析页面结束: 第${liuqinEnd}行`);
        console.log(`   ${line.trim()}`);
        foundEnd = true;
        break;
      }
    }
    
    if (!foundEnd) {
      console.log('❌ 未找到六亲分析页面的结束标签');
    }
  }
  
  // 检查scroll-view标签匹配
  console.log('\n📍 scroll-view 标签匹配:');
  
  let scrollStart = -1;
  let scrollEnd = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('<scroll-view')) {
      scrollStart = i + 1;
      console.log(`✅ scroll-view 开始: 第${scrollStart}行`);
    }
    if (line.includes('</scroll-view>')) {
      scrollEnd = i + 1;
      console.log(`✅ scroll-view 结束: 第${scrollEnd}行`);
    }
  }
  
  console.log('\n📊 检查结果:');
  console.log(`六亲分析页面: 第${liuqinStart}行 - 第${liuqinEnd}行`);
  console.log(`scroll-view: 第${scrollStart}行 - 第${scrollEnd}行`);
  
  if (liuqinEnd && scrollEnd && liuqinEnd > scrollEnd) {
    console.log('❌ 问题发现: 六亲分析页面的结束标签在scroll-view结束标签之后！');
    return false;
  } else {
    console.log('✅ 标签顺序看起来正确');
    return true;
  }
}

// 运行检查
if (require.main === module) {
  checkSpecificArea();
}

module.exports = { checkSpecificArea };
