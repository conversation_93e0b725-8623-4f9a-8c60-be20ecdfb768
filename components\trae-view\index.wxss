/* components/trae-view/index.wxss */
.trae-view {
  width: 100%;
  padding: 20rpx;
  position: relative;
}

/* 增强选项点击的视觉反馈 */
.option {
  position: relative;
  transition: all 0.3s ease;
}

.option.selected {
  background-color: rgba(108, 92, 231, 0.1);
  border-color: #6C5CE7;
  transform: scale(1.02);
}

.option-feedback {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #6C5CE7;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

.option-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.option-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-weight: bold;
}

.option.selected .option-indicator {
  background-color: #6C5CE7;
  color: white;
}

.question-container {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.question-number {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 20rpx;
}

.question-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 会话问题样式 */
.conversational-question {
  font-size: 38rpx; 
  color: #1a73e8;
  background-color: #f8f9fa;
  padding: 20rpx 24rpx;
  border-radius: 10rpx;
  margin-bottom: 50rpx;
  border-left: 8rpx solid #1a73e8;
  line-height: 1.6;
  font-weight: 400;
}

.speed-answer-badge {
  display: inline-flex;
  align-items: center;
  background-color: #fff4e5;
  color: #ff8f00;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

.speed-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.options-list {
  margin-top: 30rpx;
}

.option {
  background-color: #f7f7f7;
  border-radius: 10rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  position: relative;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.option-indicator {
  width: 40rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  color: #555;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.option-text {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.option.selected {
  background-color: #e3f2fd;
  border: 2rpx solid #1a73e8;
}

.option.selected .option-indicator {
  background-color: #1a73e8;
  color: white;
}

.option-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

.checkmark {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #1a73e8;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.checkmark:after {
  content: '';
  width: 16rpx;
  height: 10rpx;
  border-left: 3rpx solid white;
  border-bottom: 3rpx solid white;
  transform: rotate(-45deg);
  position: absolute;
}