const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer.js');

// 创建分析器实例
const analyzer = new EnhancedPatternAnalyzer();

// 测试问题案例：甲寅 甲寅 己酉 甲寅
const testCase = {
  bazi: '甲寅 甲寅 己酉 甲寅',
  birth_info: {
    year: 1995, month: 12, day: 8, hour: 20,
    location: { province: '上海', city: '上海' }
  }
};

console.log('🔍 测试问题案例:', testCase.bazi);
console.log('='.repeat(60));

// 1. 解析八字
const fourPillars = testCase.bazi.split(' ').map(pillar => ({
  gan: pillar[0],
  zhi: pillar[1]
}));

// 2. 构建分析数据
const baziData = {
  year: { gan: fourPillars[0].gan, zhi: fourPillars[0].zhi },
  month: { gan: fourPillars[1].gan, zhi: fourPillars[1].zhi },
  day: { gan: fourPillars[2].gan, zhi: fourPillars[2].zhi },
  hour: { gan: fourPillars[3].gan, zhi: fourPillars[3].zhi },
  birth_info: testCase.birth_info
};

// 3. 计算出生日期
const birthDate = new Date(
  testCase.birth_info.year,
  testCase.birth_info.month - 1,
  testCase.birth_info.day,
  testCase.birth_info.hour
);

console.log('📅 四柱:', fourPillars);
console.log('📊 八字数据:', baziData);
console.log('🕐 出生时间:', birthDate);

// 4. 执行格局分析（使用与边界测试相同的方法）
console.log('\n🎯 开始格局分析（边界测试方式）');
const patternResult = analyzer.determinePattern(baziData, fourPillars, birthDate);

console.log('\n📋 格局分析结果:');
console.log('格局:', patternResult.pattern);
console.log('格局类型:', patternResult.pattern_type);
console.log('清浊评分:', patternResult.clarity_score);

// 5. 检查特殊格局判定逻辑
console.log('\n🔍 检查特殊格局判定逻辑:');

// 从patternResult中提取五行力量
const elementPowers = patternResult.element_powers || {
  powers: { '木': 6.25, '火': 2, '土': 0, '金': 0, '水': 0 },
  percentages: { '木': 75.76, '火': 24.24, '土': 0, '金': 0, '水': 0 },
  total: 8.25
};

console.log('五行力量分布:', elementPowers);

// 检查是否满足特殊格局条件
console.log('\n🔍 特殊格局条件检查:');
const maxPercentage = Math.max(...Object.values(elementPowers.percentages));
console.log('最强五行占比:', maxPercentage.toFixed(1) + '%');
console.log('专旺格阈值: >70%');
console.log('是否满足专旺格条件:', maxPercentage > 70);

// 检查日主力量
const dayGan = fourPillars[2].gan;
const dayElement = analyzer.wuxingMap[dayGan];
const dayPower = elementPowers.percentages[dayElement] || 0;
console.log('\n日主:', dayGan + '(' + dayElement + ')');
console.log('日主力量占比:', dayPower.toFixed(1) + '%');

// 手动调用isSpecialPattern
console.log('\n🔍 手动调用isSpecialPattern:');
try {
  const isSpecial = analyzer.isSpecialPattern(elementPowers, fourPillars);
  console.log('isSpecialPattern结果:', isSpecial);

  if (isSpecial) {
    const specialResult = analyzer.classifySpecialPattern(elementPowers, fourPillars);
    console.log('特殊格局分类结果:', specialResult);
  }
} catch (error) {
  console.log('调用isSpecialPattern出错:', error.message);
}

console.log('\n🎉 测试完成');
