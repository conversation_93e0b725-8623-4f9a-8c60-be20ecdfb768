/**
 * WXML 文件验证测试
 * 验证 WXML 文件的标签匹配和语法正确性
 */

const fs = require('fs');
const path = require('path');

class WXMLValidator {
  constructor() {
    this.errors = [];
  }

  /**
   * 验证 WXML 文件
   */
  validateWXMLFile(filePath) {
    console.log(`🔍 验证 WXML 文件: ${filePath}`);
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      this.validateTagMatching(content, filePath);
      this.validateSyntax(content, filePath);
      
      if (this.errors.length === 0) {
        console.log('✅ WXML 文件验证通过');
        return true;
      } else {
        console.log('❌ WXML 文件验证失败:');
        this.errors.forEach(error => console.log(`  - ${error}`));
        return false;
      }
    } catch (error) {
      console.error('❌ 读取文件失败:', error.message);
      return false;
    }
  }

  /**
   * 验证标签匹配
   */
  validateTagMatching(content, filePath) {
    const lines = content.split('\n');
    const stack = [];
    const tagRegex = /<(\/?)([\w-]+)(?:\s[^>]*)?>/g;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      let match;
      
      while ((match = tagRegex.exec(line)) !== null) {
        const isClosing = match[1] === '/';
        const tagName = match[2];
        const lineNumber = i + 1;
        
        // 跳过自闭合标签
        if (line.includes('/>') && !isClosing) {
          continue;
        }
        
        if (isClosing) {
          // 闭合标签
          if (stack.length === 0) {
            this.errors.push(`第${lineNumber}行: 多余的闭合标签 </${tagName}>`);
          } else {
            const lastTag = stack.pop();
            if (lastTag.name !== tagName) {
              this.errors.push(`第${lineNumber}行: 标签不匹配，期望 </${lastTag.name}>，实际 </${tagName}>`);
            }
          }
        } else {
          // 开始标签
          stack.push({
            name: tagName,
            line: lineNumber
          });
        }
      }
    }
    
    // 检查未闭合的标签
    if (stack.length > 0) {
      stack.forEach(tag => {
        this.errors.push(`第${tag.line}行: 未闭合的标签 <${tag.name}>`);
      });
    }
  }

  /**
   * 验证语法
   */
  validateSyntax(content, filePath) {
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;
      
      // 检查常见语法错误
      if (line.includes('{{') && !line.includes('}}')) {
        this.errors.push(`第${lineNumber}行: 未闭合的插值表达式 {{}}`);
      }
      
      if (line.includes('wx:if') && !line.includes('{{')) {
        this.errors.push(`第${lineNumber}行: wx:if 条件缺少插值表达式`);
      }
    }
  }
}

/**
 * 运行验证测试
 */
function runValidationTest() {
  console.log('🚀 开始 WXML 文件验证测试\n');
  
  const validator = new WXMLValidator();
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  
  const isValid = validator.validateWXMLFile(wxmlPath);
  
  console.log('\n📊 验证结果:');
  console.log(`✅ 文件有效性: ${isValid ? '通过' : '失败'}`);
  
  if (isValid) {
    console.log('🎉 WXML 文件结构正确，可以正常编译！');
  } else {
    console.log('⚠️ WXML 文件存在问题，需要修复！');
  }
  
  return isValid;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runValidationTest();
}

module.exports = {
  WXMLValidator,
  runValidationTest
};
