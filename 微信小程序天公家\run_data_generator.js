/**
 * 运行名人数据生成器
 */

const CelebrityDataGenerator = require('./utils/celebrity_data_generator.js');

async function runGenerator() {
  try {
    console.log('🚀 启动历史名人数据生成器');
    console.log('============================================================');
    
    const generator = new CelebrityDataGenerator();
    
    // 生成15位名人数据（基于真实史书记录）
    const generatedData = generator.generateBatchData(15);
    
    // 保存数据
    const filePath = generator.saveGeneratedData(generatedData, 'batch_generated_celebrities.js');
    
    console.log('\n📊 生成统计:');
    console.log(`   - 总生成数: ${generatedData.length} 位名人`);
    console.log(`   - 朝代覆盖: ${[...new Set(generatedData.map(c => c.basicInfo.dynasty))].length} 个朝代`);
    console.log(`   - 格局覆盖: ${[...new Set(generatedData.map(c => c.pattern.mainPattern))].length} 种格局`);
    console.log(`   - 平均验证度: ${(generatedData.reduce((sum, c) => sum + c.verification.algorithmMatch, 0) / generatedData.length).toFixed(3)}`);
    
    console.log('\n✅ 数据生成完成!');
    
  } catch (error) {
    console.error('❌ 生成失败:', error);
  }
}

// 运行生成器
runGenerator();
