// utils/layered_rules_manager.js
// 分层规则管理器 - 管理多层次的规则系统

class LayeredRulesManager {
  constructor() {
    this.layers = new Map();
    this.ruleCache = new Map();
    this.initialized = false;
    this.defaultLayer = 'base';
  }

  // 初始化管理器
  init(config = {}) {
    try {
      this.defaultLayer = config.defaultLayer || 'base';
      this.initialized = true;
      
      // 初始化基础层
      this.addLayer('base', { priority: 0, description: '基础规则层' });
      this.addLayer('professional', { priority: 10, description: '专业规则层' });
      this.addLayer('advanced', { priority: 20, description: '高级规则层' });
      
      console.log('✅ LayeredRulesManager 初始化成功');
      return true;
    } catch (error) {
      console.error('❌ LayeredRulesManager 初始化失败:', error);
      return false;
    }
  }

  // 添加规则层
  addLayer(name, config = {}) {
    if (!this.initialized) {
      console.warn('⚠️ LayeredRulesManager 未初始化');
      return false;
    }

    try {
      this.layers.set(name, {
        name: name,
        priority: config.priority || 0,
        description: config.description || '',
        rules: new Map(),
        enabled: config.enabled !== false,
        metadata: config.metadata || {}
      });
      
      console.log(`✅ 规则层 ${name} 添加成功`);
      return true;
    } catch (error) {
      console.error(`❌ 规则层 ${name} 添加失败:`, error);
      return false;
    }
  }

  // 添加规则到指定层
  addRule(layerName, ruleId, rule) {
    const layer = this.layers.get(layerName);
    if (!layer) {
      console.warn(`⚠️ 未找到规则层: ${layerName}`);
      return false;
    }

    try {
      const ruleData = {
        id: ruleId,
        rule: rule,
        layer: layerName,
        priority: rule.priority || 0,
        enabled: rule.enabled !== false,
        metadata: rule.metadata || {},
        createdAt: Date.now()
      };

      layer.rules.set(ruleId, ruleData);
      
      // 清除缓存
      this.clearCache();
      
      console.log(`✅ 规则 ${ruleId} 添加到层 ${layerName} 成功`);
      return true;
    } catch (error) {
      console.error(`❌ 规则 ${ruleId} 添加失败:`, error);
      return false;
    }
  }

  // 获取规则
  getRule(ruleId, layerName = null) {
    const cacheKey = `${ruleId}_${layerName || 'all'}`;
    
    // 检查缓存
    if (this.ruleCache.has(cacheKey)) {
      return this.ruleCache.get(cacheKey);
    }

    let result = null;

    if (layerName) {
      // 从指定层获取
      const layer = this.layers.get(layerName);
      if (layer && layer.enabled) {
        result = layer.rules.get(ruleId);
      }
    } else {
      // 从所有层按优先级获取
      const sortedLayers = Array.from(this.layers.values())
        .filter(layer => layer.enabled)
        .sort((a, b) => b.priority - a.priority);

      for (const layer of sortedLayers) {
        if (layer.rules.has(ruleId)) {
          result = layer.rules.get(ruleId);
          break;
        }
      }
    }

    // 缓存结果
    if (result) {
      this.ruleCache.set(cacheKey, result);
    }

    return result;
  }

  // 获取层中的所有规则
  getLayerRules(layerName) {
    const layer = this.layers.get(layerName);
    if (!layer || !layer.enabled) {
      return [];
    }

    return Array.from(layer.rules.values())
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority);
  }

  // 获取所有适用的规则
  getAllRules(filter = {}) {
    const allRules = [];
    
    const sortedLayers = Array.from(this.layers.values())
      .filter(layer => layer.enabled)
      .sort((a, b) => b.priority - a.priority);

    for (const layer of sortedLayers) {
      const layerRules = Array.from(layer.rules.values())
        .filter(rule => rule.enabled)
        .filter(rule => this.matchesFilter(rule, filter));
      
      allRules.push(...layerRules);
    }

    return allRules.sort((a, b) => b.priority - a.priority);
  }

  // 匹配过滤器
  matchesFilter(rule, filter) {
    if (!filter || Object.keys(filter).length === 0) {
      return true;
    }

    for (const [key, value] of Object.entries(filter)) {
      if (rule.metadata[key] !== value) {
        return false;
      }
    }

    return true;
  }

  // 启用/禁用层
  setLayerEnabled(layerName, enabled) {
    const layer = this.layers.get(layerName);
    if (!layer) {
      console.warn(`⚠️ 未找到规则层: ${layerName}`);
      return false;
    }

    layer.enabled = enabled;
    this.clearCache();
    
    console.log(`✅ 规则层 ${layerName} ${enabled ? '启用' : '禁用'}成功`);
    return true;
  }

  // 清除缓存
  clearCache() {
    this.ruleCache.clear();
  }

  // 获取状态
  getStatus() {
    const layerStats = {};
    for (const [name, layer] of this.layers) {
      layerStats[name] = {
        enabled: layer.enabled,
        ruleCount: layer.rules.size,
        priority: layer.priority
      };
    }

    return {
      initialized: this.initialized,
      layerCount: this.layers.size,
      cacheSize: this.ruleCache.size,
      layers: layerStats
    };
  }

  // 清理管理器
  cleanup() {
    this.layers.clear();
    this.ruleCache.clear();
    this.initialized = false;
    console.log('✅ LayeredRulesManager 清理完成');
  }
}

// 创建单例实例
const layeredRulesManager = new LayeredRulesManager();

module.exports = {
  LayeredRulesManager,
  default: layeredRulesManager,
  manager: layeredRulesManager
};
