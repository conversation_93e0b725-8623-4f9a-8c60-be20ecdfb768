/**
 * 功能扩展测试脚本
 * 验证新增的特殊格局、社会环境分析、精确应期分析等功能
 */

// 引入测试模块
const EnhancedPatternAnalyzer = require('./utils/enhanced_pattern_analyzer.js');
const AdvancedSocialAnalyzer = require('./utils/advanced_social_analyzer.js');
const PreciseTimingAnalyzer = require('./utils/precise_timing_analyzer.js');
const EnhancedAdviceGenerator = require('./utils/enhanced_advice_generator.js');

class FeatureExpansionTester {
  constructor() {
    this.initializeTestModules();
    this.testResults = {
      special_patterns: [],
      social_analysis: [],
      timing_analysis: [],
      personality_depth: [],
      overall_score: 0
    };
  }

  /**
   * 初始化测试模块
   */
  initializeTestModules() {
    console.log('🧪 初始化功能扩展测试模块...');
    
    this.patternAnalyzer = new EnhancedPatternAnalyzer();
    this.socialAnalyzer = new AdvancedSocialAnalyzer();
    this.timingAnalyzer = new PreciseTimingAnalyzer();
    this.adviceGenerator = new EnhancedAdviceGenerator();
    
    console.log('✅ 测试模块初始化完成');
  }

  /**
   * 执行完整的功能扩展测试
   */
  async executeComprehensiveTest() {
    console.log('🚀 开始功能扩展综合测试...');
    console.log('=' .repeat(60));

    try {
      // 1. 特殊格局识别测试
      await this.testSpecialPatternRecognition();
      
      // 2. 高级社会环境分析测试
      await this.testAdvancedSocialAnalysis();
      
      // 3. 精确应期分析测试
      await this.testPreciseTimingAnalysis();
      
      // 4. 个性化深度分析测试
      await this.testPersonalityDepthAnalysis();
      
      // 5. 生成测试报告
      this.generateTestReport();

    } catch (error) {
      console.error('❌ 功能扩展测试失败:', error);
      return false;
    }

    return true;
  }

  /**
   * 测试特殊格局识别功能
   */
  async testSpecialPatternRecognition() {
    console.log('\n📊 测试1: 特殊格局识别功能');
    console.log('-' .repeat(40));

    const testCases = [
      {
        name: '从财格测试',
        bazi: {
          year: { heavenly: '戊', earthly: '辰' },
          month: { heavenly: '戊', earthly: '辰' },
          day: { heavenly: '乙', earthly: '未' },
          hour: { heavenly: '戊', earthly: '辰' }
        },
        expected_pattern: '从财格'
      },
      {
        name: '从官格测试',
        bazi: {
          year: { heavenly: '庚', earthly: '申' },
          month: { heavenly: '庚', earthly: '申' },
          day: { heavenly: '甲', earthly: '酉' },
          hour: { heavenly: '辛', earthly: '酉' }
        },
        expected_pattern: '从官格'
      },
      {
        name: '从儿格测试',
        bazi: {
          year: { heavenly: '丁', earthly: '巳' },
          month: { heavenly: '丁', earthly: '巳' },
          day: { heavenly: '甲', earthly: '巳' },
          hour: { heavenly: '丁', earthly: '巳' }
        },
        expected_pattern: '从儿格'
      }
    ];

    for (const testCase of testCases) {
      try {
        console.log(`\n🔍 测试案例: ${testCase.name}`);
        
        // 转换数据格式（从 heavenly/earthly 到 gan/zhi）
        const convertedBazi = this.convertBaziFormat(testCase.bazi);

        // 计算五行分布
        const wuxingDistribution = this.calculateTestWuxingDistribution(convertedBazi);
        const elementPowers = this.calculateTestElementPowers(convertedBazi);

        // 执行特殊格局分析
        const followPatterns = this.patternAnalyzer.analyzeFollowPatterns(
          convertedBazi,
          wuxingDistribution,
          elementPowers
        );

        // 验证结果
        const testResult = {
          test_case: testCase.name,
          expected: testCase.expected_pattern,
          detected_patterns: followPatterns.map(p => p.type),
          success: followPatterns.some(p => p.type === testCase.expected_pattern),
          details: followPatterns
        };

        this.testResults.special_patterns.push(testResult);

        if (testResult.success) {
          console.log(`✅ ${testCase.name} - 成功识别 ${testCase.expected_pattern}`);
        } else {
          console.log(`❌ ${testCase.name} - 未能识别 ${testCase.expected_pattern}`);
          console.log(`   检测到的格局: ${testResult.detected_patterns.join(', ')}`);
        }

      } catch (error) {
        console.error(`❌ ${testCase.name} 测试失败:`, error.message);
        this.testResults.special_patterns.push({
          test_case: testCase.name,
          expected: testCase.expected_pattern,
          success: false,
          error: error.message
        });
      }
    }

    const successCount = this.testResults.special_patterns.filter(r => r.success).length;
    const successRate = (successCount / testCases.length * 100).toFixed(1);
    console.log(`\n📈 特殊格局识别测试结果: ${successCount}/${testCases.length} (${successRate}%)`);
  }

  /**
   * 测试高级社会环境分析功能
   */
  async testAdvancedSocialAnalysis() {
    console.log('\n🌍 测试2: 高级社会环境分析功能');
    console.log('-' .repeat(40));

    const testPersonalInfo = {
      age: 30,
      gender: '男',
      industry: '科技',
      education: 'master',
      location: '一线城市',
      family_economic_status: 'middle',
      social_network_size: 'medium'
    };

    try {
      console.log('🔍 执行社会环境综合分析...');
      
      const socialAnalysisResult = this.socialAnalyzer.analyzeComprehensiveSocialEnvironment(
        testPersonalInfo,
        { include_macro: true, include_micro: true }
      );

      // 验证分析结果
      const testResult = {
        test_case: '社会环境综合分析',
        success: this.validateSocialAnalysisResult(socialAnalysisResult),
        macro_environment: socialAnalysisResult.macro_environment,
        micro_environment: socialAnalysisResult.micro_environment,
        development_stage: socialAnalysisResult.development_stage,
        confidence: socialAnalysisResult.confidence
      };

      this.testResults.social_analysis.push(testResult);

      if (testResult.success) {
        console.log('✅ 社会环境分析 - 成功完成');
        console.log(`   宏观环境评分: ${(testResult.macro_environment.overall_macro_score * 100).toFixed(1)}%`);
        console.log(`   微观环境评分: ${(testResult.micro_environment.overall_micro_score * 100).toFixed(1)}%`);
        console.log(`   分析置信度: ${(testResult.confidence * 100).toFixed(1)}%`);
      } else {
        console.log('❌ 社会环境分析 - 分析结果不完整');
      }

    } catch (error) {
      console.error('❌ 社会环境分析测试失败:', error.message);
      this.testResults.social_analysis.push({
        test_case: '社会环境综合分析',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试精确应期分析功能
   */
  async testPreciseTimingAnalysis() {
    console.log('\n⏰ 测试3: 精确应期分析功能');
    console.log('-' .repeat(40));

    const testBazi = {
      year: { gan: '甲', zhi: '寅' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '戌' },
      hour: { gan: '壬', zhi: '子' }
    };

    const testYongshen = {
      yongshen: '水',
      xishen: '金',
      jishen: '火',
      priority_system: {
        primary: { type: '调候用神', element: '水', priority: 1 },
        secondary: { type: '格局用神', element: '金', priority: 2 },
        tertiary: { type: '五行制衡', element: '木', priority: 3 }
      }
    };

    const testPersonalInfo = {
      age: 28,
      gender: '女',
      birth_year: 1995
    };

    try {
      console.log('🔍 执行精确应期分析...');
      
      const timingAnalysisResult = this.timingAnalyzer.analyzePreciseTiming(
        testBazi,
        testYongshen,
        testPersonalInfo,
        { forecast_years: 5 }
      );

      // 验证分析结果
      const testResult = {
        test_case: '精确应期分析',
        success: this.validateTimingAnalysisResult(timingAnalysisResult),
        timing_factors: timingAnalysisResult.timing_factors,
        event_timings: timingAnalysisResult.event_timings,
        precision_assessment: timingAnalysisResult.precision_assessment,
        confidence: timingAnalysisResult.confidence
      };

      this.testResults.timing_analysis.push(testResult);

      if (testResult.success) {
        console.log('✅ 精确应期分析 - 成功完成');
        console.log(`   综合评分: ${(testResult.timing_factors.综合评分 * 100).toFixed(1)}%`);
        console.log(`   精度等级: ${testResult.precision_assessment.precision_level}`);
        console.log(`   分析置信度: ${(testResult.confidence * 100).toFixed(1)}%`);
        
        // 显示事件应期信息
        Object.keys(testResult.event_timings).forEach(eventType => {
          const timing = testResult.event_timings[eventType];
          console.log(`   ${eventType}: 成功概率 ${(timing.success_probability * 100).toFixed(1)}%`);
        });
      } else {
        console.log('❌ 精确应期分析 - 分析结果不完整');
      }

    } catch (error) {
      console.error('❌ 精确应期分析测试失败:', error.message);
      this.testResults.timing_analysis.push({
        test_case: '精确应期分析',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试个性化深度分析功能
   */
  async testPersonalityDepthAnalysis() {
    console.log('\n🧠 测试4: 个性化深度分析功能');
    console.log('-' .repeat(40));

    const testBazi = {
      year: { gan: '甲', zhi: '寅' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '戌' },
      hour: { gan: '壬', zhi: '子' }
    };

    const testPatternResult = {
      primary_pattern: '正财格',
      clarity_score: 0.75,
      pattern_strength: 0.8
    };

    const testPersonalInfo = {
      age: 32,
      gender: '男',
      education: 'bachelor',
      industry: '金融'
    };

    try {
      console.log('🔍 执行个性化深度分析...');
      
      // 性格特征深度分析
      const personalityDepth = this.adviceGenerator.analyzePersonalityDepth(
        testBazi,
        testPatternResult,
        testPersonalInfo
      );

      // 能力倾向分析
      const abilityTendencies = this.adviceGenerator.analyzeAbilityTendencies(
        testBazi,
        testPatternResult,
        testPersonalInfo
      );

      // 验证分析结果
      const testResult = {
        test_case: '个性化深度分析',
        success: this.validatePersonalityAnalysisResult(personalityDepth, abilityTendencies),
        personality_depth: personalityDepth,
        ability_tendencies: abilityTendencies
      };

      this.testResults.personality_depth.push(testResult);

      if (testResult.success) {
        console.log('✅ 个性化深度分析 - 成功完成');
        console.log(`   MBTI类型: ${personalityDepth.mbti_type.type} (${personalityDepth.mbti_type.description})`);
        console.log(`   主导五行: ${personalityDepth.wuxing_personality.dominant_element}`);
        console.log(`   创造力指数: ${(abilityTendencies.creativity_index.creativity_score * 100).toFixed(1)}%`);
        console.log(`   领导力潜质: ${(abilityTendencies.leadership_potential.leadership_score * 100).toFixed(1)}%`);
      } else {
        console.log('❌ 个性化深度分析 - 分析结果不完整');
      }

    } catch (error) {
      console.error('❌ 个性化深度分析测试失败:', error.message);
      this.testResults.personality_depth.push({
        test_case: '个性化深度分析',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log('\n📋 功能扩展测试报告');
    console.log('=' .repeat(60));

    // 计算各模块成功率
    const specialPatternsSuccess = this.testResults.special_patterns.filter(r => r.success).length;
    const specialPatternsTotal = this.testResults.special_patterns.length;
    const specialPatternsRate = specialPatternsTotal > 0 ? (specialPatternsSuccess / specialPatternsTotal * 100).toFixed(1) : 0;

    const socialAnalysisSuccess = this.testResults.social_analysis.filter(r => r.success).length;
    const socialAnalysisTotal = this.testResults.social_analysis.length;
    const socialAnalysisRate = socialAnalysisTotal > 0 ? (socialAnalysisSuccess / socialAnalysisTotal * 100).toFixed(1) : 0;

    const timingAnalysisSuccess = this.testResults.timing_analysis.filter(r => r.success).length;
    const timingAnalysisTotal = this.testResults.timing_analysis.length;
    const timingAnalysisRate = timingAnalysisTotal > 0 ? (timingAnalysisSuccess / timingAnalysisTotal * 100).toFixed(1) : 0;

    const personalityDepthSuccess = this.testResults.personality_depth.filter(r => r.success).length;
    const personalityDepthTotal = this.testResults.personality_depth.length;
    const personalityDepthRate = personalityDepthTotal > 0 ? (personalityDepthSuccess / personalityDepthTotal * 100).toFixed(1) : 0;

    // 计算总体成功率
    const totalSuccess = specialPatternsSuccess + socialAnalysisSuccess + timingAnalysisSuccess + personalityDepthSuccess;
    const totalTests = specialPatternsTotal + socialAnalysisTotal + timingAnalysisTotal + personalityDepthTotal;
    const overallSuccessRate = totalTests > 0 ? (totalSuccess / totalTests * 100).toFixed(1) : 0;

    console.log(`📊 特殊格局识别: ${specialPatternsSuccess}/${specialPatternsTotal} (${specialPatternsRate}%)`);
    console.log(`🌍 社会环境分析: ${socialAnalysisSuccess}/${socialAnalysisTotal} (${socialAnalysisRate}%)`);
    console.log(`⏰ 精确应期分析: ${timingAnalysisSuccess}/${timingAnalysisTotal} (${timingAnalysisRate}%)`);
    console.log(`🧠 个性化深度分析: ${personalityDepthSuccess}/${personalityDepthTotal} (${personalityDepthRate}%)`);
    console.log('-' .repeat(40));
    console.log(`🎯 总体成功率: ${totalSuccess}/${totalTests} (${overallSuccessRate}%)`);

    // 保存总体评分
    this.testResults.overall_score = parseFloat(overallSuccessRate);

    // 生成结论
    if (this.testResults.overall_score >= 80) {
      console.log('\n🎉 功能扩展测试结果: 优秀');
      console.log('   所有新增功能运行正常，达到预期效果');
    } else if (this.testResults.overall_score >= 60) {
      console.log('\n✅ 功能扩展测试结果: 良好');
      console.log('   大部分新增功能运行正常，少数功能需要优化');
    } else {
      console.log('\n⚠️ 功能扩展测试结果: 需要改进');
      console.log('   部分新增功能存在问题，需要进一步调试');
    }

    console.log('\n📝 详细测试数据已保存到 this.testResults');
  }

  // 辅助验证方法
  validateSocialAnalysisResult(result) {
    return result && 
           result.macro_environment && 
           result.micro_environment && 
           result.development_stage && 
           typeof result.confidence === 'number';
  }

  validateTimingAnalysisResult(result) {
    return result && 
           result.timing_factors && 
           result.event_timings && 
           result.precision_assessment && 
           typeof result.confidence === 'number';
  }

  validatePersonalityAnalysisResult(personalityDepth, abilityTendencies) {
    return personalityDepth && 
           personalityDepth.mbti_type && 
           personalityDepth.wuxing_personality && 
           abilityTendencies && 
           abilityTendencies.creativity_index && 
           abilityTendencies.leadership_potential;
  }

  // 数据格式转换方法
  convertBaziFormat(bazi) {
    return {
      year: { gan: bazi.year.heavenly, zhi: bazi.year.earthly },
      month: { gan: bazi.month.heavenly, zhi: bazi.month.earthly },
      day: { gan: bazi.day.heavenly, zhi: bazi.day.earthly },
      hour: { gan: bazi.hour.heavenly, zhi: bazi.hour.earthly }
    };
  }

  // 测试数据生成方法
  calculateTestWuxingDistribution(bazi) {
    // 根据八字生成更真实的五行分布
    const wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };

    const zhiWuxingMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };

    const distribution = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };

    // 统计天干
    [bazi.year, bazi.month, bazi.day, bazi.hour].forEach(pillar => {
      const wuxing = wuxingMap[pillar.gan];
      if (wuxing) distribution[wuxing] += 0.25;
    });

    // 统计地支
    [bazi.year, bazi.month, bazi.day, bazi.hour].forEach(pillar => {
      const wuxing = zhiWuxingMap[pillar.zhi];
      if (wuxing) distribution[wuxing] += 0.2;
    });

    return distribution;
  }

  calculateTestElementPowers(bazi) {
    // 根据八字生成更真实的五行力量
    const wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };

    const zhiWuxingMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };

    const powers = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };
    const dayElement = wuxingMap[bazi.day.gan];

    // 天干力量（权重1.0），但日干不计入自身力量（从格特殊处理）
    [bazi.year, bazi.month, bazi.hour].forEach(pillar => {
      const wuxing = wuxingMap[pillar.gan];
      if (wuxing) powers[wuxing] += 1.0;
    });

    // 地支力量（权重0.8）
    [bazi.year, bazi.month, bazi.day, bazi.hour].forEach(pillar => {
      const wuxing = zhiWuxingMap[pillar.zhi];
      if (wuxing) powers[wuxing] += 0.8;
    });

    // 日干只有在地支有根气时才计算力量
    const dayZhiElement = zhiWuxingMap[bazi.day.zhi];
    if (dayZhiElement === dayElement) {
      powers[dayElement] += 0.5; // 日干有根时的基础力量
    }

    return powers;
  }
}

// 执行测试
async function runFeatureExpansionTest() {
  const tester = new FeatureExpansionTester();
  const success = await tester.executeComprehensiveTest();
  
  if (success) {
    console.log('\n🎊 功能扩展测试完成！');
    return tester.testResults;
  } else {
    console.log('\n💥 功能扩展测试失败！');
    return null;
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { FeatureExpansionTester, runFeatureExpansionTest };
} else if (typeof window !== 'undefined') {
  window.FeatureExpansionTester = FeatureExpansionTester;
  window.runFeatureExpansionTest = runFeatureExpansionTest;
}

// 如果直接运行此脚本
if (require.main === module) {
  runFeatureExpansionTest().then(results => {
    if (results) {
      console.log('\n📊 测试结果摘要:');
      console.log(`总体评分: ${results.overall_score}%`);
    }
  });
}
