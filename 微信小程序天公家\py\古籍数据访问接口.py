#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字命理古籍数据访问接口
自动生成的集成代码
"""

import sqlite3
import json
from typing import Dict, List, Optional

class ClassicalBaziDataAccess:
    """古籍八字数据访问类"""
    
    def __init__(self, db_path: str = "占卜系统/data/bazi_classical_complete.db"):
        self.db_path = db_path
    
    def query_pattern_rules(self, pattern_name: str = None, category: str = None, 
                          book_source: str = None, min_confidence: float = 0.5) -> List[Dict]:
        """查询格局规则"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM classical_rules WHERE confidence >= ?"
        params = [min_confidence]
        
        if pattern_name:
            query += " AND pattern_name LIKE ?"
            params.append(f"%{pattern_name}%")
        
        if category:
            query += " AND category = ?"
            params.append(category)
        
        if book_source:
            query += " AND book_source = ?"
            params.append(book_source)
        
        query += " ORDER BY confidence DESC"
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 转换为字典格式
        columns = [description[0] for description in cursor.description]
        results = []
        for row in rows:
            rule_dict = dict(zip(columns, row))
            # 解析JSON字段
            if rule_dict['conditions']:
                rule_dict['conditions'] = json.loads(rule_dict['conditions'])
            if rule_dict['interpretations']:
                rule_dict['interpretations'] = json.loads(rule_dict['interpretations'])
            results.append(rule_dict)
        
        conn.close()
        return results
    
    def get_statistics(self) -> Dict:
        """获取数据统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM classical_rules")
        total_rules = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(confidence) FROM classical_rules")
        avg_confidence = cursor.fetchone()[0] or 0
        
        # 按书籍统计
        cursor.execute("""
            SELECT book_source, COUNT(*), AVG(confidence) 
            FROM classical_rules 
            GROUP BY book_source
        """)
        book_stats = cursor.fetchall()
        
        # 按类别统计
        cursor.execute("""
            SELECT category, COUNT(*) 
            FROM classical_rules 
            GROUP BY category
        """)
        category_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            "total_rules": total_rules,
            "avg_confidence": round(avg_confidence, 3),
            "book_stats": book_stats,
            "category_stats": category_stats
        }

# 使用示例
if __name__ == "__main__":
    data_access = ClassicalBaziDataAccess()
    
    # 查询正格规则
    zhengge_rules = data_access.query_pattern_rules(category="正格", min_confidence=0.7)
    print(f"正格规则数量: {len(zhengge_rules)}")
    
    # 查询渊海子平的规则
    yuanhai_rules = data_access.query_pattern_rules(book_source="渊海子平", min_confidence=0.6)
    print(f"渊海子平规则数量: {len(yuanhai_rules)}")
    
    # 获取统计信息
    stats = data_access.get_statistics()
    print(f"数据库统计: {stats}")
