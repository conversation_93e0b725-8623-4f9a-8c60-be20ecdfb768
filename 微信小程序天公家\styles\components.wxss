/* 组件样式文件 */

/* 按钮组件 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border-radius: 30rpx;
  padding: 24rpx 48rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 16rpx var(--shadow-color);
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: var(--primary-light);
  opacity: 0;
  transform: translate(-50%, -50%) scale(1);
  transition: opacity 0.3s ease;
}

.btn-primary:hover::after {
  opacity: 0.2;
}

.btn-primary:active::after {
  opacity: 0.4;
  transform: translate(-50%, -50%) scale(0.95);
}

.btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 8rpx rgba(75, 123, 245, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  border-radius: 30rpx;
  padding: 24rpx 48rpx;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  transform: scale(0.95);
}

.btn-fun {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 40rpx;
  padding: 24rpx 48rpx;
  animation: pulse 2s infinite;
}

/* 卡片组件 */
.card {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: var(--spacing-xl);
  box-shadow: 0 8rpx 24rpx var(--shadow-color);
  transition: all 0.3s ease;
  border: 2rpx solid var(--border-light);
}

.card:hover {
  box-shadow: 0 16rpx 32rpx var(--shadow-dark);
  border-color: var(--border-color);
  transform: translateY(-4rpx);
}

.card.interactive {
  cursor: pointer;
}

.card.interactive:active {
  transform: scale(0.98) translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx var(--shadow-light);
}

.card:active {
  transform: scale(0.98);
}

/* 表单组件 */
.form-input {
  height: 88rpx;
  border-radius: 16rpx;
  border: 2rpx solid var(--border-color);
  padding: 0 24rpx;
  transition: all 0.3s ease;
  background-color: var(--background-light);
  color: var(--text-primary);
}

.form-input::placeholder {
  color: var(--text-disabled);
}

.form-input:hover {
  border-color: var(--primary-light);
  background-color: var(--background);
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: var(--background);
  box-shadow: 0 0 0 4rpx var(--primary-light);
  outline: none;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3rpx rgba(75, 123, 245, 0.1);
}

.form-textarea {
  height: 240rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  background-color: #F8FAFC;
  border: 2rpx solid var(--border-color);
}

.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3rpx rgba(75, 123, 245, 0.1);
}

/* 列表项组件 */
.list-item {
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.list-item:active {
  background-color: #F5F9FF;
}

/* 徽章组件 */
.badge {
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: var(--font-xs);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  box-shadow: 0 4rpx 8rpx rgba(75, 123, 245, 0.2);
  color: white;
}

/* 进度条组件 */
.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: var(--border-color);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-bar__inner {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 16rpx;
  font-size: var(--font-xs);
  background-color: #F5F9FF;
  color: var(--primary-color);
}

/* 图标按钮 */
.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: var(--background);
  transition: all 0.3s ease;
}

.icon-btn:active {
  transform: scale(0.9);
  background-color: #F5F9FF;
}