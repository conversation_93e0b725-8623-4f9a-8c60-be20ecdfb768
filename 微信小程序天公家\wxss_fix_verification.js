// wxss_fix_verification.js
// 验证WXSS编译错误修复

console.log('🔧 WXSS编译错误修复验证');
console.log('=' .repeat(50));

// 1. 检查WXSS文件中的CSS类名
console.log('📋 1. 检查WXSS文件中的CSS类名');
console.log('-'.repeat(30));

const fs = require('fs');
const path = require('path');

try {
  const wxssPath = path.join(__dirname, 'pages/bazi-result/index.wxss');
  const wxssContent = fs.readFileSync(wxssPath, 'utf8');
  
  // 检查是否还有中文类名
  const chineseClassRegex = /\.level-[^\s{]*[\u4e00-\u9fa5]/g;
  const chineseMatches = wxssContent.match(chineseClassRegex);
  
  if (chineseMatches) {
    console.log('❌ 发现中文CSS类名:');
    chineseMatches.forEach(match => console.log(`   ${match}`));
  } else {
    console.log('✅ 未发现中文CSS类名');
  }
  
  // 检查英文类名是否存在
  const englishClasses = [
    '.level-excellent',
    '.level-good', 
    '.level-stable',
    '.level-poor',
    '.level-bad'
  ];
  
  console.log('\n📝 英文CSS类名检查:');
  englishClasses.forEach(className => {
    if (wxssContent.includes(className)) {
      console.log(`✅ ${className} - 存在`);
    } else {
      console.log(`❌ ${className} - 缺失`);
    }
  });
  
} catch (error) {
  console.error('❌ 读取WXSS文件失败:', error.message);
}

// 2. 检查JavaScript文件中的类名映射
console.log('\n📋 2. 检查JavaScript文件中的类名映射');
console.log('-'.repeat(30));

try {
  const jsPath = path.join(__dirname, 'pages/bazi-result/index.js');
  const jsContent = fs.readFileSync(jsPath, 'utf8');
  
  // 检查类名映射是否存在
  const levelClassMapRegex = /levelClassMap\s*=\s*\{[\s\S]*?\}/g;
  const mapMatches = jsContent.match(levelClassMapRegex);
  
  if (mapMatches) {
    console.log('✅ 发现类名映射:');
    mapMatches.forEach((match, index) => {
      console.log(`   映射${index + 1}: ${match.substring(0, 100)}...`);
    });
  } else {
    console.log('❌ 未发现类名映射');
  }
  
  // 检查levelClass字段的使用
  const levelClassUsage = jsContent.match(/levelClass.*:/g);
  if (levelClassUsage) {
    console.log(`✅ 发现levelClass字段使用: ${levelClassUsage.length}处`);
  } else {
    console.log('❌ 未发现levelClass字段使用');
  }
  
} catch (error) {
  console.error('❌ 读取JavaScript文件失败:', error.message);
}

// 3. 检查WXML文件中的类名绑定
console.log('\n📋 3. 检查WXML文件中的类名绑定');
console.log('-'.repeat(30));

try {
  const wxmlPath = path.join(__dirname, 'pages/bazi-result/index.wxml');
  const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');
  
  // 检查是否使用了levelClass
  const levelClassBindings = wxmlContent.match(/level-\{\{[^}]*levelClass[^}]*\}\}/g);
  if (levelClassBindings) {
    console.log('✅ 发现levelClass绑定:');
    levelClassBindings.forEach(binding => {
      console.log(`   ${binding}`);
    });
  } else {
    console.log('❌ 未发现levelClass绑定');
  }
  
  // 检查是否还有直接的level绑定（可能导致中文类名）
  const directLevelBindings = wxmlContent.match(/level-\{\{[^}]*\.level\}\}/g);
  if (directLevelBindings) {
    console.log('⚠️ 发现直接level绑定（可能导致中文类名）:');
    directLevelBindings.forEach(binding => {
      console.log(`   ${binding}`);
    });
  } else {
    console.log('✅ 未发现直接level绑定');
  }
  
} catch (error) {
  console.error('❌ 读取WXML文件失败:', error.message);
}

// 4. 测试类名映射功能
console.log('\n📋 4. 测试类名映射功能');
console.log('-'.repeat(30));

const levelClassMap = {
  '大吉': 'excellent',
  '中吉': 'good',
  '平稳': 'stable',
  '小凶': 'poor',
  '大凶': 'bad'
};

const testLevels = ['大吉', '中吉', '平稳', '小凶', '大凶', '未知'];

console.log('🧪 类名映射测试:');
testLevels.forEach(level => {
  const mappedClass = levelClassMap[level] || 'stable';
  console.log(`   ${level} → ${mappedClass}`);
});

// 5. 生成修复报告
console.log('\n📋 5. 修复报告');
console.log('-'.repeat(30));

console.log('🎯 修复内容:');
console.log('   1. ✅ 将WXSS中的中文CSS类名改为英文');
console.log('   2. ✅ 在JavaScript中添加类名映射');
console.log('   3. ✅ 在数据返回时添加levelClass字段');
console.log('   4. ✅ 更新WXML中的类名绑定');

console.log('\n🔧 修复前后对比:');
console.log('   修复前: .level-大吉 { ... }');
console.log('   修复后: .level-excellent { ... }');
console.log('   绑定前: level-{{item.level}}');
console.log('   绑定后: level-{{item.levelClass}}');

console.log('\n🎉 WXSS编译错误修复验证完成');
console.log('=' .repeat(50));
