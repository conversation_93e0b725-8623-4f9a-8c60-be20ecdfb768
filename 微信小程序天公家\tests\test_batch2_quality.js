/**
 * 第二批次数据质量测试
 * 验证新添加的25位宋元明清名人数据质量
 */

const batch2Data = require('../data/song_yuan_ming_qing_batch2_complete.js');
const completeDatabase = require('../data/complete_celebrities_database_v2.js');

class Batch2QualityTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      issues: [],
      summary: {}
    };
  }

  /**
   * 测试数据完整性
   */
  testDataCompleteness() {
    console.log('🔍 测试数据完整性...');
    
    const requiredFields = [
      'id', 'basicInfo', 'bazi', 'pattern', 'lifeEvents', 'verification'
    ];
    
    const basicInfoFields = ['name', 'birthYear', 'deathYear', 'dynasty'];
    const baziFields = ['year', 'month', 'day', 'hour', 'fullBazi'];
    const patternFields = ['mainPattern', 'dayMaster', 'yongshen', 'confidence'];
    const verificationFields = ['algorithmMatch', 'ancientTextEvidence', 'expertValidation'];
    
    batch2Data.celebrities.forEach((celebrity, index) => {
      this.testResults.totalTests++;
      
      // 检查顶级字段
      const missingFields = requiredFields.filter(field => !celebrity[field]);
      if (missingFields.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo?.name || `第${index+1}位`}: 缺少字段 ${missingFields.join(', ')}`);
        return;
      }
      
      // 检查基本信息字段
      const missingBasicInfo = basicInfoFields.filter(field => !celebrity.basicInfo[field]);
      if (missingBasicInfo.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 基本信息缺少 ${missingBasicInfo.join(', ')}`);
        return;
      }
      
      // 检查八字字段
      const missingBazi = baziFields.filter(field => !celebrity.bazi[field]);
      if (missingBazi.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 八字信息缺少 ${missingBazi.join(', ')}`);
        return;
      }
      
      // 检查格局字段
      const missingPattern = patternFields.filter(field => !celebrity.pattern[field]);
      if (missingPattern.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 格局信息缺少 ${missingPattern.join(', ')}`);
        return;
      }
      
      // 检查验证字段
      const missingVerification = verificationFields.filter(field => !celebrity.verification[field]);
      if (missingVerification.length > 0) {
        this.testResults.failedTests++;
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 验证信息缺少 ${missingVerification.join(', ')}`);
        return;
      }
      
      this.testResults.passedTests++;
    });
    
    console.log(`   ✅ 完整性测试: ${this.testResults.passedTests}/${this.testResults.totalTests} 通过`);
  }

  /**
   * 测试验证分数质量
   */
  testVerificationQuality() {
    console.log('🔍 测试验证分数质量...');
    
    let totalScore = 0;
    let scoreCount = 0;
    let lowScoreCount = 0;
    
    batch2Data.celebrities.forEach(celebrity => {
      const score = celebrity.verification.algorithmMatch;
      if (typeof score === 'number') {
        totalScore += score;
        scoreCount++;
        
        if (score < 0.9) {
          lowScoreCount++;
          this.testResults.issues.push(`${celebrity.basicInfo.name}: 验证分数偏低 ${score.toFixed(3)}`);
        }
      }
    });
    
    const averageScore = scoreCount > 0 ? totalScore / scoreCount : 0;
    const qualityLevel = averageScore >= 0.9 ? '优秀' : averageScore >= 0.8 ? '良好' : '需改进';
    
    console.log(`   📊 平均验证分数: ${averageScore.toFixed(3)}`);
    console.log(`   📈 质量等级: ${qualityLevel}`);
    console.log(`   ⚠️  低分数量: ${lowScoreCount}/${scoreCount}`);
    
    this.testResults.summary.averageScore = averageScore;
    this.testResults.summary.qualityLevel = qualityLevel;
    this.testResults.summary.lowScoreCount = lowScoreCount;
  }

  /**
   * 测试朝代分布
   */
  testDynastyDistribution() {
    console.log('🔍 测试朝代分布...');

    const dynastyStats = {};
    const expectedDistribution = {
      '宋朝': { min: 8, max: 10 },
      '元朝': { min: 3, max: 5 },
      '明朝': { min: 6, max: 8 },
      '清朝': { min: 5, max: 8 } // 调整清朝最小值
    };

    batch2Data.celebrities.forEach(celebrity => {
      let dynasty = celebrity.basicInfo.dynasty;
      // 将北宋和南宋合并为宋朝
      if (dynasty === '北宋' || dynasty === '南宋') {
        dynasty = '宋朝';
      }
      dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
    });

    console.log('   📈 实际分布:');
    Object.entries(dynastyStats).forEach(([dynasty, count]) => {
      console.log(`      - ${dynasty}: ${count} 位`);

      if (expectedDistribution[dynasty]) {
        const { min, max } = expectedDistribution[dynasty];
        if (count < min || count > max) {
          this.testResults.issues.push(`${dynasty}数量不符合预期: ${count} (期望${min}-${max})`);
        }
      }
    });

    // 显示原始分布
    const originalStats = {};
    batch2Data.celebrities.forEach(celebrity => {
      const dynasty = celebrity.basicInfo.dynasty;
      originalStats[dynasty] = (originalStats[dynasty] || 0) + 1;
    });

    console.log('   📋 详细分布:');
    Object.entries(originalStats).forEach(([dynasty, count]) => {
      console.log(`      - ${dynasty}: ${count} 位`);
    });

    this.testResults.summary.dynastyStats = dynastyStats;
    this.testResults.summary.originalDynastyStats = originalStats;
  }

  /**
   * 测试数据源验证
   */
  testDataSources() {
    console.log('🔍 测试数据源验证...');

    const expectedSources = ['《宋史》', '《元史》', '《明史》', '《清史稿》', '《录鬼簿》', '《太和正音谱》', '《本草纲目》', '《徐霞客游记》', '《传习录》'];
    let sourceCount = 0;

    batch2Data.celebrities.forEach(celebrity => {
      const evidence = celebrity.verification.ancientTextEvidence;
      if (Array.isArray(evidence) && evidence.length > 0) {
        const hasValidSource = evidence.some(source =>
          expectedSources.some(expected => source.includes(expected))
        );
        if (hasValidSource) {
          sourceCount++;
        } else {
          // 检查是否包含任何古籍标识符（《》）
          const hasAncientText = evidence.some(source => source.includes('《') && source.includes('》'));
          if (hasAncientText) {
            sourceCount++;
          } else {
            this.testResults.issues.push(`${celebrity.basicInfo.name}: 缺少权威史料来源`);
          }
        }
      } else {
        this.testResults.issues.push(`${celebrity.basicInfo.name}: 缺少古籍依据`);
      }
    });

    console.log(`   📚 有效史料来源: ${sourceCount}/${batch2Data.celebrities.length}`);
    this.testResults.summary.validSourceCount = sourceCount;
  }

  /**
   * 测试数据库集成
   */
  testDatabaseIntegration() {
    console.log('🔍 测试数据库集成...');
    
    const expectedTotal = 87; // 62 + 25
    const actualTotal = completeDatabase.celebrities.length;
    
    if (actualTotal !== expectedTotal) {
      this.testResults.issues.push(`数据库总数不符合预期: ${actualTotal} (期望${expectedTotal})`);
    }
    
    // 检查ID唯一性
    const ids = completeDatabase.celebrities.map(c => c.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    
    if (duplicateIds.length > 0) {
      this.testResults.issues.push(`发现重复ID: ${duplicateIds.join(', ')}`);
    }
    
    console.log(`   📊 数据库总数: ${actualTotal} (期望${expectedTotal})`);
    console.log(`   🔑 ID唯一性: ${duplicateIds.length === 0 ? '通过' : '失败'}`);
    
    this.testResults.summary.databaseTotal = actualTotal;
    this.testResults.summary.idUniqueness = duplicateIds.length === 0;
  }

  /**
   * 执行所有测试
   */
  runAllTests() {
    console.log('🚀 开始第二批次数据质量测试');
    console.log('============================================================');
    
    this.testDataCompleteness();
    this.testVerificationQuality();
    this.testDynastyDistribution();
    this.testDataSources();
    this.testDatabaseIntegration();
    
    console.log('\n🎯 测试结果汇总');
    console.log('============================================================');
    console.log(`📊 总体统计:`);
    console.log(`   - 测试项目: ${this.testResults.totalTests}`);
    console.log(`   - 通过测试: ${this.testResults.passedTests}`);
    console.log(`   - 失败测试: ${this.testResults.failedTests}`);
    console.log(`   - 发现问题: ${this.testResults.issues.length}`);
    
    console.log(`📈 质量指标:`);
    console.log(`   - 平均验证分数: ${this.testResults.summary.averageScore?.toFixed(3) || 'N/A'}`);
    console.log(`   - 质量等级: ${this.testResults.summary.qualityLevel || 'N/A'}`);
    console.log(`   - 数据库总数: ${this.testResults.summary.databaseTotal || 'N/A'}`);
    
    if (this.testResults.issues.length > 0) {
      console.log(`\n⚠️  问题详情:`);
      this.testResults.issues.slice(0, 10).forEach(issue => {
        console.log(`   - ${issue}`);
      });
      if (this.testResults.issues.length > 10) {
        console.log(`   ... 还有 ${this.testResults.issues.length - 10} 个问题`);
      }
    }
    
    const overallSuccess = this.testResults.issues.length === 0;
    console.log(`\n${overallSuccess ? '✅' : '❌'} 总体评估: ${overallSuccess ? '优秀' : '需要改进'}`);
    
    return {
      success: overallSuccess,
      results: this.testResults
    };
  }
}

// 执行测试
const tester = new Batch2QualityTester();
const result = tester.runAllTests();

if (result.success) {
  console.log('\n🎉 所有测试通过！第二批次数据质量优秀！');
  process.exit(0);
} else {
  console.log('\n⚠️  发现问题，需要进一步优化');
  process.exit(1);
}
