// pages/dialogue-assessment/index.js
const app = getApp();
const navColorUtil = require('../../utils/navigation_color');

Page({
  data: {
    messages: [], // 对话消息记录
    inputValue: '', // 输入框内容
    sending: false, // 是否正在发送消息
    typing: false, // AI是否正在输入
    activeTab: 'today', // 当前选中的标签
    currentDate: '', // 当前日期
    lastMessageId: '', // 最后一条消息ID
    welcomeTitle: '无事不占', // 欢迎标题
    welcomeSubtitle: '天公师兄·李淳风六壬时课，一事一占，为您洞察先机', // 欢迎副标题
    role: 'tarot', // 当前角色，默认为天公师兄
    roleName: '天公师兄', // 角色名称
    assistantName: '天公师兄', // 助手显示名称
    themeClass: 'tarot-theme', // 主题样式类
    themeColor: '#6B5B73', // 默认主题颜色
    themeColorSecondary: 'rgba(107, 91, 115, 0.8)', // 默认次要颜色
    customNavBarStyle: '', // 自定义导航栏样式
    inputAreaStyle: '', // 输入区域样式

    // 占卜相关状态
    userReady: false, // 用户是否准备好开始占卜
    waitingForReadyConfirmation: false // 是否等待用户准备确认
  },

  onLoad(options) {
    console.log('✅ 对话页面加载，语法错误已修复');

    // 处理路由问题 - 使用安全的方式
    try {
      const currentRoute = 'pages/dialogue-assessment/index';
      const app = getApp();
      if (app && typeof app._fixRouteIssue === 'function') {
        app._fixRouteIssue(currentRoute);
      }
      this.route = currentRoute;
      console.log('页面路径设置为:', currentRoute);
    } catch (e) {
      console.log('路由修复尝试失败，忽略此错误', e);
    }

    // 延迟初始化，避免jsbridge调用过早
    setTimeout(() => {
      this.initializePage(options);
    }, 100);
  },

  /**
   * 初始化页面（延迟执行）
   */
  initializePage(options) {
    // 从传入参数获取角色和名称
    if (options.role) {
      const role = options.role;
      const roleName = options.roleName || '天公师兄';
      
      // 设置对应主题类
      let themeClass = 'ai-theme'; // 默认为AI伴侣主题
      
      // 根据角色设置不同的主题和欢迎信息
      if (role === 'tarot') {
        themeClass = 'tarot-theme';
        this.setData({
          welcomeTitle: '无事不占',
          welcomeSubtitle: '天公师兄·李淳风六壬时课，一事一占，为您洞察先机'
        });
      } else if (role === 'bazi') {
        themeClass = 'bazi-theme';
        this.setData({
          welcomeTitle: '玉匣记传承',
          welcomeSubtitle: '天工师父·八字排盘，命定一生，为您解析人生轨迹'
        });
      } else if (role === 'yijing') {
        themeClass = 'yijing-theme';
        this.setData({
          welcomeTitle: '欢迎来到易经八卦',
          welcomeSubtitle: '古老的智慧传承，阴阳变化洞察天机'
        });
      } else if (role === 'ziwei') {
        themeClass = 'ziwei-theme';
        this.setData({
          welcomeTitle: '欢迎来到紫微斗数',
          welcomeSubtitle: '星宿命盘解析，预测人生运势轨迹'
        });
      } else if (role === 'qimen') {
        themeClass = 'qimen-theme';
        this.setData({
          welcomeTitle: '欢迎来到奇门遁甲',
          welcomeSubtitle: '帝王之学秘术，时空奥秘尽在掌握'
        });
      } else if (role === 'liuyao') {
        themeClass = 'liuyao-theme';
        this.setData({
          welcomeTitle: '欢迎来到六爻占卜',
          welcomeSubtitle: '铜钱摇卦问事，六爻变化知吉凶'
        });
      }
      
      this.setData({
        role,
        roleName,
        assistantName: roleName, // 设置助手显示名称
        themeClass
      });
      
      // 保存当前角色到全局变量，方便其他页面共享主题色
      try {
        const app = getApp();
        if (!app.globalData) {
          app.globalData = {};
        }
        app.globalData.currentRole = role;
        console.log('保存当前角色到全局变量:', role);
      } catch (error) {
        console.error('保存角色到全局变量失败:', error);
      }
      
      // 使用导航颜色工具设置导航栏和背景颜色
      const colors = navColorUtil.setNavigationBarColorByRole(role);
      
      // 手动设置样式以确保自定义导航栏和输入区也能够正确应用主题色
      this.setData({
        themeColor: colors.primary,
        themeColorSecondary: colors.secondary,
        customNavBarStyle: `background-color: ${colors.secondary}`,
        inputAreaStyle: `background-color: ${colors.secondary}`
      });
    }
    
    // 设置当前日期
    this.setCurrentDate();
    

    
    // 发送欢迎消息
    setTimeout(() => {
      // 天公师兄占卜欢迎消息
      const welcomeMessage = "吾乃天公师兄，若你有事相询，请告知已准备妥当，吾即刻为你占卜问事。";
      // 设置等待确认状态
      this.setData({
        waitingForReadyConfirmation: true
      });

      this.addMessage("AI", welcomeMessage, "greeting");
    }, 500);
  },
  
  // 当页面显示或重新显示时再次确认颜色设置
  onShow() {
    // 重新应用导航栏颜色设置
    if (this.data.role) {
      const colors = navColorUtil.getRoleColors(this.data.role);
      
      // 确保样式正确应用
      this.setData({
        customNavBarStyle: `background-color: ${colors.secondary}`,
        inputAreaStyle: `background-color: ${colors.secondary}`
      });
    }
  },
  
  // 当页面显示完成后确保样式正确应用
  onReady() {
    // 再次确保样式正确应用
    if (this.data.role) {
      const colors = navColorUtil.getRoleColors(this.data.role);
      this.setData({
        customNavBarStyle: `background-color: ${colors.secondary}`,
        inputAreaStyle: `background-color: ${colors.secondary}`
      });
    }

    // 处理已有消息中的选项题
    setTimeout(() => {
      const { messages } = this.data;
      const updatedMessages = messages.map(message => {
        // 处理包含选项的消息
        if (message.role === 'AI' && message.content && 
            message.content.includes('A.') && message.content.includes('B.') && 
            !message.parsedOptions) {
          message.parsedOptions = this.parseMessageOptions(message.content);
        }
        return message;
      });
      
      if (updatedMessages.length > 0) {
        this.setData({ messages: updatedMessages });
      }
    }, 300);
  },
  
  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
    const chineseDays = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                        '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十', '三十一'];

    const weekday = weekdays[now.getDay()];
    const month = months[now.getMonth()];
    const day = chineseDays[now.getDate()];

    const formattedDate = `${weekday}，${month}${day}`;
    this.setData({
      currentDate: formattedDate.toUpperCase()
    });
  },



  // 添加消息到对话列表
  addMessage(role, content, type = "normal", emotionType = null) {
    const { messages } = this.data;
    const id = Date.now();
    
    // 创建消息对象
    const messageObj = {
      id,
      role,
      content,
      type,
      emotionType,
      time: new Date().toLocaleTimeString()
    };
    
    // 如果是AI消息且包含选项题格式，预处理选项
    if (role === "AI" && content.includes("A.") && content.includes("B.")) {
      // 解析选项内容
      const parsedOptions = this.parseMessageOptions(content);
      messageObj.parsedOptions = parsedOptions;
    }
    
    messages.push(messageObj);

    this.setData({
      messages,
      typing: role === "AI"
    });

    // 设置最后一条消息ID，用于滚动到底部
    this.setData({
      lastMessageId: `msg-${id}`
    });

    // 如果是AI消息，模拟打字效果
    if (role === "AI") {
      setTimeout(() => {
        this.setData({
          typing: false
        });
      }, 1000);
    }
  },
  
  // 解析选项消息的选项部分
  parseMessageOptions(content) {
    try {
      // 将内容按行分割，取第2行开始的部分（选项部分）
      const lines = content.split('\n');
      const optionLines = lines.slice(2); // 跳过标题和空行
      
      // 过滤并清理选项行
      const parsedOptions = optionLines
        .map(line => line.trim())
        .filter(line => line.length > 0);
      
      return parsedOptions;
    } catch (error) {
      console.error('解析选项出错:', error);
      return []; // 出错时返回空数组
    }
  },

  // 输入框内容变化
  onInputChange(e) {
    const now = Date.now();
    this.setData({
      inputValue: e.detail.value,
      lastInputTime: now,
      inputPaused: false
    });
    
    // 清除之前的计时器
    if (this.data.inputTimer) {
      clearTimeout(this.data.inputTimer);
    }
    
    // 如果有内容，开始监测输入暂停
    if (e.detail.value) {
      const timer = setTimeout(() => {
        this.checkInputPause();
      }, 5000); // 5秒无输入则检查
      
      this.setData({
        inputTimer: timer
      });
    } else {
      // 如果输入框被清空，隐藏提示
      this.setData({
        showSmartTip: false
      });
    }
  },

  // 检查输入暂停状态
  checkInputPause() {
    const { inputValue, lastInputTime, currentQuestionIndex, questions } = this.data;
    const now = Date.now();
    
    // 如果暂停超过5秒并且输入框有内容，显示提示
    if (now - lastInputTime >= 5000 && inputValue && !this.data.inputPaused) {
      const currentQuestion = questions[currentQuestionIndex];
      let tipContent = "似乎你在思考如何回答...\n";
      
      // 根据当前问题维度提供针对性提示
      if (currentQuestion && currentQuestion.dimension) {
        switch(currentQuestion.dimension) {
          case 'emotion':
            tipContent += "你可以描述一下具体的感受，比如快乐、焦虑或平静等。";
            break;
          case 'social':
            tipContent += "可以谈谈你与朋友或家人的互动，以及这些互动给你带来的感受。";
            break;
          case 'academic':
            tipContent += "你可以从学习压力、成绩表现或学习方法等方面来描述。";
            break;
          case 'physical':
            tipContent += "可以谈谈你的睡眠质量、饮食习惯或身体状况。";
            break;
          default:
            tipContent += "你可以简单描述一下最近的经历和感受。";
        }
      } else {
        tipContent += "可以简单描述一下你的想法，没有对错之分。";
      }
      
      this.setData({
        showSmartTip: true,
        smartTipContent: tipContent,
        inputPaused: true
      });
      
      // 10秒后自动隐藏提示
      setTimeout(() => {
        this.setData({
          showSmartTip: false
        });
      }, 10000);
    }
  },

  // 发送消息
  sendMessage() {
    // 获取用户输入
    const input = this.data.inputValue.trim();
    
    // 检查输入是否为空
    if (!input) {
      return;
    }
    
    // 添加用户消息
    this.addMessage("user", input);
    
    // 清空输入框并设置状态
    this.setData({
      inputValue: '',
      sending: true
    });
    
    console.log('处理用户消息:', input, '当前状态:', this.data);
    

    
    // 处理占卜逻辑
    if (this.data.waitingForReadyConfirmation) {
      // 检查用户是否准备好开始占卜
      const userReady = this.checkIfUserReady(input);
      console.log('用户准备状态:', userReady);
      if (userReady === 'ready') {
        this.handleUserReady();
      } else {
        this.handleUserNotReady();
      }
    } else {
      // 默认消息处理
      console.log('默认消息处理');
      this.processDefaultMessage(input);
    }
  },
  


  // 处理默认消息（占卜之外的交互）
  processDefaultMessage(message) {
    // 其他对话 - 天公师兄回复
    setTimeout(() => {
      const defaultMessage = "贫道天公师兄在此。若您有事相询，请告知已准备妥当，贫道即为您占卜问事。";

      this.addMessage("AI", defaultMessage);
      this.setData({
        sending: false,
        waitingForReadyConfirmation: true
      });
    }, 1000);
  },




  

  




  // 导航到个人资料页面
  navigateToProfile() {
    wx.navigateTo({
      url: '/pages/profile/index'
    });
  },

  // 导航到历史记录页面
  navigateToHistory() {
    wx.navigateTo({
      url: '/pages/assessment-history/index'
    });
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    
    this.setData({
      activeTab: tab
    });
    
    // 如果切换到历史标签，跳转到历史页面
    if (tab === 'history') {
      this.navigateToHistory();
    }
    
    // 刷新颜色设置
    if (this.data.role) {
      navColorUtil.refreshNavigationBarColor(this.data.role);
    }
  },

  // 查看完整报告
  viewFullReport() {
    wx.navigateTo({
      url: '/pages/assessment-report/index'
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '天公师兄占卜',
      path: '/pages/dialogue-assessment/index'
    };
  },

  // 添加情绪分析函数
  analyzeEmotionType(text) {
    // 情感关键词
    const emotionKeywords = {
      positive: ['快乐', '开心', '高兴', '满意', '喜欢', '愉悦', '幸福', '积极', '兴奋', '好', '很好', '不错', '棒', '优秀'],
      negative: ['伤心', '难过', '痛苦', '焦虑', '沮丧', '烦恼', '悲伤', '抑郁', '紧张', '害怕', '担心', '痛苦', '不好', '糟糕', '差'],
      neutral: ['一般', '还行', '普通', '正常', '平静', '平常', '一般般', '马马虎虎']
    };
    
    // 转为小写以进行大小写不敏感匹配
    const lowerText = text.toLowerCase();
    
    // 统计各情绪类型的匹配次数
    let counts = { positive: 0, negative: 0, neutral: 0 };
    
    for (const type in emotionKeywords) {
      for (const keyword of emotionKeywords[type]) {
        if (lowerText.includes(keyword)) {
          counts[type]++;
        }
      }
    }
    
    // 确定主要情绪类型
    if (counts.positive > counts.negative && counts.positive > counts.neutral) {
      return 'positive';
    } else if (counts.negative > counts.positive && counts.negative > counts.neutral) {
      return 'negative';
    } else if (counts.neutral > 0) {
      return 'neutral';
    } else {
      // 默认为中性
      return 'neutral';
    }
  },

  // 检查用户是否准备好开始评测
  checkIfUserReady(response) {
    console.log('检查用户是否准备好:', response);
    
    // 表示准备好的关键词（包含天公师兄相关的确认词汇）
    const readyKeywords = ['准备好', '可以开始', '开始吧', '好的', '可以', '是的', '好啊', '嗯', '好', '开始', '没问题', '行', '确定', '准备妥当', '妥当', '请占卜', '占卜', '问事', '求占', '请问', '想问'];
    
    // 表示没准备好的关键词
    const notReadyKeywords = ['还没', '不', '等等', '等一下', '稍等', '不行', '不可以', '不好', '不是', '没准备好', '不开始', '不要'];
    
    // 检查用户回答中是否包含准备好的关键词
    for (const keyword of readyKeywords) {
      if (response.includes(keyword)) {
        console.log('用户已准备好，匹配关键词:', keyword);
        return 'ready';
      }
    }
    
    // 检查用户回答中是否包含没准备好的关键词
    for (const keyword of notReadyKeywords) {
      if (response.includes(keyword)) {
        console.log('用户未准备好，匹配关键词:', keyword);
        return 'not_ready';
      }
    }
    
    // 无法确定用户是否准备好，默认当作已准备好处理
    console.log('无法确定用户是否准备好，默认准备好');
    return 'ready';
  },
  
  // 处理用户准备好的情况
  handleUserReady() {
    console.log('处理用户准备好的情况，当前角色:', this.data.role);
    
    setTimeout(() => {
      const readyMessage = "太好了！天公师兄将运用李淳风六壬时课为您占卜。请在即将打开的页面中详细描述您的问题，记住：无事不占，一事一占。";

      this.addMessage("AI", readyMessage);
      
      setTimeout(() => {
        // 设置用户已准备好状态
        this.setData({
          userReady: true,
          waitingForReadyConfirmation: false,
          sending: false,
          // 不再询问身份，因为已经从入口确定了身份
          askingForIdentity: false
        });
        
        // 天公师兄占卜流程
        this.setData({ userRole: 'tarot' });

        // 显示跳转提示，优化用户体验
        this.addMessage("AI", "正在为您准备占卜界面...");

        // 显示加载状态
        wx.showLoading({
          title: '准备中...',
          mask: true
        });

        // 延迟2秒后跳转到占卜信息收集页面
        setTimeout(() => {
          wx.hideLoading();
          wx.navigateTo({
            url: '/pages/divination-input/index',
            success: () => {
              console.log('成功跳转到占卜信息收集页面');
            },
            fail: (err) => {
              console.error('跳转到占卜页面失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'error'
              });
              this.addMessage("AI", "抱歉，无法打开占卜页面。请稍后再试。");
              this.setData({ sending: false });
            }
          });
        }, 2000);
      }, 1000);
    }, 1000);
  },
  
  // 处理用户未准备好的情况
  handleUserNotReady() {
    console.log('处理用户未准备好的情况');
    
    // 生成随机回复，增加对话的多样性
    const responses = [
      "没关系，我们可以稍后再开始。当您准备好了，随时告诉我。",
      "我理解，有时候需要一点时间做好准备。准备好了告诉我就行。",
      "好的，不着急。您可以随时告诉我您准备好了，我们再开始测评。"
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    setTimeout(() => {
      this.addMessage("AI", randomResponse);
      
      // 设置用户未准备好状态，但保持等待用户自主发起准备好的状态
      this.setData({
        userReady: false,
        waitingForReadyConfirmation: true,
        sending: false
      });
      
      // 移除自动再次询问的代码，由用户自主发起"准备好了"的消息
    }, 1000);
  },
  
  // 处理用户身份回答
  handleIdentityResponse(response) {
    const lowerResponse = response.toLowerCase();
    
    // 根据关键词判断身份
    if (lowerResponse.includes('学生') || lowerResponse.includes('孩子') || 
        lowerResponse.includes('我是学生') || lowerResponse.includes('自己')) {
      // 学生身份
      setTimeout(() => {
        this.addMessage("AI", "了解了，您是学生。为了提供更准确的评测，请先完成个人信息填写。请在即将打开的页面中填写您的个人信息，完成后点击\"开始测评\"按钮。");
        
        // 设置用户角色
        this.setData({
          userRole: 'student',
          sending: false
        });
        
        // 学生身份已确认，开始占卜流程
        setTimeout(() => {
          this.addMessage("AI", "好的，现在请告诉我您想要占卜的问题。");
        }, 1000);
      }, 1000);
    } else {
      // 其他回复，继续占卜对话
      setTimeout(() => {
        this.addMessage("AI", "好的，现在请告诉我您想要占卜的问题。");

        // 设置状态，准备接收占卜问题
        this.setData({
          askingForIdentity: false,
          sending: false
        });
      }, 1000);
    }
  },
  
  // 提问当前问题
  askCurrentQuestion() {
    const { currentGradeQuestions, currentQuestionIndex } = this.data;
    
    console.log(`提问问题 ${currentQuestionIndex + 1}/${currentGradeQuestions.length}`);
    
    // 修复：确保currentGradeQuestions存在且不为空
    if (!currentGradeQuestions || currentGradeQuestions.length === 0) {
      console.error('评估题库为空，无法继续测评');
      this.addMessage("AI", "抱歉，测评题库出现问题，请尝试重新开始测评。");
      this.setData({ sending: false });
      return;
    }
    
    if (currentQuestionIndex >= currentGradeQuestions.length) {
      // 所有问题已经完成
      console.log('所有问题已完成，生成评估结果');
      this.completeAssessment();
      return;
    }
    
    const currentQuestion = currentGradeQuestions[currentQuestionIndex];
    
    if (!currentQuestion) {
      console.error('当前问题为空');
      this.completeAssessment();
      return;
    }
    
    // 重要：确保在问题阶段正确设置状态
    this.setData({
      askingForIdentity: false,
      askingForStudentInfo: false,
      userReady: true,
      waitingForReadyConfirmation: false
    });
    
    // 获取问题内容
    let questionText = '';
    
    // 尝试从不同字段获取问题内容
    if (currentQuestion.content) {
      questionText = currentQuestion.content;
    } else if (currentQuestion.text) {
      questionText = currentQuestion.text;
    } else if (currentQuestion.question) {
      questionText = currentQuestion.question;
    } else {
      questionText = "此题缺少问题内容";
      console.error('问题缺少内容:', currentQuestion);
    }
    
    // 获取选项
    let options = [];
    if (currentQuestion.options && Array.isArray(currentQuestion.options)) {
      // 选项可能是字符串数组或对象数组
      options = currentQuestion.options.map(option => {
        if (typeof option === 'string') {
          return option;
        } else if (option.text) {
          return option.text;
        } else if (option.content) {
          return option.content;
        } else if (option.label) {
          return option.label;
        } else {
          return "选项内容缺失";
        }
      });
    } else {
      console.error('问题缺少选项:', currentQuestion);
      options = ["选项A", "选项B", "选项C"]; // 提供默认选项以防止崩溃
    }
    
    // 生成带字母标签的选项
    const optionLabels = ['A', 'B', 'C', 'D', 'E'];
    
    // 构建带选项的问题文本
    let fullQuestionText = `问题 ${currentQuestionIndex + 1}/${currentGradeQuestions.length}：${questionText}\n\n`;
    
    options.forEach((option, index) => {
      if (index < optionLabels.length) {
        fullQuestionText += `${optionLabels[index]}. ${option}\n`;
      }
    });
    
    // 保存当前选项对应的标签
    this.setData({
      currentOptionLabels: optionLabels.slice(0, options.length)
    });
    
    console.log('发送问题:', fullQuestionText);
    
    // 发送问题
    this.addMessage("AI", fullQuestionText);
    
    // 设置可以输入回答
    this.setData({ sending: false });
    
    // 更新进度
    const progress = Math.min(100, Math.round(((currentQuestionIndex + 1) / currentGradeQuestions.length) * 100));
    this.setData({
      assessmentProgress: progress
    });
  },
  
  // 处理用户对选择题的回答
  handleOptionSelection(response) {
    const { currentGradeQuestions, currentQuestionIndex, currentOptionLabels } = this.data;
    
    // 修复：确保题库和当前问题索引有效
    if (!currentGradeQuestions || currentGradeQuestions.length === 0) {
      console.error('评估题库为空，无法处理选项');
      this.addMessage("AI", "抱歉，测评遇到问题，请尝试重新开始。");
      this.setData({ sending: false });
      return;
    }
    
    if (currentQuestionIndex >= currentGradeQuestions.length) {
      console.error('问题索引超出范围，无法处理选项');
      this.completeAssessment();
      return;
    }
    
    const currentQuestion = currentGradeQuestions[currentQuestionIndex];
    
    if (!currentQuestion) {
      console.error('当前问题为空，无法处理选项');
      return;
    }
    
    console.log('处理选项回答:', response);
    console.log('当前问题:', currentQuestion);
    console.log('当前问题选项标签:', currentOptionLabels);
    
    // 尝试找出用户选择的选项
    const upperResponse = response.toUpperCase().trim();
    let selectedOptionIndex = -1;
    
    // 添加更全面的选项匹配逻辑
    // 1. 直接匹配字母（A, B, C...）和小写字母（a, b, c...）
    // 2. 匹配"选A", "选B"等格式
    // 3. 匹配完整的选项文本
    for (let i = 0; i < currentOptionLabels.length; i++) {
      if (upperResponse === currentOptionLabels[i] || 
          upperResponse.startsWith(currentOptionLabels[i] + '.') || 
          upperResponse.startsWith(currentOptionLabels[i] + '、') ||
          upperResponse.includes(currentOptionLabels[i]) ||
          upperResponse.includes('选' + currentOptionLabels[i]) ||
          upperResponse.includes('选择' + currentOptionLabels[i])) {
        
        selectedOptionIndex = i;
        console.log('匹配到选项:', currentOptionLabels[i], '索引:', i);
        break;
      }
    }
    
    // 如果没有找到匹配的选项，但回答只有一个字符，尝试按顺序解释
    if (selectedOptionIndex === -1 && response.length === 1) {
      const lowerChar = response.toLowerCase();
      const upperChar = response.toUpperCase();
      
      // 匹配字母 a-e 或 A-E
      const letterIndex = 'ABCDE'.indexOf(upperChar);
      if (letterIndex >= 0 && letterIndex < currentOptionLabels.length) {
        selectedOptionIndex = letterIndex;
        console.log('通过字母匹配到选项:', upperChar, '索引:', letterIndex);
      }
      
      // 匹配数字 1-5
      const numIndex = '12345'.indexOf(response);
      if (numIndex >= 0 && numIndex < currentOptionLabels.length) {
        selectedOptionIndex = numIndex;
        console.log('通过数字匹配到选项:', response, '索引:', numIndex);
      }
    }
    
    // 如果无法识别用户选项，提示并让用户重新选择
    if (selectedOptionIndex === -1) {
      this.addMessage("AI", "抱歉，我没能识别您的选择。请输入选项对应的字母（A, B, C...）或数字（1, 2, 3...）。");
      this.setData({ sending: false });
      return;
    }
    
    // 记录用户选择的分数
    if (currentQuestion.dimension) {
      // 假设选项顺序对应分数5,4,3,2,1或1,2,3,4,5
      let score = 0;
      
      // 根据问题类型确定分数计算方式
      if (currentQuestion.reverse) {
        // 反向计分题目
        score = selectedOptionIndex + 1;
      } else {
        // 正向计分题目
        score = currentOptionLabels.length - selectedOptionIndex;
      }
      
      console.log(`问题"${currentQuestion.text || currentQuestion.content}"的得分:`, score);
      
      // 更新维度得分
      this.updateDimensionScore(currentQuestion.dimension, score);
    }
    
    // 移动到下一个问题
    setTimeout(() => {
      this.setData({
        currentQuestionIndex: currentQuestionIndex + 1
      });
      
      // 继续提问或完成评估
      setTimeout(() => {
        this.askCurrentQuestion();
      }, 500);
    }, 1000);
  },
  
  // 更新维度得分
  updateDimensionScore(dimension, score) {
    const { dimensions } = this.data;
    
    if (!dimensions[dimension]) {
      dimensions[dimension] = {
        scores: [score],
        total: score,
        count: 1
      };
    } else {
      dimensions[dimension].scores.push(score);
      dimensions[dimension].total += score;
      dimensions[dimension].count += 1;
    }
    
    this.setData({ dimensions });
    console.log('更新维度得分:', dimensions);
  },
  
  // 完成评估
  completeAssessment() {
    this.addMessage("AI", "感谢您完成所有问题！我正在生成评估报告...");
    
    // 更新状态
    this.setData({
      assessmentComplete: true,
      assessmentProgress: 100
    });
    
    // 生成评估结果
    setTimeout(() => {
      this.generateAssessmentResult();
    }, 1500);
  },
  
  // 简化版年级问题加载函数（测试用）
  loadSimplifiedGradeQuestions(grade) {
    const gradeNumber = this.extractGradeNumber(grade);
    console.log(`加载简化版${gradeNumber}年级题库`);
    
    // 根据年级返回不同的题目集
    let questions = [];
    
    // 共同的问题结构
    const baseQuestions = [
      {
        text: "最近一周，你的心情如何？",
        dimension: "emotion",
        options: ["非常好", "较好", "一般", "不太好", "很差"],
        reverse: false
      },
      {
        text: "你最近的睡眠质量怎么样？",
        dimension: "physical",
        options: ["非常好", "较好", "一般", "不太好", "很差"],
        reverse: false
      },
      {
        text: "你能集中注意力的时间有多长？",
        dimension: "attention",
        options: ["2小时以上", "1-2小时", "30分钟-1小时", "不到30分钟", "几乎无法集中"],
        reverse: false
      },
      {
        text: "你与同学的关系如何？",
        dimension: "social",
        options: ["非常好", "较好", "一般", "不太好", "很差"],
        reverse: false
      },
      {
        text: "你目前的学习压力有多大？",
        dimension: "stress",
        options: ["几乎没有", "较小", "一般", "较大", "非常大"],
        reverse: true
      }
    ];
    
    // 根据年级对问题进行调整
    if (gradeNumber <= 6) {
      // 小学生版本 - 使用更简单的语言
      questions = baseQuestions.map(q => {
        // 简化问题表述
        if (q.text.includes("最近一周")) {
          q.text = "这个星期你开心吗？";
        } else if (q.text.includes("睡眠质量")) {
          q.text = "你晚上睡得好吗？";
        } else if (q.text.includes("注意力")) {
          q.text = "你做作业时能专心多久？";
        } else if (q.text.includes("同学的关系")) {
          q.text = "你和小朋友们玩得开心吗？";
        } else if (q.text.includes("学习压力")) {
          q.text = "你觉得功课难吗？";
        }
        return q;
      });
    } else if (gradeNumber <= 9) {
      // 初中生版本 - 标准语言
      questions = baseQuestions;
    } else {
      // 高中生版本 - 更详细的问题
      questions = baseQuestions.map(q => {
        // 增加问题复杂度
        if (q.text.includes("最近一周")) {
          q.text = "最近一周，你的情绪状态和心理感受如何？";
        } else if (q.text.includes("睡眠质量")) {
          q.text = "你近期的睡眠质量和作息规律性如何？";
        } else if (q.text.includes("注意力")) {
          q.text = "在学习任务中，你的专注度和持续注意力维持时间如何？";
        } else if (q.text.includes("同学的关系")) {
          q.text = "你与同学的人际互动和社交关系状况如何？";
        } else if (q.text.includes("学习压力")) {
          q.text = "目前学业和未来规划给你带来的压力程度如何？";
        }
        return q;
      });
    }
    
    return questions;
  },
  
  // 从年级文本中提取数字表示
  extractGradeNumber(gradeText) {
    // 打印原始年级文本，便于调试
    console.log('提取年级数字，原始文本:', gradeText);
    
    // 针对不同格式的年级文本提取数字
    if (!gradeText) {
      console.log('年级文本为空，使用默认值7(初一)');
      return 7; // 默认初一
    }
    
    // 检查是否包含"小学"关键词
    const isPrimary = gradeText.includes('小学');
    
    // 直接是数字的情况
    if (!isNaN(gradeText)) {
      const num = parseInt(gradeText);
      console.log(`数字格式年级: ${num}`);
      return num;
    }
    
    // 处理"x年级"格式
    const gradeMatch = gradeText.match(/(\d+)年级/);
    if (gradeMatch) {
      const num = parseInt(gradeMatch[1]);
      
      // 确保小于7的数字被识别为小学年级
      if (num <= 6) {
        console.log(`匹配到${num}年级，识别为小学${num}年级`);
        return num; // 直接返回数字，表示小学年级
      } else {
        console.log(`匹配到${num}年级，识别为中学年级`);
        return num;
      }
    }
    
    // 处理"小学x年级"格式
    const primaryMatch = gradeText.match(/小学(\d+)年级/);
    if (primaryMatch) {
      const num = parseInt(primaryMatch[1]);
      console.log(`匹配到小学${num}年级`);
      return num;
    }
    
    // 处理"六年级"等含中文数字的年级
    const chineseNumbers = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9};
    for (const [chinese, number] of Object.entries(chineseNumbers)) {
      // 匹配"六年级"这种格式
      if (gradeText.includes(`${chinese}年级`)) {
        if (isPrimary || number <= 6) {
          console.log(`匹配到${chinese}年级，识别为小学${number}年级`);
          return number; // 返回数字表示小学年级
        }
      }
    }
    
    // 处理"初x"或"高x"格式
    const juniorMatch = gradeText.match(/初(\d+)/);
    if (juniorMatch) {
      const num = 6 + parseInt(juniorMatch[1]);
      console.log(`匹配到初${juniorMatch[1]}，识别为${num}年级`);
      return num; // 初一 = 7年级
    }
    
    const seniorMatch = gradeText.match(/高(\d+)/);
    if (seniorMatch) {
      const num = 9 + parseInt(seniorMatch[1]);
      console.log(`匹配到高${seniorMatch[1]}，识别为${num}年级`);
      return num; // 高一 = 10年级
    }
    
    // 处理中文数字(初中/高中)
    for (const [chinese, number] of Object.entries(chineseNumbers)) {
      if (gradeText.includes(`小学${chinese}年级`)) {
        console.log(`匹配到小学${chinese}年级，识别为${number}年级`);
        return number;
      }
      if (gradeText.includes(`初${chinese}`)) {
        const num = 6 + number;
        console.log(`匹配到初${chinese}，识别为${num}年级`);
        return num;
      }
      if (gradeText.includes(`高${chinese}`)) {
        const num = 9 + number;
        console.log(`匹配到高${chinese}，识别为${num}年级`);
        return num;
      }
    }
    
    // 默认返回初一
    console.log('未匹配到任何年级格式，使用默认值7(初一)');
    return 7;
  },
  
  // 处理学生信息输入
  handleStudentInfoInput(input) {
    console.log('处理学生信息输入:', input);
    
    // 简单解析输入的学生信息
    const info = this.parseStudentInfo(input);
    
    if (info && (info.name || info.grade)) {
      // 保存提取到的信息
      const userInfo = {
        ...this.data.userInfo,
        ...info
      };
      
      this.setData({
        userInfo,
        hasUserInfo: true,
        askingForStudentInfo: false,
        sending: false
      });
      
      // 确认收到的信息
      let confirmMessage = "谢谢您提供的信息。";
      if (info.name) confirmMessage += `学生姓名: ${info.name}. `;
      if (info.gender) confirmMessage += `性别: ${info.gender}. `;
      if (info.age) confirmMessage += `年龄: ${info.age}岁. `;
      if (info.grade) confirmMessage += `年级: ${info.grade}. `;
      
      this.addMessage("AI", confirmMessage);
      
      // 开始测评
      setTimeout(() => {
        this.addMessage("AI", "现在我们开始针对性的测评。");
        
        // 根据年级加载题库
        const grade = info.grade || '初一';
        console.log('根据年级加载题库:', grade);
        
        // 加载年级题库并开始测评
        const gradeQuestions = this.loadSimplifiedGradeQuestions(grade);
        this.setData({
          currentGradeQuestions: gradeQuestions
        });
        
        setTimeout(() => {
          this.startGradeAssessment();
        }, 1000);
      }, 1500);
    } else {
      // 无法识别完整信息，给出提示
      this.addMessage("AI", "抱歉，我没能完全理解您提供的信息。请按以下格式提供：学生姓名、性别、年龄和年级。例如：'张三，男，12岁，初一'。");
      this.setData({ sending: false });
    }
  },
  
  // 简单解析学生信息
  parseStudentInfo(text) {
    console.log('解析学生信息:', text);
    const info = {};
    
    // 尝试提取姓名 (通常是2-4个字的中文名)
    const nameMatch = text.match(/([一-龥]{2,4})[,，\s]/);
    if (nameMatch) {
      info.name = nameMatch[1];
    }
    
    // 尝试提取性别
    if (text.includes('男') || text.includes('男生') || text.includes('男孩')) {
      info.gender = '男';
    } else if (text.includes('女') || text.includes('女生') || text.includes('女孩')) {
      info.gender = '女';
    }
    
    // 尝试提取年龄
    const ageMatch = text.match(/(\d+)[岁歲]/);
    if (ageMatch) {
      info.age = ageMatch[1];
    }
    
    // 尝试提取年级
    const gradeKeywords = ['幼儿园', '小学', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', 
                          '初中', '初一', '初二', '初三', '高中', '高一', '高二', '高三'];
    
    for (const keyword of gradeKeywords) {
      if (text.includes(keyword)) {
        info.grade = keyword;
        break;
      }
    }
    
    return info;
  }
});