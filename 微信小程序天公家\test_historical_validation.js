// test_historical_validation.js
// 历史案例库验证测试
// 验证曾国藩、李白、诸葛亮等历史人物八字案例

const HistoricalCaseValidator = require('./utils/historical_case_validator.js');

/**
 * 历史案例验证测试套件
 */
class HistoricalValidationTest {
  constructor() {
    this.validator = new HistoricalCaseValidator();
  }

  /**
   * 运行完整的历史案例验证测试
   */
  async runCompleteValidation() {
    console.log('🏛️ 开始历史案例库完整验证测试');
    console.log('=' .repeat(60));

    try {
      // 1. 执行历史案例验证
      const validationResults = await this.validator.validateAllCases();
      
      // 2. 打印详细结果
      this.printDetailedResults(validationResults);
      
      // 3. 生成验证报告
      this.generateValidationReport(validationResults);
      
      // 4. 检查技术文档符合度
      this.checkDocumentCompliance(validationResults);
      
      return validationResults;

    } catch (error) {
      console.error('❌ 历史案例验证测试失败:', error);
      throw error;
    }
  }

  /**
   * 打印详细验证结果
   * @param {Object} results - 验证结果
   */
  printDetailedResults(results) {
    console.log('\n📊 详细验证结果:');
    console.log('-'.repeat(60));

    results.detailed_results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.name}`);
      console.log(`   八字: ${result.bazi}`);
      console.log(`   预期格局: ${result.expected_pattern}`);
      console.log(`   实际格局: ${result.actual_pattern}`);
      console.log(`   格局匹配: ${result.pattern_match ? '✅' : '❌'}`);
      console.log(`   用神匹配: ${result.yongshen_match ? '✅' : '❌'}`);
      console.log(`   特征匹配: ${result.characteristics_match ? '✅' : '❌'}`);
      console.log(`   准确率: ${(result.actual_accuracy * 100).toFixed(1)}%`);
      console.log(`   验证状态: ${result.validation_passed ? '✅ 通过' : '❌ 失败'}`);
      
      if (result.error) {
        console.log(`   错误信息: ${result.error}`);
      }
    });
  }

  /**
   * 生成验证报告
   * @param {Object} results - 验证结果
   */
  generateValidationReport(results) {
    console.log('\n📋 验证报告总结:');
    console.log('='.repeat(60));
    
    console.log(`总案例数: ${results.total_cases}`);
    console.log(`通过案例: ${results.passed_cases} ✅`);
    console.log(`失败案例: ${results.failed_cases} ❌`);
    console.log(`总体准确率: ${(results.overall_accuracy * 100).toFixed(1)}%`);
    console.log(`验证等级: ${results.summary.grade}`);
    console.log(`状态描述: ${results.summary.description}`);
    console.log(`技术文档符合度: ${results.summary.compliance_status}`);

    if (results.summary.recommendations.length > 0) {
      console.log('\n🔧 改进建议:');
      results.summary.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }
  }

  /**
   * 检查技术文档符合度
   * @param {Object} results - 验证结果
   */
  checkDocumentCompliance(results) {
    console.log('\n📚 技术文档符合度检查:');
    console.log('-'.repeat(60));

    // 技术文档要求的准确率标准
    const documentRequirements = {
      '曾国藩': { pattern: '正官格', accuracy: 0.987 },
      '李白': { pattern: '食神格', accuracy: 0.952 },
      '诸葛亮': { pattern: '偏印格', accuracy: 0.971 }
    };

    let compliantCases = 0;
    let totalRequiredAccuracy = 0;
    let actualTotalAccuracy = 0;

    results.detailed_results.forEach(result => {
      const requirement = documentRequirements[result.name];
      if (requirement) {
        const patternCompliant = result.actual_pattern === requirement.pattern;
        const accuracyCompliant = result.actual_accuracy >= requirement.accuracy;
        const overallCompliant = patternCompliant && accuracyCompliant;

        console.log(`\n${result.name}:`);
        console.log(`   要求格局: ${requirement.pattern} | 实际: ${result.actual_pattern} ${patternCompliant ? '✅' : '❌'}`);
        console.log(`   要求准确率: ${(requirement.accuracy * 100).toFixed(1)}% | 实际: ${(result.actual_accuracy * 100).toFixed(1)}% ${accuracyCompliant ? '✅' : '❌'}`);
        console.log(`   文档符合度: ${overallCompliant ? '✅ 符合' : '❌ 不符合'}`);

        if (overallCompliant) compliantCases++;
        totalRequiredAccuracy += requirement.accuracy;
        actualTotalAccuracy += result.actual_accuracy;
      }
    });

    const overallCompliance = compliantCases / Object.keys(documentRequirements).length;
    const averageRequiredAccuracy = totalRequiredAccuracy / Object.keys(documentRequirements).length;
    const averageActualAccuracy = actualTotalAccuracy / Object.keys(documentRequirements).length;

    console.log(`\n📈 总体技术文档符合度:`);
    console.log(`   符合案例比例: ${(overallCompliance * 100).toFixed(1)}%`);
    console.log(`   平均要求准确率: ${(averageRequiredAccuracy * 100).toFixed(1)}%`);
    console.log(`   平均实际准确率: ${(averageActualAccuracy * 100).toFixed(1)}%`);
    console.log(`   准确率达标: ${averageActualAccuracy >= averageRequiredAccuracy ? '✅' : '❌'}`);

    // 判断是否符合技术文档整体要求
    const documentCompliant = overallCompliance >= 0.8 && averageActualAccuracy >= averageRequiredAccuracy;
    console.log(`\n🎯 技术文档整体符合度: ${documentCompliant ? '✅ 符合' : '❌ 不符合'}`);

    return {
      compliant: documentCompliant,
      compliance_rate: overallCompliance,
      accuracy_comparison: {
        required: averageRequiredAccuracy,
        actual: averageActualAccuracy,
        meets_standard: averageActualAccuracy >= averageRequiredAccuracy
      }
    };
  }

  /**
   * 运行单个案例测试
   * @param {string} caseName - 案例名称
   */
  async runSingleCaseTest(caseName) {
    console.log(`🔍 单独验证历史案例: ${caseName}`);
    
    const testCase = this.validator.historicalCases.find(c => c.name === caseName);
    if (!testCase) {
      throw new Error(`未找到历史案例: ${caseName}`);
    }

    try {
      const result = await this.validator.validateSingleCase(testCase);
      
      console.log('\n📊 验证结果:');
      console.log(`格局匹配: ${result.pattern_match ? '✅' : '❌'} (${result.expected_pattern} -> ${result.actual_pattern})`);
      console.log(`用神匹配: ${result.yongshen_match ? '✅' : '❌'}`);
      console.log(`准确率: ${(result.actual_accuracy * 100).toFixed(1)}%`);
      console.log(`验证状态: ${result.validation_passed ? '✅ 通过' : '❌ 失败'}`);

      return result;

    } catch (error) {
      console.error(`❌ ${caseName} 验证失败:`, error.message);
      throw error;
    }
  }

  /**
   * 生成改进建议
   * @param {Object} results - 验证结果
   */
  generateImprovementSuggestions(results) {
    console.log('\n🔧 算法改进建议:');
    console.log('-'.repeat(60));

    const failedCases = results.detailed_results.filter(r => !r.validation_passed);
    
    if (failedCases.length === 0) {
      console.log('✅ 所有历史案例验证通过，算法表现优秀！');
      return;
    }

    console.log(`❌ ${failedCases.length} 个案例验证失败，需要改进:`);

    failedCases.forEach(failedCase => {
      console.log(`\n${failedCase.name}:`);
      
      if (!failedCase.pattern_match) {
        console.log(`   - 格局判定错误: ${failedCase.expected_pattern} -> ${failedCase.actual_pattern}`);
        console.log(`     建议: 检查月令藏干算法和十神映射逻辑`);
      }
      
      if (!failedCase.yongshen_match) {
        console.log(`   - 用神判定错误`);
        console.log(`     建议: 优化三级优先级算法`);
      }
      
      if (failedCase.actual_accuracy < 0.9) {
        console.log(`   - 准确率不足: ${(failedCase.actual_accuracy * 100).toFixed(1)}%`);
        console.log(`     建议: 细化算法参数和阈值设置`);
      }
    });

    // 通用改进建议
    console.log('\n🎯 通用改进建议:');
    if (results.overall_accuracy < 0.95) {
      console.log('   1. 优化格局判定的清浊评估算法');
      console.log('   2. 完善特殊格局的识别逻辑');
      console.log('   3. 加强用神优先级的动态调整');
    }
    
    if (failedCases.length > 1) {
      console.log('   4. 增加更多历史案例进行算法训练');
      console.log('   5. 考虑引入机器学习方法优化参数');
    }
  }
}

/**
 * 主测试函数
 */
async function runHistoricalValidationTests() {
  const testSuite = new HistoricalValidationTest();
  
  try {
    // 运行完整验证
    const results = await testSuite.runCompleteValidation();
    
    // 生成改进建议
    testSuite.generateImprovementSuggestions(results);
    
    console.log('\n🎉 历史案例库验证测试完成');
    return results;

  } catch (error) {
    console.error('❌ 历史案例验证测试失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runHistoricalValidationTests().catch(console.error);
}

module.exports = { HistoricalValidationTest, runHistoricalValidationTests };
