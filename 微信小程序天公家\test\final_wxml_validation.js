/**
 * 最终WXML验证测试
 * 验证修复后的WXML文件是否正确
 */

const fs = require('fs');
const path = require('path');

function validateWXML() {
  console.log('🔍 最终WXML验证测试');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📄 文件总行数: ${lines.length}`);
  
  // 检查关键结构
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`📍 scroll-view 开始: 第${scrollViewStart + 1}行`);
  console.log(`📍 scroll-view 结束: 第${scrollViewEnd + 1}行`);
  
  // 检查timing页面
  const timingStart = lines.findIndex(line => line.includes('wx:elif') && line.includes('timing'));
  const liuqinStart = lines.findIndex(line => line.includes('wx:elif') && line.includes('liuqin'));
  
  console.log(`📍 timing页面开始: 第${timingStart + 1}行`);
  console.log(`📍 liuqin页面开始: 第${liuqinStart + 1}行`);
  
  // 验证timing页面的view标签匹配
  let timingDepth = 0;
  for (let i = timingStart; i < liuqinStart; i++) {
    const line = lines[i];
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    timingDepth += openViews - closeViews;
  }
  
  console.log(`\n📊 验证结果:`);
  console.log(`✅ timing页面view标签深度: ${timingDepth} ${timingDepth === 0 ? '(正确)' : '(错误)'}`);
  
  // 检查第2660行附近（原来的错误位置）
  const originalErrorLine = 2660;
  if (originalErrorLine <= lines.length) {
    console.log(`📍 原错误行(${originalErrorLine}): ${lines[originalErrorLine - 1]?.trim() || '(空行)'}`);
  } else {
    console.log(`📍 原错误行(${originalErrorLine}): 已超出文件范围，说明多余的行已被删除`);
  }
  
  // 检查文件结尾
  console.log(`\n📄 文件结尾几行:`);
  for (let i = Math.max(0, lines.length - 5); i < lines.length; i++) {
    console.log(`${i + 1}: ${lines[i]}`);
  }
  
  // 总体验证
  const hasScrollView = scrollViewStart >= 0 && scrollViewEnd >= 0;
  const timingFixed = timingDepth === 0;
  const fileReduced = lines.length < 2667; // 原来的文件长度
  
  console.log(`\n🎯 总体验证:`);
  console.log(`${hasScrollView ? '✅' : '❌'} scroll-view 结构完整`);
  console.log(`${timingFixed ? '✅' : '❌'} timing页面标签匹配`);
  console.log(`${fileReduced ? '✅' : '❌'} 多余标签已删除`);
  
  if (hasScrollView && timingFixed && fileReduced) {
    console.log(`\n🎉 WXML修复成功！所有关键问题已解决。`);
    return true;
  } else {
    console.log(`\n❌ 仍有问题需要解决。`);
    return false;
  }
}

// 运行验证
if (require.main === module) {
  validateWXML();
}

module.exports = { validateWXML };
