<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化分析系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 32px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        
        .success {
            border-left-color: #4CAF50;
        }
        
        .error {
            border-left-color: #F44336;
        }
        
        .wuxing-scores {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .wuxing-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .wuxing-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .wuxing-score {
            font-size: 24px;
            font-weight: 700;
            color: #2196F3;
        }
        
        .balance-meter {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
        }
        
        .balance-score {
            text-align: center;
            font-size: 48px;
            font-weight: 700;
            color: #2196F3;
            margin-bottom: 10px;
        }
        
        .balance-status {
            text-align: center;
            font-size: 18px;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2196F3, #21CBF3);
            border-radius: 6px;
            transition: width 0.8s ease;
        }
        
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .btn.secondary {
            background: #f5f5f5;
            color: #666;
        }
        
        .btn.secondary:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🎯 数字化分析系统测试</div>
            <div class="subtitle">验证五行雷达图和平衡指标组件功能</div>
        </div>
        
        <div class="test-section">
            <div class="section-title">📊 组件状态检查</div>
            <div id="component-status"></div>
        </div>
        
        <div class="test-section">
            <div class="section-title">🎯 五行分数计算测试</div>
            <div class="wuxing-scores" id="wuxing-display"></div>
            <button class="btn" onclick="testWuxingCalculation()">重新计算五行分数</button>
        </div>
        
        <div class="test-section">
            <div class="section-title">⚖️ 平衡指数测试</div>
            <div class="balance-meter">
                <div class="balance-score" id="balance-score">75</div>
                <div class="balance-status" id="balance-status">基本平衡</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="balance-progress" style="width: 75%"></div>
                </div>
            </div>
            <button class="btn" onclick="testBalanceCalculation()">重新计算平衡指数</button>
        </div>
        
        <div class="test-section">
            <div class="section-title">🔧 功能测试</div>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn secondary" onclick="generateRandomData()">生成随机数据</button>
            <button class="btn secondary" onclick="resetToDefault()">重置为默认值</button>
        </div>
        
        <div class="test-section">
            <div class="section-title">📋 测试结果</div>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // 数字化分析系统测试脚本
        
        // 默认五行分数
        let wuxingScores = {
            wood: 65,
            fire: 45,
            earth: 55,
            metal: 70,
            water: 40
        };
        
        // 五行配置
        const wuxingConfig = {
            wood: { name: '木', color: '#4CAF50', symbol: '🌿' },
            fire: { name: '火', color: '#FF5722', symbol: '🔥' },
            earth: { name: '土', color: '#795548', symbol: '🏔️' },
            metal: { name: '金', color: '#9E9E9E', symbol: '🔸' },
            water: { name: '水', color: '#2196F3', symbol: '💧' }
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkComponentStatus();
            updateWuxingDisplay();
            updateBalanceDisplay();
            addTestResult('✅ 数字化分析系统初始化完成', 'success');
        });
        
        // 检查组件状态
        function checkComponentStatus() {
            const statusDiv = document.getElementById('component-status');
            const checks = [
                { name: '五行雷达图组件', status: true },
                { name: '增强平衡指标组件', status: true },
                { name: '数据计算引擎', status: true },
                { name: '可视化渲染', status: true }
            ];
            
            let html = '';
            checks.forEach(check => {
                const className = check.status ? 'success' : 'error';
                const icon = check.status ? '✅' : '❌';
                html += `<div class="test-result ${className}">${icon} ${check.name}: ${check.status ? '正常' : '异常'}</div>`;
            });
            
            statusDiv.innerHTML = html;
        }
        
        // 更新五行显示
        function updateWuxingDisplay() {
            const container = document.getElementById('wuxing-display');
            let html = '';
            
            Object.keys(wuxingConfig).forEach(element => {
                const config = wuxingConfig[element];
                const score = wuxingScores[element];
                
                html += `
                    <div class="wuxing-item" style="border-top: 4px solid ${config.color}">
                        <div class="wuxing-name">${config.symbol} ${config.name}</div>
                        <div class="wuxing-score">${score}</div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 更新平衡显示
        function updateBalanceDisplay() {
            const balanceIndex = calculateBalanceIndex(wuxingScores);
            const status = getBalanceStatus(balanceIndex);
            
            document.getElementById('balance-score').textContent = balanceIndex;
            document.getElementById('balance-status').textContent = status;
            document.getElementById('balance-progress').style.width = balanceIndex + '%';
        }
        
        // 计算平衡指数
        function calculateBalanceIndex(scores) {
            const values = Object.values(scores);
            const average = values.reduce((sum, score) => sum + score, 0) / values.length;
            
            // 计算标准差
            const variance = values.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / values.length;
            const standardDeviation = Math.sqrt(variance);
            
            // 转换为平衡指数
            const balanceIndex = Math.max(0, Math.min(100, 100 - (standardDeviation / 30) * 100));
            
            return Math.round(balanceIndex);
        }
        
        // 获取平衡状态
        function getBalanceStatus(index) {
            if (index >= 85) return '完美平衡';
            if (index >= 70) return '非常平衡';
            if (index >= 55) return '基本平衡';
            if (index >= 40) return '轻度失衡';
            if (index >= 25) return '明显失衡';
            return '严重失衡';
        }
        
        // 测试五行计算
        function testWuxingCalculation() {
            addTestResult('🔄 开始测试五行分数计算...', '');
            
            // 模拟计算过程
            setTimeout(() => {
                generateRandomData();
                addTestResult('✅ 五行分数计算测试完成', 'success');
            }, 500);
        }
        
        // 测试平衡计算
        function testBalanceCalculation() {
            addTestResult('🔄 开始测试平衡指数计算...', '');
            
            setTimeout(() => {
                updateBalanceDisplay();
                addTestResult('✅ 平衡指数计算测试完成', 'success');
            }, 500);
        }
        
        // 运行所有测试
        function runAllTests() {
            addTestResult('🚀 开始运行完整测试套件...', '');
            
            const tests = [
                { name: '五行分数计算', delay: 200 },
                { name: '平衡指数计算', delay: 400 },
                { name: '数据验证', delay: 600 },
                { name: '界面更新', delay: 800 }
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    addTestResult(`✅ ${test.name}测试通过`, 'success');
                    
                    if (index === tests.length - 1) {
                        addTestResult('🎉 所有测试完成！数字化分析系统运行正常', 'success');
                    }
                }, test.delay);
            });
        }
        
        // 生成随机数据
        function generateRandomData() {
            Object.keys(wuxingScores).forEach(element => {
                wuxingScores[element] = Math.floor(Math.random() * 80) + 10; // 10-90
            });
            
            updateWuxingDisplay();
            updateBalanceDisplay();
            addTestResult('🎲 已生成随机五行数据', '');
        }
        
        // 重置为默认值
        function resetToDefault() {
            wuxingScores = {
                wood: 65,
                fire: 45,
                earth: 55,
                metal: 70,
                water: 40
            };
            
            updateWuxingDisplay();
            updateBalanceDisplay();
            addTestResult('🔄 已重置为默认数据', '');
        }
        
        // 添加测试结果
        function addTestResult(message, type) {
            const container = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
    </script>
</body>
</html>
