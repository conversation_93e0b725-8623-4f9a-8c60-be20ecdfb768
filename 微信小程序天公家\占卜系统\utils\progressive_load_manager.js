// utils/progressive_load_manager.js
// 渐进式加载管理器 - 管理资源的渐进式加载

class ProgressiveLoadManager {
  constructor() {
    this.loadQueue = [];
    this.loadedResources = new Map();
    this.loadingResources = new Set();
    this.loadStrategies = new Map();
    this.initialized = false;
    this.maxConcurrent = 3;
  }

  // 初始化管理器
  init(config = {}) {
    try {
      this.maxConcurrent = config.maxConcurrent || 3;
      this.initialized = true;
      
      // 注册默认加载策略
      this.registerStrategy('immediate', {
        name: '立即加载',
        priority: 100,
        condition: () => true,
        loader: this.immediateLoad.bind(this)
      });
      
      this.registerStrategy('lazy', {
        name: '懒加载',
        priority: 50,
        condition: (resource) => resource.lazy === true,
        loader: this.lazyLoad.bind(this)
      });
      
      this.registerStrategy('preload', {
        name: '预加载',
        priority: 75,
        condition: (resource) => resource.preload === true,
        loader: this.preload.bind(this)
      });
      
      console.log('✅ ProgressiveLoadManager 初始化成功');
      return true;
    } catch (error) {
      console.error('❌ ProgressiveLoadManager 初始化失败:', error);
      return false;
    }
  }

  // 注册加载策略
  registerStrategy(name, strategy) {
    if (!this.initialized) {
      console.warn('⚠️ ProgressiveLoadManager 未初始化');
      return false;
    }

    try {
      this.loadStrategies.set(name, {
        name: strategy.name || name,
        priority: strategy.priority || 0,
        condition: strategy.condition || (() => true),
        loader: strategy.loader,
        metadata: strategy.metadata || {}
      });
      
      console.log(`✅ 加载策略 ${name} 注册成功`);
      return true;
    } catch (error) {
      console.error(`❌ 加载策略 ${name} 注册失败:`, error);
      return false;
    }
  }

  // 添加资源到加载队列
  addResource(resourceId, config) {
    if (!this.initialized) {
      console.warn('⚠️ ProgressiveLoadManager 未初始化');
      return false;
    }

    try {
      const resource = {
        id: resourceId,
        url: config.url,
        type: config.type || 'data',
        priority: config.priority || 0,
        lazy: config.lazy || false,
        preload: config.preload || false,
        dependencies: config.dependencies || [],
        metadata: config.metadata || {},
        addedAt: Date.now()
      };

      // 检查是否已存在
      if (this.loadedResources.has(resourceId) || this.loadingResources.has(resourceId)) {
        console.log(`⚠️ 资源 ${resourceId} 已存在或正在加载`);
        return true;
      }

      this.loadQueue.push(resource);
      
      // 按优先级排序
      this.loadQueue.sort((a, b) => b.priority - a.priority);
      
      console.log(`✅ 资源 ${resourceId} 添加到加载队列`);
      
      // 尝试开始加载
      this.processQueue();
      
      return true;
    } catch (error) {
      console.error(`❌ 资源 ${resourceId} 添加失败:`, error);
      return false;
    }
  }

  // 处理加载队列
  async processQueue() {
    while (this.loadQueue.length > 0 && this.loadingResources.size < this.maxConcurrent) {
      const resource = this.loadQueue.shift();
      
      // 检查依赖
      if (!this.checkDependencies(resource)) {
        // 依赖未满足，重新加入队列末尾
        this.loadQueue.push(resource);
        continue;
      }

      // 选择加载策略
      const strategy = this.selectStrategy(resource);
      if (strategy) {
        this.loadResource(resource, strategy);
      }
    }
  }

  // 检查依赖
  checkDependencies(resource) {
    for (const depId of resource.dependencies) {
      if (!this.loadedResources.has(depId)) {
        return false;
      }
    }
    return true;
  }

  // 选择加载策略
  selectStrategy(resource) {
    const applicableStrategies = Array.from(this.loadStrategies.values())
      .filter(strategy => strategy.condition(resource))
      .sort((a, b) => b.priority - a.priority);

    return applicableStrategies.length > 0 ? applicableStrategies[0] : null;
  }

  // 加载资源
  async loadResource(resource, strategy) {
    this.loadingResources.add(resource.id);
    
    try {
      console.log(`🔄 开始加载资源 ${resource.id} (策略: ${strategy.name})`);
      
      const result = await strategy.loader(resource);
      
      this.loadedResources.set(resource.id, {
        ...resource,
        data: result,
        loadedAt: Date.now(),
        strategy: strategy.name
      });
      
      this.loadingResources.delete(resource.id);
      
      console.log(`✅ 资源 ${resource.id} 加载完成`);
      
      // 继续处理队列
      this.processQueue();
      
      return result;
    } catch (error) {
      this.loadingResources.delete(resource.id);
      console.error(`❌ 资源 ${resource.id} 加载失败:`, error);
      
      // 继续处理队列
      this.processQueue();
      
      throw error;
    }
  }

  // 立即加载策略
  async immediateLoad(resource) {
    if (resource.type === 'data') {
      // 模拟数据加载
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ type: 'data', content: `Data for ${resource.id}` });
        }, 100);
      });
    } else if (resource.type === 'image') {
      // 模拟图片加载
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = resource.url;
      });
    }
    
    return null;
  }

  // 懒加载策略
  async lazyLoad(resource) {
    // 懒加载：延迟一段时间后加载
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.immediateLoad(resource);
  }

  // 预加载策略
  async preload(resource) {
    // 预加载：低优先级后台加载
    return new Promise((resolve) => {
      setTimeout(() => {
        this.immediateLoad(resource).then(resolve);
      }, 200);
    });
  }

  // 获取资源
  getResource(resourceId) {
    return this.loadedResources.get(resourceId);
  }

  // 检查资源是否已加载
  isLoaded(resourceId) {
    return this.loadedResources.has(resourceId);
  }

  // 检查资源是否正在加载
  isLoading(resourceId) {
    return this.loadingResources.has(resourceId);
  }

  // 等待资源加载完成
  async waitForResource(resourceId, timeout = 10000) {
    const startTime = Date.now();
    
    while (!this.isLoaded(resourceId) && Date.now() - startTime < timeout) {
      if (!this.isLoading(resourceId) && !this.loadQueue.find(r => r.id === resourceId)) {
        throw new Error(`Resource ${resourceId} not found in queue`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (!this.isLoaded(resourceId)) {
      throw new Error(`Resource ${resourceId} load timeout`);
    }
    
    return this.getResource(resourceId);
  }

  // 获取加载状态
  getLoadStatus() {
    return {
      queueLength: this.loadQueue.length,
      loadingCount: this.loadingResources.size,
      loadedCount: this.loadedResources.size,
      maxConcurrent: this.maxConcurrent
    };
  }

  // 获取状态
  getStatus() {
    return {
      initialized: this.initialized,
      ...this.getLoadStatus(),
      strategyCount: this.loadStrategies.size
    };
  }

  // 清理管理器
  cleanup() {
    this.loadQueue = [];
    this.loadedResources.clear();
    this.loadingResources.clear();
    this.loadStrategies.clear();
    this.initialized = false;
    console.log('✅ ProgressiveLoadManager 清理完成');
  }
}

// 创建单例实例
const progressiveLoadManager = new ProgressiveLoadManager();

module.exports = {
  ProgressiveLoadManager,
  default: progressiveLoadManager,
  manager: progressiveLoadManager
};
