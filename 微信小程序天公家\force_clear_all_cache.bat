@echo off
echo 🔧 强制清理所有缓存和重启微信开发者工具...

echo.
echo 1. 强制关闭所有微信开发者工具进程...
taskkill /f /im "微信开发者工具.exe" 2>nul
taskkill /f /im "wechatdevtools.exe" 2>nul
taskkill /f /im "WeChat Developer Tools.exe" 2>nul
taskkill /f /im "wechat-devtools.exe" 2>nul
timeout /t 5 >nul

echo.
echo 2. 清理项目缓存...
if exist ".wxcache" (
    rmdir /s /q ".wxcache"
    echo    ✅ 删除 .wxcache 文件夹
)

if exist "node_modules" (
    rmdir /s /q "node_modules"
    echo    ✅ 删除 node_modules 文件夹
)

echo.
echo 3. 清理微信开发者工具缓存...
set APPDATA_DIR=%APPDATA%
set LOCALAPPDATA_DIR=%LOCALAPPDATA%

rmdir /s /q "%APPDATA_DIR%\微信开发者工具" 2>nul
rmdir /s /q "%APPDATA_DIR%\wechatdevtools" 2>nul
rmdir /s /q "%LOCALAPPDATA_DIR%\微信开发者工具" 2>nul
rmdir /s /q "%LOCALAPPDATA_DIR%\wechatdevtools" 2>nul
echo    ✅ 清理用户缓存完成

echo.
echo 4. 清理系统临时文件...
del /q /s "%TEMP%\wx*" 2>nul
del /q /s "%TEMP%\wechat*" 2>nul
del /q /s "%TEMP%\*.tmp" 2>nul
echo    ✅ 清理临时文件完成

echo.
echo 5. 清理Windows文件缓存...
del /q /s "%WINDIR%\Temp\*" 2>nul
echo    ✅ 清理Windows临时文件完成

echo.
echo 🎯 所有缓存清理完成！
echo.
echo 📋 接下来请手动执行：
echo    1. 重启电脑（推荐）
echo    2. 或者重新启动微信开发者工具
echo    3. 重新打开项目
echo    4. 在工具中：工具 → 清缓存 → 全部清除
echo    5. 重新编译项目
echo.
echo ⚠️ 如果问题仍然存在，请检查：
echo    - 模块文件是否存在于 utils 目录
echo    - 文件路径是否正确
echo    - 是否有语法错误
echo.
pause
