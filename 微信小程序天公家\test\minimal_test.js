// test/minimal_test.js
// 最小测试

console.log('🔧 开始最小测试...');

// 尝试创建一个简化的类来测试语法
class TestClass {
  method1() {
    return {
      data: 'test'
    };
  }

  method2(param) {
    try {
      return { result: param };
    } catch (error) {
      return { error: error.message };
    }
  }

  method3() {
    return 'test';
  }
}

console.log('✅ 类语法正确');

// 测试实例化
const instance = new TestClass();
console.log('✅ 实例化成功');

// 测试方法调用
const result1 = instance.method1();
const result2 = instance.method2('test');
const result3 = instance.method3();

console.log('✅ 方法调用成功');
console.log('结果1:', result1);
console.log('结果2:', result2);
console.log('结果3:', result3);

console.log('🎉 最小测试完成！');
