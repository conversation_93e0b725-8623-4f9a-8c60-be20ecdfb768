// 深度调试前端月柱计算问题
// 检查是否有其他隐藏的计算逻辑

console.log('🔍 深度调试前端月柱计算');

const tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"];
const dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"];

// 测试日期
const year = 1953;
const month = 6;
const day = 15;

console.log(`\n📅 测试日期: ${year}年${month}月${day}日`);

// 1. 检查年柱计算是否正确
console.log(`\n🗓️ 年柱计算验证:`);
const yearGanIndex = (year - 4) % 10;
const yearZhiIndex = (year - 4) % 12;
const yearPillar = {
  gan: tiangan[yearGanIndex],
  zhi: dizhi[yearZhiIndex]
};

console.log(`年干索引: (${year} - 4) % 10 = ${yearGanIndex}`);
console.log(`年支索引: (${year} - 4) % 12 = ${yearZhiIndex}`);
console.log(`年柱: ${yearPillar.gan}${yearPillar.zhi}`);

// 2. 检查可能的错误：是否使用了错误的五虎遁起始
console.log(`\n🐅 五虎遁起始检查:`);

// 正确的五虎遁
const correctWuhuDun = {
  '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
  '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
  '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
  '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
  '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
};

// 可能的错误五虎遁（如果有人用了错误的起始）
const wrongWuhuDun1 = {
  '甲': 0, '己': 0, // 错误：甲己之年甲作首
  '乙': 2, '庚': 2, // 错误：乙庚之年丙为头
  '丙': 4, '辛': 4, // 错误：丙辛之年戊寅上
  '丁': 6, '壬': 6, // 错误：丁壬庚寅顺水流
  '戊': 8, '癸': 8  // 错误：戊癸之年壬寅始
};

const wrongWuhuDun2 = {
  '甲': 6, '己': 6, // 错误：可能用了庚的起始
  '乙': 8, '庚': 8, // 错误：可能用了壬的起始
  '丙': 0, '辛': 0, // 错误：可能用了甲的起始
  '丁': 2, '壬': 2, // 错误：可能用了丙的起始
  '戊': 4, '癸': 4  // 错误：可能用了戊的起始
};

const solarMonth = 5; // 午月

function testWuhuDun(wuhuDunMap, name) {
  const monthGanStart = wuhuDunMap[yearPillar.gan];
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  const monthZhi = '午';
  
  console.log(`${name}:`);
  console.log(`  癸年起始: ${tiangan[monthGanStart]} (索引${monthGanStart})`);
  console.log(`  月干计算: (${monthGanStart} + ${solarMonth} - 1) % 10 = ${monthGanIndex}`);
  console.log(`  结果: ${monthGan}${monthZhi}`);
  
  if (monthGan === '庚') {
    console.log(`  ⚠️ 这个算法会得到庚午！`);
  }
  
  return monthGan + monthZhi;
}

console.log(`\n测试不同的五虎遁算法:`);
testWuhuDun(correctWuhuDun, '正确五虎遁');
testWuhuDun(wrongWuhuDun1, '错误五虎遁1');
testWuhuDun(wrongWuhuDun2, '错误五虎遁2');

// 3. 检查是否有数组索引错误
console.log(`\n🔢 数组索引检查:`);

// 检查是否有人用了错误的月份序号
const testMonths = [4, 5, 6]; // 可能的午月序号

testMonths.forEach(testMonth => {
  const monthGanStart = correctWuhuDun[yearPillar.gan];
  const monthGanIndex = (monthGanStart + testMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  
  console.log(`如果午月序号是${testMonth}: ${monthGan}午`);
  
  if (monthGan === '庚') {
    console.log(`  ⚠️ 序号${testMonth}会得到庚午！`);
  }
});

// 4. 检查是否有年干计算错误
console.log(`\n🗓️ 年干计算错误检查:`);

const possibleYearGans = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

possibleYearGans.forEach(testYearGan => {
  const monthGanStart = correctWuhuDun[testYearGan];
  const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;
  const monthGan = tiangan[monthGanIndex];
  
  if (monthGan === '庚') {
    console.log(`如果年干是${testYearGan}: ${monthGan}午 ⚠️ 会得到庚午！`);
  }
});

// 5. 反推：如果结果是庚午，那么可能的原因
console.log(`\n🔍 反推分析：如果结果是庚午，可能的原因:`);

// 庚的索引是6
const targetGanIndex = 6; // 庚

// 反推可能的起始点
for (let startIndex = 0; startIndex < 10; startIndex++) {
  for (let monthIndex = 1; monthIndex <= 12; monthIndex++) {
    if ((startIndex + monthIndex - 1) % 10 === targetGanIndex) {
      console.log(`起始${tiangan[startIndex]}(${startIndex}) + 月序号${monthIndex} = 庚(${targetGanIndex})`);
    }
  }
}

// 6. 检查是否有其他计算方式
console.log(`\n🔧 可能的错误计算方式:`);

// 错误方式1：直接用月份数字
const wrongMethod1 = (correctWuhuDun[yearPillar.gan] + month - 1) % 10;
console.log(`错误方式1 (直接用月份${month}): ${tiangan[wrongMethod1]}午`);

// 错误方式2：用错误的基准年
const wrongYearGanIndex = (year - 1984) % 10; // 有些算法用1984年为基准
const wrongYearGan = tiangan[wrongYearGanIndex];
const wrongMethod2 = (correctWuhuDun[wrongYearGan] + solarMonth - 1) % 10;
console.log(`错误方式2 (用1984年基准): 年干${wrongYearGan}, 月干${tiangan[wrongMethod2]}午`);

// 错误方式3：用错误的月份映射
const wrongSolarMonth = 6; // 如果错误地认为6月15日是未月
const wrongMethod3 = (correctWuhuDun[yearPillar.gan] + wrongSolarMonth - 1) % 10;
console.log(`错误方式3 (错误月份映射${wrongSolarMonth}): ${tiangan[wrongMethod3]}午`);

console.log(`\n🎯 结论:`);
console.log(`如果前端显示庚午，最可能的原因是:`);
console.log(`1. 使用了错误的五虎遁起始点`);
console.log(`2. 使用了错误的月份序号`);
console.log(`3. 使用了错误的年干计算`);
console.log(`4. 使用了错误的基准年份`);

// 7. 找出最可能的错误
if (tiangan[wrongMethod1] === '庚') {
  console.log(`❌ 最可能的错误：直接使用阳历月份${month}而不是节气月份${solarMonth}`);
}

if (tiangan[wrongMethod2] === '庞') {
  console.log(`❌ 最可能的错误：使用了1984年基准计算年干`);
}

if (tiangan[wrongMethod3] === '庚') {
  console.log(`❌ 最可能的错误：错误地将6月15日映射为未月(${wrongSolarMonth})而不是午月(${solarMonth})`);
}
