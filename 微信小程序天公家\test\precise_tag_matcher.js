/**
 * 精确标签匹配器
 * 找到确切的不匹配标签位置
 */

const fs = require('fs');
const path = require('path');

function findExactMismatches() {
  console.log('🔍 精确查找标签不匹配位置');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到scroll-view的范围
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`📍 分析范围: 第${scrollViewStart + 1}行 - 第${scrollViewEnd + 1}行`);
  
  // 逐行分析，记录每个标签的详细信息
  const tagStack = [];
  const mismatches = [];
  
  for (let i = scrollViewStart + 1; i < scrollViewEnd; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 更精确的标签匹配
    const tagRegex = /<(\/?)([\w-]+)(?:\s[^>]*)?>/g;
    let match;
    
    while ((match = tagRegex.exec(line)) !== null) {
      const isClosing = match[1] === '/';
      const tagName = match[2];
      const fullTag = match[0];
      
      // 跳过自闭合标签
      if (fullTag.endsWith('/>')) {
        continue;
      }
      
      if (isClosing) {
        // 结束标签
        if (tagStack.length > 0) {
          const lastTag = tagStack[tagStack.length - 1];
          if (lastTag.name === tagName) {
            // 匹配，移除栈顶
            tagStack.pop();
          } else {
            // 不匹配
            mismatches.push({
              line: lineNum,
              type: 'mismatch',
              expected: lastTag.name,
              found: tagName,
              expectedLine: lastTag.line,
              content: line.trim()
            });
            // 尝试在栈中找到匹配的标签
            let found = false;
            for (let j = tagStack.length - 1; j >= 0; j--) {
              if (tagStack[j].name === tagName) {
                // 找到匹配，移除这个标签及其之后的所有标签
                tagStack.splice(j);
                found = true;
                break;
              }
            }
            if (!found) {
              // 完全没有匹配的开始标签
              mismatches.push({
                line: lineNum,
                type: 'orphan_close',
                found: tagName,
                content: line.trim()
              });
            }
          }
        } else {
          // 栈为空，但有结束标签
          mismatches.push({
            line: lineNum,
            type: 'orphan_close',
            found: tagName,
            content: line.trim()
          });
        }
      } else {
        // 开始标签
        tagStack.push({
          name: tagName,
          line: lineNum,
          content: line.trim()
        });
      }
    }
  }
  
  // 报告结果
  console.log(`\n📊 分析结果:`);
  console.log(`未关闭的标签: ${tagStack.length}`);
  console.log(`不匹配问题: ${mismatches.length}`);
  
  if (tagStack.length > 0) {
    console.log(`\n❌ 未关闭的标签:`);
    tagStack.forEach(tag => {
      console.log(`  第${tag.line}行: <${tag.name}> - ${tag.content}`);
    });
  }
  
  if (mismatches.length > 0) {
    console.log(`\n❌ 标签不匹配问题:`);
    mismatches.forEach(issue => {
      if (issue.type === 'mismatch') {
        console.log(`  第${issue.line}行: 期望 </${issue.expected}> (第${issue.expectedLine}行) 但找到 </${issue.found}>`);
        console.log(`    内容: ${issue.content}`);
      } else if (issue.type === 'orphan_close') {
        console.log(`  第${issue.line}行: 多余的结束标签 </${issue.found}>`);
        console.log(`    内容: ${issue.content}`);
      }
    });
  }
  
  // 给出具体的修复建议
  console.log(`\n🔧 修复建议:`);
  
  if (mismatches.length > 0) {
    console.log(`需要删除以下多余的结束标签:`);
    mismatches.forEach(issue => {
      if (issue.type === 'orphan_close') {
        console.log(`  第${issue.line}行: </${issue.found}>`);
      }
    });
  }
  
  if (tagStack.length > 0) {
    console.log(`需要在scroll-view结束前添加以下结束标签:`);
    for (let i = tagStack.length - 1; i >= 0; i--) {
      console.log(`  </${tagStack[i].name}> (对应第${tagStack[i].line}行)`);
    }
  }
  
  return { mismatches, unclosedTags: tagStack };
}

// 运行分析
if (require.main === module) {
  findExactMismatches();
}

module.exports = { findExactMismatches };
