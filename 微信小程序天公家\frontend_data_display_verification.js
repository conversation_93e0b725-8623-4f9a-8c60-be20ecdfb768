/**
 * 前端专业级五行分析数据显示深度验证
 * 验证计算数据是否能正确传递到前端并正确显示
 */

const UnifiedWuxingAPI = require('./utils/unified_wuxing_api.js');

class FrontendDataDisplayVerification {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      details: []
    };
  }

  /**
   * 执行完整的数据显示验证
   */
  async runVerification() {
    console.log('🔍 开始前端数据显示深度验证...\n');

    // 测试样本数据
    const testSamples = [
      {
        name: '测试样本1 - 2021年6月24日19:30',
        fourPillars: [
          { gan: '辛', zhi: '丑' }, // 年柱
          { gan: '甲', zhi: '午' }, // 月柱  
          { gan: '癸', zhi: '卯' }, // 日柱
          { gan: '壬', zhi: '戌' }  // 时柱
        ]
      },
      {
        name: '测试样本2 - 强木局',
        fourPillars: [
          { gan: '甲', zhi: '寅' }, // 年柱
          { gan: '丙', zhi: '寅' }, // 月柱
          { gan: '甲', zhi: '子' }, // 日柱
          { gan: '乙', zhi: '卯' }  // 时柱
        ]
      },
      {
        name: '测试样本3 - 水火冲突',
        fourPillars: [
          { gan: '壬', zhi: '子' }, // 年柱
          { gan: '丁', zhi: '午' }, // 月柱
          { gan: '癸', zhi: '亥' }, // 日柱
          { gan: '丙', zhi: '午' }  // 时柱
        ]
      }
    ];

    for (const sample of testSamples) {
      await this.verifyDataDisplayForSample(sample);
    }

    this.outputVerificationResults();
  }

  /**
   * 验证单个样本的数据显示
   */
  async verifyDataDisplayForSample(sample) {
    console.log(`📊 验证样本: ${sample.name}`);
    
    try {
      // 1. 执行专业级分析
      const api = new UnifiedWuxingAPI();
      const completeAnalysis = await api.performCompleteAnalysis(sample.fourPillars);
      
      // 2. 验证数据结构完整性
      this.verifyDataStructure(sample.name, completeAnalysis);
      
      // 3. 验证前端数据格式
      this.verifyFrontendDataFormat(sample.name, completeAnalysis);
      
      // 4. 验证数据内容合理性
      this.verifyDataContentValidity(sample.name, completeAnalysis);
      
      // 5. 验证千人千面特性
      this.verifyPersonalizationFeatures(sample.name, completeAnalysis);
      
    } catch (error) {
      console.log(`  ❌ 样本分析失败: ${error.message}`);
      this.recordResult(`${sample.name} - 整体分析`, false, { error: error.message });
    }
    
    console.log('');
  }

  /**
   * 验证数据结构完整性
   */
  verifyDataStructure(sampleName, analysis) {
    console.log('  🔍 验证数据结构完整性...');
    
    const checks = [
      { name: '静态分析数据', path: 'static', required: true },
      { name: '动态分析数据', path: 'dynamic', required: true },
      { name: '统一数据结构', path: 'unified', required: true },
      { name: '前端数据', path: 'unified.professionalData.frontendData', required: true },
      { name: '元素对比数据', path: 'unified.professionalData.frontendData.elementComparisons', required: true },
      { name: '交互详情数据', path: 'unified.professionalData.frontendData.interactionDetails', required: true },
      { name: '影响评估数据', path: 'unified.professionalData.impactEvaluation', required: true },
      { name: '个性化建议', path: 'unified.professionalData.frontendData.recommendations', required: true }
    ];

    let passed = 0;
    checks.forEach(check => {
      const value = this.getNestedValue(analysis, check.path);
      const exists = value !== undefined && value !== null;
      
      if (exists) {
        console.log(`    ✅ ${check.name}`);
        passed++;
      } else {
        console.log(`    ❌ ${check.name} - 路径: ${check.path}`);
      }
    });

    this.recordResult(`${sampleName} - 数据结构`, passed === checks.length, {
      passed: passed,
      total: checks.length,
      rate: `${(passed/checks.length*100).toFixed(1)}%`
    });
  }

  /**
   * 验证前端数据格式
   */
  verifyFrontendDataFormat(sampleName, analysis) {
    console.log('  🎨 验证前端数据格式...');
    
    try {
      const frontendData = analysis.unified.professionalData.frontendData;
      const impactEvaluation = analysis.unified.professionalData.impactEvaluation;
      
      const checks = [
        {
          name: '元素对比数组格式',
          test: () => Array.isArray(frontendData.elementComparisons) && frontendData.elementComparisons.length === 5
        },
        {
          name: '交互详情对象格式',
          test: () => frontendData.interactionDetails && typeof frontendData.interactionDetails === 'object'
        },
        {
          name: '影响评估数值格式',
          test: () => typeof impactEvaluation.confidence === 'number' && impactEvaluation.confidence >= 0 && impactEvaluation.confidence <= 100
        },
        {
          name: '建议数组格式',
          test: () => Array.isArray(frontendData.recommendations) && frontendData.recommendations.length > 0
        },
        {
          name: '元素对比数据完整性',
          test: () => frontendData.elementComparisons.every(item => 
            item.element && typeof item.staticPower === 'number' && typeof item.dynamicPower === 'number'
          )
        },
        {
          name: '交互详情分类完整性',
          test: () => frontendData.interactionDetails.all && Array.isArray(frontendData.interactionDetails.all)
        },
        {
          name: '影响评估文本格式',
          test: () => typeof impactEvaluation.overallLevelText === 'string' && impactEvaluation.overallLevelText.length > 0
        },
        {
          name: '建议内容格式',
          test: () => frontendData.recommendations.every(rec => 
            rec.title && rec.content && typeof rec.confidence === 'number'
          )
        }
      ];

      let passed = 0;
      checks.forEach(check => {
        try {
          const result = check.test();
          if (result) {
            console.log(`    ✅ ${check.name}`);
            passed++;
          } else {
            console.log(`    ❌ ${check.name}`);
          }
        } catch (error) {
          console.log(`    ❌ ${check.name} - 错误: ${error.message}`);
        }
      });

      this.recordResult(`${sampleName} - 前端数据格式`, passed === checks.length, {
        passed: passed,
        total: checks.length,
        rate: `${(passed/checks.length*100).toFixed(1)}%`
      });

    } catch (error) {
      console.log(`    ❌ 前端数据格式验证失败: ${error.message}`);
      this.recordResult(`${sampleName} - 前端数据格式`, false, { error: error.message });
    }
  }

  /**
   * 验证数据内容合理性
   */
  verifyDataContentValidity(sampleName, analysis) {
    console.log('  📊 验证数据内容合理性...');
    
    try {
      const frontendData = analysis.unified.professionalData.frontendData;
      const impactEvaluation = analysis.unified.professionalData.impactEvaluation;
      
      const checks = [
        {
          name: '五行力量数值合理性',
          test: () => frontendData.elementComparisons.every(item => 
            item.staticPower >= 0 && item.dynamicPower >= 0 && 
            item.staticPower <= 1000 && item.dynamicPower <= 1000
          )
        },
        {
          name: '力量变化计算正确性',
          test: () => frontendData.elementComparisons.every(item => {
            const expectedChange = item.dynamicPower - item.staticPower;
            return Math.abs(item.change - expectedChange) < 0.01;
          })
        },
        {
          name: '变化等级分类正确性',
          test: () => frontendData.elementComparisons.every(item => 
            ['major-increase', 'minor-increase', 'stable', 'minor-decrease', 'major-decrease'].includes(item.changeLevel)
          )
        },
        {
          name: '交互关系数量合理性',
          test: () => frontendData.interactionDetails.all.length >= 0 && frontendData.interactionDetails.all.length <= 20
        },
        {
          name: '影响评估置信度合理性',
          test: () => impactEvaluation.confidence >= 50 && impactEvaluation.confidence <= 100
        },
        {
          name: '建议数量合理性',
          test: () => frontendData.recommendations.length >= 1 && frontendData.recommendations.length <= 10
        },
        {
          name: '建议置信度合理性',
          test: () => frontendData.recommendations.every(rec => 
            rec.confidence >= 60 && rec.confidence <= 100
          )
        },
        {
          name: '吉凶趋势分类正确性',
          test: () => ['吉', '凶', '平'].includes(impactEvaluation.fortuneTrendText)
        }
      ];

      let passed = 0;
      checks.forEach(check => {
        try {
          const result = check.test();
          if (result) {
            console.log(`    ✅ ${check.name}`);
            passed++;
          } else {
            console.log(`    ❌ ${check.name}`);
          }
        } catch (error) {
          console.log(`    ❌ ${check.name} - 错误: ${error.message}`);
        }
      });

      this.recordResult(`${sampleName} - 数据内容合理性`, passed === checks.length, {
        passed: passed,
        total: checks.length,
        rate: `${(passed/checks.length*100).toFixed(1)}%`
      });

    } catch (error) {
      console.log(`    ❌ 数据内容合理性验证失败: ${error.message}`);
      this.recordResult(`${sampleName} - 数据内容合理性`, false, { error: error.message });
    }
  }

  /**
   * 验证千人千面特性
   */
  verifyPersonalizationFeatures(sampleName, analysis) {
    console.log('  🎯 验证千人千面特性...');
    
    try {
      const frontendData = analysis.unified.professionalData.frontendData;
      const impactEvaluation = analysis.unified.professionalData.impactEvaluation;
      
      // 提取个性化特征
      const personalizedFeatures = {
        elementPattern: frontendData.elementComparisons.map(item => `${item.element}:${item.changeLevel}`).join(','),
        interactionPattern: frontendData.interactionDetails.all.map(item => item.type).join(','),
        impactLevel: impactEvaluation.overallLevelText,
        fortuneTrend: impactEvaluation.fortuneTrendText,
        recommendationCount: frontendData.recommendations.length,
        primaryRecommendation: frontendData.recommendations[0]?.title || 'none'
      };

      console.log(`    📋 个性化特征摘要:`);
      console.log(`      - 元素变化模式: ${personalizedFeatures.elementPattern}`);
      console.log(`      - 交互关系模式: ${personalizedFeatures.interactionPattern || '无'}`);
      console.log(`      - 整体影响等级: ${personalizedFeatures.impactLevel}`);
      console.log(`      - 吉凶趋势: ${personalizedFeatures.fortuneTrend}`);
      console.log(`      - 建议数量: ${personalizedFeatures.recommendationCount}`);
      console.log(`      - 主要建议: ${personalizedFeatures.primaryRecommendation}`);

      // 验证个性化程度
      const uniqueElements = new Set(frontendData.elementComparisons.map(item => item.changeLevel)).size;
      const hasInteractions = frontendData.interactionDetails.all.length > 0;
      const hasVariedRecommendations = frontendData.recommendations.length > 1;
      
      const personalizationScore = (
        (uniqueElements > 1 ? 25 : 0) +
        (hasInteractions ? 25 : 0) +
        (hasVariedRecommendations ? 25 : 0) +
        (impactEvaluation.confidence > 80 ? 25 : 0)
      );

      console.log(`    🎯 个性化程度评分: ${personalizationScore}/100`);

      this.recordResult(`${sampleName} - 千人千面特性`, personalizationScore >= 75, {
        score: personalizationScore,
        features: personalizedFeatures,
        uniqueElements: uniqueElements,
        hasInteractions: hasInteractions,
        hasVariedRecommendations: hasVariedRecommendations
      });

    } catch (error) {
      console.log(`    ❌ 千人千面特性验证失败: ${error.message}`);
      this.recordResult(`${sampleName} - 千人千面特性`, false, { error: error.message });
    }
  }

  /**
   * 获取嵌套对象值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * 记录验证结果
   */
  recordResult(testName, success, details) {
    this.testResults.total++;
    if (success) {
      this.testResults.passed++;
    } else {
      this.testResults.failed++;
    }
    
    this.testResults.details.push({
      name: testName,
      success: success,
      details: details
    });
  }

  /**
   * 输出验证结果
   */
  outputVerificationResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 前端数据显示验证结果汇总');
    console.log('='.repeat(60));

    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    console.log(`\n🎯 总体成功率: ${successRate}% (${this.testResults.passed}/${this.testResults.total})`);
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);

    console.log('\n📊 详细结果:');
    this.testResults.details.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}`);
      
      if (result.details && typeof result.details === 'object') {
        Object.entries(result.details).forEach(([key, value]) => {
          if (typeof value === 'object') {
            console.log(`   - ${key}: ${JSON.stringify(value)}`);
          } else {
            console.log(`   - ${key}: ${value}`);
          }
        });
      }
    });

    console.log('\n🏆 数据显示状态评估:');
    if (successRate >= 95) {
      console.log('🟢 优秀 - 前端数据显示完全正确，可以立即使用');
    } else if (successRate >= 85) {
      console.log('🟡 良好 - 前端数据显示基本正确，建议修复少量问题');
    } else if (successRate >= 70) {
      console.log('🟠 一般 - 前端数据显示需要进一步完善');
    } else {
      console.log('🔴 需要改进 - 前端数据显示存在重大问题');
    }
  }
}

// 执行验证
async function runVerification() {
  const verifier = new FrontendDataDisplayVerification();
  await verifier.runVerification();
}

// 如果直接运行此文件
if (require.main === module) {
  runVerification().catch(console.error);
}

module.exports = FrontendDataDisplayVerification;
