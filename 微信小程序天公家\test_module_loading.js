// test_module_loading.js
// 测试模块加载是否正常

console.log('🧪 开始测试模块加载...');

const modules = [
  'utils/isolated_integration_manager.js',
  'utils/layered_rules_manager.js', 
  'utils/version_switch_manager.js',
  'utils/progressive_load_manager.js'
];

let allSuccess = true;

modules.forEach(modulePath => {
  try {
    console.log(`🔄 测试加载: ${modulePath}`);
    const Module = require(`./${modulePath}`);
    
    // 尝试创建实例
    const instance = new Module();
    
    // 测试基本方法
    if (typeof instance.init === 'function') {
      const initResult = instance.init({});
      console.log(`✅ ${modulePath} 加载成功，初始化: ${initResult}`);
    } else {
      console.log(`✅ ${modulePath} 加载成功`);
    }
    
  } catch (error) {
    console.error(`❌ ${modulePath} 加载失败:`, error.message);
    allSuccess = false;
  }
});

console.log('\n' + '='.repeat(50));
if (allSuccess) {
  console.log('🎉 所有模块加载测试通过！');
  console.log('✅ 模块加载问题已解决');
} else {
  console.log('⚠️ 部分模块加载失败，需要进一步检查');
}
console.log('='.repeat(50));
