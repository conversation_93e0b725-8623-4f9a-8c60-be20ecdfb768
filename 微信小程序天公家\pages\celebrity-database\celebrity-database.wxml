<!--历史名人数据库页面-->
<view class="celebrity-database-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">📚 历史名人命理数据库</text>
    <text class="page-subtitle">权威验证 · 古籍依据 · 专家认证</text>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索名人姓名、朝代、职业..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
      <button class="search-btn" bindtap="onSearch">🔍</button>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tags">
      <scroll-view class="filter-scroll" scroll-x="true">
        <view class="filter-tag {{currentFilter === 'all' ? 'active' : ''}}" 
              data-filter="all" bindtap="onFilterChange">全部</view>
        <view class="filter-tag {{currentFilter === 'dynasty' ? 'active' : ''}}" 
              data-filter="dynasty" bindtap="onFilterChange">按朝代</view>
        <view class="filter-tag {{currentFilter === 'pattern' ? 'active' : ''}}" 
              data-filter="pattern" bindtap="onFilterChange">按格局</view>
        <view class="filter-tag {{currentFilter === 'occupation' ? 'active' : ''}}" 
              data-filter="occupation" bindtap="onFilterChange">按职业</view>
        <view class="filter-tag {{currentFilter === 'verification' ? 'active' : ''}}" 
              data-filter="verification" bindtap="onFilterChange">高验证度</view>
      </scroll-view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="statistics-section" wx:if="{{showStatistics}}">
    <view class="stat-card">
      <text class="stat-number">{{statistics.totalCelebrities}}</text>
      <text class="stat-label">历史名人</text>
    </view>
    <view class="stat-card">
      <text class="stat-number">{{statistics.averageVerificationScore}}</text>
      <text class="stat-label">平均验证度</text>
    </view>
    <view class="stat-card">
      <text class="stat-number">{{Object.keys(statistics.patternDistribution).length}}</text>
      <text class="stat-label">格局类型</text>
    </view>
  </view>

  <!-- 名人列表 -->
  <view class="celebrity-list">
    <view class="celebrity-card" 
          wx:for="{{displayedCelebrities}}" 
          wx:key="id"
          bindtap="onCelebrityTap"
          data-celebrity="{{item}}">
      
      <!-- 名人基本信息 -->
      <view class="celebrity-header">
        <view class="celebrity-name-section">
          <text class="celebrity-name">{{item.basicInfo.name}}</text>
          <text class="celebrity-courtesy" wx:if="{{item.basicInfo.courtesy}}">字{{item.basicInfo.courtesy}}</text>
          <text class="celebrity-nickname" wx:if="{{item.basicInfo.nickname}}">{{item.basicInfo.nickname}}</text>
        </view>
        <view class="verification-badge">
          <text class="verification-score">{{item.verification.algorithmMatch * 100}}%</text>
          <text class="verification-label">验证度</text>
        </view>
      </view>

      <!-- 朝代和职业 -->
      <view class="celebrity-meta">
        <view class="meta-item">
          <text class="meta-label">🏛️</text>
          <text class="meta-value">{{item.basicInfo.dynasty}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">👤</text>
          <text class="meta-value">{{item.basicInfo.occupation[0]}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">📍</text>
          <text class="meta-value">{{item.basicInfo.birthplace.province}}</text>
        </view>
      </view>

      <!-- 命理信息 -->
      <view class="bazi-section">
        <view class="bazi-header">
          <text class="bazi-label">八字命理</text>
          <text class="pattern-tag">{{item.pattern.mainPattern}}</text>
        </view>
        <view class="bazi-content">
          <text class="bazi-text">{{item.bazi.fullBazi}}</text>
          <text class="yongshen-text">用神：{{item.pattern.yongshen}}</text>
        </view>
      </view>

      <!-- 重要事件预览 -->
      <view class="events-preview" wx:if="{{item.lifeEvents.length > 0}}">
        <text class="events-label">重要事件</text>
        <view class="event-item">
          <text class="event-date">{{item.lifeEvents[0].date}}</text>
          <text class="event-desc">{{item.lifeEvents[0].description}}</text>
        </view>
      </view>

      <!-- 古籍依据 -->
      <view class="evidence-section" wx:if="{{item.verification.ancientTextEvidence.length > 0}}">
        <text class="evidence-label">📜 古籍依据</text>
        <text class="evidence-text">{{item.verification.ancientTextEvidence[0]}}</text>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="onLoadMore">
    <text class="load-more-text">加载更多名人数据</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{displayedCelebrities.length === 0}}">
    <text class="empty-icon">📚</text>
    <text class="empty-title">暂无匹配的名人数据</text>
    <text class="empty-subtitle">尝试调整搜索条件或筛选器</text>
  </view>
</view>

<!-- 名人详情弹窗 -->
<view class="celebrity-modal {{showModal ? 'show' : ''}}" bindtap="onModalClose">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{selectedCelebrity.basicInfo.name}} 详细资料</text>
      <text class="modal-close" bindtap="onModalClose">✕</text>
    </view>
    
    <scroll-view class="modal-body" scroll-y="true">
      <!-- 基础信息 -->
      <view class="detail-section">
        <text class="section-title">📋 基础档案</text>
        <view class="detail-grid">
          <view class="detail-item">
            <text class="detail-label">姓名</text>
            <text class="detail-value">{{selectedCelebrity.basicInfo.name}}</text>
          </view>
          <view class="detail-item" wx:if="{{selectedCelebrity.basicInfo.courtesy}}">
            <text class="detail-label">字</text>
            <text class="detail-value">{{selectedCelebrity.basicInfo.courtesy}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">生卒年</text>
            <text class="detail-value">{{selectedCelebrity.basicInfo.birthYear}}-{{selectedCelebrity.basicInfo.deathYear}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">籍贯</text>
            <text class="detail-value">{{selectedCelebrity.basicInfo.birthplace.province}}{{selectedCelebrity.basicInfo.birthplace.city}}</text>
          </view>
        </view>
      </view>

      <!-- 命理分析 -->
      <view class="detail-section">
        <text class="section-title">🔮 命理特征</text>
        <view class="bazi-detail">
          <text class="bazi-full">{{selectedCelebrity.bazi.fullBazi}}</text>
          <view class="pattern-detail">
            <text class="pattern-main">主格局：{{selectedCelebrity.pattern.mainPattern}}</text>
            <text class="pattern-sub">副格局：{{selectedCelebrity.pattern.subPattern}}</text>
            <text class="yongshen-detail">用神：{{selectedCelebrity.pattern.yongshen}}</text>
          </view>
        </view>
      </view>

      <!-- 人生事件 -->
      <view class="detail-section">
        <text class="section-title">📅 人生重要事件</text>
        <view class="events-timeline">
          <view class="timeline-item" wx:for="{{selectedCelebrity.lifeEvents}}" wx:key="date">
            <view class="timeline-date">{{item.date}}</view>
            <view class="timeline-content">
              <text class="timeline-title">{{item.eventType}}</text>
              <text class="timeline-desc">{{item.description}}</text>
              <text class="timeline-impact">影响程度：{{item.impact}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 验证信息 -->
      <view class="detail-section">
        <text class="section-title">✅ 验证标签</text>
        <view class="verification-detail">
          <text class="verification-item">算法吻合度：{{selectedCelebrity.verification.algorithmMatch * 100}}%</text>
          <text class="verification-item">历史准确性：{{selectedCelebrity.verification.historicalAccuracy * 100}}%</text>
          <view class="ancient-evidence">
            <text class="evidence-title">古籍依据：</text>
            <text class="evidence-item" wx:for="{{selectedCelebrity.verification.ancientTextEvidence}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
