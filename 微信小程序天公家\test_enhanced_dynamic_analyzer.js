// test_enhanced_dynamic_analyzer.js
// 测试增强版动态分析器

const EnhancedDynamicAnalyzer = require('./utils/enhanced_dynamic_analyzer.js');

/**
 * 测试动态分析模块
 */
function testDynamicAnalyzer() {
  console.log('🧪 开始测试动态分析模块');
  console.log('=' * 50);

  const analyzer = new EnhancedDynamicAnalyzer();

  // 测试用例1：完整动态分析
  console.log('\n📋 测试用例1：完整动态分析');
  const testCase1 = {
    bazi: {
      fourPillars: [
        { gan: '甲', zhi: '寅' },
        { gan: '丁', zhi: '巳' },
        { gan: '甲', zhi: '子' },
        { gan: '己', zhi: '未' }
      ],
      element_powers: {
        percentages: {
          '木': 30, '火': 25, '土': 25, '金': 10, '水': 10
        }
      }
    },
    yongshen: {
      yongshen: '火',
      xishen: ['木'],
      jishen: ['金']
    },
    personalInfo: {
      birth_year: 1985,
      age: 39,
      gender: '男'
    },
    analysisOptions: {
      dayun_years: 20,
      forecast_years: 5,
      social_context: {
        economic_phase: 'stable',
        industry: 'tech'
      }
    }
  };

  const result1 = analyzer.analyzeDynamicTrends(
    testCase1.bazi, 
    testCase1.yongshen, 
    testCase1.personalInfo, 
    testCase1.analysisOptions
  );
  
  console.log('🎯 完整分析结果:');
  console.log('  大运分析:', result1.dayun_analysis ? '✅ 完成' : '❌ 失败');
  console.log('  流年分析:', result1.liunian_analysis ? '✅ 完成' : '❌ 失败');
  console.log('  转折点检测:', result1.turning_points ? `✅ 发现${result1.turning_points.length}个转折点` : '❌ 失败');
  console.log('  社会环境分析:', result1.social_impact ? '✅ 完成' : '❌ 失败');
  console.log('  动态预测:', result1.dynamic_forecast ? '✅ 完成' : '❌ 失败');
  console.log('  置信度:', (result1.confidence * 100).toFixed(1) + '%');

  // 测试用例2：大运分析
  console.log('\n📋 测试用例2：大运分析');
  testDayunAnalysis(analyzer);

  // 测试用例3：流年分析
  console.log('\n📋 测试用例3：流年分析');
  testLiuNianAnalysis(analyzer);

  // 测试用例4：刑冲合害检测
  console.log('\n📋 测试用例4：刑冲合害检测');
  testClashDetection(analyzer);

  // 测试用例5：社会环境因素
  console.log('\n📋 测试用例5：社会环境因素');
  testSocialFactors(analyzer);

  console.log('\n✅ 动态分析器测试完成');
}

/**
 * 测试大运分析功能
 */
function testDayunAnalysis(analyzer) {
  const testBazi = {
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ]
  };

  const currentAge = 35;
  const analysisYears = 20;

  const result = analyzer.analyzeDayun(testBazi, currentAge, analysisYears);
  
  console.log('🌊 大运分析测试:');
  console.log(`  当前大运: ${result.current_dayun.gan}${result.current_dayun.zhi} (${result.current_dayun.start_age}-${result.current_dayun.end_age}岁)`);
  console.log(`  能量阶段: ${result.current_dayun.energy_curve.phase}`);
  console.log(`  能量水平: ${(result.current_dayun.energy_curve.energy_level * 100).toFixed(0)}%`);
  console.log(`  整体趋势: ${result.current_dayun.overall_trend}`);
  console.log(`  相互作用: ${result.current_dayun.interactions.length}项`);
  
  if (result.upcoming_dayuns.length > 0) {
    console.log(`  下个大运: ${result.upcoming_dayuns[0].gan}${result.upcoming_dayuns[0].zhi} (${result.upcoming_dayuns[0].start_age}-${result.upcoming_dayuns[0].end_age}岁)`);
  }
  
  console.log(`  ✅ 大运分析: ${result.current_dayun ? '成功' : '失败'}`);
}

/**
 * 测试流年分析功能
 */
function testLiuNianAnalysis(analyzer) {
  const testBazi = {
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ],
    element_powers: {
      percentages: { '木': 30, '火': 25, '土': 25, '金': 10, '水': 10 }
    }
  };

  const testYongshen = {
    yongshen: '火',
    jishen: ['金']
  };

  const currentYear = 2024;
  const forecastYears = 3;

  const result = analyzer.analyzeLiuNian(testBazi, testYongshen, currentYear, forecastYears);
  
  console.log('📅 流年分析测试:');
  result.yearly_analysis.forEach((year, index) => {
    console.log(`  ${year.year}年 (${year.gan}${year.zhi}):`);
    console.log(`    运势等级: ${year.fortune_trend.level}`);
    console.log(`    用神影响: ${year.yongshen_impact.impact} (${(year.yongshen_impact.score * 100).toFixed(0)}分)`);
    console.log(`    刑冲情况: ${year.clash_analysis.summary}`);
    console.log(`    关键事件: ${year.key_events.length}个`);
  });
  
  console.log(`  整体趋势: ${result.overall_trend.direction}`);
  console.log(`  最佳年份: ${result.best_years.map(y => y.year).join('、')}`);
  console.log(`  挑战年份: ${result.challenging_years.map(y => y.year).join('、')}`);
  console.log(`  ✅ 流年分析: ${result.yearly_analysis.length > 0 ? '成功' : '失败'}`);
}

/**
 * 测试刑冲合害检测
 */
function testClashDetection(analyzer) {
  const testCases = [
    { zhi1: '子', zhi2: '午', expected: '相冲' },
    { zhi1: '寅', zhi2: '申', expected: '相冲' },
    { zhi1: '子', zhi2: '丑', expected: '相合' },
    { zhi1: '寅', zhi2: '亥', expected: '相合' },
    { zhi1: '子', zhi2: '寅', expected: null }
  ];

  console.log('🔄 刑冲合害检测测试:');
  testCases.forEach((testCase, index) => {
    const result = analyzer.checkClash(testCase.zhi1, testCase.zhi2);
    const actual = result ? result.type : null;
    const isCorrect = actual === testCase.expected;
    
    console.log(`  测试${index + 1}: ${testCase.zhi1}与${testCase.zhi2}`);
    console.log(`    预期: ${testCase.expected || '无关系'}`);
    console.log(`    实际: ${actual || '无关系'}`);
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
    
    if (result) {
      console.log(`    强度: ${result.intensity}, 影响: ${result.effect}`);
    }
  });
}

/**
 * 测试社会环境因素
 */
function testSocialFactors(analyzer) {
  const testCases = [
    {
      name: '青年科技从业者',
      personalInfo: { age: 28 },
      socialContext: { economic_phase: 'prosperity', industry: 'tech' }
    },
    {
      name: '中年金融从业者',
      personalInfo: { age: 42 },
      socialContext: { economic_phase: 'stable', industry: 'finance' }
    },
    {
      name: '老年传统行业',
      personalInfo: { age: 65 },
      socialContext: { economic_phase: 'recession', industry: 'traditional' }
    }
  ];

  console.log('🌍 社会环境因素测试:');
  testCases.forEach((testCase, index) => {
    const result = analyzer.analyzeSocialFactors(testCase.personalInfo, testCase.socialContext);
    
    console.log(`  测试${index + 1}: ${testCase.name}`);
    console.log(`    综合影响系数: ${result.overall_impact.toFixed(2)}`);
    console.log(`    影响因素数量: ${result.factors.length}个`);
    console.log(`    建议数量: ${result.recommendations.length}条`);
    
    result.factors.forEach(factor => {
      console.log(`      - ${factor.type}: ${factor.description}`);
    });
    
    console.log(`    ✅ 分析: ${result.factors.length > 0 ? '成功' : '失败'}`);
  });
}

/**
 * 测试用神影响分析
 */
function testYongshenImpact() {
  console.log('\n🧪 测试用神影响分析');
  
  const analyzer = new EnhancedDynamicAnalyzer();
  
  const testCases = [
    {
      name: '用神得力年',
      liuNianGan: '丙',
      liuNianZhi: '午',
      yongshen: { yongshen: '火', jishen: ['金'] },
      expected: 'favorable'
    },
    {
      name: '忌神当值年',
      liuNianGan: '庚',
      liuNianZhi: '申',
      yongshen: { yongshen: '火', jishen: ['金'] },
      expected: 'unfavorable'
    },
    {
      name: '中性年份',
      liuNianGan: '甲',
      liuNianZhi: '寅',
      yongshen: { yongshen: '火', jishen: ['金'] },
      expected: 'neutral'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.name}`);
    const result = analyzer.analyzeYongshenImpact(testCase.liuNianGan, testCase.liuNianZhi, testCase.yongshen);
    
    console.log(`    流年: ${testCase.liuNianGan}${testCase.liuNianZhi}`);
    console.log(`    预期影响: ${testCase.expected}`);
    console.log(`    实际影响: ${result.impact}`);
    console.log(`    影响得分: ${(result.score * 100).toFixed(0)}分`);
    console.log(`    描述: ${result.description}`);
    
    const isCorrect = result.impact.includes(testCase.expected) || 
                     (testCase.expected === 'neutral' && result.impact === 'neutral');
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 测试转折点检测
 */
function testTurningPointDetection() {
  console.log('\n🧪 测试转折点检测');
  
  const analyzer = new EnhancedDynamicAnalyzer();
  
  // 模拟大运和流年分析结果
  const mockDayunAnalysis = {
    transition_analysis: [
      {
        period: '35-37岁',
        description: '大运交接期，运势波动较大'
      }
    ]
  };

  const mockLiuNianAnalysis = {
    yearly_analysis: [
      {
        year: 2024,
        clash_analysis: { total_intensity: 2.0 },
        yongshen_impact: { score: 0.9 }
      },
      {
        year: 2025,
        clash_analysis: { total_intensity: 0.5 },
        yongshen_impact: { score: 0.3 }
      }
    ]
  };

  const mockBazi = {};

  const result = analyzer.detectTurningPoints(mockDayunAnalysis, mockLiuNianAnalysis, mockBazi);
  
  console.log(`  检测到转折点: ${result.length}个`);
  result.forEach((point, index) => {
    console.log(`    ${index + 1}. ${point.type} (${point.timing})`);
    console.log(`       强度: ${point.intensity}`);
    console.log(`       描述: ${point.description}`);
    console.log(`       建议: ${point.advice}`);
  });
  
  console.log(`  ✅ 转折点检测: ${result.length > 0 ? '成功' : '需要检查'}`);
}

/**
 * 验证动态预测准确性
 */
function validateDynamicForecast() {
  console.log('\n📚 验证动态预测准确性');
  
  const analyzer = new EnhancedDynamicAnalyzer();
  
  // 使用历史数据验证（简化版）
  const historicalCase = {
    bazi: {
      fourPillars: [
        { gan: '甲', zhi: '寅' },
        { gan: '丁', zhi: '巳' },
        { gan: '甲', zhi: '子' },
        { gan: '己', zhi: '未' }
      ],
      element_powers: {
        percentages: { '木': 35, '火': 30, '土': 20, '金': 10, '水': 5 }
      }
    },
    yongshen: { yongshen: '火', jishen: ['金'] },
    personalInfo: { birth_year: 1980, age: 44 },
    analysisOptions: { forecast_years: 3 }
  };

  const result = analyzer.analyzeDynamicTrends(
    historicalCase.bazi,
    historicalCase.yongshen,
    historicalCase.personalInfo,
    historicalCase.analysisOptions
  );

  console.log('🔮 动态预测验证:');
  console.log(`  短期预测趋势: ${result.dynamic_forecast.short_term.trend}`);
  console.log(`  短期预测得分: ${(result.dynamic_forecast.short_term.score * 100).toFixed(0)}分`);
  console.log(`  社会环境调整: ${result.dynamic_forecast.short_term.social_adjustment.toFixed(2)}`);
  console.log(`  关键建议数量: ${result.dynamic_forecast.key_recommendations.length}条`);
  
  result.dynamic_forecast.key_recommendations.forEach((rec, index) => {
    console.log(`    ${index + 1}. ${rec.category}: ${rec.content}`);
  });
  
  console.log(`  ✅ 动态预测: ${result.dynamic_forecast ? '成功生成' : '生成失败'}`);
}

// 运行测试
if (require.main === module) {
  try {
    testDynamicAnalyzer();
    testYongshenImpact();
    testTurningPointDetection();
    validateDynamicForecast();
    
    console.log('\n🎉 所有测试完成！');
    console.log('📈 动态分析模块已按照文档要求实现：');
    console.log('  ✅ 大运能量衰减曲线模型');
    console.log('  ✅ 流年刑冲合害检测');
    console.log('  ✅ 关键转折点识别');
    console.log('  ✅ 社会环境因素注入');
    console.log('  ✅ 多维度动态预测');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    console.error(error.stack);
  }
}

module.exports = {
  testDynamicAnalyzer,
  testDayunAnalysis,
  testLiuNianAnalysis,
  testClashDetection,
  testSocialFactors,
  testYongshenImpact,
  testTurningPointDetection,
  validateDynamicForecast
};
