/**
 * 第三批次历史名人数据合并工具
 * 合并先秦春秋、先秦战国、魏晋南北朝三个数据文件
 */

const preQinSpringAutumn = require('../data/pre_qin_spring_autumn_celebrities');
const preQinWarringStates = require('../data/pre_qin_warring_states_celebrities');
const weiJinNanBei = require('../data/wei_jin_nanbeichao_celebrities');
const nanBeiPart2 = require('../data/nanbeichao_celebrities_part2');
const fs = require('fs');

class Batch3DataMerger {
  constructor() {
    this.mergedData = {
      metadata: {
        batchNumber: 3,
        periods: ["先秦", "魏晋南北朝"],
        totalRecords: 0,
        creationDate: "2025-01-02",
        dataQuality: "优秀",
        verificationStandard: "专家交叉校验+古籍依据双重认证"
      },
      celebrities: []
    };
  }

  /**
   * 合并所有数据源
   */
  mergeAllData() {
    console.log('🔄 开始合并第三批次数据...');
    
    // 合并先秦春秋数据
    console.log(`📚 合并先秦春秋数据: ${preQinSpringAutumn.celebrities.length} 位`);
    this.mergedData.celebrities.push(...preQinSpringAutumn.celebrities);
    
    // 合并先秦战国数据
    console.log(`📚 合并先秦战国数据: ${preQinWarringStates.celebrities.length} 位`);
    this.mergedData.celebrities.push(...preQinWarringStates.celebrities);
    
    // 合并魏晋南北朝数据
    console.log(`📚 合并魏晋南北朝数据: ${weiJinNanBei.celebrities.length} 位`);
    this.mergedData.celebrities.push(...weiJinNanBei.celebrities);
    
    // 合并南北朝第二部分数据
    console.log(`📚 合并南北朝第二部分数据: ${nanBeiPart2.celebrities.length} 位`);
    this.mergedData.celebrities.push(...nanBeiPart2.celebrities);
    
    this.mergedData.metadata.totalRecords = this.mergedData.celebrities.length;
    
    console.log(`✅ 合并完成，总计: ${this.mergedData.metadata.totalRecords} 位名人`);
  }

  /**
   * 数据质量检查
   */
  validateData() {
    console.log('🔍 进行数据质量检查...');
    
    const issues = [];
    const idSet = new Set();
    let totalVerificationScore = 0;
    
    this.mergedData.celebrities.forEach((celebrity, index) => {
      // 检查ID唯一性
      if (idSet.has(celebrity.id)) {
        issues.push(`重复ID: ${celebrity.id}`);
      } else {
        idSet.add(celebrity.id);
      }
      
      // 检查必要字段
      if (!celebrity.basicInfo || !celebrity.basicInfo.name) {
        issues.push(`第${index + 1}位名人缺少基本信息`);
      }
      
      if (!celebrity.bazi || !celebrity.bazi.fullBazi) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少八字信息`);
      }
      
      if (!celebrity.pattern || !celebrity.pattern.mainPattern) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少格局信息`);
      }
      
      if (!celebrity.verification || !celebrity.verification.algorithmMatch) {
        issues.push(`${celebrity.basicInfo?.name || '未知'}: 缺少验证信息`);
      } else {
        totalVerificationScore += celebrity.verification.algorithmMatch;
      }
    });
    
    const averageVerification = totalVerificationScore / this.mergedData.celebrities.length;
    
    console.log(`📊 数据质量报告:`);
    console.log(`   - 总记录数: ${this.mergedData.celebrities.length}`);
    console.log(`   - 平均验证分数: ${averageVerification.toFixed(3)}`);
    console.log(`   - 发现问题: ${issues.length} 个`);
    
    if (issues.length > 0) {
      console.log(`⚠️  问题列表:`);
      issues.forEach(issue => console.log(`     - ${issue}`));
    }
    
    return {
      totalRecords: this.mergedData.celebrities.length,
      averageVerification: averageVerification,
      issues: issues,
      qualityGrade: averageVerification >= 0.9 ? '优秀' : averageVerification >= 0.8 ? '良好' : '需改进'
    };
  }

  /**
   * 生成朝代分布统计
   */
  generateDynastyStats() {
    console.log('📈 生成朝代分布统计...');
    
    const dynastyStats = {};
    
    this.mergedData.celebrities.forEach(celebrity => {
      const dynasty = celebrity.basicInfo.dynasty;
      dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
    });
    
    console.log('📊 朝代分布:');
    Object.entries(dynastyStats)
      .sort((a, b) => b[1] - a[1])
      .forEach(([dynasty, count]) => {
        console.log(`   - ${dynasty}: ${count} 位`);
      });
    
    return dynastyStats;
  }

  /**
   * 保存合并后的数据
   */
  saveData() {
    const outputPath = 'data/batch3_complete_celebrities.js';
    
    const fileContent = `/**
 * 第三批次历史名人数据库完整版
 * 包含35位先秦、魏晋南北朝时期重要历史人物
 * 合并时间: ${new Date().toISOString()}
 */

const batch3CompleteCelebrities = ${JSON.stringify(this.mergedData, null, 2)};

module.exports = batch3CompleteCelebrities;`;
    
    fs.writeFileSync(outputPath, fileContent, 'utf8');
    console.log(`💾 数据已保存到: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 执行完整的合并流程
   */
  execute() {
    console.log('🚀 开始第三批次数据合并流程');
    console.log('============================================================');
    
    try {
      // 1. 合并数据
      this.mergeAllData();
      
      // 2. 质量检查
      const qualityReport = this.validateData();
      
      // 3. 朝代统计
      const dynastyStats = this.generateDynastyStats();
      
      // 4. 保存数据
      const outputPath = this.saveData();
      
      console.log('============================================================');
      console.log('🎉 第三批次数据合并完成！');
      console.log(`📊 质量等级: ${qualityReport.qualityGrade}`);
      console.log(`📈 平均验证分数: ${qualityReport.averageVerification.toFixed(3)}`);
      console.log(`📁 输出文件: ${outputPath}`);
      
      return {
        success: true,
        outputPath: outputPath,
        qualityReport: qualityReport,
        dynastyStats: dynastyStats
      };
      
    } catch (error) {
      console.error('❌ 合并过程中发生错误:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 如果直接运行此文件，执行合并
if (require.main === module) {
  const merger = new Batch3DataMerger();
  merger.execute();
}

module.exports = Batch3DataMerger;
