/**
 * 统一五行计算接口 - 安全版本
 * 带有完整的错误处理和降级机制
 */

class UnifiedWuxingCalculator {
  constructor() {
    this.engine = null;
    this.cache = new Map();
    this.version = '1.0.0';
    this.initialized = false;
    
    this.initializeEngine();
  }

  /**
   * 初始化引擎
   */
  initializeEngine() {
    try {
      const ProfessionalWuxingEngine = require('./professional_wuxing_engine.js');
      this.engine = new ProfessionalWuxingEngine();
      this.initialized = true;
      console.log('🎯 统一五行计算器初始化完成');
    } catch (error) {
      console.warn('⚠️ 专业级引擎加载失败，使用降级模式:', error.message);
      this.engine = null;
      this.initialized = false;
    }
  }

  /**
   * 统一五行计算入口
   */
  calculate(baziData, options = {}) {
    try {
      // 参数验证
      if (!this.validateInput(baziData)) {
        throw new Error('八字数据格式不正确');
      }

      // 🔧 强制清除所有缓存（包括专业级引擎缓存）
      this.cache.clear();
      if (this.engine && typeof this.engine.clearCache === 'function') {
        this.engine.clearCache();
        console.log('🗑️ 已清除专业级引擎缓存');
      }
      console.log('🗑️ 已清除所有缓存，强制重新计算');

      console.log('🎯 开始真实五行计算');
      console.log('🔍 引擎状态检查:');
      console.log('   - initialized:', this.initialized);
      console.log('   - engine:', !!this.engine);
      console.log('   - version:', this.version);

      let result;

      if (this.initialized && this.engine) {
        console.log('✅ 使用专业级引擎计算');
        result = this.calculateWithProfessionalEngine(baziData);
      } else {
        console.log('⚠️ 专业级引擎不可用，使用改进的降级计算');
        result = this.calculateWithFallback(baziData);
      }

      console.log('🎯 计算结果预览:', {
        wood: result.wood,
        fire: result.fire,
        earth: result.earth,
        metal: result.metal,
        water: result.water
      });

      return result;
      
    } catch (error) {
      console.error('❌ 五行计算错误:', error.message);
      return this.handleCalculationError(error, baziData);
    }
  }

  /**
   * 使用专业级引擎计算
   */
  calculateWithProfessionalEngine(baziData) {
    try {
      console.log('🔄 使用专业级引擎计算...');

      // 🔧 强制清除专业级引擎缓存（确保不使用旧缓存）
      if (this.engine && typeof this.engine.clearCache === 'function') {
        this.engine.clearCache();
        console.log('🗑️ 专业级引擎缓存已清除，强制重新计算');
      }

      // 转换八字数据格式为四柱格式
      const fourPillars = this.convertToFourPillars(baziData);

      // 调用专业级引擎计算
      const rawResult = this.engine.generateDetailedReport(fourPillars);
      
      // 标准化结果格式
      return this.standardizeResult(rawResult, baziData);
      
    } catch (error) {
      console.warn('⚠️ 专业级引擎计算失败，降级到基础计算:', error.message);
      return this.calculateWithFallback(baziData);
    }
  }

  /**
   * 降级计算方法
   */
  calculateWithFallback(baziData) {
    console.log('🎯 使用改进的真实五行计算方法...');

    // 🌟 改进的五行力量计算（不再是简单计数）
    const wuxingPowers = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };
    const wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
      '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
      '戌': '土', '亥': '水'
    };

    // 🌙 月令调节系数
    const monthAdjustment = this.getMonthAdjustment(baziData.month.zhi);

    // 🌟 计算天干力量（权重更高）
    const pillars = [baziData.year, baziData.month, baziData.day, baziData.hour];
    pillars.forEach((pillar, index) => {
      if (pillar.gan && wuxingMap[pillar.gan]) {
        const element = wuxingMap[pillar.gan];
        // 日主力量最强，月干次之，年干时干较弱
        const baseStrength = index === 2 ? 30 : (index === 1 ? 25 : 20);
        wuxingPowers[element] += baseStrength;
      }
    });

    // 🏔️ 计算地支力量（包含本气和部分藏干）
    pillars.forEach((pillar, index) => {
      if (pillar.zhi && wuxingMap[pillar.zhi]) {
        const element = wuxingMap[pillar.zhi];
        // 日支力量较强，其他地支次之
        const baseStrength = index === 2 ? 25 : 20;
        wuxingPowers[element] += baseStrength;

        // 简化的藏干计算（主要藏干）
        this.addCangganPower(wuxingPowers, pillar.zhi, baseStrength * 0.3, wuxingMap);
      }
    });

    // 🌙 应用月令调节
    Object.keys(wuxingPowers).forEach(element => {
      const englishElement = this.getEnglishElement(element);
      const adjustment = monthAdjustment[englishElement] || 1.0;
      wuxingPowers[element] = Math.round(wuxingPowers[element] * adjustment);
      // 确保最小值
      wuxingPowers[element] = Math.max(5, wuxingPowers[element]);
    });

    // 转换为英文格式
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    const total = Object.values(wuxingPowers).reduce((sum, power) => sum + power, 0);
    
    const result = {
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'fallback_calculation',
      algorithm: '基础统计算法',
      
      bazi: {
        year: baziData.year,
        month: baziData.month,
        day: baziData.day,
        hour: baziData.hour,
        fullBazi: `${baziData.year.gan}${baziData.year.zhi} ${baziData.month.gan}${baziData.month.zhi} ${baziData.day.gan}${baziData.day.zhi} ${baziData.hour.gan}${baziData.hour.zhi}`
      },
      
      wuxingStrength: {},
      
      balance: {
        strongest: '未知',
        weakest: '未知',
        balanceScore: 50,
        balanceLevel: '基础计算',
        recommendation: '建议使用专业级计算获得更准确结果'
      },
      
      calculationDetails: {
        totalStrength: total,
        confidence: 30
      }
    };

    // 填充五行数据
    Object.entries(elementMap).forEach(([chineseName, englishName]) => {
      const power = wuxingPowers[chineseName];
      const percentage = total > 0 ? (power / total * 100) : 0;

      result.wuxingStrength[englishName] = {
        value: power,
        percentage: Math.round(percentage),
        level: this.getStrengthLevel(percentage),
        chineseName: chineseName
      };

      result[englishName] = power;
    });

    return result;
  }

  /**
   * 验证输入数据
   */
  validateInput(baziData) {
    if (!baziData) return false;
    
    const requiredFields = ['year', 'month', 'day', 'hour'];
    return requiredFields.every(field => {
      return baziData[field] && 
             baziData[field].gan && 
             baziData[field].zhi;
    });
  }

  /**
   * 转换八字数据为四柱格式
   */
  convertToFourPillars(baziData) {
    return [
      { gan: baziData.year.gan, zhi: baziData.year.zhi },
      { gan: baziData.month.gan, zhi: baziData.month.zhi },
      { gan: baziData.day.gan, zhi: baziData.day.zhi },
      { gan: baziData.hour.gan, zhi: baziData.hour.zhi }
    ];
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(baziData, options) {
    const baziString = `${baziData.year.gan}${baziData.year.zhi}_${baziData.month.gan}${baziData.month.zhi}_${baziData.day.gan}${baziData.day.zhi}_${baziData.hour.gan}${baziData.hour.zhi}`;
    const optionsString = JSON.stringify(options);
    return `${baziString}_${optionsString}_v${this.version}`;
  }

  /**
   * 标准化计算结果 - 🔧 修复：使用真实的专业级引擎计算结果
   */
  standardizeResult(rawResult, baziData) {
    console.log('🔄 标准化专业级引擎计算结果...');
    console.log('🔍 原始结果:', rawResult);

    // 🔧 从专业级引擎结果中提取真实的五行力量
    const finalPowers = rawResult.results?.finalPowers || {};

    // 转换中文五行名称到英文
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    const wuxingValues = {};

    Object.entries(elementMap).forEach(([chinese, english]) => {
      wuxingValues[english] = finalPowers[chinese] || 0;
    });

    console.log('🔧 提取的五行力量:', wuxingValues);

    // 计算总力量和百分比
    const totalPower = Object.values(wuxingValues).reduce((sum, val) => sum + val, 0);
    const wuxingStrength = {};

    Object.entries(wuxingValues).forEach(([element, power]) => {
      const percentage = totalPower > 0 ? Math.round((power / totalPower) * 100) : 0;
      wuxingStrength[element] = {
        value: Math.round(power * 10), // 放大10倍以匹配前端期望的数值范围
        percentage: percentage,
        level: this.getStrengthLevel(percentage),
        chineseName: Object.keys(elementMap).find(key => elementMap[key] === element)
      };
    });

    console.log('🎯 标准化后的五行强度:', wuxingStrength);

    return {
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'professional_wuxing_engine',
      algorithm: rawResult.algorithm || '专业级三层权重模型',
      bazi: baziData,

      // 🔧 使用真实计算结果而不是固定值
      wood: Math.round(wuxingValues.wood * 10),
      fire: Math.round(wuxingValues.fire * 10),
      earth: Math.round(wuxingValues.earth * 10),
      metal: Math.round(wuxingValues.metal * 10),
      water: Math.round(wuxingValues.water * 10),

      wuxingStrength: wuxingStrength,

      // 保留原始专业级引擎结果
      rawEngineResult: rawResult
    };
  }

  /**
   * 🌙 获取月令调节系数
   */
  getMonthAdjustment(monthZhi) {
    const seasonAdjustment = {
      // 春季 - 木旺
      '寅': { wood: 1.4, fire: 1.0, earth: 0.8, metal: 0.6, water: 0.9 },
      '卯': { wood: 1.5, fire: 1.1, earth: 0.7, metal: 0.5, water: 0.8 },
      '辰': { wood: 1.2, fire: 1.0, earth: 1.3, metal: 0.7, water: 0.9 },

      // 夏季 - 火旺
      '巳': { wood: 0.9, fire: 1.4, earth: 1.2, metal: 0.6, water: 0.5 },
      '午': { wood: 0.8, fire: 1.5, earth: 1.3, metal: 0.5, water: 0.4 },
      '未': { wood: 0.9, fire: 1.2, earth: 1.4, metal: 0.7, water: 0.6 },

      // 秋季 - 金旺
      '申': { wood: 0.6, fire: 0.8, earth: 1.1, metal: 1.4, water: 1.0 },
      '酉': { wood: 0.5, fire: 0.7, earth: 1.0, metal: 1.5, water: 1.1 },
      '戌': { wood: 0.7, fire: 0.9, earth: 1.3, metal: 1.2, water: 0.9 },

      // 冬季 - 水旺
      '亥': { wood: 1.0, fire: 0.6, earth: 0.8, metal: 1.1, water: 1.4 },
      '子': { wood: 1.1, fire: 0.5, earth: 0.7, metal: 1.0, water: 1.5 },
      '丑': { wood: 0.9, fire: 0.7, earth: 1.2, metal: 1.2, water: 1.2 }
    };

    return seasonAdjustment[monthZhi] || { wood: 1.0, fire: 1.0, earth: 1.0, metal: 1.0, water: 1.0 };
  }

  /**
   * 🏔️ 添加藏干力量
   */
  addCangganPower(wuxingPowers, zhi, baseStrength, wuxingMap) {
    const cangganMap = {
      '丑': ['癸', '辛'], '寅': ['丙', '戊'], '辰': ['乙', '癸'],
      '巳': ['庚', '戊'], '未': ['丁', '乙'], '申': ['壬', '戊'],
      '戌': ['辛', '丁'], '亥': ['甲']
    };

    const cangganList = cangganMap[zhi];
    if (cangganList) {
      cangganList.forEach(gan => {
        const element = wuxingMap[gan];
        if (element) {
          wuxingPowers[element] += Math.round(baseStrength * 0.5);
        }
      });
    }
  }

  /**
   * 🔄 获取英文五行名称
   */
  getEnglishElement(chineseElement) {
    const map = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    return map[chineseElement] || chineseElement;
  }

  /**
   * 获取五行强度等级
   */
  getStrengthLevel(percentage) {
    if (percentage >= 35) return '极旺';
    if (percentage >= 25) return '偏旺';
    if (percentage >= 15) return '中和';
    if (percentage >= 8) return '偏弱';
    return '极弱';
  }

  /**
   * 处理计算错误
   */
  handleCalculationError(error, baziData) {
    return {
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'error_handler',
      error: true,
      errorMessage: error.message,
      wood: 0, fire: 0, earth: 0, metal: 0, water: 0
    };
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('🗑️ 五行计算缓存已清理');
  }
}

// 创建单例实例
const unifiedCalculator = new UnifiedWuxingCalculator();

// 🔧 强制清除所有缓存，确保使用新的计算逻辑
unifiedCalculator.clearCache();

// 🔧 强制清除专业级引擎缓存
if (unifiedCalculator.engine && typeof unifiedCalculator.engine.clearCache === 'function') {
  unifiedCalculator.engine.clearCache();
  console.log('🗑️ 已强制清除专业级引擎缓存');
}

console.log('🎯 统一五行计算器已重置，将使用改进的计算逻辑');
console.log('🚀 修复版本已加载 - 版本时间戳:', new Date().toISOString());

module.exports = unifiedCalculator;
