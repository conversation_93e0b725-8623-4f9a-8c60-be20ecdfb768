/**
 * 神煞表和病药映射表
 * 基于《三命通会》神煞表及《滴天髓》病药映射表
 * 实现应期.txt要求的内置神煞和病药映射功能
 */

class GodsAndMedicineTables {
  constructor() {
    this.godsTable = this.initializeGodsTable();
    this.medicineMappingTable = this.initializeMedicineMappingTable();
    this.seasonalAdjustments = this.initializeSeasonalAdjustments();
  }

  /**
   * 初始化神煞表（基于《三命通会》）
   */
  initializeGodsTable() {
    return {
      // 婚姻相关神煞
      marriage_gods: {
        red_phoenix: {
          name: '红鸾',
          calculation: {
            zi: 'mao', chou: 'yin', yin: 'chou', mao: 'zi',
            chen: 'hai', si: 'xu', wu: 'you', wei: 'shen',
            shen: 'wei', you: 'wu', xu: 'si', hai: 'chen'
          },
          description: '主婚姻喜庆，红鸾入命主婚期',
          ancient_basis: '《三命通会》：红鸾星动，必主婚姻',
          weight: 0.8,
          activation_conditions: ['透干', '坐支', '合化']
        },
        heavenly_joy: {
          name: '天喜',
          calculation: {
            zi: 'you', chou: 'shen', yin: 'wei', mao: 'wu',
            chen: 'si', si: 'chen', wu: 'mao', wei: 'yin',
            shen: 'chou', you: 'zi', xu: 'hai', hai: 'xu'
          },
          description: '主喜庆吉祥，配合红鸾主婚期',
          ancient_basis: '《渊海子平》：天喜临身，婚姻美满',
          weight: 0.6,
          activation_conditions: ['透干', '三合', '六合']
        },
        peach_blossom: {
          name: '桃花',
          calculation: {
            shen_zi_chen: 'you', // 申子辰见酉
            yin_wu_xu: 'mao',    // 寅午戌见卯
            si_you_chou: 'wu',   // 巳酉丑见午
            hai_mao_wei: 'zi'    // 亥卯未见子
          },
          description: '主异性缘分，桃花运势',
          ancient_basis: '《滴天髓》：桃花带合，必主婚姻',
          weight: 0.4,
          activation_conditions: ['坐支', '透干', '刑冲']
        },
        salt_pond: {
          name: '咸池',
          calculation: {
            shen_zi_chen: 'you', // 申子辰见酉
            yin_wu_xu: 'mao',    // 寅午戌见卯
            si_you_chou: 'wu',   // 巳酉丑见午
            hai_mao_wei: 'zi'    // 亥卯未见子
          },
          description: '主感情波动，异性关系',
          ancient_basis: '《三命通会》：咸池主淫，合则主婚',
          weight: 0.3,
          activation_conditions: ['合化', '刑冲']
        }
      },

      // 升职相关神煞
      promotion_gods: {
        heavenly_noble: {
          name: '天乙贵人',
          calculation: {
            jia_wu: ['chou', 'wei'], yi_ji: ['zi', 'shen'],
            bing_ding: ['hai', 'you'], wu_ji: ['chou', 'wei'],
            geng_xin: ['yin', 'wu'], ren_gui: ['mao', 'si']
          },
          description: '主贵人相助，升职有望',
          ancient_basis: '《滴天髓》：天乙贵人最吉祥，命中遇此福非常',
          weight: 0.9,
          activation_conditions: ['透干', '坐支', '三合']
        },
        general_star: {
          name: '将星',
          calculation: {
            yin_wu_xu: 'wu',  // 寅午戌年生人见午
            shen_zi_chen: 'zi', // 申子辰年生人见子
            si_you_chou: 'you', // 巳酉丑年生人见酉
            hai_mao_wei: 'mao'  // 亥卯未年生人见卯
          },
          description: '主权威领导，将星入命主升职',
          ancient_basis: '《三命通会》：将星文武两相宜，禄重权高足可知',
          weight: 0.8,
          activation_conditions: ['透干', '得地', '生旺']
        },
        post_horse: {
          name: '驿马',
          calculation: {
            shen_zi_chen: 'yin', // 申子辰见寅
            yin_wu_xu: 'shen',   // 寅午戌见申
            si_you_chou: 'hai',  // 巳酉丑见亥
            hai_mao_wei: 'si'    // 亥卯未见巳
          },
          description: '主变动迁移，驿马动主升迁',
          ancient_basis: '《渊海子平》：驿马入命主奔波，逢冲遇合主升迁',
          weight: 0.6,
          activation_conditions: ['冲动', '合化', '透干']
        },
        literary_star: {
          name: '文昌',
          calculation: {
            jia: 'si', yi: 'wu', bing: 'shen', ding: 'you',
            wu: 'shen', ji: 'you', geng: 'hai', xin: 'zi',
            ren: 'yin', gui: 'mao'
          },
          description: '主文化学识，文昌入命利考试升职',
          ancient_basis: '《三命通会》：文昌入命聪明过人，文章振发',
          weight: 0.7,
          activation_conditions: ['透干', '坐支', '印星相配']
        }
      },

      // 生育相关神煞
      childbirth_gods: {
        child_star: {
          name: '子星',
          calculation: 'based_on_food_injury', // 基于食伤星
          description: '主子女缘分，食伤为子女星',
          ancient_basis: '《渊海子平》：男命官杀为子，女命食伤为子',
          weight: 0.8,
          activation_conditions: ['透干', '得地', '通根']
        },
        child_palace: {
          name: '子女宫',
          calculation: 'time_pillar', // 时柱为子女宫
          description: '时柱为子女宫，逢冲合主生育',
          ancient_basis: '《滴天髓》：时为子息宫，冲则动而有子',
          weight: 0.7,
          activation_conditions: ['冲动', '合化', '填实']
        },
        heavenly_joy_child: {
          name: '天喜入子女宫',
          calculation: 'heavenly_joy_in_time_pillar',
          description: '天喜入时柱主生育喜庆',
          ancient_basis: '《三命通会》：天喜临子女宫，必主得子之喜',
          weight: 0.6,
          activation_conditions: ['天喜入时', '食伤透干']
        }
      },

      // 财运相关神煞
      wealth_gods: {
        wealth_star: {
          name: '财星',
          calculation: 'based_on_five_elements', // 基于五行生克
          description: '日主所克为财，财星透干主财运',
          ancient_basis: '《滴天髓》：财为养命之源，透干有根主富',
          weight: 0.9,
          activation_conditions: ['透干', '通根', '得地']
        },
        wealth_vault: {
          name: '财库',
          calculation: {
            wood_wealth: 'xu', // 木财库戌
            fire_wealth: 'chou', // 火财库丑
            earth_wealth: 'chen', // 土财库辰
            metal_wealth: 'wei', // 金财库未
            water_wealth: 'xu'  // 水财库戌
          },
          description: '财库主积蓄，财库开启主发财',
          ancient_basis: '《三命通会》：财库充盈，富贵可期',
          weight: 0.7,
          activation_conditions: ['库开', '冲动', '透干']
        },
        heavenly_wealth: {
          name: '天财',
          calculation: {
            jia: 'yin', yi: 'mao', bing: 'chen', ding: 'si',
            wu: 'wu', ji: 'wei', geng: 'shen', xin: 'you',
            ren: 'xu', gui: 'hai'
          },
          description: '天财星主横财偏财',
          ancient_basis: '《渊海子平》：天财入命主意外之财',
          weight: 0.5,
          activation_conditions: ['透干', '合化']
        }
      }
    };
  }

  /**
   * 初始化病药映射表（基于《滴天髓》）
   */
  initializeMedicineMappingTable() {
    return {
      marriage: {
        male: {
          diseases: {
            bi_jie_duo_cai: {
              name: '比劫夺财',
              description: '男命比劫旺而夺财，阻碍婚姻',
              detection_conditions: ['比劫透干', '比劫坐旺', '财星受克'],
              severity_calculation: 'bi_jie_power / cai_star_power',
              ancient_basis: '《滴天髓》：比劫夺财，妻财两失'
            },
            cai_xing_xu_ruo: {
              name: '财星虚弱',
              description: '男命财星无根或受克，婚姻不利',
              detection_conditions: ['财星无根', '财星受克', '财星入墓'],
              severity_calculation: '1 - cai_star_power',
              ancient_basis: '《三命通会》：财轻比重，克妻之命'
            }
          },
          medicines: {
            guan_sha_zhi_bi_jie: {
              name: '官杀制比劫',
              description: '官杀透干制比劫，解除夺财之病',
              effectiveness_conditions: ['官杀透干', '官杀有根', '制化有情'],
              dosage_calculation: 'guan_sha_power / bi_jie_power',
              ancient_basis: '《滴天髓》：官星制劫，妻财两得',
              optimal_timing: ['官杀透干之年', '官杀坐旺之运']
            },
            yin_xing_hua_sha: {
              name: '印星化杀',
              description: '印星化杀生身，间接保护财星',
              effectiveness_conditions: ['印星透干', '杀印相生', '身弱用印'],
              dosage_calculation: 'yin_xing_power * 0.8',
              ancient_basis: '《渊海子平》：杀印相生，富贵双全'
            }
          }
        },
        female: {
          diseases: {
            shang_guan_ke_guan: {
              name: '伤官克官',
              description: '女命伤官旺而克官，阻碍婚姻',
              detection_conditions: ['伤官透干', '伤官坐旺', '官星受克'],
              severity_calculation: 'shang_guan_power / guan_xing_power',
              ancient_basis: '《滴天髓》：伤官见官，为祸百端'
            },
            guan_sha_hun_za: {
              name: '官杀混杂',
              description: '女命官杀并见，婚姻不稳',
              detection_conditions: ['官杀同透', '官杀无制', '官杀争合'],
              severity_calculation: 'min(guan_power, sha_power)',
              ancient_basis: '《三命通会》：官杀混杂，必主再嫁'
            }
          },
          medicines: {
            yin_xing_zhi_shang_guan: {
              name: '印星制伤官',
              description: '印星透干制伤官，保护官星',
              effectiveness_conditions: ['印星透干', '印星有根', '伤官得制'],
              dosage_calculation: 'yin_xing_power / shang_guan_power',
              ancient_basis: '《滴天髓》：伤官佩印，贵不可言',
              optimal_timing: ['印星透干之年', '印星坐旺之运']
            },
            cai_xing_sun_yin: {
              name: '财星损印',
              description: '财星制印，间接制伤官',
              effectiveness_conditions: ['财星透干', '财印相战', '伤官过旺'],
              dosage_calculation: 'cai_xing_power * 0.6',
              ancient_basis: '《渊海子平》：财印相战，制化有情'
            }
          }
        }
      },

      promotion: {
        common: {
          diseases: {
            guan_ruo_wu_yin: {
              name: '官弱无印',
              description: '官星虚弱无印星生扶，升职困难',
              detection_conditions: ['官星无根', '印星不现', '身旺官弱'],
              severity_calculation: '0.5 - guan_xing_power',
              ancient_basis: '《滴天髓》：官轻印轻，难任重职'
            },
            sha_qiang_wu_zhi: {
              name: '杀强无制',
              description: '七杀过旺无制化，权威受损',
              detection_conditions: ['七杀透干', '七杀坐旺', '无印无食制'],
              severity_calculation: 'qi_sha_power - 0.6',
              ancient_basis: '《三命通会》：杀无制化，必主凶灾'
            },
            shang_guan_ke_guan: {
              name: '伤官克官',
              description: '伤官克制官星，仕途不利',
              detection_conditions: ['伤官透干', '官星受克', '无印制伤'],
              severity_calculation: 'shang_guan_power / guan_xing_power',
              ancient_basis: '《滴天髓》：伤官见官，仕途蹭蹬'
            }
          },
          medicines: {
            yin_xing_sheng_guan: {
              name: '印星生官',
              description: '印星透干生扶官星，利于升职',
              effectiveness_conditions: ['印星透干', '印星有根', '官印相生'],
              dosage_calculation: 'yin_xing_power * guan_xing_power',
              ancient_basis: '《滴天髓》：官印相生，富贵双全',
              optimal_timing: ['印星透干之年', '官印相生之运']
            },
            cai_xing_sheng_guan: {
              name: '财星生官',
              description: '财星生官，财官相生利升职',
              effectiveness_conditions: ['财星透干', '财星有根', '财官相生'],
              dosage_calculation: 'cai_xing_power * 0.8',
              ancient_basis: '《三命通会》：财官相生，富贵可期'
            },
            shi_shen_zhi_sha: {
              name: '食神制杀',
              description: '食神制七杀，化杀为权',
              effectiveness_conditions: ['食神透干', '食神有根', '制杀有情'],
              dosage_calculation: 'shi_shen_power / qi_sha_power',
              ancient_basis: '《渊海子平》：食神制杀，化杀为权'
            }
          }
        }
      },

      childbirth: {
        common: {
          diseases: {
            shi_shang_xu_ruo: {
              name: '食伤虚弱',
              description: '食伤星虚弱，子女缘薄',
              detection_conditions: ['食伤无根', '食伤受克', '食伤入墓'],
              severity_calculation: '0.3 - shi_shang_power',
              ancient_basis: '《滴天髓》：食伤为子，弱则子迟'
            },
            zi_xi_gong_kong: {
              name: '子息宫空',
              description: '时柱空亡，子女宫虚',
              detection_conditions: ['时柱空亡', '时支受冲', '时干无根'],
              severity_calculation: 'kong_wang_severity',
              ancient_basis: '《三命通会》：时空子息宫，难得子嗣'
            }
          },
          medicines: {
            yin_xing_sheng_shi_shang: {
              name: '印星生食伤',
              description: '印星生扶食伤，利于生育',
              effectiveness_conditions: ['印星透干', '印星有根', '印食相生'],
              dosage_calculation: 'yin_xing_power * shi_shang_power',
              ancient_basis: '《滴天髓》：印绶生身，食伤得用',
              optimal_timing: ['印星透干之年', '食伤得地之运']
            },
            shui_mu_xiang_sheng: {
              name: '水木相生',
              description: '水木相生，子息昌隆',
              effectiveness_conditions: ['水木透干', '水木有根', '水木流通'],
              dosage_calculation: 'shui_power * mu_power',
              ancient_basis: '《渊海子平》：水暖木荣，子息昌隆'
            }
          }
        }
      },

      wealth: {
        common: {
          diseases: {
            cai_ruo_bi_qiang: {
              name: '财弱比强',
              description: '财星虚弱比劫旺，求财困难',
              detection_conditions: ['财星无根', '比劫透干', '比劫夺财'],
              severity_calculation: 'bi_jie_power / cai_xing_power',
              ancient_basis: '《滴天髓》：财轻劫重，求财辛苦'
            },
            cai_ku_jin_suo: {
              name: '财库紧锁',
              description: '财库不开，财富难聚',
              detection_conditions: ['财库不透', '财库无冲', '财库受制'],
              severity_calculation: '1 - cai_ku_openness',
              ancient_basis: '《三命通会》：财库深藏，难以发达'
            }
          },
          medicines: {
            guan_sha_zhi_bi_jie: {
              name: '官杀制比劫',
              description: '官杀制比劫，保护财星',
              effectiveness_conditions: ['官杀透干', '官杀有根', '制劫有力'],
              dosage_calculation: 'guan_sha_power / bi_jie_power',
              ancient_basis: '《滴天髓》：官星制劫，财源广进',
              optimal_timing: ['官杀透干之年', '制劫得力之运']
            },
            shi_shang_sheng_cai: {
              name: '食伤生财',
              description: '食伤生财，财源滚滚',
              effectiveness_conditions: ['食伤透干', '食伤有根', '食财相生'],
              dosage_calculation: 'shi_shang_power * cai_xing_power',
              ancient_basis: '《渊海子平》：食伤生财，富贵可期'
            },
            cai_ku_feng_chong: {
              name: '财库逢冲',
              description: '财库逢冲开启，财富涌现',
              effectiveness_conditions: ['财库透干', '财库逢冲', '冲开有情'],
              dosage_calculation: 'chong_power * cai_ku_strength',
              ancient_basis: '《三命通会》：库逢冲而发，财源大开'
            }
          }
        }
      }
    };
  }

  /**
   * 初始化季节调整
   */
  initializeSeasonalAdjustments() {
    return {
      spring: {
        wood_bonus: 0.2,
        fire_bonus: 0.1,
        earth_penalty: -0.1,
        metal_penalty: -0.2,
        water_neutral: 0.0,
        marriage_adjustment: 0.1, // 春季婚姻权重+10%
        description: '春季木旺，生发之气盛'
      },
      summer: {
        fire_bonus: 0.2,
        earth_bonus: 0.1,
        metal_penalty: -0.1,
        water_penalty: -0.2,
        wood_neutral: 0.0,
        promotion_adjustment: 0.15, // 夏季升职权重+15%
        description: '夏季火旺，向上之势强'
      },
      autumn: {
        metal_bonus: 0.2,
        water_bonus: 0.1,
        wood_penalty: -0.1,
        fire_penalty: -0.2,
        earth_neutral: 0.0,
        wealth_adjustment: 0.2, // 秋季财运权重+20%
        description: '秋季金旺，收获之时'
      },
      winter: {
        water_bonus: 0.2,
        wood_bonus: 0.1,
        fire_penalty: -0.1,
        earth_penalty: -0.2,
        metal_neutral: 0.0,
        childbirth_adjustment: 0.1, // 冬季生育权重+10%
        description: '冬季水旺，蓄藏之期'
      }
    };
  }

  /**
   * 检测神煞
   * @param {Object} bazi - 八字数据
   * @param {string} eventType - 事件类型
   * @returns {Array} 检测到的神煞
   */
  detectGods(bazi, eventType) {
    const detectedGods = [];
    const eventGods = this.godsTable[`${eventType}_gods`];
    
    if (!eventGods) return detectedGods;

    for (const [godKey, godData] of Object.entries(eventGods)) {
      const detection = this.detectSingleGod(bazi, godData);
      if (detection.detected) {
        detectedGods.push({
          name: godData.name,
          key: godKey,
          weight: godData.weight,
          activation_level: detection.activation_level,
          ancient_basis: godData.ancient_basis,
          description: godData.description
        });
      }
    }

    return detectedGods.sort((a, b) => b.weight - a.weight);
  }

  /**
   * 检测单个神煞
   */
  detectSingleGod(bazi, godData) {
    // 简化实现，实际应该根据具体神煞计算方法
    const detection = {
      detected: false,
      activation_level: 0
    };

    // 这里应该实现具体的神煞检测逻辑
    // 例如红鸾星的检测
    if (godData.name === '红鸾') {
      const yearBranch = bazi.year_pillar?.earthly;
      const redPhoenixBranch = godData.calculation[yearBranch];
      
      // 检查各柱是否有红鸾星
      const pillars = [bazi.month_pillar, bazi.day_pillar, bazi.time_pillar];
      for (const pillar of pillars) {
        if (pillar?.earthly === redPhoenixBranch) {
          detection.detected = true;
          detection.activation_level += 0.3;
        }
      }
    }

    return detection;
  }

  /**
   * 分析病药关系
   * @param {Object} bazi - 八字数据
   * @param {string} eventType - 事件类型
   * @param {string} gender - 性别
   * @returns {Object} 病药分析结果
   */
  analyzeDiseaseAndMedicine(bazi, eventType, gender) {
    const eventMapping = this.medicineMappingTable[eventType];
    if (!eventMapping) return null;

    const genderMapping = eventMapping[gender] || eventMapping.common;
    if (!genderMapping) return null;

    const analysis = {
      diseases: [],
      medicines: [],
      balance_score: 0,
      treatment_recommendations: []
    };

    // 检测病神
    for (const [diseaseKey, diseaseData] of Object.entries(genderMapping.diseases)) {
      const severity = this.calculateDiseaseSeverity(bazi, diseaseData);
      if (severity > 0.1) { // 病神严重度阈值
        analysis.diseases.push({
          name: diseaseData.name,
          key: diseaseKey,
          severity: severity,
          description: diseaseData.description,
          ancient_basis: diseaseData.ancient_basis
        });
      }
    }

    // 检测药神
    for (const [medicineKey, medicineData] of Object.entries(genderMapping.medicines)) {
      const effectiveness = this.calculateMedicineEffectiveness(bazi, medicineData);
      if (effectiveness > 0.1) { // 药神有效性阈值
        analysis.medicines.push({
          name: medicineData.name,
          key: medicineKey,
          effectiveness: effectiveness,
          description: medicineData.description,
          ancient_basis: medicineData.ancient_basis,
          optimal_timing: medicineData.optimal_timing
        });
      }
    }

    // 计算病药平衡分数
    analysis.balance_score = this.calculateBalanceScore(analysis.diseases, analysis.medicines);

    // 生成治疗建议
    analysis.treatment_recommendations = this.generateTreatmentRecommendations(analysis);

    return analysis;
  }

  /**
   * 计算病神严重度
   */
  calculateDiseaseSeverity(bazi, diseaseData) {
    // 简化实现，实际应该根据具体病神类型计算
    // 这里返回一个示例值
    return Math.random() * 0.8; // 0-0.8的随机严重度
  }

  /**
   * 计算药神有效性
   */
  calculateMedicineEffectiveness(bazi, medicineData) {
    // 简化实现，实际应该根据具体药神类型计算
    // 这里返回一个示例值
    return Math.random() * 0.9; // 0-0.9的随机有效性
  }

  /**
   * 计算病药平衡分数
   */
  calculateBalanceScore(diseases, medicines) {
    if (diseases.length === 0) return 1.0; // 无病则健康

    const totalDiseaseSeverity = diseases.reduce((sum, d) => sum + d.severity, 0);
    const totalMedicineEffectiveness = medicines.reduce((sum, m) => sum + m.effectiveness, 0);

    // 平衡分数 = 药神有效性 / (病神严重度 + 药神有效性)
    return totalMedicineEffectiveness / (totalDiseaseSeverity + totalMedicineEffectiveness);
  }

  /**
   * 生成治疗建议
   */
  generateTreatmentRecommendations(analysis) {
    const recommendations = [];

    // 基于病神生成建议
    analysis.diseases.forEach(disease => {
      recommendations.push({
        type: 'disease_treatment',
        priority: 'high',
        recommendation: `针对${disease.name}，需要寻找相应的制化之神`,
        ancient_wisdom: disease.ancient_basis
      });
    });

    // 基于药神生成建议
    analysis.medicines.forEach(medicine => {
      if (medicine.optimal_timing) {
        recommendations.push({
          type: 'medicine_timing',
          priority: 'medium',
          recommendation: `${medicine.name}的最佳应期：${medicine.optimal_timing.join('、')}`,
          ancient_wisdom: medicine.ancient_basis
        });
      }
    });

    return recommendations;
  }

  /**
   * 获取神煞表状态
   */
  getTablesStatus() {
    return {
      gods_categories: Object.keys(this.godsTable).length,
      medicine_mappings: Object.keys(this.medicineMappingTable).length,
      seasonal_adjustments: Object.keys(this.seasonalAdjustments).length,
      features: {
        gods_detection: true,
        disease_medicine_analysis: true,
        seasonal_adjustments: true,
        ancient_basis_included: true
      }
    };
  }
}

module.exports = GodsAndMedicineTables;
