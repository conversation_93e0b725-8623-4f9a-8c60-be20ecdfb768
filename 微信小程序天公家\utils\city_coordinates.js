// utils/city_coordinates.js
// 中国主要城市经纬度数据库 - 用于真太阳时精确计算

/**
 * 中国主要城市坐标数据库
 * 数据来源：国家测绘局标准坐标
 * 坐标系：WGS84（与GPS一致）
 */
const CITY_COORDINATES = {
  // 直辖市
  "北京": { longitude: 116.4074, latitude: 39.9042, timezone: 8 },
  "上海": { longitude: 121.4737, latitude: 31.2304, timezone: 8 },
  "天津": { longitude: 117.1901, latitude: 39.1084, timezone: 8 },
  "重庆": { longitude: 106.5516, latitude: 29.5630, timezone: 8 },

  // 省会城市
  "广州": { longitude: 113.2644, latitude: 23.1291, timezone: 8 },
  "深圳": { longitude: 114.0579, latitude: 22.5431, timezone: 8 },
  "成都": { longitude: 104.0668, latitude: 30.5728, timezone: 8 },
  "杭州": { longitude: 120.1551, latitude: 30.2741, timezone: 8 },
  "武汉": { longitude: 114.3054, latitude: 30.5931, timezone: 8 },
  "西安": { longitude: 108.9402, latitude: 34.3416, timezone: 8 },
  "南京": { longitude: 118.7969, latitude: 32.0603, timezone: 8 },
  "青岛": { longitude: 120.3826, latitude: 36.0671, timezone: 8 },
  "大连": { longitude: 121.6147, latitude: 38.9140, timezone: 8 },
  "宁波": { longitude: 121.5440, latitude: 29.8683, timezone: 8 },
  "厦门": { longitude: 118.1689, latitude: 24.4797, timezone: 8 },

  // 东北地区
  "哈尔滨": { longitude: 126.6394, latitude: 45.7566, timezone: 8 },
  "长春": { longitude: 125.3245, latitude: 43.8171, timezone: 8 },
  "沈阳": { longitude: 123.4315, latitude: 41.8057, timezone: 8 },

  // 华北地区
  "石家庄": { longitude: 114.5149, latitude: 38.0428, timezone: 8 },
  "太原": { longitude: 112.5489, latitude: 37.8706, timezone: 8 },
  "呼和浩特": { longitude: 111.7519, latitude: 40.8414, timezone: 8 },

  // 华东地区
  "济南": { longitude: 117.1205, latitude: 36.6519, timezone: 8 },
  "合肥": { longitude: 117.2272, latitude: 31.8206, timezone: 8 },
  "福州": { longitude: 119.2965, latitude: 26.0745, timezone: 8 },
  "南昌": { longitude: 115.8921, latitude: 28.6765, timezone: 8 },

  // 华中地区
  "郑州": { longitude: 113.6254, latitude: 34.7466, timezone: 8 },
  "长沙": { longitude: 112.9388, latitude: 28.2282, timezone: 8 },

  // 华南地区
  "南宁": { longitude: 108.3669, latitude: 22.8170, timezone: 8 },
  "海口": { longitude: 110.3312, latitude: 20.0311, timezone: 8 },

  // 西南地区
  "昆明": { longitude: 102.8329, latitude: 24.8801, timezone: 8 },
  "贵阳": { longitude: 106.6302, latitude: 26.6477, timezone: 8 },
  "拉萨": { longitude: 91.1409, latitude: 29.6456, timezone: 8 },

  // 西北地区
  "兰州": { longitude: 103.8236, latitude: 36.0581, timezone: 8 },
  "西宁": { longitude: 101.7782, latitude: 36.6171, timezone: 8 },
  "银川": { longitude: 106.2309, latitude: 38.4872, timezone: 8 },
  "乌鲁木齐": { longitude: 87.6168, latitude: 43.8256, timezone: 8 },

  // 港澳台地区
  "香港": { longitude: 114.1694, latitude: 22.3193, timezone: 8 },
  "澳门": { longitude: 113.5491, latitude: 22.1987, timezone: 8 },
  "台北": { longitude: 121.5654, latitude: 25.0330, timezone: 8 },

  // 江苏省城市
  "苏州": { longitude: 120.6519, latitude: 31.3989, timezone: 8 },
  "无锡": { longitude: 120.3019, latitude: 31.5747, timezone: 8 },
  "徐州": { longitude: 117.1851, latitude: 34.2618, timezone: 8 },
  "常州": { longitude: 119.9772, latitude: 31.7728, timezone: 8 },
  "扬州": { longitude: 119.4216, latitude: 32.3932, timezone: 8 },
  "南通": { longitude: 120.8644, latitude: 32.0162, timezone: 8 },
  "连云港": { longitude: 119.1781, latitude: 34.5996, timezone: 8 },
  "淮安": { longitude: 119.0153, latitude: 33.5975, timezone: 8 },
  "盐城": { longitude: 120.1398, latitude: 33.3776, timezone: 8 },
  "泰州": { longitude: 119.9153, latitude: 32.4849, timezone: 8 },
  "镇江": { longitude: 119.4763, latitude: 32.2044, timezone: 8 },
  "宿迁": { longitude: 118.3013, latitude: 33.9630, timezone: 8 },
  "江阴": { longitude: 120.2659, latitude: 31.9111, timezone: 8 },
  "宜兴": { longitude: 119.8244, latitude: 31.3408, timezone: 8 },
  "邳州": { longitude: 118.0130, latitude: 34.3364, timezone: 8 },
  "新沂": { longitude: 118.3520, latitude: 34.3687, timezone: 8 },
  "金坛": { longitude: 119.5656, latitude: 31.7235, timezone: 8 },
  "溧阳": { longitude: 119.4840, latitude: 31.4301, timezone: 8 },
  "如皋": { longitude: 120.5653, latitude: 32.3746, timezone: 8 },
  "通州": { longitude: 121.0731, latitude: 32.0679, timezone: 8 },
  "海门": { longitude: 121.1815, latitude: 31.8946, timezone: 8 },
  "启东": { longitude: 121.6574, latitude: 31.8081, timezone: 8 },
  "大丰": { longitude: 120.4675, latitude: 33.2005, timezone: 8 },
  "高邮": { longitude: 119.4330, latitude: 32.7944, timezone: 8 },
  "仪征": { longitude: 119.1803, latitude: 32.2741, timezone: 8 },
  "兴化": { longitude: 119.8376, latitude: 32.9396, timezone: 8 },
  "靖江": { longitude: 120.2692, latitude: 32.0168, timezone: 8 },
  "泰兴": { longitude: 120.0517, latitude: 32.1687, timezone: 8 },
  "姜堰": { longitude: 120.1484, latitude: 32.5078, timezone: 8 },
  "丹阳": { longitude: 119.5707, latitude: 32.0107, timezone: 8 },
  "扬中": { longitude: 119.7977, latitude: 32.2373, timezone: 8 },
  "句容": { longitude: 119.1647, latitude: 31.9445, timezone: 8 },

  // 浙江省城市
  "温州": { longitude: 120.6994, latitude: 27.9944, timezone: 8 },
  "嘉兴": { longitude: 120.7554, latitude: 30.7469, timezone: 8 },
  "台州": { longitude: 121.4286, latitude: 28.6561, timezone: 8 },
  "金华": { longitude: 119.6492, latitude: 29.0895, timezone: 8 },
  "绍兴": { longitude: 120.5820, latitude: 30.0023, timezone: 8 },
  "湖州": { longitude: 120.0867, latitude: 30.8703, timezone: 8 },
  "衢州": { longitude: 118.8718, latitude: 28.9417, timezone: 8 },
  "舟山": { longitude: 122.2070, latitude: 30.0360, timezone: 8 },
  "丽水": { longitude: 119.9215, latitude: 28.4517, timezone: 8 },
  "义乌": { longitude: 120.0750, latitude: 29.3063, timezone: 8 },
  "乐清": { longitude: 120.9817, latitude: 28.1240, timezone: 8 },
  "瑞安": { longitude: 120.6565, latitude: 27.7803, timezone: 8 },
  "永康": { longitude: 120.0470, latitude: 28.8882, timezone: 8 },
  "东阳": { longitude: 120.2419, latitude: 29.2714, timezone: 8 },
  "兰溪": { longitude: 119.4608, latitude: 29.2087, timezone: 8 },
  "诸暨": { longitude: 120.2441, latitude: 29.7138, timezone: 8 },
  "上虞": { longitude: 120.8682, latitude: 30.0333, timezone: 8 },
  "嵊州": { longitude: 120.8306, latitude: 29.5951, timezone: 8 },
  "新昌": { longitude: 120.9033, latitude: 29.4996, timezone: 8 },
  "德清": { longitude: 119.9777, latitude: 30.5433, timezone: 8 },
  "安吉": { longitude: 119.6800, latitude: 30.6346, timezone: 8 },
  "长兴": { longitude: 119.9107, latitude: 31.0258, timezone: 8 },
  "海宁": { longitude: 120.6800, latitude: 30.5097, timezone: 8 },
  "桐乡": { longitude: 120.5649, latitude: 30.6303, timezone: 8 },
  "平湖": { longitude: 121.0146, latitude: 30.6706, timezone: 8 },
  "海盐": { longitude: 120.9465, latitude: 30.5257, timezone: 8 },
  "嘉善": { longitude: 120.9257, latitude: 30.8408, timezone: 8 },
  "慈溪": { longitude: 121.2665, latitude: 30.1699, timezone: 8 },
  "余姚": { longitude: 121.1544, latitude: 30.0388, timezone: 8 },
  "奉化": { longitude: 121.4070, latitude: 29.6550, timezone: 8 },
  "象山": { longitude: 121.8687, latitude: 29.4777, timezone: 8 },
  "宁海": { longitude: 121.4307, latitude: 29.2888, timezone: 8 },
  "临海": { longitude: 121.1390, latitude: 28.8426, timezone: 8 },
  "温岭": { longitude: 121.3859, latitude: 28.3723, timezone: 8 },
  "玉环": { longitude: 121.2317, latitude: 28.1363, timezone: 8 },
  "三门": { longitude: 121.3953, latitude: 29.1053, timezone: 8 },
  "天台": { longitude: 121.0060, latitude: 29.1441, timezone: 8 },
  "仙居": { longitude: 120.7284, latitude: 28.8477, timezone: 8 },
  "江山": { longitude: 118.6267, latitude: 28.7372, timezone: 8 },
  "常山": { longitude: 118.5110, latitude: 28.9016, timezone: 8 },
  "开化": { longitude: 118.4157, latitude: 29.1374, timezone: 8 },
  "龙游": { longitude: 119.1717, latitude: 29.0281, timezone: 8 },
  "青田": { longitude: 120.2893, latitude: 28.1351, timezone: 8 },
  "缙云": { longitude: 120.0910, latitude: 28.6596, timezone: 8 },
  "遂昌": { longitude: 119.2761, latitude: 28.5927, timezone: 8 },
  "松阳": { longitude: 119.4817, latitude: 28.4493, timezone: 8 },
  "云和": { longitude: 119.5733, latitude: 28.1168, timezone: 8 },
  "庆元": { longitude: 119.0626, latitude: 27.6186, timezone: 8 },
  "景宁": { longitude: 119.6357, latitude: 27.9739, timezone: 8 },

  // 广东省城市
  "佛山": { longitude: 113.1220, latitude: 23.0291, timezone: 8 },
  "东莞": { longitude: 113.7518, latitude: 23.0489, timezone: 8 },
  "中山": { longitude: 113.3823, latitude: 22.5211, timezone: 8 },
  "珠海": { longitude: 113.5767, latitude: 22.2707, timezone: 8 },
  "惠州": { longitude: 114.4152, latitude: 23.1115, timezone: 8 },
  "江门": { longitude: 113.0946, latitude: 22.5901, timezone: 8 },
  "汕头": { longitude: 116.7081, latitude: 23.3540, timezone: 8 },
  "湛江": { longitude: 110.3594, latitude: 21.2707, timezone: 8 },
  "肇庆": { longitude: 112.4721, latitude: 23.0519, timezone: 8 },
  "茂名": { longitude: 110.9255, latitude: 21.6687, timezone: 8 },
  "韶关": { longitude: 113.5972, latitude: 24.8029, timezone: 8 },
  "梅州": { longitude: 116.1255, latitude: 24.2886, timezone: 8 },
  "河源": { longitude: 114.6974, latitude: 23.7572, timezone: 8 },
  "清远": { longitude: 113.0518, latitude: 23.6817, timezone: 8 },
  "阳江": { longitude: 111.9827, latitude: 21.8571, timezone: 8 },
  "潮州": { longitude: 116.6302, latitude: 23.6618, timezone: 8 },
  "揭阳": { longitude: 116.3729, latitude: 23.5479, timezone: 8 },
  "云浮": { longitude: 112.0446, latitude: 22.9379, timezone: 8 },
  "汕尾": { longitude: 115.3648, latitude: 22.7864, timezone: 8 },

  // 山东省城市
  "烟台": { longitude: 121.3914, latitude: 37.5393, timezone: 8 },
  "潍坊": { longitude: 119.1070, latitude: 36.7093, timezone: 8 },
  "临沂": { longitude: 118.3269, latitude: 35.1045, timezone: 8 },
  "淄博": { longitude: 118.0371, latitude: 36.8113, timezone: 8 },
  "济宁": { longitude: 116.5874, latitude: 35.4154, timezone: 8 },
  "泰安": { longitude: 117.1289, latitude: 36.1948, timezone: 8 },
  "聊城": { longitude: 115.9851, latitude: 36.4560, timezone: 8 },
  "威海": { longitude: 122.1201, latitude: 37.5097, timezone: 8 },
  "枣庄": { longitude: 117.5578, latitude: 34.8564, timezone: 8 },
  "德州": { longitude: 116.3073, latitude: 37.4513, timezone: 8 },
  "日照": { longitude: 119.4610, latitude: 35.4164, timezone: 8 },
  "东营": { longitude: 118.6748, latitude: 37.4341, timezone: 8 },
  "菏泽": { longitude: 115.4697, latitude: 35.2465, timezone: 8 },
  "滨州": { longitude: 118.0371, latitude: 37.3835, timezone: 8 },
  "莱芜": { longitude: 117.6526, latitude: 36.2045, timezone: 8 },

  // 河南省城市
  "洛阳": { longitude: 112.4540, latitude: 34.6197, timezone: 8 },
  "开封": { longitude: 114.3411, latitude: 34.7971, timezone: 8 },
  "新乡": { longitude: 113.9268, latitude: 35.3030, timezone: 8 },
  "焦作": { longitude: 113.2418, latitude: 35.2158, timezone: 8 },
  "安阳": { longitude: 114.3927, latitude: 36.1102, timezone: 8 },
  "南阳": { longitude: 112.5404, latitude: 32.9909, timezone: 8 },
  "许昌": { longitude: 113.8260, latitude: 34.0229, timezone: 8 },
  "平顶山": { longitude: 113.3073, latitude: 33.7453, timezone: 8 },
  "信阳": { longitude: 114.0757, latitude: 32.1470, timezone: 8 },
  "周口": { longitude: 114.6496, latitude: 33.6204, timezone: 8 },
  "商丘": { longitude: 115.6506, latitude: 34.4138, timezone: 8 },
  "驻马店": { longitude: 114.0220, latitude: 32.9800, timezone: 8 },
  "漯河": { longitude: 114.0460, latitude: 33.5758, timezone: 8 },
  "濮阳": { longitude: 115.0410, latitude: 35.7617, timezone: 8 },
  "鹤壁": { longitude: 114.2951, latitude: 35.7554, timezone: 8 },
  "三门峡": { longitude: 111.2008, latitude: 34.7833, timezone: 8 },
  "济源": { longitude: 112.6016, latitude: 35.0904, timezone: 8 },

  // 河北省城市
  "唐山": { longitude: 118.2020, latitude: 39.6304, timezone: 8 },
  "保定": { longitude: 115.4648, latitude: 38.8971, timezone: 8 },
  "邯郸": { longitude: 114.4775, latitude: 36.6256, timezone: 8 },
  "邢台": { longitude: 114.5086, latitude: 37.0682, timezone: 8 },
  "沧州": { longitude: 116.8579, latitude: 38.2971, timezone: 8 },
  "衡水": { longitude: 115.6656, latitude: 37.7161, timezone: 8 },
  "廊坊": { longitude: 116.7046, latitude: 39.5237, timezone: 8 },
  "承德": { longitude: 117.9635, latitude: 40.9925, timezone: 8 },
  "张家口": { longitude: 114.8794, latitude: 40.8118, timezone: 8 },
  "秦皇岛": { longitude: 119.6004, latitude: 39.9398, timezone: 8 },

  // 湖北省城市
  "宜昌": { longitude: 111.3209, latitude: 30.7327, timezone: 8 },
  "襄阳": { longitude: 112.1441, latitude: 32.0420, timezone: 8 },
  "荆州": { longitude: 112.2390, latitude: 30.3269, timezone: 8 },
  "十堰": { longitude: 110.7848, latitude: 32.6470, timezone: 8 },
  "黄石": { longitude: 115.0770, latitude: 30.2200, timezone: 8 },
  "孝感": { longitude: 113.9260, latitude: 30.9240, timezone: 8 },
  "黄冈": { longitude: 114.8794, latitude: 30.4477, timezone: 8 },
  "恩施": { longitude: 109.4889, latitude: 30.2830, timezone: 8 },
  "咸宁": { longitude: 114.3221, latitude: 29.8418, timezone: 8 },
  "荆门": { longitude: 112.2047, latitude: 31.0354, timezone: 8 },
  "鄂州": { longitude: 114.8946, latitude: 30.3844, timezone: 8 },
  "随州": { longitude: 113.3741, latitude: 31.7177, timezone: 8 },
  "潜江": { longitude: 112.8969, latitude: 30.4210, timezone: 8 },
  "天门": { longitude: 113.1660, latitude: 30.6531, timezone: 8 },
  "仙桃": { longitude: 113.4539, latitude: 30.3648, timezone: 8 },

  // 湖南省城市
  "株洲": { longitude: 113.1518, latitude: 27.8274, timezone: 8 },
  "湘潭": { longitude: 112.9445, latitude: 27.8294, timezone: 8 },
  "衡阳": { longitude: 112.6072, latitude: 26.8934, timezone: 8 },
  "邵阳": { longitude: 111.4681, latitude: 27.2368, timezone: 8 },
  "岳阳": { longitude: 113.1287, latitude: 29.3570, timezone: 8 },
  "常德": { longitude: 111.6990, latitude: 29.0319, timezone: 8 },
  "张家界": { longitude: 110.4790, latitude: 29.1274, timezone: 8 },
  "益阳": { longitude: 112.3550, latitude: 28.5701, timezone: 8 },
  "郴州": { longitude: 113.0322, latitude: 25.7706, timezone: 8 },
  "永州": { longitude: 111.6139, latitude: 26.4207, timezone: 8 },
  "怀化": { longitude: 109.9980, latitude: 27.5501, timezone: 8 },
  "娄底": { longitude: 112.0085, latitude: 27.7281, timezone: 8 },
  "湘西": { longitude: 109.7390, latitude: 28.3144, timezone: 8 },

  // 安徽省城市
  "芜湖": { longitude: 118.3760, latitude: 31.3262, timezone: 8 },
  "蚌埠": { longitude: 117.3889, latitude: 32.9164, timezone: 8 },
  "淮南": { longitude: 117.0187, latitude: 32.6264, timezone: 8 },
  "马鞍山": { longitude: 118.5077, latitude: 31.6890, timezone: 8 },
  "淮北": { longitude: 116.7947, latitude: 33.9717, timezone: 8 },
  "铜陵": { longitude: 117.8165, latitude: 30.9299, timezone: 8 },
  "安庆": { longitude: 117.0537, latitude: 30.5255, timezone: 8 },
  "黄山": { longitude: 118.3175, latitude: 29.7091, timezone: 8 },
  "滁州": { longitude: 118.3162, latitude: 32.3173, timezone: 8 },
  "阜阳": { longitude: 115.8197, latitude: 32.8969, timezone: 8 },
  "宿州": { longitude: 116.9840, latitude: 33.6341, timezone: 8 },
  "六安": { longitude: 116.5078, latitude: 31.7529, timezone: 8 },
  "亳州": { longitude: 115.7823, latitude: 33.8712, timezone: 8 },
  "池州": { longitude: 117.4890, latitude: 30.6600, timezone: 8 },
  "宣城": { longitude: 118.7576, latitude: 30.9456, timezone: 8 },

  // 四川省城市
  "绵阳": { longitude: 104.6419, latitude: 31.4678, timezone: 8 },
  "德阳": { longitude: 104.3982, latitude: 31.1270, timezone: 8 },
  "南充": { longitude: 106.0830, latitude: 30.7953, timezone: 8 },
  "宜宾": { longitude: 104.6308, latitude: 28.7602, timezone: 8 },
  "自贡": { longitude: 104.7794, latitude: 29.3528, timezone: 8 },
  "乐山": { longitude: 103.7614, latitude: 29.5647, timezone: 8 },
  "泸州": { longitude: 105.4433, latitude: 28.8718, timezone: 8 },
  "达州": { longitude: 107.5023, latitude: 31.2090, timezone: 8 },
  "内江": { longitude: 105.0661, latitude: 29.5803, timezone: 8 },
  "遂宁": { longitude: 105.5713, latitude: 30.5133, timezone: 8 },
  "攀枝花": { longitude: 101.7160, latitude: 26.5804, timezone: 8 },
  "眉山": { longitude: 103.8313, latitude: 30.0756, timezone: 8 },
  "广安": { longitude: 106.6333, latitude: 30.4564, timezone: 8 },
  "佛山": { longitude: 107.2348, latitude: 31.8773, timezone: 8 },
  "雅安": { longitude: 103.0010, latitude: 29.9877, timezone: 8 },
  "巴中": { longitude: 106.7537, latitude: 31.8691, timezone: 8 },
  "资阳": { longitude: 104.6419, latitude: 30.1222, timezone: 8 },
  "阿坝": { longitude: 102.2211, latitude: 31.8998, timezone: 8 },
  "甘孜": { longitude: 101.9638, latitude: 30.0498, timezone: 8 },
  "凉山": { longitude: 102.2587, latitude: 27.8868, timezone: 8 },

  // 辽宁省城市
  "鞍山": { longitude: 122.9951, latitude: 41.1106, timezone: 8 },
  "抚顺": { longitude: 123.9570, latitude: 41.8654, timezone: 8 },
  "本溪": { longitude: 123.7708, latitude: 41.2944, timezone: 8 },
  "丹东": { longitude: 124.3540, latitude: 40.1290, timezone: 8 },
  "锦州": { longitude: 121.1353, latitude: 41.1192, timezone: 8 },
  "营口": { longitude: 122.2352, latitude: 40.6674, timezone: 8 },
  "阜新": { longitude: 121.6708, latitude: 42.0118, timezone: 8 },
  "辽阳": { longitude: 123.1751, latitude: 41.2694, timezone: 8 },
  "盘锦": { longitude: 122.0709, latitude: 41.1245, timezone: 8 },
  "铁岭": { longitude: 123.8444, latitude: 42.2906, timezone: 8 },
  "朝阳": { longitude: 120.4516, latitude: 41.5718, timezone: 8 },
  "葫芦岛": { longitude: 120.8560, latitude: 40.7430, timezone: 8 },

  // 吉林省城市
  "吉林": { longitude: 126.5502, latitude: 43.8436, timezone: 8 },
  "四平": { longitude: 124.3707, latitude: 43.1703, timezone: 8 },
  "辽源": { longitude: 125.1455, latitude: 42.9027, timezone: 8 },
  "通化": { longitude: 125.9365, latitude: 41.7214, timezone: 8 },
  "白山": { longitude: 126.4230, latitude: 41.9437, timezone: 8 },
  "松原": { longitude: 124.8254, latitude: 45.1182, timezone: 8 },
  "白城": { longitude: 122.8397, latitude: 45.6196, timezone: 8 },
  "延边": { longitude: 129.5133, latitude: 42.9048, timezone: 8 },

  // 黑龙江省城市
  "齐齐哈尔": { longitude: 123.9180, latitude: 47.3420, timezone: 8 },
  "鸡西": { longitude: 130.9759, latitude: 45.3000, timezone: 8 },
  "鹤岗": { longitude: 130.2773, latitude: 47.3320, timezone: 8 },
  "双鸭山": { longitude: 131.1570, latitude: 46.6434, timezone: 8 },
  "大庆": { longitude: 125.1031, latitude: 46.5907, timezone: 8 },
  "伊春": { longitude: 128.8990, latitude: 47.7248, timezone: 8 },
  "佳木斯": { longitude: 130.3619, latitude: 46.8099, timezone: 8 },
  "七台河": { longitude: 131.0159, latitude: 45.7712, timezone: 8 },
  "牡丹江": { longitude: 129.6186, latitude: 44.5831, timezone: 8 },
  "黑河": { longitude: 127.4990, latitude: 50.2496, timezone: 8 },
  "绥化": { longitude: 126.9929, latitude: 46.6374, timezone: 8 },
  "大兴安岭": { longitude: 124.1116, latitude: 52.3353, timezone: 8 },

  // 山西省城市
  "大同": { longitude: 113.3007, latitude: 40.0769, timezone: 8 },
  "阳泉": { longitude: 113.5830, latitude: 37.8570, timezone: 8 },
  "长治": { longitude: 113.1143, latitude: 36.1915, timezone: 8 },
  "晋城": { longitude: 112.8513, latitude: 35.4975, timezone: 8 },
  "朔州": { longitude: 112.4333, latitude: 39.3313, timezone: 8 },
  "晋中": { longitude: 112.7536, latitude: 37.6965, timezone: 8 },
  "运城": { longitude: 111.0037, latitude: 35.0228, timezone: 8 },
  "忻州": { longitude: 112.7341, latitude: 38.4177, timezone: 8 },
  "临汾": { longitude: 111.5179, latitude: 36.0881, timezone: 8 },
  "吕梁": { longitude: 111.1347, latitude: 37.5177, timezone: 8 },

  // 陕西省城市
  "宝鸡": { longitude: 107.2372, latitude: 34.3615, timezone: 8 },
  "咸阳": { longitude: 108.7093, latitude: 34.3336, timezone: 8 },
  "渭南": { longitude: 109.5022, latitude: 34.4994, timezone: 8 },
  "延安": { longitude: 109.4897, latitude: 36.5965, timezone: 8 },
  "汉中": { longitude: 107.0281, latitude: 33.0777, timezone: 8 },
  "榆林": { longitude: 109.7341, latitude: 38.2900, timezone: 8 },
  "安康": { longitude: 109.0295, latitude: 32.6903, timezone: 8 },
  "商洛": { longitude: 109.9398, latitude: 33.8739, timezone: 8 },

  // 甘肃省城市
  "嘉峪关": { longitude: 98.2773, latitude: 39.7865, timezone: 8 },
  "金昌": { longitude: 102.1879, latitude: 38.5142, timezone: 8 },
  "白银": { longitude: 104.1735, latitude: 36.5457, timezone: 8 },
  "天水": { longitude: 105.7249, latitude: 34.5785, timezone: 8 },
  "武威": { longitude: 102.6348, latitude: 37.9282, timezone: 8 },
  "张掖": { longitude: 100.4510, latitude: 38.9248, timezone: 8 },
  "平凉": { longitude: 106.6650, latitude: 35.5428, timezone: 8 },
  "酒泉": { longitude: 98.5109, latitude: 39.7320, timezone: 8 },
  "庆阳": { longitude: 107.6434, latitude: 35.7342, timezone: 8 },
  "定西": { longitude: 104.6264, latitude: 35.5796, timezone: 8 },
  "陇南": { longitude: 104.9214, latitude: 33.4007, timezone: 8 },
  "临夏": { longitude: 103.2112, latitude: 35.5993, timezone: 8 },
  "甘南": { longitude: 102.9110, latitude: 34.9864, timezone: 8 }
};

/**
 * 获取城市坐标信息
 * @param {string} cityName - 城市名称
 * @returns {object|null} 城市坐标信息
 */
function getCityCoordinates(cityName) {
  return CITY_COORDINATES[cityName] || null;
}

/**
 * 获取所有支持的城市列表
 * @returns {Array} 城市名称数组
 */
function getSupportedCities() {
  return Object.keys(CITY_COORDINATES).sort();
}

/**
 * 搜索城市（支持模糊匹配）
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 匹配的城市列表
 */
function searchCities(keyword) {
  if (!keyword) return getSupportedCities();
  
  const cities = Object.keys(CITY_COORDINATES);
  return cities.filter(city => city.includes(keyword)).sort();
}

/**
 * 计算两个城市之间的距离（公里）
 * @param {string} city1 - 城市1
 * @param {string} city2 - 城市2
 * @returns {number|null} 距离（公里）
 */
function calculateDistance(city1, city2) {
  const coord1 = getCityCoordinates(city1);
  const coord2 = getCityCoordinates(city2);
  
  if (!coord1 || !coord2) return null;
  
  const R = 6371; // 地球半径（公里）
  const dLat = (coord2.latitude - coord1.latitude) * Math.PI / 180;
  const dLon = (coord2.longitude - coord1.longitude) * Math.PI / 180;
  
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(coord1.latitude * Math.PI / 180) * Math.cos(coord2.latitude * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  
  return Math.round(distance);
}

/**
 * 根据经纬度查找最近的城市
 * @param {number} longitude - 经度
 * @param {number} latitude - 纬度
 * @returns {object|null} 最近的城市信息
 */
function findNearestCity(longitude, latitude) {
  let nearestCity = null;
  let minDistance = Infinity;
  
  for (const [cityName, coords] of Object.entries(CITY_COORDINATES)) {
    const distance = Math.sqrt(
      Math.pow(coords.longitude - longitude, 2) + 
      Math.pow(coords.latitude - latitude, 2)
    );
    
    if (distance < minDistance) {
      minDistance = distance;
      nearestCity = {
        name: cityName,
        coordinates: coords,
        distance: distance
      };
    }
  }
  
  return nearestCity;
}

module.exports = {
  CITY_COORDINATES,
  getCityCoordinates,
  getSupportedCities,
  searchCities,
  calculateDistance,
  findNearestCity
};
