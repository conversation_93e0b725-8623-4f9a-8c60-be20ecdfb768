// test_enhanced_yongshen_calculator.js
// 测试增强版用神计算器

const EnhancedYongshenCalculator = require('./utils/enhanced_yongshen_calculator.js');

/**
 * 测试用神三级优先级算法
 */
function testYongshenCalculator() {
  console.log('🧪 开始测试用神三级优先级算法');
  console.log('=' * 50);

  const calculator = new EnhancedYongshenCalculator();

  // 测试用例1：冬生寒局，需要调候用神
  console.log('\n📋 测试用例1：冬生寒局（调候用神优先）');
  const testCase1 = {
    bazi: {
      element_powers: {
        percentages: {
          '木': 10, '火': 5, '土': 15, '金': 25, '水': 45
        }
      }
    },
    pattern: {
      pattern: '正印格',
      pattern_type: '正格'
    },
    fourPillars: [
      { gan: '壬', zhi: '子' },
      { gan: '癸', zhi: '亥' },
      { gan: '甲', zhi: '寅' },
      { gan: '壬', zhi: '申' }
    ],
    birthInfo: {
      month: 12, // 冬季
      gender: '男',
      age: 35
    }
  };

  const result1 = calculator.calculateFavors(testCase1.bazi, testCase1.pattern, testCase1.fourPillars, testCase1.birthInfo);
  console.log('🎯 分析结果1:', JSON.stringify(result1, null, 2));

  // 测试用例2：夏生炎局，需要调候用神
  console.log('\n📋 测试用例2：夏生炎局（调候用神优先）');
  const testCase2 = {
    bazi: {
      element_powers: {
        percentages: {
          '木': 15, '火': 50, '土': 20, '金': 10, '水': 5
        }
      }
    },
    pattern: {
      pattern: '食神格',
      pattern_type: '正格'
    },
    fourPillars: [
      { gan: '丙', zhi: '午' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '寅' },
      { gan: '丙', zhi: '午' }
    ],
    birthInfo: {
      month: 7, // 夏季
      gender: '女',
      age: 28
    }
  };

  const result2 = calculator.calculateFavors(testCase2.bazi, testCase2.pattern, testCase2.fourPillars, testCase2.birthInfo);
  console.log('🎯 分析结果2:', JSON.stringify(result2, null, 2));

  // 测试用例3：平衡格局，格局用神为主
  console.log('\n📋 测试用例3：平衡格局（格局用神为主）');
  const testCase3 = {
    bazi: {
      element_powers: {
        percentages: {
          '木': 20, '火': 25, '土': 20, '金': 20, '水': 15
        }
      }
    },
    pattern: {
      pattern: '正官格',
      pattern_type: '正格'
    },
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '辛', zhi: '酉' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ],
    birthInfo: {
      month: 4, // 春季
      gender: '男',
      age: 42
    }
  };

  const result3 = calculator.calculateFavors(testCase3.bazi, testCase3.pattern, testCase3.fourPillars, testCase3.birthInfo);
  console.log('🎯 分析结果3:', JSON.stringify(result3, null, 2));

  // 测试用例4：五行相战，制衡用神
  console.log('\n📋 测试用例4：五行相战（制衡用神）');
  const testCase4 = {
    bazi: {
      element_powers: {
        percentages: {
          '木': 35, '火': 10, '土': 30, '金': 15, '水': 10
        }
      }
    },
    pattern: {
      pattern: '普通格局',
      pattern_type: '正格'
    },
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '乙', zhi: '卯' },
      { gan: '戊', zhi: '辰' },
      { gan: '己', zhi: '未' }
    ],
    birthInfo: {
      month: 3, // 春季
      gender: '女',
      age: 25
    }
  };

  const result4 = calculator.calculateFavors(testCase4.bazi, testCase4.pattern, testCase4.fourPillars, testCase4.birthInfo);
  console.log('🎯 分析结果4:', JSON.stringify(result4, null, 2));

  console.log('\n✅ 用神计算器测试完成');
}

/**
 * 测试调候用神功能
 */
function testTiaohouFunction() {
  console.log('\n🧪 测试调候用神功能');
  
  const calculator = new EnhancedYongshenCalculator();
  
  const testCases = [
    {
      name: '冬生寒局',
      bazi: { element_powers: { percentages: { '水': 45, '金': 25, '木': 15, '火': 10, '土': 5 } } },
      birthInfo: { month: 1 },
      expected: '火'
    },
    {
      name: '夏生炎局',
      bazi: { element_powers: { percentages: { '火': 50, '木': 20, '土': 15, '金': 10, '水': 5 } } },
      birthInfo: { month: 7 },
      expected: '水'
    },
    {
      name: '春生木旺',
      bazi: { element_powers: { percentages: { '木': 50, '水': 20, '火': 15, '土': 10, '金': 5 } } },
      birthInfo: { month: 4 },
      expected: '火'
    },
    {
      name: '秋生金旺',
      bazi: { element_powers: { percentages: { '金': 50, '土': 20, '水': 15, '木': 10, '火': 5 } } },
      birthInfo: { month: 10 },
      expected: '水'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.name}`);
    const result = calculator.calculateTiaohouYongshen(testCase.bazi, testCase.birthInfo);
    console.log(`    预期用神: ${testCase.expected}`);
    console.log(`    实际用神: ${result.yongshen || '无需调候'}`);
    console.log(`    是否需要: ${result.needed ? '是' : '否'}`);
    console.log(`    原因: ${result.reason}`);
    
    const isCorrect = result.needed && result.yongshen === testCase.expected;
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 测试格局用神功能
 */
function testPatternYongshenFunction() {
  console.log('\n🧪 测试格局用神功能');
  
  const calculator = new EnhancedYongshenCalculator();
  
  const testCases = [
    {
      pattern: { pattern: '正官格' },
      expected: '印星'
    },
    {
      pattern: { pattern: '七杀格' },
      expected: '食伤'
    },
    {
      pattern: { pattern: '正财格' },
      expected: '食伤'
    },
    {
      pattern: { pattern: '食神格' },
      expected: '财星'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.pattern.pattern}`);
    const result = calculator.calculatePatternYongshen(testCase.pattern, []);
    console.log(`    预期用神: ${testCase.expected}`);
    console.log(`    实际用神: ${result.yongshen || '未找到'}`);
    console.log(`    格局描述: ${result.description || '无'}`);
    
    const isCorrect = result.available && result.yongshen === testCase.expected;
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 测试五行制衡功能
 */
function testBalanceYongshenFunction() {
  console.log('\n🧪 测试五行制衡功能');
  
  const calculator = new EnhancedYongshenCalculator();
  
  const testCases = [
    {
      name: '金木相战',
      elementPowers: { percentages: { '金': 35, '木': 30, '火': 15, '土': 15, '水': 5 } },
      expected: '水'
    },
    {
      name: '水火相战',
      elementPowers: { percentages: { '水': 35, '火': 30, '木': 15, '土': 15, '金': 5 } },
      expected: '木'
    },
    {
      name: '木过强',
      elementPowers: { percentages: { '木': 60, '火': 15, '土': 10, '金': 10, '水': 5 } },
      expected: '金'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.name}`);
    const result = calculator.calculateBalanceYongshen(testCase);
    console.log(`    预期用神: ${testCase.expected}`);
    console.log(`    实际用神: ${result.yongshen || '无需制衡'}`);
    console.log(`    制衡类型: ${result.type || '无'}`);
    console.log(`    原因: ${result.reason || '无'}`);
    
    const isCorrect = result.needed && result.yongshen === testCase.expected;
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 测试个人化调整功能
 */
function testPersonalAdjustments() {
  console.log('\n🧪 测试个人化调整功能');
  
  const calculator = new EnhancedYongshenCalculator();
  
  const baseDecision = {
    type: '格局用神',
    yongshen: '官杀',
    confidence: 0.8
  };

  const testCases = [
    { gender: '男', age: 35, expected: '置信度提升' },
    { gender: '女', age: 28, expected: '置信度不变' },
    { gender: '男', age: 25, expected: '置信度不变' },
    { gender: '女', age: 45, expected: '置信度不变' }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.gender}性，${testCase.age}岁`);
    const result = calculator.applyPersonalAdjustments(baseDecision, testCase);
    console.log(`    原始置信度: ${baseDecision.confidence}`);
    console.log(`    调整后置信度: ${result.confidence.toFixed(3)}`);
    console.log(`    调整项目: ${result.personal_adjustments?.length || 0}项`);
    
    if (result.personal_adjustments && result.personal_adjustments.length > 0) {
      result.personal_adjustments.forEach(adj => {
        console.log(`      - ${adj.type}: ${adj.description}`);
      });
    }
  });
}

/**
 * 验证三级优先级逻辑
 */
function validatePriorityLogic() {
  console.log('\n📚 验证三级优先级逻辑');
  
  const calculator = new EnhancedYongshenCalculator();
  
  // 模拟同时满足三级条件的情况
  const complexCase = {
    bazi: {
      element_powers: {
        percentages: { '木': 30, '火': 5, '土': 25, '金': 15, '水': 25 } // 木土相战 + 火弱需调候
      }
    },
    pattern: { pattern: '正官格', pattern_type: '正格' },
    fourPillars: [],
    birthInfo: { month: 1, gender: '男', age: 35 } // 冬季需调候
  };

  const result = calculator.calculateFavors(complexCase.bazi, complexCase.pattern, complexCase.fourPillars, complexCase.birthInfo);
  
  console.log('🎯 复杂情况分析:');
  console.log(`  最终用神: ${result.yongshen}`);
  console.log(`  用神类型: ${result.type}`);
  console.log(`  优先级: ${result.priority}`);
  console.log(`  置信度: ${(result.confidence * 100).toFixed(1)}%`);
  console.log(`  决策原因: ${result.reason}`);
  
  if (result.all_options) {
    console.log('\n  所有候选方案:');
    result.all_options.forEach((option, index) => {
      console.log(`    ${index + 1}. ${option.type}: ${option.yongshen} (优先级: ${option.priority})`);
    });
  }
  
  // 验证调候用神是否优先
  const shouldBeTiaohou = result.type === '调候用神';
  console.log(`\n  ✅ 调候用神优先验证: ${shouldBeTiaohou ? '通过' : '需要检查'}`);
}

// 运行测试
if (require.main === module) {
  try {
    testYongshenCalculator();
    testTiaohouFunction();
    testPatternYongshenFunction();
    testBalanceYongshenFunction();
    testPersonalAdjustments();
    validatePriorityLogic();
    
    console.log('\n🎉 所有测试完成！');
    console.log('📈 用神三级优先级算法已按照文档要求实现：');
    console.log('  ✅ 调候用神优先（寒暖燥湿急救）');
    console.log('  ✅ 格局用神次之（扶抑通关）');
    console.log('  ✅ 五行制衡最后（病药原理）');
    console.log('  ✅ 性别年龄个人化调整');
    console.log('  ✅ 综合决策与置信度评估');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    console.error(error.stack);
  }
}

module.exports = {
  testYongshenCalculator,
  testTiaohouFunction,
  testPatternYongshenFunction,
  testBalanceYongshenFunction,
  testPersonalAdjustments,
  validatePriorityLogic
};
