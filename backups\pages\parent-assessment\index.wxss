/* pages/parent-assessment/index.wxss */

.chat-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #7928ca; /* 家长评估深紫色 */
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #7928ca; /* 家长测评紫色，统一使用纯色而非渐变 */
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.nav-bar {
  padding: 40rpx 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 100;
  /* 移除固定背景色，继承主容器背景色 */
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.back-button {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
}

.back-button image {
  width: 40rpx;
  height: 40rpx;
}

/* 日期显示 */
.date-display {
  padding: 20rpx 40rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 0 30rpx;
  overflow-y: auto;
  position: relative;
}

/* 欢迎消息 */
.welcome-message {
  padding: 30rpx;
  margin: 20rpx 0 40rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.welcome-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.welcome-subtitle {
  font-size: 28rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 30rpx;
}

.welcome-instruction {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  padding: 20rpx 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 评估问卷区域 */
.assessment-container {
  padding-bottom: 120rpx;
}

.question-card {
  padding: 30rpx;
  margin-bottom: 40rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.question-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.question-description {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 选项列表 */
.options-list {
  margin-top: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5rpx);
  transition: all 0.3s ease;
}

.option-item.selected {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.option-radio {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  margin-right: 20rpx;
  position: relative;
  flex-shrink: 0;
}

.option-item.selected .option-radio {
  border-color: white;
}

.option-item.selected .option-radio::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.option-text {
  font-size: 28rpx;
  flex: 1;
}

/* 进度指示器 */
.progress-container {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  padding: 0 10rpx;
}

.progress-bar {
  flex: 1;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5rpx;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 20rpx;
}

/* 提交按钮区域 */
.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.nav-button {
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.nav-button.next {
  background: rgba(255, 255, 255, 0.25);
}

.nav-button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.nav-button image {
  width: 32rpx;
  height: 32rpx;
}

.prev-button image {
  margin-right: 10rpx;
}

.next-button image {
  margin-left: 10rpx;
}

/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  align-items: center;
  background: rgba(121, 40, 202, 0.9); /* 家长测评主题色的半透明版本 */
  backdrop-filter: blur(10rpx);
  z-index: 100;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
  color: white;
  font-size: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.send-button, .voice-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.send-button {
  margin-right: 10rpx;
}

.send-button.active {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.send-button image, .voice-button image {
  width: 40rpx;
  height: 40rpx;
}

.button-hover {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 结果页样式 */
.result-container {
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.result-summary {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.score-label {
  font-size: 28rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.25);
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  margin-top: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.share-button text {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.share-button image {
  width: 32rpx;
  height: 32rpx;
}

/* 屏幕适配调整 */
@media screen and (max-height: 700px) {
  .welcome-message, .question-card, .result-container {
    padding: 20rpx;
    margin-bottom: 30rpx;
  }
  
  .welcome-title, .result-title {
    font-size: 34rpx;
    margin-bottom: 15rpx;
  }
  
  .welcome-subtitle, .question-title {
    font-size: 26rpx;
    margin-bottom: 20rpx;
  }
  
  .option-item {
    padding: 16rpx;
    margin-bottom: 15rpx;
  }
}

/* 自定义导航栏 */
.custom-nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 100;
  background: rgba(121, 40, 202, 0.8); /* 家长主题色半透明 */
}

.nav-bar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.nav-bar-right {
  display: flex;
  align-items: center;
}

.menu-icon {
  margin-right: 20rpx;
  font-size: 40rpx;
  font-weight: bold;
}

.settings-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(121, 40, 202, 0.3); /* 家长主题色 */
  border-radius: 50%;
}

.settings-icon:active {
  background: rgba(121, 40, 202, 0.5);
}

/* 标签容器 */
.tab-container {
  display: flex;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  background: rgba(121, 40, 202, 0.7); /* 家长主题色半透明 */
}

.tab {
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: rgba(121, 40, 202, 0.9); /* 家长主题色 */
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 按钮样式 */
.welcome-start-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: rgba(121, 40, 202, 0.9); /* 家长主题色 */
  border-radius: 50rpx;
  width: 80%;
  box-shadow: 0 6rpx 20rpx rgba(121, 40, 202, 0.4);
  transition: all 0.3s ease;
  animation: fadeInUp 1.4s ease-out;
}

.welcome-start-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(121, 40, 202, 0.3);
}

.welcome-start-button image {
  width: 36rpx;
  height: 36rpx;
}

/* 输入区域样式 */
.input-area {
  background: rgba(121, 40, 202, 0.9); /* 家长主题色 */
}

.message-input {
  background: rgba(121, 40, 202, 0.2);
  border: 1px solid rgba(121, 40, 202, 0.3);
}

/* 发送按钮样式 */
.send-button {
  background: rgba(121, 40, 202, 0.8); /* 家长主题色 */
}

.send-button.active {
  background: rgba(121, 40, 202, 1.0); /* 家长主题色全不透明 */
}

/* 语音按钮样式 */
.voice-button {
  background: rgba(121, 40, 202, 0.5); /* 家长主题色更透明 */
}

.voice-button:active {
  background: rgba(121, 40, 202, 0.7);
}

/* 打字动画点 */
.typing-dot {
  background: #7928ca; /* 家长主题色 */
}

/* 查看报告按钮 */
.action-button.view-report {
  background: rgba(121, 40, 202, 0.9); /* 家长主题色 */
}

/* 家长主题专用样式 */
.parent-theme .custom-nav-bar {
  background: rgba(121, 40, 202, 0.8);
}

.parent-theme .settings-icon {
  background: rgba(121, 40, 202, 0.3);
}

.parent-theme .settings-icon:active {
  background: rgba(121, 40, 202, 0.5);
}

.parent-theme .tab.active {
  background: rgba(121, 40, 202, 0.9);
}

.parent-theme .welcome-start-button {
  background: rgba(121, 40, 202, 0.9);
}

.parent-theme .send-button {
  background: rgba(121, 40, 202, 0.8);
}

.parent-theme .send-button.active {
  background: rgba(121, 40, 202, 1.0);
}

.parent-theme .voice-button {
  background: rgba(121, 40, 202, 0.5);
}

.parent-theme .voice-button:active {
  background: rgba(121, 40, 202, 0.7);
}

.parent-theme .typing-dot {
  background: #7928ca;
}

.parent-theme .action-button.view-report {
  background: rgba(121, 40, 202, 0.9);
} 