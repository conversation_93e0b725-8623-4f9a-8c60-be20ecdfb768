// components/ec-canvas/wx-canvas.js

/**
 * 微信小程序Canvas适配器
 * 用于在微信小程序中使用ECharts
 */

function WxCanvas(ctx, canvasId, isNew, canvasNode) {
  this.ctx = ctx;
  this.canvasId = canvasId;
  this.chart = null;
  this.isNew = isNew;
  if (isNew) {
    this.canvasNode = canvasNode;
  }
}

WxCanvas.prototype.getContext = function (contextType) {
  if (contextType === '2d') {
    return this.ctx;
  }
};

WxCanvas.prototype.setChart = function (chart) {
  this.chart = chart;
};

WxCanvas.prototype.attachEvent = function () {
  // 实现事件附加
  if (this.isNew && this.canvasNode) {
    // 新版本的canvas 2d接口支持事件
    const eventNames = ['click', 'mousemove', 'mousedown', 'mouseup', 'mouseout'];
    eventNames.forEach(name => {
      this.canvasNode._listeners = this.canvasNode._listeners || {};
      this.canvasNode._listeners[name] = [];
      
      // 确保事件能够正确触发
      this.canvasNode.addEventListener(name, (e) => {
        if (this.chart && this.chart.getZr && typeof this.chart.getZr === 'function') {
          const zr = this.chart.getZr();
          if (zr && zr.handler) {
            const handler = zr.handler;
            const touch = e.touches && e.touches.length ? e.touches[0] : e;
            handler.dispatch(name, {
              zrX: touch.x,
              zrY: touch.y,
              preventDefault: () => {},
              stopPropagation: () => {}
            });
          }
        }
      });
    });
  }
};

WxCanvas.prototype.detachEvent = function () {
  // 实现事件分离
  if (this.isNew && this.canvasNode && this.canvasNode._listeners) {
    this.canvasNode._listeners = {};
  }
};

WxCanvas.prototype.addEventListener = function (type, listener) {
  // 实现事件监听
  if (this.isNew && this.canvasNode) {
    this.canvasNode.addEventListener(type, (e) => {
      // 将事件传递给ECharts实例
      if (this.chart && this.chart.getZr()) {
        const handler = this.chart.getZr().handler;
        const touch = e.touches && e.touches.length ? e.touches[0] : e;
        handler.dispatch(type, {
          zrX: touch.x,
          zrY: touch.y,
          preventDefault: () => {},
          stopPropagation: () => {}
        });
      }
      // 调用原始监听器
      listener.call(this, e);
    });
  }
};

WxCanvas.prototype.removeEventListener = function (type, listener) {
  // 实现事件移除
  if (this.isNew && this.canvasNode) {
    this.canvasNode.removeEventListener(type, listener);
  }
};

WxCanvas.prototype.getDpr = function () {
  return wx.getWindowInfo().pixelRatio;
};

WxCanvas.prototype.measureText = function (text) {
  return this.ctx.measureText(text);
};

module.exports = WxCanvas;