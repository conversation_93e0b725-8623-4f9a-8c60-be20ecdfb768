/**
 * 小运计算系统测试套件
 * 
 * 测试内容：
 * 1. 基础功能测试
 * 2. 边界条件测试  
 * 3. 《三命通会》算例验证
 * 4. 错误处理测试
 * 5. 性能测试
 */

// 导入小运计算器
const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');

class MinorFortuneTestSuite {
  constructor() {
    this.calculator = new MinorFortuneCalculator();
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      details: []
    };
    
    console.log('🧪 小运计算系统测试套件初始化完成');
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('\n🚀 开始运行小运计算系统完整测试套件...\n');
    
    // 1. 基础功能测试
    this.testBasicFunctionality();
    
    // 2. 边界条件测试
    this.testBoundaryConditions();
    
    // 3. 《三命通会》算例验证
    this.testClassicalExamples();
    
    // 4. 错误处理测试
    this.testErrorHandling();
    
    // 5. 性能测试
    this.testPerformance();
    
    // 输出测试报告
    this.generateTestReport();
  }

  /**
   * 基础功能测试
   */
  testBasicFunctionality() {
    console.log('📋 1. 基础功能测试');
    
    // 测试用例：标准八字
    const testBazi = {
      yearPillar: { gan: '庚', zhi: '寅' },  // 阳年
      monthPillar: { gan: '戊', zhi: '寅' },
      dayPillar: { gan: '乙', zhi: '酉' },
      hourPillar: { gan: '庚', zhi: '辰' },  // 时柱
      gender: '男'
    };

    // 测试1：1岁小运计算
    this.runTest('1岁小运为时柱本身', () => {
      const result = this.calculator.calculate(testBazi, 1);
      return result && result.pillar === '庚辰' && result.age === 1;
    });

    // 测试2：阳男顺行推演
    this.runTest('阳男顺行推演验证', () => {
      const result1 = this.calculator.calculate(testBazi, 1);
      const result2 = this.calculator.calculate(testBazi, 2);
      
      // 庚辰 → 辛巳（顺推一步）
      return result1.pillar === '庚辰' && result2.pillar === '辛巳';
    });

    // 测试3：批量计算功能
    this.runTest('批量计算1-10岁小运', () => {
      const results = this.calculator.calculateAllMinorFortunes(testBazi);
      return results.length === 10 && results[0].age === 1 && results[9].age === 10;
    });

    // 测试4：阴女阳年逆行
    const testBaziF = { ...testBazi, gender: '女' };
    this.runTest('阴女阳年逆行验证', () => {
      const result1 = this.calculator.calculate(testBaziF, 1);
      const result2 = this.calculator.calculate(testBaziF, 2);
      
      // 庚辰 → 己卯（逆推一步）
      return result1.pillar === '庚辰' && result2.pillar === '己卯';
    });
  }

  /**
   * 边界条件测试
   */
  testBoundaryConditions() {
    console.log('\n📋 2. 边界条件测试');
    
    const testBazi = {
      yearPillar: { gan: '庚', zhi: '寅' },
      monthPillar: { gan: '戊', zhi: '寅' },
      dayPillar: { gan: '乙', zhi: '酉' },
      hourPillar: { gan: '庚', zhi: '辰' },
      gender: '男'
    };

    // 测试1：年龄边界
    this.runTest('0岁应返回null', () => {
      const result = this.calculator.calculate(testBazi, 0);
      return result === null;
    });

    this.runTest('10岁应正常计算', () => {
      const result = this.calculator.calculate(testBazi, 10);
      return result !== null && result.age === 10;
    });

    this.runTest('11岁应返回null', () => {
      const result = this.calculator.calculate(testBazi, 11);
      return result === null;
    });

    // 测试2：负数年龄
    this.runTest('负数年龄应返回null', () => {
      const result = this.calculator.calculate(testBazi, -1);
      return result === null;
    });

    // 测试3：小数年龄
    this.runTest('小数年龄应返回null', () => {
      const result = this.calculator.calculate(testBazi, 5.5);
      return result === null;
    });
  }

  /**
   * 《三命通会》算例验证
   */
  testClassicalExamples() {
    console.log('\n📋 3. 《三命通会》算例验证');
    
    // 算例1：2010年立春出生男婴
    const example1 = {
      yearPillar: { gan: '庚', zhi: '寅' },  // 庚寅年（阳年）
      monthPillar: { gan: '戊', zhi: '寅' },  // 戊寅月
      dayPillar: { gan: '乙', zhi: '酉' },   // 乙酉日
      hourPillar: { gan: '庚', zhi: '辰' },  // 庚辰时
      gender: '男'
    };

    this.runTest('算例1：庚寅年男婴小运推演', () => {
      const results = this.calculator.calculateAllMinorFortunes(example1);
      
      // 验证关键年龄的小运
      const age1 = results.find(r => r.age === 1);
      const age2 = results.find(r => r.age === 2);
      const age10 = results.find(r => r.age === 10);
      
      return age1.pillar === '庚辰' &&  // 1岁：时柱本身
             age2.pillar === '辛巳' &&  // 2岁：阳男顺推
             age10.pillar === '己丑';   // 10岁：推演结果
    });

    // 算例2：2005年冬至出生女婴
    const example2 = {
      yearPillar: { gan: '乙', zhi: '酉' },  // 乙酉年（阴年）
      monthPillar: { gan: '戊', zhi: '子' },  // 戊子月
      dayPillar: { gan: '庚', zhi: '辰' },   // 庚辰日
      hourPillar: { gan: '壬', zhi: '午' },  // 壬午时
      gender: '女'
    };

    this.runTest('算例2：乙酉年女婴小运推演', () => {
      const results = this.calculator.calculateAllMinorFortunes(example2);

      // 验证关键年龄的小运
      const age1 = results.find(r => r.age === 1);
      const age2 = results.find(r => r.age === 2);
      const age10 = results.find(r => r.age === 10);

      // 修正：阴女阴年应该顺行，壬午 → 癸未
      return age1.pillar === '壬午' &&  // 1岁：时柱本身
             age2.pillar === '癸未' &&  // 2岁：阴女阴年顺推
             age10.pillar === '辛卯';   // 10岁：推演结果
    });
  }

  /**
   * 错误处理测试
   */
  testErrorHandling() {
    console.log('\n📋 4. 错误处理测试');
    
    // 测试1：空数据
    this.runTest('空八字数据处理', () => {
      try {
        const result = this.calculator.calculate(null, 5);
        return result === null;
      } catch (error) {
        return true; // 预期会抛出错误
      }
    });

    // 测试2：缺失时柱
    this.runTest('缺失时柱数据处理', () => {
      const invalidBazi = {
        yearPillar: { gan: '庚', zhi: '寅' },
        gender: '男'
        // 缺失 hourPillar
      };
      const result = this.calculator.calculate(invalidBazi, 5);
      return result === null;
    });

    // 测试3：无效干支
    this.runTest('无效干支处理', () => {
      const invalidBazi = {
        yearPillar: { gan: '庚', zhi: '寅' },
        hourPillar: { gan: '无效', zhi: '辰' },
        gender: '男'
      };
      const result = this.calculator.calculate(invalidBazi, 5);
      return result === null;
    });

    // 测试4：无效性别
    this.runTest('无效性别处理', () => {
      const invalidBazi = {
        yearPillar: { gan: '庚', zhi: '寅' },
        hourPillar: { gan: '庚', zhi: '辰' },
        gender: '无效'
      };
      const result = this.calculator.calculate(invalidBazi, 5);
      return result === null;
    });
  }

  /**
   * 性能测试
   */
  testPerformance() {
    console.log('\n📋 5. 性能测试');
    
    const testBazi = {
      yearPillar: { gan: '庚', zhi: '寅' },
      monthPillar: { gan: '戊', zhi: '寅' },
      dayPillar: { gan: '乙', zhi: '酉' },
      hourPillar: { gan: '庚', zhi: '辰' },
      gender: '男'
    };

    // 性能测试：批量计算
    this.runTest('批量计算性能测试', () => {
      const startTime = Date.now();
      
      // 计算100次批量小运
      for (let i = 0; i < 100; i++) {
        this.calculator.calculateAllMinorFortunes(testBazi);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`   性能结果: 100次批量计算耗时 ${duration}ms`);
      
      // 性能要求：100次计算应在1秒内完成
      return duration < 1000;
    });

    // 内存测试
    this.runTest('内存使用测试', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 大量计算
      for (let i = 0; i < 1000; i++) {
        this.calculator.calculate(testBazi, (i % 10) + 1);
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      console.log(`   内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // 内存要求：1000次计算内存增长应小于10MB
      return memoryIncrease < 10 * 1024 * 1024;
    });
  }

  /**
   * 运行单个测试
   */
  runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      const result = testFunction();
      if (result) {
        this.testResults.passed++;
        console.log(`   ✅ ${testName}`);
        this.testResults.details.push({ name: testName, status: 'PASSED' });
      } else {
        this.testResults.failed++;
        console.log(`   ❌ ${testName}`);
        this.testResults.details.push({ name: testName, status: 'FAILED', reason: '测试条件不满足' });
      }
    } catch (error) {
      this.testResults.failed++;
      console.log(`   ❌ ${testName} - 错误: ${error.message}`);
      this.testResults.details.push({ name: testName, status: 'ERROR', reason: error.message });
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${this.testResults.total}`);
    console.log(`通过: ${this.testResults.passed}`);
    console.log(`失败: ${this.testResults.failed}`);
    console.log(`成功率: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults.details
        .filter(detail => detail.status !== 'PASSED')
        .forEach(detail => {
          console.log(`   - ${detail.name}: ${detail.reason || detail.status}`);
        });
    }
    
    // 评级
    const successRate = (this.testResults.passed / this.testResults.total) * 100;
    let grade = '';
    if (successRate >= 95) grade = '🏆 优秀';
    else if (successRate >= 85) grade = '🥈 良好';
    else if (successRate >= 70) grade = '🥉 及格';
    else grade = '❌ 不及格';
    
    console.log(`\n🎯 测试评级: ${grade}`);
    console.log('='.repeat(50));
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new MinorFortuneTestSuite();
  testSuite.runAllTests();
}

module.exports = MinorFortuneTestSuite;
