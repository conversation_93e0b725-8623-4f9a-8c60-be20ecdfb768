// utils/professional_dayun_calculator.js
// 专业级大运计算器 - 整合精确节气计算和真太阳时修正
// 依据：《大小运，流年.txt》技术文档要求

const PreciseSolarTermsEngine = require('./precise_solar_terms_engine.js');
const TrueSolarTimeCorrector = require('./true_solar_time_corrector.js');

/**
 * 专业级大运计算器
 * 
 * 功能特点：
 * 1. 精确节气时间计算（分钟级精度）
 * 2. 真太阳时修正（确保时辰准确）
 * 3. 专业起运时间计算（三天折一年）
 * 4. 完整大运序列生成（120年）
 * 5. 大运过渡期分析
 * 
 * 技术原理：
 * - 阳男阴女：顺行至下一个"节"
 * - 阴男阳女：逆行至上一个"节"
 * - 起运公式：天数 ÷ 3 = 年数，余数 × 4 = 月数
 * - 精确到分钟级的起运时间计算
 */
class ProfessionalDayunCalculator {
  constructor() {
    this.name = 'ProfessionalDayunCalculator';
    this.version = '1.0.0';
    
    // 初始化依赖组件
    this.solarTermsEngine = new PreciseSolarTermsEngine();
    this.timeCorrector = new TrueSolarTimeCorrector();
    
    // 天干地支数据
    this.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    console.log('🔮 专业级大运计算器初始化完成');
    console.log('📅 集成精确节气计算引擎');
    console.log('🌅 集成真太阳时修正系统');
  }
  
  /**
   * 计算专业级大运
   * @param {Object} baziData - 八字数据
   * @param {Object} birthInfo - 出生信息
   * @param {number|string} location - 出生地经度或城市名称
   * @returns {Object} 完整大运分析结果
   */
  calculateProfessionalDayun(baziData, birthInfo, location) {
    console.log('🔮 开始专业级大运计算...');
    
    try {
      // 1. 构建出生时间
      const birthDatetime = new Date(
        birthInfo.year,
        birthInfo.month - 1,
        birthInfo.day,
        birthInfo.hour,
        birthInfo.minute || 0,
        0
      );
      
      // 2. 真太阳时修正
      const timeCorrection = this.timeCorrector.correctTimeZhi(birthDatetime, location);
      const trueBirthTime = timeCorrection.result.trueSolarTime;
      
      console.log(`🌅 时间修正: ${timeCorrection.input.beijingTimeString} → ${timeCorrection.result.trueSolarTimeString}`);
      if (timeCorrection.timeZhi.changed) {
        console.log(`⚠️ ${timeCorrection.timeZhi.changeDescription}`);
      }
      
      // 3. 判断大运方向
      const dayunDirection = this.determineDayunDirection(baziData.gender, baziData.yearPillar.gan);
      
      // 4. 计算起运时间
      const qiyunResult = this.calculateQiyunTime(trueBirthTime, dayunDirection.isForward);
      
      // 5. 生成大运序列
      const dayunSequence = this.generateDayunSequence(baziData.monthPillar, dayunDirection.isForward, 12);
      
      // 6. 计算大运时间表
      const dayunTimeline = this.calculateDayunTimeline(qiyunResult, dayunSequence);
      
      // 7. 分析当前大运
      const currentDayun = this.analyzeCurrentDayun(dayunTimeline, new Date());
      
      // 8. 生成完整分析结果
      const analysisResult = {
        input: {
          birthInfo: birthInfo,
          location: location,
          originalTime: birthDatetime,
          correctedTime: trueBirthTime,
          timeCorrection: timeCorrection
        },
        calculation: {
          direction: dayunDirection,
          qiyunResult: qiyunResult,
          dayunSequence: dayunSequence,
          dayunTimeline: dayunTimeline
        },
        analysis: {
          currentDayun: currentDayun,
          nextDayun: this.getNextDayun(dayunTimeline, currentDayun),
          transitionAnalysis: this.analyzeTransition(dayunTimeline, currentDayun)
        },
        metadata: {
          calculationTime: new Date(),
          precision: 'minute-level',
          dataSource: '权威天文台数据',
          algorithm: '《大小运，流年.txt》专业算法'
        }
      };
      
      console.log('✅ 专业级大运计算完成');
      console.log(`🎯 起运时间: ${qiyunResult.qiyunAge.years}岁${qiyunResult.qiyunAge.months}个月`);
      console.log(`🔄 大运方向: ${dayunDirection.description}`);
      
      return analysisResult;
      
    } catch (error) {
      console.error('❌ 专业级大运计算失败:', error);
      throw new Error(`大运计算失败: ${error.message}`);
    }
  }
  
  /**
   * 判断大运方向
   * @param {string} gender - 性别
   * @param {string} yearGan - 年干
   * @returns {Object} 方向信息
   */
  determineDayunDirection(gender, yearGan) {
    // 阳干：甲丙戊庚壬
    const yangGan = ['甲', '丙', '戊', '庚', '壬'];
    const isYangYear = yangGan.includes(yearGan);
    
    // 阳男阴女顺行，阴男阳女逆行
    let isForward;
    if (gender === '男') {
      isForward = isYangYear; // 阳男顺行，阴男逆行
    } else {
      isForward = !isYangYear; // 阴女顺行，阳女逆行
    }
    
    const direction = {
      isForward: isForward,
      yearGan: yearGan,
      yearType: isYangYear ? '阳年' : '阴年',
      gender: gender,
      rule: gender === '男' 
        ? (isYangYear ? '阳男顺行' : '阴男逆行')
        : (isYangYear ? '阳女逆行' : '阴女顺行'),
      description: `${gender}命${isYangYear ? '阳' : '阴'}年生，${isForward ? '顺' : '逆'}行大运`,
      targetDirection: isForward ? '下一个节' : '上一个节'
    };
    
    console.log(`🧭 大运方向: ${direction.description}`);
    return direction;
  }
  
  /**
   * 计算起运时间
   * @param {Date} trueBirthTime - 真太阳时出生时间
   * @param {boolean} isForward - 是否顺行
   * @returns {Object} 起运计算结果
   */
  calculateQiyunTime(trueBirthTime, isForward) {
    console.log('📅 计算精确起运时间...');
    
    // 获取目标节气
    const targetTerm = this.solarTermsEngine.getTargetTermForDayun(trueBirthTime, isForward);
    
    // 计算时间差（毫秒）
    const timeDiffMs = Math.abs(targetTerm.date.getTime() - trueBirthTime.getTime());
    const daysDiff = timeDiffMs / (1000 * 60 * 60 * 24);
    
    // 三天折一年的精确计算
    const years = Math.floor(daysDiff / 3);
    const remainingDays = daysDiff % 3;
    const months = Math.floor(remainingDays * 4); // 余数×4=月数
    const remainingHours = (remainingDays * 4 - months) * 30 * 24; // 剩余小时
    const days = Math.floor(remainingHours / 24);
    const hours = Math.floor(remainingHours % 24);
    
    // 计算起运具体时间
    const qiyunDate = new Date(trueBirthTime);
    qiyunDate.setFullYear(qiyunDate.getFullYear() + years);
    qiyunDate.setMonth(qiyunDate.getMonth() + months);
    qiyunDate.setDate(qiyunDate.getDate() + days);
    qiyunDate.setHours(qiyunDate.getHours() + hours);
    
    const result = {
      targetTerm: targetTerm,
      daysDifference: daysDiff,
      qiyunAge: {
        years: years,
        months: months,
        days: days,
        hours: hours,
        totalDays: daysDiff
      },
      qiyunDate: qiyunDate,
      calculation: {
        formula: '天数 ÷ 3 = 年数，余数 × 4 = 月数',
        precision: 'minute-level',
        method: '《大小运，流年.txt》专业算法'
      }
    };
    
    console.log(`📅 起运计算: ${daysDiff.toFixed(2)}天 → ${years}岁${months}个月${days}天${hours}小时`);
    console.log(`🎯 目标节气: ${targetTerm.name} (${this.formatDateTime(targetTerm.date)})`);
    
    return result;
  }
  
  /**
   * 生成大运序列
   * @param {Object} monthPillar - 月柱
   * @param {boolean} isForward - 是否顺行
   * @param {number} count - 生成数量
   * @returns {Array} 大运序列
   */
  generateDayunSequence(monthPillar, isForward, count = 12) {
    const sequence = [];
    
    // 获取月柱干支索引
    const ganIndex = this.tiangan.indexOf(monthPillar.gan);
    const zhiIndex = this.dizhi.indexOf(monthPillar.zhi);
    
    if (ganIndex === -1 || zhiIndex === -1) {
      throw new Error(`无效的月柱: ${monthPillar.gan}${monthPillar.zhi}`);
    }
    
    // 生成大运序列
    for (let i = 0; i < count; i++) {
      const step = isForward ? i + 1 : -(i + 1);
      
      const newGanIndex = (ganIndex + step + this.tiangan.length * 10) % this.tiangan.length;
      const newZhiIndex = (zhiIndex + step + this.dizhi.length * 10) % this.dizhi.length;
      
      const dayun = {
        sequence: i + 1,
        gan: this.tiangan[newGanIndex],
        zhi: this.dizhi[newZhiIndex],
        pillar: this.tiangan[newGanIndex] + this.dizhi[newZhiIndex],
        startAge: i * 10, // 每步10年
        endAge: (i + 1) * 10 - 1,
        period: `${i * 10}-${(i + 1) * 10 - 1}岁`
      };
      
      sequence.push(dayun);
    }
    
    console.log(`🔄 生成${count}步大运序列 (${isForward ? '顺行' : '逆行'})`);
    return sequence;
  }
  
  /**
   * 计算大运时间表
   * @param {Object} qiyunResult - 起运结果
   * @param {Array} dayunSequence - 大运序列
   * @returns {Array} 大运时间表
   */
  calculateDayunTimeline(qiyunResult, dayunSequence) {
    const timeline = [];
    
    for (const dayun of dayunSequence) {
      const startDate = new Date(qiyunResult.qiyunDate);
      startDate.setFullYear(startDate.getFullYear() + dayun.startAge);
      
      const endDate = new Date(qiyunResult.qiyunDate);
      endDate.setFullYear(endDate.getFullYear() + dayun.endAge + 1);
      
      const timelineItem = {
        ...dayun,
        startDate: startDate,
        endDate: endDate,
        startDateString: this.formatDateTime(startDate),
        endDateString: this.formatDateTime(endDate),
        duration: '10年',
        isActive: false // 将在分析当前大运时设置
      };
      
      timeline.push(timelineItem);
    }
    
    return timeline;
  }
  
  /**
   * 分析当前大运
   * @param {Array} dayunTimeline - 大运时间表
   * @param {Date} currentDate - 当前时间
   * @returns {Object} 当前大运分析
   */
  analyzeCurrentDayun(dayunTimeline, currentDate) {
    for (const dayun of dayunTimeline) {
      if (currentDate >= dayun.startDate && currentDate < dayun.endDate) {
        dayun.isActive = true;
        
        // 计算大运进度
        const totalDuration = dayun.endDate.getTime() - dayun.startDate.getTime();
        const elapsed = currentDate.getTime() - dayun.startDate.getTime();
        const progress = (elapsed / totalDuration) * 100;
        
        // 计算剩余时间
        const remaining = dayun.endDate.getTime() - currentDate.getTime();
        const remainingYears = remaining / (1000 * 60 * 60 * 24 * 365.25);
        
        return {
          ...dayun,
          progress: progress,
          progressDescription: `已行${progress.toFixed(1)}%`,
          remainingYears: remainingYears,
          remainingDescription: `还剩${remainingYears.toFixed(1)}年`,
          status: 'current'
        };
      }
    }
    
    // 如果没有找到当前大运，可能还未起运或已超出计算范围
    const firstDayun = dayunTimeline[0];
    if (currentDate < firstDayun.startDate) {
      return {
        status: 'not-started',
        description: '尚未起运',
        nextDayun: firstDayun
      };
    } else {
      return {
        status: 'beyond-range',
        description: '超出计算范围',
        lastDayun: dayunTimeline[dayunTimeline.length - 1]
      };
    }
  }
  
  /**
   * 获取下一步大运
   * @param {Array} dayunTimeline - 大运时间表
   * @param {Object} currentDayun - 当前大运
   * @returns {Object} 下一步大运
   */
  getNextDayun(dayunTimeline, currentDayun) {
    if (currentDayun.status !== 'current') {
      return null;
    }
    
    const currentIndex = dayunTimeline.findIndex(d => d.sequence === currentDayun.sequence);
    if (currentIndex >= 0 && currentIndex < dayunTimeline.length - 1) {
      return dayunTimeline[currentIndex + 1];
    }
    
    return null;
  }
  
  /**
   * 分析大运过渡期
   * @param {Array} dayunTimeline - 大运时间表
   * @param {Object} currentDayun - 当前大运
   * @returns {Object} 过渡期分析
   */
  analyzeTransition(dayunTimeline, currentDayun) {
    if (currentDayun.status !== 'current') {
      return null;
    }
    
    const nextDayun = this.getNextDayun(dayunTimeline, currentDayun);
    if (!nextDayun) {
      return null;
    }
    
    // 判断是否接近过渡期（最后2年）
    const isNearTransition = currentDayun.remainingYears <= 2;
    
    return {
      isNearTransition: isNearTransition,
      currentPillar: currentDayun.pillar,
      nextPillar: nextDayun.pillar,
      transitionDate: nextDayun.startDate,
      transitionDescription: isNearTransition 
        ? `即将进入${nextDayun.pillar}大运，建议关注运势变化`
        : `当前${currentDayun.pillar}大运稳定期`,
      remainingTime: currentDayun.remainingDescription
    };
  }
  
  /**
   * 格式化日期时间
   * @param {Date} date - 日期对象
   * @returns {string} 格式化字符串
   */
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
  
  /**
   * 获取计算器状态
   * @returns {Object} 状态信息
   */
  getCalculatorStatus() {
    return {
      name: this.name,
      version: this.version,
      components: {
        solarTermsEngine: this.solarTermsEngine.getCorrectorStatus ? this.solarTermsEngine.getCorrectorStatus() : 'loaded',
        timeCorrector: this.timeCorrector.getCorrectorStatus()
      },
      features: [
        '精确节气计算（分钟级）',
        '真太阳时修正',
        '专业起运时间计算',
        '完整大运序列生成',
        '大运过渡期分析'
      ],
      algorithm: '《大小运，流年.txt》专业标准',
      precision: 'minute-level',
      status: 'ready'
    };
  }
}

// 导出计算器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProfessionalDayunCalculator;
} else if (typeof window !== 'undefined') {
  window.ProfessionalDayunCalculator = ProfessionalDayunCalculator;
}

console.log('🔮 专业级大运计算器模块加载完成');
