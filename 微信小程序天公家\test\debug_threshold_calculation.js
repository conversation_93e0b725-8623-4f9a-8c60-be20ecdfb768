/**
 * 调试阈值计算问题
 * 找出为什么阈值被错误放大的原因
 */

// 模拟当前的阈值配置
const thresholdConfig = {
  spouse_star_threshold: 0.15,      // 15% = 0.15
  official_seal_threshold: 0.20,    // 20% = 0.20
  food_injury_threshold: 0.12,      // 12% = 0.12
  wealth_star_threshold: 0.18       // 18% = 0.18
};

// 模拟五行能量
const elementEnergies = {
  金: 45.2,
  木: 23.8,
  水: 67.1,
  火: 34.6,
  土: 29.3
};

// 当前的算法（有问题的版本）
function calculateMarriageEnergyBuggy(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  const spouseStarEnergy = (金 + 火) * 0.3;
  const palaceEnergy = 水 * 0.2;
  const constraintFactor = Math.min((木 + 土) * 0.1, spouseStarEnergy * 0.3);
  
  const totalEnergy = Math.max(0, spouseStarEnergy + palaceEnergy - constraintFactor);
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  const met = totalEnergy >= (thresholdConfig.spouse_star_threshold * 100);

  return {
    marriage_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdConfig.spouse_star_threshold * 100, // 🔥 问题在这里！
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${(thresholdConfig.spouse_star_threshold * 100).toFixed(1)}%`
    }
  };
}

// 修正后的算法
function calculateMarriageEnergyFixed(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  const spouseStarEnergy = (金 + 火) * 0.3;
  const palaceEnergy = 水 * 0.2;
  const constraintFactor = Math.min((木 + 土) * 0.1, spouseStarEnergy * 0.3);
  
  const totalEnergy = Math.max(0, spouseStarEnergy + palaceEnergy - constraintFactor);
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  
  // 🔧 修正：阈值应该直接使用百分比数值，不需要乘以100
  const thresholdPercentage = thresholdConfig.spouse_star_threshold * 100; // 0.15 * 100 = 15
  const met = totalEnergy >= thresholdPercentage;

  return {
    marriage_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdPercentage, // 🔧 修正：直接使用15，而不是1500
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${thresholdPercentage.toFixed(1)}%`
    }
  };
}

// 调试函数
function debugThresholdCalculation() {
  console.log('🧪 ===== 阈值计算问题调试 =====\n');
  
  console.log('📋 输入数据:');
  console.log('  阈值配置:', thresholdConfig);
  console.log('  五行能量:', elementEnergies);
  
  console.log('\n🔍 问题版本计算:');
  const buggyResult = calculateMarriageEnergyBuggy(elementEnergies, thresholdConfig);
  console.log('  计算结果:', buggyResult);
  
  console.log('\n🔧 修正版本计算:');
  const fixedResult = calculateMarriageEnergyFixed(elementEnergies, thresholdConfig);
  console.log('  计算结果:', fixedResult);
  
  console.log('\n📊 对比分析:');
  
  const buggyData = buggyResult.marriage_energy;
  const fixedData = fixedResult.marriage_energy;
  
  console.log('  用户能量:');
  console.log(`    问题版本: ${buggyData.actual.toFixed(1)}%`);
  console.log(`    修正版本: ${fixedData.actual.toFixed(1)}%`);
  console.log(`    是否一致: ${Math.abs(buggyData.actual - fixedData.actual) < 0.1 ? '✅' : '❌'}`);
  
  console.log('  所需阈值:');
  console.log(`    问题版本: ${buggyData.required}%`);
  console.log(`    修正版本: ${fixedData.required}%`);
  console.log(`    差异: ${buggyData.required - fixedData.required}倍`);
  
  console.log('  达标状态:');
  console.log(`    问题版本: ${buggyData.met ? '✅ 达标' : '❌ 未达标'}`);
  console.log(`    修正版本: ${fixedData.met ? '✅ 达标' : '❌ 未达标'}`);
  
  console.log('  完成比例:');
  console.log(`    问题版本: ${buggyData.completion_ratio}`);
  console.log(`    修正版本: ${fixedData.completion_ratio}`);
  
  console.log('\n🎯 问题根源分析:');
  
  console.log('  阈值计算过程:');
  console.log(`    1. 配置值: ${thresholdConfig.spouse_star_threshold} (小数形式)`);
  console.log(`    2. 乘以100: ${thresholdConfig.spouse_star_threshold * 100} (百分比数值)`);
  console.log(`    3. 问题版本直接使用: ${thresholdConfig.spouse_star_threshold * 100}`);
  console.log(`    4. 但前端可能再次处理，导致显示错误`);
  
  console.log('\n💡 修正方案:');
  console.log('  1. 确保阈值计算一致性');
  console.log('  2. 统一使用百分比数值（15而不是0.15）');
  console.log('  3. 避免重复的单位转换');
  console.log('  4. 前端显示时正确处理数值');
  
  console.log('\n🔍 实际问题可能在于:');
  console.log('  - 数据传递过程中的单位转换错误');
  console.log('  - 前端显示逻辑的重复处理');
  console.log('  - 不同模块间的数据格式不一致');
  
  // 模拟前端可能的错误处理
  console.log('\n🚨 前端可能的错误处理模拟:');
  const frontendBuggyDisplay = fixedData.required * 100; // 错误地再次乘以100
  console.log(`  如果前端错误地再次乘以100: ${frontendBuggyDisplay}%`);
  console.log(`  这就解释了为什么显示3000%而不是15%`);
  
  return {
    problemIdentified: true,
    rootCause: '阈值计算中的重复单位转换',
    solution: '统一数据格式，避免重复转换',
    buggyThreshold: buggyData.required,
    fixedThreshold: fixedData.required
  };
}

// 运行调试
debugThresholdCalculation();
