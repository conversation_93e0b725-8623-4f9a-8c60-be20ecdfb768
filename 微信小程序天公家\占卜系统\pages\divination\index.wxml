<!-- pages/divination/index.wxml -->
<view class="divination-container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="nav-bar">
      <view class="nav-left" bindtap="goHome">
        <text class="nav-back">‹ 返回</text>
      </view>
      <view class="nav-title">玉匣记占卜</view>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- API连接状态 -->
  <view class="api-status" wx:if="{{!apiConnected}}">
    <view class="status-text">正在连接API服务...</view>
    <view class="status-tip">请确保FastAPI服务运行在localhost:8000</view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section" wx:if="{{apiConnected}}">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="请输入搜索关键词，如：嫁娶、出行、求财..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <button 
        class="search-btn" 
        bindtap="onSearch"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        搜索
      </button>
    </view>
    
    <view class="search-actions" wx:if="{{searchKeyword}}">
      <button class="clear-btn" bindtap="onClearSearch">清空</button>
    </view>
  </view>

  <!-- 随机占卜推荐 -->
  <view class="random-section" wx:if="{{apiConnected && randomDivination}}">
    <view class="section-header">
      <view class="section-title">今日推荐</view>
      <button class="refresh-btn" bindtap="onRandomTap">换一个</button>
    </view>
    
    <view class="divination-card random-card" bindtap="onDivinationTap" data-item="{{randomDivination}}">
      <view class="card-header">
        <view class="card-title">{{randomDivination.title}}</view>
        <view class="luck-badge luck-{{randomDivination.luck_level}}">
          {{randomDivination.luck_level === 'very_auspicious' ? '大吉' : 
            randomDivination.luck_level === 'auspicious' ? '吉' :
            randomDivination.luck_level === 'neutral' ? '平' :
            randomDivination.luck_level === 'inauspicious' ? '凶' :
            randomDivination.luck_level === 'very_inauspicious' ? '大凶' : '未知'}}
        </view>
      </view>
      
      <view class="card-content">
        <text class="content-text">{{randomDivination.description}}</text>
      </view>
      
      <view class="card-meta">
        <text class="meta-text">{{randomDivination.main_category}}</text>
        <text class="meta-text">{{randomDivination.content_type}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="results-section" wx:if="{{apiConnected}}">
    <!-- 搜索结果标题 -->
    <view class="section-header" wx:if="{{searchResults.length > 0}}">
      <view class="section-title">搜索结果</view>
      <view class="result-count">第{{currentPage}}页，共{{totalPages}}页</view>
    </view>
    
    <!-- 结果列表 -->
    <view class="result-list" wx:if="{{searchResults.length > 0}}">
      <view 
        class="divination-card result-card"
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="onDivinationTap"
        data-item="{{item}}"
      >
        <view class="card-header">
          <view class="card-title">{{item.title}}</view>
          <view class="luck-badge luck-{{item.luck_level}}">
            {{item.luck_level === 'very_auspicious' ? '大吉' : 
              item.luck_level === 'auspicious' ? '吉' :
              item.luck_level === 'neutral' ? '平' :
              item.luck_level === 'inauspicious' ? '凶' :
              item.luck_level === 'very_inauspicious' ? '大凶' : '未知'}}
          </view>
        </view>
        
        <view class="card-content">
          <text class="content-text">{{item.description}}</text>
        </view>
        
        <!-- 关键词标签 -->
        <view class="keywords" wx:if="{{item.keywords && item.keywords.length > 0}}">
          <text 
            class="keyword-tag" 
            wx:for="{{item.keywords}}" 
            wx:for-item="keyword"
            wx:key="keyword"
          >
            {{keyword}}
          </text>
        </view>
        
        <view class="card-meta">
          <text class="meta-text">{{item.main_category}}</text>
          <text class="meta-text">{{item.content_type}}</text>
        </view>
      </view>
    </view>
    
    <!-- 加载更多提示 -->
    <view class="load-more" wx:if="{{hasMore && searchResults.length > 0 && !loading}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-more" wx:if="{{loading && searchResults.length > 0}}">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 没有更多提示 -->
    <view class="no-more" wx:if="{{!hasMore && searchResults.length > 0}}">
      <text class="no-more-text">没有更多了</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{searchKeyword && searchResults.length === 0 && !loading}}">
      <view class="empty-icon">🔍</view>
      <view class="empty-text">没有找到相关占卜信息</view>
      <view class="empty-tip">试试其他关键词，如：嫁娶、出行、求财</view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section" wx:if="{{apiConnected && statistics}}">
    <view class="section-title">系统信息</view>
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{statistics.data.total_entries}}</view>
        <view class="stat-label">占卜条目</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{statistics.data.total_keywords}}</view>
        <view class="stat-label">关键词</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{statistics.data.total_chapters}}</view>
        <view class="stat-label">章节</view>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section" wx:if="{{apiConnected && searchResults.length === 0 && !searchKeyword}}">
    <view class="section-title">使用说明</view>
    <view class="help-content">
      <view class="help-item">
        <view class="help-title">🔍 搜索占卜</view>
        <view class="help-text">输入关键词搜索相关占卜信息，如：嫁娶、出行、求财等</view>
      </view>
      <view class="help-item">
        <view class="help-title">🎲 随机推荐</view>
        <view class="help-text">点击"换一个"获取随机占卜推荐</view>
      </view>
      <view class="help-item">
        <view class="help-title">📖 查看详情</view>
        <view class="help-text">点击任意占卜条目查看详细信息</view>
      </view>
    </view>
  </view>
</view>
