/**
 * 八字系统万年历模块
 * 基于压缩存储 + 内存索引的高性能万年历数据管理
 * 专为八字计算系统优化
 */

class BaziLunarCalendar {
    constructor() {
        this.isLoaded = false;
        this.ganzhiTable = [];
        this.yearIndex = {}; // {year: {month: {day: ganzhiIndex}}}
        this.yearGanzhiIndex = {}; // {year: ganzhiIndex}
        this.loadPromise = null;
    }

    /**
     * 初始化万年历数据
     * @param {string} dataUrl - 压缩数据文件URL，默认为 './lunar_data_compressed.json'
     * @returns {Promise<boolean>} 加载成功返回true
     */
    async initialize(dataUrl = './lunar_data_compressed.json') {
        // 避免重复加载
        if (this.loadPromise) {
            return await this.loadPromise;
        }

        this.loadPromise = this._loadData(dataUrl);
        return await this.loadPromise;
    }

    async _loadData(dataUrl) {
        if (this.isLoaded) return true;

        try {
            console.log('🔄 正在加载万年历数据...');
            const startTime = performance.now();

            // 加载压缩数据
            const response = await fetch(dataUrl);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const compressedData = await response.json();

            // 验证数据格式
            if (!compressedData.meta || !compressedData.data) {
                throw new Error('数据格式错误：缺少meta或data字段');
            }

            // 解析元数据
            this.ganzhiTable = compressedData.meta.ganzhi_table;
            
            // 构建年份索引
            this._buildYearIndex(compressedData.data);

            const loadTime = performance.now() - startTime;
            console.log(`✅ 万年历数据加载完成 (${loadTime.toFixed(2)}ms)`);
            console.log(`📊 数据范围: ${compressedData.meta.year_range[0]}-${compressedData.meta.year_range[1]}`);
            console.log(`📈 总记录数: ${compressedData.meta.total_records.toLocaleString()}`);

            this.isLoaded = true;
            return true;

        } catch (error) {
            console.error('❌ 万年历数据加载失败:', error);
            this.isLoaded = false;
            return false;
        }
    }

    _buildYearIndex(data) {
        for (const [yearStr, yearData] of Object.entries(data)) {
            const year = parseInt(yearStr);
            this.yearGanzhiIndex[year] = yearData.g;
            
            // 构建月日索引
            this.yearIndex[year] = {};
            for (const record of yearData.r) {
                const [month, day, ganzhiIndex] = record;
                
                if (!this.yearIndex[year][month]) {
                    this.yearIndex[year][month] = {};
                }
                this.yearIndex[year][month][day] = ganzhiIndex;
            }
        }
    }

    /**
     * 获取指定日期的干支
     * @param {number} year - 年份
     * @param {number} month - 月份 (1-12)
     * @param {number} day - 日期 (1-31)
     * @returns {Object|null} {gan: string, zhi: string, ganzhi: string} 或 null
     */
    getGanzhi(year, month, day) {
        if (!this.isLoaded) {
            console.warn('⚠️ 万年历数据未加载，请先调用 initialize()');
            return null;
        }

        const yearData = this.yearIndex[year];
        if (!yearData) {
            console.warn(`⚠️ 未找到 ${year} 年的数据`);
            return null;
        }

        const monthData = yearData[month];
        if (!monthData) {
            console.warn(`⚠️ 未找到 ${year}年${month}月 的数据`);
            return null;
        }

        const ganzhiIndex = monthData[day];
        if (ganzhiIndex === undefined) {
            console.warn(`⚠️ 未找到 ${year}年${month}月${day}日 的数据`);
            return null;
        }

        const ganzhi = this.ganzhiTable[ganzhiIndex];
        return {
            gan: ganzhi[0],
            zhi: ganzhi[1],
            ganzhi: ganzhi
        };
    }

    /**
     * 获取年份干支
     * @param {number} year - 年份
     * @returns {Object|null} {gan: string, zhi: string, ganzhi: string} 或 null
     */
    getYearGanzhi(year) {
        if (!this.isLoaded) {
            console.warn('⚠️ 万年历数据未加载，请先调用 initialize()');
            return null;
        }

        const ganzhiIndex = this.yearGanzhiIndex[year];
        if (ganzhiIndex === undefined) {
            console.warn(`⚠️ 未找到 ${year} 年的干支数据`);
            return null;
        }

        const ganzhi = this.ganzhiTable[ganzhiIndex];
        return {
            gan: ganzhi[0],
            zhi: ganzhi[1],
            ganzhi: ganzhi
        };
    }

    /**
     * 检查数据是否已加载
     * @returns {boolean}
     */
    isDataLoaded() {
        return this.isLoaded;
    }

    /**
     * 获取支持的年份范围
     * @returns {Array<number>|null} [startYear, endYear] 或 null
     */
    getSupportedYearRange() {
        if (!this.isLoaded) return null;
        
        const years = Object.keys(this.yearIndex).map(y => parseInt(y));
        return [Math.min(...years), Math.max(...years)];
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaziLunarCalendar;
} else if (typeof window !== 'undefined') {
    window.BaziLunarCalendar = BaziLunarCalendar;
}
