<!--pages/divination-input/index.wxml-->
<!-- 🎯 天公师兄主系统 - 集成版占卜界面 -->
<!-- 用途：完整的用户产品界面，多功能集成 -->
<!-- 特点：用户友好，完整体验，生产环境 -->
<scroll-view class="scroll-container" scroll-y="{{true}}" enhanced="{{false}}" show-scrollbar="{{false}}" enable-flex="{{true}}" scroll-with-animation="{{false}}" paging-enabled="{{false}}" fast-deceleration="{{false}}">
  <view class="container {{themeClass}}">
    <!-- 背景装饰 -->
    <view class="background-decoration"></view>
  
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="title-container">
      <image class="master-avatar" src="{{master === '天工师父' ? '/assets/icons/tiangong-shifu.png' : '/assets/icons/tiangong-master.svg'}}"></image>
    </view>
    <view class="description">
      <text>{{master}}\n无事不占，一事一占</text>
    </view>
  </view>



  <!-- 占卜方式选择 -->
  <view class="method-selection">
    <view class="section-title">
      <text>请选择占卜方式</text>
    </view>
    
    <view class="method-options">
      <view class="method-card {{selectedMethod === 'time' ? 'selected' : ''}}" 
            bindtap="selectMethod" data-method="time">
        <view class="method-icon">⏰</view>
        <view class="method-info">
          <text class="method-name">按时间占卜</text>
          <text class="method-desc">传统月日时起卦法</text>
        </view>
        <view class="method-badge">推荐</view>
      </view>
      
      <view class="method-card {{selectedMethod === 'number' ? 'selected' : ''}}" 
            bindtap="selectMethod" data-method="number">
        <view class="method-icon">🔢</view>
        <view class="method-info">
          <text class="method-name">按数字占卜</text>
          <text class="method-desc">数字取数变通法</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 真太阳时显示区域 -->
  <view class="solar-time-section" wx:if="{{trueSolarTime.showCorrection}}">
    <view class="section-title">
      <text>真太阳时校正</text>
      <text class="solar-time-badge">🌞 已启用</text>
    </view>

    <view class="solar-time-info">
      <view class="solar-time-item">
        <text class="solar-time-label">当前时间：</text>
        <text class="solar-time-value">{{currentTimeDisplay}}</text>
      </view>
      <view class="solar-time-item">
        <text class="solar-time-label">真太阳时：</text>
        <text class="solar-time-value corrected">{{trueSolarTime.correctedTimeDisplay}}</text>
      </view>
      <view class="solar-time-item">
        <text class="solar-time-label">时间差：</text>
        <text class="solar-time-value diff">
          {{trueSolarTime.timeDifference > 0 ? '+' : ''}}{{trueSolarTime.timeDifference}}分钟
        </text>
      </view>
    </view>
  </view>

  <!-- 位置获取失败提示 -->
  <view class="location-failed-section" wx:if="{{trueSolarTime.locationStatus === 'failed'}}">
    <view class="location-failed">
      <text class="failed-icon">⚠️</text>
      <text class="failed-text">位置获取失败，使用北京时间占卜</text>
      <text class="retry-btn" bindtap="initializeTrueSolarTime">重试</text>
    </view>
  </view>

  <!-- 问题输入区域 -->
  <view class="question-section">
    <view class="section-title">
      <text>请详细描述您的问题</text>
    </view>
    
    <view class="question-types">
      <view class="type-tag {{selectedType === item.value ? 'selected' : ''}}"
            wx:for="{{questionTypes}}" wx:key="value"
            bindtap="selectQuestionType" data-type="{{item.value}}">
        <text>{{item.label}}</text>
      </view>
    </view>
    
    <textarea class="question-input"
              placeholder="请描述您遇到的问题或困惑..."
              value="{{questionText}}"
              bindinput="onQuestionInput"
              maxlength="200"
              show-confirm-bar="{{false}}"
              auto-focus="{{false}}"
              cursor-spacing="10">
    </textarea>
    <view class="input-counter">
      <text>{{questionText.length}}/200</text>
    </view>
  </view>

  <!-- 数字输入区域（仅数字占卜时显示） -->
  <view class="number-section" wx:if="{{selectedMethod === 'number'}}">
    <view class="section-title">
      <text>请输入三个数字（1-9）</text>
    </view>
    <view class="number-inputs">
      <input class="number-input"
             type="number"
             placeholder="第一个数字"
             value="{{numbers[0]}}"
             bindinput="onNumberInput"
             data-index="0"
             maxlength="1" />
      <input class="number-input"
             type="number"
             placeholder="第二个数字"
             value="{{numbers[1]}}"
             bindinput="onNumberInput"
             data-index="1"
             maxlength="1" />
      <input class="number-input"
             type="number"
             placeholder="第三个数字"
             value="{{numbers[2]}}"
             bindinput="onNumberInput"
             data-index="2"
             maxlength="1" />
    </view>
    <view class="number-hint">
      <text>💡 可根据直觉选择，或使用生日、幸运数字等</text>
    </view>
  </view>

  <!-- 时间信息区域（仅时间占卜时显示） -->
  <view class="time-section" wx:if="{{selectedMethod === 'time'}}">
    <view class="section-title">
      <text>事件发生时间</text>
    </view>

    <!-- 时间选择器 -->
    <view class="time-picker-section">
      <view class="picker-item">
        <text class="picker-label">发生日期：</text>
        <picker mode="date"
                value="{{eventDate}}"
                bindchange="onEventDateChange"
                class="picker-input">
          <view class="picker-display">
            {{eventDate || '请选择日期'}}
          </view>
        </picker>
      </view>

      <view class="picker-item">
        <text class="picker-label">发生时间：</text>
        <picker mode="time"
                value="{{eventTime}}"
                bindchange="onEventTimeChange"
                class="picker-input">
          <view class="picker-display">
            {{eventTime || '请选择时间'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 时间选择提示 -->
    <view class="time-hint">
      <text>💡 请选择所问事情发生的时间</text>
    </view>

    <!-- 计算后的时间信息 -->
    <view class="time-info" wx:if="{{eventDateTime}}">
      <view class="time-item">
        <text class="time-label">事件时间：</text>
        <text class="time-value">{{eventDateTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">真太阳时：</text>
        <text class="time-value">{{eventTrueSolarTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">农历时间：</text>
        <text class="time-value">{{eventLunarTime}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">事件时辰：</text>
        <text class="time-value">{{eventShichen}}</text>
      </view>
    </view>
    
    <view class="location-info" wx:if="{{locationInfo}}">
      <text class="location-text">📍 {{locationInfo}}</text>
    </view>
    
    <view class="time-note">
      <text>⚠️ 李淳风六壬时课使用农历时间和真太阳时计算</text>
    </view>

    <!-- GPS授权说明 -->
    <view class="gps-notice">
      <view class="notice-icon">📍</view>
      <view class="notice-content">
        <text class="notice-title">为什么需要位置授权？</text>
        <text class="notice-desc">传统占卜需要精确的真太阳时，根据您的地理位置校正时间偏差，确保占卜结果准确。</text>
        <text class="notice-formula">计算公式：真太阳时 = 北京时间 + 经度差</text>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="action-section">
    <view class="divination-rules">
      <text>🔮 无事不占，一事一占，诚心为要</text>
    </view>
    
    <button class="start-divination-btn {{canStartDivination ? 'active' : 'disabled'}}"
            bindtap="startDivination"
            disabled="{{!canStartDivination}}">
      <text>开始占卜</text>
      <view class="btn-decoration"></view>
    </button>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-overlay" wx:if="{{isCalculating}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">天公师兄正在为您推演...</text>
    </view>
  </view>
  </view>
</scroll-view>
