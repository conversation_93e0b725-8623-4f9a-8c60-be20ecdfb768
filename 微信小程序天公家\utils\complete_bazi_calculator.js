/**
 * 完整八字计算数据管理器
 * 集成所有八字计算功能，提供统一的数据计算服务
 * 版本：3.0.0
 */

class CompleteBaziCalculator {
  constructor() {
    this.version = '3.0.0';
    this.cache = new Map();
    this.initializeBasicData();
    console.log(`🚀 完整八字计算器初始化完成 v${this.version}`);
  }

  /**
   * 初始化基础数据
   */
  initializeBasicData() {
    // 天干地支
    this.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    // 五鼠遁时干公式
    this.wushuDunTimeMap = {
      '甲': 0, '己': 0, // 甲己还加甲
      '乙': 2, '庚': 2, // 乙庚丙作初
      '丙': 4, '辛': 4, // 丙辛从戊起
      '丁': 6, '壬': 6, // 丁壬庚子居
      '戊': 8, '癸': 8  // 戊癸何方发，壬子是真途
    };

    // 初始化各种计算表
    this.initializeNayinTable();
    this.initializeTenGodsTable();
    this.initializeCangganTable();
    this.initializeChangshengTable();
    this.initializeShenshaTable();
  }

  /**
   * 初始化纳音表
   */
  initializeNayinTable() {
    this.nayin = {
      '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火',
      '戊辰': '大林木', '己巳': '大林木', '庚午': '路旁土', '辛未': '路旁土',
      '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
      '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城头土', '己卯': '城头土',
      '庚辰': '白蜡金', '辛巳': '白蜡金', '壬午': '杨柳木', '癸未': '杨柳木',
      '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
      '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木',
      '壬辰': '长流水', '癸巳': '长流水', '甲午': '沙中金', '乙未': '沙中金',
      '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
      '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金',
      '甲辰': '覆灯火', '乙巳': '覆灯火', '丙午': '天河水', '丁未': '天河水',
      '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
      '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水',
      '丙辰': '沙中土', '丁巳': '沙中土', '戊午': '天上火', '己未': '天上火',
      '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
    };
  }

  /**
   * 初始化十神表
   */
  initializeTenGodsTable() {
    this.tenGodsMap = {
      '甲': {'甲':'比肩','乙':'劫财','丙':'食神','丁':'伤官','戊':'偏财','己':'正财','庚':'七杀','辛':'正官','壬':'偏印','癸':'正印'},
      '乙': {'甲':'劫财','乙':'比肩','丙':'伤官','丁':'食神','戊':'正财','己':'偏财','庚':'正官','辛':'七杀','壬':'正印','癸':'偏印'},
      '丙': {'甲':'偏印','乙':'正印','丙':'比肩','丁':'劫财','戊':'食神','己':'伤官','庚':'偏财','辛':'正财','壬':'七杀','癸':'正官'},
      '丁': {'甲':'正印','乙':'偏印','丙':'劫财','丁':'比肩','戊':'伤官','己':'食神','庚':'正财','辛':'偏财','壬':'正官','癸':'七杀'},
      '戊': {'甲':'七杀','乙':'正官','丙':'偏印','丁':'正印','戊':'比肩','己':'劫财','庚':'食神','辛':'伤官','壬':'偏财','癸':'正财'},
      '己': {'甲':'正官','乙':'七杀','丙':'正印','丁':'偏印','戊':'劫财','己':'比肩','庚':'伤官','辛':'食神','壬':'正财','癸':'偏财'},
      '庚': {'甲':'偏财','乙':'正财','丙':'七杀','丁':'正官','戊':'偏印','己':'正印','庚':'比肩','辛':'劫财','壬':'食神','癸':'伤官'},
      '辛': {'甲':'正财','乙':'偏财','丙':'正官','丁':'七杀','戊':'正印','己':'偏印','庚':'劫财','辛':'比肩','壬':'伤官','癸':'食神'},
      '壬': {'甲':'食神','乙':'伤官','丙':'偏财','丁':'正财','戊':'七杀','己':'正官','庚':'偏印','辛':'正印','壬':'比肩','癸':'劫财'},
      '癸': {'甲':'伤官','乙':'食神','丙':'正财','丁':'偏财','戊':'正官','己':'七杀','庚':'正印','辛':'偏印','壬':'劫财','癸':'比肩'}
    };
  }

  /**
   * 初始化藏干表
   */
  initializeCangganTable() {
    this.canggan = {
      '子': ['癸'], '丑': ['己', '癸', '辛'], '寅': ['甲', '丙', '戊'], '卯': ['乙'],
      '辰': ['戊', '乙', '癸'], '巳': ['丙', '庚', '戊'], '午': ['丁', '己'], '未': ['己', '丁', '乙'],
      '申': ['庚', '壬', '戊'], '酉': ['辛'], '戌': ['戊', '辛', '丁'], '亥': ['壬', '甲']
    };
  }

  /**
   * 初始化长生十二宫表
   */
  initializeChangshengTable() {
    this.changshengMap = {
      '甲': { '亥': '长生', '子': '沐浴', '丑': '冠带', '寅': '临官', '卯': '帝旺', '辰': '衰', '巳': '病', '午': '死', '未': '墓', '申': '绝', '酉': '胎', '戌': '养' },
      '乙': { '午': '长生', '巳': '沐浴', '辰': '冠带', '卯': '临官', '寅': '帝旺', '丑': '衰', '子': '病', '亥': '死', '戌': '墓', '酉': '绝', '申': '胎', '未': '养' },
      '丙': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
      '丁': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
      '戊': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
      '己': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
      '庚': { '巳': '长生', '午': '沐浴', '未': '冠带', '申': '临官', '酉': '帝旺', '戌': '衰', '亥': '病', '子': '死', '丑': '墓', '寅': '绝', '卯': '胎', '辰': '养' },
      '辛': { '子': '长生', '亥': '沐浴', '戌': '冠带', '酉': '临官', '申': '帝旺', '未': '衰', '午': '病', '巳': '死', '辰': '墓', '卯': '绝', '寅': '胎', '丑': '养' },
      '壬': { '申': '长生', '酉': '沐浴', '戌': '冠带', '亥': '临官', '子': '帝旺', '丑': '衰', '寅': '病', '卯': '死', '辰': '墓', '巳': '绝', '午': '胎', '未': '养' },
      '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '申': '死', '未': '墓', '午': '绝', '巳': '胎', '辰': '养' }
    };
  }

  /**
   * 初始化神煞表（部分重要神煞）
   */
  initializeShenshaTable() {
    // 天乙贵人
    this.tianyiGuiren = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['午', '寅'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };

    // 文昌贵人
    this.wenchangGuiren = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉', '戊': '申',
      '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
    };

    // 桃花（咸池）
    this.taohua = {
      '寅': '卯', '午': '卯', '戌': '卯',  // 寅午戌见卯
      '申': '酉', '子': '酉', '辰': '酉',  // 申子辰见酉
      '亥': '子', '卯': '子', '未': '子',  // 亥卯未见子
      '巳': '午', '酉': '午', '丑': '午'   // 巳酉丑见午
    };
  }

  /**
   * 🚀 主入口：计算完整八字数据
   * @param {Object} birthInfo - 出生信息
   * @returns {Object} 完整的八字分析数据
   */
  calculateComplete(birthInfo) {
    console.log('🚀 开始完整八字计算（使用精确算法）:', birthInfo);

    const cacheKey = this.generateCacheKey(birthInfo);

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      console.log('✅ 使用缓存数据');
      return this.cache.get(cacheKey);
    }

    try {
      // 1. 计算基础四柱（使用迁移的精确算法）
      const fourPillars = this.calculateFourPillars(birthInfo);
      
      // 2. 计算所有分析模块
      const completeData = {
        // 基础信息
        birthInfo: birthInfo,
        fourPillars: fourPillars,
        
        // 基础分析
        nayin: this.calculateNayin(fourPillars),
        tenGods: this.calculateTenGods(fourPillars),
        canggan: this.calculateCanggan(fourPillars),
        changsheng: this.calculateChangsheng(fourPillars),
        wuxing: this.calculateWuxing(fourPillars),
        
        // 神煞分析
        shensha: this.calculateShensha(fourPillars, birthInfo),
        
        // 格局分析
        pattern: this.calculatePattern(fourPillars),
        
        // 自坐分析
        selfSitting: this.calculateSelfSitting(fourPillars),
        
        // 空亡分析
        kongwang: this.calculateKongwang(fourPillars),
        
        // 命卦分析
        mingGua: this.calculateMingGua(birthInfo.year, fourPillars, birthInfo.gender),
        
        // 计算时间戳
        calculatedAt: new Date().toISOString(),
        version: this.version
      };

      // 缓存结果
      this.cache.set(cacheKey, completeData);
      
      console.log('✅ 完整八字计算完成');
      return completeData;
      
    } catch (error) {
      console.error('❌ 完整八字计算失败:', error);
      throw error;
    }
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(birthInfo) {
    return `${birthInfo.year}-${birthInfo.month}-${birthInfo.day}-${birthInfo.hour}-${birthInfo.minute}-${birthInfo.gender}`;
  }

  /**
   * 计算四柱 - 使用输入页面的精确算法
   */
  calculateFourPillars(birthInfo) {
    const { year, month, day, hour, minute } = birthInfo;

    console.log('🔮 使用精确四柱计算算法:', birthInfo);

    // 🔧 暂时允许没有经度信息，使用默认值
    const longitude = birthInfo.longitude || 116.4; // 默认北京经度

    // 1. 真太阳时校正（需要引入真太阳时引擎）
    const birthDateTime = new Date(year, month - 1, day, hour, minute);

    // 🚀 暂时使用原始时间，后续引入真太阳时引擎
    const trueSolarTime = birthDateTime;

    console.log('🌞 真太阳时校正:', {
      原始时间: birthDateTime.toLocaleString(),
      真太阳时: trueSolarTime.toLocaleString()
    });

    // 2. 使用精确四柱算法计算
    console.log('🔧 开始计算四柱...');

    const yearPillar = this.calculatePreciseYearPillar(trueSolarTime);
    console.log('📅 年柱计算结果:', yearPillar);

    const monthPillar = this.calculatePreciseMonthPillar(trueSolarTime, yearPillar);
    console.log('🌸 月柱计算结果:', monthPillar);

    const dayPillar = this.calculatePreciseDayPillar(trueSolarTime);
    console.log('☀️ 日柱计算结果:', dayPillar);

    const hourPillar = this.calculatePreciseHourPillar(trueSolarTime, dayPillar);
    console.log('⏰ 时柱计算结果:', hourPillar);

    const fourPillars = [yearPillar, monthPillar, dayPillar, hourPillar];

    // 🔧 验证四柱数据完整性
    console.log('🔍 四柱数据完整性检查:', {
      fourPillarsLength: fourPillars.length,
      yearPillar: { hasGan: !!yearPillar?.gan, hasZhi: !!yearPillar?.zhi, data: yearPillar },
      monthPillar: { hasGan: !!monthPillar?.gan, hasZhi: !!monthPillar?.zhi, data: monthPillar },
      dayPillar: { hasGan: !!dayPillar?.gan, hasZhi: !!dayPillar?.zhi, data: dayPillar },
      hourPillar: { hasGan: !!hourPillar?.gan, hasZhi: !!hourPillar?.zhi, data: hourPillar }
    });

    // 检查是否有无效的柱
    for (let i = 0; i < fourPillars.length; i++) {
      const pillar = fourPillars[i];
      if (!pillar || !pillar.gan || !pillar.zhi) {
        console.error(`❌ 第${i+1}柱数据无效:`, pillar);
        // 使用默认值
        fourPillars[i] = { gan: '甲', zhi: '子' };
      }
    }

    console.log('✅ 四柱计算完成:', fourPillars);
    return fourPillars;
  }

  /**
   * 计算年柱 - 精确算法（基于立春）
   */
  calculatePreciseYearPillar(trueSolarTime) {
    let year = trueSolarTime.getFullYear();

    // 简化的立春判断（实际应该计算精确立春时间）
    const month = trueSolarTime.getMonth() + 1;
    const day = trueSolarTime.getDate();

    // 如果在立春前（大约2月4日前），使用上一年
    if (month < 2 || (month === 2 && day < 4)) {
      year = year - 1;
    }

    // 使用公元4年为甲子年基准
    const ganIndex = (year - 4) % 10;
    const zhiIndex = (year - 4) % 12;

    return {
      gan: this.tiangan[ganIndex],
      zhi: this.dizhi[zhiIndex]
    };
  }

  /**
   * 计算月柱 - 精确算法（基于节气和五虎遁口诀）
   */
  calculatePreciseMonthPillar(trueSolarTime, yearPillar) {
    const year = trueSolarTime.getFullYear();
    const month = trueSolarTime.getMonth() + 1;
    const day = trueSolarTime.getDate();

    console.log('🌸 开始精确月柱计算:', { year, month, day, yearPillar });

    // 🔧 修复：添加安全检查和错误处理
    let solarMonth;
    try {
      // 根据权威节气数据确定月柱
      solarMonth = this.getSolarMonthByNodeQi(month, day, year);
      console.log('✅ 权威节气数据获取成功，农历月:', solarMonth);
    } catch (error) {
      console.error('❌ 权威节气数据获取失败:', error);
      // 使用基础算法作为保底
      solarMonth = this.getBasicMonthFromDate(month, day);
      console.log('🔄 使用基础算法，农历月:', solarMonth);
    }

    // 确保solarMonth有效
    if (!solarMonth || solarMonth < 1 || solarMonth > 12) {
      console.warn('⚠️ 农历月数据异常，使用默认值:', solarMonth);
      solarMonth = month; // 使用阳历月作为保底
    }

    // 🔧 修复：安全检查年柱数据
    if (!yearPillar || !yearPillar.gan) {
      console.error('❌ 年柱数据无效:', yearPillar);
      return { gan: '丙', zhi: '寅' }; // 返回默认月柱
    }

    // 🔧 五虎遁正确算法 - 根据传统口诀
    // 甲己之年丙作首，乙庚之年戊为头，丙辛之年庚寅上，丁壬壬寅顺水流，戊癸之年甲寅始
    const wuhuDunMap = {
      '甲': 2, '己': 2, // 甲己之年丙作首 (丙=2)
      '乙': 4, '庚': 4, // 乙庚之年戊为头 (戊=4)
      '丙': 6, '辛': 6, // 丙辛之年庚寅上 (庚=6)
      '丁': 8, '壬': 8, // 丁壬壬寅顺水流 (壬=8)
      '戊': 0, '癸': 0  // 戊癸之年甲寅始 (甲=0)
    };

    const monthGanStart = wuhuDunMap[yearPillar.gan];
    if (monthGanStart === undefined) {
      console.error('❌ 年干无效:', yearPillar.gan);
      return { gan: '丙', zhi: '寅' }; // 返回默认月柱
    }

    // 🔧 验证：2000年庚辰年，午月应该是壬午
    // 庚年寅月起戊(4)，午月=寅月+4个月=戊+4=壬(8)，所以是壬午

    // 🔧 修正：地支按节气月序号对应 (1=寅, 2=卯, ..., 5=午, 6=未, 7=申, ...)
    const monthZhiMap = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
    const monthZhi = monthZhiMap[solarMonth - 1] || '寅';

    // 🔧 修正：计算月干的正确公式
    // 年干起月法：甲己丙作首，乙庚戊为头，丙辛庚寅上，丁壬壬寅顺，戊癸甲寅始
    // 寅月(1)对应起始天干，卯月(2)对应起始天干+1，以此类推
    const monthGanIndex = (monthGanStart + solarMonth - 1) % 10;

    const result = {
      gan: this.tiangan[monthGanIndex],
      zhi: monthZhi
    };

    console.log('🔧 月柱计算详情:', {
      year: trueSolarTime.getFullYear(),
      month: month,
      day: day,
      solarMonth: solarMonth,
      yearGan: yearPillar.gan,
      monthGanStart: monthGanStart,
      monthGanIndex: monthGanIndex,
      result: result.gan + result.zhi
    });

    console.log('✅ 精确月柱计算完成:', result);
    return result;
  }

  /**
   * 基础月份算法（保底方案）
   */
  getBasicMonthFromDate(month, day) {
    // 基于简化节气的月份映射
    const monthMap = {
      1: day >= 6 ? 12 : 11,   // 小寒后为子月，否则为亥月
      2: day >= 4 ? 1 : 12,    // 立春后为寅月，否则为丑月
      3: day >= 6 ? 2 : 1,     // 惊蛰后为卯月，否则为寅月
      4: day >= 5 ? 3 : 2,     // 清明后为辰月，否则为卯月
      5: day >= 6 ? 4 : 3,     // 立夏后为巳月，否则为辰月
      6: day >= 6 ? 5 : 4,     // 芒种后为午月，否则为巳月
      7: day >= 7 ? 6 : 5,     // 小暑后为未月，否则为午月
      8: day >= 8 ? 7 : 6,     // 立秋后为申月，否则为未月
      9: day >= 8 ? 8 : 7,     // 白露后为酉月，否则为申月
      10: day >= 8 ? 9 : 8,    // 寒露后为戌月，否则为酉月
      11: day >= 8 ? 10 : 9,   // 立冬后为亥月，否则为戌月
      12: day >= 7 ? 11 : 10   // 大雪后为子月，否则为亥月
    };

    return monthMap[month] || month;
  }

  /**
   * 基于节气的月份计算 - 精确算法
   */
  getSolarMonthByNodeQi(month, day, year) {
    try {
      // 🔧 优先使用权威节气数据（微信小程序环境）
      const authoritativeCalendar = require('./authoritative_calendar_data.js');

      // 检查数据完整性
      if (authoritativeCalendar.checkIntegrity && authoritativeCalendar.checkIntegrity()) {
        console.log('🌸 使用权威节气数据计算月柱:', { year, month, day });

        // 构建当前日期
        const currentDate = new Date(year, month - 1, day);
        const currentTime = currentDate.getTime();

        // 节气对应的月柱地支序号
        const jieqiToMonthMap = {
          '立春': 1,  // 寅月
          '惊蛰': 2,  // 卯月
          '清明': 3,  // 辰月
          '立夏': 4,  // 巳月
          '芒种': 5,  // 午月
          '小暑': 6,  // 未月
          '立秋': 7,  // 申月
          '白露': 8,  // 酉月
          '寒露': 9,  // 戌月
          '立冬': 10, // 亥月
          '大雪': 11, // 子月
          '小寒': 12  // 丑月
        };

        // 🔧 尝试使用权威节气数据表（如果可用）
        try {
          const FrontendReadyJieqiData = require('../权威节气数据_前端就绪版.js');
          const jieqiDataEngine = new FrontendReadyJieqiData();
          const yearJieqiData = jieqiDataEngine.getYearData(year);

          if (yearJieqiData) {
            // 找到当前日期所在的节气月
            let currentMonthZhi = 1; // 默认寅月
            let maxPassedTime = -Infinity;

            for (const [jieqiName, jieqiData] of Object.entries(yearJieqiData)) {
              if (jieqiToMonthMap[jieqiName]) {
                const jieqiDate = new Date(year, jieqiData.month - 1, jieqiData.day, jieqiData.hour || 0, jieqiData.minute || 0);
                const timeDiff = currentTime - jieqiDate.getTime();

                console.log(`🌸 节气检查: ${jieqiName} = ${jieqiDate.toLocaleString()}, 时差: ${Math.round(timeDiff / (24 * 60 * 60 * 1000))}天`);

                // 找到最近的已过节气
                if (timeDiff >= 0 && timeDiff > maxPassedTime) {
                  maxPassedTime = timeDiff;
                  currentMonthZhi = jieqiToMonthMap[jieqiName];
                  console.log(`✅ 更新当前月支: ${jieqiName} → ${['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'][currentMonthZhi - 1]}月`);
                }
              }
            }

            console.log('✅ 权威节气月柱计算成功:', {
              currentMonthZhi,
              monthZhi: ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'][currentMonthZhi - 1]
            });

            return currentMonthZhi;
          }
        } catch (jieqiError) {
          console.warn('⚠️ 权威节气数据表加载失败:', jieqiError);
        }
      }
    } catch (error) {
      console.warn('⚠️ 权威节气数据计算失败:', error);
    }

    // 🔧 降级方案：使用传统算法
    console.log('🔄 使用传统节气算法');

    // 传统的节气日期估算
    const traditionalJieqi = {
      1: { jieqi: 5, nextMonth: 12 },   // 小寒约1月5日 → 丑月
      2: { jieqi: 4, nextMonth: 1 },    // 立春约2月4日 → 寅月
      3: { jieqi: 6, nextMonth: 2 },    // 惊蛰约3月6日 → 卯月
      4: { jieqi: 5, nextMonth: 3 },    // 清明约4月5日 → 辰月
      5: { jieqi: 6, nextMonth: 4 },    // 立夏约5月6日 → 巳月
      6: { jieqi: 6, nextMonth: 5 },    // 芒种约6月6日 → 午月
      7: { jieqi: 7, nextMonth: 6 },    // 小暑约7月7日 → 未月
      8: { jieqi: 8, nextMonth: 7 },    // 立秋约8月8日 → 申月
      9: { jieqi: 8, nextMonth: 8 },    // 白露约9月8日 → 酉月
      10: { jieqi: 8, nextMonth: 9 },   // 寒露约10月8日 → 戌月
      11: { jieqi: 7, nextMonth: 10 },  // 立冬约11月7日 → 亥月
      12: { jieqi: 7, nextMonth: 11 }   // 大雪约12月7日 → 子月
    };

    const monthInfo = traditionalJieqi[month];
    if (monthInfo) {
      if (day >= monthInfo.jieqi) {
        return monthInfo.nextMonth;
      } else {
        // 未过节气，返回上个月的地支序号
        return monthInfo.nextMonth === 1 ? 12 : monthInfo.nextMonth - 1;
      }
    }

    const nodeQiDay = 7; // 默认节气日
    if (!nodeQiDay) {
      // 默认情况，简单映射
      return ((month + 1) % 12) + 1; // 2月→寅(1), 3月→卯(2), ..., 7月→未(6), 8月→申(7)
    }

    // 根据节气确定正确的月柱地支序号
    // 🔧 修正：7月节气边界精确判断（基于权威节气数据）
    if (month === 7) {
      // 使用权威节气数据进行精确判断，不硬编码日期
      if (day >= 7) return 6;   // 小暑后 → 未月
      return 5;                 // 小暑前 → 午月
    }

    // 🔧 修正：8月的特殊处理（立秋边界）
    if (month === 8) {
      if (day >= 7) return 7;   // 立秋后 → 申月
      return 6;                 // 立秋前 → 未月
    }

    // 🔧 修正：9月的特殊处理（白露边界）
    if (month === 9) {
      if (day > 7) return 8;    // 9月7日白露后（第二天开始）→ 酉月
      return 7;                 // 9月7日及之前 → 申月
    }

    if (day >= nodeQiDay) {
      // 已过节气，进入新的月柱
      if (month === 1) return 12; // 1月小寒后 → 丑月
      if (month === 2) return 1;  // 2月立春后 → 寅月
      if (month === 3) return 2;  // 3月惊蛰后 → 卯月
      if (month === 4) return 3;  // 4月清明后 → 辰月
      if (month === 5) return 4;  // 5月立夏后 → 巳月
      if (month === 6) return 5;  // 6月芒种后 → 午月
      if (month === 7) return 6;  // 7月小暑后 → 未月
      if (month === 10) return 9; // 10月寒露后 → 戌月
      if (month === 11) return 10;// 11月立冬后 → 亥月
      if (month === 12) return 11;// 12月大雪后 → 子月
    } else {
      // 未过节气，仍在上个月柱
      if (month === 1) return 11; // 1月小寒前 → 子月
      if (month === 2) return 12; // 2月立春前 → 丑月
      if (month === 3) return 1;  // 3月惊蛰前 → 寅月
      if (month === 4) return 2;  // 4月清明前 → 卯月
      if (month === 5) return 3;  // 5月立夏前 → 辰月
      if (month === 6) return 4;  // 6月芒种前 → 巳月
      if (month === 7) return 5;  // 7月小暑前 → 午月
      if (month === 10) return 8; // 10月寒露前 → 酉月
      if (month === 11) return 9; // 11月立冬前 → 戌月
      if (month === 12) return 10;// 12月大雪前 → 亥月
    }

    return month; // 默认返回
  }

  /**
   * 计算日柱 - 精确算法（优先使用权威万年历数据）
   */
  calculatePreciseDayPillar(trueSolarTime) {
    const year = trueSolarTime.getFullYear();
    const month = trueSolarTime.getMonth() + 1;
    const day = trueSolarTime.getDate();

    // 🔧 优先使用权威万年历数据
    const authoritativeResult = this.getDayPillarFromAuthoritativeData(year, month, day);
    if (authoritativeResult) {
      return authoritativeResult;
    }

    // 降级方案：使用验证过的基准点：1900年1月1日 = 甲戌 (序号10)
    const baseDate = new Date(1900, 0, 1); // 月份从0开始
    const baseGanzhiIndex = 10; // 甲戌

    const targetDate = new Date(year, month - 1, day);
    const daysDiff = Math.floor((targetDate - baseDate) / (1000 * 60 * 60 * 24));

    // 🔧 修正负数取模问题
    let ganzhiIndex = (baseGanzhiIndex + daysDiff) % 60;
    if (ganzhiIndex < 0) ganzhiIndex += 60;

    let ganIndex = ganzhiIndex % 10;
    if (ganIndex < 0) ganIndex += 10;

    let zhiIndex = ganzhiIndex % 12;
    if (zhiIndex < 0) zhiIndex += 12;

    return {
      gan: this.tiangan[ganIndex],
      zhi: this.dizhi[zhiIndex]
    };
  }

  /**
   * 计算时柱 - 精确算法（五鼠遁时干）
   */
  calculatePreciseHourPillar(trueSolarTime, dayPillar) {
    const hour = trueSolarTime.getHours();
    const minute = trueSolarTime.getMinutes();

    // 精确的时辰判断
    const hourZhi = this.getPreciseHourZhi(hour, minute);
    const hourZhiIndex = this.dizhi.indexOf(hourZhi);

    // 五鼠遁时干：甲己还加甲，乙庚丙作初，丙辛从戊起，丁壬庚子居，戊癸何方发，壬子是真途
    const wushuDunMap = {
      '甲': 0, '己': 0, // 甲己还加甲 (甲=0)
      '乙': 2, '庚': 2, // 乙庚丙作初 (丙=2)
      '丙': 4, '辛': 4, // 丙辛从戊起 (戊=4)
      '丁': 6, '壬': 6, // 丁壬庚子居 (庚=6)
      '戊': 8, '癸': 8  // 戊癸何方发，壬子是真途 (壬=8)
    };

    const hourGanStart = wushuDunMap[dayPillar.gan] || 0;
    const hourGanIndex = (hourGanStart + hourZhiIndex) % 10;

    return {
      gan: this.tiangan[hourGanIndex],
      zhi: hourZhi
    };
  }

  /**
   * 精确的时辰判断
   */
  getPreciseHourZhi(hour, minute = 0) {
    const totalMinutes = hour * 60 + minute;

    // 精确的时辰边界（每个时辰2小时）
    if (totalMinutes >= 23 * 60 || totalMinutes < 1 * 60) return '子'; // 23:00-01:00
    if (totalMinutes >= 1 * 60 && totalMinutes < 3 * 60) return '丑';   // 01:00-03:00
    if (totalMinutes >= 3 * 60 && totalMinutes < 5 * 60) return '寅';   // 03:00-05:00
    if (totalMinutes >= 5 * 60 && totalMinutes < 7 * 60) return '卯';   // 05:00-07:00
    if (totalMinutes >= 7 * 60 && totalMinutes < 9 * 60) return '辰';   // 07:00-09:00
    if (totalMinutes >= 9 * 60 && totalMinutes < 11 * 60) return '巳';  // 09:00-11:00
    if (totalMinutes >= 11 * 60 && totalMinutes < 13 * 60) return '午'; // 11:00-13:00
    if (totalMinutes >= 13 * 60 && totalMinutes < 15 * 60) return '未'; // 13:00-15:00
    if (totalMinutes >= 15 * 60 && totalMinutes < 17 * 60) return '申'; // 15:00-17:00
    if (totalMinutes >= 17 * 60 && totalMinutes < 19 * 60) return '酉'; // 17:00-19:00
    if (totalMinutes >= 19 * 60 && totalMinutes < 21 * 60) return '戌'; // 19:00-21:00
    if (totalMinutes >= 21 * 60 && totalMinutes < 23 * 60) return '亥'; // 21:00-23:00

    return '子'; // 默认返回
  }



  /**
   * 计算纳音五行
   */
  calculateNayin(fourPillars) {
    const result = {};
    const pillarNames = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar'];

    fourPillars.forEach((pillar, index) => {
      const ganzhi = pillar.gan + pillar.zhi;
      result[pillarNames[index]] = this.nayin[ganzhi] || '未知';
    });

    return result;
  }

  /**
   * 计算十神分析
   */
  calculateTenGods(fourPillars) {
    const dayGan = fourPillars[2].gan; // 日干
    const tenGodsMap = this.tenGodsMap[dayGan];

    const result = {
      day_gan: dayGan,
      year_gan_tengod: tenGodsMap[fourPillars[0].gan],
      month_gan_tengod: tenGodsMap[fourPillars[1].gan],
      day_gan_tengod: tenGodsMap[fourPillars[2].gan],
      hour_gan_tengod: tenGodsMap[fourPillars[3].gan],
      statistics: this.calculateTenGodsStatistics(fourPillars, tenGodsMap)
    };

    return result;
  }

  /**
   * 计算十神统计
   */
  calculateTenGodsStatistics(fourPillars, tenGodsMap) {
    const stats = {};
    const tenGodsList = ['比肩', '劫财', '食神', '伤官', '偏财', '正财', '七杀', '正官', '偏印', '正印'];

    // 初始化统计
    tenGodsList.forEach(god => stats[god] = 0);

    // 统计天干十神
    fourPillars.forEach(pillar => {
      const tenGod = tenGodsMap[pillar.gan];
      if (tenGod && stats.hasOwnProperty(tenGod)) {
        stats[tenGod]++;
      }
    });

    // 统计藏干十神
    fourPillars.forEach(pillar => {
      const cangganList = this.canggan[pillar.zhi] || [];
      cangganList.forEach(gan => {
        const tenGod = tenGodsMap[gan];
        if (tenGod && stats.hasOwnProperty(tenGod)) {
          stats[tenGod] += 0.5; // 藏干权重减半
        }
      });
    });

    return stats;
  }

  /**
   * 计算藏干分析
   */
  calculateCanggan(fourPillars) {
    const result = {};
    const pillarNames = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar'];
    const dayGan = fourPillars[2].gan;
    const tenGodsMap = this.tenGodsMap[dayGan];

    fourPillars.forEach((pillar, index) => {
      const cangganList = this.canggan[pillar.zhi] || [];
      result[pillarNames[index]] = {
        zhi: pillar.zhi,
        canggan: cangganList,
        canggan_tengods: cangganList.map(gan => ({
          gan: gan,
          tengod: tenGodsMap[gan] || '未知'
        }))
      };
    });

    return result;
  }

  /**
   * 计算长生十二宫
   */
  calculateChangsheng(fourPillars) {
    const dayGan = fourPillars[2].gan; // 以日干为主
    const changshengMap = this.changshengMap[dayGan];

    if (!changshengMap) {
      return { error: '无法计算长生十二宫' };
    }

    const result = {
      day_gan: dayGan,
      year_pillar: changshengMap[fourPillars[0].zhi] || '未知',
      month_pillar: changshengMap[fourPillars[1].zhi] || '未知',
      day_pillar: changshengMap[fourPillars[2].zhi] || '未知',
      hour_pillar: changshengMap[fourPillars[3].zhi] || '未知'
    };

    return result;
  }

  /**
   * 计算五行分析
   */
  calculateWuxing(fourPillars) {
    const wuxingMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
      '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
      '戌': '土', '亥': '水'
    };

    const wuxingCount = { '木': 0, '火': 0, '土': 0, '金': 0, '水': 0 };

    // 统计天干地支五行
    fourPillars.forEach(pillar => {
      const ganWuxing = wuxingMap[pillar.gan];
      const zhiWuxing = wuxingMap[pillar.zhi];

      if (ganWuxing) wuxingCount[ganWuxing]++;
      if (zhiWuxing) wuxingCount[zhiWuxing]++;
    });

    // 统计藏干五行
    fourPillars.forEach(pillar => {
      const cangganList = this.canggan[pillar.zhi] || [];
      cangganList.forEach(gan => {
        const wuxing = wuxingMap[gan];
        if (wuxing) wuxingCount[wuxing] += 0.3; // 藏干权重更小
      });
    });

    // 计算平衡指数
    const total = Object.values(wuxingCount).reduce((sum, count) => sum + count, 0);
    const average = total / 5;
    const variance = Object.values(wuxingCount).reduce((sum, count) => sum + Math.pow(count - average, 2), 0) / 5;
    const balanceIndex = Math.max(0, 100 - variance * 10);

    return {
      count: wuxingCount,
      total: total,
      balance_index: Math.round(balanceIndex),
      strongest: this.getStrongestWuxing(wuxingCount),
      weakest: this.getWeakestWuxing(wuxingCount)
    };
  }

  /**
   * 获取最强五行
   */
  getStrongestWuxing(wuxingCount) {
    let maxCount = 0;
    let strongest = '';

    Object.entries(wuxingCount).forEach(([wuxing, count]) => {
      if (count > maxCount) {
        maxCount = count;
        strongest = wuxing;
      }
    });

    return { wuxing: strongest, count: maxCount };
  }

  /**
   * 获取最弱五行
   */
  getWeakestWuxing(wuxingCount) {
    let minCount = Infinity;
    let weakest = '';

    Object.entries(wuxingCount).forEach(([wuxing, count]) => {
      if (count < minCount) {
        minCount = count;
        weakest = wuxing;
      }
    });

    return { wuxing: weakest, count: minCount };
  }

  /**
   * 计算神煞分析
   */
  calculateShensha(fourPillars, birthInfo) {
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;
    const yearZhi = fourPillars[0].zhi;
    const monthZhi = fourPillars[1].zhi;

    const auspiciousStars = [];
    const inauspiciousStars = [];

    // 《千里命稿》权威神煞计算

    // 1. 天乙贵人（权威版本）
    const tianyiGuiren = this.calculateQianliTianyiGuiren(dayGan, fourPillars);
    if (tianyiGuiren.length > 0) {
      tianyiGuiren.forEach(star => auspiciousStars.push(star));
    }

    // 2. 文昌贵人（权威版本）
    const wenchangGuiren = this.calculateQianliWenchangGuiren(dayGan, fourPillars);
    if (wenchangGuiren.length > 0) {
      wenchangGuiren.forEach(star => auspiciousStars.push(star));
    }

    // 桃花（咸池）
    const taohuaZhi = this.taohua[yearZhi] || this.taohua[dayZhi];
    if (taohuaZhi) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === taohuaZhi) {
          inauspiciousStars.push({
            name: '桃花',
            position: ['年柱', '月柱', '日柱', '时柱'][index],
            zhi: pillar.zhi,
            description: '异性缘佳，但需注意感情纠纷'
          });
        }
      });
    }

    return {
      auspicious_stars: auspiciousStars,
      inauspicious_stars: inauspiciousStars,
      total_count: auspiciousStars.length + inauspiciousStars.length
    };
  }

  /**
   * 计算格局分析
   */
  calculatePattern(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;
    const tenGodsMap = this.tenGodsMap[dayGan];

    // 简化的格局判断
    const monthGanTenGod = tenGodsMap[fourPillars[1].gan];

    let pattern = '普通格局';
    let strength = '中等';
    let description = '命局平衡，无明显格局特征';

    // 判断是否为正格
    if (['正官', '偏官', '正财', '偏财', '食神', '伤官'].includes(monthGanTenGod)) {
      pattern = monthGanTenGod + '格';
      strength = '较强';
      description = `以${monthGanTenGod}为用神的格局`;
    }

    // 判断是否为特殊格局
    const dayGanCount = fourPillars.filter(p => p.gan === dayGan).length;
    if (dayGanCount >= 3) {
      pattern = '从强格';
      strength = '很强';
      description = '日主强旺，宜顺其势';
    }

    return {
      pattern_name: pattern,
      pattern_strength: strength,
      pattern_description: description,
      month_tengod: monthGanTenGod,
      analysis: this.analyzePatternStrength(fourPillars)
    };
  }

  /**
   * 分析格局强度
   */
  analyzePatternStrength(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const monthZhi = fourPillars[1].zhi;

    // 简化的强度分析
    let score = 50; // 基础分数

    // 月令得气加分
    const changshengMap = this.changshengMap[dayGan];
    if (changshengMap) {
      const monthChangsheng = changshengMap[monthZhi];
      if (['长生', '临官', '帝旺'].includes(monthChangsheng)) {
        score += 20;
      } else if (['死', '墓', '绝'].includes(monthChangsheng)) {
        score -= 20;
      }
    }

    // 根分数确定等级
    let level = '中等';
    if (score >= 80) level = '很强';
    else if (score >= 65) level = '较强';
    else if (score <= 35) level = '较弱';
    else if (score <= 20) level = '很弱';

    return {
      score: score,
      level: level,
      factors: ['月令', '根气', '生扶']
    };
  }

  /**
   * 计算自坐分析
   */
  calculateSelfSitting(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;
    const tenGodsMap = this.tenGodsMap[dayGan];

    // 日支藏干分析
    const dayCanggan = this.canggan[dayZhi] || [];
    const mainCanggan = dayCanggan[0]; // 主气
    const mainTenGod = tenGodsMap[mainCanggan] || '未知';

    // 长生十二宫状态
    const changshengMap = this.changshengMap[dayGan];
    const changshengState = changshengMap ? changshengMap[dayZhi] : '未知';

    return {
      day_gan: dayGan,
      day_zhi: dayZhi,
      main_canggan: mainCanggan,
      main_tengod: mainTenGod,
      changsheng_state: changshengState,
      analysis: this.analyzeSelfSitting(mainTenGod, changshengState),
      all_canggan: dayCanggan.map(gan => ({
        gan: gan,
        tengod: tenGodsMap[gan] || '未知'
      }))
    };
  }

  /**
   * 分析自坐情况
   */
  analyzeSelfSitting(mainTenGod, changshengState) {
    let description = '';
    let strength = '中等';

    // 根据十神分析
    switch (mainTenGod) {
      case '比肩':
      case '劫财':
        description = '自坐比劫，个性强烈，独立自主';
        strength = '较强';
        break;
      case '正官':
      case '偏官':
        description = '自坐官杀，有责任心，但压力较大';
        strength = '中等';
        break;
      case '正财':
      case '偏财':
        description = '自坐财星，善于理财，物质条件较好';
        strength = '较好';
        break;
      case '食神':
      case '伤官':
        description = '自坐食伤，聪明灵活，有才华';
        strength = '较好';
        break;
      case '正印':
      case '偏印':
        description = '自坐印星，学习能力强，有贵人相助';
        strength = '较好';
        break;
      default:
        description = '自坐情况一般';
    }

    // 结合长生十二宫
    if (['长生', '临官', '帝旺'].includes(changshengState)) {
      description += '，且处于旺相状态';
      strength = '很好';
    } else if (['死', '墓', '绝'].includes(changshengState)) {
      description += '，但处于衰弱状态';
      if (strength === '很好') strength = '中等';
      else if (strength === '较好') strength = '一般';
    }

    return {
      description: description,
      strength: strength,
      changsheng_effect: changshengState
    };
  }

  /**
   * 计算空亡
   */
  calculateKongwang(fourPillars) {
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;

    // 六甲旬空亡表
    const kongwangTable = {
      '甲子': ['戌', '亥'], '甲戌': ['申', '酉'], '甲申': ['午', '未'],
      '甲午': ['辰', '巳'], '甲辰': ['寅', '卯'], '甲寅': ['子', '丑']
    };

    const dayGanzhi = dayGan + dayZhi;

    // 找到对应的旬
    let xunName = '';
    let emptyBranches = [];

    for (const [xun, empty] of Object.entries(kongwangTable)) {
      const xunGan = xun[0];
      if (dayGan === xunGan) {
        // 简化判断，实际需要更精确的旬计算
        xunName = xun;
        emptyBranches = empty;
        break;
      }
    }

    // 检查哪些柱位受空亡影响
    const affectedPillars = [];
    fourPillars.forEach((pillar, index) => {
      if (emptyBranches.includes(pillar.zhi)) {
        affectedPillars.push(['年柱', '月柱', '日柱', '时柱'][index]);
      }
    });

    return {
      xun_name: xunName,
      empty_branches: emptyBranches,
      affected_pillars: affectedPillars,
      effect: affectedPillars.length > 0 ? '有空亡影响，主虚空、变化' : '无空亡影响'
    };
  }

  /**
   * 计算命卦
   */
  calculateMingGua(year, fourPillars, gender = '男') {
    try {
      const yearNum = parseInt(year);
      const yearLastTwo = yearNum % 100;

      console.log('🏠 命卦计算详细调试:', {
        输入年份: year,
        年份类型: typeof year,
        解析年份: yearNum,
        年份后两位: yearLastTwo,
        输入性别: gender,
        性别类型: typeof gender,
        调用栈: new Error().stack.split('\n')[1]
      });

      let guaNumber;
      if (gender === '男') {
        // 🔧 修正：男性公式 (99 - 年份后两位) % 9
        guaNumber = (99 - yearLastTwo) % 9;
        if (guaNumber === 0) guaNumber = 9;
        console.log('🚹 男性命卦计算:', { 公式: `(99 - ${yearLastTwo}) % 9`, 结果: guaNumber });
      } else {
        // 🔧 修正：女性公式 (年份后两位 + 4) % 9
        guaNumber = (yearLastTwo + 4) % 9;
        if (guaNumber === 0) guaNumber = 9;
        console.log('🚺 女性命卦计算:', { 公式: `(${yearLastTwo} + 4) % 9`, 结果: guaNumber });
      }

      const guaNames = {
        1: '坎卦', 2: '坤卦', 3: '震卦', 4: '巽卦',
        6: '乾卦', 7: '兑卦', 8: '艮卦', 9: '离卦'
      };

      // 5数归坤（女）或艮（男）
      if (guaNumber === 5) {
        guaNumber = gender === '男' ? 2 : 8;
      }

      const guaName = guaNames[guaNumber] || '未知';

      return {
        gua_number: guaNumber,
        gua_name: guaName,
        category: this.getGuaCategory(guaNumber),
        element: this.getGuaElement(guaNumber),
        lucky_directions: this.getGuaDirections(guaNumber),
        description: `${gender}命${guaName}，先天能量场特征`
      };
    } catch (error) {
      return {
        error: '命卦计算失败',
        gua_number: 0,
        gua_name: '未知'
      };
    }
  }

  /**
   * 获取卦类
   */
  getGuaCategory(guaNumber) {
    const eastGroup = [1, 3, 4, 9]; // 东四命
    const westGroup = [2, 6, 7, 8]; // 西四命

    if (eastGroup.includes(guaNumber)) return '东四命';
    if (westGroup.includes(guaNumber)) return '西四命';
    return '未知';
  }

  /**
   * 获取卦五行
   */
  getGuaElement(guaNumber) {
    const elements = {
      1: '水', 2: '土', 3: '木', 4: '木',
      6: '金', 7: '金', 8: '土', 9: '火'
    };
    return elements[guaNumber] || '未知';
  }

  /**
   * 获取吉方
   */
  getGuaDirections(guaNumber) {
    const directions = {
      1: '东、东南、南、北',
      2: '西南、西北、西、东北',
      3: '南、北、东南、东',
      4: '北、南、东、东南',
      6: '西、东北、西南、西北',
      7: '东北、西、西北、西南',
      8: '西北、西南、东北、西',
      9: '东、东南、北、南'
    };
    return directions[guaNumber] || '未知';
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('✅ 缓存已清除');
  }

  /**
   * 获取版本信息
   */
  getVersion() {
    return {
      version: this.version,
      cacheSize: this.cache.size,
      features: [
        '完整四柱计算',
        '纳音五行分析',
        '十神分析',
        '藏干分析',
        '长生十二宫',
        '五行分析',
        '神煞分析',
        '格局分析',
        '自坐分析',
        '空亡分析',
        '命卦分析'
      ]
    };
  }

  /**
   * 天乙贵人计算（《千里命稿》权威版本）
   */
  calculateQianliTianyiGuiren(dayGan, fourPillars) {
    // 权威天乙贵人口诀：甲戊庚牛羊，乙己鼠猴乡，丙丁猪鸡位，壬癸兔蛇藏，六辛逢虎马
    const authoritativeGuirenMap = {
      '甲': ['丑', '未'], '戊': ['丑', '未'], '庚': ['丑', '未'], // 甲戊庚牛羊
      '乙': ['子', '申'], '己': ['子', '申'], // 乙己鼠猴乡
      '丙': ['亥', '酉'], '丁': ['亥', '酉'], // 丙丁猪鸡位
      '壬': ['卯', '巳'], '癸': ['卯', '巳'], // 壬癸兔蛇藏
      '辛': ['寅', '午'] // 六辛逢虎马
    };

    const targetZhi = authoritativeGuirenMap[dayGan] || [];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    fourPillars.forEach((pillar, index) => {
      if (targetZhi.includes(pillar.zhi)) {
        result.push({
          name: '天乙贵人',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          effect: '逢凶化吉，贵人相助',
          strength: '强',
          source: '《千里命稿》'
        });
      }
    });

    return result;
  }

  /**
   * 文昌贵人计算（《千里命稿》权威版本）
   */
  calculateQianliWenchangGuiren(dayGan, fourPillars) {
    // 权威文昌贵人口诀：甲巳乙午报君知，丙戊申宫丁己鸡；庚猪辛鼠壬逢虎，癸人见兔入云梯
    const wenchangMap = {
      '甲': '巳', '乙': '午', '丙': '申', '丁': '酉', '戊': '申',
      '己': '酉', '庚': '亥', '辛': '子', '壬': '寅', '癸': '卯'
    };

    const targetZhi = wenchangMap[dayGan];
    const result = [];
    const pillarNames = ['年柱', '月柱', '日柱', '时柱'];

    if (targetZhi) {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === targetZhi) {
          result.push({
            name: '文昌贵人',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            effect: '聪明好学，文思敏捷',
            strength: '强',
            source: '《千里命稿》'
          });
        }
      });
    }

    return result;
  }

  /**
   * 从权威万年历数据获取日柱
   */
  getDayPillarFromAuthoritativeData(year, month, day) {
    try {
      // 🔧 加载权威万年历数据（微信小程序环境）
      const authoritativeCalendar = require('./authoritative_calendar_data.js');

      console.log('🔍 权威万年历查询日柱:', { year, month, day });

      // 检查年份范围
      if (year < 1900 || year > 2025) {
        console.warn('⚠️ 年份超出权威数据范围:', year);
        return null;
      }

      // 使用新的查询接口
      const dayData = authoritativeCalendar.query(year, month, day);
      if (!dayData) {
        console.warn(`⚠️ 未找到日期数据: ${year}-${month}-${day}`);
        return null;
      }

      // 使用新的数据结构
      const dayGanzhi = dayData.ganzhi;
      const [gan, zhi] = [dayGanzhi[0], dayGanzhi[1]];

      console.log('✅ 权威万年历日柱查询成功:', {
        gan,
        zhi,
        ganzhi: dayGanzhi,
        source: '权威万年历数据'
      });

      return { gan, zhi };

      console.warn('⚠️ 权威万年历中未找到匹配的日柱');
      return null;

    } catch (error) {
      console.warn('⚠️ 权威万年历日柱查询失败:', error);
      return null;
    }
  }
}

// 导出模块
module.exports = CompleteBaziCalculator;
