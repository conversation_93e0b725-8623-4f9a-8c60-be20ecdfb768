#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副星系统分析
实现各柱位的十神关系分析，对标"问真八字"的副星功能
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class PillarStarInfo:
    """柱位星曜信息"""
    pillar_name: str            # 柱位名称
    pillar_position: int        # 柱位序号（0-3）
    main_star: str             # 主星（天干十神）
    hidden_stars: List[str]    # 副星（藏干十神）
    star_combination: str      # 星曜组合
    pillar_strength: str       # 柱位强度
    pillar_influence: str      # 柱位影响
    life_stage_meaning: str    # 人生阶段含义
    relationship_impact: str   # 人际关系影响
    career_guidance: str       # 事业指导

@dataclass
class StarRelationship:
    """星曜关系"""
    star1: str                 # 星曜1
    star2: str                 # 星曜2
    relationship_type: str     # 关系类型
    interaction_strength: str  # 互动强度
    positive_effects: List[str] # 正面影响
    negative_effects: List[str] # 负面影响
    harmony_score: float       # 和谐度评分

@dataclass
class ComprehensiveStarAnalysisResult:
    """完整副星分析结果"""
    pillar_stars: List[PillarStarInfo]      # 各柱星曜
    star_relationships: List[StarRelationship] # 星曜关系
    overall_star_pattern: str               # 整体星曜格局
    life_cycle_analysis: Dict               # 人生周期分析
    interpersonal_analysis: Dict            # 人际关系分析
    development_timeline: List[Dict]        # 发展时间线
    comprehensive_guidance: List[str]       # 综合指导

class ComprehensiveStarAnalyzer:
    """完整的副星分析系统"""
    
    def __init__(self):
        """初始化副星分析器"""
        # 柱位人生阶段对应
        self.pillar_life_stages = {
            0: {"name": "年柱", "stage": "童年期(1-16岁)", "meaning": "祖辈影响、早年环境、性格基础"},
            1: {"name": "月柱", "stage": "青年期(17-32岁)", "meaning": "父母影响、求学工作、事业起步"},
            2: {"name": "日柱", "stage": "中年期(33-48岁)", "meaning": "自身核心、婚姻家庭、事业巅峰"},
            3: {"name": "时柱", "stage": "晚年期(49-64岁)", "meaning": "子女关系、晚年生活、人生总结"}
        }
        
        # 十神星曜特性
        self.star_characteristics = {
            "正官": {"性质": "权威", "影响": "正面", "强度": "稳定", "人际": "权威关系"},
            "偏官": {"性质": "权力", "影响": "挑战", "强度": "波动", "人际": "竞争关系"},
            "正财": {"性质": "财富", "影响": "正面", "强度": "稳定", "人际": "合作关系"},
            "偏财": {"性质": "机遇", "影响": "变化", "强度": "灵活", "人际": "广泛关系"},
            "食神": {"性质": "才华", "影响": "正面", "强度": "温和", "人际": "和谐关系"},
            "伤官": {"性质": "才智", "影响": "复杂", "强度": "激烈", "人际": "复杂关系"},
            "比肩": {"性质": "自我", "影响": "中性", "强度": "稳定", "人际": "平等关系"},
            "劫财": {"性质": "合作", "影响": "变化", "强度": "波动", "人际": "友谊关系"},
            "正印": {"性质": "学识", "影响": "正面", "强度": "稳定", "人际": "师长关系"},
            "偏印": {"性质": "专业", "影响": "复杂", "强度": "深沉", "人际": "特殊关系"}
        }
        
        # 星曜关系矩阵
        self.star_relationship_matrix = self._init_star_relationship_matrix()
    
    def _init_star_relationship_matrix(self) -> Dict:
        """初始化星曜关系矩阵"""
        # 简化的星曜关系评估
        return {
            ("正官", "正财"): {"type": "相生", "strength": "强", "harmony": 0.9},
            ("正官", "正印"): {"type": "相生", "strength": "强", "harmony": 0.8},
            ("正财", "食神"): {"type": "相生", "strength": "中", "harmony": 0.7},
            ("食神", "偏财"): {"type": "相生", "strength": "中", "harmony": 0.6},
            ("正印", "比肩"): {"type": "相生", "strength": "中", "harmony": 0.7},
            ("偏官", "偏印"): {"type": "相生", "strength": "中", "harmony": 0.6},
            ("伤官", "偏财"): {"type": "相生", "strength": "中", "harmony": 0.5},
            ("劫财", "伤官"): {"type": "相生", "strength": "弱", "harmony": 0.4},
            
            ("正官", "伤官"): {"type": "相克", "strength": "强", "harmony": 0.2},
            ("正印", "伤官"): {"type": "相克", "strength": "中", "harmony": 0.3},
            ("食神", "偏印"): {"type": "相克", "strength": "中", "harmony": 0.3},
            ("正财", "劫财"): {"type": "相克", "strength": "强", "harmony": 0.2},
            ("偏财", "劫财"): {"type": "相克", "strength": "中", "harmony": 0.3},
            
            ("比肩", "比肩"): {"type": "同类", "strength": "中", "harmony": 0.6},
            ("劫财", "劫财"): {"type": "同类", "strength": "中", "harmony": 0.5},
            ("正官", "偏官"): {"type": "同类", "strength": "弱", "harmony": 0.4},
            ("正财", "偏财"): {"type": "同类", "strength": "弱", "harmony": 0.5},
            ("正印", "偏印"): {"type": "同类", "strength": "弱", "harmony": 0.4},
            ("食神", "伤官"): {"type": "同类", "strength": "弱", "harmony": 0.4}
        }
    
    def analyze_pillar_stars(self, four_pillars: List[Tuple[str, str]], 
                           day_gan: str, main_stars: List[str], 
                           hidden_stars_by_pillar: List[List[str]]) -> ComprehensiveStarAnalysisResult:
        """分析各柱位星曜"""
        pillar_stars = []
        
        # 分析各柱位星曜
        for i in range(4):
            pillar_star_info = self._analyze_single_pillar_star(
                i, four_pillars[i], main_stars[i] if i < len(main_stars) else "比肩",
                hidden_stars_by_pillar[i] if i < len(hidden_stars_by_pillar) else []
            )
            pillar_stars.append(pillar_star_info)
        
        # 分析星曜关系
        star_relationships = self._analyze_star_relationships(pillar_stars)
        
        # 整体星曜格局
        overall_pattern = self._analyze_overall_star_pattern(pillar_stars, star_relationships)
        
        # 人生周期分析
        life_cycle_analysis = self._analyze_life_cycle(pillar_stars)
        
        # 人际关系分析
        interpersonal_analysis = self._analyze_interpersonal_relationships(pillar_stars)
        
        # 发展时间线
        development_timeline = self._create_development_timeline(pillar_stars)
        
        # 综合指导
        comprehensive_guidance = self._generate_comprehensive_guidance(
            pillar_stars, star_relationships, overall_pattern
        )
        
        return ComprehensiveStarAnalysisResult(
            pillar_stars=pillar_stars,
            star_relationships=star_relationships,
            overall_star_pattern=overall_pattern,
            life_cycle_analysis=life_cycle_analysis,
            interpersonal_analysis=interpersonal_analysis,
            development_timeline=development_timeline,
            comprehensive_guidance=comprehensive_guidance
        )
    
    def _analyze_single_pillar_star(self, pillar_index: int, pillar: Tuple[str, str],
                                  main_star: str, hidden_stars: List[str]) -> PillarStarInfo:
        """分析单个柱位星曜"""
        pillar_info = self.pillar_life_stages[pillar_index]
        gan, zhi = pillar
        
        # 星曜组合
        star_combination = f"{main_star}"
        if hidden_stars:
            star_combination += f" + {'+'.join(hidden_stars[:2])}"  # 显示前2个副星
        
        # 柱位强度评估
        pillar_strength = self._evaluate_pillar_strength(main_star, hidden_stars, pillar_index)
        
        # 柱位影响
        pillar_influence = self._analyze_pillar_influence(main_star, pillar_index)
        
        # 人际关系影响
        relationship_impact = self._analyze_relationship_impact(main_star, pillar_index)
        
        # 事业指导
        career_guidance = self._generate_career_guidance(main_star, hidden_stars, pillar_index)
        
        return PillarStarInfo(
            pillar_name=pillar_info["name"],
            pillar_position=pillar_index,
            main_star=main_star,
            hidden_stars=hidden_stars,
            star_combination=star_combination,
            pillar_strength=pillar_strength,
            pillar_influence=pillar_influence,
            life_stage_meaning=pillar_info["meaning"],
            relationship_impact=relationship_impact,
            career_guidance=career_guidance
        )
    
    def _evaluate_pillar_strength(self, main_star: str, hidden_stars: List[str], 
                                pillar_index: int) -> str:
        """评估柱位强度"""
        # 基础强度
        base_strength = 1.0
        
        # 主星强度
        star_char = self.star_characteristics.get(main_star, {})
        if star_char.get("强度") == "稳定":
            base_strength += 0.3
        elif star_char.get("强度") == "波动":
            base_strength += 0.1
        
        # 副星加成
        base_strength += len(hidden_stars) * 0.1
        
        # 柱位权重
        position_weights = [0.8, 1.0, 1.2, 0.6]  # 年月日时
        final_strength = base_strength * position_weights[pillar_index]
        
        if final_strength >= 1.5:
            return "强"
        elif final_strength >= 1.0:
            return "中"
        else:
            return "弱"
    
    def _analyze_pillar_influence(self, main_star: str, pillar_index: int) -> str:
        """分析柱位影响"""
        star_char = self.star_characteristics.get(main_star, {})
        influence_type = star_char.get("影响", "中性")
        
        stage_influences = {
            0: f"童年期{influence_type}影响，塑造性格基础",
            1: f"青年期{influence_type}影响，影响事业发展",
            2: f"中年期{influence_type}影响，决定人生成就",
            3: f"晚年期{influence_type}影响，影响晚年生活"
        }
        
        return stage_influences.get(pillar_index, "平稳影响")
    
    def _analyze_relationship_impact(self, main_star: str, pillar_index: int) -> str:
        """分析人际关系影响"""
        star_char = self.star_characteristics.get(main_star, {})
        relationship_type = star_char.get("人际", "一般关系")
        
        relationship_impacts = {
            0: f"与长辈关系：{relationship_type}",
            1: f"与同辈关系：{relationship_type}",
            2: f"与配偶关系：{relationship_type}",
            3: f"与晚辈关系：{relationship_type}"
        }
        
        return relationship_impacts.get(pillar_index, "人际关系一般")
    
    def _generate_career_guidance(self, main_star: str, hidden_stars: List[str], 
                                pillar_index: int) -> str:
        """生成事业指导"""
        career_map = {
            "正官": "适合管理、公职、法律等权威性工作",
            "偏官": "适合竞争性强的行业，如销售、体育等",
            "正财": "适合金融、会计、商业等财务相关工作",
            "偏财": "适合投资、贸易、创新等灵活性工作",
            "食神": "适合文艺、教育、娱乐等创意性工作",
            "伤官": "适合技术、艺术、媒体等创新性工作",
            "比肩": "适合独立创业或个人能力导向的工作",
            "劫财": "适合团队合作或服务性质的工作",
            "正印": "适合教育、文化、宗教等知识性工作",
            "偏印": "适合研究、技术、玄学等专业性工作"
        }
        
        stage_guidance = {
            0: "早年培养方向",
            1: "青年发展重点",
            2: "中年事业核心",
            3: "晚年发展方向"
        }
        
        main_guidance = career_map.get(main_star, "稳步发展")
        stage_prefix = stage_guidance.get(pillar_index, "")
        
        return f"{stage_prefix}：{main_guidance}"
    
    def _analyze_star_relationships(self, pillar_stars: List[PillarStarInfo]) -> List[StarRelationship]:
        """分析星曜关系"""
        relationships = []
        
        # 分析相邻柱位的星曜关系
        for i in range(len(pillar_stars) - 1):
            star1 = pillar_stars[i].main_star
            star2 = pillar_stars[i + 1].main_star
            
            relationship = self._get_star_relationship(star1, star2)
            if relationship:
                relationships.append(relationship)
        
        # 分析特殊组合（年时、月日等）
        if len(pillar_stars) >= 4:
            # 年时关系
            year_star = pillar_stars[0].main_star
            hour_star = pillar_stars[3].main_star
            year_hour_rel = self._get_star_relationship(year_star, hour_star)
            if year_hour_rel:
                year_hour_rel.relationship_type += "(年时呼应)"
                relationships.append(year_hour_rel)
        
        return relationships
    
    def _get_star_relationship(self, star1: str, star2: str) -> Optional[StarRelationship]:
        """获取两个星曜的关系"""
        # 查找关系矩阵
        rel_key = (star1, star2)
        rel_key_reverse = (star2, star1)
        
        rel_data = self.star_relationship_matrix.get(rel_key) or \
                  self.star_relationship_matrix.get(rel_key_reverse)
        
        if not rel_data:
            return None
        
        # 生成正负面影响
        positive_effects = self._generate_positive_effects(star1, star2, rel_data["type"])
        negative_effects = self._generate_negative_effects(star1, star2, rel_data["type"])
        
        return StarRelationship(
            star1=star1,
            star2=star2,
            relationship_type=rel_data["type"],
            interaction_strength=rel_data["strength"],
            positive_effects=positive_effects,
            negative_effects=negative_effects,
            harmony_score=rel_data["harmony"]
        )
    
    def _generate_positive_effects(self, star1: str, star2: str, rel_type: str) -> List[str]:
        """生成正面影响"""
        if rel_type == "相生":
            return [f"{star1}生{star2}，相互促进", "发展顺利，配合默契"]
        elif rel_type == "同类":
            return [f"{star1}与{star2}同气连枝", "力量集中，特色鲜明"]
        else:
            return ["在冲突中成长", "激发潜能"]
    
    def _generate_negative_effects(self, star1: str, star2: str, rel_type: str) -> List[str]:
        """生成负面影响"""
        if rel_type == "相克":
            return [f"{star1}克{star2}，容易冲突", "需要调和平衡"]
        elif rel_type == "同类":
            return ["过于单一，缺乏变化", "需要其他元素调节"]
        else:
            return ["需要适当调节", "保持平衡发展"]
    
    def _analyze_overall_star_pattern(self, pillar_stars: List[PillarStarInfo],
                                    star_relationships: List[StarRelationship]) -> str:
        """分析整体星曜格局"""
        # 统计星曜类型
        star_types = [star.main_star for star in pillar_stars]
        
        # 分析主导星曜
        from collections import Counter
        star_counter = Counter(star_types)
        
        if len(star_counter) == 1:
            dominant_star = list(star_counter.keys())[0]
            return f"{dominant_star}专旺格局 - 特色鲜明，专业发展"
        elif len(star_counter) == 4:
            return "四星均衡格局 - 全面发展，适应性强"
        else:
            most_common = star_counter.most_common(1)[0]
            return f"{most_common[0]}主导格局 - 以{most_common[0]}为核心的发展模式"
    
    def _analyze_life_cycle(self, pillar_stars: List[PillarStarInfo]) -> Dict:
        """分析人生周期"""
        return {
            "童年特征": f"{pillar_stars[0].main_star}主导，{pillar_stars[0].pillar_influence}",
            "青年特征": f"{pillar_stars[1].main_star}主导，{pillar_stars[1].pillar_influence}",
            "中年特征": f"{pillar_stars[2].main_star}主导，{pillar_stars[2].pillar_influence}",
            "晚年特征": f"{pillar_stars[3].main_star}主导，{pillar_stars[3].pillar_influence}",
            "发展轨迹": "从童年到晚年的星曜变化体现了人生发展轨迹"
        }
    
    def _analyze_interpersonal_relationships(self, pillar_stars: List[PillarStarInfo]) -> Dict:
        """分析人际关系"""
        return {
            "长辈关系": pillar_stars[0].relationship_impact,
            "同辈关系": pillar_stars[1].relationship_impact,
            "配偶关系": pillar_stars[2].relationship_impact,
            "晚辈关系": pillar_stars[3].relationship_impact,
            "社交特点": "基于各柱星曜的人际关系特征分析"
        }
    
    def _create_development_timeline(self, pillar_stars: List[PillarStarInfo]) -> List[Dict]:
        """创建发展时间线"""
        timeline = []
        
        for i, star_info in enumerate(pillar_stars):
            stage_info = self.pillar_life_stages[i]
            timeline.append({
                "阶段": stage_info["stage"],
                "主导星曜": star_info.main_star,
                "发展重点": star_info.career_guidance,
                "注意事项": star_info.pillar_influence
            })
        
        return timeline
    
    def _generate_comprehensive_guidance(self, pillar_stars: List[PillarStarInfo],
                                       star_relationships: List[StarRelationship],
                                       overall_pattern: str) -> List[str]:
        """生成综合指导"""
        guidance = []
        
        # 基于整体格局的建议
        if "专旺" in overall_pattern:
            guidance.append("发挥专业优势，深度发展特长领域")
        elif "均衡" in overall_pattern:
            guidance.append("保持全面发展，注重各方面平衡")
        else:
            guidance.append("以主导星曜为核心，协调其他方面发展")
        
        # 基于星曜关系的建议
        harmony_scores = [rel.harmony_score for rel in star_relationships]
        if harmony_scores:
            avg_harmony = sum(harmony_scores) / len(harmony_scores)
            if avg_harmony >= 0.7:
                guidance.append("星曜关系和谐，发展顺利")
            elif avg_harmony >= 0.5:
                guidance.append("星曜关系一般，需要适当调节")
            else:
                guidance.append("星曜关系紧张，需要重点化解冲突")
        
        # 基于人生阶段的建议
        current_stage_star = pillar_stars[2].main_star  # 以日柱为当前核心
        guidance.append(f"当前以{current_stage_star}为核心，{pillar_stars[2].career_guidance}")
        
        return guidance[:5]  # 返回前5个建议


# 测试和使用示例
def main():
    """测试副星系统分析"""
    print("⭐ 副星系统分析测试")
    print("=" * 50)
    
    # 创建副星分析器
    analyzer = ComprehensiveStarAnalyzer()
    
    # 测试数据
    test_four_pillars = [
        ("乙", "巳"),  # 年柱
        ("癸", "未"),  # 月柱
        ("丁", "丑"),  # 日柱
        ("丁", "未")   # 时柱
    ]
    day_gan = "丁"
    
    # 主星（天干十神）
    main_stars = ["正印", "偏官", "比肩", "比肩"]
    
    # 副星（藏干十神）
    hidden_stars_by_pillar = [
        ["劫财", "伤官", "偏财"],  # 年柱藏干十神
        ["食神", "比肩", "正印"],  # 月柱藏干十神
        ["食神", "偏官", "正财"],  # 日柱藏干十神
        ["食神", "比肩", "正印"]   # 时柱藏干十神
    ]
    
    # 进行副星分析
    result = analyzer.analyze_pillar_stars(
        test_four_pillars, day_gan, main_stars, hidden_stars_by_pillar
    )
    
    # 显示各柱星曜
    print("🏛️ 各柱星曜分析:")
    for star_info in result.pillar_stars:
        print(f"\n   {star_info.pillar_name} ({star_info.life_stage_meaning.split('(')[0]}):")
        print(f"      主星: {star_info.main_star}")
        print(f"      副星: {', '.join(star_info.hidden_stars[:3])}")
        print(f"      组合: {star_info.star_combination}")
        print(f"      强度: {star_info.pillar_strength}")
        print(f"      影响: {star_info.pillar_influence}")
        print(f"      人际: {star_info.relationship_impact}")
        print(f"      事业: {star_info.career_guidance}")
    
    # 显示星曜关系
    print("\n🔗 星曜关系分析:")
    for rel in result.star_relationships:
        print(f"   {rel.star1} ↔ {rel.star2}:")
        print(f"      关系: {rel.relationship_type} (强度: {rel.interaction_strength})")
        print(f"      和谐度: {rel.harmony_score:.1f}")
        print(f"      正面: {', '.join(rel.positive_effects)}")
        if rel.negative_effects:
            print(f"      注意: {', '.join(rel.negative_effects)}")
        print()
    
    # 显示整体格局
    print(f"🎭 整体星曜格局: {result.overall_star_pattern}")
    
    # 显示人生周期分析
    print("\n📅 人生周期分析:")
    for stage, description in result.life_cycle_analysis.items():
        print(f"   {stage}: {description}")
    
    # 显示人际关系分析
    print("\n👥 人际关系分析:")
    for relationship, description in result.interpersonal_analysis.items():
        print(f"   {relationship}: {description}")
    
    # 显示发展时间线
    print("\n⏰ 发展时间线:")
    for timeline_item in result.development_timeline:
        print(f"   {timeline_item['阶段']}: {timeline_item['主导星曜']}")
        print(f"      重点: {timeline_item['发展重点']}")
        print()
    
    # 显示综合指导
    print("💡 综合指导:")
    for i, guidance in enumerate(result.comprehensive_guidance, 1):
        print(f"   {i}. {guidance}")
    
    print("\n✅ 副星系统分析测试完成！")


if __name__ == "__main__":
    main()
