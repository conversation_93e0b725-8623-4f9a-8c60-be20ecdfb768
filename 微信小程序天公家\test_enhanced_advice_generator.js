// test_enhanced_advice_generator.js
// 测试增强版专业建议生成器

const EnhancedAdviceGenerator = require('./utils/enhanced_advice_generator.js');

/**
 * 测试专业建议生成器
 */
function testAdviceGenerator() {
  console.log('🧪 开始测试专业建议生成器');
  console.log('=' * 50);

  const generator = new EnhancedAdviceGenerator();

  // 测试用例1：完整建议生成
  console.log('\n📋 测试用例1：完整建议生成');
  const testCase1 = {
    bazi: {
      fourPillars: [
        { gan: '甲', zhi: '寅' },
        { gan: '丁', zhi: '巳' },
        { gan: '甲', zhi: '子' },
        { gan: '己', zhi: '未' }
      ],
      element_powers: {
        percentages: {
          '木': 35, '火': 30, '土': 20, '金': 10, '水': 5
        }
      }
    },
    pattern: {
      pattern_type: '正格',
      clarity_score: 0.75,
      description: '木火通明格'
    },
    yongshen: {
      yongshen: '火',
      xishen: ['木'],
      jishen: ['金', '水']
    },
    dynamicAnalysis: {
      dayun_analysis: {
        current_dayun: {
          gan: '丙', zhi: '午',
          energy_curve: { phase: '巅峰', energy_level: 1.0 }
        }
      },
      liunian_analysis: {
        yearly_analysis: [
          { year: 2024, fortune_trend: { score: 0.8, level: '大吉' } },
          { year: 2025, fortune_trend: { score: 0.6, level: '中吉' } },
          { year: 2026, fortune_trend: { score: 0.4, level: '平常' } }
        ]
      },
      turning_points: [
        { type: '用神激活', timing: 2024, advice: '把握机遇，主动出击' }
      ]
    },
    personalInfo: {
      age: 35,
      gender: '男',
      birth_year: 1989
    }
  };

  const result1 = generator.generateComprehensiveAdvice(
    testCase1.bazi,
    testCase1.pattern,
    testCase1.yongshen,
    testCase1.dynamicAnalysis,
    testCase1.personalInfo
  );

  console.log('🎯 完整建议生成结果:');
  console.log('  事业建议:', result1.career ? '✅ 完成' : '❌ 失败');
  console.log('  财运建议:', result1.wealth ? '✅ 完成' : '❌ 失败');
  console.log('  健康建议:', result1.health ? '✅ 完成' : '❌ 失败');
  console.log('  人际关系建议:', result1.relationship ? '✅ 完成' : '❌ 失败');
  console.log('  时间规划建议:', result1.timing ? '✅ 完成' : '❌ 失败');
  console.log('  个性化建议:', result1.personalized ? '✅ 完成' : '❌ 失败');
  console.log('  优先级建议:', result1.prioritized ? `✅ ${result1.prioritized.length}条` : '❌ 失败');
  console.log('  置信度:', (result1.confidence * 100).toFixed(1) + '%');

  // 测试用例2：事业建议生成
  console.log('\n📋 测试用例2：事业建议生成');
  testCareerAdvice(generator);

  // 测试用例3：财运建议生成
  console.log('\n📋 测试用例3：财运建议生成');
  testWealthAdvice(generator);

  // 测试用例4：健康建议生成
  console.log('\n📋 测试用例4：健康建议生成');
  testHealthAdvice(generator);

  // 测试用例5：建议优先级排序
  console.log('\n📋 测试用例5：建议优先级排序');
  testAdvicePrioritization(generator);

  console.log('\n✅ 专业建议生成器测试完成');
}

/**
 * 测试事业建议生成
 */
function testCareerAdvice(generator) {
  const testBazi = {
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ],
    element_powers: {
      percentages: { '木': 35, '火': 30, '土': 20, '金': 10, '水': 5 }
    }
  };

  const testPattern = {
    pattern_type: '正格',
    clarity_score: 0.75
  };

  const testYongshen = {
    yongshen: '火',
    xishen: ['木']
  };

  const testPersonalInfo = {
    age: 35,
    gender: '男'
  };

  const result = generator.generateCareerAdvice(testBazi, testPattern, testYongshen, testPersonalInfo);

  console.log('💼 事业建议测试:');
  console.log(`  适合职业: ${result.suitable_careers ? result.suitable_careers.join('、') : '未生成'}`);
  console.log(`  发展策略: ${result.development_strategy || '未生成'}`);
  console.log(`  领导风格: ${result.leadership_style || '未生成'}`);
  console.log(`  时机指导: ${result.timing_guidance || '未生成'}`);
  console.log(`  推荐行业: ${result.industry_recommendations ? result.industry_recommendations.join('、') : '未生成'}`);
  console.log(`  技能发展: ${result.skill_development ? result.skill_development.join('、') : '未生成'}`);
  
  if (result.career_planning) {
    console.log(`  发展方向: ${result.career_planning.direction || '未指定'}`);
    console.log(`  年龄建议: ${result.career_planning.age_specific || '未指定'}`);
  }

  console.log(`  ✅ 事业建议: ${result.suitable_careers ? '成功' : '失败'}`);
}

/**
 * 测试财运建议生成
 */
function testWealthAdvice(generator) {
  const testBazi = {
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ],
    element_powers: {
      percentages: { '木': 35, '火': 30, '土': 20, '金': 10, '水': 5 }
    }
  };

  const testYongshen = {
    yongshen: '火',
    jishen: ['金']
  };

  const testDynamicAnalysis = {
    liunian_analysis: {
      yearly_analysis: [
        { year: 2024, fortune_trend: { score: 0.8 } },
        { year: 2025, fortune_trend: { score: 0.6 } },
        { year: 2026, fortune_trend: { score: 0.4 } }
      ]
    }
  };

  const result = generator.generateWealthAdvice(testBazi, testYongshen, testDynamicAnalysis);

  console.log('💰 财运建议测试:');
  console.log(`  投资策略: ${result.investment_strategy || '未生成'}`);
  console.log(`  适合投资: ${result.suitable_investments ? result.suitable_investments.join('、') : '未生成'}`);
  console.log(`  风险评估: ${result.risk_assessment || '未生成'}`);
  console.log(`  时机策略: ${result.timing_strategy || '未生成'}`);
  
  if (result.wealth_development) {
    console.log(`  财富潜力: ${result.wealth_development.potential || '未分析'}`);
    console.log(`  发展策略: ${result.wealth_development.strategy || '未分析'}`);
  }

  if (result.financial_planning) {
    console.log(`  财务规划: ${result.financial_planning.planning_advice || '已生成'}`);
  }

  console.log(`  ✅ 财运建议: ${result.investment_strategy ? '成功' : '失败'}`);
}

/**
 * 测试健康建议生成
 */
function testHealthAdvice(generator) {
  const testBazi = {
    fourPillars: [
      { gan: '甲', zhi: '寅' },
      { gan: '丁', zhi: '巳' },
      { gan: '甲', zhi: '子' },
      { gan: '己', zhi: '未' }
    ],
    element_powers: {
      percentages: { '木': 35, '火': 30, '土': 20, '金': 10, '水': 5 }
    }
  };

  const testPersonalInfo = {
    age: 35,
    gender: '男'
  };

  const result = generator.generateHealthAdvice(testBazi, testPersonalInfo);

  console.log('🏥 健康建议测试:');
  console.log(`  健康倾向: ${result.health_tendencies ? result.health_tendencies.length + '项' : '未分析'}`);
  
  if (result.health_tendencies && result.health_tendencies.length > 0) {
    result.health_tendencies.forEach((tendency, index) => {
      console.log(`    ${index + 1}. ${tendency.element}行: ${tendency.organs.join('、')}`);
    });
  }

  console.log(`  预防建议: ${result.prevention_advice ? result.prevention_advice.length + '条' : '未生成'}`);
  console.log(`  饮食建议: ${result.diet_recommendations ? result.diet_recommendations.length + '条' : '未生成'}`);
  console.log(`  运动建议: ${result.exercise_suggestions ? result.exercise_suggestions.length + '条' : '未生成'}`);
  
  if (result.lifestyle_guidance) {
    console.log(`  体质倾向: ${result.lifestyle_guidance.tendency || '未分析'}`);
    console.log(`  注意事项: ${result.lifestyle_guidance.attention || '未分析'}`);
  }

  console.log(`  年龄建议: ${result.age_specific || '未生成'}`);
  console.log(`  性别建议: ${result.gender_specific || '未生成'}`);

  console.log(`  ✅ 健康建议: ${result.health_tendencies ? '成功' : '失败'}`);
}

/**
 * 测试建议优先级排序
 */
function testAdvicePrioritization(generator) {
  const mockAdvices = {
    career: { suitable_careers: ['教育', '文化'], development_strategy: '稳步发展' },
    wealth: { investment_strategy: '稳健投资', risk_assessment: '低风险' },
    health: { health_tendencies: [{ element: '木', organs: ['肝', '胆'] }] },
    relationship: { leadership_style: '温和型领导', communication_style: '善于沟通' },
    timing: { current_period: { strategy: '积极进取' } },
    personalized: { personality_traits: ['稳重', '有责任心'] }
  };

  const result = generator.prioritizeAdvice(
    mockAdvices.career,
    mockAdvices.wealth,
    mockAdvices.health,
    mockAdvices.relationship,
    mockAdvices.timing,
    mockAdvices.personalized
  );

  console.log('📊 建议优先级排序测试:');
  console.log(`  优先级建议数量: ${result.length}条`);
  
  result.forEach((advice, index) => {
    console.log(`    ${index + 1}. ${advice.category} (优先级${advice.priority}, 紧急度${advice.urgency})`);
    console.log(`       内容: ${advice.content}`);
    console.log(`       行动项: ${advice.action_items.join('、')}`);
  });

  console.log(`  ✅ 优先级排序: ${result.length > 0 ? '成功' : '失败'}`);
}

/**
 * 测试十神识别
 */
function testTenGodIdentification() {
  console.log('\n🧪 测试十神识别');
  
  const generator = new EnhancedAdviceGenerator();
  
  const testCases = [
    {
      name: '甲日丁月',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '丁', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '己', zhi: '未' }
        ]
      },
      expected: '伤官'
    },
    {
      name: '甲日己月',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '己', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '丁', zhi: '未' }
        ]
      },
      expected: '正财'
    },
    {
      name: '甲日辛月',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '辛', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '癸', zhi: '未' }
        ]
      },
      expected: '正官'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.name}`);
    const result = generator.getMainTenGod(testCase.bazi);
    
    console.log(`    预期十神: ${testCase.expected}`);
    console.log(`    实际十神: ${result || '未识别'}`);
    
    const isCorrect = result === testCase.expected;
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 测试财星强弱分析
 */
function testWealthStrengthAnalysis() {
  console.log('\n🧪 测试财星强弱分析');
  
  const generator = new EnhancedAdviceGenerator();
  
  const testCases = [
    {
      name: '财星强势',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '己', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '己', zhi: '未' }
        ],
        element_powers: {
          percentages: { '木': 20, '火': 15, '土': 35, '金': 15, '水': 15 }
        }
      },
      expected: '正财强'
    },
    {
      name: '财多身弱',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '己', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '己', zhi: '未' }
        ],
        element_powers: {
          percentages: { '木': 15, '火': 10, '土': 40, '金': 20, '水': 15 }
        }
      },
      expected: '财多身弱'
    },
    {
      name: '财星偏弱',
      bazi: {
        fourPillars: [
          { gan: '甲', zhi: '寅' },
          { gan: '乙', zhi: '巳' },
          { gan: '甲', zhi: '子' },
          { gan: '丙', zhi: '未' }
        ],
        element_powers: {
          percentages: { '木': 40, '火': 25, '土': 15, '金': 10, '水': 10 }
        }
      },
      expected: '财弱'
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n  测试${index + 1}: ${testCase.name}`);
    const result = generator.analyzeWealthStrength(testCase.bazi);
    
    console.log(`    预期财星强弱: ${testCase.expected}`);
    console.log(`    实际财星强弱: ${result}`);
    
    const isCorrect = result === testCase.expected;
    console.log(`    ✅ 结果: ${isCorrect ? '正确' : '需要检查'}`);
  });
}

/**
 * 验证建议实用性
 */
function validateAdvicePracticality() {
  console.log('\n📚 验证建议实用性');
  
  const generator = new EnhancedAdviceGenerator();
  
  // 使用真实案例验证
  const realCase = {
    bazi: {
      fourPillars: [
        { gan: '甲', zhi: '寅' },
        { gan: '丁', zhi: '巳' },
        { gan: '甲', zhi: '子' },
        { gan: '己', zhi: '未' }
      ],
      element_powers: {
        percentages: { '木': 35, '火': 30, '土': 20, '金': 10, '水': 5 }
      }
    },
    pattern: { pattern_type: '正格' },
    yongshen: { yongshen: '火', jishen: ['金'] },
    dynamicAnalysis: {
      dayun_analysis: { current_dayun: { energy_curve: { phase: '巅峰' } } },
      liunian_analysis: { yearly_analysis: [{ year: 2024, fortune_trend: { score: 0.8 } }] }
    },
    personalInfo: { age: 35, gender: '男' }
  };

  const result = generator.generateComprehensiveAdvice(
    realCase.bazi,
    realCase.pattern,
    realCase.yongshen,
    realCase.dynamicAnalysis,
    realCase.personalInfo
  );

  console.log('🔍 建议实用性验证:');
  console.log(`  建议完整性: ${Object.keys(result).length}个维度`);
  console.log(`  优先级建议: ${result.prioritized ? result.prioritized.length : 0}条`);
  
  if (result.prioritized && result.prioritized.length > 0) {
    console.log('  前三优先建议:');
    result.prioritized.slice(0, 3).forEach((advice, index) => {
      console.log(`    ${index + 1}. ${advice.category}: ${advice.content}`);
      console.log(`       行动项: ${advice.action_items.slice(0, 2).join('、')}`);
    });
  }

  console.log(`  建议置信度: ${(result.confidence * 100).toFixed(1)}%`);
  console.log(`  ✅ 实用性验证: ${result.prioritized && result.prioritized.length > 0 ? '通过' : '需要改进'}`);
}

// 运行测试
if (require.main === module) {
  try {
    testAdviceGenerator();
    testTenGodIdentification();
    testWealthStrengthAnalysis();
    validateAdvicePracticality();
    
    console.log('\n🎉 所有测试完成！');
    console.log('📋 专业建议系统已按照文档要求实现：');
    console.log('  ✅ 多维度建议生成（事业、财运、健康、人际、时机、个性化）');
    console.log('  ✅ 基于命局特征的个性化建议');
    console.log('  ✅ 智能优先级排序系统');
    console.log('  ✅ 实用性行动指导');
    console.log('  ✅ 高置信度建议生成');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    console.error(error.stack);
  }
}

module.exports = {
  testAdviceGenerator,
  testCareerAdvice,
  testWealthAdvice,
  testHealthAdvice,
  testAdvicePrioritization,
  testTenGodIdentification,
  testWealthStrengthAnalysis,
  validateAdvicePracticality
};
