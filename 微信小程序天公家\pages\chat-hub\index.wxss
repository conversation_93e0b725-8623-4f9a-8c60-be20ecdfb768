/* pages/chat-hub/index.wxss */

/* 整体容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(to bottom, #1f2937, #111827);
  color: white;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

/* 顶部导航栏 */
.top-nav {
  padding: 40rpx 30rpx 10rpx;
  display: flex;
  flex-direction: column;
  z-index: 10;
  position: relative;
}

/* 新增自定义导航栏样式 */
.custom-nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 10rpx;
  position: relative;
  z-index: 10;
}

.nav-bar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  flex: 1;
  text-align: center;
}

.nav-bar-right {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  font-size: 40rpx;
  color: white;
}

/* 修改原来的设置图标样式 */
.settings-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.settings-icon:active, .icon-hover {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.15);
}

.settings-icon image {
  width: 40rpx;
  height: 40rpx;
}

/* 角色tab选项卡 */
.role-tabs {
  display: flex;
  width: 100%;
  justify-content: space-around;
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.role-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 20rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.role-tab.active {
  opacity: 1;
  transform: scale(1.05);
}

/* 文本图标样式 */
.role-icon-text {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.role-tab text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 字体图标 */
.iconfont {
  font-family: sans-serif;
  font-style: normal;
  font-size: 28rpx;
}

.send-icon, .mic-icon, .swipe-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 日期显示 */
.date-display {
  padding: 10rpx 40rpx 20rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 26rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
  display: flex;
  align-items: center;
}

.date-dot {
  margin: 0 10rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 标签指示器 */
.tab-indicator {
  height: 6rpx;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

/* 滑动区域 - 垂直方向 */
.chat-swiper {
  flex: 1;
  width: 100%;
  height: calc(100% - 260rpx);
}

.swiper-item {
  height: 100%;
  overflow: hidden;
}

.role-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 欢迎消息样式 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 60rpx 40rpx;
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.6s ease-out;
}

.student-welcome {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
}

.parent-welcome {
  background: linear-gradient(135deg, rgba(180, 83, 9, 0.8), rgba(245, 158, 11, 0.6));
}

.teacher-welcome {
  background: linear-gradient(135deg, rgba(16, 128, 68, 0.8), rgba(5, 203, 109, 0.6));
}

.doctor-welcome {
  background: linear-gradient(135deg, rgba(3, 105, 161, 0.8), rgba(14, 165, 233, 0.6));
}

.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: white;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.welcome-start-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 40rpx;
  border-radius: 50rpx;
  width: 80%;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  margin-top: 20rpx;
}

.student-welcome .welcome-start-button {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
}

.parent-welcome .welcome-start-button {
  background: linear-gradient(135deg, #b45309, #f59e0b);
}

.teacher-welcome .welcome-start-button {
  background: linear-gradient(135deg, #108044, #05cb6d);
}

.doctor-welcome .welcome-start-button {
  background: linear-gradient(135deg, #0369a1, #0ea5e9);
}

.welcome-start-button:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.welcome-start-button text {
  font-size: 32rpx;
  font-weight: 500;
}

.welcome-start-button image {
  width: 36rpx;
  height: 36rpx;
}

/* 消息区域 */
.message-area {
  flex: 1;
  padding: 20rpx 40rpx;
  overflow-y: auto;
}

.message-wrapper {
  margin-bottom: 40rpx;
  animation: fadeIn 0.5s ease-out;
  position: relative;
  clear: both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.avatar-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  float: left;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 100%;
  height: 100%;
}

.message {
  border-radius: 20rpx;
  padding: 24rpx 30rpx;
  max-width: 70%;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.ai-message {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  color: white;
  float: left;
  margin-left: 100rpx;
  border-top-left-radius: 4rpx;
}

/* 角色特定的AI消息样式 */
.student-ai-message {
  border-left: 8rpx solid #3b82f6;
}

.parent-ai-message {
  border-left: 8rpx solid #f59e0b;
}

.teacher-ai-message {
  border-left: 8rpx solid #05cb6d;
}

.doctor-ai-message {
  border-left: 8rpx solid #0ea5e9;
}

.user-message {
  color: white;
  float: right;
  border-top-right-radius: 4rpx;
}

/* 角色特定的用户消息样式 */
.student-user-message {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
}

.parent-user-message {
  background: linear-gradient(135deg, #b45309, #f59e0b);
}

.teacher-user-message {
  background: linear-gradient(135deg, #108044, #05cb6d);
}

.doctor-user-message {
  background: linear-gradient(135deg, #0369a1, #0ea5e9);
}

.message-content {
  font-size: 30rpx;
  line-height: 1.5;
}

.message-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 10rpx;
  clear: both;
}

.ai-wrapper .message-time {
  margin-left: 100rpx;
}

.user-wrapper .message-time {
  text-align: right;
}

/* 正在输入指示器 */
.typing-wrapper {
  display: flex;
  margin-bottom: 40rpx;
  animation: fadeIn 0.5s ease-out;
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  margin-left: 100rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  margin: 0 6rpx;
  animation: typingAnimation 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10rpx);
  }
}

/* 选项消息 */
.options-message {
  margin-top: 20rpx;
}

.options-list {
  display: flex;
  flex-direction: column;
}

.option-item {
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.student-option {
  background: rgba(59, 130, 246, 0.2);
}

.parent-option {
  background: rgba(245, 158, 11, 0.2);
}

.teacher-option {
  background: rgba(5, 203, 109, 0.2);
}

.doctor-option {
  background: rgba(14, 165, 233, 0.2);
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.option-content {
  font-size: 28rpx;
  color: white;
}

/* 底部输入区域 */
.input-area {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
  align-items: center;
  position: relative;
  z-index: 2;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  height: 80rpx;
  color: white;
  font-size: 30rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.send-button, .voice-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.send-button {
  margin-right: 10rpx;
}

.voice-button {
  background: rgba(255, 255, 255, 0.1);
}

.send-button image, .voice-button image {
  width: 40rpx;
  height: 40rpx;
}

.button-hover {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 滑动提示 - 修改为垂直滑动提示 */
.swipe-hint {
  position: fixed;
  left: 50%;
  bottom: 180rpx;
  transform: translateX(-50%) translateY(100rpx);
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  opacity: 0;
  transition: all 0.5s ease;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);
  z-index: 100;
}

.swipe-hint.show {
  opacity: 0.8;
  transform: translateX(-50%) translateY(0);
}

.swipe-hint image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.swipe-hint text {
  font-size: 24rpx;
  color: white;
}

/* 动画 */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
} 