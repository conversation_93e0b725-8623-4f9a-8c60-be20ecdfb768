// gender_algorithm_deep_verification.js
// 深度验证大运、小运、流年的性别区分算法

const ProfessionalDayunCalculator = require('./utils/professional_dayun_calculator.js');
const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
const ProfessionalLiunianCalculator = require('./utils/professional_liunian_calculator.js');

console.log('🔍 大运小运流年性别区分算法深度验证');
console.log('=' .repeat(60));

// 测试数据：同一个八字，不同性别
const testBazi = {
  yearPillar: { gan: '辛', zhi: '丑' },
  monthPillar: { gan: '甲', zhi: '午' },
  dayPillar: { gan: '癸', zhi: '卯' },
  hourPillar: { gan: '壬', zhi: '戌' }
};

const testBirthInfo = {
  year: 2021,
  month: 6,
  day: 24,
  hour: 19,
  minute: 30
};

const testLocation = '北京';

console.log('📊 测试八字:', {
  年柱: `${testBazi.yearPillar.gan}${testBazi.yearPillar.zhi}`,
  月柱: `${testBazi.monthPillar.gan}${testBazi.monthPillar.zhi}`,
  日柱: `${testBazi.dayPillar.gan}${testBazi.dayPillar.zhi}`,
  时柱: `${testBazi.hourPillar.gan}${testBazi.hourPillar.zhi}`,
  出生时间: `${testBirthInfo.year}年${testBirthInfo.month}月${testBirthInfo.day}日 ${testBirthInfo.hour}:${testBirthInfo.minute}`,
  出生地点: testLocation
});

// 1. 大运系统性别区分验证
console.log('\n🌟 1. 大运系统性别区分验证');
console.log('-'.repeat(40));

function testDayunGenderDifference() {
  try {
    const dayunCalculator = new ProfessionalDayunCalculator();
    
    // 测试男性
    const maleResult = dayunCalculator.calculateProfessionalDayun({
      ...testBazi,
      gender: '男'
    }, testBirthInfo, testLocation);

    // 测试女性
    const femaleResult = dayunCalculator.calculateProfessionalDayun({
      ...testBazi,
      gender: '女'
    }, testBirthInfo, testLocation);
    
    console.log('👨 男性大运结果:');
    console.log(`  - 年干: ${testBazi.yearPillar.gan} (${maleResult.calculation.direction.yearType})`);
    console.log(`  - 大运方向: ${maleResult.calculation.direction.rule}`);
    console.log(`  - 起运年龄: ${maleResult.calculation.qiyunResult.qiyunAge.years}岁${maleResult.calculation.qiyunResult.qiyunAge.months}个月`);
    console.log(`  - 大运序列前3步: ${maleResult.calculation.dayunSequence.slice(0, 3).map(d => d.ganzhi).join(' → ')}`);

    console.log('\n👩 女性大运结果:');
    console.log(`  - 年干: ${testBazi.yearPillar.gan} (${femaleResult.calculation.direction.yearType})`);
    console.log(`  - 大运方向: ${femaleResult.calculation.direction.rule}`);
    console.log(`  - 起运年龄: ${femaleResult.calculation.qiyunResult.qiyunAge.years}岁${femaleResult.calculation.qiyunResult.qiyunAge.months}个月`);
    console.log(`  - 大运序列前3步: ${femaleResult.calculation.dayunSequence.slice(0, 3).map(d => d.ganzhi).join(' → ')}`);

    // 分析差异
    console.log('\n🔍 大运性别差异分析:');
    const directionSame = maleResult.calculation.direction.isForward === femaleResult.calculation.direction.isForward;
    const qiyunAgeSame = maleResult.calculation.qiyunResult.qiyunAge.years === femaleResult.calculation.qiyunResult.qiyunAge.years;
    const sequenceSame = maleResult.calculation.dayunSequence[0].ganzhi === femaleResult.calculation.dayunSequence[0].ganzhi;

    console.log(`  - 大运方向相同: ${directionSame ? '❌ 是（错误）' : '✅ 否（正确）'}`);
    console.log(`  - 起运年龄相同: ${qiyunAgeSame ? '✅ 是（正确）' : '❌ 否（可能错误）'}`);
    console.log(`  - 大运序列相同: ${sequenceSame ? '❌ 是（错误）' : '✅ 否（正确）'}`);

    // 验证传统规则
    const yangGan = ['甲', '丙', '戊', '庚', '壬'];
    const isYangYear = yangGan.includes(testBazi.yearPillar.gan);
    const expectedMaleForward = isYangYear; // 阳男顺行
    const expectedFemaleForward = !isYangYear; // 阴女顺行

    console.log('\n📚 传统规则验证:');
    console.log(`  - 年干${testBazi.yearPillar.gan}为${isYangYear ? '阳' : '阴'}干`);
    console.log(`  - 男性应该${expectedMaleForward ? '顺' : '逆'}行: ${maleResult.calculation.direction.isForward === expectedMaleForward ? '✅ 正确' : '❌ 错误'}`);
    console.log(`  - 女性应该${expectedFemaleForward ? '顺' : '逆'}行: ${femaleResult.calculation.direction.isForward === expectedFemaleForward ? '✅ 正确' : '❌ 错误'}`);

    return {
      male: maleResult,
      female: femaleResult,
      hasGenderDifference: !directionSame || !sequenceSame,
      rulesCorrect: (maleResult.calculation.direction.isForward === expectedMaleForward) &&
                   (femaleResult.calculation.direction.isForward === expectedFemaleForward)
    };
    
  } catch (error) {
    console.error('❌ 大运性别验证失败:', error.message);
    return null;
  }
}

// 2. 小运系统性别区分验证
console.log('\n🌙 2. 小运系统性别区分验证');
console.log('-'.repeat(40));

function testMinorFortuneGenderDifference() {
  try {
    const minorCalculator = new MinorFortuneCalculator();
    
    // 测试男性小运
    const maleMinorResults = minorCalculator.calculateAllMinorFortunes({
      ...testBazi,
      gender: '男'
    });
    const maleMinorResult = maleMinorResults.find(m => m.age === 5); // 5岁小运

    // 测试女性小运
    const femaleMinorResults = minorCalculator.calculateAllMinorFortunes({
      ...testBazi,
      gender: '女'
    });
    const femaleMinorResult = femaleMinorResults.find(m => m.age === 5); // 5岁小运
    
    console.log('👨 男性5岁小运:');
    console.log(`  - 小运干支: ${maleMinorResult ? maleMinorResult.ganzhi : '未找到'}`);
    console.log(`  - 推演方向: ${maleMinorResult ? (maleMinorResult.direction ? '顺行' : '逆行') : '未知'}`);
    console.log(`  - 计算规则: ${maleMinorResult ? maleMinorResult.rule : '未知'}`);

    console.log('\n👩 女性5岁小运:');
    console.log(`  - 小运干支: ${femaleMinorResult ? femaleMinorResult.ganzhi : '未找到'}`);
    console.log(`  - 推演方向: ${femaleMinorResult ? (femaleMinorResult.direction ? '顺行' : '逆行') : '未知'}`);
    console.log(`  - 计算规则: ${femaleMinorResult ? femaleMinorResult.rule : '未知'}`);

    // 分析差异
    console.log('\n🔍 小运性别差异分析:');
    if (maleMinorResult && femaleMinorResult) {
      const minorDirectionSame = maleMinorResult.direction === femaleMinorResult.direction;
      const minorGanzhiSame = maleMinorResult.ganzhi === femaleMinorResult.ganzhi;

      console.log(`  - 推演方向相同: ${minorDirectionSame ? '❌ 是（错误）' : '✅ 否（正确）'}`);
      console.log(`  - 小运干支相同: ${minorGanzhiSame ? '❌ 是（错误）' : '✅ 否（正确）'}`);

      return {
        male: maleMinorResult,
        female: femaleMinorResult,
        hasGenderDifference: !minorDirectionSame || !minorGanzhiSame
      };
    } else {
      console.log('  - ❌ 无法比较：缺少小运数据');
      return null;
    }
    
  } catch (error) {
    console.error('❌ 小运性别验证失败:', error.message);
    return null;
  }
}

// 3. 流年系统性别区分验证
console.log('\n⭐ 3. 流年系统性别区分验证');
console.log('-'.repeat(40));

function testLiunianGenderDifference() {
  try {
    const liunianCalculator = new ProfessionalLiunianCalculator();
    
    // 流年计算通常不直接依赖性别，但可能在十神分析等方面有差异
    const testYear = 2025;
    const liunianGanzhi = liunianCalculator.calculateLiunianGanzhi(testYear);
    
    console.log(`📅 ${testYear}年流年干支: ${liunianGanzhi.ganzhi}`);
    
    // 测试十神分析（可能涉及性别）
    const maleShishen = liunianCalculator.analyzeTenGodsRelation(testBazi.dayPillar.gan, liunianGanzhi.gan);
    const femaleShishen = liunianCalculator.analyzeTenGodsRelation(testBazi.dayPillar.gan, liunianGanzhi.gan);
    
    console.log('\n🔍 流年十神分析:');
    console.log(`  - 日主: ${testBazi.dayPillar.gan}`);
    console.log(`  - 流年天干: ${liunianGanzhi.gan}`);
    console.log(`  - 十神关系: ${maleShishen.tenGod}`);
    console.log(`  - 关系说明: ${maleShishen.description}`);
    
    // 流年计算本身不分性别，但在应用中可能有差异
    console.log('\n📝 流年性别影响分析:');
    console.log('  - 流年干支计算: ✅ 不分性别（正确）');
    console.log('  - 十神关系分析: ✅ 不分性别（正确）');
    console.log('  - 应用解读: ⚠️ 可能需要考虑性别因素');
    
    return {
      liunianGanzhi: liunianGanzhi,
      tenGodsAnalysis: maleShishen,
      genderIndependent: true
    };
    
  } catch (error) {
    console.error('❌ 流年性别验证失败:', error.message);
    return null;
  }
}

// 执行所有测试
const dayunTest = testDayunGenderDifference();
const minorTest = testMinorFortuneGenderDifference();
const liunianTest = testLiunianGenderDifference();

// 4. 综合分析报告
console.log('\n📋 4. 综合性别算法分析报告');
console.log('-'.repeat(40));

console.log('🎯 性别区分必要性分析:');
console.log(`  1. 大运计算: ${dayunTest?.hasGenderDifference ? '✅ 必须区分性别' : '❌ 未正确区分性别'}`);
console.log(`  2. 小运计算: ${minorTest?.hasGenderDifference ? '✅ 必须区分性别' : '❌ 未正确区分性别'}`);
console.log(`  3. 流年计算: ${liunianTest?.genderIndependent ? '✅ 不需要区分性别' : '❌ 性别处理异常'}`);

console.log('\n📚 传统理论验证:');
console.log(`  - 大运规则正确性: ${dayunTest?.rulesCorrect ? '✅ 符合传统理论' : '❌ 违背传统理论'}`);
console.log('  - 阳男阴女顺行，阴男阳女逆行');
console.log('  - 小运遵循相同的顺逆规则');
console.log('  - 流年干支计算不分性别');

console.log('\n⚠️ 关键发现:');
if (dayunTest && !dayunTest.hasGenderDifference) {
  console.log('  🚨 大运系统未正确区分性别 - 这是根本性错误！');
}
if (minorTest && !minorTest.hasGenderDifference) {
  console.log('  🚨 小运系统未正确区分性别 - 这是根本性错误！');
}
if (dayunTest && !dayunTest.rulesCorrect) {
  console.log('  🚨 大运顺逆规则错误 - 违背传统命理理论！');
}

console.log('\n🎉 性别算法深度验证完成');
console.log('=' .repeat(60));
