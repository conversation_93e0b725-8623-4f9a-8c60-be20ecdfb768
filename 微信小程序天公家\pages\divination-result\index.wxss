/* pages/divination-result/index.wxss */

/* 页面根容器 - 确保滚动正常 */
page {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
}

.scroll-container {
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto;
  will-change: scroll-position;
}

.container {
  min-height: 100vh;
  background: transparent; /* 背景移到scroll-container */
  position: relative;
  padding: 40rpx 30rpx 120rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.04) 0%, transparent 35%),
    radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.04) 0%, transparent 35%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 40%);
  z-index: 0;
}

/* 顶部标题区域 */
.header-section {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 50rpx;
}

.master-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.master-avatar {
  width: 120rpx; /* 统一头像尺寸 */
  height: 120rpx; /* 统一头像尺寸 */
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3px solid rgba(139, 69, 19, 0.6);
  object-fit: cover; /* 确保图片填充方式一致 */
  box-shadow: 0 4rpx 15rpx rgba(139, 69, 19, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.master-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.master-name {
  font-size: 42rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.master-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5rpx;
}

/* 六神结果展示 */
.god-result-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.god-card {
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid;
  border-radius: 25rpx;
  padding: 40rpx 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* 吉卦样式 - 绿色系 */
.god-card.auspicious {
  background: rgba(72, 187, 120, 0.08);
  border-color: #48BB78 !important;
  box-shadow: 0 12rpx 40rpx rgba(72, 187, 120, 0.2),
              inset 0 1px 2px rgba(72, 187, 120, 0.1);
  animation: auspicious-glow 3s ease-in-out infinite alternate;
}

.god-card.major-auspicious {
  background: rgba(72, 187, 120, 0.12);
  box-shadow: 0 15rpx 50rpx rgba(72, 187, 120, 0.25),
              inset 0 1px 3px rgba(72, 187, 120, 0.15);
}

/* 凶卦样式 - 红色系 */
.god-card.inauspicious {
  background: rgba(245, 101, 101, 0.08);
  border-color: #F56565 !important;
  box-shadow: 0 12rpx 40rpx rgba(245, 101, 101, 0.2),
              inset 0 1px 2px rgba(245, 101, 101, 0.1);
  animation: inauspicious-pulse 2s ease-in-out infinite;
}

.god-card.major-inauspicious {
  background: rgba(245, 101, 101, 0.12);
  box-shadow: 0 15rpx 50rpx rgba(245, 101, 101, 0.25),
              inset 0 1px 3px rgba(245, 101, 101, 0.15);
}

/* 中等吉凶样式 - 橙色系 */
.god-card.medium-auspicious {
  background: rgba(72, 187, 120, 0.06);
}

.god-card.medium-inauspicious {
  background: rgba(237, 137, 54, 0.08);
  border-color: #ED8936 !important;
  box-shadow: 0 12rpx 40rpx rgba(237, 137, 54, 0.2),
              inset 0 1px 2px rgba(237, 137, 54, 0.1);
}

.god-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.god-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 吉卦图标样式 */
.god-icon.auspicious-icon {
  box-shadow: 0 8rpx 25rpx rgba(72, 187, 120, 0.4),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(72, 187, 120, 0.3);
}

/* 凶卦图标样式 */
.god-icon.inauspicious-icon {
  box-shadow: 0 8rpx 25rpx rgba(245, 101, 101, 0.4),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(245, 101, 101, 0.3);
}

.god-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.god-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.god-element {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.god-fortune {
  font-size: 36rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

/* 吉凶文字样式 */
.fortune-auspicious {
  text-shadow: 0 2rpx 8rpx rgba(72, 187, 120, 0.3);
}

.fortune-inauspicious {
  text-shadow: 0 2rpx 8rpx rgba(245, 101, 101, 0.3);
}

.god-description {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.description-text {
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 1.6;
  text-align: center;
  display: block;
  margin-bottom: 10rpx;
}

.source-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-style: italic;
  display: block;
}



/* 问题回顾 */
.question-review {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-content {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 15rpx;
  padding: 25rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.question-type {
  display: inline-block;
  background: rgba(255, 215, 0, 0.2);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.question-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  display: block;
}

/* 详细分析 */
.analysis-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.analysis-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 25rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5rpx);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 20rpx;
  text-align: center;
}

.card-content {
  color: rgba(255, 255, 255, 0.9);
}

.specific-advice {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 20rpx;
  display: block;
}

.advice-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.detail-item {
  font-size: 26rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
}

/* 行动建议样式 */
.action-items {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-title {
  font-size: 28rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
  display: block;
}

.action-item {
  font-size: 26rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
  display: block;
  padding-left: 10rpx;
}

/* 时间线 */
.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.timeline-item {
  display: flex;
  align-items: center;
}

.timeline-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 215, 0, 0.8);
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.timeline-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.timeline-day {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.timeline-date {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin: 5rpx 0;
}

.timeline-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 列表样式 */
.precaution-list,
.ritual-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.precaution-item,
.ritual-item {
  font-size: 26rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
}

/* 计算过程 */
.calculation-section {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.toggle-icon {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.calculation-detail {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-top: 20rpx;
}

.time-calculation,
.number-calculation,
.intelligent-analysis {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.calc-step {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.calc-result {
  font-size: 30rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-align: center;
  margin-top: 10rpx;
  padding: 15rpx;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 10rpx;
}

.calc-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 15rpx;
  text-align: center;
  display: block;
}

.calc-divider {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin: 15rpx 0;
  display: block;
}

.calc-warning {
  font-size: 22rpx;
  color: #ED8936;
  text-align: center;
  margin-top: 10rpx;
  display: block;
  line-height: 1.4;
}

.calc-subtitle {
  font-size: 26rpx;
  font-weight: bold;
  color: #FFD700;
  margin: 15rpx 0 10rpx 0;
  display: block;
}

.calc-note {
  font-size: 24rpx;
  color: rgba(255, 215, 0, 0.8);
  background: rgba(255, 215, 0, 0.1);
  padding: 10rpx 15rpx;
  border-radius: 8rpx;
  margin: 10rpx 0;
  display: block;
  line-height: 1.4;
}

.confidence-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin: 15rpx 0;
  padding: 12rpx 20rpx;
  background: rgba(0, 150, 255, 0.1);
  border-radius: 10rpx;
  border: 1px solid rgba(0, 150, 255, 0.3);
}

.confidence-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.confidence-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #00BFFF;
}

.confidence-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 5rpx;
}

/* 动画效果 */
@keyframes auspicious-glow {
  0% {
    box-shadow: 0 12rpx 40rpx rgba(72, 187, 120, 0.2),
                inset 0 1px 2px rgba(72, 187, 120, 0.1);
  }
  100% {
    box-shadow: 0 15rpx 50rpx rgba(72, 187, 120, 0.3),
                inset 0 2px 4px rgba(72, 187, 120, 0.2);
  }
}

@keyframes inauspicious-pulse {
  0%, 100% {
    box-shadow: 0 12rpx 40rpx rgba(245, 101, 101, 0.2),
                inset 0 1px 2px rgba(245, 101, 101, 0.1);
  }
  50% {
    box-shadow: 0 8rpx 30rpx rgba(245, 101, 101, 0.15),
                inset 0 1px 1px rgba(245, 101, 101, 0.05);
  }
}

/* 页面进入动画 - 已移除以避免滚动冲突 */

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停效果 */
.action-button {
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 化解建议样式 */
.resolution-content {
  padding: 20rpx 0;
}

.resolution-section {
  margin-bottom: 25rpx;
}

.resolution-subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 15rpx;
  display: block;
}

.resolution-methods,
.resolution-taboos {
  margin-left: 20rpx;
}

.method-item,
.taboo-item {
  margin-bottom: 12rpx;
}

.method-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  display: block;
}

.taboo-text {
  font-size: 26rpx;
  color: rgba(255, 182, 193, 0.9);
  line-height: 1.5;
  display: block;
}

.resolution-timing {
  margin-top: 20rpx;
  padding: 15rpx;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 10rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
  text-align: center;
}

.timing-text {
  font-size: 26rpx;
  color: #FFD700;
  font-weight: bold;
  display: block;
}

/* 底部操作区域 */
.action-section {
  position: relative;
  z-index: 1;
  margin-top: 60rpx;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  height: 90rpx;
  border: none;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, rgba(107, 91, 115, 1) 0%, rgba(168, 146, 109, 1) 100%);
  color: #FFFFFF;
  box-shadow: 0 6rpx 20rpx rgba(107, 91, 115, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.action-btn.tertiary {
  background: rgba(139, 115, 85, 0.2);
  color: #FFFFFF;
  border: 2px solid rgba(139, 115, 85, 0.4);
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.action-btn.primary:active {
  box-shadow: 0 3rpx 10rpx rgba(107, 91, 115, 0.5);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.2);
}

.action-btn.tertiary:active {
  background: rgba(139, 115, 85, 0.3);
}

.divination-note {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

/* 分享遮罩 */
.share-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.share-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 0 40rpx;
  max-width: 600rpx;
  width: 100%;
  backdrop-filter: blur(10rpx);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
}

.share-options {
  display: flex;
  gap: 30rpx;
}

.share-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: rgba(107, 91, 115, 0.1);
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.share-option:active {
  transform: scale(0.95);
  background: rgba(107, 91, 115, 0.2);
}

.share-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

/* 主题适配 */
.scroll-container.tarot-theme {
  background: linear-gradient(135deg, #6B5B73 0%, #A8926D 50%, #6B5B73 100%) !important;
}

/* 玉匣记结果样式 */
.yujiaji-result-section {
  margin: 30rpx 0;
}

.yujiaji-card {
  background: rgba(255, 215, 0, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.2);
  margin-bottom: 30rpx;
}

.yujiaji-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.yujiaji-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.yujiaji-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.yujiaji-info {
  flex: 1;
}

.yujiaji-subtitle {
  display: block;
  font-size: 28rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.yujiaji-luck {
  font-size: 24rpx;
  color: #FFA500;
  background: rgba(255, 215, 0, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.yujiaji-original {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 215, 0, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #FFD700;
}

.original-label {
  font-size: 24rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.original-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #E6E6FA;
  font-style: italic;
}

.yujiaji-interpretation {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
}

.interpretation-label {
  font-size: 24rpx;
  color: #87CEEB;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.interpretation-text {
  font-size: 30rpx;
  line-height: 1.7;
  color: #F0F8FF;
}

.yujiaji-title-section {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(135, 206, 235, 0.1);
  border-radius: 12rpx;
}

.title-label {
  font-size: 24rpx;
  color: #87CEEB;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.title-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #F0F8FF;
  font-weight: bold;
}

.yujiaji-keywords {
  margin-bottom: 25rpx;
}

.keywords-label {
  font-size: 24rpx;
  color: #DDA0DD;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.keyword-item {
  background: rgba(221, 160, 221, 0.2);
  color: #DDA0DD;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  border: 1rpx solid rgba(221, 160, 221, 0.3);
}

.yujiaji-source {
  text-align: right;
  margin-top: 20rpx;
}

.yujiaji-source .source-text {
  font-size: 22rpx;
  color: #B0B0B0;
  font-style: italic;
}

/* 备选解读样式 */
.alternatives-section {
  margin-top: 30rpx;
}

.alternatives-section .section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.toggle-btn {
  font-size: 24rpx;
  color: #87CEEB;
  padding: 8rpx 16rpx;
  background: rgba(135, 206, 235, 0.2);
  border-radius: 16rpx;
  border: 1rpx solid rgba(135, 206, 235, 0.3);
}

.alternatives-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.alternative-item {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.alternative-item:active {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
}

.alternative-title {
  font-size: 26rpx;
  color: #F0F8FF;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.alternative-desc {
  font-size: 24rpx;
  color: #B0B0B0;
  line-height: 1.5;
}
