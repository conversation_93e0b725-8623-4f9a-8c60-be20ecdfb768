#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def simple_structure_check(file_path):
    """简单的结构检查，模拟微信小程序编译器的检查逻辑"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 使用栈来跟踪标签嵌套
    tag_stack = []
    
    for line_num, line in enumerate(lines, 1):
        # 移除注释
        if '<!--' in line and '-->' in line:
            line = re.sub(r'<!--.*?-->', '', line)
        
        # 查找所有标签
        tags = re.findall(r'<(/?)([a-zA-Z-]+)(?:\s[^>]*)?>', line)
        
        for is_closing, tag_name in tags:
            if is_closing:  # 结束标签
                if not tag_stack:
                    print(f"❌ 第{line_num}行：意外的结束标签 </{tag_name}>，没有对应的开始标签")
                    return False
                
                expected_tag = tag_stack[-1][1]
                if tag_name != expected_tag:
                    print(f"❌ 第{line_num}行：标签不匹配")
                    print(f"   期望: </{expected_tag}>")
                    print(f"   实际: </{tag_name}>")
                    print(f"   开始标签位置: 第{tag_stack[-1][0]}行")
                    return False
                
                tag_stack.pop()
            else:  # 开始标签
                tag_stack.append((line_num, tag_name))
    
    if tag_stack:
        print(f"❌ 有 {len(tag_stack)} 个未闭合的标签:")
        for line_num, tag_name in tag_stack:
            print(f"   第{line_num}行: <{tag_name}>")
        return False
    
    print("✅ WXML结构检查通过！所有标签都正确匹配。")
    return True

if __name__ == "__main__":
    simple_structure_check("pages/bazi-result/index.wxml")
