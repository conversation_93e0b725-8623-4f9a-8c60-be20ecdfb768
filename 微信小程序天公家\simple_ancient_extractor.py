#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版古籍深度提取器
专门为数据分析、择日、匹配关系模块提取高质量数据
"""

import json
import re
import os
from datetime import datetime

class SimpleAncientExtractor:
    def __init__(self):
        self.rule_id_counter = 20000
        
        # 模块配置
        self.modules = {
            "数据分析系统": {
                "keywords": ["分析", "计算", "评估", "判断", "测算", "推断", "论断"],
                "target": 500
            },
            "择日模块": {
                "keywords": ["择日", "选日", "吉日", "凶日", "宜", "忌", "时辰", "日课"],
                "target": 300
            },
            "匹配关系模块": {
                "keywords": ["配偶", "夫妻", "婚姻", "合婚", "相配", "匹配", "和谐"],
                "target": 400
            }
        }
    
    def load_book_content(self, book_name):
        """加载古籍内容"""
        book_files = {
            "千里命稿": "千里命稿.txt",
            "渊海子平": "渊海子平.docx",
            "五行精纪": "五行精纪.docx"
        }
        
        if book_name not in book_files:
            return ""
        
        file_path = os.path.join("古籍资料", book_files[book_name])
        if not os.path.exists(file_path):
            return ""
        
        try:
            if file_path.endswith('.txt'):
                encodings = ['utf-8', 'gbk', 'gb2312']
                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            return f.read()
                    except UnicodeDecodeError:
                        continue
            elif file_path.endswith('.docx'):
                try:
                    from docx import Document
                    doc = Document(file_path)
                    return '\n'.join([p.text for p in doc.paragraphs])
                except ImportError:
                    print(f"需要安装python-docx库来处理{book_name}")
                    return ""
        except Exception as e:
            print(f"加载{book_name}失败: {e}")
            return ""
        
        return ""
    
    def extract_module_rules(self, content, module_name, config, book_name):
        """提取模块专用规则"""
        if not content:
            return []
        
        rules = []
        keywords = config["keywords"]
        target = config["target"]
        
        # 按句子分割
        sentences = re.split(r'[。；！？]', content)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 30 or len(sentence) > 300:
                continue
            
            # 检查关键词匹配
            keyword_count = sum(1 for keyword in keywords if keyword in sentence)
            
            if keyword_count >= 1:
                # 检查命理相关性
                theory_words = ['格局', '用神', '五行', '十神', '神煞', '调候', '旺衰']
                theory_count = sum(1 for word in theory_words if word in sentence)
                
                if theory_count >= 1:
                    rule = self.create_rule(sentence, module_name, book_name, keyword_count + theory_count)
                    rules.append(rule)
                    
                    if len(rules) >= target:
                        break
        
        return rules
    
    def create_rule(self, text, module_name, book_name, score):
        """创建规则"""
        rule = {
            "rule_id": f"SIMPLE_{module_name[:2].upper()}_{self.rule_id_counter:04d}",
            "pattern_name": f"{book_name}·{module_name}专用规则",
            "category": module_name,
            "module_type": module_name,
            "book_source": book_name,
            "original_text": text,
            "interpretations": f"出自《{book_name}》的{module_name}专业理论",
            "confidence": min(0.95, 0.85 + score * 0.02),
            "quality_score": score,
            "extracted_at": datetime.now().isoformat(),
            "extraction_phase": "简化古籍深度解析",
            "rule_type": f"{module_name}专用规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def extract_from_all_books(self):
        """从所有古籍提取"""
        print("🚀 开始简化古籍深度解析...")
        
        books = ["千里命稿", "渊海子平", "五行精纪"]
        all_module_data = {}
        
        for module_name, config in self.modules.items():
            all_module_data[module_name] = []
        
        for book_name in books:
            print(f"\n📖 处理{book_name}...")
            content = self.load_book_content(book_name)
            
            if content:
                print(f"  内容长度: {len(content):,} 字符")
                
                for module_name, config in self.modules.items():
                    rules = self.extract_module_rules(content, module_name, config, book_name)
                    all_module_data[module_name].extend(rules)
                    print(f"  {module_name}: 提取 {len(rules)} 条规则")
            else:
                print(f"  ❌ 无法加载{book_name}")
        
        # 统计结果
        total_rules = sum(len(rules) for rules in all_module_data.values())
        
        result_data = {
            "metadata": {
                "extraction_type": "简化古籍深度解析",
                "extraction_date": datetime.now().isoformat(),
                "total_rules": total_rules,
                "module_distribution": {module: len(rules) for module, rules in all_module_data.items()}
            },
            "module_data": all_module_data
        }
        
        return {
            "success": True,
            "data": result_data,
            "summary": {
                "总提取规则": total_rules,
                "数据分析系统": len(all_module_data["数据分析系统"]),
                "择日模块": len(all_module_data["择日模块"]),
                "匹配关系模块": len(all_module_data["匹配关系模块"])
            }
        }

def main():
    """主函数"""
    extractor = SimpleAncientExtractor()
    
    # 执行提取
    result = extractor.extract_from_all_books()
    
    if result.get("success"):
        # 保存结果
        output_filename = "simple_ancient_extraction_results.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        # 打印结果
        print("\n" + "="*60)
        print("简化古籍深度解析完成")
        print("="*60)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print(f"\n✅ 结果已保存到: {output_filename}")
        
    else:
        print(f"❌ 提取失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
