/* pages/bazi-input/index.wxss */
/* 八字排盘信息输入页面样式 */

.container {
  height: 100vh;
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 50%, #8B4513 100%);
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;
}

.container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 30% 30%, rgba(218, 165, 32, 0.08) 0%, transparent 25%),
                    radial-gradient(circle at 70% 70%, rgba(218, 165, 32, 0.08) 0%, transparent 25%);
  z-index: 0;
  pointer-events: none;
}

/* 师父身份信息 */
.master-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(139, 69, 19, 0.2);
  position: relative;
  z-index: 1;
}

.master-avatar {
  width: 120rpx; /* 统一头像尺寸 */
  height: 120rpx; /* 统一头像尺寸 */
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(218, 165, 32, 0.3);
  object-fit: cover; /* 确保图片填充方式一致 */
}

.master-text {
  display: flex;
  flex-direction: column;
}

.master-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
}

.master-subtitle {
  font-size: 24rpx;
  color: #A0522D;
  opacity: 0.8;
}



/* 输入区域 */
.input-section, .analysis-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 八字显示 */
.bazi-display {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx 0;
  color: #333;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 分析模块通用样式 */
.analysis-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(139, 69, 19, 0.1);
}

.section-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 主星分析样式 */
.main-stars-grid {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
}

.star-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 2rpx solid rgba(139, 69, 19, 0.1);
}

.star-item.day-star {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-color: #d4a574;
}

.star-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.star-value {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 副星分析样式 */
.deputy-stars-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.deputy-pillar {
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
  border-left: 4rpx solid #8B4513;
}

.deputy-header {
  font-size: 24rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.deputy-content {
  display: flex;
  justify-content: space-between;
}

.deputy-main, .deputy-sub {
  font-size: 22rpx;
  color: #666;
}

/* 藏干分析样式 */
.canggan-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.canggan-pillar {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 25rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.15);
}

.canggan-header {
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 15rpx;
  text-align: center;
}

.canggan-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.canggan-main {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.canggan-hidden, .canggan-tengods {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.canggan-label {
  color: #666;
  margin-right: 10rpx;
  min-width: 80rpx;
}

.canggan-list {
  color: #8B4513;
  flex: 1;
}

/* 纳音分析样式 */
.nayin-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.nayin-item {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  border: 1rpx solid rgba(40, 167, 69, 0.2);
}

.nayin-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.nayin-value {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #28a745;
}

/* 自坐分析样式 */
.self-sitting-content {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #d4a574;
}

.self-sitting-text {
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
}

/* 十神统计样式 */
.tengods-stats {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tengods-row {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
}

.tengods-stat {
  flex: 1;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx 10rpx;
  text-align: center;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
}

.stat-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
}

/* 格局分析样式 */
.pattern-content {
  background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #17a2b8;
}

.pattern-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.pattern-item:last-child {
  margin-bottom: 0;
}

.pattern-label {
  font-size: 22rpx;
  color: #666;
  min-width: 140rpx;
}

.pattern-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #17a2b8;
  flex: 1;
}

/* 长生十二宫分析样式 */
.changsheng-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.changsheng-pillar {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #6f42c1;
}

.changsheng-pillar.day-changsheng {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-left-color: #d4a574;
}

.changsheng-header {
  font-size: 26rpx;
  font-weight: bold;
  color: #6f42c1;
  margin-bottom: 15rpx;
  text-align: center;
}

.changsheng-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.changsheng-state {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 10rpx;
  background: rgba(111, 66, 193, 0.1);
  border-radius: 8rpx;
}

.changsheng-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 空亡分析样式 */
.kongwang-container {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #e53e3e;
}

.kongwang-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.kongwang-label {
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
}

.kongwang-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #e53e3e;
  flex: 1;
}

.kongwang-analysis, .kongwang-pillars {
  margin-top: 20rpx;
}

.kongwang-title {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.kongwang-desc {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
}

.kongwang-pillar-list {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.kongwang-pillar {
  background: rgba(229, 62, 62, 0.1);
  color: #e53e3e;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 命卦分析样式 */
.minggua-container {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #38b2ac;
}

.minggua-main {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.minggua-gua, .minggua-number, .minggua-category, .minggua-element, .minggua-direction {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.minggua-label {
  font-size: 22rpx;
  color: #666;
  min-width: 80rpx;
}

.minggua-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #38b2ac;
}

.minggua-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #38b2ac;
}

.minggua-analysis {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(56, 178, 172, 0.2);
}

.minggua-title {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.minggua-desc {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
}

.bazi-pillars {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
}

.pillar-item {
  text-align: center;
  flex: 1;
}

.pillar-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.pillar-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #d63384;
  font-family: 'KaiTi', serif;
}

.bazi-summary {
  text-align: center;
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10rpx;
}

.bazi-full {
  font-size: 32rpx;
  font-weight: bold;
  color: #495057;
  font-family: 'KaiTi', serif;
  letter-spacing: 8rpx;
}

/* 节气信息显示 */
.jieqi-info {
  margin-top: 20rpx;
  padding: 25rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
  border-radius: 15rpx;
  border: 2rpx solid rgba(218, 165, 32, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-label {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #6c757d;
  font-family: 'PingFang SC', sans-serif;
}

.info-content {
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10rpx;
  border-left: 4rpx solid #ffc107;
}

.jieqi-text {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.6;
  font-family: 'PingFang SC', sans-serif;
  word-break: break-all;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-right: 20rpx;
}

.title-tip {
  font-size: 24rpx;
  color: #95a5a6;
  flex: 1;
  min-width: 100%;
  margin-top: 8rpx;
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.required-mark {
  color: #e74c3c;
  margin-left: 5rpx;
  font-weight: bold;
}

.calendar-indicator {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-left: 10rpx;
}

/* 姓名输入 */
.name-input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #2c3e50;
  box-sizing: border-box;
  line-height: 80rpx;
}

.name-input::placeholder {
  color: #95a5a6;
}

/* 历法选择 */
.calendar-type-group {
  display: flex;
  gap: 20rpx;
}

.calendar-option {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.calendar-option.selected {
  background: rgba(139, 69, 19, 0.1);
  border-color: #8B4513;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.option-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.option-text {
  display: flex;
  flex-direction: column;
}

.option-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5rpx;
}

.calendar-option.selected .option-name {
  color: #8B4513;
}

.option-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 日期转换显示 */
.date-conversion {
  margin-top: 20rpx;
  padding: 15rpx;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 8rpx;
  border-left: 4rpx solid #8B4513;
}

.conversion-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.conversion-item:last-child {
  margin-bottom: 0;
}

.conversion-label {
  font-size: 24rpx;
  color: #8B4513;
  font-weight: bold;
  width: 80rpx;
}

.conversion-value {
  font-size: 24rpx;
  color: #2c3e50;
}

/* 真太阳时显示 */
.solar-time-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
  font-weight: bold;
}

.solar-time-info {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(247, 147, 30, 0.05));
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid #ff6b35;
}

.solar-time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.solar-time-item:last-child {
  margin-bottom: 0;
}

.solar-time-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.solar-time-value {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: bold;
}

.solar-time-value.corrected {
  color: #ff6b35;
}

.solar-time-value.diff {
  color: #f7931e;
}

/* 位置获取失败显示 */
.location-failed {
  display: flex;
  align-items: center;
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 8rpx;
  padding: 15rpx;
}

.failed-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.failed-text {
  flex: 1;
  font-size: 24rpx;
  color: #856404;
}

.retry-btn {
  font-size: 24rpx;
  color: #007bff;
  padding: 5rpx 10rpx;
  border: 1rpx solid #007bff;
  border-radius: 4rpx;
  background: white;
}

/* 城市选择器样式 */
.location-note {
  font-size: 20rpx;
  color: #999;
  margin-left: 10rpx;
}

.city-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.city-selector:active {
  background: #f5f5f5;
  border-color: #8B4513;
}

.city-name {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.city-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 城市选择弹窗样式 */
.city-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.city-modal.show {
  opacity: 1;
  visibility: visible;
}

.city-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.city-modal .modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.city-modal.show .modal-content {
  transform: translateY(0);
}

.city-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.city-modal .modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.city-modal .modal-close {
  font-size: 36rpx;
  color: #999;
  padding: 10rpx;
}

.city-modal .modal-body {
  padding: 0 30rpx 30rpx;
  max-height: 60vh;
}

.city-search-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  background: #f8f9fa;
}

.city-list {
  max-height: 50vh;
}

.city-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item:active {
  background: #f5f5f5;
}

.city-item-name {
  font-size: 28rpx;
  color: #2c3e50;
}

.city-item-check {
  font-size: 32rpx;
  color: #8B4513;
  font-weight: bold;
}

/* 真太阳时显示 */
.solar-time-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
  font-weight: bold;
}

.solar-time-info {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(247, 147, 30, 0.05));
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid #ff6b35;
}

.solar-time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.solar-time-item:last-child {
  margin-bottom: 0;
}

.solar-time-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.solar-time-value {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: bold;
}

.solar-time-value.corrected {
  color: #ff6b35;
}

.solar-time-value.diff {
  color: #f7931e;
}

/* 位置获取失败显示 */
.location-failed {
  display: flex;
  align-items: center;
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 8rpx;
  padding: 15rpx;
}

.failed-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.failed-text {
  flex: 1;
  font-size: 24rpx;
  color: #856404;
}

.retry-btn {
  font-size: 24rpx;
  color: #007bff;
  padding: 5rpx 10rpx;
  border: 1rpx solid #007bff;
  border-radius: 4rpx;
  background: white;
}

/* 日期选择器 */
.date-picker-group {
  display: flex;
  gap: 15rpx;
}

.date-picker {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-display {
  padding: 20rpx 15rpx;
  text-align: center;
  font-size: 28rpx;
  color: #495057;
}

/* 时间选择器 */
.time-picker-group {
  display: flex;
  gap: 15rpx;
}

.time-picker {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

/* 性别选择器 */
.gender-picker {
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.gender-display {
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  font-size: 28rpx;
  color: #495057;
}

.gender-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

/* 地点输入 */
.location-input {
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.location-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #495057;
}

.location-arrow {
  font-size: 24rpx;
  color: #adb5bd;
}

/* 分析功能展示 */
.analysis-features {
  margin-top: 25rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-row {
  display: flex;
  gap: 20rpx;
}

.feature-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(139, 69, 19, 0.08);
  transform: translateY(-2rpx);
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  width: 40rpx;
  text-align: center;
}

.feature-text {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
  line-height: 1.3;
}

/* 按钮区域 */
.button-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.help-button {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.start-button {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.start-button[disabled] {
  opacity: 0.6;
}

.button-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 地点输入弹窗 */
.location-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.location-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 500rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}

.modal-close {
  font-size: 32rpx;
  color: #adb5bd;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.location-input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #e9ecef;
}

.modal-button {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 28rpx;
  background: white;
}

.modal-button.cancel {
  color: #6c757d;
  border-right: 1rpx solid #e9ecef;
}

.modal-button.confirm {
  color: #667eea;
  font-weight: 500;
}

/* 底部信息 */
.footer-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.info-text {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.4;
}
