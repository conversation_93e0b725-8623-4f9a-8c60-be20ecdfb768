/**
 * 执行宋元明清第二批次数据合并
 */

const SongYuanMingQingBatch2Merger = require('../utils/merge_song_yuan_ming_qing_batch2.js');

async function main() {
  try {
    const merger = new SongYuanMingQingBatch2Merger();
    const result = await merger.execute();
    
    if (result.success) {
      console.log('\n✅ 合并成功完成!');
      process.exit(0);
    } else {
      console.log('\n❌ 合并失败!');
      process.exit(1);
    }
  } catch (error) {
    console.error('执行错误:', error);
    process.exit(1);
  }
}

main();
