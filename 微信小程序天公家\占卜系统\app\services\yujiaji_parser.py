#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
《玉匣记》生产级文本解析器
优化版本，用于实际项目部署
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict, field
from enum import Enum
from collections import defaultdict, Counter
from pathlib import Path
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentType(Enum):
    """内容类型枚举"""
    GANZHI_DIVINATION = "干支占卜"
    FESTIVAL_BIRTHDAY = "节气圣诞"
    CONSTELLATION = "二十八宿"
    TIME_DIVINATION = "时辰占卜"
    SEASONAL_PREDICTION = "季节预测"
    BODY_SIGNS = "身体征象"
    DAILY_TABOO = "日常禁忌"
    FORMULA_SONG = "歌诀口诀"
    GENERAL = "通用"

class LuckLevel(Enum):
    """吉凶等级枚举"""
    VERY_AUSPICIOUS = "大吉"
    AUSPICIOUS = "吉"
    NEUTRAL = "平"
    INAUSPICIOUS = "凶"
    VERY_INAUSPICIOUS = "大凶"
    UNKNOWN = "未知"

@dataclass
class DateInfo:
    """日期信息"""
    heavenly_stem: Optional[str] = None
    earthly_branch: Optional[str] = None
    full_name: Optional[str] = None
    lunar_date: Optional[str] = None
    
    def __post_init__(self):
        if self.heavenly_stem and self.earthly_branch and not self.full_name:
            self.full_name = f"{self.heavenly_stem}{self.earthly_branch}"

@dataclass
class TimeInfo:
    """时辰信息"""
    time_period: str
    description: Optional[str] = None

@dataclass
class ConstellationInfo:
    """二十八宿信息"""
    name: str
    element: Optional[str] = None
    animal: Optional[str] = None
    person: Optional[str] = None
    luck_level: LuckLevel = LuckLevel.UNKNOWN

@dataclass
class DivinationEntry:
    """占卜条目"""
    id: str
    title: str
    content_type: ContentType
    original_text: str
    description: str
    interpretation: Optional[str] = None
    luck_level: LuckLevel = LuckLevel.UNKNOWN
    
    # 结构化信息
    date_info: Optional[DateInfo] = None
    time_info: Optional[TimeInfo] = None
    constellation_info: Optional[ConstellationInfo] = None
    direction: Optional[str] = None
    
    # 分类信息
    main_category: str = ""
    sub_category: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    
    # 元数据
    source_line: int = 0
    confidence_score: float = 1.0
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())

class YuJiaJiParser:
    """《玉匣记》生产级解析器"""
    
    def __init__(self):
        self.entries: List[DivinationEntry] = []
        self.statistics = defaultdict(int)
        self.current_chapter = ""
        self.current_section = ""
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 初始化关键词库
        self._init_keywords()
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        # 章节标题模式
        self.chapter_pattern = re.compile(r'^(理论吉凶日篇|民俗吉凶日篇|杂占篇)(?:\s+(.+))?$')
        
        # 干支模式
        self.ganzhi_pattern = re.compile(r'([甲乙丙丁戊己庚辛壬癸])([子丑寅卯辰巳午未申酉戌亥])(?:日)?')
        
        # 时辰模式
        self.time_pattern = re.compile(r'([子丑寅卯辰巳午未申酉戌亥])时[:：]?')
        
        # 农历日期模式
        self.lunar_pattern = re.compile(r'(初一|初二|初三|初四|初五|初六|初七|初八|初九|初十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十|廿一|廿二|廿三|廿四|廿五|廿六|廿七|廿八|廿九|三十)日')
        
        # 二十八宿模式
        self.constellation_pattern = re.compile(r'([角亢氐房心尾箕斗牛女虚危室壁奎娄胃昴毕觜参井鬼柳星张翼轸])([木火土金水])([蛟龙貉兔狐虎豹斗牛女土貐狼狗雉鸡乌猴猿犴羊獐马鹿蛇蚓])\s*([^\s]*)\s*(吉|凶)')
        
        # 吉凶模式
        self.luck_pattern = re.compile(r'(大吉庆?|大吉利?|大吉昌?|大吉|吉庆?|吉利?|吉|平平?|平|大凶|凶)')
        
        # 方位模式
        self.direction_pattern = re.compile(r'(正?[东西南北]|东南|东北|西南|西北|中央)')
        
        # 节日模式
        self.festival_pattern = re.compile(r'(初一|初二|初三|初四|初五|初六|初七|初八|初九|初十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十|廿一|廿二|廿三|廿四|廿五|廿六|廿七|廿八|廿九|三十)日[:：](.+)')
        
        logger.info("正则表达式模式编译完成")
    
    def _init_keywords(self):
        """初始化关键词库"""
        self.keywords_dict = {
            '人生大事': ['嫁娶', '婚姻', '出行', '开张', '修造', '安葬', '祭祀'],
            '财运相关': ['求财', '财帛', '招财', '生财', '破财'],
            '健康医疗': ['治病', '疾病', '健康', '医药', '养生'],
            '家庭关系': ['子孙', '父母', '夫妻', '兄弟', '家庭'],
            '事业官职': ['官职', '仕途', '升迁', '考试', '功名'],
            '季节时令': ['春', '夏', '秋', '冬', '节气', '时令'],
            '宗教神佛': ['佛', '菩萨', '真人', '真君', '帝君', '大帝', '娘娘']
        }
    
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """解析文件"""
        logger.info(f"开始解析文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            raise
        
        self.statistics['total_lines'] = len(lines)
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为章节标题
            chapter_match = self.chapter_pattern.match(line)
            if chapter_match:
                self.current_chapter = chapter_match.group(1)
                self.current_section = chapter_match.group(2) if chapter_match.group(2) else ""
                self.statistics['chapters'] += 1
                continue
            
            # 解析内容条目
            entry = self._parse_line(line, line_num)
            if entry:
                self.entries.append(entry)
                self.statistics['parsed_entries'] += 1
                self.statistics[f'type_{entry.content_type.value}'] += 1
        
        logger.info(f"解析完成: 总行数 {self.statistics['total_lines']}, 解析条目 {self.statistics['parsed_entries']}")
        return self._generate_result()
    
    def _parse_line(self, line: str, line_num: int) -> Optional[DivinationEntry]:
        """解析单行文本"""
        entry_id = f"entry_{uuid.uuid4().hex[:8]}"
        
        # 确定内容类型和提取信息
        content_type, extracted_info = self._classify_content(line)
        
        # 创建条目
        entry = DivinationEntry(
            id=entry_id,
            title=self._generate_title(line, extracted_info),
            content_type=content_type,
            original_text=line,
            description=self._extract_description(line),
            luck_level=self._extract_luck_level(line),
            main_category=self.current_chapter,
            sub_category=self.current_section if self.current_section else None,
            keywords=self._extract_keywords(line),
            source_line=line_num,
            confidence_score=self._calculate_confidence(line, content_type)
        )
        
        # 设置结构化信息
        if extracted_info.get('date_info'):
            entry.date_info = extracted_info['date_info']
        if extracted_info.get('time_info'):
            entry.time_info = extracted_info['time_info']
        if extracted_info.get('constellation_info'):
            entry.constellation_info = extracted_info['constellation_info']
        if extracted_info.get('direction'):
            entry.direction = extracted_info['direction']
        
        return entry
    
    def _classify_content(self, line: str) -> Tuple[ContentType, Dict[str, Any]]:
        """分类内容并提取信息"""
        extracted_info = {}
        
        # 1. 检测干支占卜
        ganzhi_match = self.ganzhi_pattern.search(line)
        if ganzhi_match:
            extracted_info['date_info'] = DateInfo(
                heavenly_stem=ganzhi_match.group(1),
                earthly_branch=ganzhi_match.group(2),
                full_name=ganzhi_match.group(0)
            )
            return ContentType.GANZHI_DIVINATION, extracted_info
        
        # 2. 检测时辰占卜
        time_match = self.time_pattern.search(line)
        if time_match and ':' in line:  # 必须有冒号格式
            extracted_info['time_info'] = TimeInfo(
                time_period=time_match.group(0),
                description=line.split(':', 1)[1].strip() if ':' in line else None
            )
            return ContentType.TIME_DIVINATION, extracted_info
        
        # 3. 检测二十八宿
        constellation_match = self.constellation_pattern.search(line)
        if constellation_match:
            extracted_info['constellation_info'] = ConstellationInfo(
                name=constellation_match.group(1) + constellation_match.group(2) + constellation_match.group(3),
                element=constellation_match.group(2),
                animal=constellation_match.group(3),
                person=constellation_match.group(4) if constellation_match.group(4) else None,
                luck_level=LuckLevel.AUSPICIOUS if constellation_match.group(5) == '吉' else LuckLevel.INAUSPICIOUS
            )
            return ContentType.CONSTELLATION, extracted_info
        
        # 4. 检测节气圣诞
        festival_match = self.festival_pattern.search(line)
        if festival_match:
            return ContentType.FESTIVAL_BIRTHDAY, extracted_info
        
        # 5. 检测歌诀
        if any(word in line for word in ['歌曰', '诀曰', '口诀']):
            return ContentType.FORMULA_SONG, extracted_info
        
        # 6. 检测身体征象
        if any(word in line for word in ['面热', '眼跳', '耳鸣', '心惊', '肉跳']):
            return ContentType.BODY_SIGNS, extracted_info
        
        # 7. 提取方位信息
        direction_match = self.direction_pattern.search(line)
        if direction_match:
            extracted_info['direction'] = direction_match.group(0)
        
        return ContentType.GENERAL, extracted_info
    
    def _generate_title(self, line: str, extracted_info: Dict[str, Any]) -> str:
        """生成条目标题"""
        if extracted_info.get('date_info'):
            return f"{extracted_info['date_info'].full_name}日占卜"
        elif extracted_info.get('time_info'):
            return f"{extracted_info['time_info'].time_period}占卜"
        elif extracted_info.get('constellation_info'):
            return f"{extracted_info['constellation_info'].name}星宿"
        else:
            # 取前20个字符作为标题
            return line[:20] + ("..." if len(line) > 20 else "")
    
    def _extract_description(self, line: str) -> str:
        """提取描述"""
        # 移除标识符，保留主要内容
        desc = line
        desc = re.sub(r'[甲乙丙丁戊己庚辛壬癸][子丑寅卯辰巳午未申酉戌亥]日?', '', desc)
        desc = re.sub(r'[子丑寅卯辰巳午未申酉戌亥]时[:：]?', '', desc)
        return desc.strip()
    
    def _extract_luck_level(self, line: str) -> LuckLevel:
        """提取吉凶等级"""
        luck_match = self.luck_pattern.search(line)
        if luck_match:
            luck_text = luck_match.group(1)
            if "大吉" in luck_text:
                return LuckLevel.VERY_AUSPICIOUS
            elif luck_text in ["吉", "吉利", "吉庆"]:
                return LuckLevel.AUSPICIOUS
            elif luck_text in ["平", "平平"]:
                return LuckLevel.NEUTRAL
            elif luck_text == "大凶":
                return LuckLevel.VERY_INAUSPICIOUS
            elif luck_text == "凶":
                return LuckLevel.INAUSPICIOUS
        return LuckLevel.UNKNOWN
    
    def _extract_keywords(self, line: str) -> List[str]:
        """提取关键词"""
        keywords = []
        for category, words in self.keywords_dict.items():
            for word in words:
                if word in line:
                    keywords.append(word)
        return list(set(keywords))
    
    def _calculate_confidence(self, line: str, content_type: ContentType) -> float:
        """计算置信度"""
        confidence = 0.5  # 基础置信度
        
        # 根据内容类型调整
        if content_type == ContentType.GANZHI_DIVINATION:
            confidence = 0.95
        elif content_type == ContentType.TIME_DIVINATION:
            confidence = 0.9
        elif content_type == ContentType.CONSTELLATION:
            confidence = 0.98
        elif content_type == ContentType.FESTIVAL_BIRTHDAY:
            confidence = 0.8
        
        # 根据吉凶信息调整
        if self.luck_pattern.search(line):
            confidence += 0.1
        
        # 根据关键词数量调整
        keywords_count = len(self._extract_keywords(line))
        confidence += min(keywords_count * 0.05, 0.2)
        
        return min(confidence, 1.0)
    
    def _generate_result(self) -> Dict[str, Any]:
        """生成解析结果"""
        return {
            "metadata": {
                "total_lines": self.statistics['total_lines'],
                "parsed_entries": self.statistics['parsed_entries'],
                "chapters": self.statistics.get('chapters', 0),
                "parsing_rate": self.statistics['parsed_entries'] / self.statistics['total_lines'] if self.statistics['total_lines'] > 0 else 0,
                "content_types": {ct.value: self.statistics.get(f'type_{ct.value}', 0) for ct in ContentType}
            },
            "entries": [asdict(entry) for entry in self.entries]
        }
    
    def save_results(self, output_file: str):
        """保存解析结果"""
        result = self._generate_result()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"解析结果已保存到: {output_file}")

def main():
    """主函数"""
    parser = YuJiaJiParser()
    
    try:
        # 解析文件
        result = parser.parse_file("玉匣记全本.txt")
        
        # 保存结果
        parser.save_results("data/yujiaji_parsed.json")
        
        # 打印统计信息
        metadata = result['metadata']
        print("=" * 60)
        print("《玉匣记》解析完成")
        print("=" * 60)
        print(f"总行数: {metadata['total_lines']}")
        print(f"解析条目数: {metadata['parsed_entries']}")
        print(f"解析成功率: {metadata['parsing_rate']:.1%}")
        print(f"章节数: {metadata['chapters']}")
        
        print("\n内容类型分布:")
        for content_type, count in metadata['content_types'].items():
            if count > 0:
                print(f"  {content_type}: {count}")
        
        print(f"\n详细结果已保存到: data/yujiaji_parsed.json")
        
    except Exception as e:
        logger.error(f"解析失败: {e}")
        raise

if __name__ == "__main__":
    main()
