/**
 * 小运计算系统测试
 * 检查小运计算器是否正常工作
 */

console.log('🔮 小运计算系统测试');
console.log('='.repeat(50));

// 模拟前端数据结构
const testBaziData = {
  baziInfo: {
    yearPillar: { heavenly: '辛', earthly: '丑' },
    monthPillar: { heavenly: '甲', earthly: '午' },
    dayPillar: { heavenly: '癸', earthly: '卯' },
    timePillar: { heavenly: '壬', earthly: '戌' }
  },
  userInfo: {
    gender: '男'
  },
  birthInfo: {
    year: 2021  // 测试4岁小运
  }
};

// 测试小运计算器
function testMinorFortuneCalculator() {
  console.log('\n📋 测试1：小运计算器基础功能');
  
  try {
    // 导入小运计算器
    const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
    const calculator = new MinorFortuneCalculator();
    
    // 构建八字数据格式
    const bazi = {
      yearPillar: {
        gan: testBaziData.baziInfo.yearPillar.heavenly,
        zhi: testBaziData.baziInfo.yearPillar.earthly
      },
      monthPillar: {
        gan: testBaziData.baziInfo.monthPillar.heavenly,
        zhi: testBaziData.baziInfo.monthPillar.earthly
      },
      dayPillar: {
        gan: testBaziData.baziInfo.dayPillar.heavenly,
        zhi: testBaziData.baziInfo.dayPillar.earthly
      },
      hourPillar: {
        gan: testBaziData.baziInfo.timePillar.heavenly,
        zhi: testBaziData.baziInfo.timePillar.earthly
      },
      gender: testBaziData.userInfo.gender
    };
    
    console.log('📊 测试数据:', bazi);
    
    // 计算当前年龄
    const currentYear = new Date().getFullYear();
    const birthYear = testBaziData.birthInfo.year;
    const currentAge = currentYear - birthYear;
    
    console.log(`👶 当前年龄: ${currentAge}岁`);
    
    // 测试单个小运计算
    const currentMinorFortune = calculator.calculate(bazi, currentAge);
    console.log('🔮 当前小运:', currentMinorFortune);
    
    // 测试所有小运计算
    const allMinorFortunes = calculator.calculateAllMinorFortunes(bazi);
    console.log('📋 所有小运:', allMinorFortunes.length, '个');
    
    return {
      success: true,
      currentMinorFortune: currentMinorFortune,
      allMinorFortunes: allMinorFortunes,
      currentAge: currentAge
    };
    
  } catch (error) {
    console.error('❌ 小运计算器测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 测试前端集成
function testFrontendIntegration() {
  console.log('\n📋 测试2：前端集成功能');
  
  try {
    // 模拟前端计算小运数据的方法
    const calculateMinorFortune = function(baziData) {
      console.log('🔮 开始计算小运数据...');

      try {
        // 使用已导入的小运计算器
        const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
        const calculator = new MinorFortuneCalculator();

        // 构建八字数据格式
        const bazi = {
          yearPillar: {
            gan: baziData.baziInfo.yearPillar.heavenly,
            zhi: baziData.baziInfo.yearPillar.earthly
          },
          monthPillar: {
            gan: baziData.baziInfo.monthPillar.heavenly,
            zhi: baziData.baziInfo.monthPillar.earthly
          },
          dayPillar: {
            gan: baziData.baziInfo.dayPillar.heavenly,
            zhi: baziData.baziInfo.dayPillar.earthly
          },
          hourPillar: {
            gan: baziData.baziInfo.timePillar.heavenly,
            zhi: baziData.baziInfo.timePillar.earthly
          },
          gender: baziData.userInfo.gender === '男' ? '男' : '女'
        };

        // 计算当前年龄
        const currentYear = new Date().getFullYear();
        const birthYear = baziData.birthInfo.year;
        const currentAge = currentYear - birthYear;

        console.log(`👶 当前年龄: ${currentAge}岁`);

        // 如果年龄超出小运适用范围，返回提示信息
        if (currentAge > 10) {
          return {
            applicable: false,
            reason: '小运仅适用于1-10岁，当前年龄已超出范围',
            currentAge: currentAge,
            note: '《三命通会·卷八》：小运补大运之不足，未交大运前用之'
          };
        }

        // 计算所有小运
        const allMinorFortunes = calculator.calculateAllMinorFortunes(bazi);

        // 获取当前小运
        const currentMinorFortune = currentAge >= 1 && currentAge <= 10
          ? calculator.calculate(bazi, currentAge)
          : null;

        console.log('✅ 小运计算完成');

        return {
          applicable: true,
          currentAge: currentAge,
          currentMinorFortune: currentMinorFortune,
          allMinorFortunes: allMinorFortunes,
          basis: "《三命通会·卷八》小运起法",
          note: "小运补大运之不足，未交大运前用之，阳男阴女顺行，阴男阳女逆行"
        };

      } catch (error) {
        console.error('❌ 小运计算失败:', error);
        return {
          applicable: false,
          error: error.message,
          reason: '小运计算系统异常'
        };
      }
    };
    
    // 执行前端集成测试
    const result = calculateMinorFortune(testBaziData);
    console.log('🎯 前端集成测试结果:', result);
    
    return result;
    
  } catch (error) {
    console.error('❌ 前端集成测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 测试年龄边界条件
function testAgeBoundaryConditions() {
  console.log('\n📋 测试3：年龄边界条件');
  
  const testCases = [
    { age: 0, expected: 'invalid' },
    { age: 1, expected: 'valid' },
    { age: 5, expected: 'valid' },
    { age: 10, expected: 'valid' },
    { age: 11, expected: 'invalid' },
    { age: 20, expected: 'invalid' }
  ];
  
  try {
    const MinorFortuneCalculator = require('./utils/minor_fortune_calculator.js');
    const calculator = new MinorFortuneCalculator();
    
    const bazi = {
      yearPillar: { gan: '辛', zhi: '丑' },
      monthPillar: { gan: '甲', zhi: '午' },
      dayPillar: { gan: '癸', zhi: '卯' },
      hourPillar: { gan: '壬', zhi: '戌' },
      gender: '男'
    };
    
    let passedTests = 0;
    
    for (const testCase of testCases) {
      const result = calculator.calculate(bazi, testCase.age);
      const isValid = result !== null;
      const expectedValid = testCase.expected === 'valid';
      
      if (isValid === expectedValid) {
        console.log(`✅ 年龄${testCase.age}岁测试通过`);
        passedTests++;
      } else {
        console.log(`❌ 年龄${testCase.age}岁测试失败`);
      }
    }
    
    console.log(`📊 边界条件测试: ${passedTests}/${testCases.length} 通过`);
    
    return {
      success: passedTests === testCases.length,
      passedTests: passedTests,
      totalTests: testCases.length
    };
    
  } catch (error) {
    console.error('❌ 边界条件测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行小运计算系统测试...\n');
  
  const test1 = testMinorFortuneCalculator();
  const test2 = testFrontendIntegration();
  const test3 = testAgeBoundaryConditions();
  
  console.log('\n📊 测试总结:');
  console.log('='.repeat(50));
  console.log(`测试1 - 小运计算器基础功能: ${test1.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`测试2 - 前端集成功能: ${test2.applicable !== undefined ? '✅ 通过' : '❌ 失败'}`);
  console.log(`测试3 - 年龄边界条件: ${test3.success ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = test1.success && (test2.applicable !== undefined) && test3.success;
  console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 存在测试失败'}`);
  
  return {
    allPassed: allPassed,
    test1: test1,
    test2: test2,
    test3: test3
  };
}

// 执行测试
const testResults = runAllTests();

// 导出测试结果
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testResults;
}
