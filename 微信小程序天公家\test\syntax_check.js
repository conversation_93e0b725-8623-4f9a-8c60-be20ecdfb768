// test/syntax_check.js
// 简单的语法检查

console.log('🔧 开始语法检查...');

try {
  // 尝试加载文件
  require('../utils/enhanced_advice_generator.js');
  console.log('✅ enhanced_advice_generator.js 语法正确');
} catch (error) {
  console.log('❌ enhanced_advice_generator.js 语法错误:');
  console.log('错误信息:', error.message);
  console.log('错误位置:', error.stack.split('\n')[0]);
  
  // 尝试定位具体问题
  const fs = require('fs');
  const content = fs.readFileSync('./utils/enhanced_advice_generator.js', 'utf8');
  const lines = content.split('\n');
  
  // 检查第376行附近
  console.log('\n🔍 第376行附近的内容:');
  for (let i = 370; i < 385; i++) {
    if (lines[i-1]) {
      console.log(`${i}: ${lines[i-1]}`);
    }
  }
  
  // 检查括号匹配
  let openBraces = 0;
  let openParens = 0;
  let openBrackets = 0;
  
  for (let i = 0; i < content.length; i++) {
    const char = content[i];
    if (char === '{') openBraces++;
    else if (char === '}') openBraces--;
    else if (char === '(') openParens++;
    else if (char === ')') openParens--;
    else if (char === '[') openBrackets++;
    else if (char === ']') openBrackets--;
  }
  
  console.log('\n🔍 括号匹配检查:');
  console.log(`大括号 {}: ${openBraces === 0 ? '匹配' : '不匹配 (' + openBraces + ')'}`);
  console.log(`小括号 (): ${openParens === 0 ? '匹配' : '不匹配 (' + openParens + ')'}`);
  console.log(`方括号 []: ${openBrackets === 0 ? '匹配' : '不匹配 (' + openBrackets + ')'}`);
}
