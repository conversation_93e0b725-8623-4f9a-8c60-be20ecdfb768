// diagnose_static_dynamic_and_advice_issues.js
// 诊断静态vs动态力量对比和个性化建议问题

console.log('🔍 诊断静态vs动态力量对比和个性化建议问题...');

// 问题1：静态vs动态数值相同的诊断
function diagnoseStaticDynamicIssue() {
  console.log('\n📋 问题1：静态vs动态力量对比数值相同');
  console.log('='.repeat(50));
  
  console.log('🔍 可能的原因分析:');
  console.log('1. 动态交互引擎没有正确应用调整');
  console.log('2. 四柱八字没有形成有效的合会冲刑关系');
  console.log('3. 动态调整系数设置过小，变化不明显');
  console.log('4. 数据传递过程中动态结果被静态结果覆盖');
  
  // 模拟正常的动态调整过程
  console.log('\n🔧 模拟正常的动态调整过程:');
  
  const mockStaticPowers = {
    '木': 16.8,
    '火': 8.2,
    '土': 12.5,
    '金': 2.4,
    '水': 15.1
  };
  
  console.log('静态力量:', mockStaticPowers);
  
  // 模拟有交互关系的情况
  const mockInteractions = {
    threeHarmony: [
      { elements: ['申', '子', '辰'], resultElement: '水', boost: 2.5 }
    ],
    sixClashes: [
      { elements: ['子', '午'], suppress: 0.6 }
    ]
  };
  
  console.log('发现的交互关系:', mockInteractions);
  
  // 应用动态调整
  const mockDynamicPowers = { ...mockStaticPowers };
  
  // 三合局：申子辰合水局，水力量提升2.5倍
  if (mockInteractions.threeHarmony.length > 0) {
    mockDynamicPowers['水'] *= 2.5;
    console.log('✅ 应用三合局调整: 水力量 × 2.5');
  }
  
  // 六冲：子午冲，水火力量削弱至60%
  if (mockInteractions.sixClashes.length > 0) {
    mockDynamicPowers['水'] *= 0.6;
    mockDynamicPowers['火'] *= 0.6;
    console.log('✅ 应用六冲调整: 水火力量 × 0.6');
  }
  
  console.log('动态力量:', mockDynamicPowers);
  
  // 计算变化
  console.log('\n📊 力量变化对比:');
  Object.keys(mockStaticPowers).forEach(element => {
    const staticValue = mockStaticPowers[element];
    const dynamicValue = mockDynamicPowers[element];
    const change = dynamicValue - staticValue;
    const changePercent = staticValue > 0 ? (change / staticValue * 100) : 0;
    
    console.log(`${element}: 静态${staticValue} → 动态${dynamicValue.toFixed(1)} (${changePercent > 0 ? '+' : ''}${changePercent.toFixed(1)}%)`);
  });
  
  console.log('\n🎯 诊断结论:');
  console.log('如果前端显示的静态和动态数值完全相同，说明:');
  console.log('1. 动态交互引擎没有被正确调用');
  console.log('2. 或者当前八字确实没有形成有效的交互关系');
  console.log('3. 或者交互关系的调整系数设置过小');
  
  return {
    staticPowers: mockStaticPowers,
    dynamicPowers: mockDynamicPowers,
    hasSignificantChanges: Object.keys(mockStaticPowers).some(element => 
      Math.abs(mockDynamicPowers[element] - mockStaticPowers[element]) > 0.1
    )
  };
}

// 问题2：个性化建议只有置信度的诊断
function diagnoseAdviceIssue() {
  console.log('\n📋 问题2：个性化建议只显示置信度90%');
  console.log('='.repeat(50));
  
  console.log('🔍 可能的原因分析:');
  console.log('1. 建议生成函数返回的数据结构不正确');
  console.log('2. 前端数据绑定路径错误');
  console.log('3. 建议内容为空或未正确生成');
  console.log('4. 数据传递过程中建议内容丢失');
  
  // 模拟正确的建议数据结构
  console.log('\n🔧 模拟正确的建议数据结构:');
  
  const mockRecommendations = [
    {
      type: 'element_balance',
      title: '五行平衡建议',
      content: '您的命局中水元素偏强，建议通过土元素来调节平衡',
      confidence: 0.92,
      priority: 'high',
      category: '五行调理'
    },
    {
      type: 'interaction_optimization',
      title: '交互关系优化',
      content: '申子辰三合水局增强了您的智慧和适应能力，建议在决策时充分发挥这一优势',
      confidence: 0.88,
      priority: 'medium',
      category: '格局运用'
    },
    {
      type: 'daymaster_support',
      title: '日主扶助建议',
      content: '日主偏弱，建议多接触木火元素相关的环境和活动',
      confidence: 0.85,
      priority: 'high',
      category: '日主调理'
    }
  ];
  
  console.log('正确的建议数据结构:');
  mockRecommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec.title}`);
    console.log(`   内容: ${rec.content}`);
    console.log(`   置信度: ${(rec.confidence * 100).toFixed(1)}%`);
    console.log(`   优先级: ${rec.priority}`);
    console.log(`   分类: ${rec.category}`);
  });
  
  // 检查前端绑定路径
  console.log('\n🔧 检查前端数据绑定路径:');
  console.log('正确的绑定路径应该是:');
  console.log('{{wuxingRecommendations[0].title}} - 建议标题');
  console.log('{{wuxingRecommendations[0].content}} - 建议内容');
  console.log('{{wuxingRecommendations[0].confidence}} - 置信度');
  
  // 检查可能的错误绑定
  console.log('\n❌ 可能的错误绑定:');
  console.log('如果只显示置信度90%，可能是:');
  console.log('1. 只绑定了 {{wuxingRecommendations.confidence}}');
  console.log('2. 或者建议数组为空，只有整体置信度');
  console.log('3. 或者前端循环渲染有问题');
  
  console.log('\n🎯 诊断结论:');
  console.log('如果个性化建议只显示置信度，说明:');
  console.log('1. 建议生成函数可能返回了空数组');
  console.log('2. 或者前端只绑定了confidence字段');
  console.log('3. 或者数据结构不匹配前端期望的格式');
  
  return {
    expectedStructure: mockRecommendations,
    hasValidRecommendations: mockRecommendations.length > 0,
    averageConfidence: mockRecommendations.reduce((sum, rec) => sum + rec.confidence, 0) / mockRecommendations.length
  };
}

// 生成修复建议
function generateFixSuggestions() {
  console.log('\n📋 修复建议');
  console.log('='.repeat(50));
  
  console.log('🔧 针对静态vs动态数值相同问题:');
  console.log('1. 检查DynamicInteractionEngine是否正确初始化');
  console.log('2. 验证四柱八字是否形成了有效的交互关系');
  console.log('3. 检查动态调整系数是否设置合理');
  console.log('4. 确认数据传递链路中动态结果没有被覆盖');
  console.log('5. 添加调试日志查看动态调整的具体过程');
  
  console.log('\n🔧 针对个性化建议只有置信度问题:');
  console.log('1. 检查generatePersonalizedRecommendations函数的返回值');
  console.log('2. 验证前端WXML中的数据绑定路径');
  console.log('3. 确认建议生成的各个子函数是否正常工作');
  console.log('4. 检查数据传递过程中建议内容是否丢失');
  console.log('5. 添加调试输出查看建议生成的详细过程');
  
  console.log('\n🎯 立即可执行的检查步骤:');
  console.log('1. 在微信开发者工具控制台查看完整的数据结构');
  console.log('2. 检查console.log输出中的动态调整日志');
  console.log('3. 验证wuxingRecommendations数组的长度和内容');
  console.log('4. 确认staticDynamicComparison数组中的change值');
  
  return {
    staticDynamicFixes: [
      '检查动态交互引擎初始化',
      '验证交互关系检测',
      '确认调整系数设置',
      '检查数据传递链路'
    ],
    adviceFixes: [
      '检查建议生成函数',
      '验证前端数据绑定',
      '确认建议内容生成',
      '检查数据结构匹配'
    ]
  };
}

// 生成测试用例
function generateTestCases() {
  console.log('\n📋 测试用例');
  console.log('='.repeat(50));
  
  console.log('🧪 测试用例1：有明显交互关系的八字');
  console.log('四柱: 甲申 丙子 戊辰 甲寅');
  console.log('预期: 申子辰合水局，水力量应该显著增强');
  console.log('检查: 静态vs动态对比中水的数值应该有明显差异');
  
  console.log('\n🧪 测试用例2：有冲突关系的八字');
  console.log('四柱: 庚午 戊子 甲寅 丙申');
  console.log('预期: 子午冲，水火力量应该削弱');
  console.log('检查: 静态vs动态对比中水火的数值应该减少');
  
  console.log('\n🧪 测试用例3：平衡八字');
  console.log('四柱: 甲寅 丙午 戊申 庚子');
  console.log('预期: 无明显交互关系，静态动态数值接近');
  console.log('检查: 这种情况下数值相同是正常的');
  
  return {
    testCase1: { pillars: '甲申 丙子 戊辰 甲寅', expectation: '水力量增强' },
    testCase2: { pillars: '庚午 戊子 甲寅 丙申', expectation: '水火力量削弱' },
    testCase3: { pillars: '甲寅 丙午 戊申 庚子', expectation: '数值接近正常' }
  };
}

// 运行完整诊断
function runCompleteDiagnosis() {
  console.log('🎯 开始完整诊断...\n');
  
  const diagnosis = {
    staticDynamicIssue: diagnoseStaticDynamicIssue(),
    adviceIssue: diagnoseAdviceIssue(),
    fixSuggestions: generateFixSuggestions(),
    testCases: generateTestCases()
  };
  
  console.log('\n📊 诊断总结:');
  console.log('==================');
  
  console.log('✅ 静态vs动态问题诊断完成');
  console.log('✅ 个性化建议问题诊断完成');
  console.log('✅ 修复建议生成完成');
  console.log('✅ 测试用例准备完成');
  
  console.log('\n🚀 下一步行动:');
  console.log('1. 检查当前八字是否形成有效交互关系');
  console.log('2. 验证动态调整算法是否正确执行');
  console.log('3. 确认个性化建议生成函数的输出');
  console.log('4. 修复前端数据绑定问题');
  
  return diagnosis;
}

// 执行诊断
const diagnosisResult = runCompleteDiagnosis();

console.log('\n🎉 诊断完成！');
console.log('请根据上述分析检查相应的代码模块。');

module.exports = {
  diagnoseStaticDynamicIssue,
  diagnoseAdviceIssue,
  generateFixSuggestions,
  generateTestCases,
  runCompleteDiagnosis
};
