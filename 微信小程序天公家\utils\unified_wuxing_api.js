/**
 * 统一五行分析API
 * 整合静态力量计算、动态交互分析、影响评估的完整解决方案
 * 为前端提供一站式五行分析服务
 */

const ProfessionalWuxingEngine = require('./professional_wuxing_engine.js');
const DynamicInteractionEngine = require('./dynamic_interaction_engine.js');
const AdvancedDynamicAdjuster = require('./advanced_dynamic_adjuster.js');
const InteractionImpactEvaluator = require('./interaction_impact_evaluator.js');

class UnifiedWuxingAPI {
  constructor() {
    this.staticEngine = new ProfessionalWuxingEngine();
    this.dynamicEngine = new DynamicInteractionEngine();
    this.adjuster = new AdvancedDynamicAdjuster();
    this.evaluator = new InteractionImpactEvaluator();
    
    console.log('🚀 统一五行分析API初始化完成');
  }

  /**
   * 执行完整的五行分析
   * @param {Array} fourPillars - 四柱数据
   * @returns {Object} 完整的分析结果
   */
  async performCompleteAnalysis(fourPillars) {
    console.log('🔍 开始完整五行分析...');
    console.log('输入四柱:', fourPillars.map(p => p.gan + p.zhi).join(' '));

    try {
      // 1. 静态力量计算
      console.log('\n📊 Step 1: 静态力量计算');
      const staticReport = this.staticEngine.generateDetailedReport(fourPillars);
      const staticPowers = staticReport.results.finalPowers;
      const season = staticReport.inputData.season;

      // 2. 动态交互分析
      console.log('\n🔄 Step 2: 动态交互分析');
      const interactions = this.dynamicEngine.analyzeAllInteractions(fourPillars);

      // 3. 高级动态调整
      console.log('\n⚡ Step 3: 高级动态调整');
      const adjustmentResult = this.adjuster.performAdvancedAdjustment(
        staticPowers, 
        interactions, 
        season
      );

      // 4. 综合影响评估
      console.log('\n📈 Step 4: 综合影响评估');
      const impactReport = this.evaluator.evaluateComprehensiveImpact(
        staticPowers,
        adjustmentResult.adjustedPowers,
        interactions,
        fourPillars
      );

      // 5. 生成统一结果
      console.log('\n🎯 Step 5: 生成统一结果');
      const unifiedResult = this.generateUnifiedResult(
        staticReport, 
        interactions, 
        adjustmentResult, 
        impactReport
      );

      console.log('✅ 完整五行分析完成');
      
      return {
        static: staticReport,
        interactions: interactions,
        dynamic: adjustmentResult,
        impact: impactReport,
        unified: unifiedResult,
        timestamp: new Date().toISOString(),
        analysisId: this.generateAnalysisId(fourPillars)
      };

    } catch (error) {
      console.error('❌ 完整五行分析失败:', error);
      throw new Error(`五行分析失败: ${error.message}`);
    }
  }

  /**
   * 生成统一的前端结果格式
   */
  generateUnifiedResult(staticReport, interactions, adjustmentResult, impactReport) {
    const staticPowers = staticReport.results.finalPowers;
    const dynamicPowers = adjustmentResult.adjustedPowers;
    
    // 转换为前端兼容格式
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    
    // 基础五行数据 (保持向后兼容)
    const basicElements = {};
    Object.entries(staticPowers).forEach(([chineseElement, power]) => {
      const englishElement = elementMap[chineseElement];
      basicElements[englishElement] = Math.round(power * 10) / 10;
    });

    // 静态vs动态对比数据
    const elementComparisons = [];
    Object.entries(staticPowers).forEach(([chineseElement, staticValue]) => {
      const englishElement = elementMap[chineseElement];
      const dynamicValue = dynamicPowers[chineseElement];
      const change = dynamicValue - staticValue;
      const changePercent = staticValue > 0 ? (change / staticValue * 100) : 0;
      
      elementComparisons.push({
        name: chineseElement,
        element: englishElement,
        staticPower: Math.round(staticValue * 10) / 10,
        dynamicPower: Math.round(dynamicValue * 10) / 10,
        staticValue: Math.round(staticValue * 10) / 10, // 保持向后兼容
        dynamicValue: Math.round(dynamicValue * 10) / 10, // 保持向后兼容
        staticPercent: this.calculatePercent(staticValue, staticPowers),
        dynamicPercent: this.calculatePercent(dynamicValue, dynamicPowers),
        change: Math.round(change * 10) / 10,
        changePercent: Math.round(changePercent * 10) / 10,
        changePercentText: Math.round(changePercent * 10) / 10 + '%',
        changeDirection: change > 0 ? '↗️' : change < 0 ? '↘️' : '➡️',
        changeLevel: this.getChangeLevel(changePercent, change)
      });
    });

    // 交互关系数据
    const interactionSummary = this.summarizeInteractions(interactions);

    // 影响评估数据
    const impactSummary = this.summarizeImpactEvaluation(impactReport);

    return {
      // 基础兼容数据
      wood: basicElements.wood,
      fire: basicElements.fire,
      earth: basicElements.earth,
      metal: basicElements.metal,
      water: basicElements.water,

      // 专业级扩展数据
      professionalData: {
        algorithm: '专业级五行动态交互分析系统',
        version: 'V2.0 - 完整版',
        isProfessional: true,
        
        // 静态分析数据
        staticAnalysis: {
          powers: staticPowers,
          statistics: staticReport.results.statistics,
          season: staticReport.inputData.season,
          monthBranch: staticReport.inputData.monthBranch
        },

        // 动态交互数据
        dynamicAnalysis: {
          interactions: interactionSummary,
          adjustedPowers: dynamicPowers,
          adjustmentSummary: adjustmentResult.summary,
          balanceCheck: adjustmentResult.balanceCheck
        },

        // 影响评估数据
        impactEvaluation: impactSummary,

        // 前端展示数据
        frontendData: {
          elementComparisons: elementComparisons,
          interactionDetails: this.formatInteractionsForFrontend(interactions),
          recommendations: this.generatePersonalizedRecommendations(staticPowers, dynamicPowers, impactReport, interactions),
          confidence: impactReport.confidence
        }
      }
    };
  }

  /**
   * 计算元素百分比
   */
  calculatePercent(value, allPowers) {
    const total = Object.values(allPowers).reduce((sum, val) => sum + val, 0);
    return total > 0 ? Math.round((value / total) * 100) : 0;
  }

  /**
   * 获取变化等级
   */
  getChangeLevel(changePercent, change) {
    const absPercent = Math.abs(changePercent);
    const isIncrease = change > 0;

    if (absPercent >= 50) {
      return isIncrease ? 'major-increase' : 'major-decrease';
    }
    if (absPercent >= 15) {
      return isIncrease ? 'minor-increase' : 'minor-decrease';
    }
    return 'stable';
  }

  /**
   * 总结交互关系
   */
  summarizeInteractions(interactions) {
    const summary = {
      totalCount: 0,
      categories: {},
      strongestType: null,
      maxStrength: 0
    };

    Object.entries(interactions).forEach(([type, typeInteractions]) => {
      if (typeInteractions && typeInteractions.length > 0) {
        const count = typeInteractions.length;
        summary.totalCount += count;
        summary.categories[type] = {
          count: count,
          interactions: typeInteractions,
          strength: this.calculateInteractionStrength(type, count)
        };

        if (summary.categories[type].strength > summary.maxStrength) {
          summary.maxStrength = summary.categories[type].strength;
          summary.strongestType = type;
        }
      }
    });

    return summary;
  }

  /**
   * 计算交互强度
   */
  calculateInteractionStrength(type, count) {
    const weights = {
      threeDirectional: 1.0,
      threeHarmony: 0.8,
      sixCombination: 0.6,
      fiveCombination: 0.5,
      sixClashes: 0.7,
      threePunishments: 0.6
    };
    return (weights[type] || 0.5) * count;
  }

  /**
   * 总结影响评估
   */
  summarizeImpactEvaluation(impactReport) {
    return {
      overallLevel: impactReport.summary.overallImpactLevel,
      overallLevelText: impactReport.summary.overallImpactLevel,
      overallPercent: this.getImpactPercent(impactReport.summary.overallImpactLevel),

      daymasterDirection: impactReport.summary.daymasterDirection,
      daymasterDirectionText: impactReport.summary.daymasterDirection,
      daymasterChange: impactReport.detailedAnalysis.daymasterImpact.strengthChange.toFixed(1),
      
      fortuneTrend: impactReport.summary.fortuneTrend,
      fortuneTrendText: this.convertFortuneTrendText(impactReport.summary.fortuneTrend),
      
      balanceImprovement: impactReport.summary.balanceImprovement,
      interactionStrength: impactReport.summary.interactionStrength,
      patternStability: impactReport.summary.patternStability,
      
      confidence: Math.round(impactReport.confidence * 100)
    };
  }

  /**
   * 获取影响等级百分比
   */
  getImpactPercent(level) {
    const levelMap = {
      '微弱影响': 20,
      '轻微影响': 40,
      '中等影响': 60,
      '强影响': 80,
      '极强影响': 100
    };
    return levelMap[level] || 50;
  }

  /**
   * 转换吉凶趋势文本
   */
  convertFortuneTrendText(fortuneTrend) {
    if (typeof fortuneTrend === 'number') {
      if (fortuneTrend > 0) return '吉';
      if (fortuneTrend < 0) return '凶';
      return '平';
    }

    // 如果已经是文本，进行标准化
    const trendText = String(fortuneTrend);
    if (trendText.includes('吉利') || trendText.includes('有利')) return '吉';
    if (trendText.includes('不利') || trendText.includes('凶')) return '凶';
    if (trendText.includes('中性') || trendText.includes('平')) return '平';

    return '平'; // 默认值
  }

  /**
   * 格式化交互关系供前端使用
   */
  formatInteractionsForFrontend(interactions) {
    const formatted = {
      combinations: [],
      conflicts: [],
      all: []
    };

    Object.entries(interactions).forEach(([type, typeInteractions]) => {
      if (typeInteractions && typeInteractions.length > 0) {
        typeInteractions.forEach(interaction => {
          const formattedInteraction = {
            type: type,
            description: interaction.description,
            effect: interaction.effect || this.getInteractionEffect(type),
            strengthLevel: this.getInteractionStrengthLevel(type),
            category: this.getInteractionCategory(type),
            icon: this.getInteractionIcon(type)
          };

          formatted.all.push(formattedInteraction);
          
          if (type.includes('Combination') || type.includes('Harmony') || type.includes('Directional')) {
            formatted.combinations.push(formattedInteraction);
          } else {
            formatted.conflicts.push(formattedInteraction);
          }
        });
      }
    });

    return formatted;
  }

  /**
   * 获取交互效果描述
   */
  getInteractionEffect(type) {
    const effectMap = {
      threeDirectional: '大幅增强对应五行力量',
      threeHarmony: '显著增强对应五行力量',
      sixCombination: '适度增强相关五行',
      fiveCombination: '轻微调整五行平衡',
      sixClashes: '削弱冲突双方力量',
      threePunishments: '减弱相关五行力量'
    };
    return effectMap[type] || '影响五行力量分布';
  }

  /**
   * 获取交互强度等级
   */
  getInteractionStrengthLevel(type) {
    const strengthMap = {
      threeDirectional: 'high',
      threeHarmony: 'high',
      sixCombination: 'medium',
      fiveCombination: 'medium',
      sixClashes: 'high',
      threePunishments: 'medium'
    };
    return strengthMap[type] || 'low';
  }

  /**
   * 获取交互分类
   */
  getInteractionCategory(type) {
    if (type.includes('Combination') || type.includes('Harmony') || type.includes('Directional')) {
      return 'positive';
    } else {
      return 'negative';
    }
  }

  /**
   * 获取交互图标
   */
  getInteractionIcon(type) {
    const iconMap = {
      threeDirectional: '🌟',
      threeHarmony: '🤝',
      sixCombination: '💫',
      fiveCombination: '🔗',
      sixClashes: '⚡',
      threePunishments: '⚠️'
    };
    return iconMap[type] || '🔄';
  }

  /**
   * 生成个性化建议
   */
  generatePersonalizedRecommendations(staticPowers, dynamicPowers, impactReport, interactions) {
    const recommendations = [];

    // 1. 基于五行力量变化的建议
    const elementChanges = this.analyzeElementChanges(staticPowers, dynamicPowers);
    recommendations.push(...this.generateElementBasedRecommendations(elementChanges));

    // 2. 基于交互关系的建议
    recommendations.push(...this.generateInteractionBasedRecommendations(interactions));

    // 3. 基于影响评估的建议
    recommendations.push(...this.generateImpactBasedRecommendations(impactReport));

    // 4. 基于日主强弱的建议
    recommendations.push(...this.generateDaymasterBasedRecommendations(impactReport));

    // 5. 如果没有建议，生成基础建议
    if (recommendations.length === 0) {
      recommendations.push(...this.generateBasicRecommendations(staticPowers, impactReport));
    }

    // 按置信度排序并限制数量
    const sortedRecommendations = recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 6); // 最多6个建议

    // 🔧 修复：转换为前端期望的数据结构
    return {
      primary: sortedRecommendations.length > 0 ? sortedRecommendations[0].content || sortedRecommendations[0].title || '暂无主要建议' : '暂无主要建议',
      secondary: sortedRecommendations.slice(1).map(rec => rec.content || rec.title || '暂无建议'),
      confidence: sortedRecommendations.length > 0 ? Math.round(sortedRecommendations[0].confidence * 100) : 90,
      all: sortedRecommendations // 保留完整数据供调试使用
    };
  }

  /**
   * 分析五行力量变化
   */
  analyzeElementChanges(staticPowers, dynamicPowers) {
    const changes = {};
    Object.keys(staticPowers).forEach(element => {
      const staticValue = staticPowers[element];
      const dynamicValue = dynamicPowers[element];
      const change = dynamicValue - staticValue;
      const changePercent = staticValue > 0 ? (change / staticValue * 100) : 0;

      changes[element] = {
        static: staticValue,
        dynamic: dynamicValue,
        change: change,
        changePercent: changePercent,
        level: this.getChangeLevel(changePercent, change)
      };
    });
    return changes;
  }

  /**
   * 基于五行变化生成建议
   */
  generateElementBasedRecommendations(elementChanges) {
    const recommendations = [];

    Object.entries(elementChanges).forEach(([element, data]) => {
      if (data.level === 'major-increase' || data.level === 'minor-increase') {
        // 力量增强
        recommendations.push({
          type: 'element_enhancement',
          title: `${element}力量显著增强`,
          content: `您的${element}力量在动态交互中得到显著提升，建议充分发挥${element}的特质，在相关领域积极行动。`,
          confidence: 85,
          priority: 'high',
          icon: '📈'
        });
      } else if (data.level === 'major-decrease' || data.level === 'minor-decrease') {
        // 力量减弱
        recommendations.push({
          type: 'element_weakness',
          title: `注意${element}力量减弱`,
          content: `您的${element}力量在动态交互中有所减弱，建议通过相应的调理方法来补强${element}的能量。`,
          confidence: 80,
          priority: 'medium',
          icon: '⚠️'
        });
      }
    });

    return recommendations;
  }

  /**
   * 基于交互关系生成建议
   */
  generateInteractionBasedRecommendations(interactions) {
    const recommendations = [];

    // 检查合局
    if (interactions.threeHarmony && interactions.threeHarmony.length > 0) {
      recommendations.push({
        type: 'harmony_advantage',
        title: '三合局带来机遇',
        content: '您的四柱中存在三合局，这是非常吉利的配置，建议在相关时机把握机会，顺势而为。',
        confidence: 90,
        priority: 'high',
        icon: '🤝'
      });
    }

    // 检查冲突
    if (interactions.sixClashes && interactions.sixClashes.length > 0) {
      recommendations.push({
        type: 'clash_caution',
        title: '注意六冲影响',
        content: '您的四柱中存在六冲关系，建议在相关时期保持谨慎，避免冲动决策，以和为贵。',
        confidence: 75,
        priority: 'medium',
        icon: '⚡'
      });
    }

    // 检查刑害
    if (interactions.threePunishments && interactions.threePunishments.length > 0) {
      recommendations.push({
        type: 'punishment_awareness',
        title: '化解三刑影响',
        content: '您的四柱中存在三刑关系，建议通过修身养性、积德行善来化解不利影响。',
        confidence: 70,
        priority: 'medium',
        icon: '⚠️'
      });
    }

    return recommendations;
  }

  /**
   * 基于影响评估生成建议
   */
  generateImpactBasedRecommendations(impactReport) {
    const recommendations = [];

    // 基于整体影响等级
    if (impactReport.summary.overallImpactLevel === '强影响') {
      recommendations.push({
        type: 'strong_impact',
        title: '重视动态变化',
        content: '您的四柱动态交互影响较强，建议密切关注环境变化，适时调整策略以应对变化。',
        confidence: 85,
        priority: 'high',
        icon: '🎯'
      });
    }

    // 基于吉凶趋势
    const fortuneTrend = impactReport.summary.fortuneTrend;
    if (fortuneTrend < 0) {
      recommendations.push({
        type: 'fortune_improvement',
        title: '改善运势建议',
        content: '当前动态交互趋势偏向不利，建议通过调整心态、改善环境来提升整体运势。',
        confidence: 75,
        priority: 'high',
        icon: '🔄'
      });
    } else if (fortuneTrend > 0) {
      recommendations.push({
        type: 'fortune_enhancement',
        title: '把握良好运势',
        content: '当前动态交互趋势较为有利，建议积极行动，把握机遇，顺势发展。',
        confidence: 80,
        priority: 'medium',
        icon: '✨'
      });
    }

    return recommendations;
  }

  /**
   * 基于日主强弱生成建议
   */
  generateDaymasterBasedRecommendations(impactReport) {
    const recommendations = [];

    const daymasterDirection = impactReport.summary.daymasterDirection;

    if (daymasterDirection === '负面影响') {
      recommendations.push({
        type: 'daymaster_support',
        title: '加强日主力量',
        content: '动态交互对您的日主产生负面影响，建议通过相应的五行调理来增强日主力量。',
        confidence: 80,
        priority: 'high',
        icon: '💪'
      });
    } else if (daymasterDirection === '正面影响') {
      recommendations.push({
        type: 'daymaster_utilization',
        title: '发挥日主优势',
        content: '动态交互对您的日主产生正面影响，建议充分发挥个人优势，积极进取。',
        confidence: 85,
        priority: 'medium',
        icon: '🌟'
      });
    }

    return recommendations;
  }

  /**
   * 生成基础建议（当没有其他建议时）
   */
  generateBasicRecommendations(staticPowers, impactReport) {
    const recommendations = [];

    // 找出最强和最弱的五行
    const powerEntries = Object.entries(staticPowers);
    const strongest = powerEntries.reduce((max, current) => current[1] > max[1] ? current : max);
    const weakest = powerEntries.reduce((min, current) => current[1] < min[1] ? current : min);

    // 基于最强五行的建议
    if (strongest[1] > 0) {
      recommendations.push({
        type: 'strength_utilization',
        title: `发挥${strongest[0]}优势`,
        content: `您的${strongest[0]}力量相对较强，建议在相关领域发挥优势，积极进取。`,
        confidence: 65,
        priority: 'medium',
        icon: '💪'
      });
    }

    // 基于最弱五行的建议
    if (weakest[1] >= 0 && strongest[0] !== weakest[0]) {
      recommendations.push({
        type: 'weakness_improvement',
        title: `补强${weakest[0]}不足`,
        content: `您的${weakest[0]}力量相对较弱，建议通过相应方法来增强${weakest[0]}的能量。`,
        confidence: 60,
        priority: 'medium',
        icon: '🔧'
      });
    }

    // 基于整体平衡的建议
    const totalPower = powerEntries.reduce((sum, [_, power]) => sum + power, 0);
    const averagePower = totalPower / powerEntries.length;
    const variance = powerEntries.reduce((sum, [_, power]) => sum + Math.pow(power - averagePower, 2), 0) / powerEntries.length;

    if (variance < 10) {
      recommendations.push({
        type: 'balance_maintenance',
        title: '保持五行平衡',
        content: '您的五行分布相对均衡，建议保持现状，注重全面发展。',
        confidence: 70,
        priority: 'medium',
        icon: '⚖️'
      });
    }

    return recommendations;
  }

  /**
   * 生成分析ID
   */
  generateAnalysisId(fourPillars) {
    const pillarsStr = fourPillars.map(p => p.gan + p.zhi).join('');
    const timestamp = Date.now().toString(36);
    return `wuxing_${pillarsStr}_${timestamp}`;
  }

  /**
   * 快速五行分析 (简化版)
   */
  async performQuickAnalysis(fourPillars) {
    console.log('⚡ 执行快速五行分析...');
    
    try {
      // 只执行静态计算和基础交互分析
      const staticReport = this.staticEngine.generateDetailedReport(fourPillars);
      const interactions = this.dynamicEngine.analyzeAllInteractions(fourPillars);
      
      return {
        static: staticReport,
        interactions: interactions,
        unified: this.generateQuickResult(staticReport, interactions),
        isQuick: true
      };
    } catch (error) {
      console.error('❌ 快速分析失败:', error);
      throw error;
    }
  }

  /**
   * 生成快速分析结果
   */
  generateQuickResult(staticReport, interactions) {
    const staticPowers = staticReport.results.finalPowers;
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    
    const basicElements = {};
    Object.entries(staticPowers).forEach(([chineseElement, power]) => {
      const englishElement = elementMap[chineseElement];
      basicElements[englishElement] = Math.round(power * 10) / 10;
    });

    return {
      ...basicElements,
      professionalData: {
        algorithm: '专业级五行静态分析',
        version: 'V2.0 - 快速版',
        isProfessional: true,
        isQuick: true,
        staticAnalysis: {
          powers: staticPowers,
          statistics: staticReport.results.statistics
        },
        interactionSummary: this.summarizeInteractions(interactions)
      }
    };
  }
}

module.exports = UnifiedWuxingAPI;
