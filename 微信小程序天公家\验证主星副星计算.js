/**
 * 验证主星副星计算
 * 测试用例：庚子 癸未 丙子 乙未
 * 分析当前的十神计算是否正确，以及主星副星的概念区分
 */

console.log('🔍 验证主星副星计算');
console.log('='.repeat(50));
console.log('');

// 测试用例四柱
const testFourPillars = [
  { gan: '庚', zhi: '子' },  // 年柱
  { gan: '癸', zhi: '未' },  // 月柱
  { gan: '丙', zhi: '子' },  // 日柱
  { gan: '乙', zhi: '未' }   // 时柱
];

const dayGan = '丙'; // 日干

console.log('📋 测试四柱：');
testFourPillars.forEach((pillar, index) => {
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  console.log(`${pillarNames[index]}：${pillar.gan}${pillar.zhi}`);
});
console.log(`日干：${dayGan}`);
console.log('');

// 十神映射表（以日干丙为基准）
const shishenMap = {
  '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
  '乙': { '甲': '劫财', '乙': '比肩', '丙': '伤官', '丁': '食神', '戊': '正财', '己': '偏财', '庚': '正官', '辛': '七杀', '壬': '正印', '癸': '偏印' },
  '丙': { '甲': '偏印', '乙': '正印', '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官' },
  '丁': { '甲': '正印', '乙': '偏印', '丙': '劫财', '丁': '比肩', '戊': '伤官', '己': '食神', '庚': '正财', '辛': '偏财', '壬': '正官', '癸': '七杀' },
  '戊': { '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印', '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财' },
  '己': { '甲': '正官', '乙': '七杀', '丙': '正印', '丁': '偏印', '戊': '劫财', '己': '比肩', '庚': '伤官', '辛': '食神', '壬': '正财', '癸': '偏财' },
  '庚': { '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印', '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官' },
  '辛': { '甲': '正财', '乙': '偏财', '丙': '正官', '丁': '七杀', '戊': '正印', '己': '偏印', '庚': '劫财', '辛': '比肩', '壬': '伤官', '癸': '食神' },
  '壬': { '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印', '壬': '比肩', '癸': '劫财' },
  '癸': { '甲': '伤官', '乙': '食神', '丙': '正财', '丁': '偏财', '戊': '正官', '己': '七杀', '庚': '正印', '辛': '偏印', '壬': '劫财', '癸': '比肩' }
};

// 计算十神
function calculateShishen(dayGan, gans) {
  const dayGanMap = shishenMap[dayGan] || {};
  return {
    year: dayGanMap[gans[0]] || '未知',
    month: dayGanMap[gans[1]] || '未知',
    day: '日主',
    hour: dayGanMap[gans[3]] || '未知'
  };
}

const gans = testFourPillars.map(pillar => pillar.gan);
const shishenResult = calculateShishen(dayGan, gans);

console.log('🎭 十神计算结果：');
console.log('='.repeat(20));
console.log(`年柱天干 ${gans[0]}：${shishenResult.year}`);
console.log(`月柱天干 ${gans[1]}：${shishenResult.month}`);
console.log(`日柱天干 ${gans[2]}：${shishenResult.day}`);
console.log(`时柱天干 ${gans[3]}：${shishenResult.hour}`);
console.log('');

// 地支藏干表
const cangganTable = {
  '子': { main_qi: '癸', hidden_gan: ['癸'] },
  '丑': { main_qi: '己', hidden_gan: ['己', '癸', '辛'] },
  '寅': { main_qi: '甲', hidden_gan: ['甲', '丙', '戊'] },
  '卯': { main_qi: '乙', hidden_gan: ['乙'] },
  '辰': { main_qi: '戊', hidden_gan: ['戊', '乙', '癸'] },
  '巳': { main_qi: '丙', hidden_gan: ['丙', '戊', '庚'] },
  '午': { main_qi: '丁', hidden_gan: ['丁', '己'] },
  '未': { main_qi: '己', hidden_gan: ['己', '丁', '乙'] },
  '申': { main_qi: '庚', hidden_gan: ['庚', '壬', '戊'] },
  '酉': { main_qi: '辛', hidden_gan: ['辛'] },
  '戌': { main_qi: '戊', hidden_gan: ['戊', '辛', '丁'] },
  '亥': { main_qi: '壬', hidden_gan: ['壬', '甲'] }
};

console.log('🌿 地支藏干十神分析：');
console.log('='.repeat(25));

const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
testFourPillars.forEach((pillar, index) => {
  const cangganInfo = cangganTable[pillar.zhi];
  if (cangganInfo) {
    const tenGods = cangganInfo.hidden_gan.map(gan => shishenMap[dayGan][gan] || '未知');
    console.log(`${pillarNames[index]} ${pillar.gan}${pillar.zhi}：`);
    console.log(`  地支主气：${cangganInfo.main_qi} (${shishenMap[dayGan][cangganInfo.main_qi]})`);
    console.log(`  藏干：${cangganInfo.hidden_gan.join(', ')}`);
    console.log(`  藏干十神：${tenGods.join(', ')}`);
    console.log('');
  }
});

console.log('🔍 主星副星概念分析：');
console.log('='.repeat(25));

console.log('传统命理学中的主星副星概念：');
console.log('1. 主星（天干十神）：');
console.log('   - 年柱天干十神：主祖上、父母宫');
console.log('   - 月柱天干十神：主兄弟、事业宫');
console.log('   - 日柱天干：日主本身，不算十神');
console.log('   - 时柱天干十神：主子女、晚年宫');

console.log('\n2. 副星（地支藏干十神）：');
console.log('   - 年柱地支藏干十神：辅助年柱分析');
console.log('   - 月柱地支藏干十神：辅助月柱分析');
console.log('   - 日柱地支藏干十神：辅助日柱分析');
console.log('   - 时柱地支藏干十神：辅助时柱分析');

console.log('\n📊 当前系统分析：');
console.log('='.repeat(20));

console.log('当前系统的问题：');
console.log('❌ 1. 概念混淆：将天干十神称为"副星"');
console.log('❌ 2. 缺少真正的主星概念');
console.log('❌ 3. 地支藏干十神没有被称为副星');

console.log('\n正确的主星副星应该是：');
console.log('✅ 主星：天干十神（年月时三柱的天干与日干的关系）');
console.log('✅ 副星：地支藏干十神（各柱地支藏干与日干的关系）');

console.log('\n🎯 建议的修改方案：');
console.log('='.repeat(20));

console.log('1. 将当前的"十神"行改为"主星"行');
console.log('2. 将当前的"副星分析"模块改为显示地支藏干十神');
console.log('3. 保持藏干十神行不变（这个是正确的副星）');
console.log('4. 将"星运分析"改为"长生十二宫"');

console.log('\n📋 具体实现建议：');
console.log('='.repeat(20));

console.log('前端显示结构：');
console.log('- 四柱八字分析');
console.log('  ├── 主星（天干十神）：年月时三柱天干十神');
console.log('  ├── 副星（地支藏干十神）：各柱地支藏干十神');
console.log('  ├── 藏干、藏干十神、藏干强度：详细藏干信息');
console.log('  └── 长生十二宫：星运分析');

console.log('\n🔧 验证当前计算准确性：');
console.log('='.repeat(25));

// 验证十神计算准确性
const expectedResults = {
  year: '偏财',  // 庚对丙
  month: '正官', // 癸对丙
  day: '日主',   // 丙对丙
  hour: '正印'   // 乙对丙
};

console.log('期望结果 vs 实际结果：');
Object.keys(expectedResults).forEach(key => {
  const expected = expectedResults[key];
  const actual = shishenResult[key];
  const isCorrect = expected === actual;
  console.log(`${key}柱：期望=${expected}，实际=${actual} ${isCorrect ? '✅' : '❌'}`);
});

const accuracy = Object.keys(expectedResults).filter(key => 
  expectedResults[key] === shishenResult[key]
).length / Object.keys(expectedResults).length * 100;

console.log(`\n十神计算准确率：${accuracy}%`);

if (accuracy === 100) {
  console.log('✅ 十神计算完全正确！');
} else {
  console.log('❌ 十神计算有误，需要检查映射表');
}

console.log('\n✅ 验证完成！');
console.log('🎯 建议：按照传统命理学概念重新组织主星副星显示结构');
