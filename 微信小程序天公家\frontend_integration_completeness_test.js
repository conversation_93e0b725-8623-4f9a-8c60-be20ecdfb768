/**
 * 前端集成完整性验证测试
 * 检查大运、小运、流年计算系统的前端集成情况
 */

const fs = require('fs');

console.log('🔍 前端集成完整性验证测试');
console.log('='.repeat(60));

// 1. 检查大运系统集成
function checkDayunIntegration() {
  console.log('\n📋 1. 大运系统集成检查');
  console.log('-'.repeat(40));
  
  try {
    // 检查前端页面文件
    const resultPageJs = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
    const resultPageWxml = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
    
    // 检查专业级大运计算器导入
    const hasProfessionalDayunImport = resultPageJs.includes('ProfessionalDayunCalculator');
    console.log(`✅ 专业级大运计算器导入: ${hasProfessionalDayunImport ? '已导入' : '❌ 未导入'}`);
    
    // 检查专业级大运计算方法
    const hasProfessionalDayunMethod = resultPageJs.includes('calculateProfessionalDayun');
    console.log(`✅ 专业级大运计算方法: ${hasProfessionalDayunMethod ? '已实现' : '❌ 未实现'}`);
    
    // 检查前端显示模块
    const hasProfessionalDayunDisplay = resultPageWxml.includes('专业级大运分析');
    console.log(`✅ 专业级大运显示模块: ${hasProfessionalDayunDisplay ? '已实现' : '❌ 未实现'}`);
    
    // 检查数据绑定
    const hasProfessionalDayunData = resultPageWxml.includes('professionalDayunData');
    console.log(`✅ 专业级大运数据绑定: ${hasProfessionalDayunData ? '已绑定' : '❌ 未绑定'}`);
    
    // 检查简化版大运方法是否已废弃
    const hasDeprecatedDayunMethods = resultPageJs.includes('calculateDayun') && resultPageJs.includes('已废弃');
    console.log(`✅ 简化版大运方法废弃: ${hasDeprecatedDayunMethods ? '已废弃' : '❌ 未废弃'}`);
    
    return {
      professionalImport: hasProfessionalDayunImport,
      professionalMethod: hasProfessionalDayunMethod,
      professionalDisplay: hasProfessionalDayunDisplay,
      professionalData: hasProfessionalDayunData,
      deprecatedMethods: hasDeprecatedDayunMethods
    };
    
  } catch (error) {
    console.error('❌ 大运系统集成检查失败:', error.message);
    return { error: error.message };
  }
}

// 2. 检查小运系统集成
function checkMinorFortuneIntegration() {
  console.log('\n📋 2. 小运系统集成检查');
  console.log('-'.repeat(40));
  
  try {
    const resultPageJs = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
    const resultPageWxml = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
    
    // 检查小运计算器导入
    const hasMinorFortuneImport = resultPageJs.includes('MinorFortuneCalculator');
    console.log(`✅ 小运计算器导入: ${hasMinorFortuneImport ? '已导入' : '❌ 未导入'}`);
    
    // 检查小运计算方法
    const hasMinorFortuneMethod = resultPageJs.includes('calculateMinorFortune');
    console.log(`✅ 小运计算方法: ${hasMinorFortuneMethod ? '已实现' : '❌ 未实现'}`);
    
    // 检查前端显示模块
    const hasMinorFortuneDisplay = resultPageWxml.includes('小运分析');
    console.log(`✅ 小运显示模块: ${hasMinorFortuneDisplay ? '已实现' : '❌ 未实现'}`);
    
    // 检查数据绑定
    const hasMinorFortuneData = resultPageWxml.includes('minorFortuneData');
    console.log(`✅ 小运数据绑定: ${hasMinorFortuneData ? '已绑定' : '❌ 未绑定'}`);
    
    // 检查年龄适用性判断
    const hasAgeValidation = resultPageJs.includes('小运仅适用于1-10岁');
    console.log(`✅ 年龄适用性判断: ${hasAgeValidation ? '已实现' : '❌ 未实现'}`);
    
    return {
      minorFortuneImport: hasMinorFortuneImport,
      minorFortuneMethod: hasMinorFortuneMethod,
      minorFortuneDisplay: hasMinorFortuneDisplay,
      minorFortuneData: hasMinorFortuneData,
      ageValidation: hasAgeValidation
    };
    
  } catch (error) {
    console.error('❌ 小运系统集成检查失败:', error.message);
    return { error: error.message };
  }
}

// 3. 检查流年系统集成
function checkLiunianIntegration() {
  console.log('\n📋 3. 流年系统集成检查');
  console.log('-'.repeat(40));
  
  try {
    const resultPageJs = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
    const resultPageWxml = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
    const inputPageJs = fs.readFileSync('pages/bazi-input/index.js', 'utf8');
    
    // 检查流年计算方法
    const hasLiunianMethod = inputPageJs.includes('calculateLiunian');
    console.log(`✅ 流年计算方法: ${hasLiunianMethod ? '已实现' : '❌ 未实现'}`);
    
    // 检查前端显示模块
    const hasLiunianDisplay = resultPageWxml.includes('流年分析');
    console.log(`✅ 流年显示模块: ${hasLiunianDisplay ? '已实现' : '❌ 未实现'}`);
    
    // 检查是否使用动态数据
    const hasStaticLiunianData = resultPageJs.includes('liunianData: [');
    const hasDynamicLiunianData = resultPageJs.includes('calculateLiunian') || resultPageWxml.includes('{{liunian');
    console.log(`⚠️  流年数据类型: ${hasStaticLiunianData ? '静态硬编码' : '动态计算'}`);
    console.log(`✅ 动态流年数据绑定: ${hasDynamicLiunianData ? '已实现' : '❌ 未实现'}`);
    
    // 检查流年干支计算
    const hasLiunianGanzhiCalculation = inputPageJs.includes('ganIndex = (year - 4) % 10');
    console.log(`✅ 流年干支计算: ${hasLiunianGanzhiCalculation ? '已实现' : '❌ 未实现'}`);
    
    return {
      liunianMethod: hasLiunianMethod,
      liunianDisplay: hasLiunianDisplay,
      staticData: hasStaticLiunianData,
      dynamicData: hasDynamicLiunianData,
      ganzhiCalculation: hasLiunianGanzhiCalculation
    };
    
  } catch (error) {
    console.error('❌ 流年系统集成检查失败:', error.message);
    return { error: error.message };
  }
}

// 4. 检查前端样式和交互
function checkFrontendStyling() {
  console.log('\n📋 4. 前端样式和交互检查');
  console.log('-'.repeat(40));
  
  try {
    const resultPageWxss = fs.readFileSync('pages/bazi-result/index.wxss', 'utf8');
    const resultPageWxml = fs.readFileSync('pages/bazi-result/index.wxml', 'utf8');
    
    // 检查专业级大运样式
    const hasProfessionalDayunStyles = resultPageWxss.includes('professional-dayun-card');
    console.log(`✅ 专业级大运样式: ${hasProfessionalDayunStyles ? '已实现' : '❌ 未实现'}`);
    
    // 检查小运样式
    const hasMinorFortuneStyles = resultPageWxss.includes('minor-fortune-card');
    console.log(`✅ 小运样式: ${hasMinorFortuneStyles ? '已实现' : '❌ 未实现'}`);
    
    // 检查流年样式
    const hasLiunianStyles = resultPageWxss.includes('liunian-card');
    console.log(`✅ 流年样式: ${hasLiunianStyles ? '已实现' : '❌ 未实现'}`);
    
    // 检查交互功能
    const hasTabSwitching = resultPageWxml.includes('bindtap="switchTab"');
    console.log(`✅ 标签页切换: ${hasTabSwitching ? '已实现' : '❌ 未实现'}`);
    
    // 检查响应式设计
    const hasResponsiveDesign = resultPageWxss.includes('flex') && resultPageWxss.includes('grid');
    console.log(`✅ 响应式设计: ${hasResponsiveDesign ? '已实现' : '❌ 未实现'}`);
    
    return {
      professionalDayunStyles: hasProfessionalDayunStyles,
      minorFortuneStyles: hasMinorFortuneStyles,
      liunianStyles: hasLiunianStyles,
      tabSwitching: hasTabSwitching,
      responsiveDesign: hasResponsiveDesign
    };
    
  } catch (error) {
    console.error('❌ 前端样式和交互检查失败:', error.message);
    return { error: error.message };
  }
}

// 5. 检查数据完整性
function checkDataCompleteness() {
  console.log('\n📋 5. 数据完整性检查');
  console.log('-'.repeat(40));
  
  try {
    const resultPageJs = fs.readFileSync('pages/bazi-result/index.js', 'utf8');
    
    // 检查大运数据转换
    const hasDayunDataTransform = resultPageJs.includes('transformDayunDataForFrontend');
    console.log(`✅ 大运数据转换: ${hasDayunDataTransform ? '已实现' : '❌ 未实现'}`);
    
    // 检查错误处理
    const hasErrorHandling = resultPageJs.includes('try {') && resultPageJs.includes('catch (error)');
    console.log(`✅ 错误处理机制: ${hasErrorHandling ? '已实现' : '❌ 未实现'}`);
    
    // 检查降级方案
    const hasFallbackData = resultPageJs.includes('getFallbackDayunData');
    console.log(`✅ 降级方案: ${hasFallbackData ? '已实现' : '❌ 未实现'}`);
    
    // 检查数据验证
    const hasDataValidation = resultPageJs.includes('validateInput') || resultPageJs.includes('isValidAge');
    console.log(`✅ 数据验证: ${hasDataValidation ? '已实现' : '❌ 未实现'}`);
    
    return {
      dataTransform: hasDayunDataTransform,
      errorHandling: hasErrorHandling,
      fallbackData: hasFallbackData,
      dataValidation: hasDataValidation
    };
    
  } catch (error) {
    console.error('❌ 数据完整性检查失败:', error.message);
    return { error: error.message };
  }
}

// 6. 生成完整性报告
function generateCompletenessReport(results) {
  console.log('\n📊 前端集成完整性报告');
  console.log('='.repeat(60));
  
  const dayunResult = results.dayun;
  const minorFortuneResult = results.minorFortune;
  const liunianResult = results.liunian;
  const stylingResult = results.styling;
  const dataResult = results.data;
  
  // 计算完成度
  let totalItems = 0;
  let completedItems = 0;
  
  // 大运系统
  if (dayunResult && !dayunResult.error) {
    totalItems += 5;
    completedItems += Object.values(dayunResult).filter(v => v === true).length;
  }
  
  // 小运系统
  if (minorFortuneResult && !minorFortuneResult.error) {
    totalItems += 5;
    completedItems += Object.values(minorFortuneResult).filter(v => v === true).length;
  }
  
  // 流年系统
  if (liunianResult && !liunianResult.error) {
    totalItems += 5;
    completedItems += Object.values(liunianResult).filter(v => v === true).length;
  }
  
  // 样式和交互
  if (stylingResult && !stylingResult.error) {
    totalItems += 5;
    completedItems += Object.values(stylingResult).filter(v => v === true).length;
  }
  
  // 数据完整性
  if (dataResult && !dataResult.error) {
    totalItems += 4;
    completedItems += Object.values(dataResult).filter(v => v === true).length;
  }
  
  const completionRate = totalItems > 0 ? (completedItems / totalItems * 100).toFixed(1) : 0;
  
  console.log(`📈 总体完成度: ${completionRate}% (${completedItems}/${totalItems})`);
  
  // 详细分析
  console.log('\n🔍 详细分析:');
  console.log(`🌟 大运系统: ${dayunResult && !dayunResult.error ? '✅ 集成完成' : '❌ 需要修复'}`);
  console.log(`👶 小运系统: ${minorFortuneResult && !minorFortuneResult.error ? '✅ 集成完成' : '❌ 需要修复'}`);
  console.log(`📅 流年系统: ${liunianResult && !liunianResult.error ? (liunianResult.staticData ? '⚠️ 使用静态数据' : '✅ 动态计算') : '❌ 需要修复'}`);
  console.log(`🎨 样式交互: ${stylingResult && !stylingResult.error ? '✅ 实现完整' : '❌ 需要完善'}`);
  console.log(`📊 数据处理: ${dataResult && !dataResult.error ? '✅ 机制完善' : '❌ 需要加强'}`);
  
  // 问题和建议
  console.log('\n⚠️ 发现的问题:');
  if (liunianResult && liunianResult.staticData) {
    console.log('1. 流年系统使用静态硬编码数据，建议改为动态计算');
  }
  if (dayunResult && !dayunResult.professionalData) {
    console.log('2. 专业级大运数据绑定可能存在问题');
  }
  if (minorFortuneResult && !minorFortuneResult.minorFortuneData) {
    console.log('3. 小运数据绑定可能存在问题');
  }
  
  console.log('\n🚀 改进建议:');
  console.log('1. 将流年系统改为动态计算，与大运、小运保持一致');
  console.log('2. 完善错误处理和用户反馈机制');
  console.log('3. 优化数据加载性能和用户体验');
  console.log('4. 增加更多交互功能和个性化设置');
  
  return {
    completionRate: parseFloat(completionRate),
    totalItems: totalItems,
    completedItems: completedItems,
    results: results
  };
}

// 运行所有检查
function runAllChecks() {
  console.log('🚀 开始运行前端集成完整性验证...\n');
  
  const results = {
    dayun: checkDayunIntegration(),
    minorFortune: checkMinorFortuneIntegration(),
    liunian: checkLiunianIntegration(),
    styling: checkFrontendStyling(),
    data: checkDataCompleteness()
  };
  
  const report = generateCompletenessReport(results);
  
  return report;
}

// 执行检查
const testResults = runAllChecks();

// 导出测试结果
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testResults;
}
