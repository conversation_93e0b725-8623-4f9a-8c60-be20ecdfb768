/**
 * 深入研究问真八字的命宫身宫算法
 * 尝试找出正确的算法来匹配丁亥和癸未
 */

// 五虎遁表
const wuhuDun = {
  '甲': ['丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁'],
  '己': ['丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁'],
  '乙': ['戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'],
  '庚': ['戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'],
  '丙': ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛'],
  '辛': ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛'],
  '丁': ['壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
  '壬': ['壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
  '戊': ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙'],
  '癸': ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙']
};

// 反推分析：如果命宫是丁亥
function analyzeWenzhenMingGong() {
  console.log('🔍 反推分析：问真八字命宫丁亥');
  console.log('='.repeat(50));
  
  const yearGan = '乙';
  const expectedMingGong = '丁亥';
  const expectedGan = '丁';
  const expectedZhi = '亥';
  
  console.log('已知条件:');
  console.log('年干:', yearGan);
  console.log('月支: 亥 (十月)');
  console.log('时支: 未 (14:00)');
  console.log('期望命宫:', expectedMingGong);
  
  // 检查五虎遁
  console.log('\n五虎遁验证:');
  const ganArray = wuhuDun[yearGan];
  const zhiArray = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const haiIndex = zhiArray.indexOf('亥');
  const calculatedGan = ganArray[haiIndex];
  
  console.log(`乙年亥位天干: ${calculatedGan}`);
  console.log(`期望天干: ${expectedGan}`);
  console.log(`五虎遁匹配: ${calculatedGan === expectedGan ? '✅' : '❌'}`);
  
  if (calculatedGan !== expectedGan) {
    console.log('❌ 五虎遁不匹配，可能使用了不同的起法');
    
    // 尝试其他起法
    console.log('\n尝试其他起法:');
    Object.keys(wuhuDun).forEach(gan => {
      const testGan = wuhuDun[gan][haiIndex];
      if (testGan === expectedGan) {
        console.log(`✅ 如果年干是${gan}，亥位天干是${testGan}`);
      }
    });
  }
  
  // 反推地支：如果天干是丁，可能的地支位置
  console.log('\n反推地支位置:');
  ganArray.forEach((gan, index) => {
    if (gan === expectedGan) {
      console.log(`天干${expectedGan}对应地支: ${zhiArray[index]} (索引${index})`);
    }
  });
}

// 反推分析：如果身宫是癸未
function analyzeWenzhenShenGong() {
  console.log('\n🔍 反推分析：问真八字身宫癸未');
  console.log('='.repeat(50));
  
  const yearGan = '乙';
  const expectedShenGong = '癸未';
  const expectedGan = '癸';
  const expectedZhi = '未';
  
  console.log('已知条件:');
  console.log('年干:', yearGan);
  console.log('月支: 亥 (十月)');
  console.log('时支: 未 (14:00)');
  console.log('期望身宫:', expectedShenGong);
  
  // 检查五虎遁
  console.log('\n五虎遁验证:');
  const ganArray = wuhuDun[yearGan];
  const zhiArray = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const weiIndex = zhiArray.indexOf('未');
  const calculatedGan = ganArray[weiIndex];
  
  console.log(`乙年未位天干: ${calculatedGan}`);
  console.log(`期望天干: ${expectedGan}`);
  console.log(`五虎遁匹配: ${calculatedGan === expectedGan ? '✅' : '❌'}`);
  
  if (calculatedGan === expectedGan) {
    console.log('✅ 五虎遁匹配！身宫地支未是正确的');
  }
}

// 尝试不同的命宫算法
function tryDifferentMingGongAlgorithms() {
  console.log('\n🔍 尝试不同的命宫算法');
  console.log('='.repeat(40));
  
  const monthZhi = '亥';
  const hourZhi = '未';
  const yearGan = '乙';
  
  const zhiToNumber = {
    '子': 1, '丑': 2, '寅': 3, '卯': 4, '辰': 5, '巳': 6,
    '午': 7, '未': 8, '申': 9, '酉': 10, '戌': 11, '亥': 12
  };
  
  const numberToZhi = {
    1: '子', 2: '丑', 3: '寅', 4: '卯', 5: '辰', 6: '巳',
    7: '午', 8: '未', 9: '申', 10: '酉', 11: '戌', 12: '亥'
  };
  
  const monthNumber = zhiToNumber[monthZhi]; // 12
  const hourNumber = zhiToNumber[hourZhi];   // 8
  
  console.log('基础数据:');
  console.log(`月支${monthZhi} = ${monthNumber}`);
  console.log(`时支${hourZhi} = ${hourNumber}`);
  
  // 方法1：我们当前的公式
  const method1 = (14 - monthNumber) + (17 - hourNumber);
  const result1 = ((method1 - 1) % 12) + 1;
  console.log(`\n方法1: (14-${monthNumber})+(17-${hourNumber}) = ${method1} → ${numberToZhi[result1]}`);
  
  // 方法2：可能的变体公式
  const method2 = (13 - monthNumber) + (18 - hourNumber);
  const result2 = ((method2 - 1) % 12) + 1;
  console.log(`方法2: (13-${monthNumber})+(18-${hourNumber}) = ${method2} → ${numberToZhi[result2]}`);
  
  // 方法3：直接相加
  const method3 = monthNumber + hourNumber;
  const result3 = ((method3 - 1) % 12) + 1;
  console.log(`方法3: ${monthNumber}+${hourNumber} = ${method3} → ${numberToZhi[result3]}`);
  
  // 方法4：如果目标是亥(12)，反推公式
  console.log('\n反推分析：如果命宫地支是亥(12)');
  console.log('需要什么公式能得到12？');
  
  // 检查哪个方法能得到亥
  [result1, result2, result3].forEach((result, index) => {
    if (numberToZhi[result] === '亥') {
      console.log(`✅ 方法${index + 1}能得到亥`);
    }
  });
  
  // 方法5：可能问真八字使用了不同的基数
  console.log('\n尝试不同基数:');
  for (let base1 = 12; base1 <= 16; base1++) {
    for (let base2 = 15; base2 <= 19; base2++) {
      const testResult = (base1 - monthNumber) + (base2 - hourNumber);
      const finalResult = ((testResult - 1) % 12) + 1;
      if (numberToZhi[finalResult] === '亥') {
        console.log(`✅ 基数(${base1}-${monthNumber})+(${base2}-${hourNumber})=${testResult} → 亥`);
      }
    }
  }
}

// 尝试不同的身宫算法
function tryDifferentShenGongAlgorithms() {
  console.log('\n🔍 尝试不同的身宫算法');
  console.log('='.repeat(40));
  
  const monthZhi = '亥';
  const hourZhi = '未';
  
  const zhiIndex = {
    '子': 0, '丑': 1, '寅': 2, '卯': 3, '辰': 4, '巳': 5,
    '午': 6, '未': 7, '申': 8, '酉': 9, '戌': 10, '亥': 11
  };
  
  const zhiArray = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  const monthIndex = zhiIndex[monthZhi]; // 11
  const hourIndex = zhiIndex[hourZhi];   // 7
  
  console.log('基础数据:');
  console.log(`月支${monthZhi} = 索引${monthIndex}`);
  console.log(`时支${hourZhi} = 索引${hourIndex}`);
  
  // 方法1：我们当前的公式
  const method1 = (monthIndex + hourIndex) % 12;
  console.log(`\n方法1: (${monthIndex}+${hourIndex})%12 = ${method1} → ${zhiArray[method1]}`);
  
  // 方法2：相减
  const method2 = Math.abs(monthIndex - hourIndex) % 12;
  console.log(`方法2: |${monthIndex}-${hourIndex}|%12 = ${method2} → ${zhiArray[method2]}`);
  
  // 方法3：如果目标是未(7)，检查
  console.log('\n目标是未(索引7)');
  if (method1 === 7) console.log('✅ 方法1匹配');
  if (method2 === 7) console.log('✅ 方法2匹配');
  if (hourIndex === 7) console.log('✅ 直接使用时支');
  
  // 方法4：可能直接使用时支
  console.log(`\n方法4: 直接使用时支 → ${hourZhi}`);
  if (hourZhi === '未') {
    console.log('✅ 如果身宫直接使用时支，则匹配！');
  }
}

// 主分析函数
function researchWenzhenAlgorithms() {
  console.log('🔮 深入研究问真八字的命宫身宫算法');
  console.log('='.repeat(60));
  
  analyzeWenzhenMingGong();
  analyzeWenzhenShenGong();
  tryDifferentMingGongAlgorithms();
  tryDifferentShenGongAlgorithms();
  
  console.log('\n🎯 研究结论:');
  console.log('='.repeat(30));
  console.log('1. 身宫癸未的地支"未"正好是时支，可能身宫算法就是直接使用时支');
  console.log('2. 命宫丁亥需要找到能得到地支"亥"的算法');
  console.log('3. 五虎遁验证：乙年亥位确实是丁，未位确实是癸');
  console.log('4. 需要进一步研究问真八字的具体算法文档');
  
  console.log('\n📝 建议修正方向:');
  console.log('1. 身宫：可能直接使用时支+五虎遁天干');
  console.log('2. 命宫：需要找到能得到月支的算法');
  console.log('3. 或者问真八字使用了完全不同的起法');
}

// 运行研究
researchWenzhenAlgorithms();
