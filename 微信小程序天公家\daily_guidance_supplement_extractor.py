#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日指南补充提取工具
专门针对每日指南模块的230条缺口进行补充
"""

import json
import re
import os
from datetime import datetime
from typing import Dict, List

class DailyGuidanceSupplementExtractor:
    def __init__(self):
        self.rule_id_counter = 800000
        self.target_supplement = 230  # 需要补充的数量
        
        # 每日指南专用提取配置
        self.daily_guidance_config = {
            "三命通会": {
                "file": "《三命通会》完整白话版  .pdf",
                "focus_patterns": [
                    # 神煞择日相关
                    r'[^。]*?[神煞|贵人|凶神|吉神][^。]*?[日|时|月][^。]*?[宜|忌|利|害|吉|凶][^。]*?。',
                    r'[^。]*?[择日|选日|日课|时课][^。]*?[方法|技巧|要领|诀窍][^。]*?。',
                    r'[^。]*?[吉日|凶日|吉时|凶时][^。]*?[如何|怎样|方法][^。]*?[选择|判断][^。]*?。',
                    r'[^。]*?[节气|月令|时令][^。]*?[宜|忌|利|害|吉|凶][^。]*?[事|物|行|为][^。]*?。',
                    r'[^。]*?[天德|月德|天乙|太乙][^。]*?[贵人][^。]*?[日|时][^。]*?[宜|忌][^。]*?。',
                    r'[^。]*?[建除十二神][^。]*?[日|时][^。]*?[宜|忌|吉|凶][^。]*?。'
                ],
                "target": 100
            },
            "五行精纪": {
                "file": "五行精纪.docx",
                "focus_patterns": [
                    # 五行时令择日
                    r'[^。]*?[春夏秋冬|正二三四五六七八九十冬腊月][^。]*?[金木水火土][^。]*?[宜|忌|利|害|吉|凶][^。]*?。',
                    r'[^。]*?[日|时|辰|月|年][^。]*?[宜|忌|可|不可|应|当|须|要][^。]*?[做|行|为|办][^。]*?。',
                    r'[^。]*?[今日|当日|此日|是日|本日][^。]*?[宜|忌|利|害|吉|凶|好|坏][^。]*?。',
                    r'[^。]*?[择日|选日|选时|择时|良辰|吉时|吉日|凶日][^。]*?[宜|忌|可|不可][^。]*?。',
                    r'[^。]*?[旺相休囚死|得令|失令|当令|不当令][^。]*?[时|日|月][^。]*?[宜|忌|利|害][^。]*?。',
                    r'[^。]*?[调候|寒暖|燥湿|温凉|冷热][^。]*?[时|日|月][^。]*?[宜|忌|用|取][^。]*?。',
                    r'[^。]*?[出行|求财|婚嫁|搬迁|开业|签约|投资|学习|治疗|祭祀][^。]*?[宜|忌|吉|凶][^。]*?。',
                    r'[^。]*?[甲乙丙丁戊己庚辛壬癸][^。]*?[日|时][^。]*?[宜|忌|利|害|吉|凶][^。]*?。',
                    r'[^。]*?[子丑寅卯辰巳午未申酉戌亥][^。]*?[日|时][^。]*?[宜|忌|利|害|吉|凶][^。]*?。'
                ],
                "target": 80
            },
            "千里命稿": {
                "file": "千里命稿.txt",
                "focus_patterns": [
                    # 实用择日技法
                    r'[^。]*?[择日|选时|良辰|吉时][^。]*?[技法|方法|诀窍|要领][^。]*?。',
                    r'[^。]*?[日课|时课][^。]*?[吉凶|好坏|利害][^。]*?[判断|分析][^。]*?。',
                    r'[^。]*?[宜|忌][^。]*?[出行|求财|婚嫁|开业|搬迁][^。]*?[日|时][^。]*?。',
                    r'[^。]*?[吉日|凶日|吉时|凶时][^。]*?[选择|判断|确定][^。]*?[方法|技巧][^。]*?。'
                ],
                "target": 30
            },
            "渊海子平": {
                "file": "渊海子平.docx",
                "focus_patterns": [
                    # 子平择日理论
                    r'[^。]*?[日主|日元|日干][^。]*?[宜|忌][^。]*?[时|日|月][^。]*?。',
                    r'[^。]*?[用神|喜神|忌神][^。]*?[时|日|月][^。]*?[宜|忌|利|害][^。]*?。',
                    r'[^。]*?[格局][^。]*?[择日|选时][^。]*?[宜|忌|吉|凶][^。]*?。'
                ],
                "target": 20
            }
        }
        
        # 每日指南关键词
        self.daily_keywords = [
            "宜", "忌", "吉", "凶", "择日", "选时", "良辰", "吉时", "日课", "时课",
            "出行", "求财", "婚嫁", "开业", "搬迁", "签约", "投资", "学习", "治疗",
            "祭祀", "节气", "月令", "时令", "神煞", "贵人", "天德", "月德",
            "春夏秋冬", "金木水火土", "甲乙丙丁戊己庚辛壬癸", "子丑寅卯辰巳午未申酉戌亥"
        ]
    
    def load_book_content(self, book_name: str) -> str:
        """加载古籍内容"""
        if book_name not in self.daily_guidance_config:
            return ""
        
        filename = self.daily_guidance_config[book_name]["file"]
        file_path = os.path.join("古籍资料", filename)
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在: {filename}")
            return ""
        
        try:
            if filename.endswith('.txt'):
                return self._load_txt_file(file_path)
            elif filename.endswith('.docx'):
                return self._load_docx_file(file_path)
            elif filename.endswith('.pdf'):
                return self._load_pdf_file(file_path)
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
            return ""
        
        return ""
    
    def _load_txt_file(self, file_path: str) -> str:
        """加载TXT文件"""
        encodings = ['utf-8', 'gbk', 'gb2312']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    print(f"  ✅ TXT加载成功: {len(content):,} 字符")
                    return content
            except UnicodeDecodeError:
                continue
        return ""
    
    def _load_docx_file(self, file_path: str) -> str:
        """加载DOCX文件"""
        try:
            from docx import Document
            doc = Document(file_path)
            content = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            print(f"  ✅ DOCX加载成功: {len(content):,} 字符")
            return content
        except ImportError:
            print("  需要安装python-docx库")
            return ""
        except Exception:
            return ""
    
    def _load_pdf_file(self, file_path: str) -> str:
        """加载PDF文件"""
        try:
            import PyPDF2
            content_parts = []
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                for i in range(total_pages):
                    try:
                        page_text = pdf_reader.pages[i].extract_text()
                        if page_text and len(page_text.strip()) > 20:
                            cleaned_text = re.sub(r'\s+', ' ', page_text).strip()
                            content_parts.append(cleaned_text)
                    except:
                        continue
                
                content = '\n'.join(content_parts)
                print(f"  ✅ PDF加载成功: {len(content):,} 字符")
                return content
        except ImportError:
            print("  需要安装PyPDF2库")
            return ""
        except Exception:
            return ""
    
    def extract_daily_guidance_rules(self, content: str, book_name: str, config: Dict) -> List[Dict]:
        """提取每日指南规则"""
        if not content:
            return []
        
        patterns = config["focus_patterns"]
        target = config["target"]
        
        all_extracted = []
        
        print(f"  🎯 专门提取每日指南规则 (目标: {target}条)...")
        
        # 1. 专用模式匹配
        for i, pattern in enumerate(patterns):
            try:
                matches = re.findall(pattern, content)
                print(f"    专用模式{i+1}: {len(matches)}条")
                
                for match in matches:
                    cleaned_text = self._clean_daily_text(match)
                    if self._validate_daily_rule(cleaned_text):
                        rule = self._create_daily_rule(cleaned_text, book_name, f"专用模式{i+1}")
                        all_extracted.append(rule)
            except Exception as e:
                continue
        
        # 2. 关键词密集提取
        keyword_rules = self._extract_daily_keywords(content, book_name)
        all_extracted.extend(keyword_rules)
        
        # 3. 上下文扩展提取
        context_rules = self._extract_daily_context(content, book_name)
        all_extracted.extend(context_rules)
        
        # 4. 去重和筛选
        unique_rules = self._deduplicate_daily_rules(all_extracted)
        
        # 5. 按质量排序并限制数量
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        final_rules = unique_rules[:target]
        
        print(f"  ✅ 提取完成: {len(final_rules)}条每日指南规则")
        return final_rules
    
    def _extract_daily_keywords(self, content: str, book_name: str) -> List[Dict]:
        """基于关键词提取每日指南规则"""
        rules = []
        sentences = re.split(r'[。；！？]', content)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if 25 <= len(sentence) <= 400:
                # 检查每日指南关键词
                keyword_count = sum(1 for keyword in self.daily_keywords if keyword in sentence)
                if keyword_count >= 2:  # 至少包含2个相关关键词
                    cleaned_text = self._clean_daily_text(sentence)
                    if self._validate_daily_rule(cleaned_text):
                        rule = self._create_daily_rule(cleaned_text, book_name, "关键词提取")
                        rules.append(rule)
        
        return rules
    
    def _extract_daily_context(self, content: str, book_name: str) -> List[Dict]:
        """上下文扩展提取"""
        rules = []
        
        # 寻找每日指南核心关键词周围的上下文
        core_keywords = ["择日", "选时", "宜", "忌", "吉时", "凶时", "良辰", "日课"]
        
        for keyword in core_keywords:
            for match in re.finditer(keyword, content):
                start = max(0, match.start() - 80)
                end = min(len(content), match.end() + 80)
                context = content[start:end]
                
                # 提取包含关键词的句子
                sentences = re.split(r'[。；]', context)
                for sentence in sentences:
                    if keyword in sentence and 30 <= len(sentence) <= 350:
                        cleaned_text = self._clean_daily_text(sentence)
                        if self._validate_daily_rule(cleaned_text):
                            rule = self._create_daily_rule(cleaned_text, book_name, "上下文扩展")
                            rules.append(rule)
        
        return rules
    
    def _clean_daily_text(self, text: str) -> str:
        """清理每日指南文本"""
        if not text:
            return ""
        
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 移除无关内容
        text = text.replace('注：', '').replace('按：', '')
        text = text.replace('又云：', '').replace('古云：', '')
        
        return text
    
    def _validate_daily_rule(self, text: str) -> bool:
        """验证每日指南规则"""
        if not text or len(text) < 20 or len(text) > 500:
            return False
        
        # 必须包含每日指南相关关键词
        daily_indicators = ["宜", "忌", "吉", "凶", "择日", "选时", "良辰", "吉时", "日课"]
        has_daily_keyword = any(indicator in text for indicator in daily_indicators)
        
        # 必须包含时间相关词汇
        time_indicators = ["日", "时", "月", "年", "辰", "节气", "春夏秋冬"]
        has_time_keyword = any(indicator in text for indicator in time_indicators)
        
        return has_daily_keyword and has_time_keyword
    
    def _create_daily_rule(self, text: str, book_name: str, method: str) -> Dict:
        """创建每日指南规则"""
        # 计算置信度
        confidence = 0.89 + (len(text) / 1000) * 0.03
        confidence = min(0.95, confidence)
        
        rule = {
            "rule_id": f"DAILY_{self.rule_id_counter:06d}",
            "pattern_name": f"《{book_name}》·每日指南补充规则",
            "category": "每日指南",
            "dimension_type": "每日指南",
            "book_source": book_name,
            "extraction_method": method,
            "original_text": text,
            "interpretations": f"出自《{book_name}》的每日指南权威理论，专门补充提取",
            "confidence": confidence,
            "daily_guidance_supplement": True,
            "extracted_at": datetime.now().isoformat(),
            "extraction_phase": "每日指南专项补充",
            "rule_type": "每日指南补充规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _deduplicate_daily_rules(self, rules: List[Dict]) -> List[Dict]:
        """去重每日指南规则"""
        seen_texts = set()
        unique_rules = []
        
        for rule in rules:
            text = rule.get('original_text', '')
            simplified = re.sub(r'[\s\W]', '', text)[:25]  # 检查前25个字符
            
            if simplified not in seen_texts and len(simplified) > 10:
                seen_texts.add(simplified)
                unique_rules.append(rule)
        
        return unique_rules
    
    def execute_daily_supplement(self) -> Dict:
        """执行每日指南补充"""
        print("🚀 开始每日指南专项补充...")
        print(f"🎯 目标补充: {self.target_supplement}条规则")
        
        all_daily_rules = []
        total_extracted = 0
        
        for book_name, config in self.daily_guidance_config.items():
            print(f"\n📚 处理《{book_name}》...")
            
            content = self.load_book_content(book_name)
            if not content:
                continue
            
            rules = self.extract_daily_guidance_rules(content, book_name, config)
            all_daily_rules.extend(rules)
            total_extracted += len(rules)
        
        # 去重和最终筛选
        unique_rules = self._deduplicate_daily_rules(all_daily_rules)
        unique_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        # 限制到目标数量
        final_rules = unique_rules[:self.target_supplement]
        
        result_data = {
            "metadata": {
                "supplement_type": "每日指南专项补充",
                "supplement_date": datetime.now().isoformat(),
                "target_supplement": self.target_supplement,
                "actual_extracted": len(final_rules),
                "completion_rate": f"{len(final_rules)/self.target_supplement*100:.1f}%",
                "books_processed": len(self.daily_guidance_config)
            },
            "supplement_rules": final_rules
        }
        
        return {
            "success": True,
            "data": result_data,
            "summary": {
                "目标补充": self.target_supplement,
                "实际提取": len(final_rules),
                "完成率": f"{len(final_rules)/self.target_supplement*100:.1f}%"
            }
        }

def main():
    """主函数"""
    extractor = DailyGuidanceSupplementExtractor()
    
    result = extractor.execute_daily_supplement()
    
    if result.get("success"):
        output_filename = f"daily_guidance_supplement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        print("\n" + "="*80)
        print("🎉 每日指南专项补充完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print(f"\n✅ 补充结果已保存到: {output_filename}")
        print(f"💡 下一步：补充数字化分析模块")
        
    else:
        print(f"❌ 补充失败")

if __name__ == "__main__":
    main()
