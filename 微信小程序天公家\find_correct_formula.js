/**
 * 找到真正正确的命宫计算公式
 * 基于三个已知案例反推
 */

function findCorrectFormula() {
  console.log('🔍 基于三个已知案例找到正确公式');
  console.log('=' * 60);
  
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  const knownCases = [
    {
      name: '1996年10月13日15:45',
      monthZhi: '戌',
      hourZhi: '申',
      expectedZhi: '亥',
      description: '已验证案例1'
    },
    {
      name: '2013年8月17日11:00',
      monthZhi: '申',
      hourZhi: '午',
      expectedZhi: '亥',
      description: '已验证案例2'
    },
    {
      name: '2015年11月20日14:00',
      monthZhi: '戌', // 假设是戌月
      hourZhi: '未',
      expectedZhi: '亥',
      description: '新案例（假设戌月）'
    }
  ];
  
  console.log('测试案例:');
  knownCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}: ${testCase.monthZhi}月${testCase.hourZhi}时 → ${testCase.expectedZhi}`);
  });
  
  // 测试各种可能的公式
  console.log('\n🔧 测试各种公式:');
  
  for (let base = 1; base <= 30; base++) {
    let allCorrect = true;
    let results = [];
    
    knownCases.forEach(testCase => {
      const monthIndex = zhiOrder.indexOf(testCase.monthZhi);
      const hourIndex = zhiOrder.indexOf(testCase.hourZhi);
      const expectedIndex = zhiOrder.indexOf(testCase.expectedZhi);
      
      let result = (base - monthIndex + hourIndex) % 12;
      if (result < 0) result += 12;
      
      const isCorrect = result === expectedIndex;
      if (!isCorrect) allCorrect = false;
      
      results.push({
        case: testCase.name,
        calculation: `(${base}-${monthIndex}+${hourIndex})%12=${result}`,
        result: zhiOrder[result],
        expected: testCase.expectedZhi,
        correct: isCorrect
      });
    });
    
    if (allCorrect) {
      console.log(`\n✅ 找到正确公式: (${base} - 月支索引 + 时支索引) % 12`);
      results.forEach(r => {
        console.log(`  ${r.case}: ${r.calculation} → ${r.result} ${r.correct ? '✅' : '❌'}`);
      });
    }
  }
  
  // 特别测试2015年案例的月支问题
  console.log('\n🌙 特别分析2015年11月20日的月支:');
  
  const possibleMonths = ['戌', '亥'];
  const hour2015 = '未';
  const expected2015 = '亥';
  
  possibleMonths.forEach(monthZhi => {
    console.log(`\n假设月支是${monthZhi}:`);
    
    const monthIndex = zhiOrder.indexOf(monthZhi);
    const hourIndex = zhiOrder.indexOf(hour2015);
    const expectedIndex = zhiOrder.indexOf(expected2015);
    
    for (let base = 1; base <= 20; base++) {
      let result = (base - monthIndex + hourIndex) % 12;
      if (result < 0) result += 12;
      
      if (result === expectedIndex) {
        console.log(`  ✅ 公式: (${base} - 月支索引 + 时支索引) % 12`);
        console.log(`  验证: (${base} - ${monthIndex} + ${hourIndex}) % 12 = ${result} → ${zhiOrder[result]}`);
        
        // 验证这个公式是否适用于其他案例
        console.log(`  验证其他案例:`);
        
        const otherCases = [
          { month: '戌', hour: '申', expected: '亥', name: '1996案例' },
          { month: '申', hour: '午', expected: '亥', name: '2013案例' }
        ];
        
        let formulaWorks = true;
        otherCases.forEach(otherCase => {
          const mIndex = zhiOrder.indexOf(otherCase.month);
          const hIndex = zhiOrder.indexOf(otherCase.hour);
          const eIndex = zhiOrder.indexOf(otherCase.expected);
          
          let testResult = (base - mIndex + hIndex) % 12;
          if (testResult < 0) testResult += 12;
          
          const works = testResult === eIndex;
          if (!works) formulaWorks = false;
          
          console.log(`    ${otherCase.name}: (${base}-${mIndex}+${hIndex})%12=${testResult} → ${zhiOrder[testResult]} ${works ? '✅' : '❌'}`);
        });
        
        if (formulaWorks) {
          console.log(`  🎯 这个公式适用于所有案例！`);
          console.log(`  结论: 2015年11月20日应该是${monthZhi}月`);
        }
      }
    }
  });
}

// 运行分析
findCorrectFormula();
