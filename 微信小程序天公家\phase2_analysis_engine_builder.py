#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段：分析引擎层建设
构建800条多系统共享的分析引擎规则
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Tuple
from collections import defaultdict

class Phase2AnalysisEngineBuilder:
    def __init__(self):
        # 分析引擎目标配置
        self.engine_targets = {
            "五行力量计算引擎": {
                "target_count": 200,
                "description": "精确计算五行强弱分布的算法规则",
                "keywords": ["五行", "旺相", "休囚", "得分", "力量", "强弱", "分数", "权重"],
                "algorithm_keywords": ["计算", "算法", "公式", "权重", "系数", "得分"],
                "priority": 1
            },
            "规则匹配引擎": {
                "target_count": 300,
                "description": "智能匹配相关规则的算法规则",
                "keywords": ["匹配", "对应", "符合", "适用", "条件", "判断", "识别", "选择"],
                "algorithm_keywords": ["匹配度", "相似度", "权重", "阈值", "算法", "逻辑"],
                "priority": 1
            },
            "古籍依据系统": {
                "target_count": 200,
                "description": "提供古籍理论依据的规则系统",
                "keywords": ["古籍", "依据", "理论", "出处", "来源", "根据", "经典", "典籍"],
                "algorithm_keywords": ["引用", "索引", "关联", "映射", "查找", "检索"],
                "priority": 2
            },
            "神煞分析引擎": {
                "target_count": 100,
                "description": "神煞作用分析的专用引擎",
                "keywords": ["神煞", "贵人", "凶煞", "吉神", "作用", "影响", "效果", "力量"],
                "algorithm_keywords": ["分析", "判断", "评估", "计算", "权重", "等级"],
                "priority": 3
            }
        }
        
        self.rule_id_counter = 2001  # 从第一阶段结束后继续
        
        # 质量标准
        self.quality_standards = {
            "min_confidence": 0.90,  # 引擎层要求更高置信度
            "min_text_length": 50,
            "max_text_length": 800
        }
    
    def load_foundation_rules(self, filename: str = "classical_rules_phase1_final.json") -> List[Dict]:
        """加载第一阶段基础理论规则"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            print(f"✅ 加载基础理论规则: {len(rules)}条")
            return rules, data.get('metadata', {})
            
        except Exception as e:
            print(f"❌ 加载基础理论规则失败: {e}")
            return [], {}
    
    def extract_engine_rules_from_original(self, engine_name: str, engine_config: Dict) -> List[Dict]:
        """从原始规则中提取分析引擎规则"""
        print(f"🔍 从原始规则提取{engine_name}...")
        
        try:
            with open("classical_rules_complete.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            original_rules = data.get('rules', [])
            
            # 加载已使用的规则ID
            used_rule_ids = self._get_used_rule_ids()
            
            candidate_rules = []
            keywords = engine_config["keywords"]
            algorithm_keywords = engine_config["algorithm_keywords"]
            target_count = engine_config["target_count"]
            
            for rule in original_rules:
                rule_id = rule.get('rule_id', '')
                if rule_id in used_rule_ids:
                    continue
                
                text = rule.get('original_text', '')
                confidence = rule.get('confidence', 0)
                
                # 质量筛选
                if (confidence >= self.quality_standards["min_confidence"] and
                    len(text) >= self.quality_standards["min_text_length"] and
                    len(text) <= self.quality_standards["max_text_length"]):
                    
                    # 计算引擎相关性
                    engine_score = self._calculate_engine_relevance(
                        text, keywords, algorithm_keywords
                    )
                    
                    if engine_score >= 2.0:  # 最低相关性阈值
                        rule['engine_relevance_score'] = engine_score
                        candidate_rules.append(rule)
            
            # 按相关性排序并选择
            candidate_rules.sort(key=lambda x: x['engine_relevance_score'], reverse=True)
            selected_rules = candidate_rules[:target_count]
            
            # 增强为引擎规则
            enhanced_rules = []
            for rule in selected_rules:
                enhanced_rule = self._enhance_engine_rule(rule, engine_name, engine_config)
                enhanced_rules.append(enhanced_rule)
            
            print(f"  提取了 {len(enhanced_rules)} 条{engine_name}规则")
            return enhanced_rules
            
        except Exception as e:
            print(f"❌ 提取{engine_name}失败: {e}")
            return []
    
    def generate_algorithmic_rules(self, engine_name: str, engine_config: Dict) -> List[Dict]:
        """生成算法性规则"""
        print(f"⚙️ 生成{engine_name}算法规则...")
        
        algorithmic_rules = []
        
        if engine_name == "五行力量计算引擎":
            algorithmic_rules.extend(self._generate_wuxing_calculation_rules())
        elif engine_name == "规则匹配引擎":
            algorithmic_rules.extend(self._generate_rule_matching_rules())
        elif engine_name == "古籍依据系统":
            algorithmic_rules.extend(self._generate_reference_system_rules())
        elif engine_name == "神煞分析引擎":
            algorithmic_rules.extend(self._generate_shensha_analysis_rules())
        
        print(f"  生成了 {len(algorithmic_rules)} 条算法规则")
        return algorithmic_rules
    
    def _generate_wuxing_calculation_rules(self) -> List[Dict]:
        """生成五行力量计算规则"""
        rules = []
        
        # 基础计算规则
        base_rules = [
            {
                "name": "月令旺衰基础分数",
                "text": "月令当令五行得基础分数100分，相生五行得60分，相克五行得20分，被克五行得10分",
                "algorithm": "月令五行力量计算的基础权重分配"
            },
            {
                "name": "天干透出加权",
                "text": "天干透出对应五行时，该五行力量增加30分，多个透出时累加但有递减系数",
                "algorithm": "天干透出对五行力量的影响计算"
            },
            {
                "name": "地支本气计算",
                "text": "地支本气五行得满分，中气得70%分数，余气得30%分数",
                "algorithm": "地支藏干对五行力量的贡献计算"
            },
            {
                "name": "相邻支撑加成",
                "text": "相邻地支为同类五行时，互相增加20%力量加成",
                "algorithm": "地支相邻效应的力量计算"
            },
            {
                "name": "刑冲减损计算",
                "text": "受到刑冲的五行力量减少30%，冲克严重时减少50%",
                "algorithm": "刑冲对五行力量的负面影响计算"
            }
        ]
        
        for i, rule_data in enumerate(base_rules):
            rule = self._create_algorithmic_rule(
                "五行力量计算引擎", rule_data, f"WX_CALC_{i+1:03d}"
            )
            rules.append(rule)
        
        return rules
    
    def _generate_rule_matching_rules(self) -> List[Dict]:
        """生成规则匹配规则"""
        rules = []
        
        base_rules = [
            {
                "name": "关键词匹配权重",
                "text": "规则匹配时，完全匹配关键词权重1.0，部分匹配权重0.7，相关词匹配权重0.5",
                "algorithm": "关键词匹配的权重分配算法"
            },
            {
                "name": "置信度阈值判断",
                "text": "规则匹配需要置信度≥0.8，高优先级匹配需要置信度≥0.9",
                "algorithm": "基于置信度的规则筛选阈值"
            },
            {
                "name": "上下文相关性",
                "text": "考虑八字整体格局，相关性高的规则匹配权重增加50%",
                "algorithm": "上下文相关性的权重调整"
            },
            {
                "name": "多规则综合评分",
                "text": "多条规则匹配时，取加权平均分，权重基于置信度和相关性",
                "algorithm": "多规则匹配的综合评分算法"
            }
        ]
        
        for i, rule_data in enumerate(base_rules):
            rule = self._create_algorithmic_rule(
                "规则匹配引擎", rule_data, f"MATCH_{i+1:03d}"
            )
            rules.append(rule)
        
        return rules
    
    def _generate_reference_system_rules(self) -> List[Dict]:
        """生成古籍依据系统规则"""
        rules = []
        
        base_rules = [
            {
                "name": "古籍权威性等级",
                "text": "三命通会、渊海子平为一级权威，滴天髓、穷通宝鉴为二级权威，其他为三级",
                "algorithm": "古籍来源的权威性分级系统"
            },
            {
                "name": "理论依据关联",
                "text": "每个分析结果关联对应的古籍理论，提供理论出处和页码引用",
                "algorithm": "分析结果与古籍理论的关联算法"
            },
            {
                "name": "多源验证机制",
                "text": "重要结论需要至少两个不同古籍来源的支撑，增加可信度",
                "algorithm": "多古籍来源的交叉验证机制"
            }
        ]
        
        for i, rule_data in enumerate(base_rules):
            rule = self._create_algorithmic_rule(
                "古籍依据系统", rule_data, f"REF_{i+1:03d}"
            )
            rules.append(rule)
        
        return rules
    
    def _generate_shensha_analysis_rules(self) -> List[Dict]:
        """生成神煞分析规则"""
        rules = []
        
        base_rules = [
            {
                "name": "神煞力量等级",
                "text": "天乙贵人、文昌等吉神为A级，桃花、华盖等为B级，其他为C级",
                "algorithm": "神煞影响力的等级分类系统"
            },
            {
                "name": "神煞作用条件",
                "text": "神煞需要在特定条件下才能发挥作用，如贵人需要有气，桃花需要旺相",
                "algorithm": "神煞生效条件的判断逻辑"
            },
            {
                "name": "神煞组合效应",
                "text": "多个神煞同现时，吉神相助力量倍增，凶煞相冲互相抵消",
                "algorithm": "神煞组合效应的计算方法"
            }
        ]
        
        for i, rule_data in enumerate(base_rules):
            rule = self._create_algorithmic_rule(
                "神煞分析引擎", rule_data, f"SHEN_{i+1:03d}"
            )
            rules.append(rule)
        
        return rules
    
    def _create_algorithmic_rule(self, engine_name: str, rule_data: Dict, rule_suffix: str) -> Dict:
        """创建算法规则"""
        rule = {
            "rule_id": f"ENGINE_{rule_suffix}_{self.rule_id_counter:03d}",
            "pattern_name": rule_data["name"],
            "category": "分析引擎",
            "engine_type": engine_name,
            "original_text": rule_data["text"],
            "interpretations": rule_data["algorithm"],
            "confidence": 0.95,  # 算法规则高置信度
            "conditions": f"{engine_name}的核心算法",
            "algorithmic_rule": True,
            "created_at": datetime.now().isoformat(),
            "extraction_phase": "第二阶段：分析引擎层建设",
            "rule_type": "算法规则"
        }
        
        self.rule_id_counter += 1
        return rule
    
    def _calculate_engine_relevance(self, text: str, keywords: List[str], 
                                   algorithm_keywords: List[str]) -> float:
        """计算引擎相关性分数"""
        score = 0
        
        # 基础关键词匹配
        for keyword in keywords:
            if keyword in text:
                score += 1.0
        
        # 算法关键词匹配（权重更高）
        for keyword in algorithm_keywords:
            if keyword in text:
                score += 1.5
        
        return score
    
    def _enhance_engine_rule(self, rule: Dict, engine_name: str, engine_config: Dict) -> Dict:
        """增强为引擎规则"""
        enhanced_rule = rule.copy()
        
        # 更新规则ID
        engine_prefix = engine_name.replace("引擎", "").replace("系统", "")[:4]
        enhanced_rule["rule_id"] = f"ENGINE_{engine_prefix}_{self.rule_id_counter:03d}"
        
        # 添加引擎标记
        enhanced_rule["engine_type"] = engine_name
        enhanced_rule["engine_description"] = engine_config["description"]
        enhanced_rule["extraction_phase"] = "第二阶段：分析引擎层建设"
        enhanced_rule["enhanced_at"] = datetime.now().isoformat()
        enhanced_rule["rule_type"] = "引擎规则"
        
        # 更新分类
        enhanced_rule["category"] = "分析引擎"
        
        # 优化解释
        original_interpretation = enhanced_rule.get("interpretations", "")
        enhanced_rule["interpretations"] = f"{engine_config['description']}：{original_interpretation}"
        
        self.rule_id_counter += 1
        return enhanced_rule
    
    def _get_used_rule_ids(self) -> set:
        """获取已使用的规则ID"""
        used_ids = set()
        
        try:
            with open("classical_rules_phase1_final.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                for rule in data.get('rules', []):
                    if 'rule_id' in rule:
                        used_ids.add(rule['rule_id'])
        except:
            pass
        
        return used_ids
    
    def execute_phase2_building(self) -> Dict:
        """执行第二阶段建设"""
        print("🚀 启动第二阶段：分析引擎层建设...")
        
        # 加载基础理论规则
        foundation_rules, foundation_metadata = self.load_foundation_rules()
        
        all_engine_rules = []
        engine_summary = {}
        
        # 为每个引擎构建规则
        for engine_name, engine_config in self.engine_targets.items():
            print(f"\n🔧 构建{engine_name}...")
            
            # 从原始规则提取
            extracted_rules = self.extract_engine_rules_from_original(engine_name, engine_config)
            
            # 生成算法规则
            algorithmic_rules = self.generate_algorithmic_rules(engine_name, engine_config)
            
            # 合并引擎规则
            engine_rules = extracted_rules + algorithmic_rules
            
            # 限制数量
            target_count = engine_config["target_count"]
            if len(engine_rules) > target_count:
                # 按质量排序选择最好的
                engine_rules.sort(key=lambda x: x.get('confidence', 0), reverse=True)
                engine_rules = engine_rules[:target_count]
            
            all_engine_rules.extend(engine_rules)
            engine_summary[engine_name] = {
                "target": target_count,
                "extracted": len(extracted_rules),
                "algorithmic": len(algorithmic_rules),
                "final": len(engine_rules)
            }
        
        # 合并所有规则
        total_rules = foundation_rules + all_engine_rules
        
        # 生成第二阶段数据
        phase2_data = {
            "metadata": {
                "phase": "第二阶段：分析引擎层建设",
                "build_date": datetime.now().isoformat(),
                "foundation_count": len(foundation_rules),
                "engine_count": len(all_engine_rules),
                "total_count": len(total_rules),
                "target_achieved": len(all_engine_rules) >= 800,
                "engine_summary": engine_summary,
                "foundation_metadata": foundation_metadata
            },
            "rules": total_rules
        }
        
        return {
            "success": True,
            "data": phase2_data,
            "summary": {
                "基础理论规则": len(foundation_rules),
                "分析引擎规则": len(all_engine_rules),
                "总规则数": len(total_rules),
                "引擎目标": "800条",
                "引擎完成": f"{len(all_engine_rules)}条",
                "完成率": f"{len(all_engine_rules)/800*100:.1f}%"
            }
        }

def main():
    """主函数"""
    builder = Phase2AnalysisEngineBuilder()
    
    # 执行第二阶段建设
    result = builder.execute_phase2_building()
    
    if result.get("success"):
        # 保存第二阶段结果
        output_filename = "classical_rules_phase2_analysis_engine.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result["data"], f, ensure_ascii=False, indent=2)
        
        # 打印结果
        print("\n" + "="*80)
        print("第二阶段：分析引擎层建设完成")
        print("="*80)
        
        summary = result["summary"]
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        # 详细引擎统计
        engine_summary = result["data"]["metadata"]["engine_summary"]
        print(f"\n🔧 引擎建设详情:")
        for engine, stats in engine_summary.items():
            print(f"  {engine}: {stats['final']}/{stats['target']} "
                  f"(提取:{stats['extracted']}, 算法:{stats['algorithmic']})")
        
        print(f"\n✅ 第二阶段数据已保存到: {output_filename}")
        
        # 检查是否达到目标
        engine_count_str = str(summary["分析引擎规则"]).replace("条", "")
        engine_count = int(engine_count_str) if engine_count_str.isdigit() else len(all_engine_rules)
        if engine_count >= 800:
            print(f"🎉 第二阶段目标达成！准备启动第三阶段...")
        else:
            remaining = 800 - int(engine_count)
            print(f"⚠️ 距离目标还差 {remaining} 条规则")
        
    else:
        print(f"❌ 第二阶段建设失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
