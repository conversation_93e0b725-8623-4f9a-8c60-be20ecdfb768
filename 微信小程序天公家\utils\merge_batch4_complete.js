/**
 * 第四批次完整数据库合并工具
 * 将古代补充、近现代、当代三个完整数据库合并为第四批次完整数据库
 */

const ancientSupplementComplete = require('../data/ancient_supplement_complete');
const modernEraComplete = require('../data/modern_era_complete');
const contemporaryEraComplete = require('../data/contemporary_era_complete');

function mergeBatch4CompleteData() {
  console.log('开始合并第四批次完整数据...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...ancientSupplementComplete.celebrities,
    ...modernEraComplete.celebrities,
    ...contemporaryEraComplete.celebrities
  ];

  // 统计时期分布
  const periodStats = {
    "古代补充": ancientSupplementComplete.metadata.totalRecords,
    "近现代": modernEraComplete.metadata.totalRecords,
    "当代": contemporaryEraComplete.metadata.totalRecords
  };

  // 统计朝代分布
  const dynastyStats = {};
  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
  });

  // 统计职业分布
  const occupationStats = {};
  allCelebrities.forEach(celebrity => {
    celebrity.basicInfo.occupation.forEach(occupation => {
      occupationStats[occupation] = (occupationStats[occupation] || 0) + 1;
    });
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建合并后的数据结构
  const mergedData = {
    metadata: {
      title: "历史名人数据库 - 第四批次完整版",
      description: "第四批次历史名人数据，包含古代补充、近现代、当代共79位重要历史人物",
      batchNumber: 4,
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      timeRange: "前284年-2021年AD",
      dataSource: "《史记》《汉书》《三国志》《宋史》《元史》《明史》《清史稿》《毛泽东选集》《钱学森传》等权威史料",
      verificationStandard: "专家交叉校验+古籍依据双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      periodDistribution: periodStats,
      dynastyDistribution: dynastyStats,
      occupationDistribution: occupationStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按时期分类的详细统计信息
    periodAnalysis: {
      古代补充: {
        timeRange: "前284年-1911年",
        count: periodStats["古代补充"],
        subPeriods: {
          "秦朝": 3,
          "东汉": 4,
          "隋朝": 2,
          "五代": 3,
          "宋朝": 8,
          "元朝": 4,
          "明朝": 3,
          "清朝": 5,
          "其他": 3
        },
        averageScore: ancientSupplementComplete.metadata.averageVerificationScore,
        characteristics: "补充重要古代人物，完善朝代分布"
      },
      近现代: {
        timeRange: "1840-1949年",
        count: periodStats["近现代"],
        subPeriods: {
          "洋务维新": 6,
          "革命先驱": 8,
          "军阀抗战": 6,
          "科学文化": 5
        },
        averageScore: modernEraComplete.metadata.averageVerificationScore,
        characteristics: "开眼看世界、革命救国、抗日救亡、文化启蒙"
      },
      当代: {
        timeRange: "1893-2021年",
        count: periodStats["当代"],
        subPeriods: {
          "开国领袖": 5,
          "科学家": 8,
          "文化艺术": 3,
          "体育英雄": 3
        },
        averageScore: contemporaryEraComplete.metadata.averageVerificationScore,
        characteristics: "建国伟业、科技创新、文化繁荣、体育突破"
      }
    },

    // 质量分析
    qualityAnalysis: {
      totalCelebrities: allCelebrities.length,
      averageVerificationScore: averageVerificationScore,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格",
      scoreDistribution: {
        excellent: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.95).length,
        good: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.90 && c.verification.algorithmMatch < 0.95).length,
        acceptable: allCelebrities.filter(c => c.verification.algorithmMatch < 0.90).length
      },
      dataIntegrity: {
        completeBasicInfo: allCelebrities.filter(c => c.basicInfo?.name && c.basicInfo?.birthYear).length,
        completeBazi: allCelebrities.filter(c => c.bazi?.fullBazi).length,
        completePattern: allCelebrities.filter(c => c.pattern?.mainPattern).length,
        completeVerification: allCelebrities.filter(c => c.verification?.algorithmMatch).length
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 第四批次完整数据合并完成 ===');
  console.log(`总计名人数量: ${mergedData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${mergedData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${mergedData.metadata.qualityGrade}`);
  console.log('\n时期分布:');
  Object.entries(periodStats).forEach(([period, count]) => {
    console.log(`  ${period}: ${count}位`);
  });
  console.log('\n主要朝代分布:');
  Object.entries(dynastyStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([dynasty, count]) => {
      console.log(`  ${dynasty}: ${count}位`);
    });
  console.log('\n主要职业分布:');
  Object.entries(occupationStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .forEach(([occupation, count]) => {
      console.log(`  ${occupation}: ${count}位`);
    });

  return mergedData;
}

// 数据质量检查
function validateBatch4CompleteData(data) {
  console.log('\n开始第四批次完整数据质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段完整性
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查验证分数范围
  celebrities.forEach((celebrity, index) => {
    const score = celebrity.verification?.algorithmMatch;
    if (score && (score < 0 || score > 1)) {
      issues.push(`验证分数超出范围: ${celebrity.basicInfo?.name || index} (${score})`);
    }
  });

  // 检查时间逻辑
  celebrities.forEach((celebrity, index) => {
    const birthYear = celebrity.basicInfo?.birthYear;
    const deathYear = celebrity.basicInfo?.deathYear;
    if (deathYear && birthYear && deathYear <= birthYear) {
      issues.push(`死亡年份早于出生年份: ${celebrity.basicInfo?.name || index}`);
    }
  });

  // 检查古籍依据格式
  celebrities.forEach((celebrity, index) => {
    const evidence = celebrity.verification?.ancientTextEvidence;
    if (!evidence || !Array.isArray(evidence) || evidence.length === 0) {
      issues.push(`缺少古籍依据: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`第四批次完整数据质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const mergedData = mergeBatch4CompleteData();
    const validation = validateBatch4CompleteData(mergedData);
    
    if (validation.isValid) {
      console.log('\n✅ 第四批次完整数据质量检查通过！');
      
      // 保存合并后的数据
      const fs = require('fs');
      const outputPath = 'data/batch4_complete.js';
      const fileContent = `/**
 * 历史名人数据库 - 第四批次完整版
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 古代补充、近现代、当代三个完整数据库合并
 * 总计: ${mergedData.metadata.totalRecords}位历史名人
 * 平均验证分数: ${mergedData.metadata.averageVerificationScore}
 */

const batch4Complete = ${JSON.stringify(mergedData, null, 2)};

module.exports = batch4Complete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 第四批次完整数据已保存到: ${outputPath}`);
    } else {
      console.log('\n❌ 第四批次完整数据质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('第四批次完整数据合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeBatch4CompleteData,
  validateBatch4CompleteData
};
