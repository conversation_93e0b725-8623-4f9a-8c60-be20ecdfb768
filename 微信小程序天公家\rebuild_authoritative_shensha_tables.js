// rebuild_authoritative_shensha_tables.js
// 重建权威神煞数据表

console.log('🏗️ 重建权威神煞数据表');
console.log('='.repeat(80));

// 测试数据：辛丑 甲午 癸卯 壬戌
const testData = {
  fourPillars: [
    { gan: '辛', zhi: '丑' },  // 年柱
    { gan: '甲', zhi: '午' },  // 月柱  
    { gan: '癸', zhi: '卯' },  // 日柱
    { gan: '壬', zhi: '戌' }   // 时柱
  ],
  dayGan: '癸',
  dayZhi: '卯',
  yearZhi: '丑',
  birthTime: '19:30'  // 戌时
};

// "问真八字"标准结果
const wenZhenStandard = {
  year: ['福星贵人', '月德合'],
  month: ['天乙贵人', '桃花', '元辰'],
  day: ['天乙贵人', '文昌贵人', '天厨贵人', '福星贵人', '德秀贵人', '童子煞', '灾煞', '丧门', '血刃'],
  hour: ['寡宿', '披麻']
};

console.log('📋 分析目标:');
console.log(`四柱: ${testData.fourPillars.map(p => p.gan + p.zhi).join(' ')}`);
console.log(`出生时间: ${testData.birthTime} (${testData.fourPillars[3].zhi}时)`);

// 1. 天乙贵人权威数据表重建
console.log('\n🌟 天乙贵人权威数据表重建:');
console.log('='.repeat(60));

// 1.1 传统天乙贵人表
const traditionalTianyiTable = {
  '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'],
  '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
  '壬': ['卯', '巳'], '癸': ['卯', '巳']
};

// 1.2 昼夜贵人表（基于出生时间）
const dayNightTianyiTable = {
  // 昼贵人（日出到日落，大约6:00-18:00）
  day: {
    '甲': '丑', '乙': '申', '丙': '酉', '丁': '酉',
    '戊': '丑', '己': '申', '庚': '丑', '辛': '午',
    '壬': '巳', '癸': '巳'
  },
  // 夜贵人（日落到日出，大约18:00-6:00）
  night: {
    '甲': '未', '乙': '子', '丙': '亥', '丁': '亥',
    '戊': '未', '己': '子', '庚': '未', '辛': '寅',
    '壬': '卯', '癸': '卯'
  }
};

// 1.3 判断昼夜
function isDayTime(timeStr) {
  const hour = parseInt(timeStr.split(':')[0]);
  return hour >= 6 && hour < 18;
}

const isDay = isDayTime(testData.birthTime);
console.log(`出生时间 ${testData.birthTime} 属于: ${isDay ? '白天' : '夜晚'}`);

// 1.4 计算天乙贵人
function calculateTianyiGuirenAuthoritative(dayGan, fourPillars, birthTime) {
  const isDay = isDayTime(birthTime);
  const tianyiTable = isDay ? dayNightTianyiTable.day : dayNightTianyiTable.night;
  const targetZhi = tianyiTable[dayGan];
  
  console.log(`\n天乙贵人计算 (${isDay ? '昼' : '夜'}贵人):`);
  console.log(`日干${dayGan}的${isDay ? '昼' : '夜'}贵人: ${targetZhi}`);
  
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  
  fourPillars.forEach((pillar, index) => {
    if (pillar.zhi === targetZhi) {
      result.push({
        name: '天乙贵人',
        position: pillarNames[index],
        pillar: pillar.gan + pillar.zhi,
        type: isDay ? '昼贵人' : '夜贵人'
      });
    }
  });
  
  return result;
}

const tianyiResult = calculateTianyiGuirenAuthoritative(testData.dayGan, testData.fourPillars, testData.birthTime);
console.log('计算结果:', tianyiResult);
console.log(`期望结果: 月柱天乙贵人, 日柱天乙贵人`);

// 检查是否匹配
const tianyiMatches = tianyiResult.map(r => r.position);
const expectedTianyi = ['月柱', '日柱'];
const tianyiMatch = expectedTianyi.every(pos => tianyiMatches.includes(pos));
console.log(`匹配度: ${tianyiMatch ? '✅ 完全匹配' : '❌ 部分匹配'}`);

// 2. 桃花系统权威重建
console.log('\n🌸 桃花系统权威重建:');
console.log('='.repeat(60));

// 2.1 多种桃花计算方法
const taohuaCalculationMethods = {
  // 咸池桃花（基于日支三合局）
  xianchi: {
    '寅': '卯', '午': '卯', '戌': '卯',  // 寅午戌见卯
    '申': '酉', '子': '酉', '辰': '酉',  // 申子辰见酉
    '亥': '子', '卯': '子', '未': '子',  // 亥卯未见子
    '巳': '午', '酉': '午', '丑': '午'   // 巳酉丑见午
  },
  
  // 沐浴桃花（基于日干长生十二宫）
  muyu: {
    '甲': '子', '乙': '巳', '丙': '卯', '丁': '申',
    '戊': '卯', '己': '申', '庚': '午', '辛': '亥',
    '壬': '酉', '癸': '寅'
  },
  
  // 红鸾桃花（基于年支）
  hongluan: {
    '子': '卯', '丑': '寅', '寅': '丑', '卯': '子',
    '辰': '亥', '巳': '戌', '午': '酉', '未': '申',
    '申': '未', '酉': '午', '戌': '巳', '亥': '辰'
  },
  
  // 天喜桃花（基于年支，红鸾对冲）
  tianxi: {
    '子': '酉', '丑': '申', '寅': '未', '卯': '午',
    '辰': '巳', '巳': '辰', '午': '卯', '未': '寅',
    '申': '丑', '酉': '子', '戌': '亥', '亥': '戌'
  }
};

function calculateTaohuaAuthoritative(dayGan, dayZhi, yearZhi, fourPillars) {
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  
  // 1. 咸池桃花
  const xianchiTarget = taohuaCalculationMethods.xianchi[dayZhi];
  if (xianchiTarget) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === xianchiTarget) {
        result.push({
          name: '桃花',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          type: '咸池',
          method: '日支三合局'
        });
      }
    });
  }
  
  // 2. 沐浴桃花
  const muyuTarget = taohuaCalculationMethods.muyu[dayGan];
  if (muyuTarget) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === muyuTarget) {
        result.push({
          name: '桃花',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          type: '沐浴',
          method: '日干长生'
        });
      }
    });
  }
  
  // 3. 红鸾桃花
  const hongluanTarget = taohuaCalculationMethods.hongluan[yearZhi];
  if (hongluanTarget) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === hongluanTarget) {
        result.push({
          name: '红鸾',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          type: '红鸾',
          method: '年支推算'
        });
      }
    });
  }
  
  // 4. 天喜桃花
  const tianxiTarget = taohuaCalculationMethods.tianxi[yearZhi];
  if (tianxiTarget) {
    fourPillars.forEach((pillar, index) => {
      if (pillar.zhi === tianxiTarget) {
        result.push({
          name: '天喜',
          position: pillarNames[index],
          pillar: pillar.gan + pillar.zhi,
          type: '天喜',
          method: '年支对冲'
        });
      }
    });
  }
  
  return result;
}

console.log('\n桃花计算分析:');
console.log(`日干: ${testData.dayGan}, 日支: ${testData.dayZhi}, 年支: ${testData.yearZhi}`);

const taohuaResult = calculateTaohuaAuthoritative(
  testData.dayGan, testData.dayZhi, testData.yearZhi, testData.fourPillars
);

console.log('\n各种桃花计算结果:');
console.log(`咸池桃花 (日支${testData.dayZhi}): ${taohuaCalculationMethods.xianchi[testData.dayZhi] || '无'}`);
console.log(`沐浴桃花 (日干${testData.dayGan}): ${taohuaCalculationMethods.muyu[testData.dayGan] || '无'}`);
console.log(`红鸾桃花 (年支${testData.yearZhi}): ${taohuaCalculationMethods.hongluan[testData.yearZhi] || '无'}`);
console.log(`天喜桃花 (年支${testData.yearZhi}): ${taohuaCalculationMethods.tianxi[testData.yearZhi] || '无'}`);

console.log('\n实际匹配结果:');
taohuaResult.forEach(result => {
  console.log(`${result.position}: ${result.name} (${result.type}) - ${result.method}`);
});

console.log(`\n期望结果: 月柱桃花`);
const taohuaMatches = taohuaResult.filter(r => r.position === '月柱');
console.log(`匹配度: ${taohuaMatches.length > 0 ? '✅ 找到月柱桃花' : '❌ 未找到月柱桃花'}`);

// 3. 福星贵人权威重建
console.log('\n⭐ 福星贵人权威重建:');
console.log('='.repeat(60));

// 3.1 多种福星贵人计算方法
const fuxingCalculationMethods = {
  // 基于年支
  byYearZhi: {
    '子': ['申', '辰'], '丑': ['巳', '酉'], '寅': ['午', '戌'], '卯': ['未', '亥'],
    '辰': ['申', '子'], '巳': ['酉', '丑'], '午': ['戌', '寅'], '未': ['亥', '卯'],
    '申': ['子', '辰'], '酉': ['丑', '巳'], '戌': ['寅', '午'], '亥': ['卯', '未']
  },
  
  // 基于日支
  byDayZhi: {
    '子': ['寅', '午'], '丑': ['卯', '未'], '寅': ['辰', '申'], '卯': ['巳', '酉'],
    '辰': ['午', '戌'], '巳': ['未', '亥'], '午': ['申', '子'], '未': ['酉', '丑'],
    '申': ['戌', '寅'], '酉': ['亥', '卯'], '戌': ['子', '辰'], '亥': ['丑', '巳']
  },
  
  // 基于日干
  byDayGan: {
    '甲': ['寅', '午'], '乙': ['卯', '未'], '丙': ['巳', '酉'], '丁': ['午', '戌'],
    '戊': ['巳', '酉'], '己': ['午', '戌'], '庚': ['申', '子'], '辛': ['酉', '丑'],
    '壬': ['亥', '卯'], '癸': ['子', '辰']
  }
};

function calculateFuxingGuirenAuthoritative(dayGan, dayZhi, yearZhi, fourPillars) {
  const result = [];
  const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
  
  // 尝试不同的计算方法
  const methods = [
    { name: '年支法', targets: fuxingCalculationMethods.byYearZhi[yearZhi] || [] },
    { name: '日支法', targets: fuxingCalculationMethods.byDayZhi[dayZhi] || [] },
    { name: '日干法', targets: fuxingCalculationMethods.byDayGan[dayGan] || [] }
  ];
  
  methods.forEach(method => {
    method.targets.forEach(target => {
      fourPillars.forEach((pillar, index) => {
        if (pillar.zhi === target) {
          result.push({
            name: '福星贵人',
            position: pillarNames[index],
            pillar: pillar.gan + pillar.zhi,
            method: method.name,
            target: target
          });
        }
      });
    });
  });
  
  return result;
}

console.log('\n福星贵人计算分析:');
const fuxingResult = calculateFuxingGuirenAuthoritative(
  testData.dayGan, testData.dayZhi, testData.yearZhi, testData.fourPillars
);

console.log('\n各种福星贵人计算结果:');
console.log(`年支法 (${testData.yearZhi}): [${fuxingCalculationMethods.byYearZhi[testData.yearZhi]?.join(', ') || '无'}]`);
console.log(`日支法 (${testData.dayZhi}): [${fuxingCalculationMethods.byDayZhi[testData.dayZhi]?.join(', ') || '无'}]`);
console.log(`日干法 (${testData.dayGan}): [${fuxingCalculationMethods.byDayGan[testData.dayGan]?.join(', ') || '无'}]`);

console.log('\n实际匹配结果:');
fuxingResult.forEach(result => {
  console.log(`${result.position}: ${result.name} (${result.method}) - ${result.target}`);
});

console.log(`\n期望结果: 年柱福星贵人, 日柱福星贵人`);
const yearFuxing = fuxingResult.filter(r => r.position === '年柱');
const dayFuxing = fuxingResult.filter(r => r.position === '日柱');
console.log(`年柱匹配: ${yearFuxing.length > 0 ? '✅ 找到' : '❌ 未找到'}`);
console.log(`日柱匹配: ${dayFuxing.length > 0 ? '✅ 找到' : '❌ 未找到'}`);

// 4. 综合分析
console.log('\n📊 权威数据表重建效果分析:');
console.log('='.repeat(60));

console.log('\n✅ 改进效果:');
console.log('1. 天乙贵人: 实现昼夜贵人区分');
console.log('2. 桃花系统: 实现4种桃花计算方法');
console.log('3. 福星贵人: 实现3种计算基准');

console.log('\n🎯 下一步重点:');
console.log('1. 实现缺失的神煞计算');
console.log('2. 建立神煞优先级和权重系统');
console.log('3. 完善神煞验证和测试体系');
