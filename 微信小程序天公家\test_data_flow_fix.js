// test_data_flow_fix.js
// 测试数据流修复效果

console.log('🧪 测试数据流修复效果...');

// 模拟修复前的问题场景
function testOriginalProblem() {
  console.log('\n📋 测试原始问题场景:');
  
  // 模拟原始数据（五行为0）
  const rawData = {
    userInfo: { name: '测试用户', gender: '男' },
    baziInfo: {
      yearPillar: { heavenly: '乙', earthly: '巳' },
      monthPillar: { heavenly: '癸', earthly: '未' },
      dayPillar: { heavenly: '癸', earthly: '卯' },
      timePillar: { heavenly: '壬', earthly: '戌' }
    },
    fiveElements: { wood: 0, fire: 0, earth: 0, metal: 0, water: 0 },
    professionalWuxingData: { wood: 16.8, fire: 15, earth: 15.6, metal: 2.4, water: 18 }
  };
  
  console.log('原始数据五行:', rawData.fiveElements);
  console.log('专业级计算结果:', rawData.professionalWuxingData);
  
  return rawData;
}

// 模拟修复后的数据统一逻辑
function testFixedUnifyDataStructure(rawData) {
  console.log('\n📋 测试修复后的数据统一逻辑:');
  
  // 统一五行信息 - 🔧 修复：保留专业级计算结果
  const unifiedFiveElements = {
    wood: rawData.fiveElements?.wood || rawData.wuxing?.wood || rawData.wood || 0,
    fire: rawData.fiveElements?.fire || rawData.wuxing?.fire || rawData.fire || 0,
    earth: rawData.fiveElements?.earth || rawData.wuxing?.earth || rawData.earth || 0,
    metal: rawData.fiveElements?.metal || rawData.wuxing?.metal || rawData.metal || 0,
    water: rawData.fiveElements?.water || rawData.wuxing?.water || rawData.water || 0
  };

  // 🔧 如果五行数据全为0，尝试从专业级计算结果中获取
  const allZero = Object.values(unifiedFiveElements).every(val => val === 0);
  if (allZero && rawData.professionalWuxingData) {
    console.log('🔧 检测到五行数据为0，尝试从专业级计算结果获取...');
    const professionalResult = rawData.professionalWuxingData;
    if (professionalResult.wood || professionalResult.fire || professionalResult.earth || 
        professionalResult.metal || professionalResult.water) {
      unifiedFiveElements.wood = professionalResult.wood || 0;
      unifiedFiveElements.fire = professionalResult.fire || 0;
      unifiedFiveElements.earth = professionalResult.earth || 0;
      unifiedFiveElements.metal = professionalResult.metal || 0;
      unifiedFiveElements.water = professionalResult.water || 0;
      console.log('✅ 已从专业级计算结果恢复五行数据:', unifiedFiveElements);
    }
  }
  
  const unifiedData = {
    userInfo: rawData.userInfo,
    baziInfo: rawData.baziInfo,
    fiveElements: unifiedFiveElements,
    professionalWuxingData: rawData.professionalWuxingData
  };
  
  console.log('统一后的五行数据:', unifiedData.fiveElements);
  
  return unifiedData;
}

// 模拟专业级五行计算后的数据更新
function testDataUpdateAfterProfessionalCalculation(unifiedData) {
  console.log('\n📋 测试专业级计算后的数据更新:');
  
  // 模拟专业级五行计算结果
  const professionalWuxingResult = {
    wood: 16.8,
    fire: 15,
    earth: 15.6,
    metal: 2.4,
    water: 18
  };
  
  // 构建最终五行数据
  const finalFiveElements = {
    wood: professionalWuxingResult.wood || 0,
    fire: professionalWuxingResult.fire || 0,
    earth: professionalWuxingResult.earth || 0,
    metal: professionalWuxingResult.metal || 0,
    water: professionalWuxingResult.water || 0
  };
  
  console.log('专业级计算结果:', finalFiveElements);
  
  // 🔧 更新统一数据结构中的五行信息
  const updatedUnifiedData = {
    ...unifiedData,
    fiveElements: finalFiveElements,
    professionalWuxingData: finalFiveElements
  };
  
  console.log('更新后的统一数据五行:', updatedUnifiedData.fiveElements);
  
  return { updatedUnifiedData, finalFiveElements };
}

// 模拟流年计算前的数据验证
function testLiunianDataValidation(unifiedData, finalFiveElements) {
  console.log('\n📋 测试流年计算前的数据验证:');
  
  console.log('🔧 流年计算前五行数据检查:', unifiedData.fiveElements);
  
  if (Object.values(unifiedData.fiveElements).every(val => val === 0)) {
    console.log('⚠️ 检测到五行数据为0，尝试使用专业级计算结果...');
    if (finalFiveElements && Object.values(finalFiveElements).some(val => val > 0)) {
      unifiedData.fiveElements = finalFiveElements;
      console.log('✅ 已更新五行数据:', unifiedData.fiveElements);
    }
  } else {
    console.log('✅ 五行数据正常，无需修复');
  }
  
  return unifiedData;
}

// 运行完整测试
function runCompleteTest() {
  console.log('🎯 开始完整数据流测试...\n');
  
  // 步骤1: 测试原始问题
  const rawData = testOriginalProblem();
  
  // 步骤2: 测试修复后的数据统一
  const unifiedData = testFixedUnifyDataStructure(rawData);
  
  // 步骤3: 测试专业级计算后的数据更新
  const { updatedUnifiedData, finalFiveElements } = testDataUpdateAfterProfessionalCalculation(unifiedData);
  
  // 步骤4: 测试流年计算前的数据验证
  const finalUnifiedData = testLiunianDataValidation(updatedUnifiedData, finalFiveElements);
  
  // 验证最终结果
  console.log('\n📊 最终验证结果:');
  console.log('==================');
  
  const issues = [];
  
  // 检查五行数据是否正确
  const fiveElementsValid = Object.values(finalUnifiedData.fiveElements).some(val => val > 0);
  if (!fiveElementsValid) {
    issues.push('❌ 五行数据仍为0');
  } else {
    console.log('✅ 五行数据正常:', finalUnifiedData.fiveElements);
  }
  
  // 检查专业级数据是否保留
  const professionalDataValid = finalUnifiedData.professionalWuxingData && 
    Object.values(finalUnifiedData.professionalWuxingData).some(val => val > 0);
  if (!professionalDataValid) {
    issues.push('❌ 专业级五行数据丢失');
  } else {
    console.log('✅ 专业级五行数据保留:', finalUnifiedData.professionalWuxingData);
  }
  
  // 检查数据一致性
  const dataConsistent = JSON.stringify(finalUnifiedData.fiveElements) === 
    JSON.stringify(finalUnifiedData.professionalWuxingData);
  if (!dataConsistent) {
    issues.push('⚠️ 五行数据与专业级数据不一致');
  } else {
    console.log('✅ 数据一致性验证通过');
  }
  
  if (issues.length === 0) {
    console.log('\n🎉 所有测试通过！数据流修复成功！');
  } else {
    console.log('\n❌ 发现问题:');
    issues.forEach(issue => console.log(`  ${issue}`));
  }
  
  return {
    success: issues.length === 0,
    issues: issues,
    finalData: finalUnifiedData
  };
}

// 执行测试
const testResult = runCompleteTest();

console.log('\n🚀 测试结论:');
if (testResult.success) {
  console.log('✅ 数据流修复验证成功！');
  console.log('📋 修复要点:');
  console.log('  1. 数据统一时保留专业级计算结果');
  console.log('  2. 专业级计算后更新统一数据结构');
  console.log('  3. 流年计算前验证五行数据完整性');
} else {
  console.log('❌ 仍存在问题，需要进一步修复');
}

module.exports = {
  testOriginalProblem,
  testFixedUnifyDataStructure,
  testDataUpdateAfterProfessionalCalculation,
  testLiunianDataValidation,
  runCompleteTest
};
