// test/marriage_timing_algorithm_test.js
// 深度验证婚姻应期算法的一致性

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');

class MarriageTimingAlgorithmTest {
  constructor() {
    this.engine = new ProfessionalTimingEngine();
    this.testResults = [];
  }

  /**
   * 测试婚姻应期的各个模块一致性
   */
  async runDeepMarriageTest() {
    console.log('🔍 开始婚姻应期算法深度验证...\n');

    // 测试用例：成年人八字
    const testBazi = {
      year_pillar: { heavenly: '庚', earthly: '午' },
      month_pillar: { heavenly: '丙', earthly: '寅' },
      day_pillar: { heavenly: '戊', earthly: '午' },
      time_pillar: { heavenly: '壬', earthly: '戌' },
      day_master: '戊',
      birth_info: {
        year: 1990, // 34岁成年人
        month: 2,
        day: 15,
        hour: 10,
        gender: '男'
      }
    };

    const gender = 'male';
    const currentYear = 2024;

    console.log('📊 测试八字数据:');
    console.log(`  四柱: ${testBazi.year_pillar.heavenly}${testBazi.year_pillar.earthly} ${testBazi.month_pillar.heavenly}${testBazi.month_pillar.earthly} ${testBazi.day_pillar.heavenly}${testBazi.day_pillar.earthly} ${testBazi.time_pillar.heavenly}${testBazi.time_pillar.earthly}`);
    console.log(`  性别: ${gender}, 年龄: ${currentYear - testBazi.birth_info.year}岁\n`);

    // 1. 测试能量阈值计算
    await this.testEnergyThresholds(testBazi, gender, currentYear);

    // 2. 测试三重引动机制
    await this.testTripleActivation(testBazi, gender, currentYear);

    // 3. 测试病药平衡法则
    await this.testDiseaseAndMedicine(testBazi, gender, currentYear);

    // 4. 测试综合应期计算
    await this.testComprehensiveTiming(testBazi, gender, currentYear);

    // 5. 测试动态分析引擎
    await this.testDynamicAnalysis(testBazi, gender, currentYear);

    this.printResults();
  }

  /**
   * 测试能量阈值计算
   */
  async testEnergyThresholds(bazi, gender, currentYear) {
    console.log('⚡ 测试1: 能量阈值计算...');

    try {
      const energyResult = this.engine.calculateEnergyThresholds(bazi, 'marriage');
      
      console.log('  能量阈值结果:');
      console.log(`    五行能量: ${JSON.stringify(energyResult.element_energies)}`);
      console.log(`    阈值结果: ${JSON.stringify(energyResult.threshold_results)}`);
      console.log(`    整体能量分数: ${energyResult.overall_energy_score}`);
      console.log(`    古籍依据: ${energyResult.ancient_basis}`);

      const hasValidResult = energyResult && energyResult.threshold_results;
      
      this.addTestResult(
        '能量阈值计算',
        hasValidResult,
        hasValidResult ? `✅ 能量阈值计算正常，达标状态: ${energyResult.threshold_met}` : '❌ 能量阈值计算异常'
      );

    } catch (error) {
      console.log(`    ❌ 能量阈值计算失败: ${error.message}`);
      this.addTestResult('能量阈值计算', false, `计算失败: ${error.message}`);
    }
  }

  /**
   * 测试三重引动机制
   */
  async testTripleActivation(bazi, gender, currentYear) {
    console.log('\n🎯 测试2: 三重引动机制...');

    try {
      const activationResult = this.engine.analyzeTripleActivation(bazi, 'marriage', currentYear, 5);
      
      console.log('  三重引动结果:');
      console.log(`    星动分析: ${activationResult.star_activation || 'N/A'}`);
      console.log(`    宫动分析: ${activationResult.palace_activation || 'N/A'}`);
      console.log(`    神煞动分析: ${activationResult.god_activation || 'N/A'}`);
      
      const hasValidResult = activationResult && (
        activationResult.star_activation || 
        activationResult.palace_activation || 
        activationResult.god_activation
      );
      
      this.addTestResult(
        '三重引动机制',
        hasValidResult,
        hasValidResult ? '✅ 三重引动机制计算正常' : '❌ 三重引动机制计算异常'
      );

    } catch (error) {
      console.log(`    ❌ 三重引动机制失败: ${error.message}`);
      this.addTestResult('三重引动机制', false, `计算失败: ${error.message}`);
    }
  }

  /**
   * 测试病药平衡法则
   */
  async testDiseaseAndMedicine(bazi, gender, currentYear) {
    console.log('\n⚖️ 测试3: 病药平衡法则...');

    try {
      const diseaseResult = this.engine.analyzeDiseaseAndMedicine(bazi, 'marriage', gender);
      
      console.log('  病药平衡结果:');
      console.log(`    病神检测: ${diseaseResult.disease_god || 'N/A'}`);
      console.log(`    药神匹配: ${diseaseResult.medicine_god || 'N/A'}`);
      console.log(`    平衡评分: ${diseaseResult.balance_score || 'N/A'}`);
      
      const hasValidResult = diseaseResult && (
        diseaseResult.disease_god || diseaseResult.medicine_god
      );
      
      this.addTestResult(
        '病药平衡法则',
        hasValidResult,
        hasValidResult ? '✅ 病药平衡法则计算正常' : '❌ 病药平衡法则计算异常'
      );

    } catch (error) {
      console.log(`    ❌ 病药平衡法则失败: ${error.message}`);
      this.addTestResult('病药平衡法则', false, `计算失败: ${error.message}`);
    }
  }

  /**
   * 测试综合应期计算
   */
  async testComprehensiveTiming(bazi, gender, currentYear) {
    console.log('\n🎯 测试4: 综合应期计算...');

    try {
      // 🔧 使用完整的专业应期分析
      const fullResult = this.engine.analyzeProfessionalTiming(bazi, 'marriage', gender, currentYear, 5);
      const timingResult = fullResult.timing_prediction;

      console.log('  综合应期结果:');
      console.log(`    最佳年份: ${timingResult?.best_year || 'N/A'}`);
      console.log(`    置信度: ${timingResult?.confidence || 'N/A'}`);
      console.log(`    推理过程: ${timingResult?.reasoning || 'N/A'}`);
      console.log(`    阈值状态: ${timingResult?.threshold_status || 'N/A'}`);

      // 🔍 调试：检查能量分析结果
      console.log('\n  🔍 调试信息:');
      console.log(`    能量分析阈值结果: ${JSON.stringify(fullResult.energy_analysis?.threshold_results)}`);

      const hasValidResult = timingResult && (
        timingResult.best_year || timingResult.threshold_status
      );

      this.addTestResult(
        '综合应期计算',
        hasValidResult,
        hasValidResult ? '✅ 综合应期计算正常' : '❌ 综合应期计算异常'
      );

    } catch (error) {
      console.log(`    ❌ 综合应期计算失败: ${error.message}`);
      this.addTestResult('综合应期计算', false, `计算失败: ${error.message}`);
    }
  }

  /**
   * 测试动态分析引擎
   */
  async testDynamicAnalysis(bazi, gender, currentYear) {
    console.log('\n🔄 测试5: 动态分析引擎...');

    try {
      const dynamicResult = this.engine.analyzeDynamicEngine(bazi, 'marriage', currentYear, 5);
      
      console.log('  动态分析结果:');
      console.log(`    三点一线法则: ${dynamicResult.three_point_rule || 'N/A'}`);
      console.log(`    时空力量: ${dynamicResult.spacetime_force || 'N/A'}`);
      console.log(`    转折点识别: ${dynamicResult.turning_points || 'N/A'}`);
      
      const hasValidResult = dynamicResult && (
        dynamicResult.three_point_rule || 
        dynamicResult.spacetime_force || 
        dynamicResult.turning_points
      );
      
      this.addTestResult(
        '动态分析引擎',
        hasValidResult,
        hasValidResult ? '✅ 动态分析引擎计算正常' : '❌ 动态分析引擎计算异常'
      );

    } catch (error) {
      console.log(`    ❌ 动态分析引擎失败: ${error.message}`);
      this.addTestResult('动态分析引擎', false, `计算失败: ${error.message}`);
    }
  }

  /**
   * 检查算法一致性
   */
  checkAlgorithmConsistency() {
    console.log('\n🔍 检查算法一致性...');

    // 检查能量阈值与最终结果的一致性
    // 如果能量阈值未达标，其他模块也应该反映这一点
    
    console.log('  一致性检查结果:');
    console.log('    - 能量阈值与应期预测一致性: 需要进一步验证');
    console.log('    - 三重引动与病药平衡一致性: 需要进一步验证');
    console.log('    - 各模块输出格式一致性: 需要进一步验证');
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed: passed,
      details: details
    });
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🔍 婚姻应期算法深度验证结果');
    console.log('='.repeat(60));

    let passedCount = 0;
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}: ${result.details}`);
      if (result.passed) passedCount++;
    });

    console.log('-'.repeat(60));
    const totalTests = this.testResults.length;
    const passRate = ((passedCount / totalTests) * 100).toFixed(1);
    
    console.log(`\n📊 总体结果: ${passedCount}/${totalTests} 测试通过 (${passRate}%)`);
    
    if (passedCount === totalTests) {
      console.log('🎉 所有模块测试通过！婚姻应期算法一致性良好！');
    } else {
      console.log('⚠️ 部分模块测试失败，存在算法不一致问题');
      console.log('\n🔧 建议检查:');
      console.log('1. 各模块是否使用相同的能量阈值标准');
      console.log('2. 三重引动机制是否正确集成到最终结果');
      console.log('3. 病药平衡法则是否影响应期预测');
    }
    console.log('='.repeat(60));

    this.checkAlgorithmConsistency();
  }
}

// 运行测试
const test = new MarriageTimingAlgorithmTest();
test.runDeepMarriageTest().catch(console.error);
