// pages/divination/index.js - 占卜功能页面
const api = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchResults: [],
    currentPage: 1,
    totalPages: 1,
    hasMore: true,
    
    // 加载状态
    loading: false,
    refreshing: false,
    
    // 随机占卜
    randomDivination: null,
    
    // 统计信息
    statistics: null,
    
    // API连接状态
    apiConnected: false
  },
  
  onLoad() {
    console.log('占卜页面加载');
    wx.setNavigationBarTitle({
      title: '玉匣记占卜'
    });
    this.initPage();
  },
  
  onShow() {
    // 更新API连接状态
    this.setData({
      apiConnected: app.globalData.apiConnected
    });
    
    // 如果API已连接，加载数据
    if (app.globalData.apiConnected) {
      this.loadPageData();
    }
  },
  
  // 初始化页面
  initPage() {
    console.log('初始化占卜页面');
    
    // 检查API连接状态
    if (app.globalData.apiConnected) {
      this.loadPageData();
    } else {
      // 监听API连接状态变化
      this.checkApiConnection();
    }
  },
  
  // 检查API连接
  checkApiConnection() {
    const checkInterval = setInterval(() => {
      if (app.globalData.apiConnected) {
        clearInterval(checkInterval);
        this.setData({ apiConnected: true });
        this.loadPageData();
      }
    }, 1000);
    
    // 10秒后停止检查
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 10000);
  },
  
  // 加载页面数据
  async loadPageData() {
    try {
      // 加载统计信息
      if (app.globalData.statistics) {
        this.setData({
          statistics: app.globalData.statistics
        });
      }
      
      // 加载随机占卜
      await this.loadRandomDivination();
      
    } catch (error) {
      console.error('加载页面数据失败:', error);
    }
  },
  
  // 加载随机占卜
  async loadRandomDivination() {
    try {
      const result = await api.getRandomDivination();
      console.log('随机占卜:', result);
      
      this.setData({
        randomDivination: result.data
      });
      
    } catch (error) {
      console.error('加载随机占卜失败:', error);
    }
  },
  
  // 搜索输入处理
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },
  
  // 执行搜索
  async onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    console.log('开始搜索:', keyword);
    
    this.setData({
      loading: true,
      currentPage: 1
    });
    
    try {
      const result = await api.searchDivination({
        keyword: keyword,
        page: 1,
        page_size: 20
      });
      
      console.log('搜索结果:', result);
      
      this.setData({
        searchResults: result.data || [],
        currentPage: result.pagination?.page || 1,
        totalPages: result.pagination?.total_pages || 1,
        hasMore: (result.pagination?.page || 1) < (result.pagination?.total_pages || 1),
        loading: false
      });
      
      if (result.data && result.data.length > 0) {
        wx.showToast({
          title: `找到${result.pagination?.total || result.data.length}条结果`,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '没有找到相关结果',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('搜索失败:', error);
      
      this.setData({ loading: false });
      api.showError(error.message);
    }
  },
  
  // 清空搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      currentPage: 1,
      totalPages: 1,
      hasMore: true
    });
  },
  
  // 随机占卜点击
  onRandomTap() {
    this.loadRandomDivination();
  },
  
  // 占卜条目点击
  onDivinationTap(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击占卜条目:', item);
    
    // 跳转到详情页面
    wx.navigateTo({
      url: `/pages/divination-result/index?data=${encodeURIComponent(JSON.stringify(item))}`
    });
  },
  
  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 下拉刷新
  async onPullDownRefresh() {
    console.log('下拉刷新');
    
    try {
      await this.loadPageData();
      wx.stopPullDownRefresh();
    } catch (error) {
      wx.stopPullDownRefresh();
      console.error('刷新失败:', error);
    }
  },
  
  // 上拉加载更多
  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading || !this.data.searchKeyword) {
      return;
    }
    
    console.log('加载更多');
    
    this.setData({ loading: true });
    
    try {
      const result = await api.searchDivination({
        keyword: this.data.searchKeyword,
        page: this.data.currentPage + 1,
        page_size: 20
      });
      
      const newResults = [...this.data.searchResults, ...(result.data || [])];
      
      this.setData({
        searchResults: newResults,
        currentPage: result.pagination?.page || this.data.currentPage + 1,
        hasMore: (result.pagination?.page || this.data.currentPage + 1) < (result.pagination?.total_pages || 1),
        loading: false
      });
      
    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({ loading: false });
      api.showError(error.message);
    }
  }
});
