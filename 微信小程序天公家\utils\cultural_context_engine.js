/**
 * 文化语境适配引擎
 * 实现历史时期规则库、地域神煞映射表等文化语境适配功能
 * 基于应期.txt第六章要求
 */

class CulturalContextEngine {
  constructor() {
    this.historicalPeriodRules = this.initializeHistoricalPeriodRules();
    this.regionalGodsMapping = this.initializeRegionalGodsMapping();
    this.regionalParameterAdjustments = this.initializeRegionalParameterAdjustments();
    this.economicContextRules = this.initializeEconomicContextRules();
  }

  /**
   * 初始化历史时期规则库
   * 基于应期.txt第六章第1节
   */
  initializeHistoricalPeriodRules() {
    return {
      song_dynasty: {
        period: { start: 960, end: 1279 },
        name: '宋代',
        rules: {
          promotion: {
            seal_star_weight_bonus: 0.15, // 印星权重+15%
            reason: '科举官格：万般皆下品，唯有读书高',
            ancient_basis: '宋重文轻武，科举取士为主'
          },
          marriage: {
            cultural_star_weight_bonus: 0.1, // 文昌权重+10%
            reason: '重视门第文化匹配'
          }
        }
      },
      ming_qing_dynasty: {
        period: { start: 1368, end: 1912 },
        name: '明清',
        rules: {
          promotion: {
            wealth_star_threshold_reduction: -0.10, // 财星阈值-10%
            reason: '吏治官格：捐纳制度盛行',
            ancient_basis: '明清捐纳制度，财能生官'
          },
          marriage: {
            family_status_weight: 0.2, // 家族地位权重+20%
            reason: '重视门第匹配，财富决定婚配'
          }
        }
      },
      modern_era: {
        period: { start: 1949, end: null },
        name: '现代',
        rules: {
          promotion: {
            creativity_threshold_reduction: -0.10, // 食伤制杀触发阈值降至40%
            digital_industry_bonus: 0.15, // 数字行业创造力权重+15%
            reason: '创造力经济崛起，知识型人才需求增加',
            ancient_basis: '时代变迁，食伤生财为主流'
          },
          marriage: {
            individual_choice_weight: 0.3, // 个人选择权重+30%
            traditional_constraint_reduction: -0.2, // 传统约束-20%
            reason: '现代社会重视个人意愿和情感匹配'
          }
        }
      },
      digital_age: {
        period: { start: 2000, end: null },
        name: '数字时代',
        rules: {
          promotion: {
            innovation_star_bonus: 0.20, // 创新星权重+20%
            traditional_authority_reduction: -0.15, // 传统权威-15%
            reason: '数字行业：食伤制杀触发阈值降至40%',
            ancient_basis: '食伤为创造力，制杀为突破传统'
          },
          wealth: {
            virtual_economy_bonus: 0.25, // 虚拟经济权重+25%
            reason: '数字经济、虚拟资产成为新的财富形式'
          }
        }
      }
    };
  }

  /**
   * 初始化地域神煞映射表
   * 基于应期.txt第六章第2节
   */
  initializeRegionalGodsMapping() {
    return {
      central_plains: {
        name: '中原地区',
        coordinates: { lat_range: [32, 40], lng_range: [110, 118] },
        red_phoenix_mapping: {
          base_year: 'zi', // 子年起卯（标准）
          starting_branch: 'mao',
          description: '中原地区标准红鸾星起法'
        },
        regional_gods: {
          marriage: ['红鸾', '天喜', '咸池'],
          promotion: ['天乙贵人', '太极贵人', '文昌'],
          wealth: ['禄神', '财库', '天财']
        },
        cultural_characteristics: ['传统文化核心区', '重视礼制', '婚嫁程序完整']
      },
      lingnan_region: {
        name: '岭南地区',
        coordinates: { lat_range: [20, 26], lng_range: [108, 117] },
        red_phoenix_mapping: {
          base_year: 'zi', // 子年起寅（婚俗提前）
          starting_branch: 'yin',
          description: '岭南地区红鸾星提前一位，反映婚俗较早'
        },
        regional_gods: {
          marriage: ['红鸾', '天喜', '桃花'],
          promotion: ['驿马', '将星', '华盖'],
          wealth: ['偏财', '横财', '商贾星']
        },
        cultural_characteristics: ['商业文化发达', '开放包容', '婚嫁相对自由']
      },
      northern_cold_regions: {
        name: '北方寒地',
        coordinates: { lat_range: [40, 50], lng_range: [115, 135] },
        red_phoenix_mapping: {
          base_year: 'zi',
          starting_branch: 'mao',
          weight_adjustment: 0.1, // 红鸾权重+0.1（婚期延迟）
          description: '北方寒地红鸾权重增加，反映婚期相对延迟'
        },
        regional_gods: {
          marriage: ['红鸾', '天喜', '月德'],
          promotion: ['印星', '贵人', '学堂'],
          wealth: ['正财', '库藏', '积蓄星']
        },
        cultural_characteristics: ['气候严寒', '婚期相对延迟', '重视稳定']
      },
      jiangnan_merchant_area: {
        name: '江南商贾区',
        coordinates: { lat_range: [28, 32], lng_range: [118, 122] },
        parameter_adjustments: {
          wealth_star_threshold: -0.05, // 财星阈值-5%
          promotion_timing_advance: -0.8, // 升职应期早0.8年
          description: '江南商贾文化发达，财富积累和升职机会较多'
        },
        regional_gods: {
          marriage: ['红鸾', '天喜', '文昌'],
          promotion: ['财星', '食神', '驿马'],
          wealth: ['正财', '偏财', '商贾星']
        },
        cultural_characteristics: ['商业文化发达', '重视教育', '社会流动性强']
      }
    };
  }

  /**
   * 初始化地域参数调整规则
   */
  initializeRegionalParameterAdjustments() {
    return {
      northern_cold_regions: {
        marriage: {
          red_phoenix_weight_bonus: 0.1, // 红鸾触发权重+0.1
          average_delay_years: 1.2, // 山西婚期平均延迟1.2年
          reason: '气候严寒，传统观念较重，婚期相对延迟'
        }
      },
      jiangnan_merchant_area: {
        promotion: {
          wealth_star_threshold_reduction: -0.05, // 财星阈值-5%
          timing_advance_years: -0.8, // 苏浙升职应期早0.8年
          reason: '商业文化发达，财富积累促进社会地位提升'
        }
      },
      western_plateau: {
        general: {
          stability_weight_bonus: 0.15, // 稳定性权重+15%
          change_resistance: 0.2, // 变化阻力+20%
          reason: '地理环境相对封闭，社会变化较为缓慢'
        }
      }
    };
  }

  /**
   * 初始化经济语境规则
   */
  initializeEconomicContextRules() {
    return {
      economic_downturn: {
        name: '经济下行期',
        indicators: ['GDP增长率<3%', '失业率>8%', '通胀率>5%'],
        adjustments: {
          promotion: {
            wealth_star_weight_multiplier: 0.7, // 财星权重×0.7
            official_star_threshold_increase: 0.1, // 官星阈值+10%
            reason: '经济下行期抑制升职概率，更重视稳定性'
          },
          wealth: {
            risk_aversion_bonus: 0.2, // 风险规避权重+20%
            speculation_penalty: -0.3, // 投机性财运-30%
            reason: '经济不景气时期，稳健投资更受青睐'
          }
        }
      },
      economic_boom: {
        name: '经济繁荣期',
        indicators: ['GDP增长率>7%', '失业率<4%', '创业活跃度高'],
        adjustments: {
          promotion: {
            innovation_bonus: 0.25, // 创新能力权重+25%
            traditional_path_reduction: -0.1, // 传统路径权重-10%
            reason: '经济繁荣期创新和冒险精神更受重视'
          },
          wealth: {
            entrepreneurship_bonus: 0.3, // 创业财运权重+30%
            investment_opportunity_bonus: 0.2, // 投资机会权重+20%
            reason: '经济繁荣期财富机会增多'
          }
        }
      },
      digital_transformation: {
        name: '数字化转型期',
        indicators: ['数字经济占比>30%', '在线服务普及', 'AI技术应用'],
        adjustments: {
          promotion: {
            creativity_threshold_reduction: -0.1, // 食伤制杀触发阈值降至40%
            digital_skills_bonus: 0.2, // 数字技能权重+20%
            reason: '数字行业：食伤制杀触发阈值降至40%，创造力权重提升'
          },
          marriage: {
            online_matching_factor: 0.15, // 在线匹配因子+15%
            traditional_introduction_reduction: -0.1, // 传统介绍方式-10%
            reason: '数字化改变了婚恋匹配方式'
          }
        }
      }
    };
  }

  /**
   * 应用文化语境适配
   * @param {Object} analysisResult - 原始分析结果
   * @param {Object} contextInfo - 语境信息
   * @returns {Object} 适配后的分析结果
   */
  applyCulturalContext(analysisResult, contextInfo) {
    console.log('🌏 应用文化语境适配...');

    const adaptedResult = JSON.parse(JSON.stringify(analysisResult)); // 深拷贝

    try {
      // 1. 应用历史时期规则
      if (contextInfo.historical_period) {
        this.applyHistoricalPeriodRules(adaptedResult, contextInfo.historical_period);
      }

      // 2. 应用地域调整
      if (contextInfo.location) {
        this.applyRegionalAdjustments(adaptedResult, contextInfo.location);
      }

      // 3. 应用经济语境
      if (contextInfo.economic_context) {
        this.applyEconomicContextRules(adaptedResult, contextInfo.economic_context);
      }

      // 4. 添加文化解读
      adaptedResult.cultural_interpretation = this.generateCulturalInterpretation(
        adaptedResult, 
        contextInfo
      );

      console.log('✅ 文化语境适配完成');
      return adaptedResult;

    } catch (error) {
      console.error('❌ 文化语境适配失败:', error);
      adaptedResult.cultural_adaptation_error = error.message;
      return adaptedResult;
    }
  }

  /**
   * 应用历史时期规则
   */
  applyHistoricalPeriodRules(result, historicalPeriod) {
    const periodRules = this.historicalPeriodRules[historicalPeriod];
    if (!periodRules) return;

    const eventType = result.event_type;
    const eventRules = periodRules.rules[eventType];
    if (!eventRules) return;

    // 应用权重调整
    if (eventRules.seal_star_weight_bonus) {
      result.historical_adjustments = result.historical_adjustments || {};
      result.historical_adjustments.seal_star_weight_bonus = eventRules.seal_star_weight_bonus;
      result.confidence *= (1 + eventRules.seal_star_weight_bonus * 0.5); // 调整置信度
    }

    if (eventRules.wealth_star_threshold_reduction) {
      result.historical_adjustments = result.historical_adjustments || {};
      result.historical_adjustments.wealth_star_threshold_reduction = eventRules.wealth_star_threshold_reduction;
    }

    // 添加历史语境说明
    result.historical_context = {
      period: periodRules.name,
      applied_rules: eventRules,
      cultural_background: periodRules.rules[eventType]?.reason || '历史时期特色规则应用'
    };
  }

  /**
   * 应用地域调整
   */
  applyRegionalAdjustments(result, location) {
    const region = this.determineRegion(location);
    if (!region) return;

    const regionData = this.regionalGodsMapping[region];
    const adjustments = this.regionalParameterAdjustments[region];

    // 应用地域神煞
    if (regionData?.regional_gods) {
      result.regional_gods = regionData.regional_gods[result.event_type] || [];
    }

    // 应用参数调整
    if (adjustments) {
      const eventAdjustments = adjustments[result.event_type] || adjustments.general;
      if (eventAdjustments) {
        result.regional_adjustments = eventAdjustments;
        
        // 调整置信度和时间预测
        if (eventAdjustments.red_phoenix_weight_bonus) {
          result.timing_adjustment_years = eventAdjustments.average_delay_years || 0;
        }
        
        if (eventAdjustments.timing_advance_years) {
          result.timing_adjustment_years = eventAdjustments.timing_advance_years;
        }
      }
    }

    // 添加地域语境说明
    result.regional_context = {
      region: regionData?.name || '未知地区',
      characteristics: regionData?.cultural_characteristics || [],
      applied_adjustments: adjustments || {}
    };
  }

  /**
   * 应用经济语境规则
   */
  applyEconomicContextRules(result, economicContext) {
    const contextRules = this.economicContextRules[economicContext];
    if (!contextRules) return;

    const eventAdjustments = contextRules.adjustments[result.event_type];
    if (!eventAdjustments) return;

    // 应用经济语境调整
    result.economic_adjustments = eventAdjustments;

    // 调整置信度
    Object.keys(eventAdjustments).forEach(key => {
      if (key.includes('multiplier')) {
        const multiplier = eventAdjustments[key];
        result.confidence *= multiplier;
      } else if (key.includes('bonus')) {
        const bonus = eventAdjustments[key];
        result.confidence *= (1 + bonus * 0.3);
      }
    });

    // 添加经济语境说明
    result.economic_context = {
      period: contextRules.name,
      indicators: contextRules.indicators,
      applied_adjustments: eventAdjustments,
      economic_reasoning: eventAdjustments.reason
    };
  }

  /**
   * 确定地域
   */
  determineRegion(location) {
    if (!location || !location.lat || !location.lng) return null;

    for (const [regionKey, regionData] of Object.entries(this.regionalGodsMapping)) {
      const coords = regionData.coordinates;
      if (coords && 
          location.lat >= coords.lat_range[0] && location.lat <= coords.lat_range[1] &&
          location.lng >= coords.lng_range[0] && location.lng <= coords.lng_range[1]) {
        return regionKey;
      }
    }

    return 'central_plains'; // 默认中原地区
  }

  /**
   * 生成文化解读
   */
  generateCulturalInterpretation(result, contextInfo) {
    const interpretation = {
      cultural_summary: '基于历史文献和地域文化的专业解读',
      historical_perspective: '',
      regional_perspective: '',
      economic_perspective: '',
      comprehensive_advice: []
    };

    // 历史视角
    if (result.historical_context) {
      interpretation.historical_perspective = 
        `${result.historical_context.period}时期特色：${result.historical_context.cultural_background}`;
    }

    // 地域视角
    if (result.regional_context) {
      interpretation.regional_perspective = 
        `${result.regional_context.region}文化特色：${result.regional_context.characteristics.join('、')}`;
    }

    // 经济视角
    if (result.economic_context) {
      interpretation.economic_perspective = 
        `${result.economic_context.period}背景：${result.economic_context.economic_reasoning}`;
    }

    // 综合建议
    interpretation.comprehensive_advice = this.generateComprehensiveAdvice(result, contextInfo);

    return interpretation;
  }

  /**
   * 生成综合建议
   */
  generateComprehensiveAdvice(result, contextInfo) {
    const advice = [];

    // 基于事件类型的建议
    if (result.event_type === 'marriage') {
      advice.push('重视文化背景匹配，考虑地域婚俗差异');
      if (result.regional_adjustments?.red_phoenix_weight_bonus) {
        advice.push('北方地区婚期可能相对延迟，需要耐心等待最佳时机');
      }
    } else if (result.event_type === 'promotion') {
      advice.push('结合时代特色发展个人能力');
      if (result.historical_adjustments?.seal_star_weight_bonus) {
        advice.push('重视学习和文化修养，印星得力有助于升职');
      }
    }

    // 基于经济语境的建议
    if (result.economic_adjustments) {
      if (result.economic_adjustments.wealth_star_weight_multiplier < 1) {
        advice.push('经济下行期需要更加谨慎，重视稳定性');
      } else {
        advice.push('经济繁荣期可以适当冒险，把握机遇');
      }
    }

    return advice;
  }

  /**
   * 获取文化语境引擎状态
   */
  getEngineStatus() {
    return {
      historical_periods: Object.keys(this.historicalPeriodRules).length,
      regional_mappings: Object.keys(this.regionalGodsMapping).length,
      economic_contexts: Object.keys(this.economicContextRules).length,
      features: {
        historical_period_adaptation: true,
        regional_gods_mapping: true,
        economic_context_adjustment: true,
        cultural_interpretation: true
      }
    };
  }
}

module.exports = CulturalContextEngine;
