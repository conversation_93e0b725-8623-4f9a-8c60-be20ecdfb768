#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接和会话管理
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from contextlib import asynccontextmanager
import logging

from config import settings
from app.models.database import Base

logger = logging.getLogger(__name__)

# 同步数据库引擎
engine = create_engine(
    settings.database_url.replace("aiomysql", "pymysql"),  # 同步版本使用pymysql
    echo=settings.debug,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# 异步数据库引擎
async_engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

def create_tables():
    """创建所有表"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise

def drop_tables():
    """删除所有表"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"删除数据库表失败: {e}")
        raise

def get_db() -> Session:
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@asynccontextmanager
async def get_async_db():
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def init_database():
    """初始化数据库"""
    try:
        # 创建表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("异步数据库初始化成功")
    except Exception as e:
        logger.error(f"异步数据库初始化失败: {e}")
        raise

async def close_database():
    """关闭数据库连接"""
    await async_engine.dispose()
    logger.info("数据库连接已关闭")

# 数据库健康检查
def check_database_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        with SessionLocal() as db:
            db.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False

async def check_async_database_health() -> bool:
    """检查异步数据库连接健康状态"""
    try:
        async with AsyncSessionLocal() as db:
            await db.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"异步数据库健康检查失败: {e}")
        return False
