// diagnose_frontend_display_issue.js
// 诊断前端显示问题

console.log('🔍 开始诊断前端显示问题');

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    const mockData = {
      'bazi_birth_info': {
        year: 2025,
        month: 8,
        day: 2,
        hour: 20,
        minute: 23,
        name: 'hg',
        gender: '男'
      },
      'bazi_frontend_result': {
        bazi: {
          year: { gan: '乙', zhi: '巳' },
          month: { gan: '戊', zhi: '申' },
          day: { gan: '壬', zhi: '戌' },
          hour: { gan: '壬', zhi: '戌' }
        },
        five_elements: {
          wood: 1.5,
          fire: 2.8,
          earth: 3.2,
          metal: 1.9,
          water: 0.6
        }
      }
    };
    return mockData[key] || null;
  }
};

// 检查问题1: 数据结构是否正确
function checkDataStructure() {
  console.log('\n📋 检查1: 数据结构');
  
  // 模拟页面初始数据
  const pageData = {
    professionalLiunianData: {
      success: false,
      currentLiunian: null,
      liunianList: [],
      summary: null,
      basis: '传统命理',
      calculation: null
    },
    liunianData: [],
    loadingStates: {
      liunian: false
    }
  };
  
  console.log('初始页面数据:', pageData);
  
  // 模拟计算结果
  const mockCalculationResult = {
    success: true,
    currentLiunian: {
      year: 2025,
      gan: '乙',
      zhi: '巳',
      fortuneLevel: {
        level: '中吉',
        score: 78,
        levelClass: 'good'
      }
    },
    liunianList: [
      {
        year: 2025,
        age: 0,
        gan: '乙',
        zhi: '巳',
        ganzhi: '乙巳',
        tenGod: '比肩',
        tenGodAnalysis: {
          description: '比肩主导，同类相助，运势平稳'
        },
        fortuneLevel: {
          level: '中吉',
          score: 78
        },
        advice: ['保持稳定', '谨慎投资'],
        activatedShensha: [{ name: '天德贵人' }],
        interactions: [{ description: '五行平衡' }]
      }
    ],
    summary: {
      totalYears: 1,
      averageScore: 78,
      averageScore_display: 78,
      bestYear: {
        year: 2025,
        fortuneLevel: { score: 78 }
      },
      worstYear: {
        year: 2025,
        fortuneLevel: { score: 78 }
      }
    },
    basis: '《三命通会·流年章》黄帝纪元法'
  };
  
  console.log('模拟计算结果:', mockCalculationResult);
  
  return { pageData, mockCalculationResult };
}

// 检查问题2: 数据转换是否正确
function checkDataTransformation() {
  console.log('\n📋 检查2: 数据转换');
  
  const { mockCalculationResult } = checkDataStructure();
  
  // 模拟 transformLiunianDataForFrontend 方法
  function transformLiunianDataForFrontend(liunianResult) {
    if (!liunianResult.success || !liunianResult.liunianList) {
      console.log('❌ 数据转换失败: 缺少必要数据');
      return [];
    }
    
    const levelColors = {
      '大吉': '#ff6b6b',
      '中吉': '#4ecdc4',
      '平稳': '#45b7d1',
      '小凶': '#f9ca24',
      '大凶': '#6c5ce7'
    };
    
    const levelClassMap = {
      '大吉': 'excellent',
      '中吉': 'good',
      '平稳': 'stable',
      '小凶': 'poor',
      '大凶': 'bad'
    };
    
    const tenGodIcons = {
      '比肩': '👥', '劫财': '💰', '食神': '🍽️', '伤官': '⚡',
      '偏财': '💎', '正财': '💵', '七杀': '⚔️', '正官': '👑',
      '偏印': '📚', '正印': '🎓'
    };
    
    return liunianResult.liunianList.map(item => {
      return {
        year: `${item.year}年`,
        age: `${item.age}岁`,
        chars: [item.gan, item.zhi],
        ganzhi: item.ganzhi,
        title: `${item.tenGod}主导年`,
        desc: item.tenGodAnalysis.description,
        score: `${item.fortuneLevel.score}分`,
        level: item.fortuneLevel.level,
        levelClass: levelClassMap[item.fortuneLevel.level] || 'stable',
        levelColor: levelColors[item.fortuneLevel.level] || '#95a5a6',
        tenGod: item.tenGod,
        tenGodIcon: tenGodIcons[item.tenGod] || '🔮',
        advice: item.advice.join('；'),
        activatedShensha: item.activatedShensha.map(s => s.name).join('、') || '无',
        interactions: item.interactions.map(i => i.description).join('；') || '无特殊交互',
        current: item.year === new Date().getFullYear()
      };
    });
  }
  
  const transformedData = transformLiunianDataForFrontend(mockCalculationResult);
  console.log('转换后的前端数据:', transformedData);
  
  return transformedData;
}

// 检查问题3: setData 调用是否正确
function checkSetDataCall() {
  console.log('\n📋 检查3: setData 调用');
  
  const { mockCalculationResult } = checkDataStructure();
  const transformedData = checkDataTransformation();
  
  // 模拟 setData 调用
  const setDataParams = {
    professionalLiunianData: mockCalculationResult,
    liunianData: transformedData,
    'loadingStates.liunian': false
  };
  
  console.log('setData 参数:', setDataParams);
  
  // 验证关键数据
  const validations = {
    hasProfessionalData: !!setDataParams.professionalLiunianData,
    hasLiunianData: Array.isArray(setDataParams.liunianData) && setDataParams.liunianData.length > 0,
    hasSummary: !!setDataParams.professionalLiunianData.summary,
    loadingComplete: setDataParams['loadingStates.liunian'] === false
  };
  
  console.log('数据验证结果:', validations);
  
  return validations;
}

// 检查问题4: WXML 条件渲染
function checkWXMLConditions() {
  console.log('\n📋 检查4: WXML 条件渲染');
  
  const { mockCalculationResult } = checkDataStructure();
  const transformedData = checkDataTransformation();
  
  // 模拟页面数据状态
  const pageState = {
    professionalLiunianData: mockCalculationResult,
    liunianData: transformedData,
    loadingStates: { liunian: false }
  };
  
  // 检查关键条件
  const conditions = {
    // 专业流年分析模块显示条件
    showProfessionalModule: true, // 这个模块应该总是显示
    
    // 数据状态指示器条件
    showStatusIndicator: !!pageState.professionalLiunianData,
    statusSuccess: pageState.professionalLiunianData.success,
    
    // 当前流年概览条件
    showCurrentOverview: !!pageState.professionalLiunianData.currentLiunian,
    
    // 流年列表条件
    showLiunianList: Array.isArray(pageState.liunianData) && pageState.liunianData.length > 0,
    
    // 加载状态条件
    showLoading: pageState.loadingStates.liunian,
    
    // 流年统计摘要条件
    showSummary: !!pageState.professionalLiunianData.summary && !pageState.loadingStates.liunian
  };
  
  console.log('WXML 条件检查:', conditions);
  
  return conditions;
}

// 检查问题5: CSS 样式应用
function checkCSSStyles() {
  console.log('\n📋 检查5: CSS 样式应用');
  
  const expectedClasses = [
    'tianggong-card',
    'liunian-card', 
    'professional-liunian',
    'card-header',
    'card-title',
    'professional-badge',
    'data-status-indicator',
    'current-liunian-overview',
    'liunian-list',
    'professional-list',
    'liunian-summary'
  ];
  
  console.log('期望的CSS类名:', expectedClasses);
  
  // 检查样式问题可能的原因
  const possibleIssues = [
    '1. CSS文件未正确加载',
    '2. 类名拼写错误',
    '3. CSS选择器优先级问题',
    '4. 微信小程序缓存问题',
    '5. 条件渲染导致元素未显示',
    '6. 数据绑定问题导致类名未应用'
  ];
  
  console.log('可能的样式问题:', possibleIssues);
  
  return { expectedClasses, possibleIssues };
}

// 运行完整诊断
function runCompleteDiagnosis() {
  console.log('🎯 开始完整诊断...\n');
  
  const dataStructure = checkDataStructure();
  const dataTransformation = checkDataTransformation();
  const setDataValidation = checkSetDataCall();
  const wxmlConditions = checkWXMLConditions();
  const cssStyles = checkCSSStyles();
  
  console.log('\n📊 诊断结果汇总:');
  console.log('==================');
  
  // 分析可能的问题
  const issues = [];
  
  if (!setDataValidation.hasProfessionalData) {
    issues.push('❌ 专业流年数据未正确设置');
  }
  
  if (!setDataValidation.hasLiunianData) {
    issues.push('❌ 流年列表数据为空');
  }
  
  if (!setDataValidation.hasSummary) {
    issues.push('❌ 流年统计摘要数据缺失');
  }
  
  if (!wxmlConditions.showSummary) {
    issues.push('❌ 流年统计摘要不满足显示条件');
  }
  
  if (issues.length === 0) {
    console.log('✅ 数据层面没有发现问题');
    console.log('\n🔧 建议检查:');
    console.log('1. 微信开发者工具是否正确编译');
    console.log('2. 页面是否正确加载最新代码');
    console.log('3. 是否存在JavaScript运行时错误');
    console.log('4. CSS样式是否被其他样式覆盖');
    console.log('5. 数据是否在页面渲染后才设置');
  } else {
    console.log('❌ 发现以下问题:');
    issues.forEach(issue => console.log(`  ${issue}`));
  }
  
  return {
    dataStructure,
    dataTransformation,
    setDataValidation,
    wxmlConditions,
    cssStyles,
    issues
  };
}

// 执行诊断
const diagnosisResult = runCompleteDiagnosis();

console.log('\n🚀 建议的解决方案:');
console.log('1. 在微信开发者工具中清除缓存并重新编译');
console.log('2. 检查控制台是否有JavaScript错误');
console.log('3. 验证数据是否正确传递到页面');
console.log('4. 检查WXML模板的条件渲染逻辑');
console.log('5. 确认CSS样式文件正确加载');

module.exports = { 
  checkDataStructure,
  checkDataTransformation, 
  checkSetDataCall,
  checkWXMLConditions,
  checkCSSStyles,
  runCompleteDiagnosis 
};
