/**
 * 将100位女性历史名人合并到主数据库
 * 解决女性样本不足问题，提升性别平衡
 */

const fs = require('fs');
const path = require('path');

class FemaleCelebritiesMerger {
  constructor() {
    this.mainDatabase = null;
    this.femaleDatabase = null;
    this.mergedDatabase = null;
  }

  /**
   * 执行合并操作
   */
  async merge() {
    console.log('🔄 开始合并100位女性历史名人到主数据库...\n');

    try {
      // 1. 加载现有数据库
      await this.loadMainDatabase();
      
      // 2. 加载女性数据库
      await this.loadFemaleDatabase();
      
      // 3. 执行合并
      await this.performMerge();
      
      // 4. 生成新的数据库文件
      await this.generateMergedDatabase();
      
      // 5. 生成统计报告
      this.generateReport();
      
      console.log('🎉 女性历史名人合并完成！');
      
    } catch (error) {
      console.error('❌ 合并过程中出现错误:', error.message);
      throw error;
    }
  }

  /**
   * 加载主数据库
   */
  async loadMainDatabase() {
    console.log('📖 加载主数据库...');
    
    const mainDbPath = path.join(__dirname, '../data/celebrities_database_200_complete.js');
    
    if (!fs.existsSync(mainDbPath)) {
      throw new Error('主数据库文件不存在: ' + mainDbPath);
    }

    // 动态导入主数据库
    delete require.cache[require.resolve('../data/celebrities_database_200_complete.js')];
    const mainDbModule = require('../data/celebrities_database_200_complete.js');
    
    this.mainDatabase = mainDbModule;
    
    console.log(`✅ 主数据库加载成功: ${this.mainDatabase.metadata.totalRecords} 位名人`);
    console.log(`   男性: ${this.mainDatabase.metadata.genderDistribution.male} 位`);
    console.log(`   女性: ${this.mainDatabase.metadata.genderDistribution.female} 位`);
  }

  /**
   * 加载女性数据库
   */
  async loadFemaleDatabase() {
    console.log('\n📖 加载女性数据库...');
    
    const femaleDbPath = path.join(__dirname, '../data/female_celebrities_100_complete.js');
    
    if (!fs.existsSync(femaleDbPath)) {
      throw new Error('女性数据库文件不存在: ' + femaleDbPath);
    }

    // 动态导入女性数据库
    delete require.cache[require.resolve('../data/female_celebrities_100_complete.js')];
    const femaleDbModule = require('../data/female_celebrities_100_complete.js');
    
    this.femaleDatabase = femaleDbModule;
    
    console.log(`✅ 女性数据库加载成功: ${this.femaleDatabase.metadata.totalRecords} 位女性名人`);
    console.log(`   平均验证分数: ${this.femaleDatabase.metadata.averageVerificationScore}`);
  }

  /**
   * 执行合并操作
   */
  async performMerge() {
    console.log('\n🔄 执行数据合并...');

    // 检查ID冲突
    const mainIds = new Set(this.mainDatabase.celebrities.map(c => c.id));
    const femaleIds = new Set(this.femaleDatabase.celebrities.map(c => c.id));
    const conflicts = [...femaleIds].filter(id => mainIds.has(id));
    
    if (conflicts.length > 0) {
      console.log(`⚠️  发现 ${conflicts.length} 个ID冲突，正在重新分配...`);
      this.resolveIdConflicts();
    }

    // 检查姓名重复
    const mainNames = new Set(this.mainDatabase.celebrities.map(c => c.basicInfo.name));
    const duplicateNames = this.femaleDatabase.celebrities.filter(c => 
      mainNames.has(c.basicInfo.name)
    );
    
    if (duplicateNames.length > 0) {
      console.log(`⚠️  发现 ${duplicateNames.length} 个重名，正在处理...`);
      duplicateNames.forEach(celebrity => {
        console.log(`   - ${celebrity.basicInfo.name} (${celebrity.basicInfo.dynasty})`);
      });
      this.handleDuplicateNames(duplicateNames);
    }

    // 合并数据
    const mergedCelebrities = [
      ...this.mainDatabase.celebrities,
      ...this.femaleDatabase.celebrities
    ];

    // 计算新的统计信息
    const newMetadata = this.calculateMergedMetadata(mergedCelebrities);

    this.mergedDatabase = {
      metadata: newMetadata,
      celebrities: mergedCelebrities
    };

    console.log(`✅ 数据合并完成: ${mergedCelebrities.length} 位名人`);
  }

  /**
   * 解决ID冲突
   */
  resolveIdConflicts() {
    let maxId = 0;
    
    // 找到主数据库中的最大ID编号
    this.mainDatabase.celebrities.forEach(celebrity => {
      const match = celebrity.id.match(/\d+/);
      if (match) {
        maxId = Math.max(maxId, parseInt(match[0]));
      }
    });

    // 重新分配女性数据库的ID
    this.femaleDatabase.celebrities.forEach((celebrity, index) => {
      celebrity.id = `female_${String(maxId + index + 1).padStart(3, '0')}`;
    });
  }

  /**
   * 处理重名问题
   */
  handleDuplicateNames(duplicates) {
    duplicates.forEach(celebrity => {
      // 在名字后添加朝代标识以区分
      const originalName = celebrity.basicInfo.name;
      celebrity.basicInfo.name = `${originalName}(${celebrity.basicInfo.dynasty})`;
      celebrity.basicInfo.nickname = originalName;
    });
  }

  /**
   * 计算合并后的元数据
   */
  calculateMergedMetadata(celebrities) {
    // 计算性别分布
    const genderCount = { male: 0, female: 0 };
    celebrities.forEach(celebrity => {
      if (celebrity.basicInfo.gender === '男') {
        genderCount.male++;
      } else if (celebrity.basicInfo.gender === '女') {
        genderCount.female++;
      }
    });

    // 计算朝代分布
    const dynastyDistribution = {};
    celebrities.forEach(celebrity => {
      const dynasty = celebrity.basicInfo.dynasty;
      dynastyDistribution[dynasty] = (dynastyDistribution[dynasty] || 0) + 1;
    });

    // 计算平均验证分数
    const totalScore = celebrities.reduce((sum, celebrity) => 
      sum + (celebrity.verification.algorithmMatch || 0), 0);
    const averageScore = Math.round((totalScore / celebrities.length) * 1000) / 1000;

    return {
      title: "历史名人数据库 - 300名人完整版(含100位女性)",
      version: "2.0.0",
      totalRecords: celebrities.length,
      averageVerificationScore: averageScore,
      lastUpdated: new Date().toISOString().split('T')[0],
      genderDistribution: {
        male: genderCount.male,
        female: genderCount.female,
        malePercentage: Math.round((genderCount.male / celebrities.length) * 100),
        femalePercentage: Math.round((genderCount.female / celebrities.length) * 100)
      },
      dynastyDistribution: dynastyDistribution,
      dynastyCount: Object.keys(dynastyDistribution).length,
      improvements: [
        "新增100位女性历史名人",
        "大幅提升女性样本比例",
        "覆盖更多历史时期和领域",
        "增强性别平衡性"
      ]
    };
  }

  /**
   * 生成合并后的数据库文件
   */
  async generateMergedDatabase() {
    console.log('\n📁 生成合并后的数据库文件...');

    // 生成完整版数据库
    const completeContent = `/**
 * 历史名人数据库 - 300名人完整版(含100位女性)
 * 版本: 2.0.0
 * 更新时间: ${new Date().toISOString().split('T')[0]}
 * 
 * 重大更新:
 * - 新增100位女性历史名人
 * - 女性比例从3%提升到35%
 * - 覆盖更多历史时期和领域
 */

const celebritiesDatabase300Complete = ${JSON.stringify(this.mergedDatabase, null, 2)};

module.exports = celebritiesDatabase300Complete;`;

    const completeFilePath = path.join(__dirname, '../data/celebrities_database_300_complete.js');
    fs.writeFileSync(completeFilePath, completeContent, 'utf8');

    // 生成简化版数据库（用于前端）
    const simplifiedDatabase = {
      metadata: this.mergedDatabase.metadata,
      celebrities: this.mergedDatabase.celebrities.map(celebrity => ({
        id: celebrity.id,
        name: celebrity.basicInfo.name,
        gender: celebrity.basicInfo.gender,
        dynasty: celebrity.basicInfo.dynasty,
        occupation: celebrity.basicInfo.occupation,
        pattern: celebrity.pattern.mainPattern,
        yongshen: celebrity.pattern.yongshen,
        verificationScore: celebrity.verification.algorithmMatch,
        bazi: celebrity.bazi
      }))
    };

    const simplifiedContent = `/**
 * 历史名人数据库 - 300名人简化版(含100位女性)
 * 用于前端显示和API调用
 */

const celebritiesDatabase300Simplified = ${JSON.stringify(simplifiedDatabase, null, 2)};

module.exports = celebritiesDatabase300Simplified;`;

    const simplifiedFilePath = path.join(__dirname, '../data/celebrities_database_300_simplified.js');
    fs.writeFileSync(simplifiedFilePath, simplifiedContent, 'utf8');

    console.log(`✅ 完整版数据库: ${completeFilePath}`);
    console.log(`✅ 简化版数据库: ${simplifiedFilePath}`);
  }

  /**
   * 生成统计报告
   */
  generateReport() {
    console.log('\n📊 合并统计报告');
    console.log('='.repeat(60));
    
    const metadata = this.mergedDatabase.metadata;
    
    console.log(`📈 数据库规模:`);
    console.log(`   总名人数: ${metadata.totalRecords} 位`);
    console.log(`   平均验证分数: ${metadata.averageVerificationScore}`);
    
    console.log(`\n👥 性别分布:`);
    console.log(`   男性: ${metadata.genderDistribution.male} 位 (${metadata.genderDistribution.malePercentage}%)`);
    console.log(`   女性: ${metadata.genderDistribution.female} 位 (${metadata.genderDistribution.femalePercentage}%)`);
    
    console.log(`\n🏛️  朝代覆盖:`);
    console.log(`   朝代数量: ${metadata.dynastyCount} 个`);
    
    const topDynasties = Object.entries(metadata.dynastyDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
    
    console.log(`   主要朝代:`);
    topDynasties.forEach(([dynasty, count]) => {
      console.log(`     ${dynasty}: ${count} 位`);
    });

    console.log(`\n🎯 改进效果:`);
    console.log(`   ✅ 女性比例从 3% 提升到 ${metadata.genderDistribution.femalePercentage}%`);
    console.log(`   ✅ 新增 100 位女性历史名人`);
    console.log(`   ✅ 覆盖更多历史时期和领域`);
    console.log(`   ✅ 大幅改善性别平衡性`);

    console.log(`\n📋 下一步建议:`);
    console.log(`   1. 更新API接口以支持新的数据库`);
    console.log(`   2. 测试前端集成功能`);
    console.log(`   3. 验证相似度匹配算法`);
    console.log(`   4. 更新用户界面显示`);
  }
}

// 导出类
module.exports = FemaleCelebritiesMerger;

// 如果直接运行此文件，执行合并
if (require.main === module) {
  const merger = new FemaleCelebritiesMerger();
  merger.merge().catch(console.error);
}
