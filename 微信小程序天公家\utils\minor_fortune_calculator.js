/**
 * 小运计算系统 - 基于《三命通会·卷八》
 * 
 * 核心特性：
 * 1. 仅适用于1-10岁的命主
 * 2. 从出生时柱开始推演
 * 3. 阳男阴女顺行，阴男阳女逆行
 * 4. 每年周岁生日时更替
 * 
 * 依据：《三命通会·卷八》"小运补大运之不足，未交大运前用之"
 */

class MinorFortuneCalculator {
  constructor() {
    // 天干地支序列
    this.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    this.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    // 阳干定义（用于判断阴阳年）
    this.yangGan = ['甲', '丙', '戊', '庚', '壬'];
    
    console.log('🎯 小运计算系统初始化完成 - 基于《三命通会·卷八》');
  }

  /**
   * 计算指定年龄的小运
   * @param {Object} bazi - 八字信息 {yearPillar, monthPillar, dayPillar, hourPillar, gender}
   * @param {number} currentAge - 当前年龄（1-10岁）
   * @returns {Object|null} 小运信息或null（超出年龄范围）
   */
  calculate(bazi, currentAge) {
    console.log(`🔮 开始计算${currentAge}岁小运...`);
    
    // 严格年龄限制验证
    if (!this.isValidAge(currentAge)) {
      console.log(`❌ 年龄${currentAge}岁超出小运适用范围（1-10岁）`);
      return null;
    }

    const { hourPillar, gender, yearPillar } = bazi;
    
    // 输入验证
    if (!this.validateInput(bazi)) {
      console.error('❌ 八字数据格式错误');
      return null;
    }

    // 判断阴阳年
    const yangYear = this.isYangYear(yearPillar.gan);
    console.log(`📅 年干${yearPillar.gan}，${yangYear ? '阳年' : '阴年'}`);

    // 确定顺逆规则
    const isForward = this.determineDirection(gender, yangYear);
    console.log(`🧭 ${gender}性${yangYear ? '阳' : '阴'}年 → ${isForward ? '顺行' : '逆行'}`);

    // 计算小运干支
    const minorFortunePillar = this.calculatePillar(hourPillar, isForward, currentAge);
    
    // 生成完整的小运信息
    const minorFortuneInfo = this.generateMinorFortuneInfo(
      minorFortunePillar, 
      currentAge, 
      isForward, 
      hourPillar
    );

    console.log(`✅ ${currentAge}岁小运计算完成: ${minorFortunePillar}`);
    return minorFortuneInfo;
  }

  /**
   * 批量计算1-10岁所有小运
   * @param {Object} bazi - 八字信息
   * @returns {Array} 小运序列
   */
  calculateAllMinorFortunes(bazi) {
    console.log('🔄 开始批量计算1-10岁小运序列...');
    
    const minorFortunes = [];
    
    for (let age = 1; age <= 10; age++) {
      const minorFortune = this.calculate(bazi, age);
      if (minorFortune) {
        minorFortunes.push(minorFortune);
      }
    }
    
    console.log(`✅ 小运序列计算完成，共${minorFortunes.length}步`);
    return minorFortunes;
  }

  /**
   * 验证年龄是否在有效范围内
   * @param {number} age - 年龄
   * @returns {boolean} 是否有效
   */
  isValidAge(age) {
    return Number.isInteger(age) && age >= 1 && age <= 10;
  }

  /**
   * 验证输入数据格式
   * @param {Object} bazi - 八字数据
   * @returns {boolean} 是否有效
   */
  validateInput(bazi) {
    if (!bazi || typeof bazi !== 'object') {
      return false;
    }

    const { hourPillar, gender, yearPillar } = bazi;
    
    // 验证时柱
    if (!hourPillar || !hourPillar.gan || !hourPillar.zhi) {
      console.error('❌ 时柱数据缺失或格式错误');
      return false;
    }

    // 验证年柱
    if (!yearPillar || !yearPillar.gan) {
      console.error('❌ 年柱数据缺失或格式错误');
      return false;
    }

    // 验证性别
    if (!gender || !['男', '女', 'male', 'female'].includes(gender)) {
      console.error('❌ 性别数据缺失或格式错误');
      return false;
    }

    // 验证干支有效性
    if (!this.tiangan.includes(hourPillar.gan) || !this.dizhi.includes(hourPillar.zhi)) {
      console.error('❌ 时柱干支无效');
      return false;
    }

    if (!this.tiangan.includes(yearPillar.gan)) {
      console.error('❌ 年干无效');
      return false;
    }

    return true;
  }

  /**
   * 判断是否为阳年
   * @param {string} yearGan - 年干
   * @returns {boolean} 是否为阳年
   */
  isYangYear(yearGan) {
    return this.yangGan.includes(yearGan);
  }

  /**
   * 确定推演方向
   * @param {string} gender - 性别
   * @param {boolean} yangYear - 是否阳年
   * @returns {boolean} 是否顺行
   */
  determineDirection(gender, yangYear) {
    // 标准化性别
    const normalizedGender = this.normalizeGender(gender);
    
    // 《三命通会》规则：阳男阴女顺行，阴男阳女逆行
    return (normalizedGender === '男' && yangYear) || (normalizedGender === '女' && !yangYear);
  }

  /**
   * 标准化性别表示
   * @param {string} gender - 性别
   * @returns {string} 标准化后的性别
   */
  normalizeGender(gender) {
    if (gender === 'male' || gender === '男') return '男';
    if (gender === 'female' || gender === '女') return '女';
    return gender;
  }

  /**
   * 计算指定步数后的干支
   * @param {Object} startPillar - 起始柱（时柱）
   * @param {boolean} isForward - 是否顺行
   * @param {number} steps - 推演步数（年龄）
   * @returns {string} 计算后的干支
   */
  calculatePillar(startPillar, isForward, steps) {
    const { gan, zhi } = startPillar;

    console.log(`🎯 从时柱${gan}${zhi}开始，${isForward ? '顺' : '逆'}推${steps}步`);

    // 获取起始位置
    const ganIndex = this.tiangan.indexOf(gan);
    const zhiIndex = this.dizhi.indexOf(zhi);

    if (ganIndex === -1 || zhiIndex === -1) {
      throw new Error(`无效的干支: ${gan}${zhi}`);
    }

    // 🔧 修正：1岁小运应该是时柱本身，推演步数应该是(年龄-1)
    const actualSteps = steps - 1;

    // 计算推演方向
    const step = isForward ? 1 : -1;

    // 计算新的干支位置
    const newGanIndex = (ganIndex + step * actualSteps + this.tiangan.length * 10) % this.tiangan.length;
    const newZhiIndex = (zhiIndex + step * actualSteps + this.dizhi.length * 10) % this.dizhi.length;

    const resultGan = this.tiangan[newGanIndex];
    const resultZhi = this.dizhi[newZhiIndex];
    const result = resultGan + resultZhi;

    console.log(`   计算过程: ${gan}(${ganIndex}) + ${step * actualSteps} = ${resultGan}(${newGanIndex})`);
    console.log(`   计算过程: ${zhi}(${zhiIndex}) + ${step * actualSteps} = ${resultZhi}(${newZhiIndex})`);
    console.log(`   结果: ${result}`);

    return result;
  }

  /**
   * 生成完整的小运信息
   * @param {string} pillar - 小运干支
   * @param {number} age - 年龄
   * @param {boolean} isForward - 是否顺行
   * @param {Object} hourPillar - 时柱
   * @returns {Object} 完整的小运信息
   */
  generateMinorFortuneInfo(pillar, age, isForward, hourPillar) {
    return {
      pillar: pillar,
      age: age,
      gan: pillar[0],
      zhi: pillar[1],
      direction: isForward ? '顺行' : '逆行',
      basis: "《三命通会·卷八》小运起法",
      description: `${age}岁期间的补充运势`,
      specialNote: "此算法为《三命通会》特有体系，与传统5年周期小运不同",
      startPillar: hourPillar.gan + hourPillar.zhi,
      applicableAge: "1-10岁",
      updateCycle: "每年周岁生日时更替",
      purpose: "补大运之不足，未交大运前用之"
    };
  }

  /**
   * 获取小运的简要分析
   * @param {string} pillar - 小运干支
   * @param {number} age - 年龄
   * @returns {Object} 小运分析
   */
  analyzeMinorFortune(pillar, age) {
    const gan = pillar[0];
    const zhi = pillar[1];
    
    // 基础五行分析
    const ganElement = this.getElementByGan(gan);
    const zhiElement = this.getElementByZhi(zhi);
    
    // 生成简要分析
    const analysis = {
      element: {
        gan: ganElement,
        zhi: zhiElement
      },
      strength: this.analyzeStrength(gan, zhi),
      tendency: this.analyzeTendency(gan, zhi, age),
      advice: this.generateAdvice(gan, zhi, age)
    };
    
    return analysis;
  }

  /**
   * 根据天干获取五行
   */
  getElementByGan(gan) {
    const ganElementMap = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };
    return ganElementMap[gan] || '未知';
  }

  /**
   * 根据地支获取五行
   */
  getElementByZhi(zhi) {
    const zhiElementMap = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
    return zhiElementMap[zhi] || '未知';
  }

  /**
   * 分析小运强度
   */
  analyzeStrength(gan, zhi) {
    // 简化的强度分析
    const ganElement = this.getElementByGan(gan);
    const zhiElement = this.getElementByZhi(zhi);
    
    if (ganElement === zhiElement) {
      return '强旺';
    } else if (this.isElementSupport(ganElement, zhiElement)) {
      return '中等';
    } else {
      return '偏弱';
    }
  }

  /**
   * 分析运势倾向
   */
  analyzeTendency(gan, zhi, age) {
    // 根据年龄和干支特性分析
    if (age <= 3) {
      return '启蒙期，宜培养基础';
    } else if (age <= 6) {
      return '成长期，宜开发潜能';
    } else {
      return '发展期，宜全面培养';
    }
  }

  /**
   * 生成建议
   */
  generateAdvice(gan, zhi, age) {
    const ganElement = this.getElementByGan(gan);
    const zhiElement = this.getElementByZhi(zhi);
    
    return `${age}岁${ganElement}${zhiElement}运，宜注重${ganElement}性特质的培养，避免过度${this.getConflictElement(ganElement)}性活动。`;
  }

  /**
   * 判断五行是否相生
   */
  isElementSupport(element1, element2) {
    const supportMap = {
      '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
    };
    return supportMap[element1] === element2;
  }

  /**
   * 获取相冲五行
   */
  getConflictElement(element) {
    const conflictMap = {
      '木': '金', '火': '水', '土': '木', '金': '火', '水': '土'
    };
    return conflictMap[element] || element;
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MinorFortuneCalculator;
} else if (typeof window !== 'undefined') {
  window.MinorFortuneCalculator = MinorFortuneCalculator;
}

console.log('📦 小运计算系统模块加载完成');
