以下是基于“李淳风六壬时课”（小六壬）的占卜小程序模块完整需求方案，涵盖核心功能、算法逻辑、数据转换及交互设计：

---

### **一、模块核心需求**
#### **1. 功能定位**
- **场景**：用户遇小事犹豫不决时（如丢物、出行、谈判、感情），提供快速吉凶占卜
- **原则**：遵循“无事不占，一事一占”的传统规则
- **输出**：吉凶判断 + 行动建议 + 应期（事情发生时间）

#### **2. 核心功能流程**
```mermaid
graph TD
A[用户输入] --> B{选择占卜方式}
B --> C[传统时间法]
B --> D[数字取数法]
C --> E[阳历转阴历/真太阳时]
D --> F[输入3个数字/汉字]
E --> G[月→日→时起卦]
F --> H[数字转六神]
G & H --> I[匹配六神结果]
I --> J[生成占卜报告]
```

---

### **二、核心算法实现**
#### **1. 时间转换（传统法必用）**
- **阳历转阴历**：
  - 使用第三方库（如`lunar-javascript`）实现公历→农历自动转换。
  - 示例：2025-07-18 → 农历乙巳年六月廿四。
- **真太阳时校正**：
  - 根据用户地理位置（GPS获取经纬度）计算当地真太阳时。
  - 公式：`真太阳时 = 北京时间 + 时差 + 经度差`（经度差 = (120° - 用户经度)×4分钟）。
- **时辰划分**：
  - 将24小时转换为12时辰：
    ```javascript
    // 时辰对照表
    const hoursToShichen = {
      '23-1': '子时', '1-3': '丑时', '3-5': '寅时',
      '5-7': '卯时', '7-9': '辰时', '9-11': '巳时',
      '11-13': '午时', '13-15': '未时', '15-17': '申时',
      '17-19': '酉时', '19-21': '戌时', '21-23': '亥时'
    }
    ```

#### **2. 占卜计算逻辑**
- **传统起卦法（月→日→时）**：
  1. **定月份位**：从`大安`起正月，顺时针数至所求月（例：三月 → 正月大安→二月留连→三月速喜）。
  2. **定日辰位**：在月份位起初一，数至所求日（例：初五 → 初一速喜→初二赤口→初三小吉→初四空亡→初五大安）。
  3. **定时辰位**：在日辰位起子时，数至所求时辰（例：辰时 → 子时大安→丑时留连→寅时速喜→卯时赤口→辰时小吉）。
- **数字取数法（变通法）**：
  - 用户输入3个数字（或汉字转笔画），求和后按6取余：
    ```javascript
    const gods = ['小吉', '空亡', '大安', '留连', '速喜', '赤口']; // 余数0~5对应
    const number = (a+b+c) % 6; // 例：数字1+5+1+3+8=18 → 18%6=0 → 小吉
    ```

#### **3. 六神吉凶对照表**
| 六神   | 五行/神兽 | 吉凶 | 应期（日） | 关键断语                          | 行动建议                     |
|----------|------------|--------|--------------|------------------------------------------------------------|------------------------------|
| **大安** | 木/青龙    | 大吉   | 1、5、7      | 事事昌，失物去不远，宅舍安                                | 宜守成，求财向西南           |
| **留连** | 水/玄武    | 中凶   | 2、8、10     | 事难成，去者未回程，防口舌                                | 失物急寻南方，事务宜缓       |
| **速喜** | 火/朱雀    | 大吉   | 3、6、9      | 喜来临，求财向南行，行人至                                | 立即行动，失物问路人         |
| **赤口** | 金/白虎    | 大凶   | 4、7、10     | 主口舌，官非要防，行人有惊慌                              | 避免冲突，失物速寻           |
| **小吉** | 木/六合    | 中吉   | 1、5、7      | 最吉昌，阴人报喜，失物在西南                              | 谈判有利，婚恋可成           |
| **空亡** | 土/勾陈    | 大凶   | 3、6、9      | 事不祥，求财无利，行人有灾                                | 谨慎决策，宜祈福化解         |

---

### **三、功能扩展设计**
1. **个性化解读**：
   - 用户输入问题类型（如“失物”“求财”），系统匹配结果中的对应诗句。
   - 例：选择“失物”+结果“留连” → 高亮显示“失物南方见，急讨方称心”。
   
2. **应期可视化**：
   - 生成时间轴：标注吉凶高峰日（如“速喜”第3/6/9日宜行动）。

3. **异常处理**：
   - 农历三十/廿九：自动识别月份最后一日。
   - 23:00-0:00：归属次日子时，需日期+1校正。

---

### **四、交互原型示例**
#### **用户界面流程**
1. **首页**：  
   - 按钮A：“按时间占卜”（需授权定位获取真太阳时）  
   - 按钮B：“按数字占卜”（输入3个数字或汉字）  

2. **结果页**：  
   - 六神图标（颜色区分：吉→绿色/凶→红色）  
   - 核心断语（如“速喜：求财向南行，失物当日寻”）  
   - 行动建议（如“宜立即行动，3日内有成”）  
   - 分享按钮（生成海报含诗句与二维码）  

---

### **五、数据验证与来源**
- **阴历转换**：使用权威库`lunar-javascript`（支持1900-2100年）。
- **真太阳时**：采用国际经纬度计算标准（需用户授权位置）。
- **断语来源**：综合《玉匣记》及多个民间传承版本，确保文本一致性。

---

### **六、注意事项**
1. 时间占卜必须用农历+真太阳时（避免时区误差）。
2. 同一问题一日内不重复占（提示“再占不验”）。
3. 凶卦（如空亡）需搭配化解建议（如“宜祈福禳解”）。

> 通过此设计，用户可在1分钟内完成从提问到获取吉凶指引的全流程，兼顾传统易学严谨性与现代交互便捷性。