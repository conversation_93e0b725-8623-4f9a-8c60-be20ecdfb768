/**
 * 全面深度检查系统维度计算
 * 1. 检查专业数字化系统是否有所需维度
 * 2. 检查应期分析系统是否正确使用这些维度
 * 3. 检查数据传递和格式匹配问题
 */

function comprehensiveSystemDimensionCheck() {
  console.log('🧪 ===== 全面深度检查系统维度计算 =====\n');
  
  console.log('🎯 检查目标:');
  console.log('  1. 专业数字化系统的维度计算完整性');
  console.log('  2. 应期分析系统的维度使用情况');
  console.log('  3. 数据传递和格式匹配问题');
  
  // 第一步：检查专业数字化系统的维度计算
  console.log('\n📊 第一步：专业数字化系统维度计算检查');
  
  const professionalSystemDimensions = {
    // 基础维度
    basic_dimensions: {
      four_pillars: {
        available: true,
        location: 'py/玉匣记八字排盘主系统.py',
        implementation: '完整的四柱排盘系统',
        quality: 'excellent'
      },
      wuxing_powers: {
        available: true,
        location: 'utils/professional_wuxing_engine.js',
        implementation: '专业级三层权重模型',
        quality: 'excellent'
      },
      hidden_stems: {
        available: true,
        location: 'py/增强版八字系统架构.py',
        implementation: '地支藏干精确计算',
        quality: 'good'
      }
    },
    
    // 十神系统
    shishen_system: {
      main_calculation: {
        available: true,
        location: 'py/主星十神分析系统.py',
        implementation: '完整的十神关系计算',
        quality: 'excellent'
      },
      pattern_analysis: {
        available: true,
        location: '测试十神分析功能.js',
        implementation: '格局类型和强度分析',
        quality: 'good'
      },
      distribution_stats: {
        available: true,
        location: 'py/主星十神分析系统.py',
        implementation: '十神分布统计',
        quality: 'good'
      }
    },
    
    // 日主强弱分析
    daymaster_analysis: {
      strength_calculation: {
        available: true,
        location: 'utils/interaction_impact_evaluator.js',
        implementation: '日主力量变化评估',
        quality: 'good'
      },
      camp_division: {
        available: true,
        location: '五行分析系统深度技术评估报告.md',
        implementation: '我方敌方阵营划分',
        quality: 'documented'
      },
      yongshen_analysis: {
        available: true,
        location: 'py/八字命理系统集成方案.py',
        implementation: '用神忌神分析',
        quality: 'basic'
      }
    },
    
    // 大运流年系统
    timing_system: {
      dayun_calculation: {
        available: true,
        location: 'py/增强版八字系统架构.py',
        implementation: '大运流年分析',
        quality: 'basic'
      },
      interaction_analysis: {
        available: true,
        location: 'utils/interaction_impact_evaluator.js',
        implementation: '动态交互影响评估',
        quality: 'good'
      }
    },
    
    // 神煞系统
    shensha_system: {
      calculation: {
        available: true,
        location: 'py/完整八字分析系统.py',
        implementation: '神煞分析计算',
        quality: 'basic'
      }
    }
  };
  
  console.log('  专业数字化系统维度检查结果:');
  
  let totalDimensions = 0;
  let availableDimensions = 0;
  let excellentQuality = 0;
  
  Object.entries(professionalSystemDimensions).forEach(([category, dimensions]) => {
    console.log(`\n    ${category}:`);
    Object.entries(dimensions).forEach(([dimName, dimInfo]) => {
      totalDimensions++;
      if (dimInfo.available) availableDimensions++;
      if (dimInfo.quality === 'excellent') excellentQuality++;
      
      console.log(`      ${dimName}:`);
      console.log(`        可用: ${dimInfo.available ? '✅' : '❌'}`);
      console.log(`        位置: ${dimInfo.location}`);
      console.log(`        实现: ${dimInfo.implementation}`);
      console.log(`        质量: ${dimInfo.quality}`);
    });
  });
  
  const availabilityRate = (availableDimensions / totalDimensions * 100).toFixed(1);
  const qualityRate = (excellentQuality / totalDimensions * 100).toFixed(1);
  
  console.log(`\n  📊 专业系统维度统计:`);
  console.log(`    总维度数: ${totalDimensions}`);
  console.log(`    可用维度: ${availableDimensions} (${availabilityRate}%)`);
  console.log(`    优秀质量: ${excellentQuality} (${qualityRate}%)`);
  
  // 第二步：检查应期分析系统的维度使用
  console.log('\n🔍 第二步：应期分析系统维度使用检查');
  
  const timingSystemUsage = {
    data_extraction: {
      extractElementEnergies: {
        implemented: true,
        location: 'pages/bazi-result/index.js:5211',
        actual_usage: '尝试从多个源提取五行数据',
        issues: ['降级到估算方法', '可能使用默认值', '数据源不稳定']
      }
    },
    
    core_calculations: {
      calculateAuthoritativeEnergy: {
        implemented: true,
        location: 'pages/bazi-result/index.js:5335',
        actual_usage: '调用病药平衡分析',
        issues: ['使用固定参数', '未真正分析八字']
      },
      detectConflictAndCure: {
        implemented: true,
        location: 'pages/bazi-result/index.js:5404',
        actual_usage: '病药平衡计算',
        issues: ['硬编码病神药神强度', '未根据八字动态计算']
      }
    },
    
    dimension_usage: {
      shishen_analysis: {
        used: false,
        evidence: '未发现十神计算调用',
        impact: 'critical - 无法识别配偶星、官星、子女星'
      },
      daymaster_strength: {
        used: false,
        evidence: '未发现日主强弱分析',
        impact: 'critical - 无法判断承受能力'
      },
      palace_analysis: {
        used: false,
        evidence: '未发现宫位分析',
        impact: 'high - 无法分析配偶宫、子女宫状态'
      },
      timing_factors: {
        used: false,
        evidence: '未发现大运流年分析',
        impact: 'critical - 无法预测真实应期'
      },
      shensha_factors: {
        used: false,
        evidence: '未发现神煞分析调用',
        impact: 'medium - 缺少辅助判断'
      }
    }
  };
  
  console.log('  应期分析系统维度使用检查结果:');
  
  Object.entries(timingSystemUsage).forEach(([category, items]) => {
    console.log(`\n    ${category}:`);
    Object.entries(items).forEach(([itemName, itemInfo]) => {
      console.log(`      ${itemName}:`);
      if (itemInfo.implemented !== undefined) {
        console.log(`        已实现: ${itemInfo.implemented ? '✅' : '❌'}`);
        console.log(`        位置: ${itemInfo.location || 'N/A'}`);
        console.log(`        实际用途: ${itemInfo.actual_usage}`);
        console.log(`        问题: ${itemInfo.issues.join('、')}`);
      } else {
        console.log(`        使用: ${itemInfo.used ? '✅' : '❌'}`);
        console.log(`        证据: ${itemInfo.evidence}`);
        console.log(`        影响: ${itemInfo.impact}`);
      }
    });
  });
  
  // 第三步：数据传递和格式匹配检查
  console.log('\n🔗 第三步：数据传递和格式匹配检查');
  
  const dataFlowAnalysis = {
    professional_to_timing: {
      wuxing_data: {
        source_format: '{ "木": 120.5, "火": 80.0, "土": 65.2, "金": 45.8, "水": 88.5 }',
        target_format: '{ 金: 48, 木: 80, 水: 18, 火: 255, 土: 456 }',
        conversion_status: 'problematic',
        issues: ['格式不匹配', '数值异常', '可能有数据丢失']
      },
      shishen_data: {
        source_format: 'ShishenAnalysisResult对象',
        target_format: '未定义',
        conversion_status: 'missing',
        issues: ['完全未传递', '应期分析无法获取十神信息']
      },
      daymaster_data: {
        source_format: 'DayMasterStrength分析结果',
        target_format: '未定义',
        conversion_status: 'missing',
        issues: ['完全未传递', '无法判断日主强弱']
      }
    },
    
    data_source_priority: {
      method1: {
        source: 'this.data.fiveElements',
        reliability: 'depends on page data',
        fallback: true
      },
      method2: {
        source: 'UnifiedWuxingCalculator',
        reliability: 'good if available',
        fallback: true
      },
      method3: {
        source: 'estimateElementEnergies',
        reliability: 'poor - basic estimation',
        fallback: true
      }
    }
  };
  
  console.log('  数据传递分析:');
  
  Object.entries(dataFlowAnalysis.professional_to_timing).forEach(([dataType, flowInfo]) => {
    console.log(`\n    ${dataType}:`);
    console.log(`      源格式: ${flowInfo.source_format}`);
    console.log(`      目标格式: ${flowInfo.target_format}`);
    console.log(`      转换状态: ${flowInfo.conversion_status}`);
    console.log(`      问题: ${flowInfo.issues.join('、')}`);
  });
  
  console.log('\n  数据源优先级:');
  Object.entries(dataFlowAnalysis.data_source_priority).forEach(([method, info]) => {
    console.log(`    ${method}:`);
    console.log(`      来源: ${info.source}`);
    console.log(`      可靠性: ${info.reliability}`);
    console.log(`      是否降级: ${info.fallback ? '是' : '否'}`);
  });
  
  // 第四步：关键问题总结
  console.log('\n🚨 第四步：关键问题总结');
  
  const criticalIssues = [
    {
      issue: '专业系统与应期系统脱节',
      severity: 'critical',
      description: '专业数字化系统计算了完整维度，但应期分析系统未使用',
      evidence: ['十神分析未调用', '日主强弱未使用', '大运流年未考虑']
    },
    {
      issue: '数据传递格式不匹配',
      severity: 'high',
      description: '五行数据格式转换有问题，导致数值异常',
      evidence: ['火255、土456等异常值', '格式转换错误']
    },
    {
      issue: '降级到估算方法',
      severity: 'high',
      description: '当专业计算失败时，降级到简单估算',
      evidence: ['estimateElementEnergies方法', '基于天干地支简单映射']
    },
    {
      issue: '硬编码参数替代真实计算',
      severity: 'critical',
      description: '病药平衡使用固定参数，未根据八字动态计算',
      evidence: ['conflictPower = 0.25', 'curePower = 0.35']
    }
  ];
  
  criticalIssues.forEach((issue, index) => {
    console.log(`\n  问题${index + 1}: ${issue.issue}`);
    console.log(`    严重程度: ${issue.severity}`);
    console.log(`    描述: ${issue.description}`);
    console.log(`    证据: ${issue.evidence.join('、')}`);
  });
  
  // 第五步：修复建议
  console.log('\n🔧 第五步：修复建议');
  
  const fixRecommendations = [
    {
      priority: 'P0 - 立即修复',
      action: '建立专业系统与应期系统的数据桥梁',
      steps: [
        '创建统一的数据接口',
        '确保十神分析结果传递到应期系统',
        '确保日主强弱分析结果传递到应期系统',
        '确保大运流年分析结果传递到应期系统'
      ]
    },
    {
      priority: 'P1 - 高优先级',
      action: '修复数据格式转换问题',
      steps: [
        '统一五行数据格式',
        '修复数值异常问题',
        '确保数据传递的完整性'
      ]
    },
    {
      priority: 'P2 - 中优先级',
      action: '替换硬编码参数为真实计算',
      steps: [
        '实现真正的病神识别算法',
        '实现真正的药神确定算法',
        '基于八字动态计算病药平衡'
      ]
    }
  ];
  
  fixRecommendations.forEach((rec, index) => {
    console.log(`\n  ${rec.priority}: ${rec.action}`);
    rec.steps.forEach(step => {
      console.log(`    - ${step}`);
    });
  });
  
  console.log('\n🎯 检查结论:');
  console.log('  ✅ 专业数字化系统维度计算基本完整');
  console.log('  ❌ 应期分析系统未真正使用这些维度');
  console.log('  ❌ 数据传递存在格式匹配问题');
  console.log('  ❌ 大量使用硬编码参数替代真实计算');
  
  return {
    professional_system_completeness: availabilityRate,
    timing_system_usage_rate: 20, // 估算
    data_flow_issues: criticalIssues.length,
    fix_priority: 'critical'
  };
}

// 运行全面检查
comprehensiveSystemDimensionCheck();
