// components/ec-canvas/ec-chart.js - ECharts图表组件辅助模块

/**
 * ECharts图表组件辅助函数
 * 提供雷达图、柱状图、饼图等多种图表类型
 * 用于测评结果的可视化展示
 */

// 导入ECharts库，这是一个轻量级的图表库
const echarts = require('./echarts');

/**
 * 初始化ECharts图表
 * @param {Object} canvas - canvas对象，用于绘制图表的画布元素
 * @param {Number} width - 宽度，图表的宽度像素值
 * @param {Number} height - 高度，图表的高度像素值
 * @param {Number} dpr - 设备像素比，用于高清适配
 * @param {Object} option - 图表配置选项，包含了图表的所有设置
 * @returns {Object} 图表实例，可以用于后续操作
 */
function initChart(canvas, width, height, dpr, option) {
  // 检查canvas是否存在，不存在则无法初始化图表
  if (!canvas) {
    console.error('Canvas element is missing for chart initialization');
    return null;
  }
  
  // 设置默认值，如果没有传入相应参数，则使用默认值
  width = width || 300; // 默认宽度为300像素
  height = height || 200; // 默认高度为200像素
  dpr = dpr || wx.getSystemInfoSync().pixelRatio; // 获取系统的设备像素比
  
  // 创建图表实例，初始化ECharts
  const chart = echarts.init(canvas, null, {
    width: width, // 设置图表宽度
    height: height, // 设置图表高度
    devicePixelRatio: dpr // 设置设备像素比，用于高清显示
  });
  
  // 设置图表配置，将canvas与chart关联并应用配置选项
  canvas.setChart(chart); // 将chart实例设置到canvas上
  chart.setOption(option || {}); // 应用图表配置，如果没有配置则使用空对象
  
  // 记录初始化成功的日志信息
  console.log('Chart initialized successfully with size:', width, 'x', height, 'dpr:', dpr);
  return chart; // 返回图表实例
}

/**
 * 创建雷达图配置选项
 * @param {Array} dimensions - 维度数据数组，包含各个维度的得分和属性
 * @param {Object} config - 配置选项，用于自定义雷达图的外观
 * @returns {Object} 雷达图配置选项，可直接用于图表初始化
 */
function createRadarOption(dimensions, config = {}) {
  // 验证维度数据是否有效，无效则使用默认值
  if (!dimensions || !Array.isArray(dimensions) || dimensions.length === 0) {
    console.warn('Invalid dimensions data for radar chart, using default values');
    // 默认维度数据，包含各心理维度的默认值
    dimensions = [
      { name: '学业压力', score: 50, percentage: 50, color: '#6C5CE7' },
      { name: '情绪问题', score: 50, percentage: 50, color: '#FF6B6B' },
      { name: '网络行为', score: 50, percentage: 50, color: '#0FB9B1' },
      { name: '社交关系', score: 50, percentage: 50, color: '#20BF6B' },
      { name: '自我认知', score: 50, percentage: 50, color: '#45AAF2' }
    ];
  }
  
  // 确保维度数据格式正确
  dimensions = dimensions.map(dim => ({
    name: dim.name || '未知维度',
    score: typeof dim.score === 'number' ? dim.score : 0,
    fullScore: typeof dim.fullScore === 'number' ? dim.fullScore : 100,
    percentage: typeof dim.percentage === 'number' ? dim.percentage : 
               (typeof dim.score === 'number' && typeof dim.fullScore === 'number' ? 
               (dim.score / dim.fullScore * 100) : 0),
    level: dim.level || 0,
    color: dim.color || '#6C5CE7'
  }));
  
  // 默认配置选项，包含雷达图的基本样式设定
  const defaultColors = ['#6C5CE7', '#FF6B6B', '#0FB9B1', '#20BF6B', '#45AAF2', '#F7B731']; // 默认颜色列表
  // 合并用户配置和默认配置
  config = Object.assign({
    shape: 'circle', // 雷达图形状，圆形
    splitNumber: 4, // 分割段数
    max: 100, // 最大值
    center: ['50%', '50%'], // 中心点位置
    radius: '60%', // 半径大小
    colors: defaultColors, // 使用默认颜色
    showLabels: true // 是否显示标签
  }, config);
  
  // 构建雷达图指示器，为每个维度创建指示器
  const indicator = dimensions.map(item => ({
    name: formatDimensionName(item.name || '未知', config.maxNameLength || 4), // 格式化维度名称
    max: item.fullScore || config.max || 100 // 设置最大值，优先使用维度自身的满分
  }));
  
  // 提取数据值，获取每个维度的得分
  const data = dimensions.map(item => item.score || 0);
  
  // 构建雷达图配置对象
  return {
    color: config.colors || defaultColors, // 设置颜色
    backgroundColor: 'rgba(255, 255, 255, 0.8)', // 设置背景色，略微透明
    tooltip: { // 提示框配置
      trigger: 'item', // 触发类型
      formatter: function(params) { // 格式化提示内容
        const dimensionIndex = params.dataIndex; // 获取维度索引
        const dimension = dimensions[dimensionIndex]; // 获取对应维度
        if (dimension) {
          // 显示维度名称、分数和百分比
          return `${dimension.name}: ${dimension.score}分 (${dimension.percentage || Math.round((dimension.score / (dimension.fullScore || 100)) * 100)}%)`;
        }
        return `${params.name}: ${params.value}`; // 默认格式
      },
      textStyle: {
        fontSize: 12
      }
    },
    radar: { // 雷达图基本设置
      shape: config.shape, // 形状
      center: config.center, // 中心位置
      radius: config.radius, // 半径
      splitNumber: config.splitNumber, // 分割段数
      nameGap: 10, // 名称与轴线的距离
      splitArea: { // 分割区域样式
        show: true,
        areaStyle: {
          color: ['rgba(240, 240, 250, 0.5)', 'rgba(250, 250, 255, 0.5)'] // 交替颜色，更柔和
        }
      },
      axisLine: { // 轴线样式
        lineStyle: {
          color: 'rgba(97, 78, 215, 0.2)' // 轴线颜色，改用主题色
        }
      },
      splitLine: { // 分割线样式
        lineStyle: {
          color: 'rgba(97, 78, 215, 0.2)' // 分割线颜色，改用主题色
        }
      },
      axisName: {
        color: '#333',
        fontSize: 12, // 名称字体大小
        padding: [0, 5] // 名称内边距
      },
      indicator: indicator // 设置指示器
    },
    series: [{ // 系列数据
      name: '维度评分', // 系列名称
      type: 'radar', // 类型为雷达图
      emphasis: { // 强调状态样式
        lineStyle: {
          width: 4 // 高亮时线条宽度
        }
      },
      data: [{ // 数据项
        value: data, // 得分数据
        name: '评分', // 数据名称
        symbol: 'circle', // 拐点符号
        symbolSize: 6, // 拐点大小
        areaStyle: { // 区域填充样式
          color: 'rgba(97, 78, 215, 0.4)' // 填充颜色及透明度，使用主题色
        },
        lineStyle: { // 线条样式
          width: 2, // 线条宽度
          color: 'rgba(97, 78, 215, 0.8)' // 线条颜色，使用主题色
        },
        itemStyle: { // 项目样式
          color: '#614ED7' // 拐点颜色
        }
      }]
    }]
  };
}

/**
 * 格式化维度名称，处理名称太长的情况
 * @param {String} name - 要格式化的维度名称
 * @param {Number} maxLength - 最大长度，默认为6个字符
 * @returns {String} 格式化后的名称
 */
function formatDimensionName(name, maxLength = 6) {
  if (!name) {
    return '未知';
  }
  
  // 如果名称长度超过最大长度，尝试换行或截断
  if (name.length > maxLength) {
    // 根据名称长度选择不同的处理方式
    if (name.length <= maxLength + 2) {
      // 对于略长的名称，直接返回，雷达图会自动调整
      return name;
    } else {
      // 对于很长的名称，截断并添加省略号
      return name.substring(0, maxLength) + '..';
    }
  }
  
  // 返回原始名称
  return name;
}

/**
 * 创建柱状图配置选项
 * @param {Array} dimensions - 维度数据数组，包含各维度的得分和属性
 * @param {Object} config - 配置选项，用于自定义柱状图的外观
 * @returns {Object} 柱状图配置选项，可直接用于图表初始化
 */
function createBarOption(dimensions, config = {}) {
  // 验证维度数据是否有效，无效则使用默认值
  if (!dimensions || !Array.isArray(dimensions) || dimensions.length === 0) {
    console.warn('Invalid dimensions data for bar chart');
    // 默认维度数据，提供各心理维度的默认值
    dimensions = [
      { name: '学业压力', score: 50, percentage: 50, color: '#6C5CE7' },
      { name: '情绪问题', score: 50, percentage: 50, color: '#FF6B6B' },
      { name: '网络行为', score: 50, percentage: 50, color: '#0FB9B1' },
      { name: '社交关系', score: 50, percentage: 50, color: '#20BF6B' },
      { name: '自我认知', score: 50, percentage: 50, color: '#45AAF2' }
    ];
  }
  
  // 默认颜色列表，用于为各维度设置颜色
  const defaultColors = ['#6C5CE7', '#FF6B6B', '#0FB9B1', '#20BF6B', '#45AAF2', '#F7B731'];
  
  // 提取数据，分别获取x轴数据、系列数据和颜色
  const xAxisData = dimensions.map(item => item.name); // x轴数据为维度名称
  const seriesData = dimensions.map(item => item.percentage || item.score || 0); // 系列数据为百分比或得分
  const colors = dimensions.map((item, index) => item.color || defaultColors[index % defaultColors.length]); // 颜色设置
  
  // 构建柱状图配置
  return {
    color: colors, // 设置颜色
    tooltip: { // 提示框配置
      trigger: 'axis', // 触发类型
      axisPointer: { // 坐标轴指示器
        type: 'shadow' // 阴影指示器
      }
    },
    grid: { // 网格设置
      left: '3%', // 左边距
      right: '4%', // 右边距
      bottom: '3%', // 下边距
      top: '3%', // 上边距
      containLabel: true // 包含坐标轴标签
    },
    xAxis: { // x轴设置
      type: 'category', // 类型为类目轴
      data: xAxisData, // 类目数据
      axisLine: { // 坐标轴线
        lineStyle: {
          color: '#ddd' // 颜色
        }
      },
      axisTick: { // 坐标轴刻度
        alignWithLabel: true // 刻度与标签对齐
      },
      axisLabel: { // 坐标轴标签
        interval: 0, // 全部显示
        rotate: dimensions.length > 5 ? 30 : 0, // 当维度超过5个时旋转标签
        fontSize: 10 // 字体大小
      }
    },
    yAxis: { // y轴设置
      type: 'value', // 类型为数值轴
      max: config.max || 100, // 最大值
      axisLine: { // 坐标轴线
        lineStyle: {
          color: '#ddd' // 颜色
        }
      },
      splitLine: { // 分割线
        lineStyle: {
          color: '#eee' // 颜色
        }
      }
    },
    series: [{ // 系列数据
      name: '评分', // 系列名称
      type: 'bar', // 类型为柱状图
      barWidth: '60%', // 柱条宽度
      data: seriesData.map((value, index) => ({ // 数据项
        value: value, // 值
        itemStyle: { // 项目样式
          color: colors[index] // 设置颜色
        }
      }))
    }]
  };
}

/**
 * 创建饼图配置选项
 * @param {Array} dimensions - 维度数据数组，包含各维度的得分和属性
 * @param {Object} config - 配置选项，用于自定义饼图的外观
 * @returns {Object} 饼图配置选项，可直接用于图表初始化
 */
function createPieOption(dimensions, config = {}) {
  // 验证维度数据是否有效，无效则使用默认值
  if (!dimensions || !Array.isArray(dimensions) || dimensions.length === 0) {
    console.warn('创建饼图: 无效的维度数据，使用默认值');
    // 默认维度数据，提供各心理维度的默认值
    dimensions = [
      { name: '学业压力', score: 50, percentage: 50, color: '#6C5CE7' },
      { name: '情绪问题', score: 50, percentage: 50, color: '#FF6B6B' },
      { name: '网络行为', score: 50, percentage: 50, color: '#0FB9B1' },
      { name: '社交关系', score: 50, percentage: 50, color: '#20BF6B' },
      { name: '自我认知', score: 50, percentage: 50, color: '#45AAF2' }
    ];
  }
  
  // 默认颜色列表，用于为各维度设置颜色
  const defaultColors = ['#6C5CE7', '#FF6B6B', '#0FB9B1', '#20BF6B', '#45AAF2', '#F7B731'];
  
  // 合并用户配置和默认配置
  config = Object.assign({
    roseType: false, // 不使用南丁格尔玫瑰图
    center: ['50%', '50%'], // 中心点位置
    radius: ['30%', '70%'], // 内外半径
    colors: defaultColors, // 使用默认颜色
    showLabels: true, // 是否显示标签
    itemGap: 10, // 图例间隔
    selectedMode: false, // 禁用选择模式
    animation: true // 启用动画
  }, config);
  
  // 构建饼图数据
  const seriesData = dimensions.map((item, index) => {
    // 计算值，优先使用percentage，其次使用score，都没有则为0
    const value = item.percentage || (item.score ? (item.score / (item.fullScore || 100) * 100) : 0);
    
    return {
      name: item.name || `维度${index + 1}`, // 名称
      value: Math.round(value), // 四舍五入取整
      itemStyle: {
        color: item.color || config.colors[index % config.colors.length] // 使用维度颜色或配置中的颜色
      }
    };
  });
  
  // 构建饼图配置对象
  return {
    backgroundColor: 'transparent', // 透明背景
    color: config.colors, // 设置颜色
    tooltip: { // 提示框配置
      trigger: 'item', // 触发类型
      formatter: function(params) { // 格式化提示内容
        // 显示维度名称和百分比
        return `${params.name}: ${params.value}%`;
      },
      textStyle: {
        fontSize: 12 // 字体大小
      }
    },
    legend: { // 图例配置
      orient: 'horizontal', // 水平布局
      bottom: '5%', // 位置在底部
      itemGap: config.itemGap, // 图例间隔
      selectedMode: config.selectedMode, // 选择模式
      textStyle: {
        color: '#333', // 文本颜色
        fontSize: 12 // 字体大小
      }
    },
    series: [{ // 系列数据
      name: '维度占比', // 系列名称
      type: 'pie', // 类型为饼图
      center: config.center, // 中心位置
      radius: config.radius, // 半径设置
      roseType: config.roseType ? 'radius' : false, // 南丁格尔玫瑰图设置
      avoidLabelOverlap: true, // 避免标签重叠
      itemStyle: { // 项目样式
        borderRadius: 6, // 边框圆角
        borderColor: '#fff', // 边框颜色
        borderWidth: 2 // 边框宽度
      },
      label: { // 标签设置
        show: config.showLabels, // 是否显示标签
        formatter: '{b}: {d}%', // 格式化标签内容
        fontSize: 12, // 字体大小
        position: 'outside' // 标签位置在外部
      },
      emphasis: { // 强调状态样式
        label: {
          show: true, // 显示标签
          fontSize: 14, // 字体大小
          fontWeight: 'bold' // 字体粗细
        },
        itemStyle: {
          shadowBlur: 10, // 阴影模糊大小
          shadowOffsetX: 0, // 阴影X偏移
          shadowColor: 'rgba(0, 0, 0, 0.5)' // 阴影颜色
        }
      },
      data: seriesData // 数据
    }]
  };
}

// 维度颜色映射，用于根据维度ID获取对应的颜色
const dimensionColors = {
  'academicPressure': '#5470c6', // 学业压力颜色
  'emotionalIssues': '#91cc75', // 情绪问题颜色
  'internetBehavior': '#fac858', // 网络行为颜色
  'socialRelationship': '#ee6666', // 社交关系颜色
  'selfAwareness': '#73c0de' // 自我意识颜色
};

// 维度文本映射，用于将维度ID转换为中文名称
const dimensionTexts = {
  'academicPressure': '学业压力', // 学业压力的中文名称
  'emotionalIssues': '情绪问题', // 情绪问题的中文名称
  'internetBehavior': '网络行为', // 网络行为的中文名称
  'socialRelationship': '社交关系', // 社交关系的中文名称
  'selfAwareness': '自我意识' // 自我意识的中文名称
};

// 导出模块中的函数和常量，供外部使用
module.exports = {
  initChart, // 图表初始化函数
  createRadarOption, // 创建雷达图配置函数
  createBarOption, // 创建柱状图配置函数
  createPieOption, // 创建饼图配置函数
  dimensionColors, // 维度颜色映射
  dimensionTexts // 维度文本映射
};
