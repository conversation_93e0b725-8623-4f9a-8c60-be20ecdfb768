/**
 * 数据库扩展计划
 * 规划如何达到200位历史名人的目标
 */

class DatabaseExpansionPlan {
  constructor() {
    this.currentCount = 37; // 当前已有名人数量
    this.targetCount = 200; // 目标名人数量
    this.remainingCount = this.targetCount - this.currentCount;
    
    // 按朝代分类的历史名人扩展计划（基于真实史书记录）
    this.expansionPlan = {
      "先秦时期": {
        target: 25,
        figures: [
          // 春秋战国思想家
          { name: "荀子", source: "《史记·孟子荀卿列传》" },
          { name: "墨子", source: "《史记·孟子荀卿列传》" },
          { name: "庄子", source: "《史记·老子韩非列传》" },
          { name: "韩非子", source: "《史记·老子韩非列传》" },
          { name: "鬼谷子", source: "《史记·苏秦列传》" },
          { name: "苏秦", source: "《史记·苏秦列传》" },
          { name: "张仪", source: "《史记·张仪列传》" },
          { name: "范蠡", source: "《史记·货殖列传》" },
          { name: "文种", source: "《史记·越王勾践世家》" },
          { name: "伍子胥", source: "《史记·伍子胥列传》" },
          // 春秋五霸
          { name: "齐桓公", source: "《史记·齐太公世家》" },
          { name: "晋文公", source: "《史记·晋世家》" },
          { name: "楚庄王", source: "《史记·楚世家》" },
          { name: "吴王阖闾", source: "《史记·吴太伯世家》" },
          { name: "越王勾践", source: "《史记·越王勾践世家》" },
          // 战国七雄君主
          { name: "秦始皇", source: "《史记·秦始皇本纪》" },
          { name: "赵武灵王", source: "《史记·赵世家》" },
          { name: "燕昭王", source: "《史记·燕召公世家》" },
          { name: "齐威王", source: "《史记·田敬仲完世家》" },
          { name: "魏文侯", source: "《史记·魏世家》" },
          // 其他重要人物
          { name: "扁鹊", source: "《史记·扁鹊仓公列传》" },
          { name: "鲁班", source: "《墨子》记载" },
          { name: "屈原", source: "《史记·屈原贾生列传》" },
          { name: "廉颇", source: "《史记·廉颇蔺相如列传》" },
          { name: "蔺相如", source: "《史记·廉颇蔺相如列传》" }
        ]
      },
      
      "秦汉时期": {
        target: 30,
        figures: [
          // 西汉皇帝
          { name: "汉文帝", source: "《史记·孝文本纪》" },
          { name: "汉景帝", source: "《史记·孝景本纪》" },
          { name: "汉武帝", source: "《史记·孝武本纪》" },
          { name: "汉宣帝", source: "《汉书·宣帝纪》" },
          // 西汉名臣
          { name: "董仲舒", source: "《汉书·董仲舒传》" },
          { name: "晁错", source: "《史记·袁盎晁错列传》" },
          { name: "主父偃", source: "《史记·平津侯主父列传》" },
          { name: "公孙弘", source: "《史记·平津侯主父列传》" },
          { name: "汲黯", source: "《史记·汲郑列传》" },
          { name: "郑当时", source: "《史记·汲郑列传》" },
          // 东汉皇帝
          { name: "光武帝", source: "《后汉书·光武帝纪》" },
          { name: "汉明帝", source: "《后汉书·明帝纪》" },
          { name: "汉章帝", source: "《后汉书·章帝纪》" },
          // 东汉名臣
          { name: "邓禹", source: "《后汉书·邓禹传》" },
          { name: "冯异", source: "《后汉书·冯异传》" },
          { name: "岑彭", source: "《后汉书·岑彭传》" },
          { name: "马援", source: "《后汉书·马援传》" },
          { name: "班超", source: "《后汉书·班超传》" },
          // 文学家
          { name: "贾谊", source: "《史记·屈原贾生列传》" },
          { name: "东方朔", source: "《汉书·东方朔传》" },
          { name: "扬雄", source: "《汉书·扬雄传》" },
          // 其他重要人物
          { name: "陈平", source: "《史记·陈丞相世家》" },
          { name: "周亚夫", source: "《史记·绛侯周勃世家》" },
          { name: "窦婴", source: "《史记·魏其武安侯列传》" },
          { name: "田蚡", source: "《史记·魏其武安侯列传》" },
          { name: "李广", source: "《史记·李将军列传》" },
          { name: "程不识", source: "《史记·李将军列传》" },
          { name: "苏武", source: "《汉书·苏武传》" },
          { name: "张骞", source: "《史记·大宛列传》" },
          { name: "王莽", source: "《汉书·王莽传》" }
        ]
      },
      
      "魏晋南北朝": {
        target: 35,
        figures: [
          // 三国蜀汉
          { name: "马超", source: "《三国志·蜀书·马超传》" },
          { name: "黄忠", source: "《三国志·蜀书·黄忠传》" },
          { name: "魏延", source: "《三国志·蜀书·魏延传》" },
          { name: "姜维", source: "《三国志·蜀书·姜维传》" },
          { name: "法正", source: "《三国志·蜀书·法正传》" },
          { name: "庞统", source: "《三国志·蜀书·庞统传》" },
          // 三国曹魏
          { name: "曹丕", source: "《三国志·魏书·文帝纪》" },
          { name: "曹植", source: "《三国志·魏书·陈思王植传》" },
          { name: "夏侯惇", source: "《三国志·魏书·夏侯惇传》" },
          { name: "夏侯渊", source: "《三国志·魏书·夏侯渊传》" },
          { name: "张辽", source: "《三国志·魏书·张辽传》" },
          { name: "徐晃", source: "《三国志·魏书·徐晃传》" },
          { name: "张郃", source: "《三国志·魏书·张郃传》" },
          { name: "于禁", source: "《三国志·魏书·于禁传》" },
          { name: "乐进", source: "《三国志·魏书·乐进传》" },
          { name: "荀彧", source: "《三国志·魏书·荀彧传》" },
          { name: "荀攸", source: "《三国志·魏书·荀攸传》" },
          { name: "郭嘉", source: "《三国志·魏书·郭嘉传》" },
          { name: "贾诩", source: "《三国志·魏书·贾诩传》" },
          // 三国东吴
          { name: "孙权", source: "《三国志·吴书·吴主传》" },
          { name: "孙策", source: "《三国志·吴书·孙策传》" },
          { name: "孙坚", source: "《三国志·吴书·孙坚传》" },
          { name: "陆逊", source: "《三国志·吴书·陆逊传》" },
          { name: "吕蒙", source: "《三国志·吴书·吕蒙传》" },
          { name: "甘宁", source: "《三国志·吴书·甘宁传》" },
          { name: "黄盖", source: "《三国志·吴书·黄盖传》" },
          { name: "程普", source: "《三国志·吴书·程普传》" },
          { name: "太史慈", source: "《三国志·吴书·太史慈传》" },
          // 西晋
          { name: "晋武帝", source: "《晋书·武帝纪》" },
          { name: "羊祜", source: "《晋书·羊祜传》" },
          { name: "杜预", source: "《晋书·杜预传》" },
          { name: "王浚", source: "《晋书·王浚传》" },
          // 东晋
          { name: "晋元帝", source: "《晋书·元帝纪》" },
          { name: "王导", source: "《晋书·王导传》" },
          { name: "谢安", source: "《晋书·谢安传》" }
        ]
      },
      
      "隋唐五代": {
        target: 40,
        figures: [
          // 隋朝
          { name: "隋文帝", source: "《隋书·高祖纪》" },
          { name: "隋炀帝", source: "《隋书·炀帝纪》" },
          { name: "杨素", source: "《隋书·杨素传》" },
          { name: "韩擒虎", source: "《隋书·韩擒虎传》" },
          // 唐朝皇帝
          { name: "唐高祖", source: "《旧唐书·高祖纪》" },
          { name: "唐太宗", source: "《旧唐书·太宗纪》" },
          { name: "武则天", source: "《旧唐书·则天皇后纪》" },
          { name: "唐玄宗", source: "《旧唐书·玄宗纪》" },
          { name: "唐肃宗", source: "《旧唐书·肃宗纪》" },
          { name: "唐代宗", source: "《旧唐书·代宗纪》" },
          { name: "唐德宗", source: "《旧唐书·德宗纪》" },
          { name: "唐宪宗", source: "《旧唐书·宪宗纪》" },
          // 唐朝名臣
          { name: "房玄龄", source: "《旧唐书·房玄龄传》" },
          { name: "杜如晦", source: "《旧唐书·杜如晦传》" },
          { name: "魏征", source: "《旧唐书·魏征传》" },
          { name: "长孙无忌", source: "《旧唐书·长孙无忌传》" },
          { name: "尉迟恭", source: "《旧唐书·尉迟恭传》" },
          { name: "秦琼", source: "《旧唐书·秦琼传》" },
          { name: "程咬金", source: "《旧唐书·程咬金传》" },
          { name: "李靖", source: "《旧唐书·李靖传》" },
          { name: "薛仁贵", source: "《旧唐书·薛仁贵传》" },
          { name: "狄仁杰", source: "《旧唐书·狄仁杰传》" },
          { name: "姚崇", source: "《旧唐书·姚崇传》" },
          { name: "宋璟", source: "《旧唐书·宋璟传》" },
          { name: "张九龄", source: "《旧唐书·张九龄传》" },
          { name: "李林甫", source: "《旧唐书·李林甫传》" },
          { name: "杨国忠", source: "《旧唐书·杨国忠传》" },
          { name: "安禄山", source: "《旧唐书·安禄山传》" },
          { name: "史思明", source: "《旧唐书·史思明传》" },
          { name: "郭子仪", source: "《旧唐书·郭子仪传》" },
          { name: "李光弼", source: "《旧唐书·李光弼传》" },
          // 唐朝文学家
          { name: "王勃", source: "《旧唐书·王勃传》" },
          { name: "杨炯", source: "《旧唐书·杨炯传》" },
          { name: "卢照邻", source: "《旧唐书·卢照邻传》" },
          { name: "骆宾王", source: "《旧唐书·骆宾王传》" },
          { name: "陈子昂", source: "《旧唐书·陈子昂传》" },
          { name: "孟浩然", source: "《旧唐书·孟浩然传》" },
          { name: "王维", source: "《旧唐书·王维传》" },
          { name: "高适", source: "《旧唐书·高适传》" },
          { name: "岑参", source: "《旧唐书·岑参传》" },
          { name: "白居易", source: "《旧唐书·白居易传》" },
          { name: "元稹", source: "《旧唐书·元稹传》" }
        ]
      },
      
      "宋元时期": {
        target: 35,
        figures: [
          // 北宋皇帝
          { name: "宋太祖", source: "《宋史·太祖纪》" },
          { name: "宋太宗", source: "《宋史·太宗纪》" },
          { name: "宋真宗", source: "《宋史·真宗纪》" },
          { name: "宋仁宗", source: "《宋史·仁宗纪》" },
          { name: "宋神宗", source: "《宋史·神宗纪》" },
          { name: "宋哲宗", source: "《宋史·哲宗纪》" },
          { name: "宋徽宗", source: "《宋史·徽宗纪》" },
          { name: "宋钦宗", source: "《宋史·钦宗纪》" },
          // 南宋皇帝
          { name: "宋高宗", source: "《宋史·高宗纪》" },
          { name: "宋孝宗", source: "《宋史·孝宗纪》" },
          { name: "宋光宗", source: "《宋史·光宗纪》" },
          { name: "宋宁宗", source: "《宋史·宁宗纪》" },
          // 宋朝名臣
          { name: "赵普", source: "《宋史·赵普传》" },
          { name: "寇准", source: "《宋史·寇准传》" },
          { name: "范仲淹", source: "《宋史·范仲淹传》" },
          { name: "欧阳修", source: "《宋史·欧阳修传》" },
          { name: "司马光", source: "《宋史·司马光传》" },
          { name: "王安石", source: "《宋史·王安石传》" },
          { name: "包拯", source: "《宋史·包拯传》" },
          { name: "文天祥", source: "《宋史·文天祥传》" },
          { name: "陆游", source: "《宋史·陆游传》" },
          { name: "辛弃疾", source: "《宋史·辛弃疾传》" },
          { name: "朱熹", source: "《宋史·朱熹传》" },
          { name: "张载", source: "《宋史·张载传》" },
          { name: "程颢", source: "《宋史·程颢传》" },
          { name: "程颐", source: "《宋史·程颐传》" },
          { name: "邵雍", source: "《宋史·邵雍传》" },
          { name: "周敦颐", source: "《宋史·周敦颐传》" },
          // 元朝
          { name: "忽必烈", source: "《元史·世祖纪》" },
          { name: "耶律楚材", source: "《元史·耶律楚材传》" },
          { name: "刘秉忠", source: "《元史·刘秉忠传》" },
          { name: "关汉卿", source: "《录鬼簿》记载" },
          { name: "马致远", source: "《录鬼簿》记载" },
          { name: "白朴", source: "《录鬼簿》记载" },
          { name: "郑光祖", source: "《录鬼簿》记载" },
          { name: "赵孟頫", source: "《元史·赵孟頫传》" },
          { name: "黄公望", source: "元代画史记载" }
        ]
      },
      
      "明清时期": {
        target: 35,
        figures: [
          // 明朝皇帝
          { name: "明太祖", source: "《明史·太祖纪》" },
          { name: "明成祖", source: "《明史·成祖纪》" },
          { name: "明仁宗", source: "《明史·仁宗纪》" },
          { name: "明宣宗", source: "《明史·宣宗纪》" },
          { name: "明英宗", source: "《明史·英宗纪》" },
          { name: "明代宗", source: "《明史·景帝纪》" },
          { name: "明宪宗", source: "《明史·宪宗纪》" },
          { name: "明孝宗", source: "《明史·孝宗纪》" },
          { name: "明武宗", source: "《明史·武宗纪》" },
          { name: "明世宗", source: "《明史·世宗纪》" },
          { name: "明穆宗", source: "《明史·穆宗纪》" },
          { name: "明神宗", source: "《明史·神宗纪》" },
          { name: "明光宗", source: "《明史·光宗纪》" },
          { name: "明熹宗", source: "《明史·熹宗纪》" },
          { name: "明思宗", source: "《明史·庄烈帝纪》" },
          // 明朝名臣
          { name: "刘基", source: "《明史·刘基传》" },
          { name: "徐达", source: "《明史·徐达传》" },
          { name: "常遇春", source: "《明史·常遇春传》" },
          { name: "胡惟庸", source: "《明史·胡惟庸传》" },
          { name: "蓝玉", source: "《明史·蓝玉传》" },
          { name: "解缙", source: "《明史·解缙传》" },
          { name: "于谦", source: "《明史·于谦传》" },
          { name: "张居正", source: "《明史·张居正传》" },
          { name: "海瑞", source: "《明史·海瑞传》" },
          { name: "戚继光", source: "《明史·戚继光传》" },
          { name: "袁崇焕", source: "《明史·袁崇焕传》" },
          { name: "崇祯", source: "《明史·庄烈帝纪》" },
          // 清朝皇帝
          { name: "努尔哈赤", source: "《清史稿·太祖纪》" },
          { name: "皇太极", source: "《清史稿·太宗纪》" },
          { name: "顺治", source: "《清史稿·世祖纪》" },
          { name: "雍正", source: "《清史稿·世宗纪》" },
          { name: "乾隆", source: "《清史稿·高宗纪》" },
          { name: "嘉庆", source: "《清史稿·仁宗纪》" },
          { name: "道光", source: "《清史稿·宣宗纪》" },
          { name: "咸丰", source: "《清史稿·文宗纪》" },
          { name: "同治", source: "《清史稿·穆宗纪》" }
        ]
      }
    };
  }

  /**
   * 显示扩展计划概览
   */
  showExpansionOverview() {
    console.log('📋 历史名人数据库扩展计划');
    console.log('============================================================');
    console.log(`📊 当前状态:`);
    console.log(`   - 已有名人: ${this.currentCount} 位`);
    console.log(`   - 目标总数: ${this.targetCount} 位`);
    console.log(`   - 还需添加: ${this.remainingCount} 位`);
    
    console.log(`\n🏛️ 分朝代扩展计划:`);
    let totalPlanned = 0;
    Object.entries(this.expansionPlan).forEach(([dynasty, plan]) => {
      console.log(`   ${dynasty}: ${plan.target} 位 (已规划 ${plan.figures.length} 位)`);
      totalPlanned += plan.target;
    });
    
    console.log(`\n📈 计划统计:`);
    console.log(`   - 计划新增: ${totalPlanned} 位`);
    console.log(`   - 预计总数: ${this.currentCount + totalPlanned} 位`);
    console.log(`   - 完成度: ${((this.currentCount + totalPlanned) / this.targetCount * 100).toFixed(1)}%`);
    
    return this.expansionPlan;
  }

  /**
   * 生成下一批数据的优先级列表
   */
  getNextBatchPriority(batchSize = 20) {
    console.log(`\n🎯 下一批 ${batchSize} 位名人优先级列表:`);
    console.log('------------------------------------------------------------');
    
    const priorityList = [];
    
    // 按朝代重要性排序
    const dynastyPriority = [
      "隋唐五代",
      "宋元时期", 
      "明清时期",
      "魏晋南北朝",
      "秦汉时期",
      "先秦时期"
    ];
    
    dynastyPriority.forEach(dynasty => {
      if (this.expansionPlan[dynasty]) {
        const figures = this.expansionPlan[dynasty].figures.slice(0, Math.ceil(batchSize / dynastyPriority.length));
        figures.forEach((figure, index) => {
          priorityList.push({
            priority: priorityList.length + 1,
            dynasty: dynasty,
            name: figure.name,
            source: figure.source,
            importance: this.calculateImportance(figure, dynasty)
          });
        });
      }
    });
    
    // 按重要性排序
    priorityList.sort((a, b) => b.importance - a.importance);
    
    // 显示前20位
    priorityList.slice(0, batchSize).forEach((item, index) => {
      console.log(`${String(index + 1).padStart(2, ' ')}. ${item.name} (${item.dynasty}) - 重要性: ${item.importance.toFixed(1)} - 来源: ${item.source}`);
    });
    
    return priorityList.slice(0, batchSize);
  }

  /**
   * 计算历史人物重要性评分
   */
  calculateImportance(figure, dynasty) {
    let score = 5.0; // 基础分
    
    // 朝代权重
    const dynastyWeights = {
      "隋唐五代": 1.0,
      "宋元时期": 0.9,
      "明清时期": 0.8,
      "魏晋南北朝": 0.7,
      "秦汉时期": 0.9,
      "先秦时期": 1.0
    };
    
    score *= (dynastyWeights[dynasty] || 0.5);
    
    // 史书来源权重
    if (figure.source.includes('史记')) score += 1.0;
    if (figure.source.includes('汉书')) score += 0.9;
    if (figure.source.includes('后汉书')) score += 0.8;
    if (figure.source.includes('三国志')) score += 0.9;
    if (figure.source.includes('宋史')) score += 0.8;
    if (figure.source.includes('明史')) score += 0.7;
    if (figure.source.includes('清史稿')) score += 0.6;
    
    // 人物类型权重
    if (figure.name.includes('帝') || figure.name.includes('王')) score += 1.5; // 帝王
    if (figure.name.includes('公') || figure.name.includes('侯')) score += 1.0; // 贵族
    
    return score;
  }

  /**
   * 生成实施建议
   */
  generateImplementationSuggestions() {
    console.log('\n💡 实施建议:');
    console.log('------------------------------------------------------------');
    console.log('1. 📚 数据收集策略:');
    console.log('   - 优先使用《史记》《汉书》等正史记载');
    console.log('   - 交叉验证多个史书来源');
    console.log('   - 确保每位名人都有可靠的古籍依据');
    
    console.log('\n2. 🔍 质量控制:');
    console.log('   - 每批数据生成后进行专家验证');
    console.log('   - 维持平均验证度在0.9以上');
    console.log('   - 定期进行数据质量审核');
    
    console.log('\n3. ⚡ 分批实施:');
    console.log('   - 每批生成20-30位名人');
    console.log('   - 按朝代重要性排序');
    console.log('   - 确保朝代和格局分布均衡');
    
    console.log('\n4. 📊 进度跟踪:');
    console.log('   - 定期更新完成度统计');
    console.log('   - 监控数据质量指标');
    console.log('   - 及时调整扩展策略');
  }
}

module.exports = DatabaseExpansionPlan;
