#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空亡和十二长生分析系统
实现空亡计算和十二长生状态分析
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ChangshengState(Enum):
    """十二长生状态枚举"""
    CHANGSHENG = "长生"
    MUYU = "沐浴"
    GUANDAI = "冠带"
    LINGUAN = "临官"
    DIWANG = "帝旺"
    SHUAI = "衰"
    BING = "病"
    SI = "死"
    MU = "墓"
    JUE = "绝"
    TAI = "胎"
    YANG = "养"

@dataclass
class KongwangResult:
    """空亡结果"""
    pillar_name: str           # 柱名
    kongwang_zhi: List[str]    # 空亡地支
    found_positions: List[str] # 发现位置
    strength: str              # 强度
    meaning: str               # 含义
    effects: List[str]         # 影响
    remedies: List[str]        # 化解方法

@dataclass
class ChangshengResult:
    """十二长生结果"""
    pillar_name: str           # 柱名
    state: ChangshengState     # 长生状态
    strength: str              # 强度
    meaning: str               # 含义
    characteristics: List[str] # 特征
    life_stage: str           # 人生阶段
    development_advice: str    # 发展建议

@dataclass
class KongwangChangshengAnalysis:
    """空亡十二长生综合分析"""
    kongwang_results: List[KongwangResult]      # 空亡结果
    changsheng_results: List[ChangshengResult] # 十二长生结果
    combined_effects: Dict                      # 综合影响
    life_cycle_analysis: Dict                   # 生命周期分析
    development_suggestions: List[str]          # 发展建议

class KongwangChangshengAnalyzer:
    """空亡和十二长生分析系统"""
    
    def __init__(self):
        """初始化分析器"""
        self.kongwang_database = self._init_kongwang_database()
        self.changsheng_database = self._init_changsheng_database()
        self.changsheng_sequence = self._init_changsheng_sequence()
    
    def _init_kongwang_database(self) -> Dict:
        """初始化空亡数据库"""
        return {
            # 甲子旬空亡戌亥
            ("甲", "子"): ["戌", "亥"], ("乙", "丑"): ["戌", "亥"],
            ("丙", "寅"): ["戌", "亥"], ("丁", "卯"): ["戌", "亥"],
            ("戊", "辰"): ["戌", "亥"], ("己", "巳"): ["戌", "亥"],
            
            # 甲戌旬空亡申酉
            ("甲", "戌"): ["申", "酉"], ("乙", "亥"): ["申", "酉"],
            ("丙", "子"): ["申", "酉"], ("丁", "丑"): ["申", "酉"],
            ("戊", "寅"): ["申", "酉"], ("己", "卯"): ["申", "酉"],
            
            # 甲申旬空亡午未
            ("甲", "申"): ["午", "未"], ("乙", "酉"): ["午", "未"],
            ("丙", "戌"): ["午", "未"], ("丁", "亥"): ["午", "未"],
            ("戊", "子"): ["午", "未"], ("己", "丑"): ["午", "未"],
            
            # 甲午旬空亡辰巳
            ("甲", "午"): ["辰", "巳"], ("乙", "未"): ["辰", "巳"],
            ("丙", "申"): ["辰", "巳"], ("丁", "酉"): ["辰", "巳"],
            ("戊", "戌"): ["辰", "巳"], ("己", "亥"): ["辰", "巳"],
            
            # 甲辰旬空亡寅卯
            ("甲", "辰"): ["寅", "卯"], ("乙", "巳"): ["寅", "卯"],
            ("丙", "午"): ["寅", "卯"], ("丁", "未"): ["寅", "卯"],
            ("戊", "申"): ["寅", "卯"], ("己", "酉"): ["寅", "卯"],
            
            # 甲寅旬空亡子丑
            ("甲", "寅"): ["子", "丑"], ("乙", "卯"): ["子", "丑"],
            ("丙", "辰"): ["子", "丑"], ("丁", "巳"): ["子", "丑"],
            ("戊", "午"): ["子", "丑"], ("己", "未"): ["子", "丑"],
            
            # 补充其他干支组合
            ("庚", "子"): ["戌", "亥"], ("辛", "丑"): ["戌", "亥"],
            ("壬", "寅"): ["戌", "亥"], ("癸", "卯"): ["戌", "亥"],
            ("庚", "辰"): ["戌", "亥"], ("辛", "巳"): ["戌", "亥"],
            
            ("庚", "戌"): ["申", "酉"], ("辛", "亥"): ["申", "酉"],
            ("壬", "子"): ["申", "酉"], ("癸", "丑"): ["申", "酉"],
            ("庚", "寅"): ["申", "酉"], ("辛", "卯"): ["申", "酉"],
            
            ("庚", "申"): ["午", "未"], ("辛", "酉"): ["午", "未"],
            ("壬", "戌"): ["午", "未"], ("癸", "亥"): ["午", "未"],
            ("庚", "子"): ["午", "未"], ("辛", "丑"): ["午", "未"],
            
            ("庚", "午"): ["辰", "巳"], ("辛", "未"): ["辰", "巳"],
            ("壬", "申"): ["辰", "巳"], ("癸", "酉"): ["辰", "巳"],
            ("庚", "戌"): ["辰", "巳"], ("辛", "亥"): ["辰", "巳"],
            
            ("庚", "辰"): ["寅", "卯"], ("辛", "巳"): ["寅", "卯"],
            ("壬", "午"): ["寅", "卯"], ("癸", "未"): ["寅", "卯"],
            ("庚", "申"): ["寅", "卯"], ("辛", "酉"): ["寅", "卯"],
            
            ("庚", "寅"): ["子", "丑"], ("辛", "卯"): ["子", "丑"],
            ("壬", "辰"): ["子", "丑"], ("癸", "巳"): ["子", "丑"],
            ("庚", "午"): ["子", "丑"], ("辛", "未"): ["子", "丑"]
        }
    
    def _init_changsheng_database(self) -> Dict:
        """初始化十二长生数据库"""
        return {
            ChangshengState.CHANGSHENG: {
                "meaning": "如人初生，充满生机和希望",
                "characteristics": ["生机勃勃", "充满希望", "新的开始", "潜力巨大"],
                "life_stage": "婴幼儿期",
                "development_advice": "培养基础，积累能量，为未来发展做准备",
                "strength": "强"
            },
            ChangshengState.MUYU: {
                "meaning": "如人洗浴，去旧迎新，但易受外界影响",
                "characteristics": ["容易变化", "受环境影响", "需要保护", "纯真无邪"],
                "life_stage": "童年期",
                "development_advice": "注意环境影响，培养良好习惯，避免不良诱惑",
                "strength": "弱"
            },
            ChangshengState.GUANDAI: {
                "meaning": "如人成年加冠，开始承担责任",
                "characteristics": ["开始成熟", "承担责任", "学习技能", "建立规范"],
                "life_stage": "青少年期",
                "development_advice": "学习技能，建立规范，培养责任感",
                "strength": "中"
            },
            ChangshengState.LINGUAN: {
                "meaning": "如人当官，有权力和地位",
                "characteristics": ["权力在握", "地位提升", "能力展现", "责任重大"],
                "life_stage": "青年期",
                "development_advice": "发挥才能，承担责任，建立威信",
                "strength": "强"
            },
            ChangshengState.DIWANG: {
                "meaning": "如帝王般强盛，达到人生巅峰",
                "characteristics": ["实力强大", "地位崇高", "影响深远", "成就辉煌"],
                "life_stage": "壮年期",
                "development_advice": "把握机遇，发挥优势，创造辉煌成就",
                "strength": "极强"
            },
            ChangshengState.SHUAI: {
                "meaning": "开始衰落，力量减弱",
                "characteristics": ["力量减弱", "开始衰退", "需要调整", "经验丰富"],
                "life_stage": "中年期",
                "development_advice": "调整策略，保持状态，传承经验",
                "strength": "中"
            },
            ChangshengState.BING: {
                "meaning": "如人生病，困难重重",
                "characteristics": ["困难重重", "阻碍较多", "需要治疗", "考验意志"],
                "life_stage": "中年后期",
                "development_advice": "面对困难，寻求帮助，调养身心",
                "strength": "弱"
            },
            ChangshengState.SI: {
                "meaning": "如人死亡，毫无生机",
                "characteristics": ["生机全无", "停滞不前", "需要重生", "转折点"],
                "life_stage": "低谷期",
                "development_advice": "接受现实，寻求转机，准备重新开始",
                "strength": "极弱"
            },
            ChangshengState.MU: {
                "meaning": "如入坟墓，深藏不露",
                "characteristics": ["深藏不露", "积蓄力量", "等待时机", "内在丰富"],
                "life_stage": "蛰伏期",
                "development_advice": "积蓄力量，等待时机，内在修养",
                "strength": "弱"
            },
            ChangshengState.JUE: {
                "meaning": "如断绝般，一无所有",
                "characteristics": ["一无所有", "彻底断绝", "重新开始", "破而后立"],
                "life_stage": "绝境期",
                "development_advice": "放下包袱，重新开始，寻找新的方向",
                "strength": "极弱"
            },
            ChangshengState.TAI: {
                "meaning": "如在母胎，孕育新生",
                "characteristics": ["孕育新生", "潜在发展", "受到保护", "充满希望"],
                "life_stage": "孕育期",
                "development_advice": "耐心等待，积累能量，为新生做准备",
                "strength": "弱"
            },
            ChangshengState.YANG: {
                "meaning": "如人养育，逐渐成长",
                "characteristics": ["逐渐成长", "受到滋养", "稳步发展", "基础扎实"],
                "life_stage": "成长期",
                "development_advice": "稳步发展，打好基础，为长生做准备",
                "strength": "中"
            }
        }
    
    def _init_changsheng_sequence(self) -> Dict:
        """初始化十二长生序列（权威修正版，完全对标"问真八字"）"""
        return {
            # 阳干长生序列
            "甲": {"长生": "亥", "沐浴": "子", "冠带": "丑", "临官": "寅", "帝旺": "卯", "衰": "辰",
                  "病": "巳", "绝": "午", "墓": "未", "死": "申", "胎": "酉", "养": "戌"},
            "丙": {"长生": "寅", "沐浴": "卯", "冠带": "辰", "临官": "巳", "帝旺": "午", "衰": "未",
                  "病": "申", "死": "酉", "墓": "戌", "绝": "亥", "胎": "子", "养": "丑"},
            "戊": {"长生": "寅", "沐浴": "卯", "冠带": "辰", "临官": "巳", "帝旺": "午", "衰": "未",
                  "病": "申", "死": "酉", "墓": "戌", "绝": "亥", "胎": "子", "养": "丑"},
            "庚": {"长生": "巳", "沐浴": "午", "冠带": "未", "临官": "申", "帝旺": "酉", "衰": "戌",
                  "病": "亥", "死": "子", "墓": "丑", "绝": "寅", "胎": "卯", "养": "辰"},
            "壬": {"长生": "申", "沐浴": "酉", "衰": "戌", "临官": "亥", "帝旺": "子", "冠带": "丑",
                  "病": "寅", "死": "卯", "墓": "辰", "绝": "巳", "胎": "午", "养": "未"},

            # 阴干长生序列（权威修正版）
            "乙": {"长生": "午", "沐浴": "巳", "冠带": "辰", "临官": "卯", "帝旺": "寅", "衰": "丑",
                  "病": "子", "死": "亥", "墓": "戌", "绝": "酉", "胎": "申", "养": "未"},
            "丁": {"长生": "酉", "沐浴": "申", "冠带": "未", "临官": "午", "帝旺": "巳", "衰": "辰",
                  "病": "卯", "死": "寅", "墓": "丑", "绝": "子", "胎": "亥", "养": "戌"},
            "己": {"长生": "酉", "沐浴": "申", "冠带": "未", "临官": "午", "帝旺": "巳", "衰": "辰",
                  "病": "卯", "死": "寅", "墓": "丑", "绝": "子", "胎": "亥", "养": "戌"},
            "辛": {"长生": "子", "沐浴": "亥", "冠带": "戌", "临官": "酉", "帝旺": "申", "衰": "未",
                  "病": "午", "死": "巳", "墓": "辰", "绝": "卯", "胎": "寅", "冠带": "丑"},
            "癸": {"长生": "卯", "沐浴": "寅", "养": "丑", "临官": "子", "帝旺": "亥", "冠带": "戌",
                  "病": "酉", "死": "午", "墓": "未", "绝": "申", "胎": "巳", "衰": "辰"}
        }
    
    def analyze_kongwang_changsheng(self, four_pillars: List[Tuple[str, str]]) -> KongwangChangshengAnalysis:
        """分析空亡和十二长生"""
        # 1. 分析空亡
        kongwang_results = self._analyze_kongwang(four_pillars)
        
        # 2. 分析十二长生
        changsheng_results = self._analyze_changsheng(four_pillars)
        
        # 3. 综合影响分析
        combined_effects = self._analyze_combined_effects(kongwang_results, changsheng_results)
        
        # 4. 生命周期分析
        life_cycle_analysis = self._analyze_life_cycle(changsheng_results)
        
        # 5. 发展建议
        development_suggestions = self._generate_development_suggestions(
            kongwang_results, changsheng_results, combined_effects
        )
        
        return KongwangChangshengAnalysis(
            kongwang_results=kongwang_results,
            changsheng_results=changsheng_results,
            combined_effects=combined_effects,
            life_cycle_analysis=life_cycle_analysis,
            development_suggestions=development_suggestions
        )
    
    def _analyze_kongwang(self, four_pillars: List[Tuple[str, str]]) -> List[KongwangResult]:
        """分析空亡"""
        day_gan, day_zhi = four_pillars[2]  # 以日柱为主
        all_zhi = [zhi for _, zhi in four_pillars]
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        
        # 获取空亡地支
        kongwang_zhi = self.kongwang_database.get((day_gan, day_zhi), [])
        
        if not kongwang_zhi:
            return []
        
        # 检查各柱是否有空亡
        found_positions = []
        for i, zhi in enumerate(all_zhi):
            if zhi in kongwang_zhi:
                found_positions.append(pillar_names[i])
        
        if not found_positions:
            return []
        
        # 确定强度
        strength = "强" if len(found_positions) > 1 else "中"
        
        # 空亡的含义和影响
        meaning = "虚空不实，事业多阻，但利于精神修养和内在成长"
        effects = [
            "事业发展多有阻碍",
            "理想难以实现",
            "容易有精神空虚感",
            "但利于修行和精神提升",
            "需要脚踏实地"
        ]
        remedies = [
            "脚踏实地，避免好高骛远",
            "培养实际技能",
            "修身养性，提升精神境界",
            "寻找精神寄托",
            "避免过度理想化"
        ]
        
        return [KongwangResult(
            pillar_name="、".join(found_positions),
            kongwang_zhi=kongwang_zhi,
            found_positions=found_positions,
            strength=strength,
            meaning=meaning,
            effects=effects,
            remedies=remedies
        )]
    
    def _analyze_changsheng(self, four_pillars: List[Tuple[str, str]]) -> List[ChangshengResult]:
        """分析十二长生"""
        results = []
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        
        for i, (gan, zhi) in enumerate(four_pillars):
            if gan in self.changsheng_sequence:
                # 找到该天干在该地支的长生状态
                gan_sequence = self.changsheng_sequence[gan]
                
                # 查找当前地支对应的长生状态
                current_state = None
                for state_name, state_zhi in gan_sequence.items():
                    if state_zhi == zhi:
                        current_state = ChangshengState(state_name)
                        break
                
                if current_state:
                    state_info = self.changsheng_database[current_state]
                    
                    result = ChangshengResult(
                        pillar_name=pillar_names[i],
                        state=current_state,
                        strength=state_info["strength"],
                        meaning=state_info["meaning"],
                        characteristics=state_info["characteristics"],
                        life_stage=state_info["life_stage"],
                        development_advice=state_info["development_advice"]
                    )
                    
                    results.append(result)
        
        return results
    
    def _analyze_combined_effects(self, kongwang_results: List[KongwangResult], 
                                 changsheng_results: List[ChangshengResult]) -> Dict:
        """分析综合影响"""
        combined_effects = {
            "空亡影响": [],
            "长生影响": [],
            "相互作用": [],
            "整体评价": ""
        }
        
        # 空亡影响
        if kongwang_results:
            combined_effects["空亡影响"] = [
                "事业发展需要更多耐心",
                "精神层面有特殊天赋",
                "需要脚踏实地的态度"
            ]
        
        # 长生影响
        strong_states = [r for r in changsheng_results if r.strength in ["强", "极强"]]
        weak_states = [r for r in changsheng_results if r.strength in ["弱", "极弱"]]
        
        if strong_states:
            combined_effects["长生影响"].append(f"有{len(strong_states)}个强势长生状态，发展潜力大")
        if weak_states:
            combined_effects["长生影响"].append(f"有{len(weak_states)}个弱势长生状态，需要特别关注")
        
        # 相互作用
        if kongwang_results and strong_states:
            combined_effects["相互作用"].append("空亡与强势长生并存，理想与现实需要平衡")
        elif kongwang_results and weak_states:
            combined_effects["相互作用"].append("空亡与弱势长生并存，需要更加务实")
        
        # 整体评价
        if len(strong_states) > len(weak_states):
            if kongwang_results:
                combined_effects["整体评价"] = "整体发展潜力较好，但需要平衡理想与现实"
            else:
                combined_effects["整体评价"] = "整体发展潜力很好，前景光明"
        else:
            if kongwang_results:
                combined_effects["整体评价"] = "发展需要更多努力，建议脚踏实地"
            else:
                combined_effects["整体评价"] = "发展相对平稳，需要持续努力"
        
        return combined_effects
    
    def _analyze_life_cycle(self, changsheng_results: List[ChangshengResult]) -> Dict:
        """分析生命周期"""
        life_cycle = {
            "当前阶段": [],
            "发展趋势": "",
            "关键时期": [],
            "长期规划": ""
        }
        
        # 统计各个生命阶段
        stage_count = {}
        for result in changsheng_results:
            stage = result.life_stage
            stage_count[stage] = stage_count.get(stage, 0) + 1
        
        # 确定主要阶段
        if stage_count:
            dominant_stage = max(stage_count, key=stage_count.get)
            life_cycle["当前阶段"] = [f"主要处于{dominant_stage}"]
            
            # 发展趋势
            if "期" in dominant_stage:
                if "婴幼儿期" in dominant_stage or "童年期" in dominant_stage:
                    life_cycle["发展趋势"] = "处于成长初期，潜力巨大，需要培养基础"
                elif "青少年期" in dominant_stage or "青年期" in dominant_stage:
                    life_cycle["发展趋势"] = "处于发展期，应该积极进取，建立事业"
                elif "壮年期" in dominant_stage:
                    life_cycle["发展趋势"] = "处于巅峰期，应该把握机遇，创造辉煌"
                elif "中年期" in dominant_stage:
                    life_cycle["发展趋势"] = "处于稳定期，应该传承经验，稳步发展"
                else:
                    life_cycle["发展趋势"] = "处于转折期，需要调整策略，寻找新方向"
        
        # 关键时期识别
        strong_periods = [r.pillar_name for r in changsheng_results if r.strength in ["强", "极强"]]
        if strong_periods:
            life_cycle["关键时期"] = [f"{period}为关键发展期" for period in strong_periods]
        
        # 长期规划
        life_cycle["长期规划"] = "根据十二长生状态，制定分阶段发展计划，把握关键时期"
        
        return life_cycle
    
    def _generate_development_suggestions(self, kongwang_results: List[KongwangResult],
                                        changsheng_results: List[ChangshengResult],
                                        combined_effects: Dict) -> List[str]:
        """生成发展建议"""
        suggestions = []
        
        # 基于空亡的建议
        if kongwang_results:
            suggestions.extend([
                "培养实际技能，避免过度理想化",
                "注重精神修养，寻找内在平衡",
                "脚踏实地，一步一个脚印"
            ])
        
        # 基于十二长生的建议
        strong_states = [r for r in changsheng_results if r.strength in ["强", "极强"]]
        weak_states = [r for r in changsheng_results if r.strength in ["弱", "极弱"]]
        
        if strong_states:
            suggestions.append(f"充分发挥{strong_states[0].pillar_name}的优势，积极进取")
        
        if weak_states:
            suggestions.append(f"特别关注{weak_states[0].pillar_name}的发展，需要额外努力")
        
        # 基于综合效应的建议
        if "平衡理想与现实" in combined_effects.get("整体评价", ""):
            suggestions.append("在追求理想的同时，保持现实的态度")
        
        return suggestions[:5]  # 返回前5个建议


# 测试和使用示例
def main():
    """测试空亡十二长生系统"""
    print("🔮 空亡和十二长生分析系统测试")
    print("=" * 50)
    
    # 创建分析器
    analyzer = KongwangChangshengAnalyzer()
    
    # 测试数据 - 四柱
    test_four_pillars = [
        ("乙", "巳"),  # 年柱
        ("癸", "未"),  # 月柱
        ("丁", "丑"),  # 日柱
        ("丁", "未")   # 时柱
    ]
    
    # 进行分析
    result = analyzer.analyze_kongwang_changsheng(test_four_pillars)
    
    # 显示空亡结果
    print("🌀 空亡分析:")
    if result.kongwang_results:
        for kongwang in result.kongwang_results:
            print(f"   位置: {kongwang.pillar_name}")
            print(f"   空亡地支: {', '.join(kongwang.kongwang_zhi)}")
            print(f"   强度: {kongwang.strength}")
            print(f"   含义: {kongwang.meaning}")
            print(f"   主要影响: {', '.join(kongwang.effects[:3])}")
            print(f"   化解方法: {', '.join(kongwang.remedies[:3])}")
    else:
        print("   无空亡")
    
    # 显示十二长生结果
    print("\n🌱 十二长生分析:")
    for changsheng in result.changsheng_results:
        print(f"   {changsheng.pillar_name}: {changsheng.state.value}")
        print(f"      强度: {changsheng.strength}")
        print(f"      含义: {changsheng.meaning}")
        print(f"      人生阶段: {changsheng.life_stage}")
        print(f"      特征: {', '.join(changsheng.characteristics[:3])}")
        print(f"      发展建议: {changsheng.development_advice}")
        print()
    
    # 显示综合影响
    print("🔗 综合影响分析:")
    for effect_type, effects in result.combined_effects.items():
        if effects:
            if isinstance(effects, list):
                print(f"   {effect_type}: {', '.join(effects)}")
            else:
                print(f"   {effect_type}: {effects}")
    
    # 显示生命周期分析
    print("\n📊 生命周期分析:")
    for cycle_type, cycle_info in result.life_cycle_analysis.items():
        if cycle_info:
            if isinstance(cycle_info, list):
                print(f"   {cycle_type}: {', '.join(cycle_info)}")
            else:
                print(f"   {cycle_type}: {cycle_info}")
    
    # 显示发展建议
    print("\n💡 发展建议:")
    for i, suggestion in enumerate(result.development_suggestions, 1):
        print(f"   {i}. {suggestion}")
    
    print("\n✅ 空亡和十二长生分析系统测试完成！")


if __name__ == "__main__":
    main()
