/**
 * 历史名人数据库功能测试
 * 验证数据库的完整性和功能正确性
 */

const CelebrityDatabaseManager = require('./utils/celebrity_database_manager.js');

class CelebrityDatabaseTester {
  constructor() {
    this.databaseManager = new CelebrityDatabaseManager();
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始历史名人数据库测试');
    console.log('============================================================');

    // 基础功能测试
    await this.testBasicFunctionality();
    
    // 数据完整性测试
    await this.testDataIntegrity();
    
    // 搜索功能测试
    await this.testSearchFunctionality();
    
    // 筛选功能测试
    await this.testFilterFunctionality();
    
    // 八字相似度测试
    await this.testBaziSimilarity();
    
    // 统计功能测试
    await this.testStatistics();
    
    // 验证功能测试
    await this.testValidation();

    // 输出测试结果
    this.printTestResults();
  }

  /**
   * 基础功能测试
   */
  async testBasicFunctionality() {
    console.log('\n📋 测试1: 基础功能测试');
    console.log('------------------------------------------------------------');

    try {
      // 测试数据库初始化
      const totalCelebrities = this.databaseManager.celebrities.length;
      this.addTestResult('数据库初始化', totalCelebrities > 0, `加载了 ${totalCelebrities} 位名人`);

      // 测试按姓名查找
      const zengguofan = this.databaseManager.findByName('曾国藩');
      this.addTestResult('按姓名查找', !!zengguofan, zengguofan ? `找到 ${zengguofan.basicInfo.name}` : '未找到');

      // 测试按字号查找
      const libai = this.databaseManager.findByName('太白');
      this.addTestResult('按字号查找', !!libai, libai ? `找到 ${libai.basicInfo.name}` : '未找到');

      // 测试按昵称查找
      const sushi = this.databaseManager.findByName('东坡居士');
      this.addTestResult('按昵称查找', !!sushi, sushi ? `找到 ${sushi.basicInfo.name}` : '未找到');

    } catch (error) {
      this.addTestResult('基础功能测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 数据完整性测试
   */
  async testDataIntegrity() {
    console.log('\n🔍 测试2: 数据完整性测试');
    console.log('------------------------------------------------------------');

    try {
      const celebrities = this.databaseManager.celebrities;
      let validCount = 0;
      let invalidItems = [];

      celebrities.forEach((celebrity, index) => {
        const validation = this.databaseManager.validateCelebrityData(celebrity);
        if (validation.isValid) {
          validCount++;
        } else {
          invalidItems.push({
            index: index,
            name: celebrity.basicInfo?.name || '未知',
            errors: validation.errors
          });
        }
      });

      this.addTestResult('数据完整性', invalidItems.length === 0, 
        `${validCount}/${celebrities.length} 条记录有效，${invalidItems.length} 条记录有问题`);

      if (invalidItems.length > 0) {
        console.log('❌ 发现数据问题:');
        invalidItems.forEach(item => {
          console.log(`   - ${item.name}: ${item.errors.join(', ')}`);
        });
      }

    } catch (error) {
      this.addTestResult('数据完整性测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 搜索功能测试
   */
  async testSearchFunctionality() {
    console.log('\n🔍 测试3: 搜索功能测试');
    console.log('------------------------------------------------------------');

    try {
      // 测试姓名搜索
      const nameResults = this.databaseManager.search('李');
      this.addTestResult('姓名搜索', nameResults.length > 0, `搜索"李"找到 ${nameResults.length} 位名人`);

      // 测试朝代搜索
      const dynastyResults = this.databaseManager.search('清朝');
      this.addTestResult('朝代搜索', dynastyResults.length > 0, `搜索"清朝"找到 ${dynastyResults.length} 位名人`);

      // 测试职业搜索
      const occupationResults = this.databaseManager.search('政治家');
      this.addTestResult('职业搜索', occupationResults.length > 0, `搜索"政治家"找到 ${occupationResults.length} 位名人`);

      // 测试地点搜索
      const locationResults = this.databaseManager.search('湖南');
      this.addTestResult('地点搜索', locationResults.length > 0, `搜索"湖南"找到 ${locationResults.length} 位名人`);

    } catch (error) {
      this.addTestResult('搜索功能测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 筛选功能测试
   */
  async testFilterFunctionality() {
    console.log('\n🏷️ 测试4: 筛选功能测试');
    console.log('------------------------------------------------------------');

    try {
      // 测试按格局筛选
      const patternResults = this.databaseManager.findByPattern('正官格');
      this.addTestResult('格局筛选', patternResults.length > 0, `正官格找到 ${patternResults.length} 位名人`);

      // 测试按朝代筛选
      const dynastyResults = this.databaseManager.findByDynasty('明朝');
      this.addTestResult('朝代筛选', dynastyResults.length >= 0, `明朝找到 ${dynastyResults.length} 位名人`);

      // 测试按职业筛选
      const occupationResults = this.databaseManager.findByOccupation('文学家');
      this.addTestResult('职业筛选', occupationResults.length > 0, `文学家找到 ${occupationResults.length} 位名人`);

      // 测试按验证度筛选
      const verificationResults = this.databaseManager.findByVerificationScore(0.9);
      this.addTestResult('验证度筛选', verificationResults.length > 0, `高验证度找到 ${verificationResults.length} 位名人`);

      // 测试按出生地筛选
      const birthplaceResults = this.databaseManager.findByBirthplace('四川');
      this.addTestResult('出生地筛选', birthplaceResults.length > 0, `四川找到 ${birthplaceResults.length} 位名人`);

    } catch (error) {
      this.addTestResult('筛选功能测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 八字相似度测试
   */
  async testBaziSimilarity() {
    console.log('\n🔮 测试5: 八字相似度测试');
    console.log('------------------------------------------------------------');

    try {
      // 使用曾国藩的八字测试相似度
      const testBazi = {
        year: { gan: "辛", zhi: "未" },
        month: { gan: "庚", zhi: "子" },
        day: { gan: "丙", zhi: "辰" },
        hour: { gan: "己", zhi: "亥" }
      };

      const similarResults = this.databaseManager.findBySimilarBazi(testBazi);
      this.addTestResult('八字相似度计算', similarResults.length > 0, `找到 ${similarResults.length} 位相似八字的名人`);

      if (similarResults.length > 0) {
        console.log('   相似度排序:');
        similarResults.slice(0, 3).forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.celebrity.basicInfo.name} - 相似度: ${(result.similarity * 100).toFixed(1)}%`);
        });
      }

    } catch (error) {
      this.addTestResult('八字相似度测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 统计功能测试
   */
  async testStatistics() {
    console.log('\n📊 测试6: 统计功能测试');
    console.log('------------------------------------------------------------');

    try {
      const statistics = this.databaseManager.getPatternStatistics();
      
      this.addTestResult('统计信息生成', !!statistics, '统计信息生成成功');
      this.addTestResult('格局分布统计', Object.keys(statistics.patternDistribution).length > 0, 
        `统计了 ${Object.keys(statistics.patternDistribution).length} 种格局`);
      this.addTestResult('朝代分布统计', Object.keys(statistics.dynastyDistribution).length > 0, 
        `统计了 ${Object.keys(statistics.dynastyDistribution).length} 个朝代`);
      this.addTestResult('职业分布统计', Object.keys(statistics.occupationDistribution).length > 0, 
        `统计了 ${Object.keys(statistics.occupationDistribution).length} 种职业`);

      console.log('   格局分布:');
      Object.entries(statistics.patternDistribution).forEach(([pattern, count]) => {
        console.log(`   - ${pattern}: ${count} 人`);
      });

    } catch (error) {
      this.addTestResult('统计功能测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 验证功能测试
   */
  async testValidation() {
    console.log('\n✅ 测试7: 验证功能测试');
    console.log('------------------------------------------------------------');

    try {
      // 测试有效数据验证
      const validCelebrity = this.databaseManager.celebrities[0];
      const validResult = this.databaseManager.validateCelebrityData(validCelebrity);
      this.addTestResult('有效数据验证', validResult.isValid, '有效数据验证通过');

      // 测试无效数据验证
      const invalidCelebrity = {
        basicInfo: { name: '测试' },
        // 缺少必要字段
      };
      const invalidResult = this.databaseManager.validateCelebrityData(invalidCelebrity);
      this.addTestResult('无效数据验证', !invalidResult.isValid, `检测到 ${invalidResult.errors.length} 个错误`);

      // 测试平均验证分数
      const avgScore = this.databaseManager.getAverageVerificationScore();
      this.addTestResult('平均验证分数', parseFloat(avgScore) > 0.8, `平均验证分数: ${avgScore}`);

    } catch (error) {
      this.addTestResult('验证功能测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, details) {
    const result = {
      name: testName,
      passed: passed,
      details: details
    };
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n🎯 测试结果汇总');
    console.log('============================================================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults.filter(r => !r.passed).forEach(result => {
        console.log(`   - ${result.name}: ${result.details}`);
      });
    }
    
    console.log('\n🎉 历史名人数据库测试完成!');
  }
}

// 运行测试
async function runTests() {
  const tester = new CelebrityDatabaseTester();
  await tester.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = CelebrityDatabaseTester;
