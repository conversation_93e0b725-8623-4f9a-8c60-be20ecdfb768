/**
 * 神煞多柱出现验证实例
 * 构造特殊八字来验证同一神煞在多柱出现的情况
 */

console.log('🔍 神煞多柱出现验证实例');
console.log('='.repeat(60));
console.log('');

// 构造一个特殊的八字，让同一神煞在多柱出现
// 例如：甲子 甲子 甲子 甲子（极端例子）
const specialBaziData = {
  year_gan: '甲', year_zhi: '子',
  month_gan: '甲', month_zhi: '子', 
  day_gan: '甲', day_zhi: '子',
  hour_gan: '甲', hour_zhi: '子'
};

console.log('📋 特殊测试用例（甲子 甲子 甲子 甲子）：');
console.log('='.repeat(40));
console.log(`四柱：${specialBaziData.year_gan}${specialBaziData.year_zhi} ${specialBaziData.month_gan}${specialBaziData.month_zhi} ${specialBaziData.day_gan}${specialBaziData.day_zhi} ${specialBaziData.hour_gan}${specialBaziData.hour_zhi}`);
console.log('');

// 构建四柱数据
const fourPillars = [
  { gan: specialBaziData.year_gan, zhi: specialBaziData.year_zhi },   // 年柱
  { gan: specialBaziData.month_gan, zhi: specialBaziData.month_zhi }, // 月柱
  { gan: specialBaziData.day_gan, zhi: specialBaziData.day_zhi },     // 日柱
  { gan: specialBaziData.hour_gan, zhi: specialBaziData.hour_zhi }    // 时柱
];

// 神煞多柱验证系统
const multiPillarVerification = {
  // 验证天乙贵人（甲日干查丑未）
  verifyTianyiGuiren: function(dayGan, fourPillars) {
    console.log(`🔸 验证天乙贵人 - 日干${dayGan}`);
    const tianyiMap = {
      '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['酉', '亥'], '丁': ['酉', '亥'],
      '戊': ['丑', '未'], '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'],
      '壬': ['卯', '巳'], '癸': ['卯', '巳']
    };
    
    const results = [];
    const tianyiTargets = tianyiMap[dayGan] || [];
    console.log(`   甲日干查找：${tianyiTargets.join('、')} 地支`);
    
    fourPillars.forEach((pillar, index) => {
      const pillarName = ['年柱', '月柱', '日柱', '时柱'][index];
      console.log(`   检查${pillarName}：${pillar.gan}${pillar.zhi} - ${tianyiTargets.includes(pillar.zhi) ? '✅匹配' : '❌不匹配'}`);
      if (tianyiTargets.includes(pillar.zhi)) {
        results.push({
          name: '天乙贵人',
          position: pillarName,
          pillar: pillar.gan + pillar.zhi
        });
      }
    });
    
    console.log(`   结果：发现 ${results.length} 个天乙贵人`);
    return results;
  },

  // 验证桃花（子年支查酉）
  verifyTaohua: function(yearZhi, fourPillars) {
    console.log(`🔸 验证桃花 - 年支${yearZhi}`);
    const taohuaMap = {
      '申': '酉', '子': '酉', '辰': '酉',  // 申子辰年查酉
      '亥': '子', '卯': '子', '未': '子',  // 亥卯未年查子
      '寅': '卯', '午': '卯', '戌': '卯',  // 寅午戌年查卯
      '巳': '午', '酉': '午', '丑': '午'   // 巳酉丑年查午
    };
    
    const results = [];
    const taohuaTarget = taohuaMap[yearZhi];
    console.log(`   ${yearZhi}年支查找：${taohuaTarget} 地支`);
    
    fourPillars.forEach((pillar, index) => {
      const pillarName = ['年柱', '月柱', '日柱', '时柱'][index];
      console.log(`   检查${pillarName}：${pillar.gan}${pillar.zhi} - ${pillar.zhi === taohuaTarget ? '✅匹配' : '❌不匹配'}`);
      if (pillar.zhi === taohuaTarget) {
        results.push({
          name: '桃花',
          position: pillarName,
          pillar: pillar.gan + pillar.zhi
        });
      }
    });
    
    console.log(`   结果：发现 ${results.length} 个桃花`);
    return results;
  },

  // 验证劫煞（子年支查巳）
  verifyJiesha: function(yearZhi, fourPillars) {
    console.log(`🔸 验证劫煞 - 年支${yearZhi}`);
    const jieshaMap = {
      '申': '巳', '子': '巳', '辰': '巳',  // 申子辰见巳
      '亥': '申', '卯': '申', '未': '申',  // 亥卯未见申
      '寅': '亥', '午': '亥', '戌': '亥',  // 寅午戌见亥
      '巳': '寅', '酉': '寅', '丑': '寅'   // 巳酉丑见寅
    };
    
    const results = [];
    const jieshaTarget = jieshaMap[yearZhi];
    console.log(`   ${yearZhi}年支查找：${jieshaTarget} 地支`);
    
    fourPillars.forEach((pillar, index) => {
      const pillarName = ['年柱', '月柱', '日柱', '时柱'][index];
      console.log(`   检查${pillarName}：${pillar.gan}${pillar.zhi} - ${pillar.zhi === jieshaTarget ? '✅匹配' : '❌不匹配'}`);
      if (pillar.zhi === jieshaTarget) {
        results.push({
          name: '劫煞',
          position: pillarName,
          pillar: pillar.gan + pillar.zhi
        });
      }
    });
    
    console.log(`   结果：发现 ${results.length} 个劫煞`);
    return results;
  }
};

console.log('🧪 开始多柱神煞验证：');
console.log('='.repeat(30));

let allResults = [];

// 1. 验证天乙贵人
console.log('\n1. 天乙贵人验证：');
console.log('-'.repeat(20));
const tianyiResults = multiPillarVerification.verifyTianyiGuiren(specialBaziData.day_gan, fourPillars);
allResults.push(...tianyiResults);

// 2. 验证桃花
console.log('\n2. 桃花验证：');
console.log('-'.repeat(20));
const taohuaResults = multiPillarVerification.verifyTaohua(specialBaziData.year_zhi, fourPillars);
allResults.push(...taohuaResults);

// 3. 验证劫煞
console.log('\n3. 劫煞验证：');
console.log('-'.repeat(20));
const jieshaResults = multiPillarVerification.verifyJiesha(specialBaziData.year_zhi, fourPillars);
allResults.push(...jieshaResults);

console.log('\n📊 验证结果统计：');
console.log('='.repeat(25));
console.log(`总发现神煞：${allResults.length} 个`);
console.log(`天乙贵人：${tianyiResults.length} 个`);
console.log(`桃花：${taohuaResults.length} 个`);
console.log(`劫煞：${jieshaResults.length} 个`);

console.log('\n✅ 发现的神煞清单：');
console.log('='.repeat(20));
allResults.forEach((shensha, index) => {
  console.log(`${index + 1}. ${shensha.name} - ${shensha.position} (${shensha.pillar})`);
});

// 现在测试一个更现实的例子
console.log('\n' + '='.repeat(60));
console.log('🔍 现实例子验证：甲丑 甲未 甲丑 甲未');
console.log('='.repeat(60));

const realisticBaziData = {
  year_gan: '甲', year_zhi: '丑',
  month_gan: '甲', month_zhi: '未',
  day_gan: '甲', day_zhi: '丑',
  hour_gan: '甲', hour_zhi: '未'
};

const realisticFourPillars = [
  { gan: realisticBaziData.year_gan, zhi: realisticBaziData.year_zhi },
  { gan: realisticBaziData.month_gan, zhi: realisticBaziData.month_zhi },
  { gan: realisticBaziData.day_gan, zhi: realisticBaziData.day_zhi },
  { gan: realisticBaziData.hour_gan, zhi: realisticBaziData.hour_zhi }
];

console.log(`四柱：${realisticBaziData.year_gan}${realisticBaziData.year_zhi} ${realisticBaziData.month_gan}${realisticBaziData.month_zhi} ${realisticBaziData.day_gan}${realisticBaziData.day_zhi} ${realisticBaziData.hour_gan}${realisticBaziData.hour_zhi}`);

console.log('\n验证天乙贵人（甲日干查丑未）：');
const realisticTianyi = multiPillarVerification.verifyTianyiGuiren(realisticBaziData.day_gan, realisticFourPillars);

console.log('\n📊 现实例子结果：');
console.log('='.repeat(20));
console.log(`天乙贵人：${realisticTianyi.length} 个`);
realisticTianyi.forEach((shensha, index) => {
  console.log(`${index + 1}. ${shensha.name} - ${shensha.position} (${shensha.pillar})`);
});

console.log('\n🎯 重要结论：');
console.log('='.repeat(15));
console.log('1. ✅ 神煞确实可以在四柱中多次出现');
console.log('2. ✅ 计算方法是以基准（日干/年支/月支）查找目标地支');
console.log('3. ✅ 在四柱中找到几个目标地支，就有几个该神煞');
console.log('4. ✅ 甲丑甲未甲丑甲未的例子中，天乙贵人出现4次！');
console.log('5. ✅ 这说明神煞的分布完全取决于四柱的干支组合');

console.log('\n💡 实际应用意义：');
console.log('='.repeat(20));
console.log('• 同一神煞在不同柱位，影响的人生阶段不同');
console.log('• 年柱：祖上、童年、大环境');
console.log('• 月柱：父母、兄弟、事业、青年');
console.log('• 日柱：自己、配偶、中年');
console.log('• 时柱：子女、晚年、下属');
console.log('• 神煞越多，该神煞的影响力越强');

console.log('\n✅ 神煞多柱出现验证完成！');
console.log('🎯 您的理解完全正确：神煞按四柱分别计算，可以多次出现！');
