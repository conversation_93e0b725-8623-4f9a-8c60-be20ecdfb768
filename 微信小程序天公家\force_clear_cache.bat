@echo off
echo 🔧 强制清理微信开发者工具缓存...

echo 1. 关闭微信开发者工具进程...
taskkill /f /im "微信开发者工具.exe" 2>nul
taskkill /f /im "wechatdevtools.exe" 2>nul
timeout /t 2 >nul

echo 2. 清理项目缓存...
if exist ".wxcache" (
    rmdir /s /q ".wxcache"
    echo    ✅ 删除 .wxcache 文件夹
)

if exist "node_modules" (
    rmdir /s /q "node_modules"
    echo    ✅ 删除 node_modules 文件夹
)

echo 3. 清理用户缓存...
set CACHE_DIR=%APPDATA%\微信开发者工具\Cache
if exist "%CACHE_DIR%" (
    rmdir /s /q "%CACHE_DIR%"
    echo    ✅ 删除用户缓存文件夹
)

echo 4. 清理临时文件...
del /q /s "%TEMP%\wx*" 2>nul
echo    ✅ 清理临时文件

echo.
echo 🎯 缓存清理完成！
echo.
echo 📋 接下来请手动执行：
echo    1. 重新启动微信开发者工具
echo    2. 重新打开项目
echo    3. 在工具中点击"清缓存" - "清除文件缓存"
echo    4. 重新编译项目
echo.
pause
