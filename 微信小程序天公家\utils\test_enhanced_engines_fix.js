/**
 * 测试增强算法引擎修复
 * 验证AdvancedSocialAnalyzer和PreciseTimingAnalyzer是否能正常导入和初始化
 */

console.log('🧪 测试增强算法引擎修复...\n');

try {
  // 测试导入模块
  console.log('📦 测试模块导入...');
  
  const AdvancedSocialAnalyzer = require('./advanced_social_analyzer.js');
  const PreciseTimingAnalyzer = require('./precise_timing_analyzer.js');
  
  console.log('✅ AdvancedSocialAnalyzer 导入成功');
  console.log('✅ PreciseTimingAnalyzer 导入成功');

  // 测试模块初始化
  console.log('\n🔧 测试模块初始化...');
  
  const socialAnalyzer = new AdvancedSocialAnalyzer();
  const timingAnalyzer = new PreciseTimingAnalyzer();
  
  console.log('✅ AdvancedSocialAnalyzer 初始化成功');
  console.log('✅ PreciseTimingAnalyzer 初始化成功');

  // 测试基本功能
  console.log('\n⚙️ 测试基本功能...');

  // 测试社会环境分析
  if (typeof socialAnalyzer.analyzeEnvironmentalFactors === 'function') {
    console.log('✅ AdvancedSocialAnalyzer.analyzeEnvironmentalFactors 方法存在');
  } else {
    console.log('❌ AdvancedSocialAnalyzer.analyzeEnvironmentalFactors 方法不存在');
  }

  if (typeof socialAnalyzer.calculateSocialImpact === 'function') {
    console.log('✅ AdvancedSocialAnalyzer.calculateSocialImpact 方法存在');
  } else {
    console.log('❌ AdvancedSocialAnalyzer.calculateSocialImpact 方法不存在');
  }

  // 测试精确应期分析
  if (typeof timingAnalyzer.calculatePreciseTiming === 'function') {
    console.log('✅ PreciseTimingAnalyzer.calculatePreciseTiming 方法存在');
  } else {
    console.log('❌ PreciseTimingAnalyzer.calculatePreciseTiming 方法不存在');
  }

  if (typeof timingAnalyzer.analyzeTimingFactors === 'function') {
    console.log('✅ PreciseTimingAnalyzer.analyzeTimingFactors 方法存在');
  } else {
    console.log('❌ PreciseTimingAnalyzer.analyzeTimingFactors 方法不存在');
  }

  // 模拟页面初始化过程
  console.log('\n🎯 模拟页面初始化过程...');
  
  const mockPageInstance = {
    initializeEnhancedEngines: function() {
      console.log('🚀 开始初始化增强算法引擎...');
      
      try {
        // 模拟原有的初始化
        console.log('初始化基础引擎...');
        
        // 🆕 初始化扩展功能模块
        this.advancedSocialAnalyzer = new AdvancedSocialAnalyzer();
        this.preciseTimingAnalyzer = new PreciseTimingAnalyzer();
        
        console.log('✅ 增强算法引擎初始化成功（包含扩展功能）');
        return true;
      } catch (error) {
        console.error('❌ 增强算法引擎初始化失败:', error);
        return false;
      }
    }
  };

  const initResult = mockPageInstance.initializeEnhancedEngines();
  console.log('页面初始化结果:', initResult ? '✅ 成功' : '❌ 失败');

  // 验证初始化后的对象
  if (mockPageInstance.advancedSocialAnalyzer) {
    console.log('✅ advancedSocialAnalyzer 对象创建成功');
  } else {
    console.log('❌ advancedSocialAnalyzer 对象创建失败');
  }

  if (mockPageInstance.preciseTimingAnalyzer) {
    console.log('✅ preciseTimingAnalyzer 对象创建成功');
  } else {
    console.log('❌ preciseTimingAnalyzer 对象创建失败');
  }

  // 测试功能调用
  console.log('\n🔍 测试功能调用...');

  try {
    // 测试社会环境分析
    const testBaziData = {
      birthInfo: { year: 1990, month: 5, day: 15 },
      baziInfo: {
        dayPillar: { heavenly: '甲', earthly: '子' }
      }
    };

    if (mockPageInstance.advancedSocialAnalyzer.analyzeEnvironmentalFactors) {
      const socialResult = mockPageInstance.advancedSocialAnalyzer.analyzeEnvironmentalFactors(testBaziData);
      console.log('✅ 社会环境分析调用成功');
      console.log('社会分析结果类型:', typeof socialResult);
    }

    if (mockPageInstance.preciseTimingAnalyzer.calculatePreciseTiming) {
      const timingResult = mockPageInstance.preciseTimingAnalyzer.calculatePreciseTiming(testBaziData);
      console.log('✅ 精确应期分析调用成功');
      console.log('应期分析结果类型:', typeof timingResult);
    }

  } catch (funcError) {
    console.log('⚠️ 功能调用测试出现错误:', funcError.message);
  }

  console.log('\n🎉 所有测试完成！');
  console.log('\n📊 修复效果总结:');
  console.log('- ✅ AdvancedSocialAnalyzer 模块导入成功');
  console.log('- ✅ PreciseTimingAnalyzer 模块导入成功');
  console.log('- ✅ 模块初始化正常');
  console.log('- ✅ 页面初始化过程正常');
  console.log('- ✅ 避免了 ReferenceError: AdvancedSocialAnalyzer is not defined');
  console.log('- ✅ 增强算法引擎功能恢复正常');

} catch (error) {
  console.error('❌ 测试过程中出现错误:', error);
  console.log('\n🔍 错误分析:');
  console.log('- 错误类型:', error.constructor.name);
  console.log('- 错误信息:', error.message);
  console.log('- 可能原因: 模块文件不存在或导出格式错误');
}
