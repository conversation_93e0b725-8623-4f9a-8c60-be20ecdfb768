/**
 * 基于传统方法重新理解命宫计算
 * "从寅宫起正月，数到生月，然后逆数到卯"
 */

function traditionalMingGongMethod(lunarMonth, hourZhi) {
  console.log('🏛️ 传统命宫起法');
  console.log(`农历月份: ${lunarMonth}月`);
  console.log(`时支: ${hourZhi}`);
  console.log('=' * 40);
  
  // 十二宫位（从寅开始）
  const gongWei = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
  
  // 第一步：从寅宫起正月，数到生月
  console.log('第一步：从寅宫起正月，数到生月');
  console.log('寅宫=正月, 卯宫=二月, 辰宫=三月...');
  
  const monthGongIndex = (lunarMonth - 1) % 12; // 正月对应寅宫(索引0)
  const monthGong = gongWei[monthGongIndex];
  
  console.log(`农历${lunarMonth}月对应: ${monthGong}宫`);
  
  // 第二步：从生月宫位起，按时支顺序数
  console.log('\n第二步：从生月宫位起，按时支顺序数');
  
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const hourIndex = zhiOrder.indexOf(hourZhi);
  
  // 从生月宫位开始，按时支顺序数到生时
  let currentGongIndex = monthGongIndex;
  
  console.log(`从${monthGong}宫开始，数到${hourZhi}时:`);
  
  // 按十二时辰顺序数
  for (let i = 0; i <= hourIndex; i++) {
    const currentZhi = zhiOrder[i];
    const currentGong = gongWei[currentGongIndex];
    console.log(`${currentZhi}时 → ${currentGong}宫`);
    
    if (currentZhi === hourZhi) {
      console.log(`生时${hourZhi}落在${currentGong}宫`);
      break;
    }
    
    currentGongIndex = (currentGongIndex + 1) % 12;
  }
  
  // 第三步：从该宫位逆数到卯
  console.log('\n第三步：从该宫位逆数到卯');
  const finalGong = gongWei[currentGongIndex];
  
  // 从当前宫位逆数到卯宫
  const maoGongIndex = gongWei.indexOf('卯'); // 1
  let distance = (currentGongIndex - maoGongIndex + 12) % 12;
  
  console.log(`从${finalGong}宫逆数到卯宫，距离: ${distance}`);
  
  // 命宫就是卯宫
  const mingGong = '卯';
  
  console.log(`命宫: ${mingGong}`);
  
  return mingGong;
}

// 另一种理解：可能是从生时宫位逆数到卯的距离，然后从卯开始数
function alternativeMethod(lunarMonth, hourZhi) {
  console.log('\n🔧 另一种理解');
  console.log('=' * 40);
  
  const gongWei = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
  const zhiOrder = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  // 生月对应的宫位
  const monthGongIndex = (lunarMonth - 1) % 12;
  const monthGong = gongWei[monthGongIndex];
  
  // 生时对应的宫位（从生月宫开始数）
  const hourIndex = zhiOrder.indexOf(hourZhi);
  const hourGongIndex = (monthGongIndex + hourIndex) % 12;
  const hourGong = gongWei[hourGongIndex];
  
  console.log(`农历${lunarMonth}月 → ${monthGong}宫`);
  console.log(`${hourZhi}时从${monthGong}宫数${hourIndex}位 → ${hourGong}宫`);
  
  // 从时宫逆数到卯的距离
  const maoIndex = 1; // 卯在gongWei中的索引
  const distance = (hourGongIndex - maoIndex + 12) % 12;
  
  console.log(`从${hourGong}宫逆数到卯宫，距离: ${distance}`);
  
  // 命宫 = 卯宫 + 距离
  const mingGongIndex = (maoIndex + distance) % 12;
  const mingGong = gongWei[mingGongIndex];
  
  console.log(`命宫: 卯宫 + ${distance} = ${mingGong}宫`);
  
  return mingGong;
}

// 测试2015年11月20日（农历十月）
console.log('🔮 测试2015年11月20日14:00');
console.log('2015年11月20日对应农历十月初九');

const result1 = traditionalMingGongMethod(10, '未'); // 农历十月，未时
const result2 = alternativeMethod(10, '未');

console.log('\n📊 结果对比:');
console.log('传统方法:', result1);
console.log('另一种理解:', result2);
console.log('期望结果: 亥');

// 测试其他已知案例
console.log('\n🔍 验证其他案例:');

// 1996年10月13日对应农历九月初二
console.log('\n1996年10月13日15:45 (农历九月申时):');
const result1996_1 = traditionalMingGongMethod(9, '申');
const result1996_2 = alternativeMethod(9, '申');
console.log('传统方法:', result1996_1, '期望: 亥');
console.log('另一种理解:', result1996_2, '期望: 亥');
