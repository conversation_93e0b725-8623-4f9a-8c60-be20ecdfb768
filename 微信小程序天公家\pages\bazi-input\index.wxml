<!--pages/bazi-input/index.wxml-->
<!-- 八字排盘信息输入页面 -->

<scroll-view class="container" scroll-y="true" enhanced="true" show-scrollbar="false" enable-flex="true">
  <!-- 师父身份显示 -->
  <view class="master-info">
    <image class="master-avatar" src="{{master === '天工师父' ? '/assets/icons/tiangong-shifu.png' : '/assets/icons/tiangong-master.svg'}}"></image>
    <view class="master-text">
      <text class="master-name">{{master}}</text>
      <text class="master-subtitle">专业综合分析</text>
    </view>
  </view>



  <!-- 出生信息输入区域 -->
  <view class="input-section">
    <view class="section-title">
      <text class="title-icon">📅</text>
      <text class="title-text">出生信息</text>
      <text class="title-tip">请准确填写，时间越精确分析越准确</text>
    </view>

    <!-- 姓名输入 -->
    <view class="input-group">
      <view class="input-label">
        <text>姓名</text>
        <text class="required-mark">*</text>
      </view>
      <input class="name-input"
             placeholder="请输入您的姓名"
             value="{{birthInfo.name}}"
             bindinput="onNameInput"
             maxlength="20" />
    </view>

    <!-- 历法选择 -->
    <view class="input-group">
      <view class="input-label">历法类型</view>
      <view class="calendar-type-group">
        <view class="calendar-option {{calendarType === 'solar' ? 'selected' : ''}}"
              bindtap="selectCalendarType" data-type="solar">
          <view class="option-icon">☀️</view>
          <view class="option-text">
            <text class="option-name">阳历</text>
            <text class="option-desc">公历/新历</text>
          </view>
        </view>
        <view class="calendar-option {{calendarType === 'lunar' ? 'selected' : ''}}"
              bindtap="selectCalendarType" data-type="lunar">
          <view class="option-icon">🌙</view>
          <view class="option-text">
            <text class="option-name">农历</text>
            <text class="option-desc">阴历/旧历</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 出生日期 -->
    <view class="input-group">
      <view class="input-label">
        <text>出生日期</text>
        <text class="calendar-indicator">({{calendarType === 'solar' ? '阳历' : '农历'}})</text>
      </view>
      <view class="date-picker-group">
        <picker class="date-picker" mode="selector" range="{{years}}" value="{{yearIndex}}" bindchange="onYearChange">
          <view class="picker-display">{{years[yearIndex] || '选择年份'}}</view>
        </picker>
        <picker class="date-picker" mode="selector" range="{{months}}" value="{{monthIndex}}" bindchange="onMonthChange">
          <view class="picker-display">{{months[monthIndex]}}</view>
        </picker>
        <picker class="date-picker" mode="selector" range="{{days}}" value="{{dayIndex}}" bindchange="onDayChange">
          <view class="picker-display">{{days[dayIndex] || '选择日期'}}</view>
        </picker>
      </view>

      <!-- 日期转换显示 -->
      <view class="date-conversion" wx:if="{{convertedDate.showConversion}}">
        <view class="conversion-item">
          <text class="conversion-label">阳历：</text>
          <text class="conversion-value">{{convertedDate.solar}}</text>
        </view>
        <view class="conversion-item">
          <text class="conversion-label">农历：</text>
          <text class="conversion-value">{{convertedDate.lunar}}</text>
        </view>
      </view>
    </view>

    <!-- 真太阳时显示 -->
    <view class="input-group" wx:if="{{trueSolarTime.showCorrection}}">
      <view class="input-label">
        <text>真太阳时校正</text>
        <text class="solar-time-badge">🌞 已启用</text>
      </view>
      <view class="solar-time-info">
        <view class="solar-time-item">
          <text class="solar-time-label">输入时间：</text>
          <text class="solar-time-value">{{birthInfo.hour}}:{{birthInfo.minute < 10 ? '0' + birthInfo.minute : birthInfo.minute}}</text>
        </view>
        <view class="solar-time-item">
          <text class="solar-time-label">真太阳时：</text>
          <text class="solar-time-value corrected">{{trueSolarTime.correctedTimeDisplay || '--:--'}}</text>
        </view>
        <view class="solar-time-item">
          <text class="solar-time-label">时间差：</text>
          <text class="solar-time-value diff">
            {{trueSolarTime.timeDifference > 0 ? '+' : ''}}{{trueSolarTime.timeDifference}}分钟
          </text>
        </view>
      </view>
    </view>



    <!-- 出生地点 -->
    <view class="input-group">
      <view class="input-label">
        <text>出生地点</text>
        <text class="location-note">（用于真太阳时计算）</text>
      </view>
      <view class="city-selector" bindtap="showCityPicker">
        <text class="city-name">{{birthInfo.birthCity}}</text>
        <text class="city-arrow">></text>
      </view>
    </view>

    <!-- 出生时间 -->
    <view class="input-group">
      <view class="input-label">出生时间</view>
      <view class="time-picker-group">
        <picker class="time-picker" mode="selector" range="{{hours}}" value="{{hourIndex}}" bindchange="onHourChange">
          <view class="picker-display">{{hours[hourIndex]}}</view>
        </picker>
        <picker class="time-picker" mode="selector" range="{{minutes}}" value="{{minuteIndex}}" bindchange="onMinuteChange">
          <view class="picker-display">{{minutes[minuteIndex]}}</view>
        </picker>
      </view>
    </view>

    <!-- 性别选择 -->
    <view class="input-group">
      <view class="input-label">性别</view>
      <picker class="gender-picker" mode="selector" range="{{genders}}" value="{{genderIndex}}" bindchange="onGenderChange">
        <view class="picker-display gender-display">
          <text class="gender-icon">{{birthInfo.gender === '男' ? '👨' : '👩'}}</text>
          <text>{{birthInfo.gender}}</text>
        </view>
      </picker>
    </view>


  </view>

  <!-- 八字显示 -->
  <view class="bazi-display" wx:if="{{baziResult}}">
    <view class="section-title">
      <text class="title-icon">🔮</text>
      <text class="title-text">八字排盘</text>
      <text class="title-tip">根据您的出生信息计算的精确八字</text>
    </view>

    <view class="bazi-pillars">
      <view class="pillar-item">
        <view class="pillar-label">年柱</view>
        <view class="pillar-value">{{baziResult.formatted.year}}</view>
      </view>
      <view class="pillar-item">
        <view class="pillar-label">月柱</view>
        <view class="pillar-value">{{baziResult.formatted.month}}</view>
      </view>
      <view class="pillar-item">
        <view class="pillar-label">日柱</view>
        <view class="pillar-value">{{baziResult.formatted.day}}</view>
      </view>
      <view class="pillar-item">
        <view class="pillar-label">时柱</view>
        <view class="pillar-value">{{baziResult.formatted.hour}}</view>
      </view>
    </view>

    <view class="bazi-summary">
      <text class="bazi-full">{{baziResult.formatted.full}}</text>
    </view>

    <!-- 节气信息显示 -->
    <view class="jieqi-info" wx:if="{{baziResult.jieqiInfo}}">
      <view class="info-label">
        <text class="info-icon">🌸</text>
        <text class="info-title">出生节气</text>
      </view>
      <view class="info-content">
        <text class="jieqi-text">{{baziResult.jieqiInfo}}</text>
      </view>
    </view>


















  </view>

  <!-- 分析说明 -->
  <view class="analysis-info">
    <view class="section-title">
      <text class="title-icon">🎯</text>
      <text class="title-text">专业分析</text>
      <text class="title-tip">{{master}}采用最高级别的综合分析模式，融合传统古籍智慧与现代解读技术</text>
    </view>

    <view class="analysis-features">
      <view class="feature-row">
        <view class="feature-item">
          <text class="feature-icon">📊</text>
          <text class="feature-text">基础排盘 · 五行分析</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">⚖️</text>
          <text class="feature-text">格局判断 · 用神分析</text>
        </view>
      </view>
      <view class="feature-row">
        <view class="feature-item">
          <text class="feature-icon">📚</text>
          <text class="feature-text">古籍理论 · 现代解读</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🔮</text>
          <text class="feature-text">运势预测 · 人生指导</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-section">
    <button class="help-button" bindtap="showHelp">
      <text class="button-icon">❓</text>
      <text>使用说明</text>
    </button>
    
    <button class="start-button" bindtap="startPaipan" loading="{{loading}}" disabled="{{loading}}">
      <text class="button-icon">🔮</text>
      <text>{{loading ? '排盘中...' : '开始排盘'}}</text>
    </button>
  </view>

  <!-- 城市选择弹窗 -->
  <view class="city-modal {{citySelection.showCityPicker ? 'show' : ''}}" wx:if="{{citySelection.showCityPicker}}">
    <view class="modal-mask" bindtap="hideCityPicker"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择出生城市</text>
        <text class="modal-close" bindtap="hideCityPicker">✕</text>
      </view>
      <view class="modal-body">
        <input class="city-search-input"
               placeholder="搜索城市名称..."
               value="{{citySelection.searchKeyword}}"
               bindinput="onCitySearch"
               maxlength="10" />
        <scroll-view class="city-list" scroll-y="true" enable-flex="true">
          <view class="city-item"
                wx:for="{{citySelection.filteredCities}}"
                wx:key="*this"
                bindtap="selectCity"
                data-city="{{item}}">
            <text class="city-item-name">{{item}}</text>
            <text class="city-item-check" wx:if="{{item === birthInfo.birthCity}}">✓</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="footer-info">
    <view class="info-item">
      <text class="info-icon">🎯</text>
      <text class="info-text">采用最高级别综合分析模式</text>
    </view>
    <view class="info-item">
      <text class="info-icon">📚</text>
      <text class="info-text">融合古籍智慧与现代解读技术</text>
    </view>
    <view class="info-item">
      <text class="info-icon">🌞</text>
      <text class="info-text">自动启用真太阳时精确校正</text>
    </view>
    <view class="info-item">
      <text class="info-icon">⚡</text>
      <text class="info-text">支持阳历农历智能转换</text>
    </view>
    <view class="info-item">
      <text class="info-icon">⚖️</text>
      <text class="info-text">仅供学习研究，理性对待结果</text>
    </view>
  </view>
</scroll-view>
