/**
 * 200名人完整数据库最终合并工具
 * 将所有批次数据合并为最终的200名人完整数据库
 */

// 导入所有批次数据
const batch1Data = require('../data/sui_tang_celebrities_batch1_complete');
const batch2Data = require('../data/song_yuan_ming_qing_batch2_complete');
const batch3Data = require('../data/batch3_complete_celebrities');
const batch4Data = require('../data/batch4_complete');
const batch5Data = require('../data/batch5_supplement_complete');

function mergeFinal200CelebritiesData() {
  console.log('开始合并200名人完整数据库...');
  
  // 合并所有名人数据
  const allCelebrities = [
    ...batch1Data.celebrities,
    ...batch2Data.celebrities,
    ...batch3Data.celebrities,
    ...batch4Data.celebrities,
    ...batch5Data.celebrities
  ];

  // 统计批次分布
  const batchStats = {
    "第一批次(隋唐五代)": batch1Data.metadata.totalRecords,
    "第二批次(宋元明清)": batch2Data.metadata.totalRecords,
    "第三批次(先秦魏晋南北朝)": batch3Data.metadata.totalRecords,
    "第四批次(古代补充+近现代+当代)": batch4Data.metadata.totalRecords,
    "第五批次(重要人物补充)": batch5Data.metadata.totalRecords
  };

  // 统计朝代分布
  const dynastyStats = {};
  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    dynastyStats[dynasty] = (dynastyStats[dynasty] || 0) + 1;
  });

  // 统计职业分布
  const occupationStats = {};
  allCelebrities.forEach(celebrity => {
    celebrity.basicInfo.occupation.forEach(occupation => {
      occupationStats[occupation] = (occupationStats[occupation] || 0) + 1;
    });
  });

  // 统计时期分布
  const periodStats = {
    "先秦": 0,
    "秦汉": 0,
    "魏晋南北朝": 0,
    "隋唐五代": 0,
    "宋元": 0,
    "明清": 0,
    "近现代": 0,
    "当代": 0
  };

  allCelebrities.forEach(celebrity => {
    const dynasty = celebrity.basicInfo.dynasty;
    if (dynasty.includes('先秦') || dynasty.includes('春秋') || dynasty.includes('战国')) {
      periodStats["先秦"]++;
    } else if (dynasty.includes('秦') || dynasty.includes('汉')) {
      periodStats["秦汉"]++;
    } else if (dynasty.includes('魏') || dynasty.includes('晋') || dynasty.includes('南北朝')) {
      periodStats["魏晋南北朝"]++;
    } else if (dynasty.includes('隋') || dynasty.includes('唐') || dynasty.includes('五代')) {
      periodStats["隋唐五代"]++;
    } else if (dynasty.includes('宋') || dynasty.includes('元')) {
      periodStats["宋元"]++;
    } else if (dynasty.includes('明') || dynasty.includes('清')) {
      periodStats["明清"]++;
    } else if (dynasty.includes('民国') || dynasty.includes('清末')) {
      periodStats["近现代"]++;
    } else if (dynasty.includes('中华人民共和国')) {
      periodStats["当代"]++;
    }
  });

  // 计算平均验证分数
  const totalVerificationScore = allCelebrities.reduce((sum, celebrity) => {
    return sum + celebrity.verification.algorithmMatch;
  }, 0);
  const averageVerificationScore = totalVerificationScore / allCelebrities.length;

  // 创建最终数据结构
  const finalData = {
    metadata: {
      title: "历史名人数据库 - 200名人完整版",
      description: "包含中国历史上200位最重要的历史人物，涵盖从先秦到当代的各个历史时期",
      version: "4.0",
      totalRecords: allCelebrities.length,
      creationDate: "2025-01-02",
      timeRange: "前551年-2021年AD",
      dataSource: "《史记》《汉书》《三国志》《宋史》《元史》《明史》《清史稿》《毛泽东选集》《钱学森传》等权威史料",
      verificationStandard: "专家交叉校验+古籍依据双重认证",
      averageVerificationScore: parseFloat(averageVerificationScore.toFixed(3)),
      batchDistribution: batchStats,
      dynastyDistribution: dynastyStats,
      periodDistribution: periodStats,
      occupationDistribution: occupationStats,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格"
    },

    // 按历史时期分类的详细统计信息
    historicalPeriodAnalysis: {
      先秦: {
        timeRange: "前551年-前221年",
        count: periodStats["先秦"],
        characteristics: "诸子百家、思想奠基、文化源头",
        representatives: ["孔子", "老子", "孟子", "荀子", "韩非子"]
      },
      秦汉: {
        timeRange: "前221年-220年",
        count: periodStats["秦汉"],
        characteristics: "统一天下、制度建立、文化融合",
        representatives: ["秦始皇", "汉武帝", "司马迁", "董仲舒"]
      },
      魏晋南北朝: {
        timeRange: "220年-589年",
        count: periodStats["魏晋南北朝"],
        characteristics: "政权分立、文化繁荣、玄学兴起",
        representatives: ["曹操", "诸葛亮", "王羲之", "陶渊明"]
      },
      隋唐五代: {
        timeRange: "581年-960年",
        count: periodStats["隋唐五代"],
        characteristics: "盛世辉煌、文化鼎盛、对外开放",
        representatives: ["唐太宗", "武则天", "李白", "杜甫"]
      },
      宋元: {
        timeRange: "960年-1368年",
        count: periodStats["宋元"],
        characteristics: "理学兴起、科技发达、文化内敛",
        representatives: ["宋太祖", "朱熹", "苏轼", "忽必烈"]
      },
      明清: {
        timeRange: "1368年-1911年",
        count: periodStats["明清"],
        characteristics: "专制强化、闭关锁国、传统巅峰",
        representatives: ["朱元璋", "康熙", "曹雪芹", "郑和"]
      },
      近现代: {
        timeRange: "1840年-1949年",
        count: periodStats["近现代"],
        characteristics: "救亡图存、变法维新、革命救国",
        representatives: ["林则徐", "康有为", "孙中山", "鲁迅"]
      },
      当代: {
        timeRange: "1949年-2021年",
        count: periodStats["当代"],
        characteristics: "建国伟业、改革开放、科技创新",
        representatives: ["毛泽东", "邓小平", "钱学森", "袁隆平"]
      }
    },

    // 质量分析
    qualityAnalysis: {
      totalCelebrities: allCelebrities.length,
      averageVerificationScore: averageVerificationScore,
      qualityGrade: averageVerificationScore >= 0.95 ? "优秀" : 
                   averageVerificationScore >= 0.90 ? "良好" : "合格",
      scoreDistribution: {
        excellent: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.95).length,
        good: allCelebrities.filter(c => c.verification.algorithmMatch >= 0.90 && c.verification.algorithmMatch < 0.95).length,
        acceptable: allCelebrities.filter(c => c.verification.algorithmMatch < 0.90).length
      },
      dataIntegrity: {
        completeBasicInfo: allCelebrities.filter(c => c.basicInfo?.name && c.basicInfo?.birthYear).length,
        completeBazi: allCelebrities.filter(c => c.bazi?.fullBazi).length,
        completePattern: allCelebrities.filter(c => c.pattern?.mainPattern).length,
        completeVerification: allCelebrities.filter(c => c.verification?.algorithmMatch).length
      },
      batchQuality: {
        batch1: batch1Data.metadata.averageVerificationScore,
        batch2: batch2Data.metadata.averageVerificationScore,
        batch3: batch3Data.metadata.averageVerificationScore,
        batch4: batch4Data.metadata.averageVerificationScore,
        batch5: batch5Data.metadata.averageVerificationScore
      }
    },

    celebrities: allCelebrities
  };

  console.log('\n=== 200名人完整数据库合并完成 ===');
  console.log(`总计名人数量: ${finalData.metadata.totalRecords}`);
  console.log(`平均验证分数: ${finalData.metadata.averageVerificationScore}`);
  console.log(`质量等级: ${finalData.metadata.qualityGrade}`);
  console.log('\n批次分布:');
  Object.entries(batchStats).forEach(([batch, count]) => {
    console.log(`  ${batch}: ${count}位`);
  });
  console.log('\n历史时期分布:');
  Object.entries(periodStats).forEach(([period, count]) => {
    console.log(`  ${period}: ${count}位`);
  });
  console.log('\n主要朝代分布:');
  Object.entries(dynastyStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([dynasty, count]) => {
      console.log(`  ${dynasty}: ${count}位`);
    });
  console.log('\n主要职业分布:');
  Object.entries(occupationStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .forEach(([occupation, count]) => {
      console.log(`  ${occupation}: ${count}位`);
    });

  return finalData;
}

// 数据质量检查
function validateFinal200CelebritiesData(data) {
  console.log('\n开始200名人完整数据库质量检查...');
  
  const issues = [];
  const celebrities = data.celebrities;
  
  // 检查总数量
  if (celebrities.length !== 200) {
    issues.push(`名人总数不等于200: 实际${celebrities.length}位`);
  }

  // 检查ID唯一性
  const ids = new Set();
  celebrities.forEach((celebrity, index) => {
    if (ids.has(celebrity.id)) {
      issues.push(`重复ID: ${celebrity.id} (索引: ${index})`);
    }
    ids.add(celebrity.id);
  });

  // 检查必要字段完整性
  celebrities.forEach((celebrity, index) => {
    if (!celebrity.basicInfo?.name) {
      issues.push(`缺少姓名: 索引 ${index}`);
    }
    if (!celebrity.bazi?.fullBazi) {
      issues.push(`缺少八字: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.pattern?.mainPattern) {
      issues.push(`缺少主格局: ${celebrity.basicInfo?.name || index}`);
    }
    if (!celebrity.verification?.algorithmMatch) {
      issues.push(`缺少算法匹配度: ${celebrity.basicInfo?.name || index}`);
    }
  });

  console.log(`200名人完整数据库质量检查完成，发现 ${issues.length} 个问题`);
  if (issues.length > 0) {
    console.log('问题列表:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return {
    isValid: issues.length === 0,
    issues: issues,
    totalCelebrities: celebrities.length,
    qualityScore: issues.length === 0 ? 1.0 : Math.max(0, 1 - issues.length / celebrities.length)
  };
}

// 执行合并和验证
if (require.main === module) {
  try {
    const finalData = mergeFinal200CelebritiesData();
    const validation = validateFinal200CelebritiesData(finalData);
    
    if (validation.isValid) {
      console.log('\n✅ 200名人完整数据库质量检查通过！');
      
      // 保存最终数据
      const fs = require('fs');
      const outputPath = 'data/celebrities_database_200_complete.js';
      const fileContent = `/**
 * 历史名人数据库 - 200名人完整版
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: 四个批次数据合并
 * 总计: ${finalData.metadata.totalRecords}位历史名人
 * 平均验证分数: ${finalData.metadata.averageVerificationScore}
 * 质量等级: ${finalData.metadata.qualityGrade}
 */

const celebritiesDatabase200Complete = ${JSON.stringify(finalData, null, 2)};

module.exports = celebritiesDatabase200Complete;`;
      
      fs.writeFileSync(outputPath, fileContent, 'utf8');
      console.log(`✅ 200名人完整数据库已保存到: ${outputPath}`);
      
      // 生成简化版本用于前端
      const simplifiedData = {
        metadata: finalData.metadata,
        celebrities: finalData.celebrities.map(celebrity => ({
          id: celebrity.id,
          name: celebrity.basicInfo.name,
          dynasty: celebrity.basicInfo.dynasty,
          occupation: celebrity.basicInfo.occupation,
          birthYear: celebrity.basicInfo.birthYear,
          deathYear: celebrity.basicInfo.deathYear,
          pattern: celebrity.pattern.mainPattern,
          verificationScore: celebrity.verification.algorithmMatch
        }))
      };
      
      const simplifiedPath = 'data/celebrities_database_200_simplified.js';
      const simplifiedContent = `/**
 * 历史名人数据库 - 200名人简化版
 * 用于前端快速加载和展示
 */

const celebritiesDatabase200Simplified = ${JSON.stringify(simplifiedData, null, 2)};

module.exports = celebritiesDatabase200Simplified;`;
      
      fs.writeFileSync(simplifiedPath, simplifiedContent, 'utf8');
      console.log(`✅ 200名人简化数据库已保存到: ${simplifiedPath}`);
      
    } else {
      console.log('\n❌ 200名人完整数据库质量检查未通过，请修复问题后重试');
    }
  } catch (error) {
    console.error('200名人完整数据库合并过程中发生错误:', error);
  }
}

module.exports = {
  mergeFinal200CelebritiesData,
  validateFinal200CelebritiesData
};
