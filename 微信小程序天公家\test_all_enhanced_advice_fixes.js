// test_all_enhanced_advice_fixes.js
// 测试所有增强建议生成器的修复

console.log('🧪 开始测试所有增强建议生成器修复');

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null
};

// 加载模块
const EnhancedAdviceGenerator = require('./utils/enhanced_advice_generator.js');

// 创建实例
const generator = new EnhancedAdviceGenerator();

console.log('✅ 增强建议生成器实例创建成功');

// 测试用例：空的八字数据
const emptyBazi = null;
const incompletePatternResult = {};

console.log('\n📋 测试所有可能出错的方法（使用空数据）:');

const testMethods = [
  {
    name: 'analyzePersonalityDepth',
    method: () => generator.analyzePersonalityDepth(emptyBazi, incompletePatternResult, {})
  },
  {
    name: 'mapToMBTI',
    method: () => generator.mapToMBTI(emptyBazi, incompletePatternResult)
  },
  {
    name: 'analyzeBehaviorPatterns',
    method: () => generator.analyzeBehaviorPatterns(emptyBazi, incompletePatternResult)
  },
  {
    name: 'assessDecisionStyle',
    method: () => generator.assessDecisionStyle(emptyBazi, incompletePatternResult)
  },
  {
    name: 'calculateCreativityIndex',
    method: () => generator.calculateCreativityIndex(emptyBazi, incompletePatternResult)
  },
  {
    name: 'assessLeadershipPotential',
    method: () => generator.assessLeadershipPotential(emptyBazi, incompletePatternResult)
  },
  {
    name: 'assessCommunicationSkills',
    method: () => generator.assessCommunicationSkills(emptyBazi, incompletePatternResult)
  },
  {
    name: 'generateHealthGuidance',
    method: () => generator.generateHealthGuidance(emptyBazi, { yongshen: '木' })
  }
];

let successCount = 0;
let totalCount = testMethods.length;

testMethods.forEach((test, index) => {
  try {
    console.log(`\n${index + 1}. 测试 ${test.name}:`);
    const result = test.method();
    console.log(`   ✅ 成功: ${JSON.stringify(result).substring(0, 100)}...`);
    successCount++;
  } catch (error) {
    console.log(`   ❌ 失败: ${error.message}`);
  }
});

console.log('\n📊 测试结果统计:');
console.log(`✅ 成功: ${successCount}/${totalCount}`);
console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);

if (successCount === totalCount) {
  console.log('\n🎉 所有测试通过！增强建议生成器已完全修复。');
  console.log('\n💡 修复总结:');
  console.log('1. ✅ 为所有访问 bazi.day.heavenly 的方法添加了安全检查');
  console.log('2. ✅ 为所有访问 bazi.month.earthly 的方法添加了安全检查');
  console.log('3. ✅ 提供了合理的默认返回值');
  console.log('4. ✅ 添加了警告日志以便调试');
  console.log('\n🔧 这应该完全解决了日志中的 "Cannot read property \'heavenly\' of undefined" 错误');
} else {
  console.log('\n⚠️ 仍有部分测试失败，需要进一步检查');
}

// 测试正常数据
console.log('\n📋 测试正常数据:');
const normalBazi = {
  year: { heavenly: '甲', earthly: '子' },
  month: { heavenly: '丙', earthly: '寅' },
  day: { heavenly: '戊', earthly: '午' },
  hour: { heavenly: '庚', earthly: '申' }
};

try {
  const personalityResult = generator.analyzePersonalityDepth(normalBazi, {}, {});
  console.log('✅ 正常数据测试成功 - analyzePersonalityDepth');
  
  const mbtiResult = generator.mapToMBTI(normalBazi, {});
  console.log('✅ 正常数据测试成功 - mapToMBTI');
  
  const behaviorResult = generator.analyzeBehaviorPatterns(normalBazi, {});
  console.log('✅ 正常数据测试成功 - analyzeBehaviorPatterns');
  
  console.log('\n🎯 正常数据测试全部通过！');
} catch (error) {
  console.log('❌ 正常数据测试失败:', error.message);
}

module.exports = { EnhancedAdviceGenerator };
