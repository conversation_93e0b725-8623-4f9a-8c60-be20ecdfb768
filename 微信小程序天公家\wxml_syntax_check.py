#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import xml.etree.ElementTree as ET

def wxml_syntax_check(file_path):
    """检查WXML语法是否正确"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除微信小程序特有的属性，转换为标准XML进行验证
        # 移除wx:前缀的属性
        content = re.sub(r'\s+wx:[^=]+="[^"]*"', '', content)
        # 移除{{}}表达式
        content = re.sub(r'\{\{[^}]*\}\}', 'placeholder', content)
        # 移除bindtap等事件绑定
        content = re.sub(r'\s+bind[^=]+="[^"]*"', '', content)
        content = re.sub(r'\s+catch[^=]+="[^"]*"', '', content)
        # 移除data-前缀的属性
        content = re.sub(r'\s+data-[^=]+="[^"]*"', '', content)
        
        # 尝试解析XML
        root = ET.fromstring(content)
        print("✅ WXML语法检查通过！文件结构正确。")
        return True
        
    except ET.ParseError as e:
        print(f"❌ WXML语法错误：{e}")
        return False
    except Exception as e:
        print(f"❌ 检查过程中出现错误：{e}")
        return False

if __name__ == "__main__":
    wxml_syntax_check("pages/bazi-result/index.wxml")
