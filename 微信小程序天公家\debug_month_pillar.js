/**
 * 调试月柱计算，找出正确的算法
 */

// 引入权威农历转换器
const AuthoritativeLunarConverter = require('./utils/authoritative_lunar_data.js');

function debugMonthPillar() {
  console.log('🔍 调试月柱计算');
  console.log('='.repeat(50));
  
  // 测试日期：2015年7月23日
  const testDate = new Date(2015, 6, 23);
  
  try {
    // 使用权威农历转换器
    const lunarResult = AuthoritativeLunarConverter.solarToLunar(testDate);
    
    console.log('📊 权威万年历详细信息:');
    console.log('年干支:', lunarResult.ganZhi.year);
    console.log('月干支:', lunarResult.ganZhi.month);
    console.log('日干支:', lunarResult.ganZhi.day);
    
    // 分析月柱计算
    console.log('\n🔍 月柱分析:');
    console.log('权威月柱:', lunarResult.ganZhi.month);
    console.log('应该是: 癸未');
    
    // 分析五虎遁
    console.log('\n📝 五虎遁分析:');
    console.log('年干: 乙');
    console.log('月支: 未');
    
    // 推算正确的五虎遁起始
    // 如果乙年未月是癸未，那么：
    // 寅月(1) -> 壬寅
    // 卯月(2) -> 癸卯  
    // 辰月(3) -> 甲辰
    // 巳月(4) -> 乙巳
    // 午月(5) -> 丙午
    // 未月(6) -> 丁未 ❌
    
    // 重新推算：如果未月是癸未
    // 那么寅月应该是戊寅
    // 戊=4，所以乙年起戊寅
    
    console.log('\n🔧 推算正确的五虎遁:');
    console.log('如果乙年未月是癸未，那么:');
    console.log('寅月(1): 戊寅 (戊=4)');
    console.log('卯月(2): 己卯 (己=5)');
    console.log('辰月(3): 庚辰 (庚=6)');
    console.log('巳月(4): 辛巳 (辛=7)');
    console.log('午月(5): 壬午 (壬=8)');
    console.log('未月(6): 癸未 (癸=9) ✅');
    
    console.log('\n所以乙年应该起戊寅，戊的索引是4');
    
    // 验证其他年份
    console.log('\n📚 传统五虎遁口诀验证:');
    console.log('甲己之年丙作首 -> 甲年起丙寅 (丙=2)');
    console.log('乙庚之年戊为头 -> 乙年起戊寅 (戊=4) ✅');
    console.log('丙辛之年庚寅上 -> 丙年起庚寅 (庚=6)');
    console.log('丁壬壬寅顺水流 -> 丁年起壬寅 (壬=8)');
    console.log('戊癸之年甲寅始 -> 戊年起甲寅 (甲=0)');
    
    return lunarResult;
    
  } catch (error) {
    console.error('❌ 权威万年历查询失败:', error);
    return null;
  }
}

// 运行调试
debugMonthPillar();
