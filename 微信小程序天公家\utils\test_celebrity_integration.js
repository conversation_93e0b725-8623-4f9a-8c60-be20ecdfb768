/**
 * 历史名人验证功能集成测试
 * 测试前端集成是否正常工作
 */

const CelebrityDatabaseAPI = require('./celebrity_database_api.js');
const BaziSimilarityMatcher = require('./bazi_similarity_matcher.js');

class CelebrityIntegrationTester {
  constructor() {
    this.celebrityAPI = CelebrityDatabaseAPI;
    this.similarityMatcher = BaziSimilarityMatcher;
    this.testResults = [];
  }

  /**
   * 运行所有集成测试
   */
  runAllTests() {
    console.log('🧪 开始历史名人验证功能集成测试...\n');

    const tests = [
      this.testDatabaseAccess,
      this.testStatistics,
      this.testSimilarityMatching,
      this.testCelebritySearch,
      this.testFrontendDataFormat
    ];

    tests.forEach((test, index) => {
      try {
        console.log(`📋 测试 ${index + 1}: ${test.name}`);
        const result = test.call(this);
        this.testResults.push({
          name: test.name,
          status: 'PASS',
          result: result
        });
        console.log('✅ 通过\n');
      } catch (error) {
        console.error('❌ 失败:', error.message);
        this.testResults.push({
          name: test.name,
          status: 'FAIL',
          error: error.message
        });
        console.log('');
      }
    });

    this.generateTestReport();
  }

  /**
   * 测试数据库访问
   */
  testDatabaseAccess() {
    const celebrities = this.celebrityAPI.getAllCelebrities();
    
    if (!Array.isArray(celebrities)) {
      throw new Error('数据库返回格式错误');
    }
    
    if (celebrities.length === 0) {
      throw new Error('数据库为空');
    }

    // 检查数据结构
    const firstCelebrity = celebrities[0];
    const requiredFields = ['id', 'basicInfo', 'bazi', 'pattern', 'verification'];
    
    requiredFields.forEach(field => {
      if (!firstCelebrity[field]) {
        throw new Error(`缺少必需字段: ${field}`);
      }
    });

    return {
      totalCelebrities: celebrities.length,
      sampleCelebrity: firstCelebrity.basicInfo.name
    };
  }

  /**
   * 测试统计信息
   */
  testStatistics() {
    const stats = this.celebrityAPI.getStatistics();
    
    const requiredStats = ['totalCelebrities', 'dynastyDistribution', 'patternDistribution', 'averageVerificationScore'];
    
    requiredStats.forEach(stat => {
      if (stats[stat] === undefined) {
        throw new Error(`缺少统计信息: ${stat}`);
      }
    });

    if (stats.totalCelebrities <= 0) {
      throw new Error('统计数据异常');
    }

    return {
      totalCelebrities: stats.totalCelebrities,
      dynastyCount: stats.dynastyDistribution.length,
      averageScore: stats.averageVerificationScore
    };
  }

  /**
   * 测试相似度匹配
   */
  testSimilarityMatching() {
    // 创建测试用户八字
    const testUserBazi = {
      bazi: {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        hour: { gan: '壬', zhi: '戌' }
      },
      pattern: {
        mainPattern: '正财格',
        dayMaster: '戊',
        yongshen: '水'
      }
    };

    const similarCelebrities = this.celebrityAPI.findSimilarCelebrities(testUserBazi, {
      limit: 5,
      minSimilarity: 0.1
    });

    if (!Array.isArray(similarCelebrities)) {
      throw new Error('相似度匹配返回格式错误');
    }

    // 测试相似度计算
    if (similarCelebrities.length > 0) {
      const firstMatch = similarCelebrities[0];
      if (!firstMatch.celebrity || typeof firstMatch.similarity !== 'number') {
        throw new Error('相似度匹配结果格式错误');
      }
    }

    return {
      matchCount: similarCelebrities.length,
      topMatch: similarCelebrities.length > 0 ? {
        name: similarCelebrities[0].celebrity.basicInfo.name,
        similarity: similarCelebrities[0].similarity
      } : null
    };
  }

  /**
   * 测试名人搜索
   */
  testCelebritySearch() {
    // 测试按姓名搜索
    const searchResults = this.celebrityAPI.searchCelebrities({
      name: '李'
    });

    if (!Array.isArray(searchResults)) {
      throw new Error('搜索返回格式错误');
    }

    // 测试按朝代搜索
    const dynastyResults = this.celebrityAPI.searchCelebrities({
      dynasty: '唐'
    });

    if (!Array.isArray(dynastyResults)) {
      throw new Error('朝代搜索返回格式错误');
    }

    return {
      nameSearchCount: searchResults.length,
      dynastySearchCount: dynastyResults.length
    };
  }

  /**
   * 测试前端数据格式
   */
  testFrontendDataFormat() {
    const celebrities = this.celebrityAPI.getAllCelebrities();
    const testCelebrity = celebrities[0];

    // 检查前端需要的数据格式
    const frontendData = {
      id: testCelebrity.id,
      name: testCelebrity.basicInfo.name,
      dynasty: testCelebrity.basicInfo.dynasty,
      pattern: testCelebrity.pattern.mainPattern,
      yongshen: testCelebrity.pattern.yongshen,
      verificationScore: testCelebrity.verification.algorithmMatch
    };

    // 验证数据完整性
    Object.entries(frontendData).forEach(([key, value]) => {
      if (value === undefined || value === null) {
        throw new Error(`前端数据字段 ${key} 为空`);
      }
    });

    return frontendData;
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log('📊 历史名人验证功能集成测试报告');
    console.log('='.repeat(50));
    
    const passCount = this.testResults.filter(r => r.status === 'PASS').length;
    const failCount = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`总测试数: ${this.testResults.length}`);
    console.log(`通过: ${passCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`);
    console.log('');

    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      
      if (result.status === 'PASS' && result.result) {
        console.log(`   结果: ${JSON.stringify(result.result, null, 2)}`);
      } else if (result.status === 'FAIL') {
        console.log(`   错误: ${result.error}`);
      }
      console.log('');
    });

    // 总结
    if (failCount === 0) {
      console.log('🎉 所有测试通过！历史名人验证功能集成成功！');
    } else {
      console.log('⚠️ 部分测试失败，需要修复问题后重新测试。');
    }
  }

  /**
   * 测试前端集成模拟
   */
  simulateFrontendIntegration() {
    console.log('🎭 模拟前端集成测试...\n');

    try {
      // 模拟用户八字数据
      const mockUserData = {
        yearGan: '甲', yearZhi: '子',
        monthGan: '丙', monthZhi: '寅', 
        dayGan: '戊', dayZhi: '午',
        hourGan: '壬', hourZhi: '戌'
      };

      // 模拟前端调用流程
      console.log('1. 构建用户八字信息...');
      const userBaziInfo = {
        bazi: {
          year: { gan: mockUserData.yearGan, zhi: mockUserData.yearZhi },
          month: { gan: mockUserData.monthGan, zhi: mockUserData.monthZhi },
          day: { gan: mockUserData.dayGan, zhi: mockUserData.dayZhi },
          hour: { gan: mockUserData.hourGan, zhi: mockUserData.hourZhi }
        },
        pattern: {
          mainPattern: '正财格',
          dayMaster: mockUserData.dayGan,
          yongshen: '水'
        }
      };

      console.log('2. 查找相似名人...');
      const similarCelebrities = this.celebrityAPI.findSimilarCelebrities(userBaziInfo, {
        limit: 5,
        minSimilarity: 0.3
      });

      console.log('3. 获取统计信息...');
      const stats = this.celebrityAPI.getStatistics();

      console.log('4. 构建前端显示数据...');
      const frontendData = {
        similarCelebrities: similarCelebrities,
        historicalStats: {
          totalCelebrities: stats.totalCelebrities,
          dynastyCount: stats.dynastyDistribution.length,
          averageScore: stats.averageVerificationScore,
          matchCount: similarCelebrities.length
        },
        historicalVerificationLoading: false
      };

      console.log('✅ 前端集成模拟成功！');
      console.log('📋 模拟结果:');
      console.log(`   - 找到 ${frontendData.similarCelebrities.length} 位相似名人`);
      console.log(`   - 数据库共有 ${frontendData.historicalStats.totalCelebrities} 位名人`);
      console.log(`   - 覆盖 ${frontendData.historicalStats.dynastyCount} 个朝代`);
      console.log(`   - 平均验证分数: ${(frontendData.historicalStats.averageScore * 100).toFixed(1)}%`);

      return frontendData;

    } catch (error) {
      console.error('❌ 前端集成模拟失败:', error);
      throw error;
    }
  }
}

// 导出测试类
module.exports = CelebrityIntegrationTester;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new CelebrityIntegrationTester();
  tester.runAllTests();
  console.log('\n' + '='.repeat(50) + '\n');
  tester.simulateFrontendIntegration();
}
