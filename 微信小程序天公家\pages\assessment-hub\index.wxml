<view class="container {{themeClass}}">
  <!-- 顶部导航区域 -->
  <view class="header-area">
    <!-- 左侧用户图标 -->
    <view class="profile-icon" bindtap="navigateToProfile">
      <image src="/assets/icons/new/profile_icon.svg" mode="aspectFit"></image>
    </view>
    
    <!-- 中间标签选择器 -->
    <view class="tab-container">
      <view class="tab {{activeTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">今天</view>
      <view class="tab {{activeTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">历史</view>
    </view>
    
    <!-- 右侧占位 -->
    <view style="width: 80rpx;"></view>
  </view>
  
  <!-- 日期显示 -->
  <view class="date-display">
    {{currentDate}} 
    <text style="margin-left:20rpx;color:#DAA520;">当前占卜: {{roles[currentIndex]}}</text>
  </view>
  
  <!-- 滑动提示 -->
  <view class="swipe-tip">
    <text>↓ 上下滑动切换占卜方式 ↓</text>
  </view>

  <!-- 内容滚动区域 -->
  <swiper class="content-swiper" bindchange="onSwiperChange" current="{{currentIndex}}" duration="300" circular="true" display-multiple-items="1" easing-function="easeInOutCubic" vertical="true" previous-margin="0px" next-margin="0px">
    <!-- 天公师兄 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card tarot-divination" bindtap="sendMessage" data-role="tarot">
          <view class="role-title">
            <text>天公师兄</text>
            <image class="role-icon" src="/assets/icons/tiangong-master.svg"></image>
          </view>
          <view class="role-description">
            <text>擅长专业\n\n即时问事    趋吉避凶</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="sendMessage" data-role="tarot">
              <text>洞察先机</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 天工师父 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card bazi-divination" bindtap="sendMessage" data-role="bazi">
          <view class="role-title">
            <text>天工师父</text>
            <image class="role-icon role-icon-png" src="/assets/icons/tiangong-shifu.png"></image>
          </view>
          <view class="role-description">
            <text>擅长专业\n\n八字起盘   命定一生</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="sendMessage" data-role="bazi">
              <text>排盘分析</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 天公师娘 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card ziwei-divination" bindtap="sendMessage" data-role="ziwei">
          <view class="role-title">
            <text>天公师娘</text>
            <image class="role-icon role-icon-png" src="/assets/icons/tiangong-shiniang.png"></image>
          </view>
          <view class="role-description">
            <text>擅长专业\n\n找到相对合适你的人</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="sendMessage" data-role="ziwei">
              <text>开始占卜</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 奇门遁甲 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card qimen-divination" bindtap="sendMessage" data-role="qimen">
          <view class="role-title">
            <text>奇门遁甲</text>
            <image class="role-icon" src="/assets/icons/qimen.png"></image>
          </view>
          <view class="role-description">
            <text>帝王之学秘术\n时空奥秘尽在掌握</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="sendMessage" data-role="qimen">
              <text>开始占卜</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <!-- 六爻占卜 -->
    <swiper-item class="swiper-item">
      <scroll-view class="role-scroll-view" scroll-y="true">
        <view class="role-card liuyao-divination" bindtap="sendMessage" data-role="liuyao">
          <view class="role-title">
            <text>六爻占卜</text>
            <image class="role-icon" src="/assets/icons/liuyao.png"></image>
          </view>
          <view class="role-description">
            <text>铜钱摇卦问事\n六爻变化知吉凶</text>
          </view>
          <view class="start-chat-wrapper">
            <view class="start-chat" catchtap="sendMessage" data-role="liuyao">
              <text>开始占卜</text>
              <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
            </view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <input class="message-input" placeholder="输入你的回答..." bindinput="onInputChange" value="{{inputValue}}" />
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage" hover-class="button-hover">
      <image src="/assets/icons/new/send_icon.svg" mode="aspectFit"></image>
    </view>
    <view class="voice-button" bindtap="activateVoiceInput" hover-class="button-hover">
      <image src="/assets/icons/new/voice_icon.svg" mode="aspectFit"></image>
    </view>
  </view>
</view> 